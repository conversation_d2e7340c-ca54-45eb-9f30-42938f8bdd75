# 纷享链 BMS 服务

## 项目概述

纷享链BMS（Business Management System）服务是一个基于Java 17和Spring Boot的企业级业务管理系统，主要用于处理计费、费用管理等业务逻辑。

## 项目结构

```
enjoylink-bms-service/
├── bms-service/          # 主服务模块
├── bms-service-api/      # API接口模块
├── pom.xml              # 根项目配置
└── README.md            # 项目说明文档
```

## 核心功能

### 自动计费系统

项目实现了基于Groovy脚本的自动计费系统，支持多种计费场景：

#### GroovyCalculateExtraType 枚举类型

1. **DEFAULT (0)** - 默认计费方式
   - 使用传统的费用构建逻辑
   - 适用于标准的单据计费场景

2. **SCHEDULT_JOB (1)** - 调度单维度计费，作业单维度分摊
   - 调度单维度进行计费
   - 作业单维度输出计费结果
   - 支持根据extraInfo进行费用分摊

### 费用构建流程

#### 默认方式 (DEFAULT)
1. 构建主费用信息 (BmsYfcostMainInfoPO)
2. 构建费用信息 (BmsYfcostInfoPO)
3. 构建中间表信息 (BmsYfexpensesMiddlePO)
4. 构建分摊表信息 (BmsYfexpensesMiddleSharePO)

#### 调度单作业单分摊方式 (SCHEDULT_JOB)
1. 检查extraInfo是否有值（key为作业单号，value为该作业单的费用）
2. 根据extraInfo构建作业单维度的分摊表信息
3. 使用CostUtil工具向上汇总到费用信息和主费用信息
4. 如果extraInfo为空，则回退到默认方式

### 核心工具类

#### CostUtil
- `allocateCost()` - 费用分摊计算
- `aggregateCost()` - 费用汇总计算
- `setCostField()` - 费用字段赋值
- `YF_COST_SETTLE_SUM_FIELDS` - 应付费用汇总字段常量

### 技术特性

- **异步处理**: 使用CompletableFuture进行异步计费处理
- **费用分摊**: 支持按重量、体积、件数等维度进行费用分摊
- **灵活配置**: 通过枚举类型支持不同的计费场景
- **数据一致性**: 使用事务保证数据的一致性

## 开发环境

- Java 17
- Maven 3.6+
- Spring Boot 2.x

## 使用说明

### 计费流程调用

```java
// 获取计费结果
CalculateDbParam param = new CalculateDbParam();
// 设置计费参数...

// 执行应付费用计算
CalculateYfDbResult result = calculateAfterService.yfDbCalculate(param);
```

### 自定义计费场景

1. 在GroovyCalculateExtraType中添加新的枚举值
2. 在buildYfCostInfo方法中添加对应的处理逻辑
3. 实现具体的费用构建方法

## 注意事项

1. **费用分摊**: 使用CostUtil工具进行分摊和汇总，确保计算准确性
2. **数据完整性**: 所有费用相关的PO对象都需要设置完整的基础信息
3. **异常处理**: 当extraInfo为空或无效时，系统会自动回退到默认处理方式
4. **日志记录**: 关键操作都有详细的日志记录，便于问题排查

## 扩展指南

如需添加新的计费场景：

1. 在GroovyCalculateExtraType枚举中添加新类型
2. 在buildYfCostInfo方法的switch语句中添加新的case
3. 实现对应的费用构建逻辑
4. 确保使用CostUtil进行正确的费用分摊和汇总

## 联系方式

如有问题请联系开发团队。
