# 纷享链 BMS 服务

## 项目概述

纷享链BMS（Business Management System）服务是一个基于Java 17和Spring Boot的企业级业务管理系统，主要用于处理计费、费用管理等业务逻辑。

## 项目结构

```
enjoylink-bms-service/
├── bms-service/          # 主服务模块
├── bms-service-api/      # API接口模块
├── pom.xml              # 根项目配置
└── README.md            # 项目说明文档
```

## 核心功能

### 自动计费系统

项目实现了基于Groovy脚本的自动计费系统，采用**策略模式**设计，支持多种计费场景的灵活扩展：

#### 策略模式架构

```
YfCostBuildStrategy (策略接口)
├── DefaultYfCostBuildStrategy (默认策略)
├── ScheduleJobYfCostBuildStrategy (调度单作业单分摊策略)
└── YfCostBuildStrategyFactory (策略工厂)
```

#### GroovyCalculateExtraType 枚举类型

1. **DEFAULT (0)** - 默认计费方式
   - 使用传统的费用构建逻辑
   - 适用于标准的单据计费场景
   - 对应策略：`DefaultYfCostBuildStrategy`

2. **SCHEDULT_JOB (1)** - 调度单维度计费，作业单维度分摊
   - 调度单维度进行计费
   - 作业单维度输出计费结果
   - 支持根据extraInfo进行费用分摊
   - 对应策略：`ScheduleJobYfCostBuildStrategy`

### 策略模式设计优势

1. **单一职责**：每个策略类只负责一种计费场景
2. **开闭原则**：对扩展开放，对修改关闭
3. **易于维护**：新增计费场景只需添加新的策略实现
4. **代码清晰**：CalculateAfterServiceImpl保持简洁，专注于业务流程

### 费用构建流程

#### 策略选择流程
1. 根据`costResult.getExtraType()`获取计费类型
2. 通过`YfCostBuildStrategyFactory`获取对应策略
3. 调用策略的`buildYfCostInfo()`方法构建费用信息
4. 将构建结果添加到相应的Map中

#### 默认策略 (DefaultYfCostBuildStrategy)
1. 构建主费用信息 (BmsYfcostMainInfoPO)
2. 构建费用信息 (BmsYfcostInfoPO)
3. 构建中间表信息 (BmsYfexpensesMiddlePO)
4. 构建分摊表信息 (BmsYfexpensesMiddleSharePO)

#### 调度单作业单分摊策略 (ScheduleJobYfCostBuildStrategy)
1. 检查extraInfo是否有值（key为作业单号，value为该作业单的费用）
2. 根据extraInfo构建作业单维度的分摊表信息
3. 使用CostUtil工具向上汇总到费用信息和主费用信息
4. 如果extraInfo为空，则回退到默认策略

### 核心工具类

#### CostUtil
- `allocateCost()` - 费用分摊计算
- `aggregateCost()` - 费用汇总计算
- `setCostField()` - 费用字段赋值
- `YF_COST_SETTLE_SUM_FIELDS` - 应付费用汇总字段常量

#### YfCostBuildResult 结果封装
- `mainCostInfo` - 主费用信息 (BmsYfcostMainInfoPO)
- `costInfos` - 费用信息列表 (List<BmsYfcostInfoPO>) **已调整为List类型**
- `middleInfo` - 中间表信息 (BmsYfexpensesMiddlePO)
- `shareInfoList` - 分摊表信息列表 (List<BmsYfexpensesMiddleSharePO>)

### 技术特性

- **异步处理**: 使用CompletableFuture进行异步计费处理
- **费用分摊**: 支持按重量、体积、件数等维度进行费用分摊
- **灵活配置**: 通过枚举类型支持不同的计费场景
- **数据一致性**: 使用事务保证数据的一致性

## 开发环境

- Java 17
- Maven 3.6+
- Spring Boot 2.x

## 使用说明

### 计费流程调用

```java
// 获取计费结果
CalculateDbParam param = new CalculateDbParam();
// 设置计费参数...

// 执行应付费用计算（内部会自动选择合适的策略）
CalculateYfDbResult result = calculateAfterService.yfDbCalculate(param);
```

### 策略使用示例

```java
// 1. 默认计费场景
CalculateCostResult costResult = new CalculateCostResult();
costResult.setExtraType(GroovyCalculateExtraType.DEFAULT.getCode());
// 系统会自动选择DefaultYfCostBuildStrategy

// 2. 调度单作业单分摊场景
CalculateCostResult costResult = new CalculateCostResult();
costResult.setExtraType(GroovyCalculateExtraType.SCHEDULT_JOB.getCode());
Map<String, Object> extraInfo = new HashMap<>();
extraInfo.put("JOB001", new BigDecimal("80.00"));  // 作业单号: 费用
extraInfo.put("JOB002", new BigDecimal("120.00"));
costResult.setExtraInfo(extraInfo);
// 系统会自动选择ScheduleJobYfCostBuildStrategy
```

### 扩展新的计费场景

1. **创建新的策略实现类**
```java
@Component
public class CustomYfCostBuildStrategy implements YfCostBuildStrategy {
    @Override
    public GroovyCalculateExtraType getSupportedType() {
        return GroovyCalculateExtraType.CUSTOM; // 新的枚举值
    }

    @Override
    public YfCostBuildResult buildYfCostInfo(...) {
        // 实现自定义的费用构建逻辑
    }
}
```

2. **在GroovyCalculateExtraType中添加新的枚举值**
```java
CUSTOM(2, "自定义计费", "自定义计费场景描述")
```

3. **策略工厂会自动注册新策略**
   - Spring容器启动时，`YfCostBuildStrategyFactory`会自动扫描并注册所有策略实现

## 注意事项

1. **策略注册**: 所有策略实现类需要添加`@Component`注解，确保被Spring容器管理
2. **费用分摊**: 使用CostUtil工具进行分摊和汇总，确保计算准确性
3. **数据完整性**: 所有费用相关的PO对象都需要设置完整的基础信息
4. **异常处理**: 当extraInfo为空或无效时，调度单作业单分摊策略会自动回退到默认策略
5. **日志记录**: 关键操作都有详细的日志记录，便于问题排查
6. **策略选择**: 系统会根据extraType自动选择合适的策略，无需手动干预

## 策略模式优势

1. **高内聚低耦合**: 每个策略独立实现，互不影响
2. **易于测试**: 可以单独测试每个策略的逻辑
3. **动态切换**: 运行时根据extraType动态选择策略
4. **代码复用**: 公共逻辑可以抽取到基类或工具类中
5. **维护性强**: 修改某个计费场景不会影响其他场景

## 联系方式

如有问题请联系开发团队。
