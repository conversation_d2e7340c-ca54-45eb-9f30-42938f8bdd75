package com.bbyb.joy.bms.calculate.service.impl;

import com.bbyb.joy.bms.calculate.domain.result.CalculateCostResult;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyCalculateExtraType;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CalculateAfterServiceImpl测试类
 * 测试不同GroovyCalculateExtraType场景下的费用构建逻辑
 */
class CalculateAfterServiceImplTest {

    @InjectMocks
    private CalculateAfterServiceImpl calculateAfterService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试默认计费方式
     */
    @Test
    void testDefaultExtraType() {
        // 创建测试数据
        CalculateCostResult costResult = new CalculateCostResult();
        costResult.setExtraType(GroovyCalculateExtraType.DEFAULT.getCode());
        costResult.setCalculateResult(new BigDecimal("100.00"));
        costResult.setFeeType(1); // 运费
        
        // 验证extraType解析
        GroovyCalculateExtraType extraType = GroovyCalculateExtraType.getByCode(costResult.getExtraType());
        assertEquals(GroovyCalculateExtraType.DEFAULT, extraType);
    }

    /**
     * 测试调度单作业单分摊方式
     */
    @Test
    void testScheduleJobExtraType() {
        // 创建测试数据
        CalculateCostResult costResult = new CalculateCostResult();
        costResult.setExtraType(GroovyCalculateExtraType.SCHEDULT_JOB.getCode());
        costResult.setCalculateResult(new BigDecimal("200.00"));
        costResult.setFeeType(1); // 运费
        
        // 设置extraInfo（作业单号和对应费用）
        Map<String, Object> extraInfo = new HashMap<>();
        extraInfo.put("JOB001", new BigDecimal("80.00"));
        extraInfo.put("JOB002", new BigDecimal("120.00"));
        costResult.setExtraInfo(extraInfo);
        
        // 验证extraType解析
        GroovyCalculateExtraType extraType = GroovyCalculateExtraType.getByCode(costResult.getExtraType());
        assertEquals(GroovyCalculateExtraType.SCHEDULT_JOB, extraType);
        
        // 验证extraInfo
        assertNotNull(costResult.getExtraInfo());
        assertEquals(2, costResult.getExtraInfo().size());
        assertEquals(new BigDecimal("80.00"), costResult.getExtraInfo().get("JOB001"));
        assertEquals(new BigDecimal("120.00"), costResult.getExtraInfo().get("JOB002"));
    }

    /**
     * 测试extraInfo为空时的回退逻辑
     */
    @Test
    void testScheduleJobExtraTypeWithEmptyExtraInfo() {
        // 创建测试数据
        CalculateCostResult costResult = new CalculateCostResult();
        costResult.setExtraType(GroovyCalculateExtraType.SCHEDULT_JOB.getCode());
        costResult.setCalculateResult(new BigDecimal("150.00"));
        costResult.setFeeType(1); // 运费
        costResult.setExtraInfo(new HashMap<>()); // 空的extraInfo
        
        // 验证extraType解析
        GroovyCalculateExtraType extraType = GroovyCalculateExtraType.getByCode(costResult.getExtraType());
        assertEquals(GroovyCalculateExtraType.SCHEDULT_JOB, extraType);
        
        // 验证extraInfo为空
        assertTrue(costResult.getExtraInfo().isEmpty());
    }

    /**
     * 测试无效extraType的处理
     */
    @Test
    void testInvalidExtraType() {
        // 创建测试数据
        CalculateCostResult costResult = new CalculateCostResult();
        costResult.setExtraType(999); // 无效的extraType
        costResult.setCalculateResult(new BigDecimal("100.00"));
        
        // 验证会回退到默认类型
        GroovyCalculateExtraType extraType = GroovyCalculateExtraType.getByCode(costResult.getExtraType());
        assertNull(extraType); // 无效的code会返回null
    }

    /**
     * 测试GroovyCalculateExtraType枚举的基本功能
     */
    @Test
    void testGroovyCalculateExtraTypeEnum() {
        // 测试DEFAULT
        assertEquals(0, GroovyCalculateExtraType.DEFAULT.getCode());
        assertEquals("默认", GroovyCalculateExtraType.DEFAULT.getName());
        
        // 测试SCHEDULT_JOB
        assertEquals(1, GroovyCalculateExtraType.SCHEDULT_JOB.getCode());
        assertEquals("调度单-单维度计费-作业单维度费用输出", GroovyCalculateExtraType.SCHEDULT_JOB.getName());
        
        // 测试getByCode方法
        assertEquals(GroovyCalculateExtraType.DEFAULT, GroovyCalculateExtraType.getByCode(0));
        assertEquals(GroovyCalculateExtraType.SCHEDULT_JOB, GroovyCalculateExtraType.getByCode(1));
        assertNull(GroovyCalculateExtraType.getByCode(999));
        
        // 测试getByName方法
        assertEquals(GroovyCalculateExtraType.DEFAULT, GroovyCalculateExtraType.getByName("默认"));
        assertEquals(GroovyCalculateExtraType.SCHEDULT_JOB, 
            GroovyCalculateExtraType.getByName("调度单-单维度计费-作业单维度费用输出"));
        assertNull(GroovyCalculateExtraType.getByName("不存在的名称"));
    }
}
