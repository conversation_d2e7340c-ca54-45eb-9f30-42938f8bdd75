package com.bbyb.joy.bms.calculate.service.impl;

import com.bbyb.joy.bms.calculate.domain.result.CalculateCostResult;
import com.bbyb.joy.bms.calculate.strategy.YfCostBuildStrategy;
import com.bbyb.joy.bms.calculate.strategy.YfCostBuildStrategyFactory;
import com.bbyb.joy.bms.calculate.strategy.impl.DefaultYfCostBuildStrategy;
import com.bbyb.joy.bms.calculate.strategy.impl.ScheduleJobYfCostBuildStrategy;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyCalculateExtraType;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * CalculateAfterServiceImpl测试类
 * 测试策略模式下不同GroovyCalculateExtraType场景的费用构建逻辑
 */
class CalculateAfterServiceImplTest {

    @InjectMocks
    private CalculateAfterServiceImpl calculateAfterService;

    @Mock
    private YfCostBuildStrategyFactory strategyFactory;

    @Mock
    private DefaultYfCostBuildStrategy defaultStrategy;

    @Mock
    private ScheduleJobYfCostBuildStrategy scheduleJobStrategy;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试策略工厂获取默认策略
     */
    @Test
    void testGetDefaultStrategy() {
        // 模拟策略工厂返回默认策略
        when(strategyFactory.getStrategy(GroovyCalculateExtraType.DEFAULT.getCode()))
            .thenReturn(defaultStrategy);

        // 获取策略
        YfCostBuildStrategy strategy = strategyFactory.getStrategy(GroovyCalculateExtraType.DEFAULT.getCode());

        // 验证返回的是默认策略
        assertEquals(defaultStrategy, strategy);
        verify(strategyFactory).getStrategy(GroovyCalculateExtraType.DEFAULT.getCode());
    }

    /**
     * 测试策略工厂获取调度单作业单分摊策略
     */
    @Test
    void testGetScheduleJobStrategy() {
        // 模拟策略工厂返回调度单作业单分摊策略
        when(strategyFactory.getStrategy(GroovyCalculateExtraType.SCHEDULT_JOB.getCode()))
            .thenReturn(scheduleJobStrategy);

        // 获取策略
        YfCostBuildStrategy strategy = strategyFactory.getStrategy(GroovyCalculateExtraType.SCHEDULT_JOB.getCode());

        // 验证返回的是调度单作业单分摊策略
        assertEquals(scheduleJobStrategy, strategy);
        verify(strategyFactory).getStrategy(GroovyCalculateExtraType.SCHEDULT_JOB.getCode());
    }

    /**
     * 测试策略工厂对无效extraType的处理
     */
    @Test
    void testGetStrategyWithInvalidExtraType() {
        // 模拟策略工厂对无效extraType返回默认策略
        when(strategyFactory.getStrategy(999)).thenReturn(defaultStrategy);

        // 获取策略
        YfCostBuildStrategy strategy = strategyFactory.getStrategy(999);

        // 验证返回的是默认策略
        assertEquals(defaultStrategy, strategy);
        verify(strategyFactory).getStrategy(999);
    }

    /**
     * 测试策略支持的类型
     */
    @Test
    void testStrategySupportedTypes() {
        // 验证默认策略支持的类型
        when(defaultStrategy.getSupportedType()).thenReturn(GroovyCalculateExtraType.DEFAULT);
        assertEquals(GroovyCalculateExtraType.DEFAULT, defaultStrategy.getSupportedType());

        // 验证调度单作业单分摊策略支持的类型
        when(scheduleJobStrategy.getSupportedType()).thenReturn(GroovyCalculateExtraType.SCHEDULT_JOB);
        assertEquals(GroovyCalculateExtraType.SCHEDULT_JOB, scheduleJobStrategy.getSupportedType());
    }

    /**
     * 测试GroovyCalculateExtraType枚举的基本功能
     */
    @Test
    void testGroovyCalculateExtraTypeEnum() {
        // 测试DEFAULT
        assertEquals(0, GroovyCalculateExtraType.DEFAULT.getCode());
        assertEquals("默认", GroovyCalculateExtraType.DEFAULT.getName());
        
        // 测试SCHEDULT_JOB
        assertEquals(1, GroovyCalculateExtraType.SCHEDULT_JOB.getCode());
        assertEquals("调度单-单维度计费-作业单维度费用输出", GroovyCalculateExtraType.SCHEDULT_JOB.getName());
        
        // 测试getByCode方法
        assertEquals(GroovyCalculateExtraType.DEFAULT, GroovyCalculateExtraType.getByCode(0));
        assertEquals(GroovyCalculateExtraType.SCHEDULT_JOB, GroovyCalculateExtraType.getByCode(1));
        assertNull(GroovyCalculateExtraType.getByCode(999));
        
        // 测试getByName方法
        assertEquals(GroovyCalculateExtraType.DEFAULT, GroovyCalculateExtraType.getByName("默认"));
        assertEquals(GroovyCalculateExtraType.SCHEDULT_JOB, 
            GroovyCalculateExtraType.getByName("调度单-单维度计费-作业单维度费用输出"));
        assertNull(GroovyCalculateExtraType.getByName("不存在的名称"));
    }
}
