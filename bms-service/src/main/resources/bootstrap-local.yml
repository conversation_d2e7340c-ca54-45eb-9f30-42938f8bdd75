spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  cloud:
    nacos:
      discovery:
        server-addr: **************:18848
        username: test-enjoy-link-user-lign
        password: test-enjoy-link-user-lign
        namespace: 45669745-e017-48cf-b3f6-3161a0c82512
      config:
        server-addr: **************:18848
        username: test-enjoy-link-user-lign
        password: test-enjoy-link-user-lign
        namespace: f3912e33-94f6-4e94-a6a1-8faa933008d6
  kafka:
    bootstrap-servers: *************:54771
  redis:
    password: xVJcHejFRgW2NU
    host: *************
    port: 52671
seata:
  service:
    groupList:
      default: **************:38091