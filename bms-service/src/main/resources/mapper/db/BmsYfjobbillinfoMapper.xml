<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfjobbillinfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYfjobbillinfo" id="BmsYfjobbillinfoResult">
        <result property="id"    column="id"    />
        <result property="pkId"    column="pk_id"    />
        <result property="relateCode"    column="relate_code"    />
        <result property="schedulingBillCode"    column="scheduling_bill_code"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="companyId"    column="company_id"    />
        <result property="networkCode"    column="network_code"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="totalNumber"    column="total_number"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="cargoValue"    column="cargo_value"    />
        <result property="ifBaseStores"    column="if_base_stores"    />
        <result property="ifSuperBaseKilometer"    column="if_Super_base_kilometer"    />
        <result property="storeDistanceKilometer"    column="store_distance_kilometer"    />
        <result property="storeCode"    column="store_code"    />
        <result property="receivingStore"    column="receiving_store"    />
        <result property="createCode"    column="create_code"    />
        <result property="workCode"    column="workcode"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="nearStoreKm"    column="near_store_km"    />
        <result property="orderProvince"    column="order_province"    />
        <result property="orderCity"    column="order_city"    />
        <result property="orderDistrict"    column="order_district"    />
    </resultMap>

    <sql id="selectBmsYfjobbillinfoVo">
        select id, pk_id, relate_code, scheduling_bill_code, client_code, client_name, company_id, network_code, total_boxes, total_number, total_weight, total_volume, cargo_value, if_base_stores, if_Super_base_kilometer, store_distance_kilometer, store_code, receiving_store, create_code, create_by, create_dept_id, create_time, oper_code, oper_by, oper_dept_id, oper_time, del_flag,near_store_km
        ,order_province,order_city,order_district   from bms_yfjobbillinfo
    </sql>

    <select id="selectBmsYfjobbillinfoList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfjobbillinfo" resultMap="BmsYfjobbillinfoResult">
        <include refid="selectBmsYfjobbillinfoVo"/>
        <where>
            <if test="relateCode != null  and relateCode != ''"> and relate_code = #{relateCode}</if>
            <if test="schedulingBillCode != null  and schedulingBillCode != ''"> and scheduling_bill_code = #{schedulingBillCode}</if>
            <if test="schedulingCodes != null and schedulingCodes.size>0 ">
                AND scheduling_bill_code in
                <foreach collection="schedulingCodes" item="schedulingCodes" open="(" separator="," close=")">
                    #{schedulingCodes}
                </foreach>
            </if>
            <if test="clientCode != null  and clientCode != ''"> and client_code = #{clientCode}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="networkCode != null  and networkCode != ''"> and network_code = #{networkCode}</if>
            <if test="totalBoxes != null "> and total_boxes = #{totalBoxes}</if>
            <if test="totalNumber != null "> and total_number = #{totalNumber}</if>
            <if test="totalWeight != null "> and total_weight = #{totalWeight}</if>
            <if test="totalVolume != null "> and total_volume = #{totalVolume}</if>
            <if test="cargoValue != null "> and cargo_value = #{cargoValue}</if>
            <if test="ifBaseStores != null  and ifBaseStores != ''"> and if_base_stores = #{ifBaseStores}</if>
            <if test="ifSuperBaseKilometer != null  and ifSuperBaseKilometer != ''"> and if_Super_base_kilometer = #{ifSuperBaseKilometer}</if>
            <if test="storeDistanceKilometer != null "> and store_distance_kilometer = #{storeDistanceKilometer}</if>
            <if test="storeCode != null  and storeCode != ''"> and store_code = #{storeCode}</if>
            <if test="receivingStore != null  and receivingStore != ''"> and receiving_store = #{receivingStore}</if>
            <if test="createCode != null  and createCode != ''"> and create_code = #{createCode}</if>
            <if test="createDeptId != null "> and create_dept_id = #{createDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operDeptId != null "> and oper_dept_id = #{operDeptId}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
        </where>
    </select>

    <select id="selectBmsYfjobbillinfoById" parameterType="java.lang.String" resultMap="BmsYfjobbillinfoResult">
        <include refid="selectBmsYfjobbillinfoVo"/>
        where pk_id = #{pkId}
    </select>

    <select id="selectBmsYfjobbillinfoListByCodes" parameterType="java.lang.String" resultMap="BmsYfjobbillinfoResult">
        <include refid="selectBmsYfjobbillinfoVo"/>
        where relate_code in
        <foreach item="code" collection="relateCode" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <insert id="insertBmsYfjobbillinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfjobbillinfo" useGeneratedKeys="true" keyProperty="pkId">
        insert into bms_yfjobbillinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="relateCode != null">relate_code,</if>
            <if test="schedulingBillCode != null">scheduling_bill_code,</if>
            <if test="clientCode != null">client_code,</if>
            <if test="clientName != null">client_name,</if>
            <if test="companyId != null">company_id,</if>
            <if test="networkCode != null">network_code,</if>
            <if test="totalBoxes != null">total_boxes,</if>
            <if test="totalNumber != null">total_number,</if>
            <if test="totalWeight != null">total_weight,</if>
            <if test="totalVolume != null">total_volume,</if>
            <if test="cargoValue != null">cargo_value,</if>
            <if test="ifBaseStores != null">if_base_stores,</if>
            <if test="ifSuperBaseKilometer != null">if_Super_base_kilometer,</if>
            <if test="storeDistanceKilometer != null">store_distance_kilometer,</if>
            <if test="storeCode != null">store_code,</if>
            <if test="receivingStore != null">receiving_store,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="nearStoreKm != null">near_store_km,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="relateCode != null">#{relateCode},</if>
            <if test="schedulingBillCode != null">#{schedulingBillCode},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="networkCode != null">#{networkCode},</if>
            <if test="totalBoxes != null">#{totalBoxes},</if>
            <if test="totalNumber != null">#{totalNumber},</if>
            <if test="totalWeight != null">#{totalWeight},</if>
            <if test="totalVolume != null">#{totalVolume},</if>
            <if test="cargoValue != null">#{cargoValue},</if>
            <if test="ifBaseStores != null">#{ifBaseStores},</if>
            <if test="ifSuperBaseKilometer != null">#{ifSuperBaseKilometer},</if>
            <if test="storeDistanceKilometer != null">#{storeDistanceKilometer},</if>
            <if test="storeCode != null">#{storeCode},</if>
            <if test="receivingStore != null">#{receivingStore},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="nearStoreKm != null">#{nearStoreKm},</if>
        </trim>
    </insert>

    <update id="updateBmsYfjobbillinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfjobbillinfo">
        update bms_yfjobbillinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null and id != ''">id = #{id},</if>
            <if test="relateCode != null">relate_code = #{relateCode},</if>
            <if test="schedulingBillCode != null">scheduling_bill_code = #{schedulingBillCode},</if>
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="networkCode != null">network_code = #{networkCode},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="totalNumber != null">total_number = #{totalNumber},</if>
            <if test="totalWeight != null">total_weight = #{totalWeight},</if>
            <if test="totalVolume != null">total_volume = #{totalVolume},</if>
            <if test="cargoValue != null">cargo_value = #{cargoValue},</if>
            <if test="ifBaseStores != null">if_base_stores = #{ifBaseStores},</if>
            <if test="ifSuperBaseKilometer != null">if_Super_base_kilometer = #{ifSuperBaseKilometer},</if>
            <if test="storeDistanceKilometer != null">store_distance_kilometer = #{storeDistanceKilometer},</if>
            <if test="storeCode != null">store_code = #{storeCode},</if>
            <if test="receivingStore != null">receiving_store = #{receivingStore},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="nearStoreKm != null">near_store_km = #{nearStoreKm},</if>
        </trim>
        where pk_id = #{pkId}
    </update>


    <delete id="deleteBmsYfjobbillinfoById" parameterType="java.lang.String">
        delete from bms_yfjobbillinfo where pk_id = #{pkId}
    </delete>

    <delete id="deleteBmsYfjobbillinfoByIds">
        delete from bms_yfjobbillinfo where pk_id in 
        <foreach item="pkId" collection="array" open="(" separator="," close=")">
            #{pkId}
        </foreach>
    </delete>

    <delete id="deleteBmsYfjobbillinfoByIds2">
        delete from bms_yfjobbillinfo where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYfjobbillinfoStatusByIds">
        update bms_yfjobbillinfo set status = #{status} where pk_id in
        <foreach item="pkId" collection="array" open="(" separator="," close=")">
            #{pkId}
        </foreach>
    </update>

    <select id="selectByCode" resultMap="BmsYfjobbillinfoResult">
        <include refid="selectBmsYfjobbillinfoVo"/>
        where
        <if test="codes != null and codes.size>0 ">
            relate_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <select id="selectBySchedulingBillCode" resultMap="BmsYfjobbillinfoResult">
        select  y.id,  y.pk_id,  y.relate_code,y.relate_code AS workCode ,  y.scheduling_bill_code,  y.client_code,  y.client_name,  y.company_id,  y.network_code,  y.total_boxes,
        y.total_number,  y.total_weight,y.total_volume,  y.cargo_value,  y.if_base_stores,  y.if_Super_base_kilometer,  y.store_distance_kilometer,  y.store_code,
        y.receiving_store,  y.create_code,  y.create_by,y.create_dept_id,  y.create_time,  y.oper_code,  y.oper_by,  y.oper_dept_id,  y.oper_time,  y.del_flag,y.near_store_km
        ,c.id clientId ,y.order_province,y.order_city,y.order_district,y.workcode
        from bms_yfjobbillinfo y left join bms_clientinfo c on y.client_code=c.client_code
        where y.del_flag = '0'
        <if test="codes != null and codes.size>0 ">
            and y.scheduling_bill_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>

    </select>

    <insert id="batchSave" parameterType="java.util.List">
        insert into bms_yfjobbillinfo
        (id, relate_code, scheduling_bill_code, client_code, client_name, company_id, network_code, total_boxes,
        total_number, total_weight, total_volume, cargo_value,if_base_stores, if_Super_base_kilometer, store_distance_kilometer,
        store_code, receiving_store, create_code, create_by, create_dept_id, create_time,
        oper_code, oper_by, oper_dept_id, oper_time, del_flag,near_store_km,order_province,order_city,order_district)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.relateCode},#{item.schedulingBillCode},#{item.clientCode},#{item.clientName},#{item.companyId},#{item.networkCode},#{item.totalBoxes},
            #{item.totalNumber},#{item.totalWeight},#{item.totalVolume},#{item.cargoValue},#{item.ifBaseStores},#{item.ifSuperBaseKilometer},#{item.storeDistanceKilometer},
            #{item.storeCode},#{item.receivingStore},#{item.createCode},#{item.createBy},#{item.createDeptId},#{item.createTime},
            #{item.operCode},#{item.operBy},#{item.operDeptId},#{item.operTime},#{item.delFlag},#{item.nearStoreKm},#{item.orderProvince},#{item.orderCity},#{item.orderDistrict})
        </foreach>
    </insert>
</mapper>