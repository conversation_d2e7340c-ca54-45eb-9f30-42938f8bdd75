<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYsstockGoodsinfoMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYsstockGoodsinfo" id="BmsYsstockGoodsinfoResult">
        <result property="id"    column="id"    />
        <result property="ysstockId"    column="ysstock_id"    />
        <result property="skuCode"    column="sku_code"    />
        <result property="skuName"    column="sku_name"    />
        <result property="temperatureType"    column="temperature_type"    />
        <result property="productDate"    column="product_date"    />
        <result property="expiryDate"    column="expiry_date"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="stockType"    column="stock_type"    />
        <result property="stockQuantity"    column="stock_quantity"    />
        <result property="palletType"    column="pallet_type"    />
        <result property="boxType"    column="box_type"    />
        <result property="boxLength"    column="box_length"    />
        <result property="weightWidth"    column="weight_width"    />
        <result property="weightHeight"    column="weight_height"    />
        <result property="calculatePallet"    column="calculate_Pallet"    />
        <result property="integerNumber"    column="integer_number"    />
        <result property="smallNumber"    column="small_number"    />
        <result property="greaterThan5"    column="greater_than5"    />
        <result property="lessThan5"    column="less_than5"    />
        <result property="totalpallet"    column="totalPallet"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectBmsYsstockGoodsinfoVo">
        select id, ysstock_id, sku_code, sku_name, temperature_type, product_date, expiry_date, batch_code, stock_type, stock_quantity, pallet_type, box_type, box_length, weight_width, weight_height, calculate_Pallet, integer_number, small_number, greater_than5, less_than5, totalPallet, del_flag from bms_ysstock_goodsinfo
    </sql>

    <select id="selectBmsYsstockGoodsinfoList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsstockGoodsinfo" resultMap="BmsYsstockGoodsinfoResult">
        <include refid="selectBmsYsstockGoodsinfoVo"/>
        <where>  
            <if test="ysstockId != null  and ysstockId != ''"> and ysstock_id = #{ysstockId}</if>
            <if test="skuCode != null  and skuCode != ''"> and sku_code = #{skuCode}</if>
            <if test="skuName != null  and skuName != ''"> and sku_name like concat('%', #{skuName}, '%')</if>
            <if test="temperatureType != null  and temperatureType != ''"> and temperature_type = #{temperatureType}</if>
            <if test="productDate != null "> and product_date = #{productDate}</if>
            <if test="expiryDate != null "> and expiry_date = #{expiryDate}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="stockType != null  and stockType != ''"> and stock_type = #{stockType}</if>
            <if test="stockQuantity != null "> and stock_quantity = #{stockQuantity}</if>
            <if test="palletType != null  and palletType != ''"> and pallet_type = #{palletType}</if>
            <if test="boxType != null  and boxType != ''"> and box_type = #{boxType}</if>
            <if test="boxLength != null "> and box_length = #{boxLength}</if>
            <if test="weightWidth != null "> and weight_width = #{weightWidth}</if>
            <if test="weightHeight != null "> and weight_height = #{weightHeight}</if>
            <if test="calculatePallet != null "> and calculate_Pallet = #{calculatePallet}</if>
            <if test="integerNumber != null "> and integer_number = #{integerNumber}</if>
            <if test="smallNumber != null "> and small_number = #{smallNumber}</if>
            <if test="greaterThan5 != null "> and greater_than5 = #{greaterThan5}</if>
            <if test="lessThan5 != null "> and less_than5 = #{lessThan5}</if>
            <if test="totalpallet != null "> and totalPallet = #{totalpallet}</if>
        </where>
    </select>
    
    <select id="selectBmsYsstockGoodsinfoById" parameterType="java.lang.String" resultMap="BmsYsstockGoodsinfoResult">
        <include refid="selectBmsYsstockGoodsinfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBmsYsstockGoodsinfo" parameterType="java.util.Map" useGeneratedKeys="true" keyProperty="id">
        insert into bms_ysstock_goodsinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ysstockId != null">ysstock_id,</if>
            <if test="skuCode != null">sku_code,</if>
            <if test="skuName != null">sku_name,</if>
            <if test="temperatureType != null">temperature_type,</if>
            <if test="productDate != null">product_date,</if>
            <if test="expiryDate != null">expiry_date,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="stockType != null">stock_type,</if>
            <if test="stockQuantity != null">stock_quantity,</if>
            <if test="palletType != null">pallet_type,</if>
            <if test="boxType != null">box_type,</if>
            <if test="boxLength != null">box_length,</if>
            <if test="weightWidth != null">weight_width,</if>
            <if test="weightHeight != null">weight_height,</if>
            <if test="calculatePallet != null">calculate_Pallet,</if>
            <if test="integerNumber != null">integer_number,</if>
            <if test="smallNumber != null">small_number,</if>
            <if test="greaterThan5 != null">greater_than5,</if>
            <if test="lessThan5 != null">less_than5,</if>
            <if test="totalpallet != null">totalPallet,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ysstockId != null">#{ysstockId},</if>
            <if test="skuCode != null">#{skuCode},</if>
            <if test="skuName != null">#{skuName},</if>
            <if test="temperatureType != null">#{temperatureType},</if>
            <if test="productDate != null">#{productDate},</if>
            <if test="expiryDate != null">#{expiryDate},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="stockType != null">#{stockType},</if>
            <if test="stockQuantity != null">#{stockQuantity},</if>
            <if test="palletType != null">#{palletType},</if>
            <if test="boxType != null">#{boxType},</if>
            <if test="boxLength != null">#{boxLength},</if>
            <if test="weightWidth != null">#{weightWidth},</if>
            <if test="weightHeight != null">#{weightHeight},</if>
            <if test="calculatePallet != null">#{calculatePallet},</if>
            <if test="integerNumber != null">#{integerNumber},</if>
            <if test="smallNumber != null">#{smallNumber},</if>
            <if test="greaterThan5 != null">#{greaterThan5},</if>
            <if test="lessThan5 != null">#{lessThan5},</if>
            <if test="totalpallet != null">#{totalpallet},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateBmsYsstockGoodsinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsstockGoodsinfo">
        update bms_ysstock_goodsinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="ysstockId != null">ysstock_id = #{ysstockId},</if>
            <if test="skuCode != null">sku_code = #{skuCode},</if>
            <if test="skuName != null">sku_name = #{skuName},</if>
            <if test="temperatureType != null">temperature_type = #{temperatureType},</if>
            <if test="productDate != null">product_date = #{productDate},</if>
            <if test="expiryDate != null">expiry_date = #{expiryDate},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="stockType != null">stock_type = #{stockType},</if>
            <if test="stockQuantity != null">stock_quantity = #{stockQuantity},</if>
            <if test="palletType != null">pallet_type = #{palletType},</if>
            <if test="boxType != null">box_type = #{boxType},</if>
            <if test="boxLength != null">box_length = #{boxLength},</if>
            <if test="weightWidth != null">weight_width = #{weightWidth},</if>
            <if test="weightHeight != null">weight_height = #{weightHeight},</if>
            <if test="calculatePallet != null">calculate_Pallet = #{calculatePallet},</if>
            <if test="integerNumber != null">integer_number = #{integerNumber},</if>
            <if test="smallNumber != null">small_number = #{smallNumber},</if>
            <if test="greaterThan5 != null">greater_than5 = #{greaterThan5},</if>
            <if test="lessThan5 != null">less_than5 = #{lessThan5},</if>
            <if test="totalpallet != null">totalPallet = #{totalpallet},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYsstockGoodsinfoById" parameterType="java.lang.String">
        delete from bms_ysstock_goodsinfo where id = #{id}
    </delete>

    <delete id="deleteBmsYsstockGoodsinfoByIds">
        delete from bms_ysstock_goodsinfo where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYsstockGoodsinfoStatusByIds">
        update bms_ysstock_goodsinfo set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchSave" parameterType="java.util.List">
        insert into bms_ysstock_goodsinfo
        (ysstock_id, sku_code, sku_name, temperature_type, product_date, expiry_date, batch_code,
         stock_type, stock_quantity, pallet_type, box_type, box_length, weight_width, weight_height,
         calculate_Pallet, integer_number, small_number, greater_than5, less_than5, totalPallet)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.ysstockId},#{item.skuCode},#{item.skuName},#{item.temperatureType},#{item.productDate},
            #{item.expiryDate},#{item.batchCode},#{item.stockType},#{item.stockQuantity},#{item.palletType},
            #{item.boxType},#{item.boxLength},#{item.weightWidth},#{item.weightHeight},#{item.calculatePallet},
            #{item.integerNumber},#{item.smallNumber},#{item.greaterThan5},#{item.lessThan5},#{item.totalpallet})
        </foreach>
    </insert>
</mapper>