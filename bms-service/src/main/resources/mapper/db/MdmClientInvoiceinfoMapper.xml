<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.MdmClientInvoiceinfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.MdmClientInvoiceinfo" id="MdmClientinfoResult">
        <result property="id"    column="id"    />
        <result property="clientid"    column="clientid"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="updateUserCode"    column="update_user_code"    />
        <result property="updateUserName"    column="update_user_name"    />
        <result property="updateUserCompanyId"    column="update_user_company_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="openingBank"    column="opening_bank"    />
        <result property="cardNumber"    column="card_number"    />
        <result property="openingName"    column="opening_name"    />
        <result property="linkPhone"    column="link_phone"    />
        <result property="taxpayerNum"    column="taxpayer_num"    />
        <result property="tacAddress"    column="tac_address"    />
        <result property="email"    column="email"    />

    </resultMap>

    <sql id="selectMdmClientInvoiceinfoVo">
SELECT
	inv.id,
    inv.clientid,
    inv.invoice_type,
    inv.opening_bank,
    inv.card_number,
    inv.link_phone,
    inv.taxpayer_num,
    inv.opening_name,
	inv.update_user_code,
	inv.update_user_name,
	inv.update_user_company_id,
	inv.update_time,
    inv.tac_address,
    inv.del_flag,
    nw.bill_name as billName
FROM
    bms_client_invoiceinfo inv
        left join company_network nw
        on nw.warehouse_code = inv.warehouse_cde

  </sql>

    <select id="selectMdmClientInvoiceinfoByClientId" parameterType="java.lang.String" resultMap="MdmClientinfoResult">
        SELECT inv.id,
            inv.clientid,
            inv.invoice_type,
            inv.opening_bank,
            inv.card_number,
            inv.link_phone,
            inv.taxpayer_num,
            inv.opening_name,
            inv.update_user_code,
            inv.update_user_name,
            inv.update_user_company_id,
            inv.update_time,
            inv.tac_address,
            inv.del_flag,
            nw.bill_name as billName,
            inv.email
        FROM bms_client_invoiceinfo inv
        left join company_network nw on nw.warehouse_code = inv.warehouse_code
        where inv.del_flag = 0
        and inv.clientid = #{clientid}
    </select>

    <select id="selectMdmClientInvoiceinfoBywarehouseCode" parameterType="java.lang.String" resultMap="MdmClientinfoResult">
        SELECT
            inv.id,

            nw.bill_name as billName
        FROM
            mdm_warehouseinfo inv
                left join company_network nw
                          on nw.warehouse_code = inv.warehouse_code
        where nw.warehouse_code = #{warehouseCode}
        <if test="companyId != null">
            and inv.company_id = #{companyId}
        </if>
          and inv.del_flag = 0
    </select>

    <select id="selectMdmClientInvoiceinfoByClientIdlimit" parameterType="java.util.Map" resultMap="MdmClientinfoResult">
        SELECT inv.id,
            inv.clientid,
            inv.invoice_type,
            inv.opening_bank,
            inv.card_number,
            inv.link_phone,
            inv.taxpayer_num,
            inv.opening_name,
            inv.update_user_code,
            inv.update_user_name,
            inv.update_user_company_id,
            inv.update_time,
            inv.tac_address,
            inv.del_flag,
            nw.bill_name AS billName
        FROM bms_client_invoiceinfo inv
        LEFT JOIN company_network nw ON nw.warehouse_code = inv.warehouse_code
        WHERE inv.del_flag = 0
        AND IFNULL(inv.warehouse_code, '') != ''
        AND inv.clientid = #{clientid}
        AND nw.mechanism_id = #{mechanismId}
    </select>

    <insert id="insertMdmClientInvoiceinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmClientInvoiceinfo" useGeneratedKeys="true" keyProperty="id">
        insert into bms_client_invoiceinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clientid != null">clientid,</if>
            <if test="updateUserCode != null">update_user_code,</if>
            <if test="updateUserName != null">update_user_name,</if>
            <if test="updateUserCompanyId != null">update_user_company_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="invoiceType != null">invoice_type,</if>
            <if test="openingBank != null">opening_bank,</if>
            <if test="cardNumber != null">card_number,</if>
            <if test="openingName != null">opening_name,</if>
            <if test="linkPhone != null">link_phone,</if>
            <if test="taxpayerNum != null">taxpayer_num,</if>
            <if test="tacAddress != null">tac_address,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="email != null">email,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clientid != null">#{clientid},</if>
            <if test="updateUserCode != null">#{updateUserCode},</if>
            <if test="updateUserName != null">#{updateUserName},</if>
            <if test="updateUserCompanyId != null">#{updateUserCompanyId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="openingBank != null">#{openingBank},</if>
            <if test="cardNumber != null">#{cardNumber},</if>
            <if test="openingName != null">#{openingName},</if>
            <if test="linkPhone != null">#{linkPhone},</if>
            <if test="taxpayerNum != null">#{taxpayerNum},</if>
            <if test="tacAddress != null">#{tacAddress},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="email != null">#{email},</if>
        </trim>
    </insert>

    <update id="updateMdmClientInvoiceinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmClientInvoiceinfo">
        update bms_client_invoiceinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientid != null">clientid = #{clientid},</if>
            <if test="updateUserCode != null">update_user_code = #{updateUserCode},</if>
            <if test="updateUserName != null">update_user_name = #{updateUserName},</if>
            <if test="updateUserCompanyId != null">update_user_company_id = #{updateUserCompanyId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>
            <if test="openingBank != null">opening_bank = #{openingBank},</if>
            <if test="cardNumber != null">card_number = #{cardNumber},</if>
            <if test="openingName != null">opening_name = #{openingName},</if>
            <if test="linkPhone != null">link_phone = #{linkPhone},</if>
            <if test="taxpayerNum != null">taxpayer_num = #{taxpayerNum},</if>
            <if test="tacAddress != null">tac_address = #{tacAddress},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="email != null">email = #{email},</if>
        </trim>
        where clientid = #{clientid}
    </update>

    <update id="deleteMdmClientInvoiceinfo">
        update bms_client_invoiceinfo set del_flag = '1' where clientid = #{clientid}
    </update>



</mapper>