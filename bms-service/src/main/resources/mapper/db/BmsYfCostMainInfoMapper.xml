<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfCostMainInfoMapper">

    <update id="batchUpdateById" parameterType="java.util.List" >
        <foreach collection="list" index="index" item="item" separator=";" >
            UPDATE bms_yfcost_main_info
            <set>
                <if test="item.pkId !=null " >
                    pk_id=#{item.pkId},
                </if>
                <if test="item.expensesCode !=null " >
                    expenses_code=#{item.expensesCode},
                </if>
                <if test="item.businessType !=null " >
                    business_type=#{item.businessType},
                </if>
                <if test="item.clientId !=null " >
                    client_id=#{item.clientId},
                </if>
                <if test="item.expensesType !=null " >
                    expenses_type=#{item.expensesType},
                </if>
                <if test="item.costDimension !=null " >
                    cost_dimension=#{item.costDimension},
                </if>
                <if test="item.chargeType !=null " >
                    charge_type=#{item.chargeType},
                </if>
                <if test="item.feeFlag !=null " >
                    fee_flag=#{item.feeFlag},
                </if>
                <if test="item.quoteruleId !=null " >
                    quoterule_id=#{item.quoteruleId},
                </if>
                <if test="item.ruleName !=null " >
                    rule_name=#{item.ruleName},
                </if>
                <if test="item.remarks !=null " >
                    remarks=#{item.remarks},
                </if>
                <if test="item.freight !=null " >
                    freight=#{item.freight},
                </if>
                <if test="item.deliveryFee !=null " >
                    delivery_fee=#{item.deliveryFee},
                </if>
                <if test="item.ultrafarFee !=null " >
                    ultrafar_fee=#{item.ultrafarFee},
                </if>
                <if test="item.superframesFee !=null " >
                    superframes_fee=#{item.superframesFee},
                </if>
                <if test="item.excessFee !=null " >
                    excess_fee=#{item.excessFee},
                </if>
                <if test="item.reduceFee !=null " >
                    reduce_fee=#{item.reduceFee},
                </if>
                <if test="item.outboundsortingFee !=null " >
                    outboundsorting_fee=#{item.outboundsortingFee},
                </if>
                <if test="item.shortbargeFee !=null " >
                    shortbarge_fee=#{item.shortbargeFee},
                </if>
                <if test="item.returnFee !=null " >
                    return_fee=#{item.returnFee},
                </if>
                <if test="item.exceptionFee !=null " >
                    exception_fee=#{item.exceptionFee},
                </if>
                <if test="item.adjustFee !=null " >
                    adjust_fee=#{item.adjustFee},
                </if>
                <if test="item.adjustRemark !=null " >
                    adjust_remark=#{item.adjustRemark},
                </if>
                <if test="item.otherCost1 !=null " >
                    other_cost1=#{item.otherCost1},
                </if>
                <if test="item.otherCost2 !=null " >
                    other_cost2=#{item.otherCost2},
                </if>
                <if test="item.otherCost3 !=null " >
                    other_cost3=#{item.otherCost3},
                </if>
                <if test="item.otherCost4 !=null " >
                    other_cost4=#{item.otherCost4},
                </if>
                <if test="item.otherCost5 !=null " >
                    other_cost5=#{item.otherCost5},
                </if>
                <if test="item.otherCost6 !=null " >
                    other_cost6=#{item.otherCost6},
                </if>
                <if test="item.otherCost7 !=null " >
                    other_cost7=#{item.otherCost7},
                </if>
                <if test="item.otherCost8 !=null " >
                    other_cost8=#{item.otherCost8},
                </if>
                <if test="item.otherCost9 !=null " >
                    other_cost9=#{item.otherCost9},
                </if>
                <if test="item.otherCost10 !=null " >
                    other_cost10=#{item.otherCost10},
                </if>
                <if test="item.otherCost11 !=null " >
                    other_cost11=#{item.otherCost11},
                </if>
                <if test="item.otherCost12 !=null " >
                    other_cost12=#{item.otherCost12},
                </if>
                <if test="item.sumFee !=null " >
                    sum_fee=#{item.sumFee},
                </if>
                <if test="item.costAttribute !=null " >
                    cost_attribute=#{item.costAttribute},
                </if>

                <if test="item.operCode !=null " >
                    oper_code=#{item.operCode},
                </if>
                <if test="item.operBy !=null " >
                    oper_by=#{item.operBy},
                </if>
                <if test="item.operTime !=null " >
                    oper_time=#{item.operTime},
                </if>
                <if test="item.settleSetting !=null " >
                    settle_setting=#{item.settleSetting},
                </if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>
</mapper>