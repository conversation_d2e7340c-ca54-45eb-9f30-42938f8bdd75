<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubSettingSheetMapper">

    <select id="queryDetail" resultMap="queryDetailResultMap">
        select pss.id,
        pss.relation_id,
        pss.outgoing_type,
        pss.is_template,
        pss.outgoing_code,
        pss.outgoing_name,
        pss.belong_bill_type_arr,
        pss.business_type,
        pss.enabled,
        pss.show_sort,
        pss.remark,
        pss.del_flag,
        pss.create_code,
        pss.created_by,
        pss.created_time,
        pss.oper_code,
        pss.oper_by,
        pss.oper_time,
        pss.outgoing_tap_name ,
        pss.is_sum_row,
        pss.is_read as pssIsRead,
        pssd.id as detailId,
        pssd.field_code,
        pssd.field_name,
        pssd.field_type,
        pssd.is_sum,
        pssd.is_read,
        pssd.is_hide,
        pssd.is_merge,
        pssd.is_edit,
        pssd.show_sort as detail_show_sort,
        pssd.remark as detail_remark,
        pssd.enabled as detail_enabled,
        pssd.belong_outgoing_code
        from pub_setting_sheet pss
        left join pub_setting_sheet_detail pssd on pss.id = pssd.belong_id and pssd.del_flag=0
        where pss.is_template=1
        and pss.del_flag = 0
        <if test="relationId !=null">
            or (pss.is_template=0
            and pss.del_flag = 0
            and pss.relation_id = #{relationId})
        </if>
        order by pss.show_sort, pssd.show_sort asc;
    </select>

    <update id="deleteSheetSettingByRelationId" parameterType="java.lang.Long">
        update pub_setting_sheet pss left join pub_setting_sheet_detail pssd
            on pssd.belong_id = pss.id
        set pss.del_flag=1,
            pssd.del_flag=1
        where pss.relation_id = #{relationId}
          and pss.is_template = 0
          and pss.del_flag = 0
    </update>

    <insert id="batchIncreaseSheetSetting">
        INSERT INTO pub_setting_sheet ( id, relation_id, outgoing_type, outgoing_code, outgoing_name,
        belong_bill_type_arr, business_type, enabled,show_sort, remark, create_code, created_by,created_time, oper_code, oper_by, oper_time,is_read,outgoing_tap_name)
        VALUES
        <foreach collection="sheetModel" item="item" separator=",">
            (#{item.id}, #{item.relationId},#{item.outgoingType},#{item.outgoingCode},
            #{item.outgoingName},#{item.belongBillTypeArr},#{item.businessType},
            #{item.enabled},#{item.showSort},#{item.remark},#{item.createCode},#{item.createdBy}, #{item.createdTime},#{item.operCode},#{item.operBy},
            #{item.operTime},#{item.isRead},#{item.outgoingTapName})
        </foreach>;
    </insert>

    <insert id="batchIncreaseSheetDetailSetting">
        INSERT INTO pub_setting_sheet_detail (belong_id,field_code, field_name,field_type, is_sum, is_read,is_hide, is_merge, enabled,show_sort,
        remark,create_code, created_by,created_time, oper_code,oper_by, oper_time,belong_outgoing_code)
        VALUES
        <foreach collection="details" item="item" separator=",">
            (#{item.belongId},#{item.fieldCode},#{item.fieldName},
            #{item.fieldType},#{item.isSum},#{item.isRead},#{item.isHide},#{item.isMerge},#{item.enabled}, #{item.showSort},
            #{item.remark},#{item.createCode},#{item.createdBy}, #{item.createdTime},#{item.operCode},#{item.operBy},
            #{item.operTime},#{item.belongOutgoingCode})
        </foreach>;
    </insert>

    <resultMap id="queryDetailResultMap" type="com.bbyb.joy.bms.domain.dto.sheet.PubSettingSheetModel">
        <id column="id" property="id"/>
        <result column="relation_id" property="relationId"/>
        <result column="outgoing_type" property="outgoingType"/>
        <result column="outgoing_code" property="outgoingCode"/>
        <result column="is_template" property="isTemplate"/>
        <result column="outgoing_name" property="outgoingName"/>
        <result column="is_sum_row" property="isSumRow"/>
        <result column="outgoing_tap_name" property="outgoingTapName"/>
        <result column="belong_bill_type_arr" property="belongBillTypeArr"/>
        <result column="business_type" property="businessType"/>
        <result column="enabled" property="enabled"/>
        <result column="show_sort" property="showSort"/>
        <result column="pssIsRead" property="isRead"/>
        <result column="remark" property="remark"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_code" property="createCode"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="oper_code" property="operCode"/>
        <result column="oper_by" property="operBy"/>
        <result column="oper_time" property="operTime"/>
        <collection property="details" ofType="com.bbyb.joy.bms.domain.dto.sheet.PubSettingSheetDetailModel">
            <id property="id" column="detailId"/>
            <result property="fieldCode" column="field_code"/>
            <result property="fieldName" column="field_name"/>
            <result column="is_sum" property="isSum"/>
            <result column="field_type" property="fieldType"/>
            <result column="is_read" property="isRead"/>
            <result column="is_hide" property="isHide"/>
            <result column="is_merge" property="isMerge"/>
            <result column="is_edit" property="isEdit"/>
            <result column="detail_enabled" property="enabled"/>
            <result column="detail_show_sort" property="showSort"/>
            <result column="detail_remark" property="remark"/>
            <result column="belong_outgoing_code" property="belongOutgoingCode"/>
        </collection>
    </resultMap>


    <sql id="pub_setting_sheet_Column">
        id, relation_id, outgoing_type, outgoing_code, outgoing_name, belong_bill_type_arr, business_type, enabled, show_sort, remark, del_flag, create_code, created_by, created_time, oper_code, oper_by, oper_time
    </sql>
</mapper>