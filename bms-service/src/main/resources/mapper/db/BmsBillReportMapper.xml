<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsBillReportMapper">

    <select id="selectBmsYsBillReportList" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsYsBillReportDto">
        select
        ys.id id,
        ys.bill_code billCode,
        ys.bill_Name billName,
        ys.bill_type billType,
        ys.company_id companyId,
        ys.bill_date billDate,
        c.client_code clientCode,
        c.client_name clientName,
        c.client_type clientType,
        c.brand_name brandName,
        c.region region,
        CAST(ys.ys_amount as DECIMAL(18,2)) AS ysAmount,
        ys.bill_state billState,
        ys.bill_remark billRemark,
        ys.audit_time auditTime,
        ys.audit_state auditState,
        ys.audit_remark auditRemark,
        ys.submit_status submitStatus,
        ys.submit_date submitDate,
        ys.ticket_state ticketState,
        ys.ticket_apply_time ticketApplyTime,
        ys.ticket_time ticketTime,
        CAST(ys.ticket_amount as DECIMAL(18,2)) AS ticketAmount,
        ys.ticket_num ticketNum,
        ys.hx_state hxState,
        ys.hx_time hxTime,
        CAST(ys.hx_amount as DECIMAL(18,2)) AS hxAmount,
        ys.hx_num hxNum,
        CAST((IFNULL(ys.adjusted_amount,0) - IFNULL(ys.hx_amount,0)) as DECIMAL(18,2)) AS noHxAmount,
        c.collection_days collectionDays,
        case when c.invoice_mode = 1 then round(ys.ys_amount * c.freight_ratio,2) else CAST(0 as DECIMAL(18,2)) end freight,
        case when c.invoice_mode = 1 then round(ys.ys_amount * c.storage_ratio,2) else CAST(0 as DECIMAL(18,2)) end storage,
        case when c.invoice_mode = 1 then round(ys.ys_amount * c.message_ratio,2) else CAST(0 as DECIMAL(18,2)) end message,
        case when c.invoice_mode = 1 then round(ys.ys_amount * c.platform_ratio,2) else CAST(0 as DECIMAL(18,2)) end platform,
        case when c.invoice_mode = 1 then round(ys.ys_amount * c.brand_ratio,2) else CAST(0 as DECIMAL(18,2)) end brand,
        case when c.invoice_mode = 1 then round(ys.ys_amount * c.delivery_ratio,2) else CAST(0 as DECIMAL(18,2)) end delivery
        from bms_ysbillmain ys
        left join bms_clientinfo c on ys.client_id = c.id and c.del_flag = 0
        where ys.del_flag = '0'
        <if test="createTimeStart != null and createTimeStart != '' and createTimeEnd != null and createTimeEnd!=''">
            AND ys.create_time >= #{createTimeStart,jdbcType=VARCHAR} AND ys.create_time &lt;= #{createTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size > 0 ">
            AND ys.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="billCode != null and billCode != ''">
            AND ys.bill_code like concat('%',trim(#{billCode}),'%')
        </if>
        <if test="submitStatus != null ">
            AND ys.submit_status = #{submitStatus}
        </if>
        <if test="auditState != null ">
            AND ys.audit_state = #{auditState}
        </if>
        <if test="groupingLatitude == 1 ">
            AND ys.fatherid is not null
        </if>
        <if test="groupingLatitude == 2 ">
            AND ys.fatherid is  null
        </if>
        <if test="ticketState != null ">
            AND ys.ticket_state = #{ticketState}
        </if>
        <if test="hxState != null ">
            AND ys.hx_state = #{hxState}
        </if>
        <if test="billMarking != null and billMarking != ''">
            AND ys.bill_marking = #{billMarking}
        </if>
        <if test="billState != null ">
            AND ys.bill_state = #{billState}
        </if>

        order by ys.create_time desc
    </select>


    <select id="selectBmsYfBillReportList" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsYfBillReportDto">
        select
        yf.id id,
        yf.bill_code billCode,
        yf.bill_Name billName,
        yf.bill_type billType,
        yf.company_id companyId,
        yf.bill_date billDate,
        yf.bill_state billState,
        ci.carrier_name carrierName,
        yf.votes votes,
        CAST(ifnull(yf.bill_amount,0) as DECIMAL(18,2)) AS billAmount,
        CAST(ifnull(yf.adjusted_amount,0) as DECIMAL(18,2)) AS adjustedAmount,
        CAST(ifnull(yf.yf_amount,0) as DECIMAL(18,2)) AS yfAmount,
        yf.audit_state auditState,
        yf.audit_user_name auditUserName,
        yf.audit_time auditTime,
        yf.audit_remark auditRemark,
        yf.submit_status submitStatus,
        yf.submit_date submitDate,
        yf.ticket_state ticketState,
        yf.ticket_time ticketTime,
        CAST(ifnull(yf.ticket_amount,0) as DECIMAL(18,2)) AS ticketAmount,
        yf.hx_state hxState,
        yf.hx_time hxTime,
        CAST(yf.hx_amount as DECIMAL(18,2)) AS hxAmount,
        yf.hx_num hxNum,
        CAST((IFNULL(yf.adjusted_amount,0) - IFNULL(yf.hx_amount,0)) as DECIMAL(18,2)) AS noHxAmount,
        c.client_name clientName
        from bms_yfbillmain yf
        left join bms_carrierinfo ci on yf.carrier_id = ci.id and ci.del_flag = '0'
        LEFT JOIN bms_yfcost_info ysi ON ysi.bill_id = yf.id AND ysi.del_flag = '0'
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where yf.del_flag = '0'
        <if test="createTimeStart != null and createTimeStart != '' and createTimeEnd != null and createTimeEnd!=''">
            AND yf.create_time >= #{createTimeStart,jdbcType=VARCHAR} AND yf.create_time &lt;= #{createTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size > 0 ">
            AND yf.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="carrierList != null">
            and  ci.carrier_code in
            <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                #{carrier}
            </foreach>
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND ci.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="billCode != null and billCode != ''">
            AND yf.bill_code like concat('%',trim(#{billCode}),'%')
        </if>
        <if test="submitStatus != null and submitStatus != ''">
            AND yf.submit_status = #{submitStatus}
        </if>
        <if test="auditState != null and auditState != ''">
            AND yf.audit_state = #{auditState}
        </if>
        <if test="groupingLatitude == 1 ">
            AND yf.fatherid is not null
        </if>
        <if test="groupingLatitude == 2 ">
            AND yf.fatherid  is null
        </if>
        <if test="ticketState != null and ticketState != ''">
            AND yf.ticket_state = #{ticketState}
        </if>
        <if test="hxState != null and hxState != ''">
            AND yf.hx_state = #{hxState}
        </if>
        <if test="billMarking != null and billMarking != ''">
            AND yf.bill_marking = #{billMarking}
        </if>
        <if test="billState != null and billState != ''">
            AND yf.bill_state = #{billState}
        </if>
        group by yf.id
        order by yf.create_time desc
    </select>

    <select id="selectBmsYsHkReportList" parameterType="java.util.Map"
            resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsYsHKReportDto">
        SELECT
        A.clientType,
        sum(A.ysAmount) receivableAmount,
        sum(A.hx_amount) receivedAmount,
        sum(A.ysAmount) - sum(A.hx_amount) notBackAmount,
        case when sum(A.ysAmount30) = 0 then 0.00
        else CONVERT(sum(hx_amount30) / sum(A.ysAmount30) * 100,DECIMAL(18,2)) end returnRateMonth,
        case when sum(ysAmount) = 0 then 0.00
        else CONVERT(sum(hx_amount) / sum(ysAmount) * 100,DECIMAL(18,2)) end returnRate,
        (sum(A.ysAmount30) - sum(A.hx_amount30)) notBackAmount30,
        (sum(A.ysAmount60) - sum(A.hx_amount60)) notBackAmount60,
        (sum(A.ysAmount90) - sum(A.hx_amount90)) notBackAmount90,
        (sum(A.ysAmount120) - sum(A.hx_amount120)) notBackAmount120,
        (sum(A.ysAmount150) - sum(A.hx_amount150)) notBackAmount150,
        (sum(A.ysAmount180) - sum(A.hx_amount180)) notBackAmount180
        FROM (
        SELECT
        ys.create_time,
        cli.client_type clientType,
        ys.ys_amount ysAmount,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate())
        and DATE_FORMAT(ys.create_time,'%Y-%m-%d') = date_add(curdate(), interval - day(curdate()) + 1 day)
        then ifnull(ys.ys_amount,0) else case when DATE_FORMAT(ys.create_time,'%Y-%m-%d') >= DATE_FORMAT(NOW()-INTERVAL
        30 DAY,'%Y-%m-%d') then ifnull(ys.ys_amount,0) else 0 end end ysAmount30,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate()) and DATE_FORMAT(ys.create_time,'%Y-%m-%d') =
        date_add(curdate()-day(curdate())+1,interval -1 month) then ifnull(ys.ys_amount,0) else case when
        DATE_FORMAT(ys.create_time,'%Y-%m-%d') between DATE_FORMAT(NOW()-INTERVAL 60 DAY,'%Y-%m-%d') and
        DATE_FORMAT(NOW()-INTERVAL 30 DAY,'%Y-%m-%d') then ifnull(ys.ys_amount,0) else 0 end end ysAmount60,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate()) and DATE_FORMAT(ys.create_time,'%Y-%m-%d') =
        date_add(curdate()-day(curdate())+1,interval -2 month) then ifnull(ys.ys_amount,0) else case when
        DATE_FORMAT(ys.create_time,'%Y-%m-%d') between DATE_FORMAT(NOW()-INTERVAL 90 DAY,'%Y-%m-%d') and
        DATE_FORMAT(NOW()-INTERVAL 60 DAY,'%Y-%m-%d') then ifnull(ys.ys_amount,0) else 0 end end ysAmount90,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate()) and DATE_FORMAT(ys.create_time,'%Y-%m-%d') =
        date_add(curdate()-day(curdate())+1,interval -3 month) then ifnull(ys.ys_amount,0) else case when
        DATE_FORMAT(ys.create_time,'%Y-%m-%d') between DATE_FORMAT(NOW()-INTERVAL 120 DAY,'%Y-%m-%d') and
        DATE_FORMAT(NOW()-INTERVAL 90 DAY,'%Y-%m-%d') then ifnull(ys.ys_amount,0) else 0 end end ysAmount120,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate()) and DATE_FORMAT(ys.create_time,'%Y-%m-%d') =
        date_add(curdate()-day(curdate())+1,interval -4 month) then ifnull(ys.ys_amount,0) else case when
        DATE_FORMAT(ys.create_time,'%Y-%m-%d') between DATE_FORMAT(NOW()-INTERVAL 150 DAY,'%Y-%m-%d') and
        DATE_FORMAT(NOW()-INTERVAL 120 DAY,'%Y-%m-%d') then ifnull(ys.ys_amount,0) else 0 end end ysAmount150,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate()) and DATE_FORMAT(ys.create_time,'%Y-%m-%d') =
        date_add(curdate()-day(curdate())+1,interval -5 month) then ifnull(ys.ys_amount,0) else case when
        DATE_FORMAT(ys.create_time,'%Y-%m-%d') between DATE_FORMAT(NOW()-INTERVAL 180 DAY,'%Y-%m-%d') and
        DATE_FORMAT(NOW()-INTERVAL 150 DAY,'%Y-%m-%d') then ifnull(ys.ys_amount,0) else 0 end end ysAmount180,
        0 hx_amount,
        0 hx_amount30,
        0 hx_amount60,
        0 hx_amount90,
        0 hx_amount120,
        0 hx_amount150,
        0 hx_amount180
        from bms_ysbillmain ys
        left join bms_clientinfo cli on ys.client_id = cli.id and cli.del_flag ='0'
        where ys.del_flag = '0'
        <if test="createTimeStart != null and createTimeStart != '' and createTimeEnd != null and createTimeEnd!=''">
            AND ys.create_time >= #{createTimeStart,jdbcType=VARCHAR} AND ys.create_time &lt;=
            #{createTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size > 0 ">
            AND ys.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="clientList != null">
            and cli.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientType != null ">
            AND cli.client_type = #{clientType}
        </if>

        UNION all

        SELECT
        y.create_time,
        cli.client_type clientType,
        0 ysAmount,
        0 ysAmount30,
        0 ysAmount60,
        0 ysAmount90,
        0 ysAmount120,
        0 ysAmount150,
        0 ysAmount180,
        ys.payment hx_amount,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate())
        and DATE_FORMAT(ys.collection_time,'%Y-%m-%d') = date_add(curdate(), interval - day(curdate()) + 1 day)
        then ifnull(ys.payment,0) else case when DATE_FORMAT(ys.collection_time,'%Y-%m-%d') >=
        DATE_FORMAT(NOW()-INTERVAL 30 DAY,'%Y-%m-%d') then ifnull(ys.payment,0) else 0 end end hx_amount30,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate())
        and DATE_FORMAT(ys.collection_time,'%Y-%m-%d') = date_add(curdate()-day(curdate())+1,interval -1 month)
        then ifnull(ys.payment,0) else case when DATE_FORMAT(ys.collection_time,'%Y-%m-%d') between
        DATE_FORMAT(NOW()-INTERVAL 60 DAY,'%Y-%m-%d') and DATE_FORMAT(NOW()-INTERVAL 30 DAY,'%Y-%m-%d') then
        ifnull(ys.payment,0) else 0 end end hx_amount60,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate())
        and DATE_FORMAT(ys.collection_time,'%Y-%m-%d') = date_add(curdate()-day(curdate())+1,interval -2 month)
        then ifnull(ys.payment,0) else case when DATE_FORMAT(ys.collection_time,'%Y-%m-%d') between
        DATE_FORMAT(NOW()-INTERVAL 90 DAY,'%Y-%m-%d') and DATE_FORMAT(NOW()-INTERVAL 60 DAY,'%Y-%m-%d') then
        ifnull(ys.payment,0) else 0 end end hx_amount90,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate())
        and DATE_FORMAT(ys.collection_time,'%Y-%m-%d') = date_add(curdate()-day(curdate())+1,interval -3 month)
        then ifnull(ys.payment,0) else case when DATE_FORMAT(ys.collection_time,'%Y-%m-%d') between
        DATE_FORMAT(NOW()-INTERVAL 120 DAY,'%Y-%m-%d') and DATE_FORMAT(NOW()-INTERVAL 90 DAY,'%Y-%m-%d') then
        ifnull(ys.payment,0) else 0 end end hx_amount120,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate())
        and DATE_FORMAT(ys.collection_time,'%Y-%m-%d') = date_add(curdate()-day(curdate())+1,interval -4 month)
        then ifnull(ys.payment,0) else case when DATE_FORMAT(ys.collection_time,'%Y-%m-%d') between
        DATE_FORMAT(NOW()-INTERVAL 150 DAY,'%Y-%m-%d') and DATE_FORMAT(NOW()-INTERVAL 120 DAY,'%Y-%m-%d') then
        ifnull(ys.payment,0) else 0 end end hx_amount150,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate())
        and DATE_FORMAT(ys.collection_time,'%Y-%m-%d') = date_add(curdate()-day(curdate())+1,interval -5 month)
        then ifnull(ys.payment,0) else case when DATE_FORMAT(ys.collection_time,'%Y-%m-%d') between
        DATE_FORMAT(NOW()-INTERVAL 180 DAY,'%Y-%m-%d') and DATE_FORMAT(NOW()-INTERVAL 150 DAY,'%Y-%m-%d') then
        ifnull(ys.payment,0) else 0 end end hx_amount180
        from bms_yshx_record_middle ys
        left join bms_ysbill_hxinfo ysh on ys.hx_id = ysh.id and ysh.del_flag=0
        left join bms_ysbillmain y on ysh.bill_codes like CONCAT('%',y.bill_code,'%') and y.del_flag=0
        left join bms_clientinfo cli on y.client_id = cli.id and cli.del_flag ='0'
        where ys.del_flag = '0'
        <if test="createTimeStart != null and createTimeStart != '' and createTimeEnd != null and createTimeEnd!=''">
            AND y.create_time >= #{createTimeStart,jdbcType=VARCHAR} AND y.create_time &lt;=
            #{createTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size > 0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="clientList != null">
            and cli.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientType != null ">
            AND cli.client_type = #{clientType}
        </if>
        GROUP BY ys.id
        ) A WHERE 1 = 1 GROUP BY A.clientType
        order by A.create_time desc
    </select>

    <select id="selectBmSysHkDetailReportList" parameterType="java.util.Map"
            resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsYsHKReportDto">
        SELECT
        A.ar_clerk arClerk,
        A.brand_name brandName,
        A.client_code clientCode,
        A.client_name clientName,
        (sum(A.ysAmount30) - sum(A.hx_amount30)) notBackAmount30
        FROM (
        select
        ys.create_time,
        cli.id clientID,
        ys.ar_clerk,
        cli.brand_name,
        cli.client_code,
        cli.client_name,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate()) and DATE_FORMAT(ys.create_time,'%Y-%m-%d') =
        date_add(curdate(), interval - day(curdate()) + 1 day) then ifnull(ys.ys_amount,0) else case when
        DATE_FORMAT(ys.create_time,'%Y-%m-%d') >= DATE_FORMAT(NOW()-INTERVAL 30 DAY,'%Y-%m-%d') then
        ifnull(ys.ys_amount,0) else 0 end end ysAmount30,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate()) and DATE_FORMAT(ys.hx_amount,'%Y-%m-%d') =
        date_add(curdate(), interval - day(curdate()) + 1 day) then ifnull(ys.hx_amount,0) else case when
        DATE_FORMAT(ys.create_time,'%Y-%m-%d') >= DATE_FORMAT(NOW()-INTERVAL 30 DAY,'%Y-%m-%d') then
        ifnull(ys.hx_amount,0) else 0 end end hx_amount30
        from bms_ysbillmain ys
        left join bms_clientinfo cli on ys.client_id = cli.id and cli.del_flag ='0'
        where ys.del_flag = '0'
        <if test="createTimeStart != null and createTimeStart != '' and createTimeEnd != null and createTimeEnd!=''">
            AND ys.create_time >= #{createTimeStart,jdbcType=VARCHAR} AND ys.create_time &lt;=
            #{createTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size > 0 ">
            AND ys.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="clientType != null  ">
            AND cli.client_type = #{clientType}
        </if>
        <if test="clientList != null">
            and cli.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientCode != null and clientCode != ''">
            AND cli.client_code like concat('%',trim(#{clientCode}),'%')
        </if>
        <if test="clientName != null and clientName != ''">
            AND cli.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="brandName != null and brandName != ''">
            AND cli.brand_name like concat('%',trim(#{brandName}),'%')
        </if>

        UNION all

        select
        y.create_time,
        cli.id clientID,
        y.ar_clerk,
        cli.brand_name,
        cli.client_code,
        cli.client_name,
        0 ysAmount30,
        case when DATE_FORMAT(NOW(),'%Y-%m-%d') = last_day(curdate())
        and DATE_FORMAT(ys.collection_time,'%Y-%m-%d') = date_add(curdate(), interval - day(curdate()) + 1 day)
        then ifnull(ys.payment,0) else case when DATE_FORMAT(ys.collection_time,'%Y-%m-%d') >=
        DATE_FORMAT(NOW()-INTERVAL 30 DAY,'%Y-%m-%d') then ifnull(ys.payment,0) else 0 end end hx_amount30
        from bms_yshx_record_middle ys
        left join bms_ysbill_hxinfo ysh on ys.hx_id = ysh.id and ysh.del_flag=0
        left join bms_ysbillmain y on ysh.bill_codes like CONCAT('%',y.bill_code,'%') and y.del_flag=0
        left join bms_clientinfo cli on y.client_id = cli.id and cli.del_flag ='0'
        where ys.del_flag = '0'
        <if test="createTimeStart != null and createTimeStart != '' and createTimeEnd != null and createTimeEnd!=''">
            AND y.create_time >= #{createTimeStart,jdbcType=VARCHAR} AND y.create_time &lt;=
            #{createTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size > 0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="clientType != null  ">
            AND cli.client_type = #{clientType}
        </if>
        <if test="clientList != null">
            and cli.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientCode != null and clientCode != ''">
            AND cli.client_code like concat('%',trim(#{clientCode}),'%')
        </if>
        <if test="clientName != null and clientName != ''">
            AND cli.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="brandName != null and brandName != ''">
            AND cli.brand_name like concat('%',trim(#{brandName}),'%')
        </if>
        GROUP BY ys.id
        ) A GROUP BY A.clientID
        order by A.create_time desc

    </select>

    <select id="selectBmsInvoiceReportList" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsInvoiceReportDto">
        SELECT * from
        (
        SELECT
        ysi.companyid companyId,
        1 invoiceMode,
        ysi.invoice_time invoiceTime,
        DATE_FORMAT(ysi.invoice_time,'%Y-%m-%d %H:%i:%s') invoiceTimeDt,
        ysi.invoicecode invoiceCode,
        ysi.opening_name openingName,
        ysi.invoice_type invoiceType,
        ysi.invoicefee invoiceFee,
        ifnull(ysi.invoicefee,0) - ifnull(ysi.tax_fee,0) incomeInvoiceFee,
        ifnull(ysi.tax_rate,0)  taxRate,
        ysi.tax_fee taxFee,
        ys.bill_code billCode,
        ysi.express_code expressCode,
        (case when DATEDIFF(ys.hx_time,ysi.invoice_time) &lt; 0 then -DATEDIFF(ys.hx_time,ysi.invoice_time)
        else DATEDIFF(ys.hx_time,ysi.invoice_time) end)  receivableDays,
        ysi.remark invoiceRemark,
        ysi.create_code creatCode,
        ysi.create_by createBy,
        DATE_FORMAT(ysi.oper_time,'%Y-%m-%d %H:%i:%s') operTimeDt,
        ysi.oper_time operTime
        from bms_ysbillinvoice ysi
        left join bms_ysbillmain ys on ysi.billid = ys.id and ys.del_flag = '0'
        left join bms_clientinfo cli on ys.client_id = cli.id and cli.del_flag ='0'
        where ysi.del_flag = '0'  and ysi.invoice_state = '0'
        <if test="clientList != null">
            and  cli.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="invoiceTimeStart != null and invoiceTimeStart != '' and invoiceTimeEnd != null and invoiceTimeEnd!=''">
            AND ysi.invoice_time >= #{invoiceTimeStart,jdbcType=VARCHAR} AND ysi.invoice_time &lt;= #{invoiceTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size > 0 ">
            AND ysi.companyid in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="openingName != null and openingName != ''">
            AND ysi.opening_name like concat('%',trim(#{openingName}),'%')
        </if>
        <if test="invoiceCode != null and invoiceCode != ''">
            AND ysi.invoicecode like concat('%',trim(#{invoiceCode}),'%')
        </if>
        <if test="invoiceType != null  ">
            AND ysi.invoice_type = #{invoiceType}
        </if>
        <if test="billCode != null and billCode != ''">
            AND ys.bill_code like concat('%',trim(#{billCode}),'%')
        </if>
        <if test="invoiceMode != null and invoiceMode != ''">
            AND '1' = #{invoiceMode}
        </if>
        <if test="createBy != null and createBy != ''">
            AND ysi.create_by = #{createBy}
        </if>
        UNION all
        SELECT
        yf.company_id companyId,
        2 invoiceMode,
        yfi.collection_time invoiceTime,
        DATE_FORMAT(yfi.collection_time,'%Y-%m-%d %H:%i:%s') invoiceTimeDt,
        '' invoiceCode,
        c.invoiceheader openingName,
        yfi.invoice_type invoiceType,
        yfi.amount invoiceFee,
        CONVERT((ifnull(yfi.amount,0) - ifnull(yfi.amount,0)/(1 +ifnull(c.taxrate,0)/100)*(ifnull(c.taxrate,0)/100)),DECIMAL(18,2)) incomeInvoiceFee,
        ifnull(c.taxrate,0) taxRate,
        CONVERT((ifnull(yfi.amount,0)/(1 +ifnull(c.taxrate,0)/100)*(ifnull(c.taxrate,0)/100)),DECIMAL(18,2))  taxFee,
        yf.bill_code billCode,
        c.monthly_code expressCode,
        (case when DATEDIFF(yf.hx_time,yfi.collection_time) &lt; 0 then -DATEDIFF(yf.hx_time,yfi.collection_time)
        else DATEDIFF(yf.hx_time,yfi.collection_time) end)  receivableDays,
        yfi.remark invoiceRemark,
        yfi.create_code creatCode,
        yfi.create_by createBy,
        DATE_FORMAT(yfi.oper_time,'%Y-%m-%d %H:%i:%s') operTimeDt,
        yfi.oper_time operTime
        from bms_invoice_record yfi
        left join bms_yfbillmain yf on yfi.collection_code = yf.bill_code and yf.del_flag = '0'
        left join bms_carrierinfo c on yfi.carrier_id = c.id and c.del_flag = '0'
        where yfi.del_flag = '0'
        <if test="carrierList != null">
            and  c.carrier_code in
            <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                #{carrier}
            </foreach>
        </if>
        <if test="invoiceTimeStart != null and invoiceTimeStart != '' and invoiceTimeEnd != null and invoiceTimeEnd!=''">
            AND yfi.collection_time >= #{invoiceTimeStart,jdbcType=VARCHAR} AND yfi.collection_time &lt;= #{invoiceTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size > 0 ">
            AND yf.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="openingName != null and openingName != ''">
            AND c.invoiceheader like concat('%',trim(#{openingName}),'%')
        </if>
        <if test="invoiceCode != null and invoiceCode != ''">
            AND '' = #{invoiceCode}
        </if>
        <if test="invoiceType != null  ">
            AND yfi.invoice_type = #{invoiceType}
        </if>
        <if test="billCode != null and billCode != ''">
            AND yf.bill_code like concat('%',trim(#{billCode}),'%')
        </if>
        <if test="invoiceMode != null and invoiceMode != ''">
            AND '2' = #{invoiceMode}
        </if>
        <if test="createBy != null and createBy != ''">
            AND yfi.create_by = #{createBy}
        </if>
        ) A WHERE 1=1
        order by A.invoiceTime desc
    </select>


    <select id="selectBmsInvoiceReportListByYs" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsInvoiceReportDto">
        SELECT
        ysi.companyid companyId,
        1 invoiceMode,
        ysi.invoice_time invoiceTime,
        DATE_FORMAT(ysi.invoice_time,'%Y-%m-%d %H:%i:%s') invoiceTimeDt,
        ysi.invoicecode invoiceCode,
        ysi.opening_name openingName,
        ysi.invoice_type invoiceType,
        ysi.taxpayer_num taxpayerNum,
        ysi.invoicefee invoiceFee,
        ysi.invoice_project invoiceProject, /** 开票项目 */
        ifnull(ysi.invoicefee,0) - ifnull(ysi.tax_fee,0) incomeInvoiceFee,
        ifnull(ysi.tax_rate,0)  taxRate,
        ysi.tax_fee taxFee,
        ysi.invoice_income invoiceIncome,
        ysi.remark remark,
        ys.bill_code billCode,
        ys.id as billId,
        ysi.express_code expressCode,
        (case when DATEDIFF(ys.hx_time,ysi.invoice_time) &lt; 0 then -DATEDIFF(ys.hx_time,ysi.invoice_time)
        else DATEDIFF(ys.hx_time,ysi.invoice_time) end)  receivableDays,
        ysi.remark invoiceRemark,
        ysi.create_code creatCode,
        ysi.create_by createBy,
        DATE_FORMAT(ysi.oper_time,'%Y-%m-%d %H:%i:%s') operTimeDt,
        ysi.oper_time operTime,
        cli.client_code clientCode,/** 客户编码 */
        cli.client_name clientName, /** 客户名称 */
        ys.ticket_num ticketNum,  /** 开票次数 */
        ysap.transtool_type as transtoolType,
        ysap.car_no as carNo,
        ysap.place_dispatch as placeDispatch,
        ysap.place_destination as placeDestination,
        ysap.trans_goods_name as transGoodsName
        from bms_ysbillinvoice ysi
        left join bms_ysbillmain ys on ysi.billid = ys.id and ys.del_flag = '0'
        left join bms_clientinfo cli on ys.client_id = cli.id and cli.del_flag ='0'
        left join (
        SELECT
        t1.billid,
        t2.bill_code,
        t1.invoice_project,
        t1.invoice_type,
        t1.transtool_type,
        t1.car_no,
        t1.place_dispatch,
        t1.place_destination,
        t1.trans_goods_name
        FROM bms_ysinvoice_apply t1
        left join bms_ysbillmain t2 on t1.billid = t2.id and t2.del_flag = '0'
        WHERE t1.del_flag = 0
        <if test="openingName != null and openingName != ''">
            AND t1.opening_name like concat('%',trim(#{openingName}),'%')
        </if>
        <if test="invoiceType != null  ">
            AND t1.invoice_type = #{invoiceType}
        </if>
        <if test="invoiceProject != null and invoiceProject!='' ">
            AND t1.invoice_project = #{invoiceProject}
        </if>
        <if test="billCode != null and billCode != ''">
            AND t2.bill_code like concat('%',trim(#{billCode}),'%')
        </if>
        GROUP BY t1.billid,t1.invoice_project,t1.transtool_type
        ) ysap ON ysap.billid = ysi.billid AND ysap.invoice_project=ysi.invoice_project AND ysi.invoice_type = ysap.invoice_type
        where ysi.del_flag = '0'  and ysi.invoice_state = '0'
        <if test="clientList != null">
            and  cli.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="invoiceTimeStart != null and invoiceTimeStart != '' and invoiceTimeEnd != null and invoiceTimeEnd!=''">
            AND ysi.invoice_time >= #{invoiceTimeStart,jdbcType=VARCHAR} AND ysi.invoice_time &lt;= #{invoiceTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size > 0 ">
            AND ysi.companyid in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="openingName != null and openingName != ''">
            AND ysi.opening_name like concat('%',trim(#{openingName}),'%')
        </if>
        <if test="invoiceCode != null and invoiceCode != ''">
            AND ysi.invoicecode like concat('%',trim(#{invoiceCode}),'%')
        </if>
        <if test="invoiceType != null  ">
            AND ysi.invoice_type = #{invoiceType}
        </if>
        <if test="invoiceProject != null  ">
            AND ysi.invoice_project = #{invoiceProject}
        </if>
        <if test="billCode != null and billCode != ''">
            AND ys.bill_code like concat('%',trim(#{billCode}),'%')
        </if>
        <if test="createBy != null and createBy != ''">
            AND ysi.create_by = #{createBy}
        </if>
        order by ysi.invoice_time desc
    </select>



    <select id="selectProjectAnalysisReportList" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsProjectAnalysisReportDto">
        select
        A.client_type clientType,
        A.brand_name brandName,
        A.client_code clientCode,
        A.client_name clientName,
        A.client_property clietProperty,
        A.city,
        sum(A.storageFee) storageFee,
        sum(A.storageOtherFee) storageOtherFee,
        sum(A.storageCutFee) storageCutFee,
        sum(A.storageTotalFee) storageTotalFee,
        sum(A.transportFee) transportFee,
        sum(A.transportOtherFee) transportOtherFee,
        sum(A.transportCutFee) transportCutFee,
        sum(A.transportTotalFee) transportTotalFee,
        (sum(A.storageTotalFee)+sum(A.transportTotalFee)) * (ifnull(A.tax_rate,0)/100)  taxTotalFee,
        case when A.invoice_mode = 1 then round((sum(A.storageTotalFee)+sum(A.transportTotalFee)) * A.freight_ratio,2) else 0 end freight,
        case when A.invoice_mode = 1 then round((sum(A.storageTotalFee)+sum(A.transportTotalFee)) * A.storage_ratio,2) else 0 end storage,
        case when A.invoice_mode = 1 then round((sum(A.storageTotalFee)+sum(A.transportTotalFee)) * A.message_ratio,2) else 0 end message,
        case when A.invoice_mode = 1 then round((sum(A.storageTotalFee)+sum(A.transportTotalFee)) * A.platform_ratio,2) else 0 end platform,
        case when A.invoice_mode = 1 then round((sum(A.storageTotalFee)+sum(A.transportTotalFee)) * A.brand_ratio,2) else 0 end brand,
        case when A.invoice_mode = 1 then round((sum(A.storageTotalFee)+sum(A.transportTotalFee)) * A.delivery_ratio,2) else 0 end delivery
        from
        (
            select
            y.create_time,
            cli.id clietId,
            cli.client_type,
            cli.brand_name,
            cli.client_code,
            cli.client_name,
            cli.client_property,
            cli.city,
            (ifnull(ysi.freight,0)+ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+
            ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+
            ifnull(ysi.return_fee,0)) storageFee,  -- 仓储费
            (ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
            ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
            ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
            ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) storageOtherFee,  -- 仓储附加费
            ifnull(ysi.exception_fee,0) storageCutFee, -- 仓储扣款
            (ifnull(ysi.freight,0)+ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+
            ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+
            ifnull(ysi.return_fee,0)+ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
            ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
            ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
            ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) storageTotalFee, -- 仓储应收总额
            0 transportFee,
            0 transportOtherFee,
            0 transportCutFee,
            0 transportTotalFee,
            cli.tax_rate,
            cli.invoice_mode,
            cli.freight_ratio,
            cli.storage_ratio,
            cli.message_ratio,
            cli.platform_ratio,
            cli.brand_ratio,
            cli.delivery_ratio
            from bms_yscost_info  ysi
            left join  bms_ysbillmain y on ysi.bill_id = y.id  and y.del_flag = '0'
            left join bms_clientinfo cli on ysi.client_id = cli.id  and cli.del_flag = '0'
            WHERE ysi.del_flag = '0' and expenses_type != 1
            <if test="createTimeStart != null and createTimeStart != '' and createTimeEnd != null and createTimeEnd!=''">
                AND y.create_time >= #{createTimeStart,jdbcType=VARCHAR} AND y.create_time &lt;= #{createTimeEnd,jdbcType=VARCHAR}
            </if>
            <if test="companyIds != null and companyIds.size > 0 ">
                AND y.company_id in
                <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                    #{companyIds}
                </foreach>
            </if>
            <if test="clientList != null">
                and  cli.client_code in
                <foreach collection="clientList" item="client" open="(" separator="," close=")">
                    #{client}
                </foreach>
            </if>
            <if test="clietProperty != null ">
                AND cli.client_type = #{clietProperty}
            </if>
            <if test="clientName != null and clientName != ''">
                AND cli.client_name like concat('%',trim(#{clientName}),'%')
            </if>
            <if test="city != null and city != ''">
                AND cli.city like concat('%',trim(#{city}),'%')
            </if>

            union all
            SELECT
            y.create_time,
            cli.id clietId,
            cli.client_type,
            cli.brand_name,
            cli.client_code,
            cli.client_name,
            cli.client_property,
            cli.city,
            0 storageFee,
            0 storageOtherFee,
            0 storageCutFee,
            0 storageTotalFee,
            (ifnull(ysi.freight,0)+ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+
            ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+
            ifnull(ysi.return_fee,0)) transportFee,
            (ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
            ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
            ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
            ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) transportOtherFee,
            ifnull(ysi.exception_fee,0) transportCutFee,
            (ifnull(ysi.freight,0)+ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+
            ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+
            ifnull(ysi.return_fee,0)+ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
            ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
            ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
            ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) transportTotalFee,
            cli.tax_rate,
            cli.invoice_mode,
            cli.freight_ratio,
            cli.storage_ratio,
            cli.message_ratio,
            cli.platform_ratio,
            cli.brand_ratio,
            cli.delivery_ratio
            from bms_yscost_info  ysi
            left join  bms_ysbillmain y on ysi.bill_id = y.id  and y.del_flag = '0'
            left join bms_clientinfo cli on ysi.client_id = cli.id  and cli.del_flag = '0'
            WHERE ysi.del_flag = '0' and expenses_type = 1
            <if test="createTimeStart != null and createTimeStart != '' and createTimeEnd != null and createTimeEnd!=''">
                AND y.create_time >= #{createTimeStart,jdbcType=VARCHAR} AND y.create_time &lt;= #{createTimeEnd,jdbcType=VARCHAR}
            </if>
            <if test="companyIds != null and companyIds.size > 0 ">
                AND y.company_id in
                <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                    #{companyIds}
                </foreach>
            </if>
            <if test="clientList != null">
                and  cli.client_code in
                <foreach collection="clientList" item="client" open="(" separator="," close=")">
                    #{client}
                </foreach>
            </if>
            <if test="clietProperty != null">
                AND cli.client_type = #{clietProperty}
            </if>
            <if test="clientName != null and clientName != ''">
                AND cli.client_name like concat('%',trim(#{clientName}),'%')
            </if>
            <if test="city != null and city != ''">
                AND cli.city like concat('%',trim(#{city}),'%')
            </if>
        ) A WHERE 1=1 GROUP BY A.clietId
        order by A.create_time desc
    </select>

    <select id="selectBillyshx" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsHxReportDto">
        select
        ys.bill_code billCode
        ,ys.bill_name billName
        ,ys.company_id companyId
        ,DATE_FORMAT(ys.create_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,ys.bill_date billDate
        ,ys.client_id jsId
        ,c.client_code jsCode
        ,c.client_name jsName
        ,CAST(ifnull(ys.adjusted_amount,ys.ys_amount) as DECIMAL(18,2)) AS billAmount
        ,CAST(ifnull(y.payment,0) as DECIMAL(18,2)) AS hxAmount
        ,y.remark hxRemark
        ,DATE_FORMAT(y.payment_time,'%Y-%m-%d %H:%i:%s') hxTimes
        ,y.create_by hxUserName
        from bms_ysbill_hxinfo y
        left join bms_ysbillmain ys on y.bill_codes like CONCAT('%',ys.bill_code,'%') and y.del_flag=0
        left join bms_clientinfo c on ys.client_id=c.id
        where y.del_flag=0
        <if test="createTimeStart != null and createTimeStart != '' and createTimeEnd != null and createTimeEnd!=''">
            AND ys.create_time &gt;= #{createTimeStart,jdbcType=VARCHAR} AND ys.create_time &lt;= #{createTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="jsName != null and jsName != ''">
            AND c.client_name = #{jsName}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND c.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ys.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="billCode != null and billCode != ''">
            AND ys.bill_code like concat('%',trim(#{billCode}),'%')
        </if>
        <if test="hxState != null and hxState != ''">
            AND ys.hx_state = #{hxState}
        </if>
        order by y.create_time desc
    </select>

    <select id="selectBillyfhx" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsHxReportDto">
        select
        yf.bill_code billCode
        ,yf.bill_name billName
        ,yf.company_id companyId
        ,DATE_FORMAT(yf.create_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,yf.bill_date billDate
        ,yf.carrier_id jsId
        ,c.carrier_code jsCode
        ,c.carrier_name jsName
        ,CAST(ifnull(yf.adjusted_amount,yf.yf_amount) as DECIMAL(18,2)) AS billAmount
        ,CAST(ifnull(y.payment,0) as DECIMAL(18,2)) AS hxAmount
        ,y.remark hxRemark
        ,DATE_FORMAT(y.payment_time,'%Y-%m-%d %H:%i:%s') hxTimes
        ,y.create_by hxUserName
        from bms_yfbill_hxinfo y
        left join bms_yfbillmain yf on y.bill_codes like CONCAT('%',yf.bill_code,'%') and y.del_flag=0
        left join bms_carrierinfo c on yf.carrier_id=c.id
        where y.del_flag=0
        <if test="createTimeStart != null and createTimeStart != '' and createTimeEnd != null and createTimeEnd!=''">
            AND yf.create_time &gt;= #{createTimeStart,jdbcType=VARCHAR} AND yf.create_time &lt;= #{createTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="jsName != null and jsName != ''">
            AND c.carrier_name = #{jsName}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND c.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yf.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="billCode != null and billCode != ''">
            AND yf.bill_code like concat('%',trim(#{billCode}),'%')
        </if>
        order by y.create_time desc
    </select>


    <select id="selectBmsYsCostReportList" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsYsCostReportDto">
        select
        companyId,
        expensesType,
        clientName,
        brandName,
        clientType,
        businessType,
        businessTime,
        CONVERT(sum(forecastAmount),DECIMAL(18,2))  forecastAmount,
        CONVERT(sum(actualAmount),DECIMAL(18,2)) actualAmount,
        round(sum(freight),2) freight,
        round(sum(storage),2) storage,
        round(sum(message),2) message,
        round(sum(platform),2) platform,
        round(sum(brand),2) brand,
        round(sum(delivery),2) delivery
        from
        (
        select
            ysi.id,
            ysi.institution_id companyId,
            1 expensesType,
            c.client_name clientName,
            c.brand_name brandName,
            c.client_type clientType,
            1  businessType,
            DATE_FORMAT(ysi.business_time,'%Y-%m') businessTime,
            case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                else 	(ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end as forecastAmount,
            (ifnull(ysi.freight,0)+
            ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
            ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
            ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
            ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
            ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
            ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) actualAmount,
            case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                else 	(ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.freight_ratio,2) else 0 end freight,
            case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                else 	(ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.storage_ratio,2) else 0 end storage,
            case when c.invoice_mode = 1 then round((ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) * c.message_ratio,2) else 0 end message,
                case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                else 	(ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.platform_ratio,2) else 0 end platform,
            case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                else 	(ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.brand_ratio,2) else 0 end brand,
            case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                else 	(ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.delivery_ratio,2) else 0 end delivery
        from bms_yscost_info ysi
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where ysi.del_flag=0   and ysi.expenses_type = 1
        <if test="createTimeStart != null and createTimeStart != '' ">
            AND DATE_FORMAT(ysi.business_time,'%Y-%m')  = #{createTimeStart,jdbcType=VARCHAR}
        </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        UNION all
        select
            ysi.id,
            ysi.institution_id companyId,
            2 expensesType,
            c.client_name clientName,
            c.brand_name brandName,
            c.client_type clientType,
            1  businessType,
            DATE_FORMAT(ysi.business_time,'%Y-%m') businessTime,
            case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                else 	(ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end as forecastAmount,
            (ifnull(ysi.freight,0)+
            ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
            ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
            ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
            ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
            ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
            ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) actualAmount,
            case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                else 	(ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.freight_ratio,2) else 0 end freight,
            case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                else 	(ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.storage_ratio,2) else 0 end storage,
                case when c.invoice_mode = 1 then round((ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) * c.message_ratio,2) else 0 end message,
            case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                else 	(ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.platform_ratio,2) else 0 end platform,
            case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                else 	(ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.brand_ratio,2) else 0 end brand,
            case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
                else 	(ifnull(ysi.freight,0)+
                ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
                ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
                ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
                ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
                ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
                ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.delivery_ratio,2) else 0 end delivery
        from bms_yscost_info ysi
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where ysi.del_flag=0   and ysi.expenses_type != 1
        <if test="createTimeStart != null and createTimeStart != '' ">
            AND DATE_FORMAT(ysi.business_time,'%Y-%m')  = #{createTimeStart,jdbcType=VARCHAR}
        </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
         AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        UNION all
<!--        预估表-->
        select
        ysi.id,
        ysi.institution_id companyId,
        1 expensesType,
        c.client_name clientName,
        c.brand_name brandName,
        c.client_type clientType,
        1  businessType,
        DATE_FORMAT(ysi.business_time,'%Y-%m') businessTime,
        case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end as forecastAmount,
        (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) actualAmount,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.freight_ratio,2) else 0 end freight,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.storage_ratio,2) else 0 end storage,
        case when c.invoice_mode = 1 then round((ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) * c.message_ratio,2) else 0 end message,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.platform_ratio,2) else 0 end platform,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.brand_ratio,2) else 0 end brand,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.delivery_ratio,2) else 0 end delivery
        from bms_yscost_info_estimate ysi
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where ysi.del_flag=0   and ysi.expenses_type = 1
        <if test="createTimeStart != null and createTimeStart != '' ">
            AND DATE_FORMAT(ysi.business_time,'%Y-%m')  = #{createTimeStart,jdbcType=VARCHAR}
        </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        UNION all
        select
        ysi.id,
        ysi.institution_id companyId,
        2 expensesType,
        c.client_name clientName,
        c.brand_name brandName,
        c.client_type clientType,
        1  businessType,
        DATE_FORMAT(ysi.business_time,'%Y-%m') businessTime,
        case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end as forecastAmount,
        (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) actualAmount,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.freight_ratio,2) else 0 end freight,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.storage_ratio,2) else 0 end storage,
        case when c.invoice_mode = 1 then round((ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) * c.message_ratio,2) else 0 end message,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.platform_ratio,2) else 0 end platform,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.brand_ratio,2) else 0 end brand,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.delivery_ratio,2) else 0 end delivery
        from bms_yscost_info_estimate ysi
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where ysi.del_flag=0   and ysi.expenses_type != 1
        <if test="createTimeStart != null and createTimeStart != '' ">
            AND DATE_FORMAT(ysi.business_time,'%Y-%m')  = #{createTimeStart,jdbcType=VARCHAR}
        </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        )A
        WHERE 1 = 1

        <if test="groupBys != null and groupBys != '' ">
            group by  ${groupBys}
        </if>
        order by A.businessTime desc
    </select>



    <select id="selectBmsYfCostReportList" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsYfCostReportDto">
        select
        companyId,
        expensesType,
        carrierName,
        clientName,
        businessType,
        businessTime,
        CONVERT(sum(forecastAmount),DECIMAL(18,2))    forecastAmount,
        CONVERT(sum(actualAmount),DECIMAL(18,2))  actualAmount
        from
        (
        select
        ysi.id,
        ysi.company_id companyId,
        1 expensesType,
        ysi.carrier_name carrierName,
        c.client_name clientName,
        1  businessType,
        DATE_FORMAT(ysi.business_time,'%Y-%m') businessTime,
        case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end as forecastAmount,
        (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) actualAmount
        from bms_yfcost_info ysi
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where ysi.del_flag=0   and ysi.expenses_type = 1
        <if test="createTimeStart != null and createTimeStart != '' ">
            AND DATE_FORMAT(ysi.business_time,'%Y-%m')  = #{createTimeStart,jdbcType=VARCHAR}
        </if>
        <if test="carrierList != null">
            and  ysi.carrier_code in
            <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                #{carrier}
            </foreach>
        </if>
        <if test="carrierName != null and carrierName != ''">
          AND ysi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        UNION all
        select
        ysi.id,
        ysi.company_id companyId,
        2 expensesType,
        ysi.carrier_name carrierName,
        c.client_name clientName,
        2 businessType,
        DATE_FORMAT(ysi.business_time,'%Y-%m') businessTime,
        case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end as forecastAmount,
        (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) actualAmount
        from bms_yfcost_info ysi
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where ysi.del_flag=0   and ysi.expenses_type != 1
        <if test="createTimeStart != null and createTimeStart != '' ">
            AND DATE_FORMAT(ysi.business_time,'%Y-%m')  = #{createTimeStart,jdbcType=VARCHAR}
        </if>
        <if test="carrierList != null">
            and  ysi.carrier_code in
            <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                #{carrier}
            </foreach>
        </if>
        <if test="carrierName != null and carrierName != ''">
           AND ysi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
<!--        预估表-->
        UNION all
        select
        ysi.id,
        ysi.company_id companyId,
        1 expensesType,
        ysi.carrier_name carrierName,
        c.client_name clientName,
        1  businessType,
        DATE_FORMAT(ysi.business_time,'%Y-%m') businessTime,
        case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end as forecastAmount,
        (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) actualAmount
        from bms_yfcost_info_estimate ysi
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where ysi.del_flag=0   and ysi.expenses_type = 1
        <if test="createTimeStart != null and createTimeStart != '' ">
            AND DATE_FORMAT(ysi.business_time,'%Y-%m')  = #{createTimeStart,jdbcType=VARCHAR}
        </if>
        <if test="carrierList != null">
            and  ysi.carrier_code in
            <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                #{carrier}
            </foreach>
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND ysi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        UNION all
        select
        ysi.id,
        ysi.company_id companyId,
        2 expensesType,
        ysi.carrier_name carrierName,
        c.client_name clientName,
        2 businessType,
        DATE_FORMAT(ysi.business_time,'%Y-%m') businessTime,
        case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end as forecastAmount,
        (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) actualAmount
        from bms_yfcost_info_estimate ysi
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where ysi.del_flag=0   and ysi.expenses_type != 1
        <if test="createTimeStart != null and createTimeStart != '' ">
            AND DATE_FORMAT(ysi.business_time,'%Y-%m')  = #{createTimeStart,jdbcType=VARCHAR}
        </if>
        <if test="carrierList != null">
            and  ysi.carrier_code in
            <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                #{carrier}
            </foreach>
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND ysi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        )A
        WHERE 1 = 1
        <if test="groupBys != null and groupBys != '' ">
            group by  ${groupBys}
        </if>
        order by A.businessTime desc
    </select>

    <select id="selectBmsYsCostReportListByCustom" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsYsCostReportDto">
        select
        companyId,
        expensesType,
        clientName,
        brandName,
        clientType,
        businessType,
        businessTime,
        CONVERT(sum(forecastAmount),DECIMAL(18,2))  forecastAmount,
        CONVERT(sum(actualAmount),DECIMAL(18,2)) actualAmount,
        round(sum(freight),2) freight,
        round(sum(storage),2) storage,
        round(sum(message),2) message,
        round(sum(platform),2) platform,
        round(sum(brand),2) brand,
        round(sum(delivery),2) delivery
        from
        (
        select
        ysi.id,
        ysi.institution_id companyId,
        1 expensesType,
        c.client_name clientName,
        c.brand_name brandName,
        c.client_type clientType,
        1  businessType,
        DATE_FORMAT(ysi.business_time,'%Y-%m') businessTime,
        case
        when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m')
        then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end as forecastAmount,
        (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) actualAmount,
        case
        when c.invoice_mode = 1
        then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.freight_ratio,2) else 0 end freight,
        case
        when c.invoice_mode = 1
        then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.storage_ratio,2) else 0 end storage,
        case
        when c.invoice_mode = 1
        then round((ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) * c.message_ratio,2) else 0 end message,
        case
        when c.invoice_mode = 1
        then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.platform_ratio,2) else 0 end platform,
        case
        when c.invoice_mode = 1
        then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.brand_ratio,2) else 0 end brand,
        case
        when c.invoice_mode = 1
        then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.delivery_ratio,2) else 0 end delivery
        from bms_yscost_info ysi
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where ysi.del_flag=0 and ysi.expenses_type = 1
        and ysi.cost_dimension !=4
        <if test="estimateStartDate!=null"> and ysi.business_time &gt;= #{estimateStartDate} </if>
        <if test="estimateEndDate!=null">  and ysi.business_time &lt;= #{estimateEndDate} </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        UNION all
        select
        ysi.id,
        ysi.institution_id companyId,
        2 expensesType,
        c.client_name clientName,
        c.brand_name brandName,
        c.client_type clientType,
        1  businessType,
        DATE_FORMAT(ysi.business_time,'%Y-%m') businessTime,
        case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end as forecastAmount,
        (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) actualAmount,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.freight_ratio,2) else 0 end freight,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.storage_ratio,2) else 0 end storage,
        case when c.invoice_mode = 1 then round((ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) * c.message_ratio,2) else 0 end message,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.platform_ratio,2) else 0 end platform,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.brand_ratio,2) else 0 end brand,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.delivery_ratio,2) else 0 end delivery
        from bms_yscost_info ysi
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where ysi.del_flag=0 and ysi.expenses_type != 1
        and ysi.cost_dimension !=4
        <if test="estimateStartDate!=null"> and ysi.business_time &gt;= #{estimateStartDate} </if>
        <if test="estimateEndDate!=null">  and ysi.business_time &lt;= #{estimateEndDate} </if>
        <if test="createTimeStart != null and createTimeStart != '' ">
            AND DATE_FORMAT(ysi.business_time,'%Y-%m')  = #{createTimeStart,jdbcType=VARCHAR}
        </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        UNION all
        <!--        预估表-->
        select
        ysi.id,
        ysi.institution_id companyId,
        1 expensesType,
        c.client_name clientName,
        c.brand_name brandName,
        c.client_type clientType,
        1  businessType,
        DATE_FORMAT(ysi.business_time,'%Y-%m') businessTime,
        case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end as forecastAmount,
        (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) actualAmount,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.freight_ratio,2) else 0 end freight,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.storage_ratio,2) else 0 end storage,
        case when c.invoice_mode = 1 then round((ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) * c.message_ratio,2) else 0 end message,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.platform_ratio,2) else 0 end platform,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.brand_ratio,2) else 0 end brand,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.delivery_ratio,2) else 0 end delivery
        from bms_yscost_info_estimate_custom ysi
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where ysi.del_flag=0 and ysi.expenses_type = 1
        <if test="estimateStartDate!=null"> and ysi.business_time &gt;= #{estimateStartDate} </if>
        <if test="estimateEndDate!=null">  and ysi.business_time &lt;= #{estimateEndDate} </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        UNION all
        select
        ysi.id,
        ysi.institution_id companyId,
        2 expensesType,
        c.client_name clientName,
        c.brand_name brandName,
        c.client_type clientType,
        1  businessType,
        DATE_FORMAT(ysi.business_time,'%Y-%m') businessTime,
        case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end as forecastAmount,
        (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) actualAmount,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.freight_ratio,2) else 0 end freight,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.storage_ratio,2) else 0 end storage,
        case when c.invoice_mode = 1 then round((ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) * c.message_ratio,2) else 0 end message,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.platform_ratio,2) else 0 end platform,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.brand_ratio,2) else 0 end brand,
        case when c.invoice_mode = 1 then round((case when DATE_FORMAT(ysi.business_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') then (ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0))/DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        * DATEDIFF(date_add(curdate()-day(curdate())+1,interval 1 month ),DATE_ADD(curdate(),interval -day(curdate())+1 day))
        else 	(ifnull(ysi.freight,0)+
        ifnull(ysi.ultrafar_fee,0)+ifnull(ysi.superframes_fee,0)+ifnull(ysi.excess_fee,0)+ifnull(ysi.delivery_fee,0)+
        ifnull(ysi.Outboundsorting_fee,0)+ifnull(ysi.shortbarge_fee,0)+ifnull(ysi.return_fee,0)+
        ifnull(ysi.exception_fee,0)+ifnull(ysi.adjust_fee,0)+ifnull(ysi.other_cost1,0)+ifnull(ysi.other_cost2,0)+
        ifnull(ysi.other_cost3,0)+ifnull(ysi.other_cost4,0)+ifnull(ysi.other_cost5,0)+ifnull(ysi.other_cost6,0)+
        ifnull(ysi.other_cost7,0)+ifnull(ysi.other_cost8,0)+ifnull(ysi.other_cost9,0)+ifnull(ysi.other_cost10,0)+
        ifnull(ysi.other_cost11,0)+ifnull(ysi.other_cost12,0)) end) * c.delivery_ratio,2) else 0 end delivery
        from bms_yscost_info_estimate_custom ysi
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        where ysi.del_flag=0   and ysi.expenses_type != 1
        <if test="estimateStartDate!=null"> and ysi.business_time &gt;= #{estimateStartDate} </if>
        <if test="estimateEndDate!=null">  and ysi.business_time &lt;= #{estimateEndDate} </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        )A
        WHERE 1 = 1

        <if test="groupBys != null and groupBys != '' ">
            group by  ${groupBys}
        </if>
        order by A.businessTime desc
    </select>

    <select id="selectBmsYsCostReportListByDay" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsYsCostReportDto">
        select
        companyId,
        expensesType,
        clientName,
        brandName,
        clientType,
        businessType,
        businessTime,
        CONVERT(sum(forecastAmount),DECIMAL(18,2))  forecastAmount,
        CONVERT(sum(actualAmount),DECIMAL(18,2)) actualAmount,
        round(sum(freight),2) freight,
        round(sum(storage),2) storage,
        round(sum(message),2) message,
        round(sum(platform),2) platform,
        round(sum(brand),2) brand,
        round(sum(delivery),2) delivery
        from (
        <!-- 非按月计费的 -->
        select
        ysi.id,
        ysi.institution_id companyId,
        case
        when ysi.expenses_type = 1 then 1
        else 2
        end expensesType,
        c.client_name clientName,
        c.brand_name brandName,
        c.client_type clientType,
        1 businessType,
        DATE_FORMAT(ysi.business_time, '%Y-%m') businessTime,
        (ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) as forecastAmount,
        (ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) as actualAmount,
        case
        when c.invoice_mode = 1
        then round(
        ((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0))
        ) * c.freight_ratio, 2)
        else 0
        end freight,
        case
        when c.invoice_mode = 1
        then round(
        ((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0))
        ) * c.storage_ratio, 2)
        else 0
        end storage,
        case
        when c.invoice_mode = 1
        then round(
        ((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0))
        ) * c.message_ratio, 2)
        else 0
        end message,
        case
        when c.invoice_mode = 1
        then round(
        ((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0))
        ) * c.platform_ratio, 2)
        else 0
        end platform,
        case
        when c.invoice_mode = 1
        then round(
        ((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0))
        ) * c.brand_ratio, 2)
        else 0
        end brand,
        case
        when c.invoice_mode = 1
        then round(
        ((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0))
        ) * c.delivery_ratio, 2)
        else 0
        end delivery
        FROM bms_yscost_info ysi
        LEFT JOIN bms_clientinfo c on ysi.client_id = c.id and c.del_flag = '0'
        WHERE ysi.del_flag=0
        and ysi.cost_dimension !=4
        <if test="estimateStartDate!=null"> and ysi.business_time &gt;= #{estimateStartDate} </if>
        <if test="estimateEndDate!=null">  and ysi.business_time &lt;= #{estimateEndDate} </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ysi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <!-- 非当月的按月计费的 -->
        <if test="monthByDaysLis!=null and monthByDaysLis.size>0 ">
            <foreach collection="monthByDaysLis" item="item">
                UNION ALL
                select
                ysi.id,
                ysi.institution_id companyId,
                case
                when ysi.expenses_type = 1 then 1
                else 2
                end expensesType,
                c.client_name clientName,
                c.brand_name brandName,
                c.client_type clientType,
                1 businessType,
                DATE_FORMAT(ysi.business_time, '%Y-%m') businessTime,
                (ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0))
                /${item.monthSumDays}*${item.monthAugDays} as forecastAmount,
                (ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0))
                /${item.monthSumDays}*${item.monthAugDays} as actualAmount,
                case
                when c.invoice_mode = 1
                then round((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) * c.freight_ratio, 2)
                /${item.monthSumDays}*${item.monthAugDays}
                else 0
                end freight,
                case
                when c.invoice_mode = 1
                then round((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) * c.storage_ratio, 2)
                /${item.monthSumDays}*${item.monthAugDays}
                else 0
                end storage,
                case
                when c.invoice_mode = 1
                then round((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) * c.message_ratio, 2)
                /${item.monthSumDays}*${item.monthAugDays}
                else 0
                end message,
                case
                when c.invoice_mode = 1
                then round((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) * c.platform_ratio, 2)
                /${item.monthSumDays}*${item.monthAugDays}
                else 0
                end platform,
                case
                when c.invoice_mode = 1
                then round((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) * c.brand_ratio, 2)
                /${item.monthSumDays}*${item.monthAugDays}
                else 0
                end brand,
                case
                when c.invoice_mode = 1
                then round((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) * c.delivery_ratio, 2)
                /${item.monthSumDays}*${item.monthAugDays}
                else 0
                end delivery
                FROM bms_yscost_info ysi
                LEFT JOIN bms_clientinfo c on ysi.client_id = c.id and c.del_flag = '0'
                WHERE ysi.del_flag=0 and ysi.cost_dimension = 4
                AND DATE_FORMAT(ysi.business_time,'%Y-%m') = #{item.month}
                <if test="clientName != null and clientName != ''">
                    AND c.client_name like concat('%',trim(#{clientName}),'%')
                </if>
                <if test="clientList != null">
                    and  c.client_code in
                    <foreach collection="clientList" item="client" open="(" separator="," close=")">
                        #{client}
                    </foreach>
                </if>
                <if test="companyIds != null and companyIds.size>0 ">
                    AND ysi.institution_id in
                    <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                        #{companyIds}
                    </foreach>
                </if>
            </foreach>
        </if>
        <if test="isIncloudCurMonth!=null and isIncloudCurMonth==0">
            UNION ALL
            select
            ysi.id,
            ysi.institution_id companyId,
            case
            when ysi.expenses_type = 1 then 1
            else 2
            end expensesType,
            c.client_name clientName,
            c.brand_name brandName,
            c.client_type clientType,
            1 businessType,
            DATE_FORMAT(ysi.business_time, '%Y-%m') businessTime,
            (ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0))
            /DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day)) * #{curMonthInfo.monthAugDays}
            as forecastAmount,
            (ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0))
            /DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day)) * #{curMonthInfo.monthAugDays}
            as actualAmount,
            case
            when c.invoice_mode = 1
            then round((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) * c.freight_ratio, 2)
            /DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day)) * #{curMonthInfo.monthAugDays}
            else 0
            end freight,
            case
            when c.invoice_mode = 1
            then round((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) * c.storage_ratio, 2)
            /DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day)) * #{curMonthInfo.monthAugDays}
            else 0
            end storage,
            case
            when c.invoice_mode = 1
            then round((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) * c.message_ratio, 2)
            /DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day)) * #{curMonthInfo.monthAugDays}
            else 0
            end message,
            case
            when c.invoice_mode = 1
            then round((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) * c.platform_ratio, 2)
            /DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day)) * #{curMonthInfo.monthAugDays}
            else 0
            end platform,
            case
            when c.invoice_mode = 1
            then round((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) * c.brand_ratio, 2)
            /DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day)) * #{curMonthInfo.monthAugDays}
            else 0
            end brand,
            case
            when c.invoice_mode = 1
            then round((ifnull(ysi.freight, 0)+ifnull(ysi.ultrafar_fee, 0)+ifnull(ysi.superframes_fee, 0)+ifnull(ysi.excess_fee, 0)+ifnull(ysi.delivery_fee, 0)+ifnull(ysi.Outboundsorting_fee, 0)+ifnull(ysi.shortbarge_fee, 0)+ifnull(ysi.return_fee, 0)+ifnull(ysi.exception_fee, 0)+ifnull(ysi.adjust_fee, 0)+ifnull(ysi.other_cost1, 0)+ifnull(ysi.other_cost2, 0)+ifnull(ysi.other_cost3, 0)+ifnull(ysi.other_cost4, 0)+ifnull(ysi.other_cost5, 0)+ifnull(ysi.other_cost6, 0)+ifnull(ysi.other_cost7, 0)+ifnull(ysi.other_cost8, 0)+ifnull(ysi.other_cost9, 0)+ifnull(ysi.other_cost10, 0)+ifnull(ysi.other_cost11, 0)+ifnull(ysi.other_cost12, 0)) * c.delivery_ratio, 2)
            /DATEDIFF(now(),DATE_ADD(curdate(),interval -day(curdate())+1 day)) * #{curMonthInfo.monthAugDays}
            else 0
            end delivery
            FROM bms_yscost_info_estimate ysi
            LEFT JOIN bms_clientinfo c on ysi.client_id = c.id and c.del_flag = '0'
            WHERE ysi.del_flag=0
            <if test="clientName != null and clientName != ''">
                AND c.client_name like concat('%',trim(#{clientName}),'%')
            </if>
            <if test="clientList != null">
                and  c.client_code in
                <foreach collection="clientList" item="client" open="(" separator="," close=")">
                    #{client}
                </foreach>
            </if>
            <if test="companyIds != null and companyIds.size>0 ">
                AND ysi.institution_id in
                <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                    #{companyIds}
                </foreach>
            </if>
        </if>
        ) A
        WHERE 1 = 1
        <if test="groupBys != null and groupBys != '' ">
            group by  ${groupBys}
        </if>
        order by A.businessTime desc
    </select>




</mapper>
