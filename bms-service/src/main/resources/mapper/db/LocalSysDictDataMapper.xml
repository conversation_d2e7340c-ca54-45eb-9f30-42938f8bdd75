<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.LocalSysDictDataMapper">
    <resultMap type="com.bbyb.joy.bms.domain.dto.LocalSysDictData" id="SysDictDataMap">
        <result property="dictCode" column="dict_code" jdbcType="INTEGER"/>
        <result property="dictSort" column="dict_sort" jdbcType="INTEGER"/>
        <result property="dictLabel" column="dict_label" jdbcType="VARCHAR"/>
        <result property="dictValue" column="dict_value" jdbcType="VARCHAR"/>
        <result property="dictType" column="dict_type" jdbcType="VARCHAR"/>
        <result property="cssClass" column="css_class" jdbcType="VARCHAR"/>
        <result property="listClass" column="list_class" jdbcType="VARCHAR"/>
        <result property="isDefault" column="is_default" jdbcType="CHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>


    <!--新增数据-->
    <insert id="insert" keyProperty="dict_code" useGeneratedKeys="true">
        insert into sys_dict_data(dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)
        values (#{dictCode},#{dictSort},#{dictLabel},#{dictValue},#{dictType},#{cssClass},#{listClass},#{isDefault},#{status},#{createBy},#{createTime},#{updateBy},#{updateTime},#{remark})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch">
        insert into sys_dict_data(dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.dictSort},#{entity.dictLabel},#{entity.dictValue},#{entity.dictType},#{entity.cssClass},#{entity.listClass},#{entity.isDefault},#{entity.status},#{entity.createBy},#{entity.createTime},#{entity.updateBy},#{entity.updateTime},#{entity.remark})
        </foreach>
    </insert>


    <select id="queryByCond" resultMap="SysDictDataMap" parameterType="java.util.Map">
        select
        dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark
        from sys_dict_data
        <where>
            <if test="dictCode != null and dictCode != ''">
                and dict_code = #{dictCode}
            </if>
            <if test="dictSort != null and dictSort != ''">
                and dict_sort = #{dictSort}
            </if>
            <if test="dictLabel != null and dictLabel != ''">
                and dict_label = #{dictLabel}
            </if>
            <if test="dictValue != null and dictValue != ''">
                and dict_value = #{dictValue}
            </if>
            <if test="dictType != null and dictType != ''">
                and dict_type = #{dictType}
            </if>
            <if test="cssClass != null and cssClass != ''">
                and css_class = #{cssClass}
            </if>
            <if test="listClass != null and listClass != ''">
                and list_class = #{listClass}
            </if>
            <if test="isDefault != null and isDefault != ''">
                and is_default = #{isDefault}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null and updateTime != ''">
                and update_time = #{updateTime}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="inDictTypes != null and inDictTypes != ''">
                and dict_type in
                <foreach collection="inDictTypes"  item="item" open="(" separator="," close=")" >
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 更新数据 -->
    <update id="update">
        update sys_dict_data
        <set>
            <if test="dictCode != null and dictCode != ''">
                dict_code = #{dictCode},
            </if>
            <if test="dictSort != null and dictSort != ''">
                dict_sort = #{dictSort},
            </if>
            <if test="dictLabel != null and dictLabel != ''">
                dict_label = #{dictLabel},
            </if>
            <if test="dictValue != null and dictValue != ''">
                dict_value = #{dictValue},
            </if>
            <if test="dictType != null and dictType != ''">
                dict_type = #{dictType},
            </if>
            <if test="cssClass != null and cssClass != ''">
                css_class = #{cssClass},
            </if>
            <if test="listClass != null and listClass != ''">
                list_class = #{listClass},
            </if>
            <if test="isDefault != null and isDefault != ''">
                is_default = #{isDefault},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="createTime != null and createTime != ''">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null and updateTime != ''">
                update_time = #{updateTime},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </set>
        where dict_code = #{dictCode}
    </update>

    <update id="updateBatch">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";" >
            update sys_dict_data
            <set>
                <if test="item.dictLabel != null and item.dictLabel != ''">
                    dict_label = #{item.dictLabel},
                </if>
                <if test="item.status != null and item.status != ''">
                    status = #{item.status},
                </if>
                <if test="item.updateTime != null and item.updateTime != ''">
                    update_time = SYSDATE(),
                </if>
            </set>
            where dict_value = #{item.dictValue} and dict_type = #{item.dictType}
        </foreach>
    </update>

    <!--通过主键作废-->
    <delete id="deleteById">
        delete from sys_dict_data where dict_code = #{dictCode}
    </delete>


</mapper>