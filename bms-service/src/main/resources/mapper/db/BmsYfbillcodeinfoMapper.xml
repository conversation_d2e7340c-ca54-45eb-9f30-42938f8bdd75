<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfbillcodeinfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYfbillcodeinfo" id="BmsYfbillcodeinfoResult">
        <result property="id"    column="id"    />
        <result property="pkId"    column="pk_id"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="schedulingBillCode"    column="scheduling_bill_code"    />
        <result property="virtualOrderNo"    column="virtual_order_no"    />
        <result property="projectQuote"    column="project_quote"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="companyId"    column="company_id"    />
        <result property="networkCode"    column="network_code"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="totalNumber"    column="total_number"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="cargoValue"    column="cargo_value"    />
        <result property="dispatchDate"    column="dispatch_date"    />
        <result property="finishDate"    column="finish_date"    />
        <result property="storageServiceProvider"    column="storage_service_provider"    />
        <result property="lineCode"    column="line_code"    />
        <result property="lineName"    column="line_name"    />
        <result property="baseStores"    column="base_stores"    />
        <result property="baseKilometer"    column="base_kilometer"    />
        <result property="totalKilometer"    column="total_kilometer"    />
        <result property="numberLoadingPoints"    column="number_loading_points"    />
        <result property="numberUnloadingPoints"    column="number_unloading_points"    />
        <result property="totalVotenumber"    column="total_votenumber"    />
        <result property="driver"    column="driver"    />
        <result property="carCode"    column="car_code"    />
        <result property="carType"    column="car_type"    />
        <result property="carModel"    column="car_model"    />
        <result property="headOfficeTimes"    column="head_office_times"    />
        <result property="bodyOfficeTimes"    column="body_office_times"    />
        <result property="provinceOrigin"    column="province_origin"    />
        <result property="originatingCity"    column="originating_City"    />
        <result property="originatingArea"    column="originating_area"    />
        <result property="originatingAddress"    column="originating_address"    />
        <result property="destinationProvince"    column="destination_Province"    />
        <result property="destinationCity"    column="destination_city"    />
        <result property="destinationArea"    column="destination_area"    />
        <result property="destinationAddress"    column="destination_address"    />
        <result property="costStatus"    column="cost_status"    />
        <result property="billingStatus"    column="billing_status"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="transportType"    column="transport_type"    />
        <result property="deliveryMode"    column="delivery_mode"    />
        <result property="tlineCode"    column="tline_code"    />
        <result property="tlineName"    column="tline_name"    />
        <result property="vehicleTemperatureType"    column="vehicle_temperature_type"    />
        <result property="hyOrderNo"    column="hy_order_no"    />
        <result property="expressNo"    column="express_no"    />
        <result property="settleSetting"    column="settle_setting"    />
        <result property="carrierpayType"    column="carrier_paytype"    />
        <result property="isToClient"    column="is_to_client"    />
    </resultMap>


    <resultMap type="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto" id="BmsYfbillcodeinfoDtoResult">
        <result property="id"    column="id"    />
        <result property="pkId"    column="pk_id"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="yfbillId" column="yfbillId" />
        <result property="schedulingBillCode"    column="scheduling_bill_code"    />
        <result property="virtualOrderNo"    column="virtual_order_no"    />
        <result property="projectQuote"    column="project_quote"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="companyId"    column="company_id"    />
        <result property="networkCode"    column="network_code"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="totalNumber"    column="total_number"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="cargoValue"    column="cargo_value"    />
        <result property="dispatchDate"    column="dispatch_date"    />
        <result property="finishDate"    column="finish_date"    />
        <result property="storageServiceProvider"    column="storage_service_provider"    />
        <result property="lineCode"    column="line_code"    />
        <result property="lineName"    column="line_name"    />
        <result property="baseStores"    column="base_stores"    />
        <result property="baseKilometer"    column="base_kilometer"    />
        <result property="totalKilometer"    column="total_kilometer"    />
        <result property="numberLoadingPoints"    column="number_loading_points"    />
        <result property="numberUnloadingPoints"    column="number_unloading_points"    />
        <result property="totalVotenumber"    column="total_votenumber"    />
        <result property="driver"    column="driver"    />
        <result property="carCode"    column="car_code"    />
        <result property="carType"    column="car_type"    />
        <result property="carModel"    column="car_model"    />
        <result property="headOfficeTimes"    column="head_office_times"    />
        <result property="bodyOfficeTimes"    column="body_office_times"    />
        <result property="provinceOrigin"    column="province_origin"    />
        <result property="originatingCity"    column="originating_City"    />
        <result property="originatingArea"    column="originating_area"    />
        <result property="originatingAddress"    column="originating_address"    />
        <result property="destinationProvince"    column="destination_Province"    />
        <result property="destinationCity"    column="destination_city"    />
        <result property="destinationArea"    column="destination_area"    />
        <result property="destinationAddress"    column="destination_address"    />
        <result property="costStatus"    column="cost_status"    />
        <result property="billingStatus"    column="billing_status"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="transportType"    column="transport_type"    />
        <result property="deliveryMode"    column="delivery_mode"    />
        <result property="tlineCode"    column="tline_code"    />
        <result property="tlineName"    column="tline_name"    />
        <result property="vehicleTemperatureType"    column="vehicle_temperature_type"    />
        <result property="hyOrderNo"    column="hy_order_no"    />
        <result property="expressNo"    column="express_no"    />
        <result property="settleSetting"    column="settle_setting"    />
        <result property="carrierpayType"    column="carrier_paytype"    />
        <result property="isToClient"    column="is_to_client"    />
    </resultMap>


    <sql id="selectBmsYfbillcodeinfoVo">
        select id, pk_id, scheduling_bill_code,virtual_order_no,project_quote, carrier_code, carrier_name, company_id, network_code, total_boxes, total_number, total_weight, total_volume, cargo_value, dispatch_date, finish_date, transport_type,delivery_mode, line_code, line_name, base_stores, base_kilometer, total_kilometer, number_loading_points, number_unloading_points, total_votenumber, driver, car_code, car_type, car_model, head_office_times, province_origin, originating_City, originating_area, originating_address, destination_Province, destination_city, destination_area, destination_address, cost_status, billing_status, create_code, create_by, create_dept_id, create_time, oper_code, oper_by, oper_dept_id, oper_time, del_flag, tline_code, tline_name,express_no from bms_yfbillcodeinfo
    </sql>

    <select id="selectOrderBillGroup" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">
        select * from
        (SELECT
        y.id yfbillId
        ,yi.id expensesId
        ,y.scheduling_bill_code schedulingBillCode
        ,y.cost_status costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,DATE_FORMAT(y.dispatch_date,'%Y-%m-%d %H:%i:%s') dispatchDates
        ,DATE_FORMAT(y.finish_date,'%Y-%m-%d %H:%i:%s') finishDates
        ,y.company_id companyId
        ,yi.rule_name ruleName
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,IFNULL(y.total_number,0) totalNumber
        ,IFNULL(y.total_weight,0) totalWeight
        ,IFNULL(y.total_volume,0) totalVolume
        ,IFNULL(y.cargo_value,0) cargoValue
        ,yi.fee_flag feeFlag
        ,yi.remarks remarks
        ,yi.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,ybm.bill_code billCode
        ,y.del_flag delFlag
        ,yi.oper_time operTime
        ,y.carrier_code carrierCode
        ,y.carrier_name carrierName
        ,y.network_code networkCode
        ,IFNULL(y.total_boxes,0) totalBoxes
        ,y.transport_type transportType
        ,y.delivery_mode deliveryMode
        ,y.line_code lineCode
        ,y.line_name lineName
        ,y.base_stores baseStores
        ,y.base_kilometer baseKilometer
        ,y.total_kilometer totalKilometer
        ,y.number_loading_points numberLoadingPoints
        ,y.number_unloading_points numberUnloadingPoints
        ,y.total_votenumber totalVotenumber
        ,y.driver
        ,y.car_code carCode
        ,y.car_type carType
        ,y.car_model carModel
        ,y.head_office_times headOfficeTimes
        ,y.dispatch_date dispatchDate
        ,y.finish_date finishDate
        ,y.create_time createTime
        FROM bms_yfbillcodeinfo y left join bms_yfexpenses_middle ym on y.id=ym.yfbill_id and ym.del_flag=0
        left join bms_yfcost_info yi on yi.id=ym.expenses_id and yi.del_flag=0
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        where yi.cost_dimension=1 or yi.cost_dimension is null
        and y.del_flag = 0
        <if test="dispatchDateStart != null and dispatchDateStart != '' and dispatchDateEnd != null and dispatchDateEnd!=''">
            AND y.dispatch_date &gt;= #{dispatchDateStart,jdbcType=VARCHAR} AND dispatchDate &lt;= #{dispatchDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="finishDateStart != null and finishDateStart != '' and finishDateEnd != null and finishDateEnd!=''">
            AND y.finish_date &gt;= #{finishDateStart,jdbcType=VARCHAR} AND finishDate &lt;= #{finishDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND operTime &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="schedulingBillCode != null and schedulingBillCode != ''">
            AND y.scheduling_bill_code like concat('%',trim(#{schedulingBillCode}),'%')
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>


        union


        SELECT
        null yfbillId
        ,yi.id expensesId
        ,GROUP_CONCAT(DISTINCT y.scheduling_bill_code SEPARATOR ',') schedulingBillCode
        ,1 costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,null dispatchDates
        ,null finishDates
        ,yi.company_id companyId
        ,yi.rule_name ruleName
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,IFNULL(sum(y.total_number),0) totalNumber
        ,IFNULL(sum(y.total_weight),0) totalWeight
        ,IFNULL(sum(y.total_volume),0) totalVolume
        ,IFNULL(sum(y.cargo_value),0) cargoValue
        ,yi.fee_flag feeFlag
        ,yi.remarks remarks
        ,yi.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,ybm.bill_code billCode
        ,yi.del_flag delFlag
        ,yi.oper_time operTime
        ,null carrierCode
        ,null carrierName
        ,null networkCode
        ,IFNULL(sum(y.total_boxes),0) totalBoxes
        ,null transportType
        ,null deliveryMode
        ,null lineCode
        ,null lineName
        ,null baseStores
        ,null baseKilometer
        ,null totalKilometer
        ,null numberLoadingPoints
        ,null numberUnloadingPoints
        ,null totalVotenumber
        ,null driver
        ,null carCode
        ,null carType
        ,null carModel
        ,null headOfficeTimes
        ,null dispatchDate
        ,null finishDate
        ,yi.oper_time createTime
        from bms_yfcost_info yi left join bms_yfexpenses_middle ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_yfbillcodeinfo y on y.id=ym.yfbill_id and y.del_flag=0
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0

        where yi.cost_dimension is not null and yi.cost_dimension!=1 and y.scheduling_bill_code is not null
        and yi.del_flag = 0
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND operTime &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="schedulingBillCode != null and schedulingBillCode != ''">
            AND GROUP_CONCAT(DISTINCT y.scheduling_bill_code SEPARATOR ',') like concat('%',trim(#{schedulingBillCode}),'%')
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>

        group by yi.id
        ) a
        <!--where delFlag=0
        <if test="dispatchDateStart != null and dispatchDateStart != '' and dispatchDateEnd != null and dispatchDateEnd!=''">
            AND dispatchDate &gt;= #{dispatchDateStart,jdbcType=VARCHAR} AND dispatchDate &lt;= #{dispatchDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="finishDateStart != null and finishDateStart != '' and finishDateEnd != null and finishDateEnd!=''">
            AND finishDate &gt;= #{finishDateStart,jdbcType=VARCHAR} AND finishDate &lt;= #{finishDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND operTime &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND operTime &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND companyId in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND costStatus = #{costStatus}
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND feeFlag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND carrierName like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="schedulingBillCode != null and schedulingBillCode != ''">
            AND schedulingBillCode like concat('%',trim(#{schedulingBillCode}),'%')
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND expensesCode like concat('%',trim(#{expensesCode}),'%')
        </if>
        -->
        order by createTime desc
    </select>

    <select id="selectOrderBill" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">
        select * from
        (SELECT
        y.id yfbillId
        ,y.pk_id pkId
        ,null expensesId
        ,1 AS codeType
        ,null AS expenses_type
        ,y.scheduling_bill_code schedulingBillCode
        ,IF(y.fail_remark is not null and y.fail_remark!='', 3 ,y.cost_status) as costStatus
        ,null chargeType
        ,null expensesCode
        ,DATE_FORMAT(y.dispatch_date,'%Y-%m-%d %H:%i:%s') dispatchDates
        ,DATE_FORMAT(y.finish_date,'%Y-%m-%d %H:%i:%s') finishDates
        ,y.company_id companyId
        ,null ruleName
        ,0 freight
        ,0 deliveryFee
        ,0 outboundsortingFee
        ,0 shortbargeFee
        ,0 superframesFee
        ,0 excessFee
        ,0 reduceFee
        ,0 ultrafarFee
        ,0 exceptionFee
        ,0 returnFee
        ,0 adjustFee
        ,0 otherCost1
        ,0 otherCost2
        ,0 otherCost3
        ,0 otherCost4
        ,0 otherCost5
        ,0 otherCost6
        ,0 otherCost7
        ,0 otherCost8
        ,0 otherCost9
        ,0 otherCost10
        ,0 otherCost11
        ,0 otherCost12
        ,IFNULL(y.total_number,0) totalNumber
        ,IFNULL(y.total_weight,0) totalWeight
        ,IFNULL(y.total_volume,0) totalVolume
        ,IFNULL(y.cargo_value,0) cargoValue
        ,null feeFlag
        ,null remarks
        ,null automaticBillingRemark
        ,null adjustRemark
        ,null billDate
        ,null expensesBy
        ,null expensesTimes
        ,y.billing_status billingStatus
        ,null billCode
        ,y.del_flag delFlag
        ,null operTime
        ,y.carrier_code carrierCode
        ,y.carrier_name carrierName
        ,y.network_code networkCode
        ,IFNULL(y.total_boxes,0) totalBoxes
        ,y.transport_type transportType
        ,y.delivery_mode deliveryMode
        ,y.line_code lineCode
        ,y.line_name lineName
        ,y.base_stores baseStores
        ,y.base_kilometer baseKilometer
        ,y.total_kilometer totalKilometer
        ,y.number_loading_points numberLoadingPoints
        ,y.number_unloading_points numberUnloadingPoints
        ,y.total_votenumber totalVotenumber
        ,y.driver
        ,y.car_code carCode
        ,y.car_type carType
        ,y.car_model carModel
        ,y.head_office_times headOfficeTimes
        ,y.dispatch_date dispatchDate
        ,y.finish_date finishDate
        ,y.create_time createTime
        ,y.virtual_order_no virtualOrderNo
        ,y.project_quote projectQuote
        ,null costDimension
        ,mc.client_code clientCode
        ,mc.client_name clientName
        ,y.province_origin provinceOrigin
        ,y.originating_city originatingCity
        ,y.originating_area originatingArea
        ,y.destination_province destinationProvince
        ,y.destination_city destinationCity
        ,y.destination_area destinationArea
        ,0 baseCostTotal
        ,0 otherCostTotal
        ,0 totalFee
        ,null costClientId
        ,DATE_FORMAT(y.create_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,y.tline_code tlineCode
        ,y.tline_name tlineName
        ,null billId
        ,null billType
        ,y.vehicle_temperature_type as vehicleTemperatureType
        ,y.order_no as orderNo
        ,y.fail_remark as failRemark
        ,y.create_by createBy
        ,y.express_no expressNo
        ,null as extraField1
        ,y.original_order_type as originalOrderType
        ,null as yfcostId
        ,y.is_to_client AS isToClient
        FROM bms_yfbillcodeinfo y
        left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1' and y.transport_mode = '2'
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="dispatchDateStart != null and dispatchDateStart != '' and dispatchDateEnd != null and dispatchDateEnd!=''">
            AND y.dispatch_date &gt;= #{dispatchDateStart,jdbcType=VARCHAR} AND y.dispatch_date &lt;= #{dispatchDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="finishDateStart != null and finishDateStart != '' and finishDateEnd != null and finishDateEnd!=''">
            AND y.finish_date &gt;= #{finishDateStart,jdbcType=VARCHAR} AND y.finish_date &lt;= #{finishDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="warehouseCompanyId != null and warehouseCompanyId.size>0 ">
            AND y.company_id in
            <foreach collection="warehouseCompanyId" item="warehouseCompanyId" open="(" separator="," close=")">
                #{warehouseCompanyId}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != '' and costStatus != 3">
            AND y.cost_status = #{costStatus} AND y.fail_remark is null
        </if>
        <if test="costStatus != null and costStatus != '' and costStatus == 3">
            AND y.fail_remark IS NOT NULL
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="schedulingBillCode != null and schedulingBillCode.size>0 ">
            AND y.scheduling_bill_code in
            <foreach collection="schedulingBillCode" item="schedulingBillCode" open="(" separator="," close=")">
                #{schedulingBillCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND y.delivery_mode = #{deliveryMode}
        </if>
        <if test="transportType != null and transportType != ''">
            AND y.transport_type = #{transportType}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        <if test="virtualOrderNo != null and virtualOrderNo != ''">
            AND y.virtual_order_no like concat('%',trim(#{virtualOrderNo}),'%')
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND y.tline_code like concat('%',trim(#{lineCode}),'%')
        </if>
        <if test="lineName != null and lineName != ''">
            AND y.tline_name like concat('%',trim(#{lineName}),'%')
        </if>
        <if test="createBy != null and createBy != ''">
            AND y.create_by like concat('%',trim(#{createBy}),'%')
        </if>
        <if test="expressNo != null and expressNo != ''">
            AND y.express_no like concat('%',trim(#{expressNo}),'%')
        </if>
        <if test="originalOrderType != null">
            AND y.original_order_type = #{originalOrderType}
        </if>
        union
        SELECT
        GROUP_CONCAT(DISTINCT bym.yfbill_id SEPARATOR ',') yfbillId
        ,yf.pk_id pkId
        ,bymi.id expensesId
        ,null AS codeType
        ,bymi.expenses_type AS expensesType
        ,ym.business_code schedulingBillCode
        ,1 costStatus
        ,bymi.charge_type chargeType
        ,bymi.expenses_code expensesCode
        ,DATE_FORMAT(ym.dispatch_date,'%Y-%m-%d %H:%i:%s') dispatchDates
        ,DATE_FORMAT(IFNULL(ym.finish_date,bymi.business_time) ,'%Y-%m-%d %H:%i:%s') finishDates
        ,bymi.company_id companyId
        ,bymi.rule_name ruleName
        ,IFNULL(bymi.freight,0) freight
        ,IFNULL(bymi.delivery_fee,0) deliveryFee
        ,IFNULL(bymi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(bymi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(bymi.superframes_fee,0) superframesFee
        ,IFNULL(bymi.excess_fee,0) excessFee
        ,IFNULL(bymi.reduce_fee,0) reduceFee
        ,IFNULL(bymi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(bymi.exception_fee,0) exceptionFee
        ,IFNULL(bymi.return_fee,0) returnFee
        ,IFNULL(bymi.adjust_fee,0) adjustFee
        ,IFNULL(bymi.other_cost1,0) otherCost1
        ,IFNULL(bymi.other_cost2,0) otherCost2
        ,IFNULL(bymi.other_cost3,0) otherCost3
        ,IFNULL(bymi.other_cost4,0) otherCost4
        ,IFNULL(bymi.other_cost5,0) otherCost5
        ,IFNULL(bymi.other_cost6,0) otherCost6
        ,IFNULL(bymi.other_cost7,0) otherCost7
        ,IFNULL(bymi.other_cost8,0) otherCost8
        ,IFNULL(bymi.other_cost9,0) otherCost9
        ,IFNULL(bymi.other_cost10,0) otherCost10
        ,IFNULL(bymi.other_cost11,0) otherCost11
        ,IFNULL(bymi.other_cost12,0) otherCost12
        ,IFNULL(bymi.total_number,0) totalNumber
        ,IFNULL(bymi.total_weight,0) totalWeight
        ,IFNULL(bymi.total_volume,0) totalVolume
        ,IFNULL(ym.cargo_value,0) cargoValue
        ,bymi.fee_flag feeFlag
        ,bymi.remarks remarks
        ,ym.automatic_billing_remark automaticBillingRemark
        ,bymi.adjust_remark adjustRemark
#         , GROUP_CONCAT(DISTINCT CONCAT(IFNULL(bymi.adjust_remark, ''), IFNULL(yi.adjust_remark, '')) SEPARATOR ',') AS adjustRemark
        ,bymi.bill_date billDate
        ,bymi.oper_by expensesBy
        ,DATE_FORMAT(bymi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when bymi.bill_id is not null then 1 else 0 end billingStatus
        ,IFNULL(ybm2.bill_code,ybm.bill_code) billCode
        ,bymi.del_flag delFlag
        ,bymi.oper_time operTime
        ,bymi.carrier_code carrierCode
        ,bymi.carrier_name carrierName
        ,yf.network_code networkCode
        ,IFNULL(bymi.total_boxes,0) totalBoxes
        ,yf.transport_type transportType
        ,yf.delivery_mode deliveryMode
        ,IFNULL(ym.line_code,yf.line_code) lineCode
        ,yf.line_name lineName
        ,yf.base_stores baseStores
        ,yf.base_kilometer baseKilometer
        ,yf.total_kilometer totalKilometer
        ,yf.number_loading_points numberLoadingPoints
        ,yf.number_unloading_points numberUnloadingPoints
        ,IFNULL(yf.total_votenumber,sum(yfm.total_votenumber)) totalVotenumber
        ,yf.driver
        ,yf.car_code carCode
        ,yf.car_type carType
        ,yf.car_model carModel
        ,yf.head_office_times headOfficeTimes
        ,ym.dispatch_date dispatchDate
        ,IFNULL(ym.finish_date,bymi.business_time) finishDate
        ,yf.create_time createTime
        ,yf.virtual_order_no virtualOrderNo
        ,yf.project_quote projectQuote
        ,bymi.cost_dimension costDimension
        ,mc.client_code clientCode
        ,mc.client_name clientName
        ,yf.province_origin provinceOrigin
        ,yf.originating_city originatingCity
        ,yf.originating_area originatingArea
        ,yf.destination_province destinationProvince
        ,yf.destination_city destinationCity
        ,yf.destination_area destinationArea
        ,(IFNULL(bymi.freight,0)
        +IFNULL(bymi.delivery_fee,0)
        +IFNULL(bymi.outboundsorting_fee,0)
        +IFNULL(bymi.shortbarge_fee,0)
        +IFNULL(bymi.superframes_fee,0)
        +IFNULL(bymi.excess_fee,0)
        +IFNULL(bymi.ultrafar_fee,0)
        +IFNULL(bymi.exception_fee,0)
        +IFNULL(bymi.adjust_fee,0)
        +IFNULL(bymi.reduce_fee,0)
        +IFNULL(bymi.return_fee,0)) baseCostTotal
        ,(IFNULL(bymi.other_cost1,0)
        +IFNULL(bymi.other_cost2,0)
        +IFNULL(bymi.other_cost3,0)
        +IFNULL(bymi.other_cost4,0)
        +IFNULL(bymi.other_cost5,0)
        +IFNULL(bymi.other_cost6,0)
        +IFNULL(bymi.other_cost7,0)
        +IFNULL(bymi.other_cost8,0)
        +IFNULL(bymi.other_cost9,0)
        +IFNULL(bymi.other_cost10,0)
        +IFNULL(bymi.other_cost11,0)
        +IFNULL(bymi.other_cost12,0)  ) otherCostTotal
        ,(IFNULL(bymi.freight,0)
        +IFNULL(bymi.delivery_fee,0)
        +IFNULL(bymi.outboundsorting_fee,0)
        +IFNULL(bymi.shortbarge_fee,0)
        +IFNULL(bymi.superframes_fee,0)
        +IFNULL(bymi.excess_fee,0)
        +IFNULL(bymi.ultrafar_fee,0)
        +IFNULL(bymi.exception_fee,0)
        +IFNULL(bymi.adjust_fee,0)
        +IFNULL(bymi.reduce_fee,0)
        +IFNULL(bymi.return_fee,0))+
        (IFNULL(bymi.other_cost1,0)
        +IFNULL(bymi.other_cost2,0)
        +IFNULL(bymi.other_cost3,0)
        +IFNULL(bymi.other_cost4,0)
        +IFNULL(bymi.other_cost5,0)
        +IFNULL(bymi.other_cost6,0)
        +IFNULL(bymi.other_cost7,0)
        +IFNULL(bymi.other_cost8,0)
        +IFNULL(bymi.other_cost9,0)
        +IFNULL(bymi.other_cost10,0)
        +IFNULL(bymi.other_cost11,0)
        +IFNULL(bymi.other_cost12,0) )  as totalFee
        ,bymi.client_id costClientId
        ,DATE_FORMAT(yf.create_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,case when IFNULL(ym.code_count,1)=1 then yf.tline_code else ym.tline_code end tlineCode
        ,case when IFNULL(ym.code_count,1)=1 then yf.tline_name else ym.tline_name end tlineName
        ,bymi.bill_id billId
        ,ybm.bill_type billType
        ,yf.vehicle_temperature_type as vehicleTemperatureType
        ,yf.order_no as orderNo
        ,null as failRemark
        ,IFNULL(bymi.create_by,bymi.oper_by) createBy
        ,yfm.express_no expressNo
        ,IF(bymi.extra_field1 IS NULL, SUM(yfm.total_boxes), bymi.extra_field1) AS extraField1
        ,yf.original_order_type originalOrderType
        ,yi.id as yfcostId
        ,yf.is_to_client AS isToClient
        from bms_yfcost_info yi
        left join bms_yfcost_main_info bymi on bymi.id = yi.main_expense_id and bymi.del_flag = '0'
        left join bms_yfcost_extend ym on ym.main_expense_id = bymi.id and ym.del_flag = '0'
        left join bms_yfexpenses_middle bym on bym.main_expense_id = bymi.id and bym.del_flag = '0'
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_yfbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on yi.client_id=mc.id
        left join bms_yfbillcodeinfo yf on yf.id=bym.yfbill_id and IFNULL(ym.code_count,1)=1
        left join bms_yfbillcodeinfo yfm on yfm.id=bym.yfbill_id and yfm.del_flag=0
        where bymi.del_flag = 0 and yi.expenses_type=1
        <if test="finishDateStart != null and finishDateStart != '' and finishDateEnd != null and finishDateEnd!=''">
            AND yi.finish_date &gt;= #{finishDateStart,jdbcType=VARCHAR} AND yi.finish_date &lt;= #{finishDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="dispatchDateStart != null and dispatchDateStart != '' and dispatchDateEnd != null and dispatchDateEnd!=''">
            AND yi.dispatch_date &gt;= #{dispatchDateStart,jdbcType=VARCHAR} AND yi.dispatch_date &lt;= #{dispatchDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND yi.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="warehouseCompanyId != null and warehouseCompanyId.size>0 ">
            AND yi.company_id in
            <foreach collection="warehouseCompanyId" item="warehouseCompanyId" open="(" separator="," close=")">
                #{warehouseCompanyId}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND yi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="schedulingBillCode != null and schedulingBillCode.size>0 ">
            AND (
            <foreach collection="schedulingBillCode" item="schedulingBillCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{schedulingBillCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND bymi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND yf.delivery_mode = #{deliveryMode}
        </if>
        <if test="transportType != null and transportType != ''">
            AND yf.transport_type = #{transportType}
        </if>
        <if test="virtualOrderNo != null and virtualOrderNo != ''">
            AND yf.virtual_order_no like concat('%',trim(#{virtualOrderNo}),'%')
        </if>
        <if test="originalOrderType != null">
            AND yf.original_order_type = #{originalOrderType}
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND (case when IFNULL(ym.code_count,1)=1 then yf.tline_code else ym.tline_code end) like concat('%',trim(#{lineCode}),'%')
        </if>
        <if test="lineName != null and lineName != ''">
            AND (case when IFNULL(ym.code_count,1)=1 then yf.tline_name else ym.tline_name end) like concat('%',trim(#{lineName}),'%')
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        <if test="createBy != null and createBy != ''">
            AND IFNULL(bymi.create_by,bymi.oper_by) like concat('%',trim(#{createBy}),'%')
        </if>
        <if test="expressNo != null and expressNo != ''">
            AND yfm.express_no like concat('%',trim(#{expressNo}),'%')
        </if>
        group by yi.main_expense_id
        ) a
        order by createTime desc,schedulingBillCode desc
    </select>



    <select id="selectOrderBilledPage" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">
        SELECT
        bym.yfbill_id yfbillId ,
        yf.pk_id pkId ,
        yi.id expensesId ,
        ym.business_code schedulingBillCode ,
        1 costStatus ,
        yi.charge_type chargeType ,
        yi.expenses_code expensesCode ,
        DATE_FORMAT(ym.dispatch_date, '%Y-%m-%d %H:%i:%s') dispatchDates ,
        DATE_FORMAT(IFNULL(ym.finish_date, bymi.business_time) , '%Y-%m-%d %H:%i:%s') finishDates ,
        yi.company_id companyId , yi.rule_name ruleName ,
        IFNULL(yi.freight, 0) freight ,
        IFNULL(yi.delivery_fee,
        0) deliveryFee ,
        IFNULL(yi.outboundsorting_fee,
        0) outboundsortingFee ,
        IFNULL(yi.shortbarge_fee,
        0) shortbargeFee ,
        IFNULL(yi.superframes_fee,
        0) superframesFee ,
        IFNULL(yi.excess_fee,
        0) excessFee ,
        IFNULL(yi.reduce_fee,
        0) reduceFee ,
        IFNULL(yi.ultrafar_fee,
        0) ultrafarFee ,
        IFNULL(yi.exception_fee,
        0) exceptionFee ,
        IFNULL(yi.return_fee,
        0) returnFee ,
        IFNULL(yi.adjust_fee,
        0) adjustFee ,
        IFNULL(yi.other_cost1,
        0) otherCost1 ,
        IFNULL(yi.other_cost2,
        0) otherCost2 ,
        IFNULL(yi.other_cost3,
        0) otherCost3 ,
        IFNULL(yi.other_cost4,
        0) otherCost4 ,
        IFNULL(yi.other_cost5,
        0) otherCost5 ,
        IFNULL(yi.other_cost6,
        0) otherCost6 ,
        IFNULL(yi.other_cost7,
        0) otherCost7 ,
        IFNULL(yi.other_cost8,
        0) otherCost8 ,
        IFNULL(yi.other_cost9,
        0) otherCost9 ,
        IFNULL(yi.other_cost10,
        0) otherCost10 ,
        IFNULL(yi.other_cost11,
        0) otherCost11 ,
        IFNULL(yi.other_cost12,
        0) otherCost12 ,
        IFNULL(ym.total_number, 0) totalNumber ,
        IFNULL(ym.total_weight, 0) totalWeight ,
        IFNULL(ym.total_volume, 0) totalVolume ,
        IFNULL(ym.cargo_value, 0) cargoValue ,
        yi.fee_flag feeFlag ,
        yi.remarks remarks ,
        ym.automatic_billing_remark automaticBillingRemark ,
        yi.adjust_remark adjustRemark ,
        yi.bill_date billDate ,
        yi.oper_by expensesBy ,
        DATE_FORMAT(yi.oper_time, '%Y-%m-%d %H:%i:%s') expensesTimes ,
        case
        when yi.bill_id is not null then 1
        else 0
        end billingStatus ,
        IFNULL(ybm2.bill_code, ybm.bill_code) billCode ,
        yi.del_flag delFlag ,
        yi.oper_time operTime ,
        yi.carrier_code carrierCode ,
        yi.carrier_name carrierName ,
        yf.network_code networkCode ,
        IFNULL(ym.total_boxes, 0) totalBoxes ,
        yf.transport_type transportType ,
        yf.delivery_mode deliveryMode ,
        IFNULL(ym.line_code, yf.line_code) lineCode ,
        yf.line_name lineName ,
        yf.base_stores baseStores ,
        yf.base_kilometer baseKilometer ,
        yf.total_kilometer totalKilometer ,
        yf.number_loading_points numberLoadingPoints ,
        yf.number_unloading_points numberUnloadingPoints ,
        IFNULL(yf.total_votenumber,
        sum(yfm.total_votenumber)) totalVotenumber ,
        yf.driver ,
        yf.car_code carCode ,
        yf.car_type carType ,
        yf.car_model carModel ,
        yf.head_office_times headOfficeTimes ,
        ym.dispatch_date dispatchDate ,
        IFNULL(ym.finish_date,
        bymi.business_time) finishDate ,
        yf.create_time createTime ,
        yf.virtual_order_no virtualOrderNo ,
        yf.project_quote projectQuote ,
        bymi.cost_dimension costDimension ,
        mc.client_code clientCode ,
        mc.client_name clientName ,
        yf.province_origin provinceOrigin ,
        yf.originating_city originatingCity ,
        yf.originating_area originatingArea ,
        yf.destination_province destinationProvince ,
        yf.destination_city destinationCity ,
        yf.destination_area destinationArea ,
        (IFNULL(yi.freight,
        0) +IFNULL(yi.delivery_fee,
        0) +IFNULL(yi.outboundsorting_fee,
        0) +IFNULL(yi.shortbarge_fee,
        0) +IFNULL(yi.superframes_fee,
        0) +IFNULL(yi.excess_fee,
        0) +IFNULL(yi.ultrafar_fee,
        0) +IFNULL(yi.exception_fee,
        0) +IFNULL(yi.adjust_fee,
        0) +IFNULL(yi.reduce_fee,
        0) +IFNULL(yi.return_fee,
        0)) baseCostTotal ,
        (IFNULL(yi.other_cost1,
        0) +IFNULL(yi.other_cost2,
        0) +IFNULL(yi.other_cost3,
        0) +IFNULL(yi.other_cost4,
        0) +IFNULL(yi.other_cost5,
        0) +IFNULL(yi.other_cost6,
        0) +IFNULL(yi.other_cost7,
        0) +IFNULL(yi.other_cost8,
        0) +IFNULL(yi.other_cost9,
        0) +IFNULL(yi.other_cost10,
        0) +IFNULL(yi.other_cost11,
        0) +IFNULL(yi.other_cost12,
        0) ) otherCostTotal ,
        (IFNULL(yi.freight,
        0) +IFNULL(yi.delivery_fee,
        0) +IFNULL(yi.outboundsorting_fee,
        0) +IFNULL(yi.shortbarge_fee,
        0) +IFNULL(yi.superframes_fee,
        0) +IFNULL(yi.excess_fee,
        0) +IFNULL(yi.ultrafar_fee,
        0) +IFNULL(yi.exception_fee,
        0) +IFNULL(yi.adjust_fee,
        0) +IFNULL(yi.reduce_fee,
        0) +IFNULL(yi.return_fee,
        0))+ (IFNULL(yi.other_cost1,
        0) +IFNULL(yi.other_cost2,
        0) +IFNULL(yi.other_cost3,
        0) +IFNULL(yi.other_cost4,
        0) +IFNULL(yi.other_cost5,
        0) +IFNULL(yi.other_cost6,
        0) +IFNULL(yi.other_cost7,
        0) +IFNULL(yi.other_cost8,
        0) +IFNULL(yi.other_cost9,
        0) +IFNULL(yi.other_cost10,
        0) +IFNULL(yi.other_cost11,
        0) +IFNULL(yi.other_cost12,
        0) ) as totalFee ,
        yi.client_id costClientId ,
        DATE_FORMAT(yf.create_time, '%Y-%m-%d %H:%i:%s') createTimes ,
        case
        when IFNULL(ym.code_count, 1)=1 then yf.tline_code
        else ym.tline_code
        end tlineCode ,
        case
        when IFNULL(ym.code_count, 1)=1 then yf.tline_name
        else ym.tline_name
        end tlineName ,
        yi.bill_id billId ,
        ybm.bill_type billType ,
        yf.vehicle_temperature_type as vehicleTemperatureType ,
        yf.order_no as orderNo ,
        null as failRemark ,
        CASE yfm.cost_status
        WHEN 1 THEN IFNULL(bymi.create_by,bymi.oper_by)
        ELSE yfm.create_by END createBy,
        yfm.express_no expressNo ,
        IF(yi.extra_field1 IS NULL, SUM(yfm.total_boxes), yi.extra_field1) AS extraField1 ,
        yf.original_order_type originalOrderType ,
        yi.id as yfcostId ,
        bymi.id AS mainExpenseId ,
        bymi.expenses_code AS mainExpenseCode,
        yi.settle_type AS settleType,
        yi.settle_amount as settleAmount,
        yi.settle_main_id AS settleMainId,
        mcr.carrier_name AS settleMainName,
        yfm.carrier_code AS mainCarrierCode,
        yfm.carrier_name AS mainCarrierName
        from bms_yfcost_info yi
        left join bms_yfcost_main_info bymi on bymi.id = yi.main_expense_id and bymi.del_flag = '0'
        left join bms_yfcost_extend ym on ym.main_expense_id = bymi.id and ym.del_flag = '0'
        left join bms_yfexpenses_middle bym on bym.main_expense_id=bymi.id and bym.del_flag = '0'
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_yfbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on yi.client_id=mc.id
        left join bms_carrierinfo mcr on mcr.id = yi.settle_main_id
        left join bms_yfbillcodeinfo yf on yf.id=bym.yfbill_id and IFNULL(ym.code_count,1)=1
        left join bms_yfbillcodeinfo yfm on yfm.id=bym.yfbill_id and yfm.del_flag=0
        where yi.del_flag=0 and yi.expenses_type=1
        <if test="finishDateStart != null and finishDateStart != '' and finishDateEnd != null and finishDateEnd!=''">
            AND yi.finish_date &gt;= #{finishDateStart,jdbcType=VARCHAR} AND yi.finish_date &lt;= #{finishDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="dispatchDateStart != null and dispatchDateStart != '' and dispatchDateEnd != null and dispatchDateEnd!=''">
            AND yi.dispatch_date &gt;= #{dispatchDateStart,jdbcType=VARCHAR} AND yi.dispatch_date &lt;= #{dispatchDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND yi.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="warehouseCompanyId != null and warehouseCompanyId.size>0 ">
            AND yi.company_id in
            <foreach collection="warehouseCompanyId" item="warehouseCompanyId" open="(" separator="," close=")">
                #{warehouseCompanyId}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND yi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="schedulingBillCode != null and schedulingBillCode.size>0 ">
            AND (
            <foreach collection="schedulingBillCode" item="schedulingBillCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{schedulingBillCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="mainExpenseCode != null and mainExpenseCode != ''">
            AND bymi.expenses_code like concat('%',trim(#{mainExpenseCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND yf.delivery_mode = #{deliveryMode}
        </if>
        <if test="transportType != null and transportType != ''">
            AND yf.transport_type = #{transportType}
        </if>
        <if test="virtualOrderNo != null and virtualOrderNo != ''">
            AND yf.virtual_order_no like concat('%',trim(#{virtualOrderNo}),'%')
        </if>
        <if test="originalOrderType != null">
            AND yf.original_order_type = #{originalOrderType}
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND (case when IFNULL(ym.code_count,1)=1 then yf.tline_code else ym.tline_code end) like concat('%',trim(#{lineCode}),'%')
        </if>
        <if test="lineName != null and lineName != ''">
            AND (case when IFNULL(ym.code_count,1)=1 then yf.tline_name else ym.tline_name end) like concat('%',trim(#{lineName}),'%')
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        <if test="createBy != null and createBy != ''">
            AND (CASE yfm.cost_status
            WHEN 1 THEN IFNULL(bymi.create_by,bymi.oper_by)
            ELSE yfm.create_by END ) like concat('%',trim(#{createBy}),'%')
        </if>
        <if test="expressNo != null and expressNo != ''">
            AND yfm.express_no like concat('%',trim(#{expressNo}),'%')
        </if>
        <if test="settleType !=null">
            AND yi.settle_type = #{settleType}
        </if>
        GROUP BY yi.id
        ORDER BY yi.oper_time DESC,yi.main_expense_id DESC
    </select>




    <select id="selectStorageBill" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDto">
        select * from
        (SELECT
        y.id yfbillId
        ,null expensesId
        ,null as yfcostId
        ,y.code_type codeType
        ,y.relate_code relateCode
        ,IF(y.fail_remark is not null and y.fail_remark!='', 3, y.cost_status) as costStatus
        ,null chargeType
        ,null expensesCode
        ,DATE_FORMAT(y.stockoper_time,'%Y-%m-%d %H:%i:%s') stockoperTimes
        ,y.company_id companyId
        ,null ruleName
        ,0 freight
        ,0 deliveryFee
        ,0 outboundsortingFee
        ,0 shortbargeFee
        ,0 superframesFee
        ,0 excessFee
        ,0 ultrafarFee
        ,0 exceptionFee
        ,0 returnFee
        ,0 adjustFee
        ,0 otherCost1
        ,0 otherCost2
        ,0 otherCost3
        ,0 otherCost4
        ,0 otherCost5
        ,0 otherCost6
        ,0 otherCost7
        ,0 otherCost8
        ,0 otherCost9
        ,0 otherCost10
        ,0 otherCost11
        ,0 otherCost12
        ,0 reduceFee
        ,null feeFlag
        ,null remarks
        ,null automaticBillingRemark
        ,null adjustRemark
        ,null billDate
        ,null expensesBy
        ,null expensesTimes
        ,y.billing_status billingStatus
        ,null billCode
        ,y.del_flag delFlag
        ,null operTime
        ,mc1.id as carrierId
        ,y.carrier_code carrierCode
        ,y.carrier_name carrierName
        ,y.network_code networkCode
        ,y.warehouse_code warehouseCode
        ,y.warehouse_name warehouseName
        ,IFNULL(y.sku_number,0) totalNumber
        ,IFNULL(y.weight,0) totalWeight
        ,IFNULL(y.volume,0) totalVolume
        ,IFNULL(y.cargo_value,0) cargoValue
        ,IFNULL(y.total_boxes,0) totalBoxes
        ,y.warehouse_area warehouseArea
        ,y.stockoper_time stockoperTime
        ,y.oper_time operTime2
        ,null instorageTime
        ,null instorageTimes
        ,null temperatureType
        ,null aqty
        ,y.pallet_number palletNumber
        ,null costDimension
        ,mc.client_code clientCode
        ,mc.client_name clientName
        ,0 baseCostTotal
        ,0 otherCostTotal
        ,DATE_FORMAT(y.oper_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,null billId
        ,null billType
        ,y.transport_type transportTypeApi
        ,y.cw_full_cases cwFullCases
        ,y.cw_split_cases cwSplitCases
        ,y.ld_full_cases ldFullCases
        ,y.ld_split_cases ldSplitCases

        ,0 isIncrement -- 是否为增值费0否1是
        ,null feeTypeFirst -- 计费大类
        ,null totalQuantity -- 附加费总数量
        ,null price -- 附加费单价
        ,y.order_date orderDate
        ,DATE_FORMAT(y.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,y.store_code as storeCode
        ,y.receiving_store  as storeName
        ,y.order_type as orderType
        ,y.cw_pallet_number as cwPalletNumber
        ,y.lc_pallet_number as lcPalletNumber
        ,y.ld_pallet_number as ldPalletNumber
        ,y.order_no as orderNo
        ,y.platform_code as platformCode
        ,y.destination_province as destinationProvince
        ,y.destination_city as destinationCity
        ,y.destination_area as destinationArea
        ,y.fail_remark as failRemark
        ,CASE y.order_source WHEN 1 THEN  y.import_by ELSE NULL END importBy
        ,y.is_timeout as isTimeout
        ,null as extraField1
        ,null as mainExpenseCode
        ,null as mainExpenseId
        FROM bms_yfstock_codeinfo y
        left join bms_clientinfo mc on y.client_id=mc.id
        left join bms_carrierinfo mc1 on y.carrier_code=mc1.carrier_code
        where y.del_flag=0 and y.cost_status!='1'
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.stockoper_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.stockoper_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND y.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != '' and costStatus != 3">
            AND y.cost_status = #{costStatus} AND y.fail_remark is null
        </if>
        <if test="costStatus != null and costStatus == 3">
            AND y.fail_remark is not null
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND y.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        <if test="importBy !=null and importBy!=''">
            AND (CASE y.order_source WHEN 1 THEN  y.import_by ELSE NULL END )  like concat('%',trim(#{importBy}),'%')
        </if>
        union
        SELECT
        GROUP_CONCAT(DISTINCT bym.yfbill_id SEPARATOR ',') yfbillId
        ,yi.id expensesId
        ,yi.id as yfcostId
        ,case yi.expenses_type when 2 then 1 when 3 then 2 when 4 then 3 end codeType
        ,ym.business_code relateCode
        ,1 costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,DATE_FORMAT(yi.business_time,'%Y-%m-%d %H:%i:%s') stockoperTimes
        ,yi.company_id companyId
        ,yi.rule_name ruleName
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.adjust_fee,0) adjustFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,IFNULL(yi.reduce_fee,0) reduceFee
        ,yi.fee_flag feeFlag
        ,case when yi.is_increment=1 then yi.other_fee_remark
        else yi.remarks end as  remarks
        ,ym.automatic_billing_remark automaticBillingRemark
        ,yi.adjust_remark adjustRemark
        ,yi.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,IFNULL(ybm2.bill_code,ybm.bill_code) billCode
        ,yi.del_flag delFlag
        ,case when yi.is_increment=1 then yi.fee_create_ate
        else yi.oper_time end as operTime
        ,mc1.id as carrierId
        ,yi.carrier_code carrierCode
        ,yi.carrier_name carrierName
        ,null networkCode
        ,ym.warehouse_code warehouseCode
        ,ym.warehouse_name warehouseName
        ,IFNULL(ym.total_number,0) totalNumber
        ,IFNULL(ym.total_weight,0) totalWeight
        ,IFNULL(ym.total_volume,0) totalVolume
        ,IFNULL(ym.cargo_value,0) cargoValue
        ,IFNULL(ym.total_boxes,0) totalBoxes
        ,null warehouseArea
        ,yi.business_time stockoperTime
        ,yi.oper_time operTime2
        ,null instorageTime
        ,null instorageTimes
        ,null temperatureType
        ,stk.aqty aqty
        ,CASE
        WHEN yi.expenses_type in (2,3) then byc.pallet_number
        WHEN yi.expenses_type in (4,5) then stk.pallet_number
        END as palletNumber
        ,yi.cost_dimension costDimension
        ,mc.client_code clientCode
        ,mc.client_name clientName
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.reduce_fee,0)) baseCostTotal
        ,(IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  ) otherCostTotal
        ,case
        when yi.expenses_type in (2,3) then DATE_FORMAT(byc.oper_time,'%Y-%m-%d %H:%i:%s')
        when yi.expenses_type = 4 then DATE_FORMAT(stk.oper_time,'%Y-%m-%d %H:%i:%s')
        end createTimes
        ,yi.bill_id billId
        ,ybm.bill_type billType
        ,byc.transport_type transportTypeApi
        ,IF(yi.expenses_type IN (2,3),SUM(byc.cw_full_cases),NULL) AS cwFullCases
        ,IF(yi.expenses_type IN (2,3),SUM(byc.cw_split_cases),NULL) AS cwSplitCases
        ,IF(yi.expenses_type IN (2,3),SUM(byc.ld_full_cases),NULL) AS ldFullCases
        ,IF(yi.expenses_type IN (2,3),SUM(byc.ld_split_cases),NULL) AS ldSplitCases
        ,yi.is_increment isIncrement -- 是否为增值费0否1是
        ,yi.fee_type_first feeTypeFirst -- 计费大类
        ,ym.total_quantity totalQuantity -- 附加费总数量
        ,ym.price price -- 附加费单价
        ,byc.order_date orderDate
        ,DATE_FORMAT(byc.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,byc.store_code as storeCode
        ,byc.receiving_store  as storeName
        ,byc.order_type as orderType
        ,byc.cw_pallet_number as cwPalletNumber
        ,byc.lc_pallet_number as lcPalletNumber
        ,byc.ld_pallet_number as ldPalletNumber
        ,byc.order_no as orderNo
        ,byc.platform_code as platformCode
        ,byc.destination_province as destinationProvince
        ,byc.destination_city as destinationCity
        ,byc.destination_area as destinationArea
        ,null as failRemark
        -- ,case yi.expenses_type when 2 then IFNULL(byc.import_by,byc.oper_by) when 3 then IFNULL(byc.import_by,byc.oper_by) when 4 then IFNULL(stk.import_by,stk.oper_by) end importBy
        ,bymi.oper_by importBy
        ,byc.is_timeout as isTimeout
        ,yi.extra_field1 as extraField1
        ,bymi.expenses_code as mainExpenseCode
        ,yi.main_expense_id as mainExpenseId
        from bms_yfcost_main_info bymi
        left join  bms_yfcost_info yi on bymi.id=yi.main_expense_id and yi.del_flag=0
        left join bms_yfcost_extend ym on yi.main_expense_id=ym.main_expense_id and ym.del_flag=0
        left join bms_yfexpenses_middle bym on bym.main_expense_id=yi.main_expense_id and bym.del_flag=0
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_yfbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on yi.client_id=mc.id
        left join bms_yfstockinfo stk on bym.yfbill_id = stk.id and IFNULL(ym.code_count,1)=1
        left join bms_yfstock_codeinfo byc on  byc.id = bym.yfbill_id and ym.code_count=1
        left join bms_carrierinfo mc1 on yi.carrier_code=mc1.carrier_code
        where bymi.del_flag=0
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND yi.business_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND yi.business_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND byc.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND byc.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND yi.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="or" close=")">
                yi.warehouse_code_arr like concat('%',trim(#{warehouseCode}),'%')
            </foreach>
            or yi.is_increment=1
            )
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type-1 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND yi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND ym.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND ym.business_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        <if test="importBy!=null and importBy!=''">
            AND (IFNULL(stk.import_by,stk.oper_by) like concat('%',trim(#{importBy}),'%') or IFNULL(byc.import_by,byc.oper_by) like concat('%',trim(#{importBy}),'%'))
        </if>
        group by bymi.id
        union
        SELECT
        y.id yfbillId
        ,bymi2.id expensesId
        ,yi.id as yfcostId
        ,3 codeType
        ,y.stock_code relateCode
        ,IF(y.fail_remark is not null and y.fail_remark!='', 3, y.cost_status) as costStatus
        ,bymi2.charge_type chargeType
        ,bymi2.expenses_code expensesCode
        ,DATE_FORMAT(y.instorage_time,'%Y-%m-%d %H:%i:%s') stockoperTimes
        ,y.company_id companyId
        ,bymi2.rule_name ruleName
        ,IFNULL(bymi2.freight,0) freight
        ,IFNULL(bymi2.delivery_fee,0) deliveryFee
        ,IFNULL(bymi2.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(bymi2.shortbarge_fee,0) shortbargeFee
        ,IFNULL(bymi2.superframes_fee,0) superframesFee
        ,IFNULL(bymi2.excess_fee,0) excessFee
        ,IFNULL(bymi2.ultrafar_fee,0) ultrafarFee
        ,IFNULL(bymi2.exception_fee,0) exceptionFee
        ,IFNULL(bymi2.return_fee,0) returnFee
        ,IFNULL(bymi2.adjust_fee,0) adjustFee
        ,IFNULL(bymi2.other_cost1,0) otherCost1
        ,IFNULL(bymi2.other_cost2,0) otherCost2
        ,IFNULL(bymi2.other_cost3,0) otherCost3
        ,IFNULL(bymi2.other_cost4,0) otherCost4
        ,IFNULL(bymi2.other_cost5,0) otherCost5
        ,IFNULL(bymi2.other_cost6,0) otherCost6
        ,IFNULL(bymi2.other_cost7,0) otherCost7
        ,IFNULL(bymi2.other_cost8,0) otherCost8
        ,IFNULL(bymi2.other_cost9,0) otherCost9
        ,IFNULL(bymi2.other_cost10,0) otherCost10
        ,IFNULL(bymi2.other_cost11,0) otherCost11
        ,IFNULL(bymi2.other_cost12,0) otherCost12
        ,IFNULL(bymi2.reduce_fee,0) reduceFee
        ,bymi2.fee_flag feeFlag
        ,case when bymi2.is_increment=1 then bymi2.other_fee_remark
        else bymi2.remarks end as  remarks
        ,ye.automatic_billing_remark automaticBillingRemark
        ,bymi2.adjust_remark adjustRemark
        ,bymi2.bill_date billDate
        ,bymi2.oper_by expensesBy
        ,DATE_FORMAT(bymi2.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when bymi2.bill_id is not null then 1 else 0 end billingStatus
        ,ybm.bill_code billCode
        ,y.del_flag delFlag
        ,case when bymi2.is_increment=1 then bymi2.fee_create_ate
        else bymi2.oper_time end as operTime
        ,mc1.id as carrierId
        ,y.carrier_code carrierCode
        ,y.carrier_name carrierName
        ,null networkCode
        ,y.warehouse_code warehouseCode
        ,null warehouseName
        ,IFNULL(y.trust,0) totalNumber
        ,IFNULL(y.weight,0) totalWeight
        ,IFNULL(y.volume,0) totalVolume
        ,null cargoValue
        ,IFNULL(y.total_boxes,0) totalBoxes
        ,y.warehouse_area warehouseArea
        ,y.instorage_time stockoperTime
        ,y.oper_time operTime2
        ,y.instorage_time instorageTime
        ,DATE_FORMAT(y.instorage_time,'%Y-%m-%d %H:%i:%s') instorageTimes
        ,y.temperature_type temperatureType
        ,y.aqty
        ,y.pallet_number palletNumber
        ,bymi2.cost_dimension costDimension
        ,mc.client_code clientCode
        ,mc.client_name clientName
        ,(IFNULL(bymi2.freight,0)
        +IFNULL(bymi2.delivery_fee,0)
        +IFNULL(bymi2.outboundsorting_fee,0)
        +IFNULL(bymi2.shortbarge_fee,0)
        +IFNULL(bymi2.superframes_fee,0)
        +IFNULL(bymi2.excess_fee,0)
        +IFNULL(bymi2.ultrafar_fee,0)
        +IFNULL(bymi2.exception_fee,0)
        +IFNULL(bymi2.return_fee,0)
        +IFNULL(bymi2.reduce_fee,0)) baseCostTotal
        ,(IFNULL(bymi2.other_cost1,0)
        +IFNULL(bymi2.other_cost2,0)
        +IFNULL(bymi2.other_cost3,0)
        +IFNULL(bymi2.other_cost4,0)
        +IFNULL(bymi2.other_cost5,0)
        +IFNULL(bymi2.other_cost6,0)
        +IFNULL(bymi2.other_cost7,0)
        +IFNULL(bymi2.other_cost8,0)
        +IFNULL(bymi2.other_cost9,0)
        +IFNULL(bymi2.other_cost10,0)
        +IFNULL(bymi2.other_cost11,0)
        +IFNULL(bymi2.other_cost12,0)  ) otherCostTotal
        ,DATE_FORMAT(y.oper_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,null billId
        ,null billType
        ,null transportTypeApi
        ,null cwFullCases
        ,null cwSplitCases
        ,null ldFullCases
        ,null ldSplitCases

        ,bymi2.is_increment isIncrement -- 是否为增值费0否1是
        ,bymi2.fee_type_first feeTypeFirst -- 计费大类
        ,ye.total_quantity totalQuantity -- 附加费总数量
        ,ye.price price -- 附加费单价
        ,null orderDate
        ,null orderDates
        ,null as storeCode
        ,null as storeName
        ,null as orderType
        ,null as cwPalletNumber
        ,null as lcPalletNumber
        ,null as ldPalletNumber
        ,null as orderNo
        ,null as platformCode
        ,null as destinationProvince
        ,null as destinationCity
        ,null as destinationArea
        ,y.fail_remark as failRemark
        ,CASE y.order_source WHEN 1 THEN y.import_by ELSE NULL END AS importBy
        ,null as isTimeout
        ,bymi2.extra_field1 as extraField1
        ,null as mainExpenseCode
        ,null as mainExpenseId
        FROM bms_yfstockinfo y
        left join bms_yfexpenses_middle ym on y.id=ym.yfbill_id and ym.del_flag=0
        left join bms_yfcost_info yi on yi.id=ym.expenses_id and yi.del_flag=0
        left join bms_yfcost_main_info bymi2 on bymi2.id=yi.main_expense_id
        left join bms_yfcost_extend ye on yi.id=ye.expenses_id and ye.del_flag=0
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on y.client_id=mc.id
        left join bms_carrierinfo mc1 on y.carrier_code=mc1.carrier_code
        where y.del_flag=0 and y.cost_status!='1'
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.instorage_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.instorage_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND 3 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != '' and costStatus!=3 ">
            AND y.cost_status = #{costStatus} AND y.fail_remark is null
        </if>
        <if test="costStatus != null and costStatus ==3 ">
            AND y.fail_remark is not null
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.stock_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND 1=2
        </if>
        <if test="importBy!=null and importBy!=''">
            and (CASE y.order_source WHEN 1 THEN y.import_by ELSE NULL END) like concat('%',trim(#{importBy}),'%')
        </if>
        ) a
        order by operTime2 desc,relateCode desc
    </select>

    <select id="selectStockinfo" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDto">
        SELECT
        y.id yfbillId
        ,yi.id expensesId
        ,y.stock_code relateCode
        ,y.sku_code skuCode
        ,y.sku_name skuName
        ,DATE_FORMAT(y.instorage_time,'%Y-%m-%d %H:%i:%s') instorageTimes
        ,y.total_boxes totalBoxes
        ,y.odd_boxes oddBoxes
        ,y.box_type boxType
        ,y.temperature_type temperatureType
        ,y.trust
        ,y.weight
        ,y.volume
        ,y.warehouse_area warehouseArea
        ,y.cost_status costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,y.company_id companyId
        ,y.carrier_code carrierCode
        ,y.carrier_name carrierName
        ,yi.rule_name ruleName
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,yi.fee_flag feeFlag
        ,yi.remarks remarks
        ,yi.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,ybm.bill_code billCode
        ,yi.oper_time operTime
        ,y.warehouse_code warehouseCode
        ,3 codeType
        FROM bms_yfstockinfo y left join bms_yfexpenses_middle ym on CONCAT(y.id,'')=ym.yfbill_id and ym.del_flag=0
        left join bms_yfcost_info yi on yi.id=ym.expenses_id and yi.del_flag=0
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        where y.del_flag=0
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="instorageTimeStart != null and instorageTimeStart != '' and instorageTimeEnd != null and instorageTimeEnd!=''">
            AND  y.instorage_time &gt;= #{instorageTimeStart,jdbcType=VARCHAR} AND y.instorage_time &lt;= #{instorageTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="relateCode != null and relateCode != ''">
            AND y.stock_code like concat('%',trim(#{relateCode}),'%')
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        order by y.oper_time desc,relateCode desc
    </select>

    <select id="selectByCode" resultMap="BmsYfbillcodeinfoResult">
        <include refid="selectBmsYfbillcodeinfoVo"/>
        where  del_flag = '0'
        <if test="codes != null and codes.size>0 ">
            and  scheduling_bill_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <select id="selectByJobRelateCode" resultType="com.bbyb.joy.bms.domain.dto.BmsYfjobbillinfo">
        SELECT
        t1.id,
        t1.relate_code AS relateCode,
        t1.scheduling_bill_code AS schedulingBillCode
        FROM bms_yfjobbillinfo t1
        WHERE t1.del_flag = '0'
        <if test="codes != null and codes.size>0 ">
            and relate_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <select id="selectIsBillByCode"  resultType="com.bbyb.joy.bms.domain.dto.charginglogic.DispatchBillingBean">
        SELECT
        yi.id yfcostId,
        yi.expenses_code expensesCode,
        yf.id yfbillId,
        yf.scheduling_bill_code yfbillCode,
        ym.business_code workCode,
        yi.bill_id billId
        from bms_yfcost_info yi
        left join bms_yfcost_extend ym on yi.main_expense_id=ym.main_expense_id and ym.del_flag=0
        left join bms_yfexpenses_middle bym on bym.main_expense_id=yi.main_expense_id and bym.yfbill_type = 1
        left join bms_yfbillcodeinfo yf on yf.id=bym.yfbill_id and yf.del_flag=0
        where yi.del_flag=0
        <if test="codes != null and codes.size>0 ">
            AND yf.scheduling_bill_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <select id="selectByProjectQuote" resultMap="BmsYfbillcodeinfoResult">
        select y.id, y.pk_id, y.scheduling_bill_code,y.virtual_order_no,y.project_quote, y.carrier_code, y.carrier_name, y.company_id, y.network_code,
               y.total_boxes, y.total_number, y.total_weight, y.total_volume, y.cargo_value, y.dispatch_date, y.finish_date, y.transport_type,y.delivery_mode,
               y.line_code, y.line_name, y.base_stores, y.base_kilometer, y.total_kilometer, y.number_loading_points, y.number_unloading_points, y.total_votenumber,
               y.driver, y.car_code, y.car_type, y.car_model, y.head_office_times, y.province_origin, y.originating_City, y.originating_area, y.originating_address,
               y.destination_Province, y.destination_city, y.destination_area, y.destination_address, y.cost_status, y.billing_status, y.create_code, y.create_by,
               y.create_dept_id, y.create_time, y.oper_code, y.oper_by, y.oper_dept_id, y.oper_time, y.del_flag,c.id carrier_id,y.tline_code, y.tline_name,y.hy_order_no,y.express_no
        from bms_yfbillcodeinfo y left join bms_carrierinfo c on y.carrier_code=c.carrier_code
        where y.cost_status!='1' and y.project_quote = #{projectQuote} and y.del_flag = '0'
    </select>

    <delete id="deleteBmsYfBillCodeByIds">
        delete from bms_yfbillcodeinfo where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="deleteBmsYfBillCodeByCodes" parameterType="java.util.List">
        update   bms_yfbillcodeinfo info
        left join bms_yfjobbillinfo job
        on info.virtual_order_no = job.scheduling_bill_code
        left join bms_yfjobbill_goodsinfo goods
        on goods.jobbill_id = job.id
        set info.del_flag =1 ,
        job.del_flag = 1,
        goods.del_flag = 1
        where
        info.scheduling_bill_code in
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
    </update>

    <update id="updateBmsYfbillcodeinfoByIds">
        update bms_yfbillcodeinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null and id != ''">id = #{id},</if>
            <if test="schedulingBillCode != null">scheduling_bill_code = #{schedulingBillCode},</if>
            <if test="carrierCode != null">carrier_code = #{carrierCode},</if>
            <if test="virtualOrderNo != null">virtual_order_no = #{virtualOrderNo},</if>
            <if test="carrierName != null">carrier_name = #{carrierName},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="networkCode != null">network_code = #{networkCode},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="totalNumber != null">total_number = #{totalNumber},</if>
            <if test="totalWeight != null">total_weight = #{totalWeight},</if>
            <if test="totalVolume != null">total_volume = #{totalVolume},</if>
            <if test="cargoValue != null">cargo_value = #{cargoValue},</if>
            <if test="dispatchDate != null">dispatch_date = #{dispatchDate},</if>
            <if test="finishDate != null">finish_date = #{finishDate},</if>
            <if test="lineCode != null">line_code = #{lineCode},</if>
            <if test="transportType != null">transport_type = #{transportType},</if>
            <if test="deliveryMode != null">delivery_mode = #{deliveryMode},</if>
            <if test="lineName != null">line_name = #{lineName},</if>
            <if test="baseStores != null">base_stores = #{baseStores},</if>
            <if test="baseKilometer != null">base_kilometer = #{baseKilometer},</if>
            <if test="totalKilometer != null">total_kilometer = #{totalKilometer},</if>
            <if test="numberLoadingPoints != null">number_loading_points = #{numberLoadingPoints},</if>
            <if test="numberUnloadingPoints != null">number_unloading_points = #{numberUnloadingPoints},</if>
            <if test="totalVotenumber != null">total_votenumber = #{totalVotenumber},</if>
            <if test="driver != null">driver = #{driver},</if>
            <if test="carCode != null">car_code = #{carCode},</if>
            <if test="carType != null">car_type = #{carType},</if>
            <if test="carModel != null">car_model = #{carModel},</if>
            <if test="headOfficeTimes != null">head_office_times = #{headOfficeTimes},</if>
            <if test="provinceOrigin != null">province_origin = #{provinceOrigin},</if>
            <if test="originatingCity != null">originating_City = #{originatingCity},</if>
            <if test="originatingArea != null">originating_area = #{originatingArea},</if>
            <if test="originatingAddress != null">originating_address = #{originatingAddress},</if>
            <if test="destinationProvince != null">destination_Province = #{destinationProvince},</if>
            <if test="destinationCity != null">destination_city = #{destinationCity},</if>
            <if test="destinationArea != null">destination_area = #{destinationArea},</if>
            <if test="destinationAddress != null">destination_address = #{destinationAddress},</if>
            <if test="costStatus != null">cost_status = #{costStatus},</if>
            <if test="billingStatus != null">billing_status = #{billingStatus},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="tlineCode != null">tline_code = #{tlineCode},</if>
            <if test="tlineName != null">tline_name = #{tlineName},</if>
            <if test="vehicleTemperatureType != null">vehicle_temperature_type = #{vehicleTemperatureType},</if>
            <if test="expressNo != null">express_no = #{expressNo},</if>
            </trim>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateBmsYfbillcodeinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfbillcodeinfo">
        update bms_yfbillcodeinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null and id != ''">id = #{id},</if>
            <if test="schedulingBillCode != null">scheduling_bill_code = #{schedulingBillCode},</if>
            <if test="virtualOrderNo != null">virtual_order_no = #{virtualOrderNo},</if>
            <if test="projectQuote != null">project_quote = #{projectQuote},</if>
            <if test="carrierCode != null">carrier_code = #{carrierCode},</if>
            <if test="virtualOrderNo != null">virtual_order_no = #{virtualOrderNo},</if>
            <if test="carrierName != null">carrier_name = #{carrierName},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="networkCode != null">network_code = #{networkCode},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="totalNumber != null">total_number = #{totalNumber},</if>
            <if test="totalWeight != null">total_weight = #{totalWeight},</if>
            <if test="totalVolume != null">total_volume = #{totalVolume},</if>
            <if test="cargoValue != null">cargo_value = #{cargoValue},</if>
            <if test="dispatchDate != null">dispatch_date = #{dispatchDate},</if>
            <if test="finishDate != null">finish_date = #{finishDate},</if>
            <if test="lineCode != null">line_code = #{lineCode},</if>
            <if test="transportType != null">transport_type = #{transportType},</if>
            <if test="deliveryMode != null">delivery_mode = #{deliveryMode},</if>
            <if test="lineName != null">line_name = #{lineName},</if>
            <if test="baseStores != null">base_stores = #{baseStores},</if>
            <if test="baseKilometer != null">base_kilometer = #{baseKilometer},</if>
            <if test="totalKilometer != null">total_kilometer = #{totalKilometer},</if>
            <if test="numberLoadingPoints != null">number_loading_points = #{numberLoadingPoints},</if>
            <if test="numberUnloadingPoints != null">number_unloading_points = #{numberUnloadingPoints},</if>
            <if test="totalVotenumber != null">total_votenumber = #{totalVotenumber},</if>
            <if test="driver != null">driver = #{driver},</if>
            <if test="carCode != null">car_code = #{carCode},</if>
            <if test="carType != null">car_type = #{carType},</if>
            <if test="carModel != null">car_model = #{carModel},</if>
            <if test="headOfficeTimes != null">head_office_times = #{headOfficeTimes},</if>
            <if test="provinceOrigin != null">province_origin = #{provinceOrigin},</if>
            <if test="originatingCity != null">originating_City = #{originatingCity},</if>
            <if test="originatingArea != null">originating_area = #{originatingArea},</if>
            <if test="originatingAddress != null">originating_address = #{originatingAddress},</if>
            <if test="destinationProvince != null">destination_Province = #{destinationProvince},</if>
            <if test="destinationCity != null">destination_city = #{destinationCity},</if>
            <if test="destinationArea != null">destination_area = #{destinationArea},</if>
            <if test="destinationAddress != null">destination_address = #{destinationAddress},</if>
            <if test="costStatus != null">cost_status = #{costStatus},</if>
            <if test="billingStatus != null">billing_status = #{billingStatus},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="bodyOfficeTimes != null">body_office_times = #{bodyOfficeTimes},</if>
            <if test="tlineCode != null">tline_code = #{tlineCode},</if>
            <if test="tlineName != null">tline_name = #{tlineName},</if>
            <if test="vehicleTemperatureType != null">vehicle_temperature_type = #{vehicleTemperatureType},</if>
            <if test="expressNo != null">express_no = #{expressNo},</if>
            </trim>
        where id = #{id}
    </update>
    <insert id="batchSave" parameterType="java.util.List">
        insert into bms_yfbillcodeinfo
        (id, scheduling_bill_code,virtual_order_no,project_quote, carrier_code, carrier_name, company_id, network_code, total_boxes, total_number, total_weight, total_volume,
        cargo_value, dispatch_date, finish_date, transport_type,delivery_mode, line_code, line_name, base_stores, base_kilometer, total_kilometer,
        number_loading_points, number_unloading_points, total_votenumber, driver, car_code, car_type, car_model, head_office_times, province_origin,
        originating_City, originating_area, originating_address, destination_Province, destination_city, destination_area, destination_address, cost_status,
        billing_status, create_code, create_by, create_dept_id, create_time, oper_code, oper_by, oper_dept_id, oper_time, del_flag,client_id,body_office_times, tline_code, tline_name,vehicle_temperature_type,hy_order_no,order_source,store_number,express_no,transport_mode)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.schedulingBillCode},#{item.virtualOrderNo},#{item.projectQuote},#{item.carrierCode},#{item.carrierName},#{item.companyId},#{item.networkCode},#{item.totalBoxes},#{item.totalNumber},
            #{item.totalWeight},#{item.totalVolume},#{item.cargoValue},#{item.dispatchDate},#{item.finishDate},#{item.transportType},#{item.deliveryMode},#{item.lineCode},#{item.lineName},
            #{item.baseStores},#{item.baseKilometer},#{item.totalKilometer},#{item.numberLoadingPoints},#{item.numberUnloadingPoints},#{item.totalVotenumber},#{item.driver},
            #{item.carCode},#{item.carType},#{item.carModel},#{item.headOfficeTimes},#{item.provinceOrigin},#{item.originatingCity},#{item.originatingArea},#{item.originatingAddress},
            #{item.destinationProvince},#{item.destinationCity},#{item.destinationArea},#{item.destinationAddress},#{item.costStatus},#{item.billingStatus},#{item.createCode},
            #{item.createBy},#{item.createDeptId},#{item.createTime}, #{item.operCode},#{item.operBy},#{item.operDeptId},#{item.operTime},#{item.delFlag},#{item.clientId},#{item.bodyOfficeTimes},#{item.tlineCode},#{item.tlineName},#{item.vehicleTemperatureType},#{item.hyOrderNo},#{item.orderSource},#{item.storeNumber},#{item.expressNo},#{item.transportMode})
        </foreach>
    </insert>

    <select id="selectOrderByExpensesIdIfFee" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">
        SELECT
            y.id yfbillId
        FROM bms_yfbillcodeinfo y
        where y.del_flag = '0'  and y.id = #{expensesId}
    </select>

    <select id="selectOrderByExpensesId" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">
        select
            y.id yfbillId
             ,yi.id expensesId
             ,y.scheduling_bill_code schedulingBillCode
             ,y.cost_status costStatus
             ,DATE_FORMAT(y.dispatch_date,'%Y-%m-%d %H:%i:%s') dispatchDates
             ,DATE_FORMAT(y.finish_date,'%Y-%m-%d %H:%i:%s') finishDates
             ,y.company_id companyId
             ,IFNULL(y.total_number,0) totalNumber
             ,IFNULL(y.total_weight,0) totalWeight
             ,IFNULL(y.total_volume,0) totalVolume
             ,IFNULL(y.cargo_value,0) cargoValue
             ,y.del_flag delFlag
             ,y.carrier_code carrierCode
             ,y.carrier_name carrierName
             ,y.network_code networkCode
             ,IFNULL(y.total_boxes,0) totalBoxes
             ,y.transport_type transportType
             ,y.delivery_mode deliveryMode
             ,y.line_code lineCode
             ,y.line_name lineName
             ,y.base_kilometer baseKilometer
             ,y.total_kilometer totalKilometer
             ,y.number_loading_points numberLoadingPoints
             ,y.number_unloading_points numberUnloadingPoints
             ,y.total_votenumber totalVotenumber
             ,y.driver
             ,y.car_code carCode
             ,y.car_type carType
             ,y.car_model carModel
             ,y.head_office_times headOfficeTimes
             ,y.dispatch_date dispatchDate
             ,y.finish_date finishDate
             ,y.create_time createTime
             ,y.province_origin provinceOrigin
             ,y.originating_City originatingCity
             ,y.originating_area originatingArea
             ,y.originating_address originatingAddress
             ,y.destination_Province destinationProvince
             ,y.destination_city destinationCity
             ,y.destination_area destinationArea
             ,y.destination_address destinationAddress
             ,y.virtual_order_no virtualOrderNo
             ,y.body_office_times bodyOfficeTimes
             ,y.client_id clientId
             ,mc.client_name clientName
             ,IFNULL(yi.freight,0) freight
             ,IFNULL(yi.delivery_fee,0) deliveryFee
             ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
             ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
             ,IFNULL(yi.superframes_fee,0) superframesFee
             ,IFNULL(yi.excess_fee,0) excessFee
             ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
             ,IFNULL(yi.exception_fee,0) exceptionFee
             ,IFNULL(yi.return_fee,0) returnFee
             ,IFNULL(yi.adjust_fee,0) adjustFee
             ,IFNULL(yi.other_cost1,0) otherCost1
             ,IFNULL(yi.other_cost2,0) otherCost2
             ,IFNULL(yi.other_cost3,0) otherCost3
             ,IFNULL(yi.other_cost4,0) otherCost4
             ,IFNULL(yi.other_cost5,0) otherCost5
             ,IFNULL(yi.other_cost6,0) otherCost6
             ,IFNULL(yi.other_cost7,0) otherCost7
             ,IFNULL(yi.other_cost8,0) otherCost8
             ,IFNULL(yi.other_cost9,0) otherCost9
             ,IFNULL(yi.other_cost10,0) otherCost10
             ,IFNULL(yi.other_cost11,0) otherCost11
             ,IFNULL(yi.other_cost12,0) otherCost12
             ,yi.expenses_code expensesCode
             ,yi.company_id institutionId
             ,yi.carrier_name  costCarrierName
             ,yi.client_id costClientId
             ,yi.remarks remarks
             ,ye.automatic_billing_remark automaticBillingRemark
             ,mr.total_mileage totalMileage
             -- ,mr.store_num baseStores
             ,y.base_stores as baseStores
             ,y.vehicle_temperature_type as vehicleTemperatureType
             ,y.order_no as orderNo
             ,y.express_no as expressNo
        FROM bms_yfbillcodeinfo y
        LEFT JOIN bms_yfexpenses_middle ym on y.id = ym.yfbill_id and ym.del_flag = 0
        LEFT JOIN bms_yfcost_info yi on yi.main_expense_id = ym.main_expense_id and yi.del_flag = 0
        LEFT JOIN bms_yfcost_extend ye on yi.main_expense_id = ye.main_expense_id and ye.del_flag = 0
        LEFT JOIN bms_clientinfo mc on mc.id = y.client_id
        LEFT JOIN bms_routeinfo mr on mr.route_code = y.line_code
        where y.del_flag=0 AND yi.id = #{expensesId}
    </select>
    <select id="selectOrderByExpensesId2" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">
        select
            y.id yfbillId
             ,yi.id expensesId
             ,y.scheduling_bill_code schedulingBillCode
             ,y.cost_status costStatus
             ,DATE_FORMAT(y.dispatch_date,'%Y-%m-%d %H:%i:%s') dispatchDates
             ,DATE_FORMAT(y.finish_date,'%Y-%m-%d %H:%i:%s') finishDates
             ,y.company_id companyId
             ,IFNULL(y.total_number,0) totalNumber
             ,IFNULL(y.total_weight,0) totalWeight
             ,IFNULL(y.total_volume,0) totalVolume
             ,IFNULL(y.cargo_value,0) cargoValue
             ,y.del_flag delFlag
             ,y.carrier_code carrierCode
             ,y.carrier_name carrierName
             ,y.network_code networkCode
             ,IFNULL(y.total_boxes,0) totalBoxes
             ,y.transport_type transportType
             ,y.delivery_mode deliveryMode
             ,y.line_code lineCode
             ,y.line_name lineName
             ,y.base_kilometer baseKilometer
             ,y.total_kilometer totalKilometer
             ,y.number_loading_points numberLoadingPoints
             ,y.number_unloading_points numberUnloadingPoints
             ,y.total_votenumber totalVotenumber
             ,y.driver
             ,y.car_code carCode
             ,y.car_type carType
             ,y.car_model carModel
             ,y.head_office_times headOfficeTimes
             ,y.dispatch_date dispatchDate
             ,y.finish_date finishDate
             ,y.create_time createTime
             ,y.province_origin provinceOrigin
             ,y.originating_City originatingCity
             ,y.originating_area originatingArea
             ,y.originating_address originatingAddress
             ,y.destination_Province destinationProvince
             ,y.destination_city destinationCity
             ,y.destination_area destinationArea
             ,y.destination_address destinationAddress
             ,y.virtual_order_no virtualOrderNo
             ,y.body_office_times bodyOfficeTimes
             ,y.client_id clientId
             ,mc.client_name clientName
             ,IFNULL(yi.freight,0) freight
             ,IFNULL(yi.delivery_fee,0) deliveryFee
             ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
             ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
             ,IFNULL(yi.superframes_fee,0) superframesFee
             ,IFNULL(yi.excess_fee,0) excessFee
             ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
             ,IFNULL(yi.exception_fee,0) exceptionFee
             ,IFNULL(yi.return_fee,0) returnFee
             ,IFNULL(yi.adjust_fee,0) adjustFee
             ,IFNULL(yi.other_cost1,0) otherCost1
             ,IFNULL(yi.other_cost2,0) otherCost2
             ,IFNULL(yi.other_cost3,0) otherCost3
             ,IFNULL(yi.other_cost4,0) otherCost4
             ,IFNULL(yi.other_cost5,0) otherCost5
             ,IFNULL(yi.other_cost6,0) otherCost6
             ,IFNULL(yi.other_cost7,0) otherCost7
             ,IFNULL(yi.other_cost8,0) otherCost8
             ,IFNULL(yi.other_cost9,0) otherCost9
             ,IFNULL(yi.other_cost10,0) otherCost10
             ,IFNULL(yi.other_cost11,0) otherCost11
             ,IFNULL(yi.other_cost12,0) otherCost12
             ,yi.expenses_code expensesCode
             ,yi.company_id institutionId
             ,yi.carrier_name  costCarrierName
             ,yi.client_id costClientId
             ,yi.remarks remarks
             ,ye.automatic_billing_remark automaticBillingRemark
             ,mr.total_mileage totalMileage
             -- ,mr.store_num baseStores
             ,y.base_stores as baseStores
             ,y.vehicle_temperature_type as vehicleTemperatureType
             ,y.order_no as orderNo
             ,y.express_no as expressNo
        FROM bms_yfbillcodeinfo y
        LEFT JOIN bms_yfexpenses_middle ym on y.id=ym.yfbill_id and ym.del_flag=0
        LEFT JOIN bms_yfcost_info yi on yi.main_expense_id=ym.main_expense_id and yi.del_flag=0
        LEFT JOIN bms_yfcost_extend ye on yi.main_expense_id=ye.main_expense_id and ye.del_flag=0
        LEFT JOIN bms_clientinfo mc on mc.id=y.client_id
        LEFT JOIN bms_routeinfo mr on mr.route_code=y.line_code
        where y.del_flag=0 AND y.id = #{expensesId}
    </select>

    <select id="selectStorageByExpensesId" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDto">
        SELECT y.id                                               yfbillId
             , yi.id                as                          expensesId
             , yi.id                  as                          yfcostId
             , y.code_type                                        codeType
             , y.relate_code                                      relateCode
             , y.cost_status                                      costStatus
             , DATE_FORMAT(y.stockoper_time, '%Y-%m-%d %H:%i:%s') stockoperTimes
             , y.company_id                                       companyId
             , y.del_flag                                         delFlag
             , y.carrier_code                                     carrierCode
             , y.carrier_name                                     carrierName
             , y.network_code                                     networkCode
             , y.warehouse_code                                   warehouseCode
             , ware.warehouse_name                                warehouseName
             , IFNULL(y.trust, 0)                                 trust
             , IFNULL(y.sku_number, 0)                            totalNumber
             , IFNULL(y.weight, 0)                                totalWeight
             , IFNULL(y.volume, 0)                                totalVolume
             , IFNULL(y.cargo_value, 0)                           cargoValue
             , IFNULL(y.total_boxes, 0)                           totalBoxes
             , IFNULL(y.split_total_number, 0)                    oddBoxes
             , y.warehouse_area                                   warehouseArea
             , y.stockoper_time                                   stockoperTime
             , y.oper_time                                        operTime2
             , null                                               instorageTime
             , null                                               instorageTimes
             , IFNULL(yi.freight, 0)            as              freight
             , IFNULL(yi.delivery_fee, 0)                       deliveryFee
             , IFNULL(yi.outboundsorting_fee, 0)                outboundsortingFee
             , IFNULL(yi.shortbarge_fee, 0)                     shortbargeFee
             , IFNULL(yi.superframes_fee, 0)                    superframesFee
             , IFNULL(yi.excess_fee, 0)                         excessFee
             , IFNULL(yi.ultrafar_fee, 0)                       ultrafarFee
             , IFNULL(yi.exception_fee, 0)                      exceptionFee
             , IFNULL(yi.return_fee, 0)                         returnFee
             , IFNULL(yi.adjust_fee, 0)                         adjustFee
             , IFNULL(yi.other_cost1, 0)                        otherCost1
             , IFNULL(yi.other_cost2, 0)                        otherCost2
             , IFNULL(yi.other_cost3, 0)                        otherCost3
             , IFNULL(yi.other_cost4, 0)                        otherCost4
             , IFNULL(yi.other_cost5, 0)                        otherCost5
             , IFNULL(yi.other_cost6, 0)                        otherCost6
             , IFNULL(yi.other_cost7, 0)                        otherCost7
             , IFNULL(yi.other_cost8, 0)                        otherCost8
             , IFNULL(yi.other_cost9, 0)                        otherCost9
             , IFNULL(yi.other_cost10, 0)                       otherCost10
             , IFNULL(yi.other_cost11, 0)                       otherCost11
             , IFNULL(yi.other_cost12, 0)                       otherCost12
             , yi.expenses_code                                 expensesCode
             , yi.company_id                                    institutionId
             , yi.carrier_name                                  costCarrierName
             , yi.client_id                                     costClientId
             , null                                               temperatureType
             , null                                               aqty
             , y.pallet_number                                    palletNumber
             , null                                               palletRuler
             , yi.remarks                                       remarks
             , ye.automatic_billing_remark                        automaticBillingRemark
             , y.transport_type                                   transportTypeApi
             , y.cw_full_cases                                    cwFullCases
             , y.cw_split_cases                                   cwSplitCases
             , y.ld_full_cases                                    ldFullCases
             , y.ld_split_cases                                   ldSplitCases
             , y.store_code           as                          storeCode
             , y.receiving_store      as                          storeName
             , y.order_type           as                          orderType
             , y.cw_pallet_number     as                          cwPalletNumber
             , y.lc_pallet_number     as                          lcPalletNumber
             , y.ld_pallet_number     as                          ldPalletNumber
             , y.order_no             as                          orderNo
             , y.platform_code        as                          platformCode
             , y.destination_province as                          destinationProvince
             , y.destination_city     as                          destinationCity
             , y.destination_area     as                          destinationArea
             , bymi.id                as                          mainExpenseId
             , bymi.expenses_code     as                          mainExpenseCode
        FROM bms_yfstock_codeinfo y
        left join bms_yfexpenses_middle ym on y.id = ym.yfbill_id and ym.del_flag = 0
        left join bms_yfcost_info yi on yi.main_expense_id = ym.main_expense_id and yi.del_flag = 0
        left join bms_yfcost_main_info bymi on bymi.id = yi.main_expense_id and bymi.del_flag = 0
        left join bms_yfcost_extend ye on yi.main_expense_id = ye.main_expense_id and ye.del_flag = 0
        left join mdm_warehouseinfo ware  on ware.warehouse_code = y.warehouse_code  and ware.status = 0
        where y.del_flag = '0'
          AND (yi.id = #{expensesId} or y.id = #{expensesId})
        union
        SELECT
        y.id                                               yfbillId
        , yi.id as                                        expensesId
        , yi.id    as                                        yfcostId
        , 3                                                  codeType
        , y.stock_code                                       relateCode
        , y.cost_status                                      costStatus
        , null                                               stockoperTimes
        , y.company_id                                       companyId
        , y.del_flag                                         delFlag
        , y.carrier_code                                     carrierCode
        , y.carrier_name                                     carrierName
        , null                                               networkCode
        , y.warehouse_code                                   warehouseCode
        , ware.warehouse_name                                warehouseName
        , IFNULL(y.trust, 0)                                 trust
        , IFNULL(y.trust, 0)                                 totalNumber
        , IFNULL(y.weight, 0)                                totalWeight
        , IFNULL(y.volume, 0)                                totalVolume
        , null                                               cargoValue
        , IFNULL(y.total_boxes, 0)                           totalBoxes
        , IFNULL(y.odd_boxes, 0)                             oddBoxes
        , y.warehouse_area                                   warehouseArea
        , null                                               stockoperTime
        , y.oper_time                                        operTime2
        , y.instorage_time                                   instorageTime
        , DATE_FORMAT(y.instorage_time, '%Y-%m-%d %H:%i:%s') instorageTimes
        , IFNULL(yi.freight, 0)                           freight
        , IFNULL(yi.delivery_fee, 0)                      deliveryFee
        , IFNULL(yi.outboundsorting_fee, 0)               outboundsortingFee
        , IFNULL(yi.shortbarge_fee, 0)                    shortbargeFee
        , IFNULL(yi.superframes_fee, 0)                   superframesFee
        , IFNULL(yi.excess_fee, 0)                        excessFee
        , IFNULL(yi.ultrafar_fee, 0)                      ultrafarFee
        , IFNULL(yi.exception_fee, 0)                     exceptionFee
        , IFNULL(yi.return_fee, 0)                        returnFee
        , IFNULL(yi.adjust_fee, 0)                        adjustFee
        , IFNULL(yi.other_cost1, 0)                       otherCost1
        , IFNULL(yi.other_cost2, 0)                       otherCost2
        , IFNULL(yi.other_cost3, 0)                       otherCost3
        , IFNULL(yi.other_cost4, 0)                       otherCost4
        , IFNULL(yi.other_cost5, 0)                       otherCost5
        , IFNULL(yi.other_cost6, 0)                       otherCost6
        , IFNULL(yi.other_cost7, 0)                       otherCost7
        , IFNULL(yi.other_cost8, 0)                       otherCost8
        , IFNULL(yi.other_cost9, 0)                       otherCost9
        , IFNULL(yi.other_cost10, 0)                      otherCost10
        , IFNULL(yi.other_cost11, 0)                      otherCost11
        , IFNULL(yi.other_cost12, 0)                      otherCost12
        , yi.expenses_code                                expensesCode
        , yi.company_id                                   institutionId
        , yi.carrier_name                                 costCarrierName
        , yi.client_id                                    costClientId
        , y.temperature_type                                 temperatureType
        , y.aqty                                             aqty
        , y.pallet_number                                    palletNumber
        , y.pallet_ruler                                     palletRuler
        , yi.remarks                                      remarks
        , ye.automatic_billing_remark                        automaticBillingRemark
        , null                                               transportTypeApi
        , null                                               cwFullCases
        , null                                               cwSplitCases
        , null                                               ldFullCases
        , null                                               ldSplitCases
        , null     as                                        storeCode
        , null     as                                        storeName
        , null     as                                        orderType
        , null     as                                        cwPalletNumber
        , null     as                                        lcPalletNumber
        , null     as                                        ldPalletNumber
        , null     as                                        orderNo
        , null     as                                        platformCode
        , null     as                                        destinationProvince
        , null     as                                        destinationCity
        , null     as                                        destinationArea
        , bymi2.id                as                          mainExpenseId
        , bymi2.expenses_code     as                          mainExpenseCode
        FROM bms_yfstockinfo y
        left join bms_yfexpenses_middle ym on y.id = ym.yfbill_id and ym.del_flag = 0
        left join bms_yfcost_info yi on yi.main_expense_id = ym.main_expense_id and yi.del_flag = 0
        left join bms_yfcost_main_info bymi2 on bymi2.id = yi.main_expense_id and bymi2.del_flag = 0
        left join bms_yfcost_extend ye on yi.main_expense_id = ye.main_expense_id and ye.del_flag = 0
        left join mdm_warehouseinfo ware on ware.warehouse_code = y.warehouse_code and ware.status = 0
        where y.del_flag = '0'
          AND (yi.id = #{expensesId} or y.id = #{expensesId})
    </select>

    <select id="getCodeInfoByIds" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">

        select
        info.scheduling_bill_code as schedulingBillCode,
        info.virtual_order_no as virtualOrderNo,
        info.company_id as companyId,
        info.carrier_code as carrierCode,
        info.carrier_name as carrierName,
        cli.client_code as clientCode,
        cli.client_name as clientName,
        info.line_code as lineCode,
        info.line_name as lineName,
        info.base_stores as baseStores,
        info.total_votenumber as totalVotenumber,
        info.total_kilometer as totalKilometer,
        info.body_office_times as bodyOfficeTimes,
        info.head_office_times as headOfficeTimes,
        info.car_code as carCode,
        info.driver as driver,
        info.car_model as carModel,
        info.delivery_mode as deliveryMode,
        info.transport_type as transportType,
        DATE_FORMAT(info.dispatch_date,'%Y-%m-%d %H:%i:%s')  as dispatchDate,
        DATE_FORMAT(info.finish_date,'%Y-%m-%d %H:%i:%s') as finishDate,
        info.total_boxes as totalBoxes,
        info.total_weight as totalWeight,
        info.total_number as totalNumber,
        info.total_volume as totalVolume,
        info.cargo_value as cargoValue,
        info.vehicle_temperature_type as vehicleTemperatureType,
        info.order_no as orderNo,
        info.express_no as expressNo
        from bms_yfbillcodeinfo info
        left join bms_clientinfo cli
        on info.client_id = cli.id
        where
        1=1
        and info.id in (

        select a.yfbillId from
        (SELECT
        y.id yfbillId
        FROM bms_yfbillcodeinfo y left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="dispatchDateStart != null and dispatchDateStart != '' and dispatchDateEnd != null and dispatchDateEnd!=''">
            AND y.dispatch_date &gt;= #{dispatchDateStart,jdbcType=VARCHAR} AND y.dispatch_date &lt;= #{dispatchDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="finishDateStart != null and finishDateStart != '' and finishDateEnd != null and finishDateEnd!=''">
            AND y.finish_date &gt;= #{finishDateStart,jdbcType=VARCHAR} AND y.finish_date &lt;= #{finishDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="warehouseCompanyId != null and warehouseCompanyId.size>0 ">
            AND y.company_id in
            <foreach collection="warehouseCompanyId" item="warehouseCompanyId" open="(" separator="," close=")">
                #{warehouseCompanyId}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="schedulingBillCode != null and schedulingBillCode.size>0 ">
            AND y.scheduling_bill_code in
            <foreach collection="schedulingBillCode" item="schedulingBillCode" open="(" separator="," close=")">
                #{schedulingBillCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND y.delivery_mode = #{deliveryMode}
        </if>
        <if test="transportType != null and transportType != ''">
            AND y.transport_type = #{transportType}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        <if test="virtualOrderNo != null and virtualOrderNo != ''">
            AND y.virtual_order_no like concat('%',trim(#{virtualOrderNo}),'%')
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND y.tline_code like concat('%',trim(#{lineCode}),'%')
        </if>
        <if test="lineName != null and lineName != ''">
            AND y.tline_name like concat('%',trim(#{lineName}),'%')
        </if>
        union
        SELECT
        bym.yfbill_id yfbillId
        from bms_yfcost_info yi
        left join bms_yfcost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_yfexpenses_middle bym on bym.expenses_id=yi.id
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on yi.client_id=mc.id
        left join bms_yfbillcodeinfo yf on yf.id=bym.yfbill_id and IFNULL(ym.code_count,1)=1
        where yi.del_flag=0 and yi.expenses_type=1
        <if test="finishDateStart != null and finishDateStart != '' and finishDateEnd != null and finishDateEnd!=''">
            AND yi.business_time &gt;= #{finishDateStart,jdbcType=VARCHAR} AND yi.business_time &lt;= #{finishDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="dispatchDateStart != null and dispatchDateStart != '' and dispatchDateEnd != null and dispatchDateEnd!=''">
            AND yf.dispatch_date &gt;= #{dispatchDateStart,jdbcType=VARCHAR} AND yf.dispatch_date &lt;= #{dispatchDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND yi.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="warehouseCompanyId != null and warehouseCompanyId.size>0 ">
            AND yi.company_id in
            <foreach collection="warehouseCompanyId" item="warehouseCompanyId" open="(" separator="," close=")">
                #{warehouseCompanyId}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND yi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="schedulingBillCode != null and schedulingBillCode.size>0 ">
            AND (
            <foreach collection="schedulingBillCode" item="schedulingBillCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{schedulingBillCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND yf.delivery_mode = #{deliveryMode}
        </if>
        <if test="transportType != null and transportType != ''">
            AND yf.transport_type = #{transportType}
        </if>
        <if test="virtualOrderNo != null and virtualOrderNo != ''">
            AND yf.virtual_order_no like concat('%',trim(#{virtualOrderNo}),'%')
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND yf.tline_code like concat('%',trim(#{lineCode}),'%')
        </if>
        <if test="lineName != null and lineName != ''">
            AND yf.tline_name like concat('%',trim(#{lineName}),'%')
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        group by yi.id
        ) a

        )
    </select>


    <select id="getWorkOrderByIds" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">


        select

        job.relate_code as relateCode,
        info.scheduling_bill_code as schedulingBillCode,
        info.virtual_order_no as virtualOrderNo,
        job.company_id as companyId,
        job.client_code as clientCode,
        cli.client_name as clientName,
        job.store_code as storeCode,
        sto.store_name as storeName,
        job.total_number as totalNumber,
        job.total_weight as totalWeight,
        job.total_volume as totalVolume,
        job.cargo_value as cargoValue,
        job.total_boxes as totalBoxes,
        job.if_base_stores as ifBaseStores,
        job.if_super_base_kilometer as ifSuperBaseKilometer,
        job.store_distance_kilometer as storeDistanceKilometer,
        job.near_store_km as nearStoreKm

        from bms_yfbillcodeinfo info
        LEFT JOIN bms_yfjobbillinfo job ON job.scheduling_bill_code = info.virtual_order_no and job.del_flag=0
        left join bms_clientinfo cli
        on cli.client_code = cli.client_code
        and cli.del_flag=0
        left join mdm_storeinfo sto
        on sto.store_code  = job.store_code
        and sto.del_flag=0
        where 1=1
        and info.del_flag=0
        <!--<if test="scheCodes!=null and scheCodes.size()>0">
            and job.scheduling_bill_code in
            <foreach collection="scheCodes" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>-->
        <if test="ids!=null and ids.size()>0">
            and info.id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
        <!--<if test="pkIds!=null and pkIds.size()>0">
            and info.pk_id in
            <foreach collection="pkIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>-->
        group by job.id

    </select>

    <select id="getWorkOrderByBean" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">


        select

        job.relate_code as relateCode,
        info.scheduling_bill_code as schedulingBillCode,
        info.virtual_order_no as virtualOrderNo,
        job.company_id as companyId,
        job.client_code as clientCode,
        job.client_name as clientName,
        job.store_code as storeCode,
        sto.store_name as storeName,
        job.total_number as totalNumber,
        job.total_weight as totalWeight,
        job.total_volume as totalVolume,
        job.cargo_value as cargoValue,
        job.total_boxes as totalBoxes,
        job.if_base_stores as ifBaseStores,
        job.if_super_base_kilometer as ifSuperBaseKilometer,
        job.store_distance_kilometer as storeDistanceKilometer,
        job.near_store_km as nearStoreKm
        ,job.order_province as destinationProvince -- 目的省
        ,job.order_city as destinationCity -- 目的市
        ,job.order_district as destinationArea -- 目的区
        ,bym2.share_type  as shareType
        ,bym2.share_amount  as shareAmount
        from bms_yfbillcodeinfo info
        LEFT JOIN bms_yfjobbillinfo job ON job.scheduling_bill_code = info.virtual_order_no and job.del_flag=0
        LEFT JOIN mdm_storeinfo sto on sto.store_code  = job.store_code  and sto.del_flag=0
        LEFT JOIN bms_yfexpenses_middle bym2 on bym2.yfbill_id=info.id and yfbill_type=1 and bym2.del_flag=0
        where 1=1
        and info.del_flag=0
        and info.id in (

        select a.yfbillId from
        (SELECT
        y.id yfbillId
        FROM bms_yfbillcodeinfo y left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="dispatchDateStart != null and dispatchDateStart != '' and dispatchDateEnd != null and dispatchDateEnd!=''">
            AND y.dispatch_date &gt;= #{dispatchDateStart,jdbcType=VARCHAR} AND y.dispatch_date &lt;= #{dispatchDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="finishDateStart != null and finishDateStart != '' and finishDateEnd != null and finishDateEnd!=''">
            AND y.finish_date &gt;= #{finishDateStart,jdbcType=VARCHAR} AND y.finish_date &lt;= #{finishDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="warehouseCompanyId != null and warehouseCompanyId.size>0 ">
            AND y.company_id in
            <foreach collection="warehouseCompanyId" item="warehouseCompanyId" open="(" separator="," close=")">
                #{warehouseCompanyId}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="schedulingBillCode != null and schedulingBillCode.size>0 ">
            AND y.scheduling_bill_code in
            <foreach collection="schedulingBillCode" item="schedulingBillCode" open="(" separator="," close=")">
                #{schedulingBillCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND y.delivery_mode = #{deliveryMode}
        </if>
        <if test="transportType != null and transportType != ''">
            AND y.transport_type = #{transportType}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        <if test="virtualOrderNo != null and virtualOrderNo != ''">
            AND y.virtual_order_no like concat('%',trim(#{virtualOrderNo}),'%')
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND y.tline_code like concat('%',trim(#{lineCode}),'%')
        </if>
        <if test="lineName != null and lineName != ''">
            AND y.tline_name like concat('%',trim(#{lineName}),'%')
        </if>
        union
        SELECT
        bym.yfbill_id yfbillId
        from bms_yfcost_info yi
        left join bms_yfcost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_yfexpenses_middle bym on bym.expenses_id=yi.id
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on yi.client_id=mc.id
        left join bms_yfbillcodeinfo yf on yf.id=bym.yfbill_id and IFNULL(ym.code_count,1)=1
        where yi.del_flag=0 and yi.expenses_type=1
        <if test="finishDateStart != null and finishDateStart != '' and finishDateEnd != null and finishDateEnd!=''">
            AND yi.business_time &gt;= #{finishDateStart,jdbcType=VARCHAR} AND yi.business_time &lt;= #{finishDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="dispatchDateStart != null and dispatchDateStart != '' and dispatchDateEnd != null and dispatchDateEnd!=''">
            AND yf.dispatch_date &gt;= #{dispatchDateStart,jdbcType=VARCHAR} AND yf.dispatch_date &lt;= #{dispatchDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND yi.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="warehouseCompanyId != null and warehouseCompanyId.size>0 ">
            AND yi.company_id in
            <foreach collection="warehouseCompanyId" item="warehouseCompanyId" open="(" separator="," close=")">
                #{warehouseCompanyId}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND yi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="schedulingBillCode != null and schedulingBillCode.size>0 ">
            AND (
            <foreach collection="schedulingBillCode" item="schedulingBillCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{schedulingBillCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND yf.delivery_mode = #{deliveryMode}
        </if>
        <if test="transportType != null and transportType != ''">
            AND yf.transport_type = #{transportType}
        </if>
        <if test="virtualOrderNo != null and virtualOrderNo != ''">
            AND yf.virtual_order_no like concat('%',trim(#{virtualOrderNo}),'%')
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND yf.tline_code like concat('%',trim(#{lineCode}),'%')
        </if>
        <if test="lineName != null and lineName != ''">
            AND yf.tline_name like concat('%',trim(#{lineName}),'%')
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        group by yi.id
        ) a

        )
        group by job.id

    </select>

    <select id="getWorkOrderDetailByIds" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">


        select

        job.relate_code as relateCode,
        goods.sku_class as skuClass,
        goods.sku_code as skuCode,
        goods.sku_name as skuName,
        goods.temperature_type as temperatureType,
        goods.box_type as boxType,
        goods.numbers as numbers,
        goods.total_boxes as totalBoxes,
        goods.odd_boxes as oddBoxes,
        goods.total_weight as totalWeight,
        goods.total_volume as totalVolume,
        goods.weight as weight,
        goods.volume as volume,
        ms.unit,
        ms.specification
        from bms_yfbillcodeinfo info
        left join bms_yfjobbillinfo job
        on job.scheduling_bill_code = info.virtual_order_no
        left join bms_yfjobbill_goodsinfo goods
        on goods.jobbill_id = job.id
        left join mdm_skuinfo ms on goods.sku_code=ms.sku_code and ms.status=0
        where 1=1
        and info.id in (
        select a.yfbillId from
        (SELECT
        y.id yfbillId
        FROM bms_yfbillcodeinfo y left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="dispatchDateStart != null and dispatchDateStart != '' and dispatchDateEnd != null and dispatchDateEnd!=''">
            AND y.dispatch_date &gt;= #{dispatchDateStart,jdbcType=VARCHAR} AND y.dispatch_date &lt;= #{dispatchDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="finishDateStart != null and finishDateStart != '' and finishDateEnd != null and finishDateEnd!=''">
            AND y.finish_date &gt;= #{finishDateStart,jdbcType=VARCHAR} AND y.finish_date &lt;= #{finishDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="warehouseCompanyId != null and warehouseCompanyId.size>0 ">
            AND y.company_id in
            <foreach collection="warehouseCompanyId" item="warehouseCompanyId" open="(" separator="," close=")">
                #{warehouseCompanyId}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="schedulingBillCode != null and schedulingBillCode.size>0 ">
            AND y.scheduling_bill_code in
            <foreach collection="schedulingBillCode" item="schedulingBillCode" open="(" separator="," close=")">
                #{schedulingBillCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND y.delivery_mode = #{deliveryMode}
        </if>
        <if test="transportType != null and transportType != ''">
            AND y.transport_type = #{transportType}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        <if test="virtualOrderNo != null and virtualOrderNo != ''">
            AND y.virtual_order_no like concat('%',trim(#{virtualOrderNo}),'%')
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND y.tline_code like concat('%',trim(#{lineCode}),'%')
        </if>
        <if test="lineName != null and lineName != ''">
            AND y.tline_name like concat('%',trim(#{lineName}),'%')
        </if>
        union
        SELECT
        bym.yfbill_id yfbillId
        from bms_yfcost_info yi
        left join bms_yfcost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_yfexpenses_middle bym on bym.expenses_id=yi.id
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on yi.client_id=mc.id
        left join bms_yfbillcodeinfo yf on yf.id=bym.yfbill_id and IFNULL(ym.code_count,1)=1
        where yi.del_flag=0 and yi.expenses_type=1
        <if test="finishDateStart != null and finishDateStart != '' and finishDateEnd != null and finishDateEnd!=''">
            AND yi.business_time &gt;= #{finishDateStart,jdbcType=VARCHAR} AND yi.business_time &lt;= #{finishDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="dispatchDateStart != null and dispatchDateStart != '' and dispatchDateEnd != null and dispatchDateEnd!=''">
            AND yf.dispatch_date &gt;= #{dispatchDateStart,jdbcType=VARCHAR} AND yf.dispatch_date &lt;= #{dispatchDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND yi.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="warehouseCompanyId != null and warehouseCompanyId.size>0 ">
            AND yi.company_id in
            <foreach collection="warehouseCompanyId" item="warehouseCompanyId" open="(" separator="," close=")">
                #{warehouseCompanyId}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND yi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="schedulingBillCode != null and schedulingBillCode.size>0 ">
            AND (
            <foreach collection="schedulingBillCode" item="schedulingBillCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{schedulingBillCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND yf.delivery_mode = #{deliveryMode}
        </if>
        <if test="transportType != null and transportType != ''">
            AND yf.transport_type = #{transportType}
        </if>
        <if test="virtualOrderNo != null and virtualOrderNo != ''">
            AND yf.virtual_order_no like concat('%',trim(#{virtualOrderNo}),'%')
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND yf.tline_code like concat('%',trim(#{lineCode}),'%')
        </if>
        <if test="lineName != null and lineName != ''">
            AND yf.tline_name like concat('%',trim(#{lineName}),'%')
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        group by yi.id
        ) a
        )
        group by goods.id
    </select>

    <select id="getWareOrderDetailByIds" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDto">
        (select
        info.stock_code as relateCode,
        DATE_FORMAT(info.instorage_time,'%Y-%m-%d %H:%i:%s') as stockoperTime,
        info.company_id as companyId,
        info.carrier_code as carrierCode,
        car.carrier_name as carrierName,
        cli.client_code as clientCode,
        cli.client_name as clientName,
        info.warehouse_code as warehouseCode,
        war.warehouse_name as warehouseName,
        info.trust as totalNumber,
        info.weight as totalWeight,
        info.volume as totalVolume,
        null as cargoValue,
        info.total_boxes as totalBoxes,
        info.aqty as aqty,
        info.pallet_number as palletNumber
        ,"" as storeCode
        ,"" as storeName
        ,null as cwPalletNumber
        ,null as lcPalletNumber
        ,null as ldPalletNumber
        ,null as orderNo
        ,null as orderType
        ,null as platformCode
        ,null as destinationProvince
        ,null as destinationCity
        ,null as destinationArea
        from bms_yfstockinfo info
        left join bms_carrierinfo car
        on car.carrier_code = info.carrier_code
        left join bms_clientinfo cli
        on cli.id = info.client_id
        left join mdm_warehouseinfo war
        on war.warehouse_code = info.warehouse_code
        where
        1=1
        and info.id in (
        select a.yfbillId from
        (SELECT
        y.id yfbillId
        FROM bms_yfstock_codeinfo y left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.stockoper_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.stockoper_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND y.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(DISTINCT bym.yfbill_id SEPARATOR ',') yfbillId
        from bms_yfcost_info yi
        left join bms_yfcost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_yfexpenses_middle bym on bym.expenses_id=yi.id
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        where yi.del_flag=0
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND yi.business_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND yi.business_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND yi.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            ym.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
            or yi.is_increment = 1
            )
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type-1 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND yi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND ym.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        group by yi.id
        union
        SELECT
        y.id yfbillId
        FROM bms_yfstockinfo y
        left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.instorage_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.instorage_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND 3 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.stock_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        ) a
        )
        group by info.id)
        UNION ALL
        (
        select
        info.relate_code as relateCode,
        info.stockoper_time as stockoperTime,
        info.company_id as companyId,
        info.carrier_code as carrierCode,
        car.carrier_name as carrierName,
        cli.client_code as clientCode,
        cli.client_name as clientName,
        info.warehouse_code as warehouseCode,
        war.warehouse_name as warehouseName,
        info.sku_number as totalNumber,
        info.weight as totalWeight,
        info.volume as totalVolume,
        info.cargo_value as cargoValue,
        info.total_boxes as totalBoxes,
        null as aqty,
        info.pallet_number as palletNumber
        ,info.store_code as storeCode
        ,info.receiving_store as storeName
        ,info.cw_pallet_number as cwPalletNumber
        ,info.lc_pallet_number as lcPalletNumber
        ,info.ld_pallet_number as ldPalletNumber
        ,info.order_no as orderNo
        ,info.order_type as orderType
        ,info.platform_code as platformCode
        ,info.destination_province as destinationProvince
        ,info.destination_city as destinationCity
        ,info.destination_area as destinationArea
        from bms_yfstock_codeinfo info
        left join bms_carrierinfo car
        on car.carrier_code = info.carrier_code
        left join bms_clientinfo cli
        on cli.id = info.client_id
        left join mdm_warehouseinfo war
        on war.warehouse_code = info.warehouse_code
        where 1=1
        and info.id in (
        select a.yfbillId from
        (SELECT
        y.id yfbillId
        FROM bms_yfstock_codeinfo y left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.stockoper_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.stockoper_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND y.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(DISTINCT bym.yfbill_id SEPARATOR ',') yfbillId
        from bms_yfcost_info yi
        left join bms_yfcost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_yfexpenses_middle bym on bym.expenses_id=yi.id
        left join bms_clientinfo mc on yi.client_id=mc.id
        where yi.del_flag=0
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND yi.business_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND yi.business_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND yi.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            ym.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
            or yi.is_increment = 1
            )
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type-1 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND yi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND ym.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        group by yi.id
        union
        SELECT
        y.id yfbillId
        FROM bms_yfstockinfo y
        left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.instorage_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.instorage_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND 3 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.stock_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        ) a
        )
        group by info.id)

    </select>
    <select id="getWareOrderDetailByIds2" parameterType="java.util.Arrays" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDto">
        select
        info.stock_code as relateCode,
        DATE_FORMAT(info.instorage_time,'%Y-%m-%d %H:%i:%s') as stockoperTime,
        info.company_id as companyId,
        info.carrier_code as carrierCode,
        car.carrier_name as carrierName,
        cli.client_code as clientCode,
        cli.client_name as clientName,
        info.warehouse_code as warehouseCode,
        war.warehouse_name as warehouseName,
        info.trust as totalNumber,
        info.weight as totalWeight,
        info.volume as totalVolume,
        null as cargoValue,
        info.total_boxes as totalBoxes,
        info.aqty as aqty,
        info.pallet_number as palletNumber
        ,"" as storeCode
        ,"" as storeName
        ,null as cwPalletNumber
        ,null as lcPalletNumber
        ,null as ldPalletNumber
        ,null as orderNo
        ,null as orderType
        ,null as platformCode
        from bms_yfstockinfo info
        left join bms_carrierinfo car
        on car.carrier_code = info.carrier_code
        left join bms_clientinfo cli
        on cli.id = info.client_id
        left join mdm_warehouseinfo war
        on war.warehouse_code = info.warehouse_code

        where
        1=1
        and info.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

        union

        select
        info.relate_code as relateCode,
        DATE_FORMAT(info.stockoper_time,'%Y-%m-%d %H:%i:%s') as stockoperTime,
        info.company_id as companyId,
        info.carrier_code as carrierCode,
        car.carrier_name as carrierName,
        cli.client_code as clientCode,
        cli.client_name as clientName,
        info.warehouse_code as warehouseCode,
        war.warehouse_name as warehouseName,
        info.sku_number as totalNumber,
        info.weight as totalWeight,
        info.volume as totalVolume,
        null as cargoValue,
        info.total_boxes as totalBoxes,
        0 as aqty,
        info.pallet_number as palletNumber
        ,info.store_code as storeCode
        ,info.receiving_store as storeName
        ,info.cw_pallet_number as cwPalletNumber
        ,info.lc_pallet_number as lcPalletNumber
        ,info.ld_pallet_number as ldPalletNumber
        ,info.order_no as orderNo
        ,info.order_type as orderType
        ,info.platform_code as platformCode
        from bms_yfstock_codeinfo info
        left join bms_carrierinfo car
        on car.carrier_code = info.carrier_code
        left join bms_clientinfo cli
        on cli.id = info.client_id
        left join mdm_warehouseinfo war
        on war.warehouse_code = info.warehouse_code

        where
        1=1
        and info.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getWareGoodsinfoDetailByIds" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDto">


        select
        info.stock_code as relateCode,
        goods.sku_code as skuCode,
        sku.sku_name as skuName,
        goods.temperature_type as temperatureType,
        sku.sku_type_name as skuTypeName,
        sku.unit,
        sku.specification,
        goods.box_type as boxType,
        null as totalBoxes,
        null as oddBoxes,
        null as cargoValue,
        sku.weight as weight,
        sku.volume as volume,
        null as totalAmount
        from bms_yfstock_goodsinfo goods
        inner join bms_yfstockinfo info
        on goods.yfstock_id = info.id
        left join mdm_skuinfo sku
        on sku.sku_code = goods.sku_code
        left join bms_carrierinfo car
        on car.carrier_code = info.carrier_code
        left join bms_clientinfo cli
        on cli.id = info.client_id
        left join mdm_warehouseinfo war
        on war.warehouse_code = info.warehouse_code

        where
        1=1
        and goods.yfstock_id in
        (
        select a.yfbillId from
        (SELECT
        y.id yfbillId

        FROM bms_yfstock_codeinfo y left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.stockoper_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.stockoper_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND y.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(DISTINCT bym.yfbill_id SEPARATOR ',') yfbillId
        from bms_yfcost_info yi
        left join bms_yfcost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_yfexpenses_middle bym on bym.expenses_id=yi.id
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on yi.client_id=mc.id
        left join bms_yfstockinfo stk on bym.yfbill_id = stk.id and IFNULL(ym.code_count,1)=1
        left join bms_yfstock_codeinfo byc on  byc.id = bym.yfbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND yi.business_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND yi.business_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND yi.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            ym.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
            or yi.is_increment = 1
            )
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type-1 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND yi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND ym.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        group by yi.id
        union
        SELECT
        y.id yfbillId

        FROM bms_yfstockinfo y
        left join bms_yfexpenses_middle ym on y.id=ym.yfbill_id and ym.del_flag=0
        left join bms_yfcost_info yi on yi.id=ym.expenses_id and yi.del_flag=0
        left join bms_yfcost_extend ye on yi.id=ye.expenses_id and ye.del_flag=0
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.instorage_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.instorage_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND 3 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.stock_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        ) a
        )
        group by goods.yfstock_id

        UNION ALL




        select
        info.relate_code as relateCode,
        goods.sku_code as skuCode,
        sku.sku_name as skuName,
        goods.temperature_type as temperatureType,
        sku.sku_type_name as skuTypeName,
        sku.unit,
        sku.specification,
        goods.box_type as boxType,
        goods.total_boxes as totalBoxes,
        goods.odd_boxes as oddBoxes,
        goods.price as cargoValue,
        goods.weight as weight,
        goods.volume as volume,
        goods.total_amount as totalAmount
        from bms_yfstockcode_detailinfo goods
        inner join bms_yfstock_codeinfo info
        on goods.yfstock_id = info.id
        left join mdm_skuinfo sku
        on sku.sku_code = goods.sku_code
        left join bms_carrierinfo car
        on car.carrier_code = info.carrier_code
        left join bms_clientinfo cli
        on cli.id = info.client_id
        left join mdm_warehouseinfo war
        on war.warehouse_code = info.warehouse_code
        and goods.yfstock_id in
        (
        select a.yfbillId from
        (SELECT
        y.id yfbillId

        FROM bms_yfstock_codeinfo y left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.stockoper_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.stockoper_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND y.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(DISTINCT bym.yfbill_id SEPARATOR ',') yfbillId
        from bms_yfcost_info yi
        left join bms_yfcost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_yfexpenses_middle bym on bym.expenses_id=yi.id
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on yi.client_id=mc.id
        left join bms_yfstockinfo stk on bym.yfbill_id = stk.id and IFNULL(ym.code_count,1)=1
        left join bms_yfstock_codeinfo byc on  byc.id = bym.yfbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND yi.business_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND yi.business_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND yi.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            ym.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
            or yi.is_increment = 1
            )
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type-1 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND yi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND ym.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        group by yi.id
        union
        SELECT
        y.id yfbillId

        FROM bms_yfstockinfo y
        left join bms_yfexpenses_middle ym on y.id=ym.yfbill_id and ym.del_flag=0
        left join bms_yfcost_info yi on yi.id=ym.expenses_id and yi.del_flag=0
        left join bms_yfcost_extend ye on yi.id=ye.expenses_id and ye.del_flag=0
        left join bms_yfbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.instorage_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.instorage_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND 3 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.stock_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        ) a

        )
        group by goods.yfstock_id

    </select>

    <select id="getWareGoodsinfoDetailByIds2" parameterType="java.util.Arrays" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDto">
        select
        info.stock_code as relateCode,
        goods.sku_code as skuCode,
        sku.sku_name as skuName,
        goods.temperature_type as temperatureType,
        sku.sku_type_name as skuTypeName,
        sku.unit,
        sku.specification,
        goods.box_type as boxType,
        null as totalBoxes,
        null as oddBoxes,
        null as cargoValue,
        sku.weight as weight,
        sku.volume as volume,
        null as totalAmount,
        bym2.share_type  as shareType,
        bym2.share_amount  as shareAmount
        from bms_yfstock_goodsinfo goods
        inner join bms_yfstockinfo info
        on goods.yfstock_id = info.id
        left join mdm_skuinfo sku
        on sku.sku_code = goods.sku_code
        left join bms_carrierinfo car
        on car.carrier_code = info.carrier_code
        left join bms_clientinfo cli
        on cli.id = info.client_id
        left join mdm_warehouseinfo war
        on war.warehouse_code = info.warehouse_code
        LEFT JOIN bms_yfexpenses_middle bym2 on bym2.yfbill_id=info.id and yfbill_type=2 and bym2.del_flag=0
        where
        1=1
        and info.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

        UNION

        select
        info.relate_code as relateCode,
        goods.sku_code as skuCode,
        sku.sku_name as skuName,
        goods.temperature_type as temperatureType,
        sku.sku_type_name as skuTypeName,
        sku.unit,
        sku.specification,
        goods.box_type as boxType,
        goods.total_boxes as totalBoxes,
        goods.odd_boxes as oddBoxes,
        goods.price as cargoValue,
        goods.weight as weight,
        goods.volume as volume,
        goods.total_amount as totalAmount,
        bym2.share_type  as shareType,
        bym2.share_amount  as shareAmount
        from bms_yfstockcode_detailinfo goods
        inner join bms_yfstock_codeinfo info
        on goods.yfstock_id = info.id
        left join mdm_skuinfo sku
        on sku.sku_code = goods.sku_code
        left join bms_carrierinfo car
        on car.carrier_code = info.carrier_code
        left join bms_clientinfo cli
        on cli.id = info.client_id
        left join mdm_warehouseinfo war
        on war.warehouse_code = info.warehouse_code
        LEFT JOIN bms_yfexpenses_middle bym2 on bym2.yfbill_id=info.id and yfbill_type=3 and bym2.del_flag=0
        where
        info.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getWareGoodsinfoDetailByMap" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDto">
        (select
        info.stock_code as relateCode,
        goods.sku_code as skuCode,
        sku.sku_name as skuName,
        goods.temperature_type as temperatureType,
        sku.sku_type_name as skuTypeName,
        goods.box_type as boxType,
        null as totalBoxes,
        null as oddBoxes,
        null as cargoValue,
        sku.weight as weight,
        sku.volume as volume,
        null as totalAmount,
        bym2.share_type  as shareType,
        bym2.share_amount  as shareAmount
        from bms_yfstock_goodsinfo goods
        inner join bms_yfstockinfo info
        on goods.yfstock_id = info.id
        left join mdm_skuinfo sku
        on sku.sku_code = goods.sku_code
        LEFT JOIN bms_yfexpenses_middle bym2 on bym2.yfbill_id=info.id  and yfbill_type=2
        where
        1=1
        and goods.yfstock_id in
        (
        select a.yfbillId from
        (SELECT
        y.id yfbillId
        FROM bms_yfstock_codeinfo y left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.stockoper_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.stockoper_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND y.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(DISTINCT bym.yfbill_id SEPARATOR ',') yfbillId
        from bms_yfcost_info yi
        left join bms_yfcost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_yfexpenses_middle bym on bym.expenses_id=yi.id
        left join bms_clientinfo mc on yi.client_id=mc.id
        where yi.del_flag=0
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND yi.business_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND yi.business_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND yi.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            ym.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
            or yi.is_increment = 1
            )
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type-1 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND yi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND ym.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        group by yi.id
        union
        SELECT
        y.id yfbillId
        FROM bms_yfstockinfo y
        left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.instorage_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.instorage_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND 3 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.stock_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        ) a
        )
        group by goods.yfstock_id)

        -- 分割节点  --
        UNION ALL
        (select
        info.relate_code as relateCode,
        goods.sku_code as skuCode,
        sku.sku_name as skuName,
        goods.temperature_type as temperatureType,
        sku.sku_type_name as skuTypeName,
        goods.box_type as boxType,
        goods.total_boxes as totalBoxes,
        goods.odd_boxes as oddBoxes,
        goods.price as cargoValue,
        goods.weight as weight,
        goods.volume as volume,
        goods.total_amount as totalAmount,
        bym2.share_type  as shareType,
        bym2.share_amount  as shareAmount
        from bms_yfstockcode_detailinfo goods
        inner join bms_yfstock_codeinfo info
        on goods.yfstock_id = info.id
        left join mdm_skuinfo sku
        on sku.sku_code = goods.sku_code
        LEFT JOIN bms_yfexpenses_middle bym2 on bym2.yfbill_id=info.id  and yfbill_type=3
        where 1=1
        and goods.yfstock_id in
        (
        select a.yfbillId from
        (SELECT
        y.id yfbillId

        FROM bms_yfstock_codeinfo y left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.stockoper_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.stockoper_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND y.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(DISTINCT bym.yfbill_id SEPARATOR ',') yfbillId
        from bms_yfcost_info yi
        left join bms_yfcost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_yfexpenses_middle bym on bym.expenses_id=yi.id
        left join bms_clientinfo mc on yi.client_id=mc.id
        where yi.del_flag=0
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND yi.business_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND yi.business_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND yi.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            ym.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
            or yi.is_increment = 1
            )
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type-1 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND yi.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND ym.warehouse_name like concat('%',trim(#{warehouseName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        group by yi.id
        union
        SELECT
        y.id yfbillId
        FROM bms_yfstockinfo y
        left join bms_clientinfo mc on y.client_id=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="stockoperTimeStart != null and stockoperTimeStart != '' and stockoperTimeEnd != null and stockoperTimeEnd!=''">
            AND y.instorage_time &gt;= #{stockoperTimeStart,jdbcType=VARCHAR} AND y.instorage_time &lt;= #{stockoperTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="carrierCode != null and carrierCode.size>0 ">
            AND y.carrier_code in
            <foreach collection="carrierCode" item="carrierCode" open="(" separator="," close=")">
                #{carrierCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND 3 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="carrierName != null and carrierName != ''">
            AND y.carrier_name like concat('%',trim(#{carrierName}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.stock_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        ) a
        )
        group by goods.yfstock_id
        )
    </select>

    <select id="selectByVirtualOrderNo" resultMap="BmsYfbillcodeinfoResult">
        <include refid="selectBmsYfbillcodeinfoVo"/>
        where  del_flag = '0'
        <if test="codes != null and codes.size>0 ">
            and virtual_order_no in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <select id="selectImportOrderByCode" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">
        select
        t1.id,
        t1.scheduling_bill_code as relateCode,
        t1.cost_status costStatus,
        t1.create_code createCode,
        t1.order_source orderSource,
        t3.bill_id billId
        from bms_yfbillcodeinfo t1
        left join bms_yfexpenses_middle t2 ON t2.yfbill_id = t1.id AND t2.del_flag = 0
        left join bms_yfcost_info t3 ON t3.id = t2.expenses_id AND t3.del_flag = 0
        where t1.del_flag = 0
        and t1.scheduling_bill_code in
        <foreach collection="codeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByOrderId" resultType="com.bbyb.joy.bms.domain.dto.BmsYfexpensesMiddle">
        select
        id,
        expenses_id expensesId
        from
        bms_yfexpenses_middle
        where
        del_flag=0
        and yfbill_type=#{codeType}
        <if test="ids != null and ids.size>0 ">
            and yfbill_id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
    </select>

    <select id="selectImportOrderByCodeType" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">
        select
        t1.id,
        t1.relate_code relateCode,
        t1.cost_status costStatus,
        t1.order_source orderSource,
        t1.import_code createCode,
        t3.bill_id billId
        from bms_yfstock_codeinfo t1
        left join bms_yfexpenses_middle t2 ON t2.yfbill_id = t1.id AND t2.del_flag = 0
        left join bms_yfcost_info t3 ON t3.id = t2.expenses_id AND t3.del_flag = 0
        where t1.del_flag=0
        and t1.code_type=#{codeType}
        and t1.relate_code in
        <foreach collection="codeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectImportStockByCode" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">
        select
        t1.id,
        t1.stock_code relateCode,
        t1.cost_status costStatus,
        t1.order_source orderSource,
        t1.import_code createCode,
        t3.bill_id billId
        from bms_yfstockinfo t1
        left join bms_yfexpenses_middle t2 ON t2.yfbill_id = t1.id AND t2.del_flag = 0
        left join bms_yfcost_info t3 ON t3.id = t2.expenses_id AND t3.del_flag = 0
        where t1.del_flag=0
        and t1.stock_code in
        <foreach collection="codeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <update id="deleteImportOrder">
        <if test="expensesIds!=null and expensesIds.size()>0">
            UPDATE bms_yfexpenses_middle
            set del_flag=1
            ,oper_by=#{loginUserInfo.userName}
            ,oper_time = now()
            WHERE expenses_id IN
            <foreach collection="expensesIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
            UPDATE bms_yfcost_info
            set del_flag=1
            ,oper_by=#{loginUserInfo.userName}
            ,oper_time = now()
            WHERE id IN
            <foreach collection="expensesIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
            UPDATE bms_yfcost_extend
            SET del_flag=1
            WHERE expenses_id IN
            <foreach collection="expensesIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
        </if>
        <if test="yfcodeInfoIds!=null and yfcodeInfoIds.size()>0 and codeType!=null and codeType in {1} ">
            update bms_yfbillcodeinfo
            set del_flag=1
            ,oper_code=#{loginUserInfo.userName}
            ,oper_by=#{loginUserInfo.nickName}
            ,oper_time=now()
            where id in
            <foreach collection="yfcodeInfoIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
            update bms_yfbillcodeinfo tb1
            left join bms_yfjobbillinfo tb2 on tb1.virtual_order_no=tb2.scheduling_bill_code
            left join bms_yfjobbill_goodsinfo tb3 on tb2.id=tb3.jobbill_id
            set tb2.del_flag=1,tb2.oper_time=now(),tb2.oper_code=#{loginUserInfo.userName},tb3.oper_by=#{loginUserInfo.nickName},
            tb3.del_flag=1,tb3.oper_time=now(),tb3.oper_code=#{loginUserInfo.userName},tb3.oper_by=#{loginUserInfo.nickName}
            where tb1.id in
            <foreach collection="yfcodeInfoIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
        </if>
        <if test="yfcodeInfoIds!=null and yfcodeInfoIds.size()>0 and codeType!=null and codeType in {2,3} ">
            UPDATE bms_yfstockcode_detailinfo
            SET del_flag=1
            WHERE yfstock_id IN
            <foreach collection="yfcodeInfoIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
            update bms_yfstock_codeinfo
            SET del_flag=1,oper_code=#{loginUserInfo.userName},oper_by=#{loginUserInfo.nickName},oper_time=now()
            where id in
            <foreach collection="yfcodeInfoIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
        </if>
        <if test="yfcodeInfoIds!=null and yfcodeInfoIds.size()>0 and codeType!=null and codeType==4">
            UPDATE bms_yfstock_goodsinfo
            SET del_flag=1
            WHERE yfstock_id IN
            <foreach collection="yfcodeInfoIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
            update bms_yfstockinfo
            SET del_flag=1,oper_code=#{loginUserInfo.userName},oper_by=#{loginUserInfo.nickName},oper_time=now()
            WHERE id IN
            <foreach collection="yfcodeInfoIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
        </if>
    </update>

    <select id="selectByExpressNo" resultMap="BmsYfbillcodeinfoResult">
        <include refid="selectBmsYfbillcodeinfoVo"/>
        where  del_flag = '0'
        <if test="codes != null and codes.size>0 ">
            and express_no in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>


    <select id="selectByCodeInfoId" resultMap="BmsYfbillcodeinfoResult">
        select byc.id,
            byc.pk_id,
            byc.scheduling_bill_code,
            byc.virtual_order_no,
            byc.hy_order_no,
            byc.project_quote,
            byc.carrier_code,
            byc.carrier_name,
            byc.company_id,
            byc.network_code,
            byc.total_boxes,
            byc.total_number,
            byc.total_weight,
            byc.total_volume,
            byc.cargo_value,
            byc.dispatch_date,
            byc.start_date,
            byc.finish_date,
            byc.transport_type,
            byc.delivery_mode,
            byc.line_code,
            byc.line_name,
            byc.base_stores,
            byc.base_kilometer,
            byc.total_kilometer,
            byc.number_loading_points,
            byc.number_unloading_points,
            byc.total_votenumber,
            byc.driver,
            byc.car_code,
            byc.car_type,
            byc.car_model,
            byc.head_office_times,
            byc.province_origin,
            byc.originating_city,
            byc.originating_area,
            byc.originating_address,
            byc.destination_province,
            byc.destination_city,
            byc.destination_area,
            byc.destination_address,
            byc.cost_status,
            byc.billing_status,
            byc.create_code,
            byc.create_by,
            byc.create_dept_id,
            byc.create_time,
            byc.oper_code,
            byc.oper_by,
            byc.oper_dept_id,
            byc.oper_time,
            byc.del_flag,
            byc.client_id,
            byc.body_office_times,
            byc.tline_code,
            byc.tline_name,
            byc.vehicle_temperature_type,
            byc.order_no,
            byc.fail_remark,
            byc.order_source,
            byc.store_number,
            byc.express_no,
            byc.original_order_type,
            byc.is_to_client,
            carr.carrier_code,
            carr.carrier_name,
            carr.settle_setting,
            carr.carrier_paytype,
            carr.id AS carrier_id
        from bms_yfbillcodeinfo byc
        left join bms_carrierinfo carr on carr.carrier_code = byc.carrier_code
        where byc.id = #{id}
    </select>


    <select id="selectByCodeInfoDtoId" resultMap="BmsYfbillcodeinfoDtoResult">
        select byc.id,
        byc.id AS yfbillId,
        byc.pk_id,
        byc.scheduling_bill_code,
        byc.virtual_order_no,
        byc.hy_order_no,
        byc.project_quote,
        byc.carrier_code,
        byc.carrier_name,
        byc.company_id,
        byc.network_code,
        byc.total_boxes,
        byc.total_number,
        byc.total_weight,
        byc.total_volume,
        byc.cargo_value,
        byc.dispatch_date,
        byc.start_date,
        byc.finish_date,
        byc.transport_type,
        byc.delivery_mode,
        byc.line_code,
        byc.line_name,
        byc.base_stores,
        byc.base_kilometer,
        byc.total_kilometer,
        byc.number_loading_points,
        byc.number_unloading_points,
        byc.total_votenumber,
        byc.driver,
        byc.car_code,
        byc.car_type,
        byc.car_model,
        byc.head_office_times,
        byc.province_origin,
        byc.originating_city,
        byc.originating_area,
        byc.originating_address,
        byc.destination_province,
        byc.destination_city,
        byc.destination_area,
        byc.destination_address,
        byc.cost_status,
        byc.billing_status,
        byc.create_code,
        byc.create_by,
        byc.create_dept_id,
        byc.create_time,
        byc.oper_code,
        byc.oper_by,
        byc.oper_dept_id,
        byc.oper_time,
        byc.del_flag,
        byc.client_id,
        byc.body_office_times,
        byc.tline_code,
        byc.tline_name,
        byc.vehicle_temperature_type,
        byc.order_no,
        byc.fail_remark,
        byc.order_source,
        byc.store_number,
        byc.express_no,
        byc.original_order_type,
        byc.is_to_client,
        carr.carrier_code,
        carr.carrier_name,
        carr.settle_setting,
        carr.carrier_paytype,
        carr.id AS carrier_id
        from bms_yfbillcodeinfo byc
        left join bms_carrierinfo carr on carr.carrier_code = byc.carrier_code
        where byc.id = #{id}
    </select>



    <resultMap id="BaseMiddleResultMap" type="com.bbyb.joy.bms.domain.dto.BmsYfexpensesMiddle" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="yfbill_id" property="yfbillId" jdbcType="VARCHAR" />
        <result column="yfbill_type" property="yfbillType" jdbcType="INTEGER" />
        <result column="expenses_id" property="expensesId" jdbcType="VARCHAR" />
        <result column="main_expense_id" property="mainExpenseId" jdbcType="VARCHAR" />
        <result column="oper_by" property="operBy" jdbcType="VARCHAR" />
        <result column="oper_time" property="operTime" jdbcType="TIMESTAMP" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
        <result column="share_type" property="shareType" jdbcType="INTEGER" />
        <result column="share_amount" property="shareAmount" jdbcType="DECIMAL" />
        <result column="freight" property="freight" jdbcType="DECIMAL" />
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL" />
        <result column="ultrafar_fee" property="ultrafarFee" jdbcType="DECIMAL" />
        <result column="superframes_fee" property="superframesFee" jdbcType="DECIMAL" />
        <result column="excess_fee" property="excessFee" jdbcType="DECIMAL" />
        <result column="reduce_fee" property="reduceFee" jdbcType="DECIMAL" />
        <result column="outboundsorting_fee" property="outboundsortingFee" jdbcType="DECIMAL" />
        <result column="shortbarge_fee" property="shortbargeFee" jdbcType="DECIMAL" />
        <result column="return_fee" property="returnFee" jdbcType="DECIMAL" />
        <result column="exception_fee" property="exceptionFee" jdbcType="DECIMAL" />
        <result column="other_cost1" property="otherCost1" jdbcType="DECIMAL" />
        <result column="other_cost2" property="otherCost2" jdbcType="DECIMAL" />
        <result column="other_cost3" property="otherCost3" jdbcType="DECIMAL" />
        <result column="other_cost4" property="otherCost4" jdbcType="DECIMAL" />
        <result column="other_cost5" property="otherCost5" jdbcType="DECIMAL" />
        <result column="other_cost6" property="otherCost6" jdbcType="DECIMAL" />
        <result column="other_cost7" property="otherCost7" jdbcType="DECIMAL" />
        <result column="other_cost8" property="otherCost8" jdbcType="DECIMAL" />
        <result column="other_cost9" property="otherCost9" jdbcType="DECIMAL" />
        <result column="other_cost10" property="otherCost10" jdbcType="DECIMAL" />
        <result column="other_cost11" property="otherCost11" jdbcType="DECIMAL" />
        <result column="other_cost12" property="otherCost12" jdbcType="DECIMAL" />
        <result column="total_weight" property="totalWeight" jdbcType="DECIMAL" />
        <result column="total_volume" property="totalVolume" jdbcType="DECIMAL" />
        <result column="total_boxes" property="totalBoxes" jdbcType="DECIMAL" />
        <result column="total_number" property="totalNumber" jdbcType="DECIMAL" />
    </resultMap>
    <resultMap id="BaseMiddleShareResultMap" type="com.bbyb.joy.bms.domain.dto.BmsYfexpensesMiddleShare" >
        <id column="id" property="id"/>
        <result column="yfbill_id" property="yfbillId" />
        <result column="relate_code" property="relateCode" />
        <result column="yfbill_type" property="yfbillType" />
        <result column="expenses_id" property="expensesId" />
        <result column="main_expense_id" property="mainExpenseId" />
        <result column="oper_by" property="operBy" />
        <result column="oper_time" property="operTime" />
        <result column="del_flag" property="delFlag" />
        <result column="share_type" property="shareType" />
        <result column="share_amount" property="shareAmount" />
        <result column="freight" property="freight" />
        <result column="delivery_fee" property="deliveryFee" />
        <result column="ultrafar_fee" property="ultrafarFee" />
        <result column="superframes_fee" property="superframesFee" />
        <result column="excess_fee" property="excessFee" />
        <result column="reduce_fee" property="reduceFee" />
        <result column="outboundsorting_fee" property="outboundsortingFee" />
        <result column="shortbarge_fee" property="shortbargeFee" />
        <result column="return_fee" property="returnFee" />
        <result column="exception_fee" property="exceptionFee" />
        <result column="other_cost1" property="otherCost1" />
        <result column="other_cost2" property="otherCost2" />
        <result column="other_cost3" property="otherCost3" />
        <result column="other_cost4" property="otherCost4" />
        <result column="other_cost5" property="otherCost5" />
        <result column="other_cost6" property="otherCost6" />
        <result column="other_cost7" property="otherCost7" />
        <result column="other_cost8" property="otherCost8" />
        <result column="other_cost9" property="otherCost9" />
        <result column="other_cost10" property="otherCost10" />
        <result column="other_cost11" property="otherCost11" />
        <result column="other_cost12" property="otherCost12" />
        <result column="total_weight" property="totalWeight" />
        <result column="total_volume" property="totalVolume" />
        <result column="total_boxes" property="totalBoxes" />
        <result column="total_number" property="totalNumber" />
        <result column="relate_code" property="relateCode" />
    </resultMap>


    <resultMap id="BaseMainPOResultMap" type="com.bbyb.joy.bms.domain.dto.dto.BmsYfCostMainInfo" >
        <!--@mbg.generated-->
        <!--@Table bms_yfcost_main_info-->
        <id column="pk_id" jdbcType="BIGINT" property="pkId" />
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="expenses_code" jdbcType="VARCHAR" property="expensesCode" />
        <result column="business_type" jdbcType="SMALLINT" property="businessType" />
        <result column="company_id" jdbcType="INTEGER" property="companyId" />
        <result column="expenses_type" jdbcType="SMALLINT" property="expensesType" />
        <result column="cost_dimension" jdbcType="SMALLINT" property="costDimension" />
        <result column="charge_type" jdbcType="SMALLINT" property="chargeType" />
        <result column="fee_flag" jdbcType="SMALLINT" property="feeFlag" />
        <result column="quoterule_id" jdbcType="BIGINT" property="quoteruleId" />
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="freight" jdbcType="DECIMAL" property="freight" />
        <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
        <result column="superframes_fee" jdbcType="DECIMAL" property="superframesFee" />
        <result column="excess_fee" jdbcType="DECIMAL" property="excessFee" />
        <result column="reduce_fee" jdbcType="DECIMAL" property="reduceFee" />
        <result column="shortbarge_fee" jdbcType="DECIMAL" property="shortbargeFee" />
        <result column="return_fee" jdbcType="DECIMAL" property="returnFee" />
        <result column="ultrafar_fee" jdbcType="DECIMAL" property="ultrafarFee" />
        <result column="outboundsorting_fee" jdbcType="DECIMAL" property="outboundsortingFee" />
        <result column="exception_fee" jdbcType="DECIMAL" property="exceptionFee" />
        <result column="adjust_fee" jdbcType="DECIMAL" property="adjustFee" />
        <result column="adjust_remark" jdbcType="VARCHAR" property="adjustRemark" />
        <result column="other_cost1" jdbcType="DECIMAL" property="otherCost1" />
        <result column="other_cost2" jdbcType="DECIMAL" property="otherCost2" />
        <result column="other_cost3" jdbcType="DECIMAL" property="otherCost3" />
        <result column="other_cost4" jdbcType="DECIMAL" property="otherCost4" />
        <result column="other_cost5" jdbcType="DECIMAL" property="otherCost5" />
        <result column="other_cost6" jdbcType="DECIMAL" property="otherCost6" />
        <result column="other_cost7" jdbcType="DECIMAL" property="otherCost7" />
        <result column="other_cost8" jdbcType="DECIMAL" property="otherCost8" />
        <result column="other_cost9" jdbcType="DECIMAL" property="otherCost9" />
        <result column="other_cost10" jdbcType="DECIMAL" property="otherCost10" />
        <result column="other_cost11" jdbcType="DECIMAL" property="otherCost11" />
        <result column="other_cost12" jdbcType="DECIMAL" property="otherCost12" />
        <result column="sum_fee" jdbcType="DECIMAL" property="sumFee" />
        <result column="cost_attribute" jdbcType="SMALLINT" property="costAttribute" />
        <result column="oper_code" jdbcType="VARCHAR" property="operCode" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="bill_id" jdbcType="BIGINT" property="billId" />
        <result column="bill_date" jdbcType="VARCHAR" property="billDate" />
        <result column="business_time" jdbcType="TIMESTAMP" property="businessTime" />
        <result column="over_num" jdbcType="INTEGER" property="overNum" />
        <result column="over_sendnum" jdbcType="INTEGER" property="overSendnum" />
        <result column="storage_fee_price" jdbcType="DECIMAL" property="storageFeePrice" />
        <result column="disposal_fee_price" jdbcType="DECIMAL" property="disposalFeePrice" />
        <result column="other_fee_remark" jdbcType="VARCHAR" property="otherFeeRemark" />
        <result column="client_id" jdbcType="INTEGER" property="clientId" />
        <result column="fee_type_first" jdbcType="VARCHAR" property="feeTypeFirst" />
        <result column="fee_create_ate" jdbcType="TIMESTAMP" property="feeCreateAte" />
        <result column="is_increment" jdbcType="TINYINT" property="isIncrement" />
        <result column="dispatch_date" jdbcType="TIMESTAMP" property="dispatchDate" />
        <result column="finish_date" jdbcType="TIMESTAMP" property="finishDate" />
        <result column="show_bill_code" jdbcType="VARCHAR" property="showBillCode" />
        <result column="extra_field1" jdbcType="DECIMAL" property="extraField1" />
        <result column="warehouse_code_arr" jdbcType="VARCHAR" property="warehouseCodeArr" />
        <result column="carrier_paytype" property="carrierpayType" jdbcType="INTEGER" />
        <result column="settle_setting" property="settleSetting" jdbcType="SMALLINT" />
        <result column="carrier_code" property="carrierCode" jdbcType="VARCHAR" />
        <result column="carrier_name" property="carrierName" jdbcType="VARCHAR" />
    </resultMap>

    <select id="queryMainPoById" resultMap="BaseMainPOResultMap">
        select
            bymi.pk_id,
            bymi.id,
            bymi.expenses_code,
            bymi.business_type,
            bymi.client_id,
            bymi.company_id,
            bymi.expenses_type,
            bymi.cost_dimension,
            bymi.charge_type,
            bymi.fee_flag,
            bymi.quoterule_id,
            bymi.rule_name,
            bymi.remarks,
            bymi.freight,
            bymi.delivery_fee,
            bymi.ultrafar_fee,
            bymi.superframes_fee,
            bymi.excess_fee,
            bymi.reduce_fee,
            bymi.outboundsorting_fee,
            bymi.shortbarge_fee,
            bymi.return_fee,
            bymi.exception_fee,
            bymi.adjust_fee,
            bymi.adjust_remark,
            bymi.other_cost1,
            bymi.other_cost2,
            bymi.other_cost3,
            bymi.other_cost4,
            bymi.other_cost5,
            bymi.other_cost6,
            bymi.other_cost7,
            bymi.other_cost8,
            bymi.other_cost9,
            bymi.other_cost10,
            bymi.other_cost11,
            bymi.other_cost12,
            bymi.sum_fee,
            bymi.cost_attribute,
            bymi.total_weight,
            bymi.total_volume,
            bymi.total_boxes,
            bymi.total_number,
            bymi.oper_code,
            bymi.oper_by,
            bymi.oper_time,
            bymi.create_code,
            bymi.create_by,
            bymi.create_time,
            bymi.del_flag,
            bymi.bill_id,
            bymi.bill_date,
            bymi.business_time,
            bymi.over_num,
            bymi.over_sendnum,
            bymi.storage_fee_price,
            bymi.disposal_fee_price,
            bymi.other_fee_remark,
            fee_type_first,
            fee_create_ate,
            bymi.is_increment,
            bymi.dispatch_date,
            bymi.finish_date,
            bymi.show_bill_code,
            bymi.extra_field1,
            bymi.warehouse_code_arr,
            mc.settle_setting,
            mc.carrier_code,
            mc.carrier_name,
            mc.carrier_paytype,
            t3.total_boxes AS totalBoxes,
            t3.total_weight AS totalWeight,
            t3.total_volume AS totalVolume,
            t3.total_number AS totalNumber
        from bms_yfcost_main_info bymi
        left join bms_carrierinfo mc on bymi.carrier_code = mc.carrier_code
        left join bms_yfcost_extend t3 on t3.main_expense_id = bymi.id and t3.del_flag = '0'
        where bymi.id = #{id}
    </select>




    <select id="queryMiddleByMainId" resultMap="BaseMiddleResultMap">
        select
            bym.id,
            bym.yfbill_id,
            bym.yfbill_type,
            bym.expenses_id,
            bym.main_expense_id,
            bym.oper_by,
            bym.oper_time,
            bym.del_flag,
            bym.share_type,
            IFNULL(bym.share_amount, 0.00)        as share_amount,
            IFNULL(bym.freight, 0.00)             as freight,
            IFNULL(bym.delivery_fee, 0.00)        as delivery_fee,
            IFNULL(bym.ultrafar_fee, 0.00)        as ultrafar_fee,
            IFNULL(bym.superframes_fee, 0.00)     as superframes_fee,
            IFNULL(bym.excess_fee, 0.00)          as excess_fee,
            IFNULL(bym.reduce_fee, 0.00)          as reduce_fee,
            IFNULL(bym.outboundsorting_fee, 0.00) as outboundsorting_fee,
            IFNULL(bym.shortbarge_fee, 0.00)      as shortbarge_fee,
            IFNULL(bym.return_fee, 0.00)          as return_fee,
            IFNULL(bym.exception_fee, 0.00)       as exception_fee,
            IFNULL(bym.other_cost1, 0.00)         as other_cost1,
            IFNULL(bym.other_cost2, 0.00)         as other_cost2,
            IFNULL(bym.other_cost3, 0.00)         as other_cost3,
            IFNULL(bym.other_cost4, 0.00)         as other_cost4,
            IFNULL(bym.other_cost5, 0.00)         as other_cost5,
            IFNULL(bym.other_cost6, 0.00)         as other_cost6,
            IFNULL(bym.other_cost7, 0.00)         as other_cost7,
            IFNULL(bym.other_cost8, 0.00)         as other_cost8,
            IFNULL(bym.other_cost9, 0.00)         as other_cost9,
            IFNULL(bym.other_cost10, 0.00)        as other_cost10,
            IFNULL(bym.other_cost11, 0.00)        as other_cost11,
            IFNULL(bym.other_cost12, 0.00)        as other_cost12,
            byc.total_weight,
            byc.total_volume,
            byc.total_boxes,
            byc.total_number
        from bms_yfexpenses_middle bym
        left join bms_yfbillcodeinfo byc on bym.yfbill_id = byc.id
        where bym.del_flag = 0
        and bym.main_expense_id = #{mainId}
        group by bym.yfbill_id
    </select>



    <select id="queryJobMiddleByMainId" resultMap="BaseMiddleShareResultMap">
        select
        bym.id,
        bym.yfbill_id,
        bym.yfbill_type,
        bym.expenses_id,
        bym.main_expense_id,
        bym.oper_by,
        bym.oper_time,
        bym.del_flag,
        bym.share_type,
        IFNULL(bym.share_amount, 0.00)        as share_amount,
        IFNULL(bym.freight, 0.00)             as freight,
        IFNULL(bym.delivery_fee, 0.00)        as delivery_fee,
        IFNULL(bym.ultrafar_fee, 0.00)        as ultrafar_fee,
        IFNULL(bym.superframes_fee, 0.00)     as superframes_fee,
        IFNULL(bym.excess_fee, 0.00)          as excess_fee,
        IFNULL(bym.reduce_fee, 0.00)          as reduce_fee,
        IFNULL(bym.outboundsorting_fee, 0.00) as outboundsorting_fee,
        IFNULL(bym.shortbarge_fee, 0.00)      as shortbarge_fee,
        IFNULL(bym.return_fee, 0.00)          as return_fee,
        IFNULL(bym.exception_fee, 0.00)       as exception_fee,
        IFNULL(bym.other_cost1, 0.00)         as other_cost1,
        IFNULL(bym.other_cost2, 0.00)         as other_cost2,
        IFNULL(bym.other_cost3, 0.00)         as other_cost3,
        IFNULL(bym.other_cost4, 0.00)         as other_cost4,
        IFNULL(bym.other_cost5, 0.00)         as other_cost5,
        IFNULL(bym.other_cost6, 0.00)         as other_cost6,
        IFNULL(bym.other_cost7, 0.00)         as other_cost7,
        IFNULL(bym.other_cost8, 0.00)         as other_cost8,
        IFNULL(bym.other_cost9, 0.00)         as other_cost9,
        IFNULL(bym.other_cost10, 0.00)        as other_cost10,
        IFNULL(bym.other_cost11, 0.00)        as other_cost11,
        IFNULL(bym.other_cost12, 0.00)        as other_cost12,
        byc.total_weight,
        byc.total_volume,
        byc.total_boxes,
        byc.total_number,
        byc.relate_code
        from bms_yfexpenses_middle_share bym
        left join bms_yfjobbillinfo byc on bym.yfbill_id = byc.id
        where bym.del_flag = '0' and bym.main_expense_id = #{mainId}
        group by bym.yfbill_id
    </select>


    <resultMap type="com.bbyb.joy.bms.domain.extend.BmsYfbillcodeinfoExtend" id="BmsYfbillcodeExtendMap">
        <result property="id" column="id" />
        <result property="pkId" column="pk_id" />
        <result property="schedulingBillCode" column="scheduling_bill_code" />
        <result property="virtualOrderNo" column="virtual_order_no" />
        <result property="hyOrderNo" column="hy_order_no" />
        <result property="projectQuote" column="project_quote" />
        <result property="isToClient" column="is_to_client" />
        <result property="carrierCode" column="carrier_code" />
        <result property="carrierName" column="carrier_name" />
        <result property="companyId" column="company_id" />
        <result property="networkCode" column="network_code" />
        <result property="totalBoxes" column="total_boxes" />
        <result property="totalNumber" column="total_number" />
        <result property="totalWeight" column="total_weight" />
        <result property="totalVolume" column="total_volume" />
        <result property="cargoValue" column="cargo_value" />
        <result property="dispatchDate" column="dispatch_date" />
        <result property="startDate" column="start_date" />
        <result property="finishDate" column="finish_date" />
        <result property="transportType" column="transport_type" />
        <result property="deliveryMode" column="delivery_mode" />
        <result property="lineCode" column="line_code" />
        <result property="lineName" column="line_name" />
        <result property="baseStores" column="base_stores" />
        <result property="baseKilometer" column="base_kilometer" />
        <result property="totalKilometer" column="total_kilometer" />
        <result property="numberLoadingPoints" column="number_loading_points" />
        <result property="numberUnloadingPoints" column="number_unloading_points" />
        <result property="totalVotenumber" column="total_votenumber" />
        <result property="driver" column="driver" />
        <result property="carCode" column="car_code" />
        <result property="carType" column="car_type" />
        <result property="carModel" column="car_model" />
        <result property="headOfficeTimes" column="head_office_times" />
        <result property="provinceOrigin" column="province_origin" />
        <result property="originatingCity" column="originating_city" />
        <result property="originatingArea" column="originating_area" />
        <result property="originatingAddress" column="originating_address" />
        <result property="destinationProvince" column="destination_province" />
        <result property="destinationCity" column="destination_city" />
        <result property="destinationArea" column="destination_area" />
        <result property="destinationAddress" column="destination_address" />
        <result property="costStatus" column="cost_status" />
        <result property="billingStatus" column="billing_status" />
        <result property="createCode" column="create_code" />
        <result property="createBy" column="create_by" />
        <result property="createDeptId" column="create_dept_id" />
        <result property="createTime" column="create_time" />
        <result property="operCode" column="oper_code" />
        <result property="operBy" column="oper_by" />
        <result property="operDeptId" column="oper_dept_id" />
        <result property="operTime" column="oper_time" />
        <result property="delFlag" column="del_flag" />
        <result property="clientId" column="client_id" />
        <result property="bodyOfficeTimes" column="body_office_times" />
        <result property="tlineCode" column="tline_code" />
        <result property="tlineName" column="tline_name" />
        <result property="vehicleTemperatureType" column="vehicle_temperature_type" />
        <result property="orderNo" column="order_no" />
        <result property="failRemark" column="fail_remark" />
        <result property="orderSource" column="order_source" />
        <result property="storeNumber" column="store_number" />
        <result property="expressNo" column="express_no" />
        <result property="originalOrderType" column="original_order_type" />
        <result property="transportMode" column="transport_mode" />
        <result property="workCode" column="workCode" />
        <result property="billId" column="billId" />
        <result property="mainExpenseCode" column="mainExpenseCode" />
        <result property="mainExpenseId" column="mainExpenseId" />
    </resultMap>

    <select id="selectBillAndCostByRelateCode" resultMap="BmsYfbillcodeExtendMap">
        select
            t1.id,
            t1.pk_id,
            t1.scheduling_bill_code,
            t1.virtual_order_no,
            t1.hy_order_no,
            t1.project_quote,
            t1.is_to_client,
            t1.carrier_code,
            t1.carrier_name,
            t1.company_id,
            t1.network_code,
            t1.total_boxes,
            t1.total_number,
            t1.total_weight,
            t1.total_volume,
            t1.cargo_value,
            t1.dispatch_date,
            t1.start_date,
            t1.finish_date,
            t1.transport_type,
            t1.delivery_mode,
            t1.line_code,
            t1.line_name,
            t1.base_stores,
            t1.base_kilometer,
            t1.total_kilometer,
            t1.number_loading_points,
            t1.number_unloading_points,
            t1.total_votenumber,
            t1.driver,
            t1.car_code,
            t1.car_type,
            t1.car_model,
            t1.head_office_times,
            t1.province_origin,
            t1.originating_city,
            t1.originating_area,
            t1.originating_address,
            t1.destination_province,
            t1.destination_city,
            t1.destination_area,
            t1.destination_address,
            t1.cost_status,
            t1.billing_status,
            t1.create_code,
            t1.create_by,
            t1.create_dept_id,
            t1.create_time,
            t1.oper_code,
            t1.oper_by,
            t1.oper_dept_id,
            t1.oper_time,
            t1.del_flag,
            t1.client_id,
            t1.body_office_times,
            t1.tline_code,
            t1.tline_name,
            t1.vehicle_temperature_type,
            t1.order_no,
            t1.fail_remark,
            t1.order_source,
            t1.store_number,
            t1.express_no,
            t1.original_order_type,
            t1.transport_mode,
            t1.virtual_order_no AS workCode,
            GROUP_CONCAT(DISTINCT t3.bill_id) billId,
            t4.expenses_code AS mainExpenseCode,
            t4.id AS mainExpenseId
        from bms_yfbillcodeinfo t1
        left join bms_yfexpenses_middle t2 on t2.yfbill_id = t1.id and t2.del_flag = '0'
        left join bms_yfcost_info t3 on t3.main_expense_id = t2.main_expense_id and t3.del_flag = '0'
        left join bms_yfcost_main_info t4 on t4.id = t3.main_expense_id and t4.del_flag = '0'
        where t1.del_flag = '0'
        <if test="codes != null and codes.size>0 ">
            AND t1.virtual_order_no in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
        GROUP BY workCode
    </select>



</mapper>
