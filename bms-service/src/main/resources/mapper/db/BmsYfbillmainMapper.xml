<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfbillmainMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain" id="BmsYfbillmainResult">
        <result property="id"    column="id"    />
        <result property="fatherid"    column="fatherid"    />
        <result property="billType"    column="bill_type"    />
        <result property="billCode"    column="bill_code"    />
        <result property="billName"    column="bill_name"    />
        <result property="billDate"    column="bill_date"    />
        <result property="companyId"    column="company_id"    />
        <result property="quoteId"    column="quote_id"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="billMarking"    column="bill_marking"    />
        <result property="votes"    column="votes"    />
        <result property="billAmount"    column="bill_amount"    />
        <result property="adjustedAmount"    column="adjusted_amount"    />
        <result property="yfAmount"    column="yf_amount"    />
        <result property="billState"    column="bill_state"    />
        <result property="auditState"    column="audit_state"    />
        <result property="auditUser"    column="audit_user"    />
        <result property="auditUserName"    column="audit_user_name"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="submitStatus"    column="submit_status"    />
        <result property="ticketState"    column="ticket_state"    />
        <result property="submitDate"    column="submit_date"    />
        <result property="ticketAmount"    column="ticket_amount"    />
        <result property="ticketNum"    column="ticket_num"    />
        <result property="ticketUserName"    column="ticket_user_name"    />
        <result property="ticketTime"    column="ticket_time"    />
        <result property="hxAmount"    column="hx_amount"    />
        <result property="hxState"    column="hx_state"    />
        <result property="hxUserName"    column="hx_user_name"    />
        <result property="hxTime"    column="hx_time"    />
        <result property="hxRemark"    column="hx_remark"    />
        <result property="hxNum"    column="hx_num"    />
        <result property="billRemark"    column="bill_remark"    />
        <result property="arClerk"    column="ar_clerk"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="responsibleMoney"    column="responsible_money"    />
        <result property="warehouseCode"    column="warehouse_code"    />

    </resultMap>

    <sql id="selectBmsYfbillmainVo">
        SELECT id,
           fatherid,
           bill_type,
           IFNULL(merge_status, 0) AS mergeStatus,
           bill_code,
           bill_name,
           bill_date,
           company_id,
           quote_id,
           carrier_id,
           bill_marking,
           votes,
           bill_amount,
           adjusted_amount,
           yf_amount,
           bill_state,
           audit_state,
           audit_user,
           audit_user_name,
           audit_time,
           audit_remark,
           submit_status,
           ticket_state,
           submit_date,
           ticket_amount,
           ticket_num,
           ticket_user_name,
           ticket_time,
           hx_amount,
           hx_state,
           hx_user_name,
           hx_time,
           hx_remark,
           hx_num,
           bill_remark,
           ar_clerk,
           create_code,
           create_by,
           create_time,
           create_dept_id,
           oper_dept_id,
           oper_code,
           oper_by,
           oper_time,
           del_flag,
           client_id,
           IFNULL(responsible_money, 0) AS responsible_money
        FROM bms_yfbillmain
    </sql>

    <select id="selectBmsYfbillmainList" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain" resultMap="BmsYfbillmainResult">
        select
            a.id,
            a.fatherid,
            a.bill_type,
            a.bill_code,
            a.bill_name,
            a.bill_date,
            a.company_id,
            a.quote_id,
            a.carrier_id,
            a.bill_marking,
            a.votes,
            a.bill_amount,
            a.adjusted_amount,
            a.yf_amount,
            a.bill_state,
            a.audit_state,
            a.audit_user,
            a.audit_user_name,
            a.audit_time,
            a.audit_remark,
            a.submit_status,
            a.ticket_state,
            a.submit_date,
            a.ticket_amount,
            a.ticket_num,
            a.ticket_user_name,
            a.ticket_time,
            a.hx_amount,
            a.hx_state,
            a.hx_user_name,
            a.hx_time,
            a.hx_remark,
            a.hx_num,
            a.bill_remark,
            ar_clerk,
            create_code,
            a.create_by,
            a.create_time,
            a.create_dept_id,
            a.oper_dept_id,
            a.oper_code,
            a.oper_by,
            a.oper_time,
            a.del_flag,
            a.yf_amount                    sfAmount,
            IFNULL(a.bill_source, 1)  as   billSource,
            b.carrier_code,
            b.carrier_name,
            d.id                      as   client_id,
            d.client_code             as   client_code,
            d.client_name             as   client_name,
            IFNULL(a.merge_status, 0) as   mergeStatus,
            ifnull(a.responsible_money, 0) responsible_money
        FROM bms_yfbillmain a
        LEFT JOIN bms_carrierinfo b on a.carrier_id = b.id
        LEFT JOIN bms_clientinfo d on a.client_id = d.id
        <where>
            a.del_flag = 0
            AND (IFNULL(a.fatherid,0)=0 OR IFNULL(a.fatherid,0)=a.id)
            <if test="carrierList != null and carrierList.size>0">
                and b.carrier_code in
                <foreach item="carrier" collection="carrierList" open="(" separator="," close=")">
                    #{carrier}
                </foreach>
            </if>
            <if test="fatherid != null "> and a.fatherid = #{fatherid}</if>
            <if test="billType != null "> and a.bill_type = #{billType}</if>
            <if test="billCode != null  and billCode != ''"> and a.bill_code = #{billCode}</if>
            <if test="billName != null  and billName != ''"> and a.bill_name like concat('%', #{billName}, '%')</if>
            <if test="carrierCode != null  and carrierCode != ''"> and b.carrier_code like concat('%', #{carrierCode}, '%')</if>
            <if test="carrierName != null  and carrierName != ''"> and b.carrier_name like concat('%', #{carrierName}, '%')</if>
            <if test="billDate != null  and billDate != ''"> and a.bill_date = #{billDate}</if>
            <if test="beginCreateTime != null"> and a.create_time &gt;= #{beginCreateTime}</if>
            <if test="endCreateTime != null"> and a.create_time &lt;= #{endCreateTime}</if>
            <if test="companyIdList != null and companyIdList.size>0">
                and a.company_id in
                <foreach item="compId" collection="companyIdList" open="(" separator="," close=")">
                    #{compId}
                </foreach>
            </if>
            <if test="clientName!=null">
                and d.client_name like concat('%', #{clientName}, '%')
            </if>
            <if test="quoteId != null "> and a.quote_id = #{quoteId}</if>
            <if test="carrierId != null "> and a.carrier_id = #{carrierId}</if>
            <if test="billMarking != null "> and a.bill_marking = #{billMarking}</if>
            <if test="votes != null "> and a.votes = #{votes}</if>
            <if test="billAmount != null "> and a.bill_amount = #{billAmount}</if>
            <if test="adjustedAmount != null "> and a.adjusted_amount = #{adjustedAmount}</if>
            <if test="yfAmount != null "> and a.yf_amount = #{yfAmount}</if>
            <if test="billState != null "> and a.bill_state = #{billState}</if>
            <if test="auditState != null "> and a.audit_state = #{auditState}</if>
            <if test="auditUser != null "> and a.audit_user = #{auditUser}</if>
            <if test="auditUserName != null  and auditUserName != ''"> and a.audit_user_name like concat('%', #{auditUserName}, '%')</if>
            <if test="auditTime != null "> and a.audit_time = #{auditTime}</if>
            <if test="auditRemark != null  and auditRemark != ''"> and a.audit_remark = #{auditRemark}</if>
            <if test="submitStatus != null "> and a.submit_status = #{submitStatus}</if>
            <if test="ticketState != null "> and a.ticket_state = #{ticketState}</if>
            <if test="submitDate != null "> and a.submit_date = #{submitDate}</if>
            <if test="ticketAmount != null "> and a.ticket_amount = #{ticketAmount}</if>
            <if test="ticketNum != null "> and a.ticket_num = #{ticketNum}</if>
            <if test="ticketUserName != null  and ticketUserName != ''"> and a.ticket_user_name like concat('%', #{ticketUserName}, '%')</if>
            <if test="ticketTime != null "> and a.ticket_time = #{ticketTime}</if>
            <if test="hxAmount != null "> and a.hx_amount = #{hxAmount}</if>
            <if test="hxState != null "> and a.hx_state = #{hxState}</if>
            <if test="hxUserName != null  and hxUserName != ''"> and a.hx_user_name like concat('%', #{hxUserName}, '%')</if>
            <if test="hxTime != null "> and a.hx_time = #{hxTime}</if>
            <if test="hxRemark != null  and hxRemark != ''"> and a.hx_remark = #{hxRemark}</if>
            <if test="hxNum != null "> and a.hx_num = #{hxNum}</if>
            <if test="billRemark != null  and billRemark != ''"> and a.bill_remark = #{billRemark}</if>
            <if test="arClerk != null  and arClerk != ''"> and a.ar_clerk = #{arClerk}</if>
            <if test="createCode != null  and createCode != ''"> and a.create_code = #{createCode}</if>
            <if test="createDeptId != null "> and a.create_dept_id = #{createDeptId}</if>
            <if test="operDeptId != null "> and a.oper_dept_id = #{operDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and a.oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and a.oper_by = #{operBy}</if>
            <if test="operTime != null "> and a.oper_time = #{operTime}</if>
            <if test="mergeStatus != null "> and a.merge_status = #{mergeStatus}</if>
        </where>
        GROUP by a.id
        order by a.create_time desc
    </select>

    <select id="selectBmsYfbillmainExportList" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYfbillmainExport">
        select
            a.id,
            fatherid,
            bill_type,
            bill_code,
            bill_name,
            bill_date,
            company_id,
            quote_id,
            carrier_id,
            bill_marking,
            votes,
            bill_amount,
            adjusted_amount,
            yf_amount,
            bill_state,
            audit_state,
            audit_user,
            audit_user_name,
            audit_time,
            audit_remark,
            submit_status,
            ticket_state,
            submit_date,
            ticket_amount,
            ticket_num,
            ticket_user_name,
            ticket_time,
            hx_amount,
            hx_state,
            hx_user_name,
            hx_time,
            hx_remark,
            hx_num,
            bill_remark,
            ar_clerk,
            create_code,
            a.create_by,
            a.create_time,
            a.create_dept_id,
            a.oper_dept_id,
            a.oper_code,
            a.oper_by,
            a.oper_time,
            a.del_flag,
            b.carrier_code,
            b.carrier_name,
            IFNULL(a.merge_status, 0) as mergeStatus
        from bms_yfbillmain a
        left join bms_carrierinfo b on a.carrier_id = b.id
        <where>
            1=1 and IFNULL(a.fatherid,0)=0
            <if test="fatherid != null "> AND a.fatherid = #{fatherid}</if>
            <if test="billType != null "> AND a.bill_type = #{billType}</if>
            <if test="billCode != null  and billCode != ''"> AND a.bill_code = #{billCode}</if>
            <if test="billName != null  and billName != ''"> AND a.bill_name like concat('%', #{billName}, '%')</if>
            <if test="carrierCode != null  and carrierCode != ''"> AND b.carrier_code LIKE concat('%', #{carrierCode}, '%')</if>
            <if test="carrierName != null  and carrierName != ''"> AND b.carrier_name LIKE concat('%', #{carrierName}, '%')</if>
            <if test="billDate != null  and billDate != ''"> AND a.bill_date = #{billDate}</if>
            <if test="beginCreateTime != null"> AND a.create_time &gt;= #{beginCreateTime}</if>
            <if test="endCreateTime != null"> AND a.create_time &lt;= #{endCreateTime}</if>
            <if test="companyIdList != null and companyIdList.size>0">
                AND a.company_id IN
                <foreach item="compId" collection="companyIdList" open="(" separator="," close=")">
                    #{compId}
                </foreach>
            </if>
            <if test="quoteId != null "> AND a.quote_id = #{quoteId}</if>
            <if test="carrierId != null "> AND a.carrier_id = #{carrierId}</if>
            <if test="billMarking != null "> AND a.bill_marking = #{billMarking}</if>
            <if test="votes != null "> AND a.votes = #{votes}</if>
            <if test="billAmount != null "> AND a.bill_amount = #{billAmount}</if>
            <if test="adjustedAmount != null "> AND a.adjusted_amount = #{adjustedAmount}</if>
            <if test="yfAmount != null "> AND a.yf_amount = #{yfAmount}</if>
            <if test="billState != null "> AND a.bill_state = #{billState}</if>
            <if test="auditState != null "> AND a.audit_state = #{auditState}</if>
            <if test="auditUser != null "> AND a.audit_user = #{auditUser}</if>
            <if test="auditUserName != null  and auditUserName != ''"> AND a.audit_user_name like concat('%', #{auditUserName}, '%')</if>
            <if test="auditTime != null "> AND a.audit_time = #{auditTime}</if>
            <if test="auditRemark != null  and auditRemark != ''"> and a.audit_remark = #{auditRemark}</if>
            <if test="submitStatus != null "> and a.submit_status = #{submitStatus}</if>
            <if test="ticketState != null "> and a.ticket_state = #{ticketState}</if>
            <if test="submitDate != null "> and a.submit_date = #{submitDate}</if>
            <if test="ticketAmount != null "> and a.ticket_amount = #{ticketAmount}</if>
            <if test="ticketNum != null "> and a.ticket_num = #{ticketNum}</if>
            <if test="ticketUserName != null  and ticketUserName != ''"> and a.ticket_user_name like concat('%', #{ticketUserName}, '%')</if>
            <if test="ticketTime != null "> and a.ticket_time = #{ticketTime}</if>
            <if test="hxAmount != null "> and a.hx_amount = #{hxAmount}</if>
            <if test="hxState != null "> and a.hx_state = #{hxState}</if>
            <if test="hxUserName != null  and hxUserName != ''"> and a.hx_user_name like concat('%', #{hxUserName}, '%')</if>
            <if test="hxTime != null "> and a.hx_time = #{hxTime}</if>
            <if test="hxRemark != null  and hxRemark != ''"> and a.hx_remark = #{hxRemark}</if>
            <if test="hxNum != null "> and a.hx_num = #{hxNum}</if>
            <if test="billRemark != null  and billRemark != ''"> and a.bill_remark = #{billRemark}</if>
            <if test="arClerk != null  and arClerk != ''"> and a.ar_clerk = #{arClerk}</if>
            <if test="createCode != null  and createCode != ''"> and a.create_code = #{createCode}</if>
            <if test="createDeptId != null "> and a.create_dept_id = #{createDeptId}</if>
            <if test="operDeptId != null "> and a.oper_dept_id = #{operDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and a.oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and a.oper_by = #{operBy}</if>
            <if test="operTime != null "> and a.oper_time = #{operTime}</if>
            <if test="mergeStatus != null "> and a.merge_status = #{mergeStatus}</if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <select id="selectBmsYfbillmainById" parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">
        SELECT
            a.id,
            a.fatherid,
            a.bill_type AS billType,
            IFNULL(a.merge_status,0) AS mergeStatus,
            a.bill_code AS billCode,
            IFNULL(a.bill_source,1) AS billSource,
            a.bill_name AS billName,
            a.bill_date AS billDate,
            a.company_id AS companyId,
            a.quote_id AS quoteI,
            a.carrier_id AS carrierId,
            a.bill_marking AS billMarking,
            a.votes,
            a.bill_amount AS billAmount,
            a.adjusted_amount AS adjustedAmount,
            a.yf_amount AS yfAmount,
            a.bill_state AS billState,
            a.audit_state AS auditState,
            a.audit_user AS auditUser,
            a.audit_user_name AS auditUserName,
            a.audit_time AS auditTime,
            a.audit_remark AS auditRemark,
            a.submit_status AS submitStatus,
            a.ticket_state AS ticketState,
            a.ticket_user_name AS ticketUserName,
            a.ticket_time AS ticketTime,
            a.ticket_num AS ticketNum,
            a.submit_date AS submitDate,
            IFNULL(a.ticket_amount,0) AS ticketAmount,
            a.ticket_num AS ticketNum,
            a.ticket_user_name AS ticketUserName,
            a.ticket_time AS ticketTime,
            a.hx_amount AS hxAmount,
            a.hx_state AS hxState,
            a.hx_user_name AS hxUserName,
            a.hx_time AS hxTime,
            a.hx_remark AS hxRemark,
            a.hx_num AS hxNum,
            a.bill_remark AS billRemark,
            a.ar_clerk AS arClerk,
            a.create_code AS createCode,
            a.create_by AS createBy,
            a.create_time AS createTime,
            a.create_dept_id AS createDeptId,
            a.oper_dept_id AS operDeptId,
            a.oper_code AS operCode,
            a.oper_by AS operBy,
            a.oper_time AS operTime,
            a.del_flag AS delFlag,
            b.carrier_code AS carrierCode,
            b.carrier_name AS carrierName,
            d.id AS clientId,
            d.client_code AS clientCode,
            d.client_name AS clientName,
            ifnull(a.responsible_money,0) responsibleMoney
        FROM bms_yfbillmain a
        LEFT JOIN bms_carrierinfo b ON a.carrier_id = b.id
        LEFT JOIN bms_clientinfo d ON d.id = a.client_id
        WHERE a.id = #{id}
        GROUP BY a.id
    </select>


    <select id="selectBmsYfbillmainBycarrierId" parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">
       select
       id as carrierId,
       carrier_code as carrierCode,
       carrier_name as carrierName
       from bms_carrierinfo
       where carrier_code = #{code}
    </select>

    <select id="selectBmsYfbillmainByFatherid" parameterType="java.lang.String" resultMap="BmsYfbillmainResult">
        <include refid="selectBmsYfbillmainVo"/>
        where
        fatherid = #{id}
        and IFNULL(del_flag,'0') = '0'
    </select>

    <select id="selectBillmainAndPurseList"  resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmainBean">
        select
        a.id as codeInfoId,
        null as codeType,
        null as relateCode,
        null as schedulingBillCode,
        a.relation_code as clientCode,
        b.carrier_name as clientName,
        a.relation_code as carrierCode,
        b.carrier_name as carrierName,
        null as companyId,
        null as networkCode,
        null as warehouseCode,
        null as totalBoxes,
        null as totalNumber,
        null as totalWeight,
        null as totalVolume,
        null as cargoValue,
        null as orderDate,
        null as signingDate,
        null as ifAutarky,
        null as storageServiceProvider,
        null as lineCode,
        null as lineName,
        null as ifBaseStores,
        null as ifSuperBaseKilometer,
        null as storeDistanceKilometer,
        null as deliveryCode,
        null as deliveryName,
        null as storeCode,
        null as receivingStore,
        null as provinceOrigin,
        null as originatingCity,
        null as originatingArea,
        null as originatingAddress,
        null as destinationProvince,
        null as destinationCity,
        null as destinationArea,
        null as destinationAddress,
        null as costStatus,
        null as billingStatus,
        null as warehouseArea,
        null as volume,
        null as weight,
        null as trust,
        null as temperatureType,
        null as boxType,
        null as oddBoxes,
        null as instorageTime,
        a.sku_name as skuName,
        null as skuCode,
        a.ys_feecode as ysFeecode,
        a.register_time as registerTime,
        a.registerr_companyname as registerrCompanyname,
        a.fee_flag as feeFlag,
        a.ys_feecode as expensesCode,
        null as ysbilType,
        a.id as id,
        null as pkId,
        null as expensesCode,
        null as businessType,
        null as clientId,
        null as institutionId,
        null as expensesType,
        null as costDimension,
        null as chargeType,
        null as feeFlag,
        null as quoteruleId,
        null as ruleName,
        null as remarks,
        null as freight,
        null as deliveryFee,
        null as OutboundsortingFee,
        null as shortbargeFee,
        null as superframesFee,
        null as excessFee,
        null as UltrafarFee,
        null as exceptionFee,
        null as otherCost1,
        null as otherCost2,
        null as otherCost3,
        null as otherCost4,
        null as otherCost5,
        null as otherCost6,
        null as otherCost7,
        null as otherCost8,
        null as otherCost9,
        null as otherCost10,
        null as otherCost11,
        null as otherCost12,
        null as adjustFee,
        a.create_code as createCode,
        a.create_by as createBy,
        a.create_time as createTime,
        a.oper_code as operCode,
        a.oper_time as operTime,
        a.oper_by as operBy,
        a.register_time as registerTime,
        a.registerr_companyname as registerrCompanyname,
        a.registerr_company_id as registerrCompanyId,
        a.num as num,
        a.price as price,
        a.unit as unit,
        /*a.remark as remark,*/
        a.adjust_remark as remark,
        a.spec as spec,
        a.total_amount as totalAmount,
        a.oper_code as operCode,
        a.oper_by as operBy,
        a.oper_time as operTime,
        a.del_flag as delFlag,
        a.bill_id as billId,
        a.bill_date as billDate
        from bms_yfpurchase_feeinfo a
        inner join bms_carrierinfo b
        on a.relation_code = b.carrier_code
        where
        1=1
        and a.bill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="selectBillmainAndCostInfoListByIds"  resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmainBean">
        SELECT * FROM (
            SELECT
                CASE
                    WHEN c.expenses_dimension = 1 AND c.expenses_type = 1 THEN bym.relate_code
                    ELSE '-'
                END                                               AS schedulingBillCode,
                CASE
                    WHEN c.expenses_dimension = 1 THEN bym.relate_code
                    ELSE '-'
                END                                               AS businessCode,
                CASE
                    WHEN c.expenses_dimension = 1 THEN bym.relate_code
                    ELSE '-'
                END                                               AS relateCode,
                c.warehouse_code                                  AS warehouseCode,
                c.total_boxes                                     AS totalBoxes,
                c.total_number                                    AS totalNumber,
                c.total_weight                                    AS totalWeight,
                c.total_volume                                    AS totalVolume,
                c.total_cargo_value                               AS cargoValue,
                c.id                                              AS id,
                c.pk_id                                           AS pkId,
                DATE_FORMAT(c.business_time, '%Y-%m-%d %H:%i:%s') AS businessTime,
                c.expenses_code                                   AS expensesCode,
                c.expenses_type                                   AS expensesType,
                c.charge_type                                     AS chargeType,
                c.quoter_rule_detail_id                           AS quoteruleId,
                c.quoter_rule_detail_name                         AS ruleName,
                c.adjust_remark                                   AS adjustRemark,
                IFNULL(c.freight, 0)                              AS freight,
                IFNULL(c.delivery_fee, 0)                         AS deliveryFee,
                IFNULL(c.outboundsorting_fee, 0)                  AS OutboundsortingFee,
                IFNULL(c.shortbarge_fee, 0)                       AS shortbargeFee,
                IFNULL(c.superframes_fee, 0)                      AS superframesFee,
                IFNULL(c.excess_fee, 0)                           AS excessFee,
                IFNULL(c.reduce_fee, 0)                           AS reduceFee,
                IFNULL(c.Ultrafar_fee, 0)                         AS UltrafarFee,
                IFNULL(c.exception_fee, 0)                        AS exceptionFee,
                IFNULL(c.return_fee, 0)                           AS returnFee,
                IFNULL(c.other_cost1, 0)                          AS otherCost1,
                IFNULL(c.other_cost2, 0)                          AS otherCost2,
                IFNULL(c.other_cost3, 0)                          AS otherCost3,
                IFNULL(c.other_cost4, 0)                          AS otherCost4,
                IFNULL(c.other_cost5, 0)                          AS otherCost5,
                IFNULL(c.other_cost6, 0)                          AS otherCost6,
                IFNULL(c.other_cost7, 0)                          AS otherCost7,
                IFNULL(c.other_cost8, 0)                          AS otherCost8,
                IFNULL(c.other_cost9, 0)                          AS otherCost9,
                IFNULL(c.other_cost10, 0)                         AS otherCost10,
                IFNULL(c.other_cost11, 0)                         AS otherCost11,
                IFNULL(c.other_cost12, 0)                         AS otherCost12,
                IFNULL(c.adjust_fee, 0)                           AS adjustFee,
                IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
                IFNULL(c.shortbarge_fee, 0) +
                IFNULL(c.superframes_fee, 0) + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
                IFNULL(c.exception_fee, 0) +
                IFNULL(c.return_fee, 0)                           AS basicFee,          -- 基础费合计
                IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) + IFNULL(c.other_cost3, 0) + IFNULL(c.other_cost4, 0) +
                IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) + IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) +
                IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) + IFNULL(c.other_cost11, 0) +
                IFNULL(c.other_cost12, 0)                         AS otherFee,          -- 其他费合计
                IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
                IFNULL(c.shortbarge_fee, 0) +
                IFNULL(c.superframes_fee, 0) + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
                IFNULL(c.exception_fee, 0) +
                IFNULL(c.return_fee, 0) + IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) + IFNULL(c.other_cost3, 0) +
                IFNULL(c.other_cost4, 0) +
                IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) + IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) +
                IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) + IFNULL(c.other_cost11, 0) +
                IFNULL(c.other_cost12, 0)                         AS sumFee,            -- 总费用
                NULL                                              AS totalAmount,
                c.oper_code                                       AS operCode,
                c.del_flag                                        AS delFlag,
                c.bill_id                                         AS billId,
                c.company_id                                      AS companyId,
                c.bill_date                                       AS billDate,
                c.id                                              AS clientId,
                cli.client_code                                   AS clientCode,
                cli.client_Name                                   AS clientName,
                CASE
                    WHEN c.charge_type = 1 THEN '自动计费'
                    WHEN c.charge_type = 2 THEN '手动计费'
                ELSE ''
                END                                               AS chargeTypeName,
                c.business_time                                   AS instorageTime,
                CASE
                    WHEN c.expenses_type = 1 THEN '运输单'
                    WHEN c.expenses_type = 2 THEN '出库单'
                    WHEN c.expenses_type = 3 THEN '入库单'
                    WHEN c.expenses_type = 4 THEN '库存单'
                ELSE '' END                                       AS codeTypeName,
                c.warehouse_name                                  AS warehouseName,
                DATE_FORMAT(c.oper_time, '%Y-%m-%d %H:%i:%s')     AS operTime,          -- 计费时间
                c.expenses_dimension                              AS costDimension,     -- 费用维度
                CASE
                    WHEN c.expenses_dimension = 1 THEN '单'
                    WHEN c.expenses_dimension = 2 THEN '趟'
                    WHEN c.expenses_dimension = 3 THEN '日'
                    WHEN c.expenses_dimension = 4 THEN '月'
                    WHEN c.expenses_dimension = 5 THEN '品规'
                ELSE '' END                                       AS costDimensionName, -- 费用维度名称
                c.expenses_mark                                   AS feeFlag,           -- 费用标识
                CASE
                    WHEN c.expenses_mark = 1 THEN '正常费用'
                    WHEN c.expenses_mark = 2 THEN '冲销费'
                ELSE '' END                                       AS feeFlagName,       -- 费用标识名称
                c.create_time                                     AS createTime,        -- 新增时间
                DATE_FORMAT(c.dispatch_date, '%Y-%m-%d %H:%i:%s') AS dispatchDate,      -- 配载日期
                DATE_FORMAT(c.finish_date, '%Y-%m-%d %H:%i:%s')   AS finishDate,        -- 完成时间
                car.carrier_name                                  AS carrierName,       -- 承运商名称
                car.carrier_code                                  AS carrierCode,       -- 承运商编码
                c.oper_by                                         AS operBy,            -- 计费人
                c.remarks                                         AS remarks,           -- 计费备注
                c.is_increment                                    AS isIncrement,
                c.fee_type_first                                  AS feeTypeFirst,
                c.total_extra_fee_number                          AS totalQuantity,
                c.total_extra_fee_price                           AS price,
                c.main_code_id                                    AS mainExpenseId,
                c.main_pk_id                            AS mainExpensePkId
            FROM bms_yfcost_info c
            LEFT JOIN bms_yfexpenses_middle bym ON c.main_pk_id = bym.main_pk_id AND bym.del_flag = 0
            LEFT JOIN bms_clientinfo cli ON cli.id = c.client_id
            LEFT JOIN bms_carrierinfo car ON car.id = c.carrier_id
            WHERE c.del_flag = 0
            <if test="mainExpensePkId!=null ">
                c.main_pk_id = #{mainExpensePkId}
            </if>
            AND c.bill_id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND c.expenses_type IN
            <foreach item="type" collection="codeTypes" open="(" separator="," close=")">
                #{type}
            </foreach>
            GROUP BY c.id
        ) a
    </select>


    <select id="getCostList" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        SELECT
            CASE
                WHEN a.expenses_dimension = 1 THEN e.relate_code
                ELSE '-'
            END                                               AS businessCode,
            CASE
                WHEN a.expenses_dimension = 1 THEN e.relate_code
                ELSE '-'
            END                                               AS relateCode,
            a.id AS id,
            a.pk_id AS pkId,
            a.expenses_code AS expensesCode,
            a.total_boxes AS totalBoxes,
            a.total_number AS totalNumber,
            a.total_weight AS totalWeight,
            a.total_volume AS totalVolume,
            a.warehouse_code AS warehouseCode,
            a.warehouse_name AS warehouseName,
            d.client_code AS clientCode,
            d.client_name AS clientName,
            c.carrier_code AS carrierCode,
            c.carrier_name AS carrierName,
            a.business_time AS businessTime,
            a.expenses_type AS expensesType,
            a.expenses_dimension AS costDimension,
            a.charge_type AS chargeType,
            a.expenses_mark AS feeFlag,
            a.remarks AS remarks,
            a.freight AS freight,
            a.ultrafar_fee AS ultrafarFee,
            a.superframes_fee AS superframesFee,
            a.excess_fee AS excessFee,
            a.reduce_fee AS reduceFee,
            a.delivery_fee AS deliveryFee,
            a.outboundsorting_fee AS outboundsortingFee,
            a.shortbarge_fee AS shortbargeFee,
            a.return_fee AS returnFee,
            a.exception_fee AS exceptionFee,
            a.other_cost1 AS otherCost1,
            a.other_cost2 AS otherCost2,
            a.other_cost3 AS otherCost3,
            a.other_cost4 AS otherCost4,
            a.other_cost5 AS otherCost5,
            a.other_cost6 AS otherCost6,
            a.other_cost7 AS otherCost7,
            a.other_cost8 AS otherCost8,
            a.other_cost9 AS otherCost9,
            a.other_cost10 AS otherCost10,
            a.other_cost11 AS otherCost11,
            a.other_cost12 AS otherCost12,
            a.adjust_fee AS adjustFee,
            a.main_code_id AS mainExpenseId,
            a.main_pk_id AS mainExpensePkId,
            a.company_id AS companyId,
            a.client_id AS clientId,
            e.code_id AS yfbillId,
            e.code_type AS yfbilType,
            a.settle_main_id AS settleMainId,
            SUM(
            IFNULL( a.freight, 0 ) + IFNULL( a.ultrafar_fee, 0 ) + IFNULL( a.superframes_fee, 0 ) + IFNULL( a.excess_fee, 0 )
            + IFNULL( a.delivery_fee, 0 ) + IFNULL( a.outboundsorting_fee, 0 ) + IFNULL( a.shortbarge_fee, 0 ) +
            IFNULL( a.return_fee, 0 ) + IFNULL( a.exception_fee, 0 ) + IFNULL( a.other_cost1, 0 ) + IFNULL( a.other_cost2, 0 ) +
            IFNULL( a.other_cost3, 0 ) + IFNULL( a.other_cost4, 0 ) + IFNULL( a.other_cost5, 0 ) + IFNULL( a.other_cost6, 0 ) +
            IFNULL( a.other_cost7, 0 ) + IFNULL( a.other_cost8, 0 ) + IFNULL( a.other_cost9, 0 ) + IFNULL( a.other_cost10, 0 ) +
            IFNULL( a.other_cost11, 0 ) + IFNULL( a.other_cost12, 0) + IFNULL( a.adjust_fee, 0 ) + IFNULL(a.reduce_fee,0)
            ) AS cargoValue,
            null AS totalAmount,
            a.bill_id AS billMainid
        FROM bms_yfcost_info a
        LEFT JOIN bms_yfexpenses_middle e ON a.main_pk_id = e.main_pk_id AND e.del_flag = 0
        LEFT JOIN bms_carrierinfo c ON a.carrier_id = c.id
        LEFT JOIN bms_clientinfo d ON d.id = a.client_id
        <where>
            1=1
            <if test="mainExpensePkId!=null ">
                a.main_pk_id = #{mainExpensePkId}
            </if>
            and IFNULL(a.bill_id,0)=0
            and a.del_flag =0
            <if test="carrierList!=null and carrierList.size>0">
                and c.carrier_code in
                <foreach item="carrier" collection="carrierList" open="(" separator="," close=")">
                    #{carrier}
                </foreach>
            </if>
            <if test="companyIdList!=null">
                and a.company_id in
                <foreach collection="companyIdList" item="compayId" open="(" separator="," close=")" >
                    #{compayId}
                </foreach>
            </if>
            <if test="expensesTypeList!=null">
                and a.expenses_type in
                <foreach collection="expensesTypeList" item="value" open="(" separator="," close=")" >
                    #{value}
                </foreach>
            </if>
            <if test="carrierCode!=null and carrierCode != ''">
                and c.carrier_code = #{carrierCode}
            </if>
            <if test="clientId!=null">
                and a.client_id = #{clientId}
            </if>
            <if test="carrierCode!=null and carrierCode != ''">
                and c.carrier_code = #{carrierCode}
            </if>
            <if test="businessCode!=null and businessCode !='' ">
                and e.relate_code like  concat('%',#{businessCode},'%')
            </if>
            <if test="expensesCode!=null and expensesCode !='' ">
                and a.expenses_code like  concat('%',#{expensesCode},'%')
            </if>
            <if test="businessType!=null and businessType !='' ">
                and a.business_type like  concat('%',#{businessType},'%')
            </if>
            <if test="warehouseCode!=null and warehouseCode !='' ">
                and a.warehouse_code like  concat('%',#{warehouseCode},'%')
            </if>
            <if test="warehouseName!=null and warehouseName !='' ">
                and a.warehouse_name like  concat('%',#{warehouseName},'%')
            </if>
            <if test="clientCode!=null and clientCode !='' ">
                and c.carrier_code like  concat('%',#{clientCode},'%')
            </if>
            <if test="clientName!=null and clientName !='' ">
                and c.carrier_name like  concat('%',#{clientName},'%')
            </if>
            <if test="beginBusinessTime!=null  ">
                and a.business_time &gt;= #{beginBusinessTime}
            </if>
            <if test="endBusinessTime!=null ">
                and a.business_time &lt;= #{endBusinessTime}
            </if>
        </where>
        GROUP BY a.id
        ORDER BY a.business_time DESC
    </select>

    <select id="getCostWareList" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        SELECT
            CASE
                WHEN a.expenses_dimension = 1 THEN b.relate_code
                ELSE '-'
            END                   AS businessCode,
            CASE
                WHEN a.expenses_dimension = 1 THEN b.relate_code
                ELSE '-'
            END                   AS relateCode,
            a.id                  AS id,
            a.pk_id               AS pkId,
            a.company_id          AS companyId,
            a.expenses_code       AS expensesCode,
            a.total_boxes         AS totalBoxes,
            a.total_number        AS totalNumber,
            a.total_weight        AS totalWeight,
            a.total_volume        AS totalVolume,
            a.warehouse_code      AS warehouseCode,
            a.warehouse_name      AS warehouseName,
            d.client_code         AS clientCode,
            d.client_name         AS clientName,
            c.carrier_code        AS carrierCode,
            c.carrier_name        AS carrierName,
            a.business_time       AS businessTime,
            a.expenses_type       AS expensesType,
            a.expenses_dimension  AS costDimension,
            a.charge_type         AS chargeType,
            a.expenses_mark       AS feeFlag,
            a.remarks             AS remarks,
            a.freight             AS freight,
            a.ultrafar_fee        AS ultrafarFee,
            a.superframes_fee     AS superframesFee,
            a.excess_fee          AS excessFee,
            a.delivery_fee        AS deliveryFee,
            a.outboundsorting_fee AS outboundsortingFee,
            a.shortbarge_fee      AS shortbargeFee,
            a.return_fee          AS returnFee,
            a.exception_fee       AS exceptionFee,
            a.other_cost1         AS otherCost1,
            a.other_cost2         AS otherCost2,
            a.other_cost3         AS otherCost3,
            a.other_cost4         AS otherCost4,
            a.other_cost5         AS otherCost5,
            a.other_cost6         AS otherCost6,
            a.other_cost7         AS otherCost7,
            a.other_cost8         AS otherCost8,
            a.other_cost9         AS otherCost9,
            a.other_cost10        AS otherCost10,
            a.other_cost11        AS otherCost11,
            a.other_cost12        AS otherCost12,
            a.adjust_fee          AS adjustFee,
            SUM(
            IFNULL(a.freight, 0) + IFNULL(a.ultrafar_fee, 0) + IFNULL(a.superframes_fee, 0) + IFNULL(a.excess_fee, 0)
            + IFNULL(a.delivery_fee, 0) + IFNULL(a.outboundsorting_fee, 0) + IFNULL(a.shortbarge_fee, 0) +
            IFNULL(a.return_fee, 0) + IFNULL(a.exception_fee, 0) + IFNULL(a.other_cost1, 0) +
            IFNULL(a.other_cost2, 0) +
            IFNULL(a.other_cost3, 0) + IFNULL(a.other_cost4, 0) + IFNULL(a.other_cost5, 0) +
            IFNULL(a.other_cost6, 0) +
            IFNULL(a.other_cost7, 0) + IFNULL(a.other_cost8, 0) + IFNULL(a.other_cost9, 0) +
            IFNULL(a.other_cost10, 0) +
            IFNULL(a.other_cost11, 0) + IFNULL(a.other_cost12, 0) + IFNULL(a.adjust_fee, 0)
            )                     AS cargoValue,
            null                  AS totalAmount,
            a.bill_id             AS billMainid,
            a.settle_type                           AS settleType,
            a.settle_amount                         AS settleAmount,
            a.main_code_id                          AS mainExpenseId,
            a.create_time                           AS feeCreateAte,
            DATE_FORMAT(a.create_time,'%Y-%m-%d %H:%i:%s') AS feeCreateDate,
            a.main_code_id                          AS mainExpenseId,
            a.main_pk_id                            AS mainExpensePkId
        FROM bms_yfcost_info a
        LEFT JOIN bms_yfexpenses_middle b ON b.main_pk_id = a.main_pk_id AND b.del_flag = 0
        LEFT JOIN bms_carrierinfo c ON a.carrier_id = c.id
        LEFT JOIN bms_clientinfo d ON d.id = a.client_id
        <where>
            a.del_flag =0
            and IFNULL(a.bill_id,0)=0
            <if test="mainExpensePkId!=null ">
                a.main_pk_id = #{mainExpensePkId}
            </if>
            <if test="carrierList!=null and carrierList.size>0">
                and c.carrier_code in
                <foreach item="carrier" collection="carrierList" open="(" separator="," close=")">
                    #{carrier}
                </foreach>
            </if>
            <if test="warehouseList!=null and  warehouseList.size>0">
                and a.warehouse_code in
                <foreach item="warehouse" collection="warehouseList" open="(" separator="," close=")">
                    #{warehouse}
                </foreach>
            </if>
            <if test="companyIdList!=null">
                and a.company_id in
                <foreach collection="companyIdList" item="compayId" open="(" separator="," close=")" >
                    #{compayId}
                </foreach>
            </if>
            <if test="expensesTypeList!=null">
                and a.expenses_type in
                <foreach collection="expensesTypeList" item="value" open="(" separator="," close=")" >
                    #{value}
                </foreach>
            </if>
            <if test="carrierCode!=null and carrierCode != ''">
                and c.carrier_code = #{carrierCode}
            </if>
            <if test="clientId!=null">
                and a.client_id = #{clientId}
            </if>
            <if test="carrierCode!=null and carrierCode != ''">
                and c.carrier_code = #{carrierCode}
            </if>
            <if test="businessCode!=null and businessCode !='' ">
                and b.relate_code like  concat('%',#{businessCode},'%')
            </if>
            <if test="expensesCode!=null and expensesCode !='' ">
                and a.expenses_code like  concat('%',#{expensesCode},'%')
            </if>
            <if test="warehouseCode!=null and warehouseCode !='' ">
                and a.warehouse_code like  concat('%',#{warehouseCode},'%')
            </if>
            <if test="warehouseName!=null and warehouseName !='' ">
                and a.warehouse_name like  concat('%',#{warehouseName},'%')
            </if>
            <if test="clientCode!=null and clientCode !='' ">
                and c.carrier_code like  concat('%',#{clientCode},'%')
            </if>
            <if test="clientName!=null and clientName !='' ">
                and c.carrier_name like  concat('%',#{clientName},'%')
            </if>
            <if test="beginBusinessTime!=null  ">
                and a.business_time &gt;= #{beginBusinessTime}
            </if>
            <if test="endBusinessTime!=null ">
                and a.business_time &lt;= #{endBusinessTime}
            </if>
        </where>
        GROUP BY a.id
        ORDER BY a.business_time DESC
    </select>


    <select id="getCostListBypursea" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        select
        null as businessCode,
        a.id as id,
        a.ys_feecode as expensesCode,
        a.fee_flag as feeFlag,
        null as businessType,
        null as warehouseCode,
        null as warehouseName,
        null as clientCode,
        null as clientName,
        a.relation_code as carrierCode,
        b.carrier_name as carrierName,
        null as businessTime,
        a.total_amount as cargoValue,
        a.register_time as registerTime,
        null as expensesType,
        null as costDimension,
        null as chargeType,
        a.fee_flag as feeFlag,
        a.remark as remarks,
        /*a.adjust_remark as remarks,*/
        0 as freight,
        0 as ultrafarFee,
        0 as superframesFee,
        0 as excessFee,
        0 as deliveryFee,
        0 as outboundsortingFee,
        0 as shortbargeFee,
        0 as returnFee,
        0 as exceptionFee,
        0 as otherCost1,
        0 as otherCost2,
        0 as otherCost3,
        0 as otherCost4,
        0 as otherCost5,
        0 as otherCost6,
        0 as otherCost7,
        0 as otherCost8,
        0 as otherCost9,
        0 as otherCost10,
        0 as otherCost11,
        0 as otherCost12,
        0 as adjustFee,
        0 as totalBoxes,
        0 as totalNumber,
        0 as totalWeight,
        0 as totalVolume,
        a.sku_name as skuName,
        a.total_amount as totalAmount,
        a.num as num,
        a.price as price,
        a.unit as unit,
        a.spec as spec,
        a.register_time as registerTime,
        a.registerr_companyname as registerrCompanyname,
        a.remark as remark,
        null as billMainid,
        null as ysbilType,
        null as ysbillId
        from bms_yfpurchase_feeinfo a
        left join bms_carrierinfo b
        on a.relation_code = b.carrier_code and b.del_flag=0
        <where>
            a.del_flag=0
            and IFNULL(a.bill_id,0)=0
            <if test="carrierList!=null and  carrierList.size>0">
                and a.relation_code in
                <foreach item="carrier" collection="carrierList" open="(" separator="," close=")">
                    #{carrier}
                </foreach>
            </if>
            <if test="companyIdList!=null and companyIdList.size>0">
                and a.company_id in
                <foreach collection="companyIdList" item="compayId" open="(" separator="," close=")">
                    #{compayId}
                </foreach>
            </if>
            <if test="expensesCode!=null and expensesCode !='' ">
                and a.yf_feecode like  concat('%',#{expensesCode},'%')
            </if>
            <if test="carrierCode !=null and carrierCode !=''">
                and a.relation_code = #{carrierCode}
            </if>
            <if test="clientCode !=null and clientCode !=''">
                and a.relation_code = #{clientCode}
            </if>

        </where>
        order by a.create_time
    </select>


    <select id="getCostListBymainId" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        SELECT a.id                             AS id,
            CASE
                WHEN a.expenses_dimension = 1 THEN b.relate_code
                ELSE '-'
            END                              AS businessCode,
            CASE
                WHEN a.expenses_dimension = 1 THEN b.relate_code
                ELSE '-'
            END                              AS relateCode,
            a.business_time                  AS businessTime,
            a.expenses_code                  AS expensesCode,
            a.warehouse_code                 AS warehouseCode,
            a.warehouse_name                 AS warehouseName,
            a.client_id                      AS clientId,
            cli.client_code                  AS clientCode,
            cli.client_name                  AS clientName,
            c.carrier_code                   AS carrierCode,
            c.carrier_name                   AS carrierName,
            a.expenses_type                  AS expensesType,
            a.expenses_dimension             AS costDimension,
            a.charge_type                    AS chargeType,
            a.expenses_mark                  AS feeFlag,
            a.remarks                        AS remarks,
            IFNULL(a.freight, 0)             AS freight,
            IFNULL(a.ultrafar_fee, 0)        AS ultrafarFee,
            IFNULL(a.superframes_fee, 0)     AS superframesFee,
            IFNULL(a.excess_fee, 0)          AS excessFee,
            IFNULL(a.reduce_fee, 0)          AS reduceFee,
            IFNULL(a.delivery_fee, 0)        AS deliveryFee,
            IFNULL(a.outboundsorting_fee, 0) AS outboundsortingFee,
            IFNULL(a.shortbarge_fee, 0)      AS shortbargeFee,
            IFNULL(a.return_fee, 0)          AS returnFee,
            IFNULL(a.exception_fee, 0)       AS exceptionFee,
            IFNULL(a.other_cost1, 0)         AS otherCost1,
            IFNULL(a.other_cost2, 0)         AS other_cost2,
            IFNULL(a.other_cost3, 0)         AS otherCost3,
            IFNULL(a.other_cost4, 0)         AS otherCost4,
            IFNULL(a.other_cost5, 0)         AS otherCost5,
            IFNULL(a.other_cost6, 0)         AS otherCost6,
            IFNULL(a.other_cost7, 0)         AS otherCost7,
            IFNULL(a.other_cost8, 0)         AS otherCost8,
            IFNULL(a.other_cost9, 0)         AS otherCost9,
            IFNULL(a.other_cost10, 0)        AS otherCost10,
            IFNULL(a.other_cost11, 0)        AS otherCost11,
            IFNULL(a.other_cost12, 0)        AS otherCost12,
            IFNULL(a.adjust_fee, 0)          AS adjustFee,
            IFNULL(a.total_boxes, 0)         AS totalBoxes,
            IFNULL(a.total_number, 0)        AS totalNumber,
            IFNULL(a.total_weight, 0)        AS totalWeight,
            IFNULL(a.total_volume, 0)        AS totalVolume,
            SUM(
            IFNULL(a.freight, 0) + IFNULL(a.ultrafar_fee, 0) + IFNULL(a.superframes_fee, 0) + IFNULL(a.excess_fee, 0)
            + IFNULL(a.delivery_fee, 0) + IFNULL(a.outboundsorting_fee, 0) + IFNULL(a.shortbarge_fee, 0) +
            IFNULL(a.return_fee, 0) + IFNULL(a.exception_fee, 0) + IFNULL(a.other_cost1, 0) +
            IFNULL(a.other_cost2, 0) +
            IFNULL(a.other_cost3, 0) + IFNULL(a.other_cost4, 0) + IFNULL(a.other_cost5, 0) +
            IFNULL(a.other_cost6, 0) +
            IFNULL(a.other_cost7, 0) + IFNULL(a.other_cost8, 0) + IFNULL(a.other_cost9, 0) +
            IFNULL(a.other_cost10, 0) +
            IFNULL(a.other_cost11, 0) + IFNULL(a.other_cost12, 0) + IFNULL(a.adjust_fee, 0) + IFNULL(a.reduce_fee, 0)
            )                                AS cargoValue,
            NULL                             AS totalAmount,
            a.bill_id                        AS billMainid,
            a.bill_date                      AS billDate,
            b.code_type                      AS yfbilType,
            a.company_id                     AS companyId,
            b.code_id                        AS yfbillId,
            b.code_id                        AS codeInfoId,
            a.dispatch_date                  AS dispatchDate,
            a.finish_date                    AS finishDate,
            a.main_code_id                   AS mainExpenseId,
            a.main_pk_id                     AS mainExpensePkId
        FROM bms_yfcost_info a
        LEFT JOIN bms_yfexpenses_middle b ON a.main_pk_id = b.main_pk_id AND b.del_flag = 0
        LEFT JOIN bms_carrierinfo c ON a.carrier_id = c.id
        LEFT JOIN bms_clientinfo cli ON cli.id = a.client_id
        <where>
            <if test="ids!=null and ids.size>0">
                and a.bill_id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="businessCode!=null and businessCode !='' ">
                and b.business_code like  concat('%',#{businessCode},'%')
            </if>
            <if test="expensesCode!=null and expensesCode !='' ">
                and a.expenses_code like  concat('%',#{expensesCode},'%')
            </if>
            <if test="businessType!=null and businessType !='' ">
                and a.business_type like  concat('%',#{businessType},'%')
            </if>
            <if test="warehouseCode!=null and warehouseCode !='' ">
                and b.warehouse_code like  concat('%',#{warehouseCode},'%')
            </if>
            <if test="warehouseName!=null and warehouseName !='' ">
                and b.warehouse_name like  concat('%',#{warehouseName},'%')
            </if>
            <if test="clientCode!=null and clientCode !='' ">
                and c.carrier_code like  concat('%',#{clientCode},'%')
            </if>
            <if test="clientName!=null and clientName !='' ">
                and c.carrier_name like  concat('%',#{clientName},'%')
            </if>
            <if test="beginBusinessTime!=null  ">
                and a.business_time &gt;= #{beginBusinessTime}
            </if>
            <if test="endBusinessTime!=null ">
                and a.business_time &lt;= #{endBusinessTime}
            </if>
        </where>
        GROUP BY a.id,b.code_id,b.code_type
        ORDER BY a.business_time
    </select>

    <select id="getCostListBymainIdBypursea" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        select
        null as businessCode,
        a.id as id,
        a.yf_feecode as expensesCode,
        null as businessType,
        null as warehouseCode,
        null as warehouseName,
        a.relation_code as clientCode,
        b.carrier_name as clientName,
        a.relation_code as carrierCode,
        b.carrier_name as carrierName,
        a.fee_flag as feeFlag,
        a.register_time as registerTime,
        a.registerr_company_id as registerrCompanyId,
        a.registerr_companyname as registerrCompanyname,
        a.sku_name as skuName,
        a.num as num,
        a.price as price,
        a.unit as unit,
        a.remark as remark,
        null as businessTime,
        a.total_amount as cargoValue,
        null as expensesType,
        null as costDimension,
        null as chargeType,
        a.fee_flag as feeFlag,
        a.remark as remarks,
        a.total_amount as freight,
        null as ultrafarFee,
        null as superframesFee,
        null as excessFee,
        null as deliveryFee,
        null as outboundsortingFee,
        null as shortbargeFee,
        null as returnFee,
        null as exceptionFee,
        null as otherCost1,
        null as otherCost2,
        null as otherCost3,
        null as otherCost4,
        null as otherCost5,
        null as otherCost6,
        null as otherCost7,
        null as otherCost8,
        null as otherCost9,
        null as otherCost10,
        null as otherCost11,
        null as otherCost12,
        null as adjustFee,
        a.total_amount as totalAmount,
        null as billMainid,
        null as ysbilType,
        a.company_id as companyId,
        a.bill_date as billDate,
        a.id as codeInfoId,
        a.id as codeInfoId
        from bms_yfpurchase_feeinfo a
        left join bms_carrierinfo b
        on a.relation_code = b.carrier_code
        <where>
            <if test="ids!=null and ids.size>0">
                and IFNULL(a.bill_id,0) in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <if test="expensesCode!=null and expensesCode !='' ">
                and a.yf_feecode like  concat('%',#{expensesCode},'%')
            </if>

        </where>
        order by a.create_time

    </select>

    <select id="getBillmainStatus" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">
        <include refid="selectBmsYfbillmainVo"/>
        WHERE
        <if test="id!=null and ids==null">
            id = #{id}
        </if>
        <if test="ids!=null and ids.size>0">
            IFNULL(id,0) IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="submitStatus!=null and submitStatus!='' ">
            AND submit_status = #{submitStatus}
        </if>
        <if test="ticketState!=null and ticketState!='' ">
            AND ticket_state = #{ticketState}
        </if>
        <if test="auditState!=null and auditState!='' ">
            AND audit_state = #{auditState}
        </if>
        <if test="hxState!=null">
            AND hx_state = #{hxState}
        </if>
    </select>

    <select id="getYfBillmainOrdersDetail" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        SELECT
            b.code_id     AS yfbillId,
            c.relate_code AS workCode,
            b.code_type   AS yfbilType,
            c.code_type   AS codeType
        FROM bms_yfcost_info a
        LEFT JOIN bms_yfexpenses_middle b ON a.main_pk_id = b.main_pk_id AND b.del_flag = 0
        LEFT JOIN bms_storage_code_info c ON b.code_pk_id = c.pk_id AND c.cost_mode = 2
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        union all
        SELECT
            b.code_id         as yfbillId,
            c.scheduling_code as workCode,
            b.code_type       as yfbilType,
            '1'               as codeType
        FROM bms_yfcost_info a
        LEFT JOIN bms_yfexpenses_middle b ON a.main_pk_id = b.main_pk_id AND b.del_flag = 0
        LEFT JOIN bms_dispatch_code_info c ON b.code_pk_id = c.pk_id
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <insert id="insertBmsYfbillmain" useGeneratedKeys="true" keyProperty="id" keyColumn="id" parameterType="java.util.Map">
        insert into bms_yfbillmain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fatherid != null">fatherid,</if>
            <if test="billType != null">bill_type,</if>
            <if test="billCode != null">bill_code,</if>
            <if test="billName != null">bill_name,</if>
            <if test="billDate != null">bill_date,</if>
            <if test="companyId != null">company_id,</if>
            <if test="quoteId != null">quote_id,</if>
            <if test="carrierId != null">carrier_id,</if>
            <if test="clientId != null">client_id,</if>
            <if test="billMarking != null">bill_marking,</if>
            <if test="votes != null">votes,</if>
            <if test="billAmount != null">bill_amount,</if>
            <if test="adjustedAmount != null">adjusted_amount,</if>
            <if test="yfAmount != null">yf_amount,</if>
            <if test="billState != null">bill_state,</if>
            <if test="auditState != null">audit_state,</if>
            <if test="auditUser != null">audit_user,</if>
            <if test="auditUserName != null">audit_user_name,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="submitStatus != null">submit_status,</if>
            <if test="ticketState != null">ticket_state,</if>
            <if test="submitDate != null">submit_date,</if>
            <if test="ticketAmount != null">ticket_amount,</if>
            <if test="ticketNum != null">ticket_num,</if>
            <if test="ticketUserName != null">ticket_user_name,</if>
            <if test="ticketTime != null">ticket_time,</if>
            <if test="hxAmount != null">hx_amount,</if>
            <if test="hxState != null">hx_state,</if>
            <if test="hxUserName != null">hx_user_name,</if>
            <if test="hxTime != null">hx_time,</if>
            <if test="hxRemark != null">hx_remark,</if>
            <if test="hxNum != null">hx_num,</if>
            <if test="billRemark != null">bill_remark,</if>
            <if test="arClerk != null">ar_clerk,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="billSource != null">bill_source,</if>
            <if test="mergeStatus != null">merge_status,</if>
            <if test="responsibleMoney != null">responsible_money,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fatherid != null">#{fatherid},</if>
            <if test="billType != null">#{billType},</if>
            <if test="billCode != null">#{billCode},</if>
            <if test="billName != null">#{billName},</if>
            <if test="billDate != null">#{billDate},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="quoteId != null">#{quoteId},</if>
            <if test="carrierId != null">#{carrierId},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="billMarking != null">#{billMarking},</if>
            <if test="votes != null">#{votes},</if>
            <if test="billAmount != null">#{billAmount},</if>
            <if test="adjustedAmount != null">#{adjustedAmount},</if>
            <if test="yfAmount != null">#{yfAmount},</if>
            <if test="billState != null">#{billState},</if>
            <if test="auditState != null">#{auditState},</if>
            <if test="auditUser != null">#{auditUser},</if>
            <if test="auditUserName != null">#{auditUserName},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="submitStatus != null">#{submitStatus},</if>
            <if test="ticketState != null">#{ticketState},</if>
            <if test="submitDate != null">#{submitDate},</if>
            <if test="ticketAmount != null">#{ticketAmount},</if>
            <if test="ticketNum != null">#{ticketNum},</if>
            <if test="ticketUserName != null">#{ticketUserName},</if>
            <if test="ticketTime != null">#{ticketTime},</if>
            <if test="hxAmount != null">#{hxAmount},</if>
            <if test="hxState != null">#{hxState},</if>
            <if test="hxUserName != null">#{hxUserName},</if>
            <if test="hxTime != null">#{hxTime},</if>
            <if test="hxRemark != null">#{hxRemark},</if>
            <if test="hxNum != null">#{hxNum},</if>
            <if test="billRemark != null">#{billRemark},</if>
            <if test="arClerk != null">#{arClerk},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="billSource != null">#{billSource},</if>
            <if test="mergeStatus != null">#{mergeStatus},</if>
            <if test="responsibleMoney != null">#{responsibleMoney},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
        </trim>
    </insert>

    <insert id="insertBmsYfbillmainList" useGeneratedKeys="true" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">
        insert into bms_yfbillmain
        (
        bill_type,
        bill_code,
        bill_name,
        bill_date,
        company_id,
        carrier_id,
        client_id,
        bill_marking,
        votes,
        bill_amount,
        adjusted_amount,
        yf_amount,
        bill_state,
        audit_state,
        submit_status,
        ticket_state,
        hx_state,
        bill_remark,
        ar_clerk,
        create_time,
        oper_time,
        del_flag,
        bill_source,
        merge_status,
        create_code,
        create_by,
        create_dept_id,
        oper_dept_id,
        oper_code,
        oper_by
        )
        values
        <foreach collection="list" item="item"  separator=",">
            (
            #{item.billType},
            #{item.billCode},
            #{item.billName},
            #{item.billDate},
            #{item.companyId},
            #{item.carrierId},
            #{item.clientId},
            #{item.billMarking},
            #{item.votes},
            #{item.billAmount},
            #{item.adjustedAmount},
            #{item.yfAmount},
            #{item.billState},
            #{item.auditState},
            #{item.submitStatus},
            #{item.ticketState},
            #{item.hxState},
            #{item.billRemark},
            #{item.arClerk},
            #{item.createTime},
            #{item.operTime},
            #{item.delFlag},
            #{item.billSource},
            #{item.mergeStatus},
            #{item.createCode},
            #{item.createBy},
            #{item.createDeptId},
            #{item.operDeptId},
            #{item.operCode},
            #{item.operBy}
            )
        </foreach>

    </insert>

    <update id="updateBmsYfbillmain" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">
        update bms_yfbillmain
        <trim prefix="SET" suffixOverrides=",">
            <if test="fatherid != null">fatherid = #{fatherid},</if>
            <if test="billType != null">bill_type = #{billType},</if>
            <if test="billCode != null">bill_code = #{billCode},</if>
            <if test="billName != null">bill_name = #{billName},</if>
            <if test="billDate != null">bill_date = #{billDate},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="quoteId != null">quote_id = #{quoteId},</if>
            <if test="carrierId != null">carrier_id = #{carrierId},</if>
            <if test="billMarking != null">bill_marking = #{billMarking},</if>
            <if test="votes != null">votes = #{votes},</if>
            <if test="billAmount != null">bill_amount = #{billAmount},</if>
            <if test="adjustedAmount != null">adjusted_amount = #{adjustedAmount},</if>
            <if test="yfAmount != null">yf_amount = #{yfAmount},</if>
            <if test="billState != null">bill_state = #{billState},</if>
            <if test="auditState != null">audit_state = #{auditState},</if>
            <if test="auditUser != null">audit_user = #{auditUser},</if>
            <if test="auditUserName != null">audit_user_name = #{auditUserName},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="submitStatus != null">submit_status = #{submitStatus},</if>
            <if test="ticketState != null">ticket_state = #{ticketState},</if>
            <if test="submitDate != null">submit_date = #{submitDate},</if>
            <if test="ticketAmount != null">ticket_amount = #{ticketAmount},</if>
            <if test="ticketNum != null">ticket_num = #{ticketNum},</if>
            <if test="ticketUserName != null">ticket_user_name = #{ticketUserName},</if>
            <if test="ticketTime != null">ticket_time = #{ticketTime},</if>
            <if test="hxAmount != null">hx_amount = #{hxAmount},</if>
            <if test="hxState != null">hx_state = #{hxState},</if>
            <if test="hxUserName != null">hx_user_name = #{hxUserName},</if>
            <if test="hxTime != null">hx_time = #{hxTime},</if>
            <if test="hxRemark != null">hx_remark = #{hxRemark},</if>
            <if test="hxNum != null">hx_num = #{hxNum},</if>
            <if test="billRemark != null">bill_remark = #{billRemark},</if>
            <if test="arClerk != null">ar_clerk = #{arClerk},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="responsibleMoney != null">responsible_money = #{responsibleMoney},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYfbillmainById" parameterType="java.lang.String">
        delete from bms_yfbillmain where id = #{id}
    </delete>

    <delete id="deleteBmsYfbillmainByIds">
        delete from bms_yfbillmain where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYfbillmainStatusByIds">
        update bms_yfbillmain set del_flag = 1  where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yfcost_info set bill_id = null, show_bill_id=null, show_bill_code=null
        where bill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yfcost_info set bill_id = null, show_bill_id=null, show_bill_code=null
        where bill_id in
        (
        select id from bms_yfbillmain
        where fatherid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        );
        UPDATE bms_addedfee SET bill_id =null,bill_code=null,show_bill_code=null,show_bill_id=null
        WHERE del_flag = 0 AND settle_type = 2 AND bill_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>;
        UPDATE bms_fixedfee set bill_id =null,bill_code=null,show_bill_code=null,show_bill_id=null
        WHERE del_flag = 0 AND settle_type = 2 AND bill_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateYfmainStatus" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">
        update bms_yfbillmain set
        oper_dept_id = #{operDeptId},
        oper_code = #{operCode},
        oper_by = #{operBy},
        oper_time = #{operTime}
        <if test="auditState!=null">
            ,audit_state = #{auditState}
        </if>
        <if test="submitStatus!=null">
            ,submit_status = #{submitStatus}
        </if>
        <if test="submitDate!=null">
            ,submit_date = #{submitDate}
        </if>
        <if test="canlenCommitDate!=null">
            ,submit_date = null
        </if>
        <if test="auditUser!=null">
            ,audit_user = #{auditUser}
        </if>
        <if test="auditUserName!=null">
            ,audit_user_name = #{auditUserName}
        </if>
        <if test="auditTime!=null">
            ,audit_time = #{auditTime}
        </if>
        <if test="billState!=null">
            ,bill_state = #{billState}
        </if>
        <if test="auditUser!=null">
            ,audit_user = #{auditUser}
        </if>
        <if test="auditUserName!=null">
            ,audit_user_name = #{auditUserName}
        </if>
        <if test="auditTime!=null">
            ,audit_time = #{auditTime}
        </if>
        where
        IFNULL(del_flag,0) = 0
        and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yfbillmain set
        bill_state = (
        case when IFNULL(submit_status,1)=1 then 1
        when IFNULL(submit_status,1)=2 and IFNULL(audit_state,1)=1 then 2
        when IFNULL(submit_status,1)=2 and IFNULL(audit_state,1)=2 then 3
        else 1 end
        )
        where
        IFNULL(del_flag,0)=0
        and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <update id="updatebillMainAmtByCost" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">
        <if test="billType == 0 or billType == 2 or billType == 3">
            update bms_yfbillmain a
            inner join
            (
                select
                a.bill_id as id,
                sum(
                IFNULL(a.freight,0)+
                IFNULL(a.delivery_fee,0) +
                IFNULL(a.superframes_fee,0) +
                IFNULL(a.excess_fee,0) +
                IFNULL(a.reduce_fee,0) +
                IFNULL(a.shortbarge_fee,0) +
                IFNULL(a.return_fee,0)+
                IFNULL(a.ultrafar_fee,0) +
                IFNULL(a.outboundsorting_fee,0) +
                IFNULL(a.exception_fee,0) +
                IFNULL(a.other_cost1,0) +
                IFNULL(a.other_cost2,0) +
                IFNULL(a.other_cost3,0) +
                IFNULL(a.other_cost4,0) +
                IFNULL(a.other_cost5,0) +
                IFNULL(a.other_cost6,0) +
                IFNULL(a.other_cost7,0) +
                IFNULL(a.other_cost8,0) +
                IFNULL(a.other_cost9,0) +
                IFNULL(a.other_cost10,0) +
                IFNULL(a.other_cost11,0) +
                IFNULL(a.other_cost12,0)
                ) as sumAmt,
                SUM(IFNULL(a.exception_fee,0)) as sumExceptionFee,
                count(1) as count
                from bms_yfcost_info a
                where a.bill_id = #{id}
            ) b on a.id = b.id
            set a.adjusted_amount = b.sumAmt,
            a.yf_amount = b.sumAmt,
            a.votes = b.count,
            <if test="submitStatus!=null">
                a.submit_status = #{submitStatus},
                a.bill_state = #{submitStatus},
            </if>
            a.oper_code = #{operCode},
            a.oper_by = #{operBy},
            a.oper_dept_id = #{operDeptId},
            a.oper_time = #{operTime},
            a.responsible_money=b.sumExceptionFee
            ;
            update bms_yfbillmain a
            inner join (
            select
            bill_id as id,
            sum(IFNULL(amount,0)) as sumAmt,
            count(1) as count
            from bms_addedfee
            where del_flag = '0'
            and settle_type=2
            and bill_id = #{id}
            GROUP BY bill_id
            ) b on a.id = b.id
            set
            a.adjusted_amount = a.adjusted_amount + b.sumAmt
            ,a.yf_amount = a.yf_amount+b.sumAmt
            ,a.votes = a.votes + b.count
            where a.id = #{id}
            ;
        </if>


        <if test="billType == 1 ">
            update bms_yfbillmain a
            inner join (
            select bill_id,sum(IFNULL(total_amount,0)) as sumAmt,count(1) as count from bms_yfpurchase_feeinfo
            where bill_id = #{id}
            group by bill_id
            ) b on a.id = b.bill_id
            set a.votes = b.count ,
            a.adjusted_amount = b.sumAmt,
            a.yf_amount = b.sumAmt,
            <if test="submitStatus!=null">
                a.submit_status = #{submitStatus},
                a.bill_state = #{submitStatus},
            </if>
            a.oper_code = #{operCode},
            a.oper_by = #{operBy},
            a.oper_dept_id = #{operDeptId},
            a.oper_time = #{operTime}
            where
            a.id = #{id};
        </if>
        <!-- 固定费 -->
        update bms_yfbillmain a
        inner join(
        select
        bill_id,
        sum(ifnull(amount,0)) amount,
        count(1) count
        from pub_yf_fixedfee
        where bill_id=#{id}
        and del_flag=0
        ) b on a.id = b.bill_id
        set a.adjusted_amount = a.adjusted_amount + b.amount
        ,a.bill_amount = a.adjusted_amount + b.amount
        ,a.yf_amount = a.yf_amount+b.amount
        ,a.votes = a.votes + b.count
        where a.id =#{id}
        ;
        <!--理赔费-->
        update bms_yfbillmain a
        inner join (
        select
        bill_id as bill_id,
        sum(IFNULL(responsible_money,0)) as sumAmt,
        count(1) as count
        from bms_claims_info
        where del_flag = 0
        and bill_id =#{id}
        GROUP BY bill_id
        ) b on a.id = b.bill_id
        set
        a.bill_amount = a.adjusted_amount + b.sumAmt
        ,a.adjusted_amount = a.adjusted_amount + b.sumAmt
        ,a.yf_amount = a.yf_amount+b.sumAmt
        ,a.responsible_money=a.responsible_money+b.sumAmt
        ,a.votes = a.votes + b.count
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_time = #{operTime}
        where a.id =#{id}
        ;
    </update>

    <update id="updatebillMainAmtByCostByMerge" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmainBean">
        update bms_yfbillmain a
        inner join
        (
        select
        a.bill_id as id,
        sum(
        IFNULL(a.freight,0)+
        IFNULL(a.delivery_fee,0) +
        IFNULL(a.superframes_fee,0) +
        IFNULL(a.excess_fee,0) +
        IFNULL(a.reduce_fee,0) +
        IFNULL(a.shortbarge_fee,0) +
        IFNULL(a.return_fee,0)+
        IFNULL(a.ultrafar_fee,0) +
        IFNULL(a.outboundsorting_fee,0) +
        IFNULL(a.exception_fee,0) +
        IFNULL(a.adjust_fee,0) +
        IFNULL(a.other_cost1,0) +
        IFNULL(a.other_cost2,0) +
        IFNULL(a.other_cost3,0) +
        IFNULL(a.other_cost4,0) +
        IFNULL(a.other_cost5,0) +
        IFNULL(a.other_cost6,0) +
        IFNULL(a.other_cost7,0) +
        IFNULL(a.other_cost8,0) +
        IFNULL(a.other_cost9,0) +
        IFNULL(a.other_cost10,0) +
        IFNULL(a.other_cost11,0) +
        IFNULL(a.other_cost12,0)
        ) as sumAmt,
        sum(IFNULL(a.exception_fee,0)) exceptionFeeSum,
        count(1) as count
        from bms_yfcost_info a
        where
        a.bill_id  in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by a.bill_id
        ) b on a.id = b.id
        set a.adjusted_amount = b.sumAmt,
        a.yf_amount = b.sumAmt,
        a.bill_amount = b.sumAmt,
        a.votes=count,
        a.responsible_money=exceptionFeeSum,
        a.oper_code = #{operCode},
        a.oper_by = #{operBy},
        a.oper_dept_id = #{operDeptId},
        a.oper_time = #{operTime}
        ;

        update bms_yfbillmain a
        inner join (
        select bill_id,sum(IFNULL(total_amount,0)) as sumAmt,count(1) as count from bms_yfpurchase_feeinfo
        where bill_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by bill_id
        ) b on a.id = b.bill_id
        SET a.votes = a.votes + b.count,
        a.adjusted_amount =a.adjusted_amount+ b.sumAmt,
        a.bill_amount=a.bill_amount+b.sumAmt,
        a.yf_amount =a.yf_amount+ b.sumAmt,
        a.oper_code = #{operCode},
        a.oper_by = #{operBy},
        a.oper_dept_id = #{operDeptId},
        a.oper_time = #{operTime}
        ;
        update bms_yfbillmain a
        inner join (
        SELECT
        ad.bill_id AS id,
        sum( IFNULL(amount,0) ) AS sumAmt,
        count( 1 ) AS count,
        DATE_FORMAT(creat_date,'%Y-%m') as billDate
        FROM bms_addedfee ad
        WHERE ad.del_flag = 0
        and settle_type=2
        and bill_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY ad.bill_id,DATE_FORMAT(creat_date,'%Y-%m')
        ) b on a.id = b.id and b.billDate = a.bill_date
        set
        a.adjusted_amount = a.adjusted_amount+b.sumAmt
        ,a.bill_amount = a.adjusted_amount+b.sumAmt
        ,a.yf_amount = a.yf_amount+b.sumAmt
        ,a.votes = a.votes+b.count
        ;
        <!-- 固定费 -->
        update bms_yfbillmain a
        inner join(
        select
        bill_id,
        sum( IFNULL(amount,0)) amount,
        count(1) count
        from pub_yf_fixedfee
        where bill_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag=0
        ) b on a.id = b.bill_id
        set a.adjusted_amount = a.adjusted_amount + b.amount
        ,a.bill_amount = a.adjusted_amount + b.amount
        ,a.yf_amount = a.yf_amount+b.amount
        ,a.votes = a.votes + b.count
        ;
        <!--理赔费-->
        update bms_yfbillmain a
        inner join (
        select
        bill_id as bill_id,
        sum(IFNULL(responsible_money,0)) as sumAmt,
        count(1) as count
        from bms_claims_info
        where del_flag = 0
        and bill_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY bill_id
        ) b on a.id = b.bill_id
        set
        a.bill_amount = a.adjusted_amount + b.sumAmt
        ,a.adjusted_amount = a.adjusted_amount + b.sumAmt
        ,a.yf_amount = a.yf_amount+b.sumAmt
        ,a.responsible_money=a.responsible_money+b.sumAmt
        ,a.votes = a.votes + b.count
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_time = #{operTime}
        where a.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yfbillmain a inner join (
        select
        fatherid as id ,
        sum(adjusted_amount) as sumAmt,
        sum(votes) as votes,
        sum(responsible_money) as  responsible_money
        from bms_yfbillmain
        where id in
        group by fatherid
        ) b on a.id = b.id
        set a.votes = b.votes,
        a.bill_amount=b.sumAmt
        ,a.adjusted_amount = b.sumAmt
        ,a.yf_amount = b.sumAmt
        ,a.responsible_money = b.responsible_money
        <if test="commitType!=null">
            ,a.submit_status = #{commitType}
            ,a.bill_state = #{commitType}
        </if>
        <if test="commitType== 2">
            ,a.submit_date = now()
        </if>
        where a.id = #{billId}
        ;
    </update>



    <update id="updatePurchaseInfo" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        update bms_yfpurchase_feeinfo set
        <if test="billId!=null">
            bill_id  = #{billId},
        </if>
        <if test="creatFlag!=null">
            creat_flag = #{creatFlag},
        </if>
        <!--<if test="deliveryFee!=null">
            delivery_fee = #{deliveryFee},
        </if>-->
        <if test="totalAmount!=null">
            total_amount = #{totalAmount},
        </if>
        <if test="adjustRemark!=null">
            adjust_remark = #{adjustRemark},
        </if>
        oper_code = #{operCode},
        oper_by = #{operBy},
        oper_time = #{operTime}
        where id = #{id}

    </update>

    <update id="reconciliation" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        update bms_yfcost_info set
        <if test="billId!=null">
            bill_id  = #{billId},
        </if>
        <if test="freight!=null">
            freight = #{freight},
        </if>
        <if test="deliveryFee!=null">
            delivery_fee = #{deliveryFee},
        </if>
        <if test="superframesFee!=null">
            superframes_fee = #{superframesFee},
        </if>
        <if test="excessFee!=null">
            excess_fee = #{excessFee},
        </if>
        <if test="shortbargeFee!=null">
            shortbarge_fee = #{shortbargeFee},
        </if>
        <if test="returnFee!=null">
            return_fee = #{returnFee},
        </if>
        <if test="ultrafarFee!=null">
            ultrafar_fee = #{ultrafarFee},
        </if>
        <if test="outboundsortingFee!=null">
            outboundsorting_fee = #{outboundsortingFee},
        </if>
        <if test="exceptionFee!=null">
            exception_fee = #{exceptionFee},
        </if>
        <if test="otherCost1!=null">
            other_cost1 = #{otherCost1},
        </if>
        <if test="otherCost2!=null">
            other_cost2 = #{otherCost2},
        </if>
        <if test="otherCost3!=null">
            other_cost3 = #{otherCost3},
        </if>
        <if test="otherCost4!=null">
            other_cost4 = #{otherCost4},
        </if>
        <if test="otherCost5!=null">
            other_cost5 = #{otherCost5},
        </if>
        <if test="otherCost6!=null">
            other_cost6 = #{otherCost6},
        </if>
        <if test="otherCost7!=null">
            other_cost7 = #{otherCost7},
        </if>
        <if test="otherCost8!=null">
            other_cost8 = #{otherCost8},
        </if>
        <if test="otherCost9!=null">
            other_cost9 = #{otherCost9},
        </if>
        <if test="otherCost10!=null">
            other_cost10 = #{otherCost10},
        </if>
        <if test="otherCost11!=null">
            other_cost11 = #{otherCost11},
        </if>
        <if test="otherCost12!=null">
            other_cost12 = #{otherCost12},
        </if>
        oper_code = #{operCode},
        oper_by = #{operBy},
        oper_time = #{operTime}
        where id = #{id}

    </update>


    <update id="updateBillmainAmt">
        <if test="type==1 or type==2">
            update bms_yfbillmain a
            inner join
            (
            select bill_id,
            sum(
            IFNULL(freight,0) +
            IFNULL(delivery_fee,0) +
            IFNULL(superframes_fee,0) +
            IFNULL(excess_fee,0) +
            IFNULL(reduce_fee,0) +
            IFNULL(shortbarge_fee,0) +
            IFNULL(return_fee,0) +
            IFNULL(ultrafar_fee,0) +
            IFNULL(outboundsorting_fee,0) +
            IFNULL(exception_fee,0) +
            IFNULL(other_cost1,0) +
            IFNULL(other_cost2,0) +
            IFNULL(other_cost3,0) +
            IFNULL(other_cost4,0) +
            IFNULL(other_cost5,0) +
            IFNULL(other_cost6,0) +
            IFNULL(other_cost7,0) +
            IFNULL(other_cost8,0) +
            IFNULL(other_cost9,0) +
            IFNULL(other_cost10,0) +
            IFNULL(other_cost11,0) +
            IFNULL(other_cost12,0) +
            IFNULL(adjust_fee, 0 )
            ) as sumAmt,
            sum(IFNULL(exception_fee,0)) exceptionFeeSum,
            count(1) as count
            from bms_yfcost_info
            where bill_id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
            group by bill_id
            ) b on a.id = b.bill_id
            set
            <if test="commitType !=null">
                a.bill_state = #{commitType},
                a.submit_status =#{commitType},
            </if>
            <if test="commitType== 2">
                a.submit_date = now(),
            </if>
            a.votes = ifnull(a.votes,0)+b.count,
            a.bill_amount = b.sumAmt+ifnull(a.responsible_money,0),
            a.adjusted_amount = b.sumAmt+ifnull(a.responsible_money,0),
            a.yf_amount = b.sumAmt+ifnull(a.responsible_money,0),
            a.responsible_money =ifnull(a.responsible_money,0)+exceptionFeeSum
            ;

            <!-- 增值费 -->
            update bms_yfbillmain a
            inner join (
            select
            fee.bill_id as id,
            sum(fee.amount) as sumAmt,
            count(1) as count
            from bms_addedfee fee
            where fee.del_flag =0
            and fee.bill_id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
            group by fee.bill_id
            ) b on a.id = b.id
            set
            a.adjusted_amount = a.adjusted_amount+b.sumAmt
            ,a.bill_amount = a.adjusted_amount+b.sumAmt
            ,a.yf_amount = a.yf_amount+b.sumAmt
            ,a.votes = a.votes+b.count
            ;
        </if>
        <if test="type==3">
            update bms_yfbillmain a
            inner join (
            select bill_id,sum(IFNULL(total_amount,0)) as sumAmt,count(1) as count from bms_yfpurchase_feeinfo
            where bill_id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
            group by bill_id
            ) b on a.id = b.bill_id
            set
            <if test="commitType != null">
                a.bill_state = #{commitType},
                a.submit_status = #{commitType},
            </if>
            <if test="commitType== 2">
                a.submit_date = now(),
            </if>
            a.votes = b.count ,
            a.bill_amount = b.sumAmt,
            a.adjusted_amount = b.sumAmt,
            a.yf_amount = b.sumAmt
            ;
        </if>

        <!--固定费-->
        update bms_yfbillmain a inner join (
        select bill_id ,sum(IFNULL(amount,0)) as sumAmt,count(1) as count from pub_yf_fixedfee
        where bill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by bill_id
        )b on a.id = b.bill_id
        set
        a.adjusted_amount = a.adjusted_amount + b.sumAmt,
        a.bill_amount = a.bill_amount + b.sumAmt,
        a.yf_amount = a.yf_amount + b.sumAmt
        ;
    </update>

    <update id="updateBmsYfcodeInfomainListGroupCarrier">
        update bms_yfcost_info
        set
        bill_id = #{billId}
        where
        carrier_code = #{carrierCode}
        and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="clientId!=null and clientId!=''">
            and client_id = #{clientId}
        </if>
        and bill_date = #{billDate}
        and IFNULL(fee_flag,1)=1
        and company_id = #{companyId}
        ;
        insert into billmain_cost(
        bill_id,
        cost_id,
        create_time
        )
        values
        <foreach collection="ids" item="id"  separator=",">
            (
            #{billId},
            #{id},
            now()
            )
        </foreach>
    </update>

    <update id="updateBmsYfcodeInfomainListGroupCarrierByFeeFlg">
        update bms_yfcost_info
        set
            bill_id = #{billId}
        where
            carrier_code = #{carrierCode}
          and id =#{id}
          and IFNULL(fee_flag,1)=2
    </update>

    <update id="updateBmsYfbillPureaseListGroupCarrier">
        update bms_yfpurchase_feeinfo
        set bill_id = #{billId},
        creat_flag = 1
        where
        relation_code = #{carrierCode}
        and IFNULL(fee_flag,1) = 1
        and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and bill_date = #{billDate}

        ;
        insert into billmain_cost(
        bill_id,
        cost_id,
        create_time
        )
        values
        <foreach collection="ids" item="id"  separator=",">
            (
            #{billId},
            #{id},
            now()
            )
        </foreach>
    </update>

    <update id="updateBmsYfbillPureaseListGroupCarrierByFeeFlag">
        update bms_yfpurchase_feeinfo
        set bill_id = #{billId},
            creat_flag = 1
        where
            relation_code = #{carrierCode}
          and IFNULL(fee_flag,1)=2
          and id =#{id}
    </update>

    <update id="updateBmsYfbillStockListGroupCarrier" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">

        update bms_yfcost_info
        set bill_id = #{billId}
        where
        carrier_code = #{carrierCode}
        and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="clientId!=null">
            and client_id=#{clientId}
        </if>
        and bill_date =#{billDate}
        and IFNULL(fee_flag,1)=1
        and company_id = #{companyId}

        ;
        insert into billmain_cost(
        bill_id,
        cost_id,
        create_time
        )
        values
        <foreach collection="ids" item="id"  separator=",">
            (
            #{billId},
            #{id},
            now()
            )
        </foreach>

    </update>

    <update id="updateBmsYfbillStockListGroupCarrierByFeeFlg" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">

        update bms_yfcost_info
        set bill_id = #{billId}
        where
            carrier_code = #{carrierCode}
          and id =#{id}
          and IFNULL(fee_flag,1)=2

    </update>

    <select id="selectBmsYfcodeInfomainListGroupCarrier" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.BmsYfbillcodeinfo">
        SELECT
        a.carrier_code AS carrierCode,
        a.bill_date as billDate,
        a.company_id as companyId,
        a.carrier_name as carrierName,
        a.client_id as clientId,
        c.client_name as clientName
        FROM bms_yfcost_info a
        INNER JOIN bms_yfexpenses_middle b ON a.main_expense_id = b.main_expense_id AND b.del_flag = '0'
        LEFT JOIN bms_clientinfo c ON a.client_id = c.id
        WHERE a.del_flag = '0' AND a.id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND a.expenses_type IN
        <foreach item="type" collection="ysbillType" open="(" separator="," close=")">
            #{type}
        </foreach>
        AND IFNULL(a.fee_flag,1)=1
        GROUP BY a.carrier_code,a.bill_date,a.client_id,a.company_id
    </select>

    <select id="selectBmsYfcodeInfomainListGroupCarrierByFeeFlag" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.BmsYfbillcodeinfo">
        SELECT
        a.carrier_code AS carrierCode,
        a.bill_date as billDate,
        a.company_id as companyId,
        a.client_id as clientId,
        c.client_name as clientName
        FROM bms_yfcost_info a
        inner join bms_yfexpenses_middle b
        on a.id = b.expenses_id
        left join bms_clientinfo c
        on a.client_id = c.id
        WHERE
        a.id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and a.expenses_type in
        <foreach item="type" collection="ysbillType" open="(" separator="," close=")">
            #{type}
        </foreach>
        AND IFNULL(a.fee_flag,1)=2
    </select>

    <select id="selectBmsYfbillStockListGroupCarrier" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.BmsYfstockinfo">
        select a.carrier_code as carrierCode from (
        select
        carrier_code
        from bms_yfstock_codeinfo
        where
        id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>

        union all

        select
        carrier_code
        from bms_yfstockinfo
        where
        id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ) a
        group by a.carrier_code
    </select>

    <select id="selectBmsYfbillPureaseListGroupCarrier" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.pay.BmsYfpurchaseFeeinfo">
        select
        a.relation_code as relationCode,
        b.id as relationId,
        a.company_id as companyId,
        b.carrier_name as relationName,
        a.bill_date as billDate
        from bms_yfpurchase_feeinfo a
        inner join bms_carrierinfo b on a.relation_code = b.carrier_code
        where
        IFNULL(a.fee_flag,1)=1
        and CONCAT(a.id,'') in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by a.relation_code,a.bill_date
    </select>

    <select id="selectBmsYfbillPureaseListGroupCarrierByFeeFlag" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.pay.BmsYfpurchaseFeeinfo">
        select
        relation_code as relationCode,
        company_id as companyId,
        id as id
        from bms_yfpurchase_feeinfo
        where
        IFNULL(fee_flag,1)=2
        and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="selectCoustAndMainListByIds" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        select client_id as clientId,id,expenses_code AS expensesCode from bms_yfcost_info
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(bill_id,0)!=0
        union all
        select '-1' as clientId,id ,yf_feecode as expensesCode from bms_yfpurchase_feeinfo where CONCAT(id,'') in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(bill_id,0)!=0
    </select>

    <select id="approveOrNot" parameterType="java.util.List" resultMap="BmsYfbillmainResult">
        <include refid="selectBmsYfbillmainVo"/>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(del_flag,'0') = '0'
        and bill_state &gt;= 1
    </select>

    <select id="selectBmsYfbillmainListSonByIds" parameterType="java.util.List" resultMap="BmsYfbillmainResult">
        <include refid="selectBmsYfbillmainVo"/>
        WHERE fatherid IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY fatherid
    </select>

    <select id="selectBmsYfbillmainListByIds" parameterType="java.util.List" resultMap="BmsYfbillmainResult">
        select fatherid from bms_yfbillmain where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and fatherid!=null
        group by fatherid
    </select>

    <select id="getCarrierGroup" parameterType="java.util.List" resultMap="BmsYfbillmainResult">
        select carrier_id from bms_yfbillmain
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(del_flag,'0') = '0'
        group by carrier_id
    </select>

    <select id="selectMainListByIds" parameterType="java.util.List" resultMap="BmsYfbillmainResult">
        <include refid="selectBmsYfbillmainVo"/>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(del_flag,'0') = '0'
    </select>

    <update id="updateCancelBillMerge">
        update bms_yfbillmain a
        inner join (
            select id
            from bms_yfbillmain
            where fatherid in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        ) b on a.id = b.id
        set fatherid = null
        ;
        delete from bms_yfbillmain where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getClientInfoByCode" parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.MdmClientinfo">
        select
            *
        from bms_clientinfo
        where
            client_code = #{code}
    </select>

    <select id="getCarrierinfoByCode" parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.MdmCarrierinfo">
        select
            *
        from bms_carrierinfo
        where
            carrier_code = #{code}
    </select>

    <select id="getBillCodeById" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">
        select
            a.bill_code billCode
             ,a.responsible_money responsibleMoney
             ,a.bill_type billType
        from bms_yfbillmain a
        where a.del_flag=0
          and (a.id = #{id} or a.fatherid=#{id})
    </select>

    <update id="batchUpdateBmsYfbillClaims">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";" >
            update bms_yfbillmain
            <set>
                <if test="item.submitStatus != null and item.submitStatus!=''">submit_status = #{item.submitStatus},</if>
                <if test="item.operCode != null">oper_code = #{item.operCode},</if>
                <if test="item.operBy != null">oper_by = #{item.operBy},</if>
                <if test="item.operDeptId != null">oper_dept_id = #{item.operDeptId},</if>
                <if test="item.votes != null">votes=votes+#{item.votes},</if>
                oper_time=now(),bill_amount=bill_amount+#{item.responsibleMoney},yf_amount=yf_amount+#{item.responsibleMoney},adjusted_amount=adjusted_amount+#{item.responsibleMoney},responsible_money=responsible_money+#{item.responsibleMoney}
            </set>
            where bill_code = #{item.billCode}
        </foreach>
    </update>
    <update id="updatebillMainAmont">
        update bms_yfbillmain
        set oper_time      = now(),
            bill_amount    = #{amont},
            yf_amount= #{amont},
            adjusted_amount=#{amont}
        where id = #{id}
    </update>

    <select id="selectBmsYfbillmainExceptionFeeById" parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">
        select a.id,
        a.fatherid,
        a.bill_type                AS   billType,
        IFNULL(a.merge_status, 0)  AS   mergeStatus,
        a.bill_code                AS   billCode,
        IFNULL(a.bill_source, 1)   AS   billSource,
        a.bill_name                AS   billName,
        a.bill_date                AS   billDate,
        a.company_id               AS   companyId,
        a.quote_id                 AS   quoteI,
        a.carrier_id               AS   carrierId,
        a.bill_marking             AS   billMarking,
        a.votes,
        a.bill_amount              AS   billAmount,
        a.adjusted_amount          AS   adjustedAmount,
        a.yf_amount                AS   yfAmount,
        a.bill_state               AS   billState,
        a.audit_state              AS   auditState,
        a.audit_user               AS   auditUser,
        a.audit_user_name          AS   auditUserName,
        a.audit_time               AS   auditTime,
        a.audit_remark             AS   auditRemark,
        a.submit_status            AS   submitStatus,
        a.ticket_state             AS   ticketState,
        a.ticket_user_name         AS   ticketUserName,
        a.ticket_time              AS   ticketTime,
        a.ticket_num               AS   ticketNum,
        a.submit_date              AS   submitDate,
        IFNULL(a.ticket_amount, 0) AS   ticketAmount,
        a.ticket_num               AS   ticketNum,
        a.ticket_user_name         AS   ticketUserName,
        a.ticket_time              AS   ticketTime,
        a.hx_amount                AS   hxAmount,
        a.hx_state                 AS   hxState,
        a.hx_user_name             AS   hxUserName,
        a.hx_time                  AS   hxTime,
        a.hx_remark                AS   hxRemark,
        a.hx_num                   AS   hxNum,
        a.bill_remark              AS   billRemark,
        a.ar_clerk                 AS   arClerk,
        a.create_code              AS   createCode,
        a.create_by                AS   createBy,
        a.create_time              AS   createTime,
        a.create_dept_id           AS   createDeptId,
        a.oper_dept_id             AS   operDeptId,
        a.oper_code                AS   operCode,
        a.oper_by                  AS   operBy,
        a.oper_time                AS   operTime,
        a.del_flag                 AS   delFlag,
        b.carrier_code             AS   carrierCode,
        b.carrier_name             AS   carrierName,
        d.id                       AS   clientId,
        d.client_code              AS   clientCode,
        d.client_name              AS   clientName
        FROM bms_yfbillmain a
        LEFT JOIN bms_carrierinfo b ON a.carrier_id = b.id
        LEFT JOIN bms_clientinfo d ON d.id = a.client_id
        WHERE (a.id = #{id} OR fatherid = #{id})
    </select>
    <select id="selectBmsYfbillmainByIds" resultMap="BmsYfbillmainResult">
        select * from bms_yfbillmain where del_flag = 0 and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectBmsPubAddedfeefeeRuleByClinet" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto">
        SELECT
        fee.id
        , fee.expenses_code     AS expensesCode
        , IFNULL(fee.amount, 0) AS amount
        , fee.remark            AS remarks
        FROM bms_addedfee fee
        LEFT JOIN bms_clientinfo clientinfo ON fee.client_code = clientinfo.client_code
        LEFT JOIN bms_carrierinfo carrierinfo ON carrierinfo.carrier_code = fee.carrier_code
        WHERE fee.del_flag = 0
        AND clientinfo.del_flag = 0
        AND carrierinfo.del_flag = 0
        AND carrierinfo.id=#{carrierId}
        AND fee.cost_status = 1
        AND fee.settle_type = 2
        AND clientinfo.id = #{clientId}
        AND IFNULL(bill_id,'')=''
        AND dept_id = #{companyId}
        AND DATE_FORMAT(creat_date,'%Y-%m') = #{billDate}
        <!--运输增值 -->
        <if test="type !=null and type ==2">
            AND fee_belong=1
        </if>
        <!--仓储增值 -->
        <if test="type !=null and type ==3">
            AND fee_belong=2
        </if>
    </select>

    <select id="selectBmsPubAddedFeeRuleByClient" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto">
        SELECT fee.id
        , fee.expenses_code                AS expensesCode
        , IFNULL(fee.amount, 0)            AS amount
        , fee.remark                       AS remarks
        , fee_belong                       AS feeBelong
        , clientinfo.id                    AS clientId
        , carrierinfo.id                   AS carrierId
        , dept_id                          AS companyId
        , DATE_FORMAT(creat_date, '%Y-%m') AS billDate
        FROM bms_addedfee fee
        LEFT JOIN bms_clientinfo clientinfo ON fee.client_code = clientinfo.client_code
        LEFT JOIN bms_carrierinfo carrierinfo ON carrierinfo.carrier_code = fee.carrier_code
        WHERE fee.del_flag = 0
            AND clientinfo.del_flag = 0
            AND carrierinfo.del_flag = 0
            AND fee.cost_status = 1
            AND fee.settle_type = 2
            AND carrierinfo.id IN
            <foreach collection="carrierIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND clientinfo.id IN
            <foreach collection="clientIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND dept_id IN
            <foreach collection="companyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND DATE_FORMAT(creat_date,'%Y-%m') IN
            <foreach collection="billDates" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND IFNULL(bill_id,'')=''
    </select>

    <select id="selectBmsPubAddedfeefeeRule" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto">
            SELECT
                fee.id,
                fee.expenses_code                                   AS expensesCode,
                IFNULL(fee.amount, 0)                               AS amount,
                fee.remark                                          AS remark,
                fee.expenses_code                                   AS expensesCode,
                fee.relate_code                                     AS relateCode,
                DATE_FORMAT(fee.charging_date, '%Y-%m-%d %H:%i:%s') AS chargingDate,
                fee.item_olevel_id                                  AS itemOlevelId,
                fee.item_id                                         AS itemId,
                fee.amount                                          AS amount,
                IFNULL(fee.number, 0)                               AS 'number',
                fee.unit                                            AS unit,
                fee.fee_source                                      AS feeSource,
                fee.order_source                                    AS orderSource,
                DATE_FORMAT(fee.creat_date, '%Y-%m-%d')             AS creatDate,
                fee.warehouse_code                                  AS warehouseCode,
                mw.warehouse_name                                   AS warehouseName,
                clientinfo.client_name                              AS clientName,
                IFNULL(fee.amount, 0)                               AS amount,
                fee.remark                                          AS remarks,
                fee.adjust_before_remark                            AS adjustBeforeRemark,
                fee.creat_date                                      AS createDate
            FROM bms_addedfee fee
            LEFT JOIN bms_clientinfo clientinfo ON fee.client_code = clientinfo.client_code
            LEFT JOIN bms_carrierinfo carrierinfo ON carrierinfo.carrier_code = fee.carrier_code
            LEFT JOIN mdm_warehouseinfo mw ON mw.warehouse_code = fee.warehouse_code
            WHERE fee.del_flag = 0
                AND clientinfo.del_flag = 0
                AND carrierinfo.del_flag = 0
                AND carrierinfo.id=#{carrierId}
                AND fee.cost_status = 1
                AND fee.settle_type = 2
                AND clientinfo.id = #{clientId}
                AND IFNULL(bill_id,'')=''
                AND dept_id = #{companyId}
    </select>
    <select id="selectBmsPubAddedfeefeeRule2" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto">
        SELECT fee.id                                              AS id,
            fee.expenses_code                                   AS expensesCode,
            IFNULL(fee.amount, 0)                               AS amount,
            fee.remark                                          AS remark,
            fee.expenses_code                                   AS expensesCode,
            fee.relate_code                                     AS relateCode,
            DATE_FORMAT(fee.charging_date, '%Y-%m-%d %H:%i:%s') AS chargingDate,
            fee.item_olevel_id                                  AS itemOlevelId,
            fee.item_id                                         AS itemId,
            fee.amount                                          AS amount,
            IFNULL(fee.number, 0)                               AS 'number',
            fee.unit                                            AS unit,
            fee.fee_source                                      AS feeSource,
            fee.order_source                                    AS orderSource,
            DATE_FORMAT(fee.creat_date, '%Y-%m-%d')             AS creatDate,
            fee.warehouse_code                                  AS warehouseCode,
            mw.warehouse_name                                   AS warehouseName,
            clientinfo.client_name                              AS clientName,
            IFNULL(fee.amount, 0)                               AS amount,
            fee.remark                                          AS remarks,
            fee.adjust_before_remark                            AS adjustBeforeRemark,
            fee.creat_date                                      AS createDate,
            fee.fee_belong                                      AS feeBelong,
            case
            when fee.fee_belong = 1 then '运输增值'
            when fee.fee_belong = 2 then '仓储增值'
            else ''
            end                                             AS feeBelongDesc
        FROM bms_addedfee fee
        LEFT JOIN bms_clientinfo clientinfo on fee.client_code = clientinfo.client_code
        LEFT JOIN bms_carrierinfo carrierinfo on carrierinfo.carrier_code = fee.carrier_code
        LEFT JOIN mdm_warehouseinfo mw on mw.warehouse_code = fee.warehouse_code
        WHERE fee.del_flag = 0
        AND clientinfo.del_flag = 0
        <if test="carrierId !=null">
            and carrierinfo.id=#{carrierId}
        </if>
        and fee.cost_status = 1
        <if test="settleType !=null ">
            and fee.settle_type=#{settleType}
        </if>
        <if test="clientId !=null ">
            and clientinfo.id=#{clientId}
        </if>
        and bill_id is null
        <if test="companyId !=null ">
            and dept_id = #{companyId}
        </if>
        <if test="warehouseCode !=null and warehouseCode !='' ">
            and fee.warehouse_code = #{warehouseCode}
        </if>
    </select>

    <select id="queryAddFeeByBillId" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto">
        SELECT
            ad.id,
            ad.expenses_code                                   AS expensesCode,
            ad.relate_code                                     AS relateCode,
            DATE_FORMAT(ad.charging_date, '%Y-%m-%d %H:%i:%s') AS chargingDate,
            ad.item_olevel_id                                  AS itemOlevelId,
            ad.item_id                                         AS itemId,
            ad.amount                                          AS amount,
            IFNULL(ad.number, 0)                               AS number,
            ad.unit,
            ad.fee_source                                      AS feeSource,
            ad.order_source                                    AS orderSource,
            DATE_FORMAT(ad.creat_date, '%Y-%m-%d')             AS creatDate,
            ad.warehouse_code                                  AS warehouseCode,
            mw.warehouse_name                                  AS warehouseName,
            cli.client_name                                    AS clientName,
            IFNULL(ad.amount, 0)                               AS amount,
            ad.remark                                          AS remark,
            ad.adjust_before_remark                            AS adjustBeforeRemark,
            ad.creat_date                                      AS createDate,
            ad.bill_id                                         AS billId,
            ad.fee_belong                                      AS feeBelong,
            case
                when ad.fee_belong = 1 then '运输增值'
                when ad.fee_belong = 2 then '仓储增值'
            else ''
            end                                                AS feeBelongDesc
        FROM bms_addedfee ad
        LEFT JOIN bms_yfbillmain bill ON bill.id = ad.bill_id
        LEFT JOIN bms_clientinfo cli ON cli.client_code = ad.client_code AND cli.del_flag = 0
        LEFT JOIN mdm_warehouseinfo mw ON mw.warehouse_code = ad.warehouse_code
        WHERE ad.del_flag = 0
            AND ad.settle_type = 2
            AND (bill.id = #{id} or bill.fatherid = #{id} )
        ORDER BY ad.item_id DESC,ad.creat_date DESC
    </select>
    <select id="queryAddFeeByBillIds" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto">
        SELECT ad.id,
            ad.expenses_code                                   AS expensesCode,
            ad.relate_code                                     AS relateCode,
            DATE_FORMAT(ad.charging_date, '%Y-%m-%d %H:%i:%s') AS chargingDate,
            ad.item_olevel_id                                  AS itemOlevelId,
            ad.item_id                                         AS itemId,
            ad.amount                                          AS amount,
            IFNULL(ad.number, 0)                               AS 'number',
            ad.unit                                            AS unit,
            ad.fee_source                                      AS feeSource,
            ad.order_source                                    AS orderSource,
            DATE_FORMAT(ad.creat_date, '%Y-%m-%d')             AS creatDate,
            ad.warehouse_code                                  AS warehouseCode,
            mw.warehouse_name                                  AS warehouseName,
            cli.client_name                                    AS clientName,
            IFNULL(ad.amount, 0)                               AS amount,
            ad.remark                                          AS remarks,
            ad.adjust_before_remark                            AS adjustBeforeRemark,
            ad.creat_date                                      AS createDate,
            ad.fee_belong                                      AS feeBelong,
            case
            when ad.fee_belong = 1 then '运输增值'
            when ad.fee_belong = 2 then '仓储增值'
            else ''
            end                                            AS feeBelongDesc
        FROM bms_addedfee ad
        LEFT JOIN bms_yfbillmain bill ON bill.id = ad.bill_id
        LEFT JOIN bms_clientinfo cli ON cli.client_code = ad.client_code AND cli.del_flag = 0
        LEFT JOIN mdm_warehouseinfo mw ON mw.warehouse_code = ad.warehouse_code
        WHERE bill.id in
        <foreach item="item" collection="billIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and ad.del_flag =0
        and ad.settle_type=2
        ORDER BY ad.item_id DESC,ad.creat_date DESC
    </select>

    <update id="updatebillMainForAddAmtByCostByMerge">
        update bms_yfbillmain a
        inner join (
        SELECT
        ad.bill_id AS id,
        sum( IFNULL(amount,0) ) AS sumAmt,
        count( 1 ) AS count
        FROM bms_addedfee ad
        WHERE ad.del_flag = 0 and ad.settle_type = 2
        and bill_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY ad.bill_id
        ) b on a.id = b.id
        set
        a.adjusted_amount = b.sumAmt
#         ,a.bill_amount = b.sumAmt
        ,a.yf_amount = b.sumAmt
        ,a.votes = b.count
        ;
    </update>

    <update id="updatebillMainForAddAmtByCost" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">
        update bms_yfbillmain a
            inner join (
            select
            bill_id as id,
            sum(IFNULL(amount,0)) as sumAmt,
            count(1) as count
            from bms_addedfee
            where del_flag = '0'
            and bill_id = #{id}
            GROUP BY bill_id
            ) b on a.id = b.id
            set
                a.adjusted_amount = b.sumAmt
                    ,a.yf_amount = b.sumAmt
                    ,a.votes = b.count
        where a.id = #{id}
    </update>

    <update id="updatebillMainAmtNoFlushFatherByCostByMerge" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmainBean">
        update bms_yfbillmain a
        inner join
        (
        select
        a.bill_id as id,
        sum(
        IFNULL(a.freight,0)+
        IFNULL(a.delivery_fee,0) +
        IFNULL(a.superframes_fee,0) +
        IFNULL(a.excess_fee,0) +
        IFNULL(a.reduce_fee,0) +
        IFNULL(a.shortbarge_fee,0) +
        IFNULL(a.return_fee,0)+
        IFNULL(a.ultrafar_fee,0) +
        IFNULL(a.outboundsorting_fee,0) +
        IFNULL(a.exception_fee,0) +
        IFNULL(a.other_cost1,0) +
        IFNULL(a.other_cost2,0) +
        IFNULL(a.other_cost3,0) +
        IFNULL(a.other_cost4,0) +
        IFNULL(a.other_cost5,0) +
        IFNULL(a.other_cost6,0) +
        IFNULL(a.other_cost7,0) +
        IFNULL(a.other_cost8,0) +
        IFNULL(a.other_cost9,0) +
        IFNULL(a.other_cost10,0) +
        IFNULL(a.other_cost11,0) +
        IFNULL(a.other_cost12,0)
        ) as sumAmt,
        sum(IFNULL(a.exception_fee,0)) exceptionFeeSum,
        count(1) as count
        from bms_yfcost_info a
        where
        a.bill_id  in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by a.bill_id
        ) b on a.id = b.id
        set a.adjusted_amount = b.sumAmt,
        a.yf_amount = b.sumAmt,
        a.votes=count,
        a.responsible_money=exceptionFeeSum,
        a.oper_code = #{operCode},
        a.oper_by = #{operBy},
        a.oper_dept_id = #{operDeptId},
        a.oper_time = #{operTime}
        ;

        update bms_yfbillmain a
        inner join (
        select bill_id,sum(IFNULL(total_amount,0)) as sumAmt,count(1) as count from bms_yfpurchase_feeinfo
        where bill_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by bill_id
        ) b on a.id = b.bill_id
        SET a.votes = a.votes + b.count,
        a.adjusted_amount =a.adjusted_amount+ b.sumAmt,
        a.yf_amount =a.yf_amount+ b.sumAmt,
        a.oper_code = #{operCode},
        a.oper_by = #{operBy},
        a.oper_dept_id = #{operDeptId},
        a.oper_time = #{operTime}
        ;
        update bms_yfbillmain a
        inner join (
        SELECT
        ad.bill_id AS id,
        sum( IFNULL(amount,0) ) AS sumAmt,
        count( 1 ) AS count,
        DATE_FORMAT(creat_date,'%Y-%m') as billDate
        FROM bms_addedfee ad
        WHERE ad.del_flag = 0
        and settle_type=2
        and bill_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY ad.bill_id,DATE_FORMAT(creat_date,'%Y-%m')
        ) b on a.id = b.id and b.billDate = a.bill_date
        set
        a.adjusted_amount = a.adjusted_amount+b.sumAmt
        ,a.yf_amount = a.yf_amount+b.sumAmt
        ,a.votes = a.votes+b.count
        ;
        <!-- 固定费 -->
        update bms_yfbillmain a
        inner join(
        select
        bill_id,
        sum( IFNULL(amount,0)) amount,
        count(1) count
        from pub_yf_fixedfee
        where bill_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag=0
        ) b on a.id = b.bill_id
        set a.adjusted_amount = a.adjusted_amount + b.amount
        ,a.yf_amount = a.yf_amount+b.amount
        ,a.votes = a.votes + b.count
        ;
        <!--理赔费-->
        update bms_yfbillmain a
        inner join (
        select
        bill_id as bill_id,
        sum(IFNULL(responsible_money,0)) as sumAmt,
        count(1) as count
        from bms_claims_info
        where del_flag = 0
        and bill_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY bill_id
        ) b on a.id = b.bill_id
        set
         a.adjusted_amount = a.adjusted_amount + b.sumAmt
        ,a.yf_amount = a.yf_amount+b.sumAmt
        ,a.responsible_money=a.responsible_money+b.sumAmt
        ,a.votes = a.votes + b.count
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_time = #{operTime}
        where a.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
    </update>


    <select id="selSumAllChildBillInfo" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsSumDto" >
        select
            sum(yf_amount) as yfAmount,
            sum( adjusted_amount ) AS sumAmt,
            sum( votes ) AS sumCount,
            IFNULL(sum(responsible_money),0) as responsible_money
        from bms_yfbillmain
        WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="flushFatherAmount" parameterType="java.util.Map">
        update bms_yfbillmain
        set
        votes = #{bmsSumDto.sumCount}
        ,adjusted_amount = #{bmsSumDto.sumAmt}
        ,yf_amount = #{bmsSumDto.sumAmt}
        ,responsible_money = #{bmsSumDto.responsibleMoney}
        <if test="commitType!=null">
            ,submit_status = #{commitType}
            ,bill_state = #{commitType}
        </if>
        <if test="commitType== 2">
            ,submit_date = now()
        </if>
        where id = #{billId}
    </update>

    <select id="selSumAllChildForAddBillInfo" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsSumDto">
        select
            fatherid as id ,
            sum(adjusted_amount) as sumAmt,
            sum(votes) as votes,
            IFNULL(sum( responsible_money ),0) AS sumExceptionFee
        from bms_yfbillmain
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="flushFatherForAddAmount" parameterType="java.util.Map">
        update bms_yfbillmain
        set
        votes = #{bmsSumDto.sumCount}
        ,bill_amount=#{bmsSumDto.sumAmt}
        ,adjusted_amount = #{bmsSumDto.sumAmt}
        ,yf_amount = #{bmsSumDto.sumAmt}
        <if test="commitType!=null">
            ,a.submit_status = #{commitType}
            ,a.bill_state = #{commitType}
        </if>
        <if test="commitType== 2">
            ,a.submit_date = now()
        </if>
        where a.id = #{billId}
    </update>
</mapper>
