<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfFloatFeeMapper">

    <sql id="mainRuleInfo" >
        id,rule_name,rule_code,rule_type,ratio,oper_by,oper_code,oper_time,created_by,created_time,updated_by,updated_time,enabled
    </sql>


    <insert id="insertInfo"  useGeneratedKeys="true" keyProperty="id">
        insert into pub_yf_floatFee_rule_main (<include refid="mainRuleInfo"></include>)
        values
        (null,#{ruleName},#{ruleCode},#{ruleType},#{ratio},#{operBy},#{operCode},#{operTime},#{createdBy},#{createdTime},#{updatedBy},#{updatedTime},#{enabled})
    </insert>
    
    <insert id="batchInsertDetailInfo">
      insert into  pub_yf_floatfee_rule(rule_main_id,festival_name, start_date, end_date, rule_type ,start_hour, end_hour, oper_by, oper_code, oper_time, created_by ,
        created_time, updated_by, updated_time ,remark)
      values
        <foreach collection="details" item="detail" index="index" separator=",">
            ( #{detail.ruleMainId},#{detail.festivalName}, #{detail.startDate}, #{detail.endDate}, #{detail.ruleType}, #{detail.startHour},
            #{detail.endHour}, #{detail.operBy}, #{detail.operCode}, #{detail.operTime}, #{detail.createdBy}, #{detail.createdTime},
             #{detail.updatedBy}, #{detail.updatedTime}, #{detail.remark})
        </foreach>
    </insert>
    
    <update id="deletedByMainIdBatch">
        update  pub_yf_floatfee_rule set del_flag=1 where rule_main_id in
        <foreach collection="mainIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="modifyMainRule" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYfFloatFeeRuleDto" >
        UPDATE pub_yf_floatFee_rule_main SET
        <if test="ruleName != null and ruleName!=''">rule_name = #{ruleName},</if>
        <if test="ratio != null">ratio = #{ratio},</if>
        <if test="ruleType != null">rule_type = #{ruleType},</if>
        <if test="operBy != null and operBy!=''">oper_by = #{operBy},</if>
        <if test="operCode != null and operCode!=''">oper_code = #{operCode},</if>
        <if test="operTime != null ">oper_time = #{operTime},</if>
        <if test="updatedBy != null and updatedBy!=''">updated_by = #{updatedBy},</if>
        <if test="updatedTime != null">updated_time = #{updatedTime},</if>
        <if test="delFlag != null">del_flag = #{delFlag},</if>
        <if test="enabled != null">enabled = #{enabled},</if>
        <if test="id!=null ">  id=#{id}</if>
        WHERE id = #{id}
    </update>

    <select id="queryList"  parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfFloatFeeRuleDto">
    select pyfFrm.id,pyfFrm.rule_name as ruleName,pyfFrm.rule_code as ruleCode ,
        pyfFrm.ratio ,pyfFrm.enabled,pyfFrm.created_by as createdBy,pyfFrm.created_time as createdTime,pyfFrm.updated_by as updatedBy,
        pyfFrm.updated_time as updatedTime,pyfFrm.oper_by as operBy,pyfFrm.oper_time as operTime
        from pub_yf_floatFee_rule_main pyfFrm  left join pub_yf_floatfee_rule_carrier pyfrc
        on pyfFrm.id = pyfrc.rule_id and pyfrc.del_flag=0
      where pyfFrm.del_flag=0
      <if test="carrierId !=null">
        and  pyfrc.carrier_id=#{carrierId}
      </if>
      <if test="ruleName !=null and ruleName !=''">
        and pyfFrm.rule_name like concat('%', #{ruleName}, '%')
     </if>
     <if test="warehouseCode !=null and warehouseCode !=''">
        and  pyfrc.warehouse_code=#{warehouseCode}
     </if>
     <if test="enabled !=null ">
        and pyfFrm.enabled=#{enabled}
     </if>
     <if test="ruleCode !=null and ruleCode !=''">
        and  pyfFrm.rule_code=#{ruleCode}
     </if>
     group by  pyfFrm.id
     order by  pyfFrm.created_time desc
    </select>

    <select id="queryDetailList" resultMap="PubYfFloatFeeRule">
        select  pyfr.id,pyfr.rule_main_id,pyfr.festival_name, pyfr.start_date, pyfr.end_date, pyfrm.rule_type ,pyfr.start_hour, pyfr.end_hour,
                pyfrm.enabled, pyfr.oper_by, pyfr.oper_code, pyfr.oper_time, pyfr.created_by ,
                pyfr.created_time,pyfr. updated_by, pyfr.updated_time ,pyfr.remark  from pub_yf_floatfee_rule_main pyfrm left join  pub_yf_floatfee_rule pyfr on
            pyfrm.id=pyfr.rule_main_id
            where pyfr.del_flag=0 and pyfrm.del_flag=0;
    </select>

    <select id="queryMainRuleById" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfFloatFeeRuleDto">
        select pyfFrm.id,pyfFrm.rule_name as ruleName,pyfFrm.rule_code as ruleCode ,pyfFrm.rule_type as ruleType,
               pyfFrm.ratio ,pyfFrm.enabled,pyfFrm.created_by as createdBy,pyfFrm.created_time as createdTime,pyfFrm.updated_by as updateBy,
               pyfFrm.updated_time as updatedTime,pyfFrm.oper_by as operBy,pyfFrm.oper_time as operTime
        from pub_yf_floatFee_rule_main pyfFrm where id =#{id}
    </select>

    <select id="queryDetailRuleById" resultMap="detailRule">
        select <include refid="detailRuleIfo"></include>
        from pub_yf_floatfee_rule  where rule_main_id = #{ruleMainId} and del_flag=0
    </select>

    <update id="deletedProjectByMainIdBatch">
        update  pub_yf_floatfee_rule_feetype set del_flag=1 where rule_id in
        <foreach collection="mainIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="deletedCarrierByMainIdBatch">
        update  pub_yf_floatfee_rule_carrier set del_flag=1 where rule_id in
        <foreach collection="mainIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <insert id="bindingFeeTypeProject" parameterType="com.bbyb.joy.bms.domain.dto.PubYfFloatFeeRuleFeeType">
        insert into pub_yf_floatfee_rule_feetype ( rule_id, subject_id, created_by, created_name, created_time, updated_by, updated_name,
                                                updated_time, remark)values
        <foreach collection="details" item="detail" index="index" separator=",">
        ( #{detail.ruleId},#{detail.subjectId}, #{detail.createdBy}, #{detail.createdName}, #{detail.createdTime}, #{detail.updatedBy},
        #{detail.updatedName}, #{detail.updatedTime}, #{detail.remark})
        </foreach>
    </insert>


    <insert id="bindingCarriers" parameterType="com.bbyb.joy.bms.domain.dto.PubYfFloatFeeRuleFeeType">
        insert into pub_yf_floatfee_rule_carrier( rule_id, carrier_id, warehouse_id, warehouse_code,
             created_by, created_name, created_time, updated_by, updated_name, updated_time, remark)
             values
        <foreach collection="details" item="detail" index="index" separator=",">
            ( #{detail.ruleId},#{detail.carrierId},  #{detail.warehouseId},#{detail.warehouseCode},
            #{detail.createdBy}, #{detail.createdName}, #{detail.createdTime}, #{detail.updatedBy},#{detail.updatedName}, #{detail.updatedTime},
            #{detail.remark})
        </foreach>
    </insert>

    <select id="queryFeeTypesByRuleId" resultMap="PubYfFloatFeeRuleFeeType">
        select  pyfrf.id, pyfrf.rule_id, pyfrf.subject_id, pyfrf.created_by, pyfrf.created_name, pyfrf.created_time, pyfrf.updated_by, pyfrf.updated_name, pyfrf.updated_time, pyfrf.remark,
                pyfrf.del_flag,pfs.item_code,pfs.item_name
        from pub_yf_floatfee_rule_feetype pyfrf
         left join pub_fee_subject  pfs  on pfs.id=pyfrf.subject_id and pfs.del_flag=0
        where rule_id=#{ruleId}
          and pyfrf.del_flag=0
    </select>

    <select id="queryCarriersByRuleId" resultMap="PubYfFloatFeeRuleCarrier">
        select  pyfrc.id, pyfrc.rule_id, pyfrc.carrier_id, mc.carrier_code, mc.carrier_name,mw.id as warehouse_id, GROUP_CONCAT(DISTINCT mw.warehouse_name) as warehouse_name,
                GROUP_CONCAT(DISTINCT  mw.warehouse_code) as warehouse_code, pyfrc.created_by, pyfrc.created_name, pyfrc.created_time,
                pyfrc.updated_by, pyfrc.updated_name, pyfrc.updated_time, pyfrc.remark, pyfrc.del_flag from
            pub_yf_floatfee_rule_carrier pyfrc
        left join bms_carrierinfo mc on mc.id=pyfrc.carrier_id and mc.del_flag=0
        left join mdm_warehouseinfo mw on mw.warehouse_code=pyfrc.warehouse_code and mw.del_flag=0
        where rule_id=#{ruleId}
        and pyfrc.del_flag=0
        group by pyfrc.carrier_id
    </select>

    <update id="deletedFloatFeeRule" >
        update  pub_yf_floatfee_rule_main set del_flag=1 where id in
        <foreach collection="mainIds" item="item" open="(" separator="," close=")">
        #{item}
       </foreach>;
        update  pub_yf_floatfee_rule_carrier set del_flag=1 where rule_id in
        <foreach collection="mainIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
        update pub_yf_floatfee_rule_feetype set del_flag=1 where rule_id in
        <foreach collection="mainIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
        update  pub_yf_floatfee_rule set del_flag=1 where rule_main_id in
        <foreach collection="mainIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
    </update>

    <sql id="detailRuleIfo">
        id,rule_main_id,festival_name, start_date, end_date, rule_type ,start_hour, end_hour, enabled, oper_by, oper_code, oper_time, created_by ,
        created_time, updated_by, updated_time ,remark
    </sql>

    <resultMap id="detailRule" type="com.bbyb.joy.bms.domain.dto.dto.BmsYfFloatFeeRuleDetailDto">
        <result column="id" property="id"/>
        <result column="rule_main_id" property="ruleMainId"/>
        <result column="festival_name" property="festivalName"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="rule_type" property="ruleType"/>
        <result column="start_hour" property="startHour"/>
        <result column="end_hour" property="endHour"/>
        <result column="enabled" property="enabled"/>
        <result column="oper_by" property="operBy"/>
        <result column="oper_code" property="operCode"/>
        <result column="oper_time" property="operTime"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!--应付浮动计费规则关联费用类型表 通用查询映射结果 -->
    <resultMap id="PubYfFloatFeeRuleFeeType" type="com.bbyb.joy.bms.domain.dto.PubYfFloatFeeRuleFeeType">
        <id column="id" property="id" />
        <result column="rule_id" property="ruleId" />
        <result column="subject_id" property="subjectId" />
        <result column="item_code" property="subjectCode" />
        <result column="item_name" property="subjectName" />
        <result column="created_by" property="createdBy" />
        <result column="created_name" property="createdName" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_name" property="updatedName" />
        <result column="updated_time" property="updatedTime" />
        <result column="remark" property="remark" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 应付浮动计费规则关联费用类型表 通用查询结果列 -->
    <sql id="pubYfFloatFeeRuleFeeType">
        id, rule_id, subject_id, created_by, created_name, created_time, updated_by, updated_name, updated_time, remark, del_flag
    </sql>


    <!-- 应付浮动计费规则关联客户表通用查询映射结果 -->
    <resultMap id="PubYfFloatFeeRuleCarrier" type="com.bbyb.joy.bms.domain.dto.PubYfFloatFeeRuleCarrier">
        <id column="id" property="id" />
        <result column="rule_id" property="ruleId" />
        <result column="carrier_id" property="carrierId" />
        <result column="carrier_code" property="carrierCode" />
        <result column="carrier_name" property="carrierName" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="warehouse_name" property="warehouseName" />
        <result column="warehouse_code" property="warehouseCode" />
        <result column="created_by" property="createdBy" />
        <result column="created_name" property="createdName" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_name" property="updatedName" />
        <result column="updated_time" property="updatedTime" />
        <result column="remark" property="remark" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 应付浮动计费规则关联客户表通用查询结果列 -->
    <sql id="PubYfFloatFeeRuleCarrier">
        id, rule_id, carrier_id, carrier_code, carrier_name, warehouse_id, warehouse_name, warehouse_code, created_by, created_name, created_time, updated_by, updated_name, updated_time, remark, del_flag
    </sql>


    <!-- 应付浮动计费规则表通用查询映射结果 -->
    <resultMap id="PubYfFloatFeeRule" type="com.bbyb.joy.bms.domain.dto.PubYfFloatFeeRule">
        <id column="id" property="id" />
        <result column="rule_main_id" property="ruleMainId" />
        <result column="festival_name" property="festivalName" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="rule_type" property="ruleType" />
        <result column="start_hour" property="startHour" />
        <result column="end_hour" property="endHour" />
        <result column="enabled" property="enabled" />
        <result column="oper_by" property="operBy" />
        <result column="oper_code" property="operCode" />
        <result column="oper_time" property="operTime" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="remark" property="remark" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

</mapper>