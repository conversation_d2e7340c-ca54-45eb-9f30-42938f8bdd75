<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsAutoMaticMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.dto.BMSAutomaticLogImplementDto" id="BMSAutomaticLogImplementDtoResult">
        <result property="id"    column="id"    />
        <result property="gid"    column="gid"    />
        <result property="starttime"    column="starttime"    />
        <result property="endtime"    column="endtime"    />
        <result property="module"    column="module"    />
        <result property="autossate"    column="autossate"    />
        <result property="operator"    column="operator"    />
        <result property="resultrek"    column="resultrek"    />
    </resultMap>

    <insert id="insertBMSAutomaticLogImplement" parameterType="com.bbyb.joy.bms.domain.dto.dto.BMSAutomaticLogImplementDto" useGeneratedKeys="true" keyProperty="id">
        insert into pub_automatic_log_implement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="gid != null">gid,</if>
            <if test="starttime != null">starttime,</if>
            <if test="endtime != null">endtime,</if>
            <if test="module != null">`module`,</if>
            <if test="autossate != null">autossate,</if>
            <if test="operator != null">operator,</if>
            <if test="resultrek != null">resultrek,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="gid != null">#{gid},</if>
            <if test="starttime != null">#{starttime},</if>
            <if test="module != null">#{module},</if>
            <if test="autossate != null">#{autossate},</if>
            <if test="operator != null">#{operator},</if>
            <if test="resultrek != null">#{resultrek},</if>
         </trim>
    </insert>

    <update id="updateBMSAutomaticLogImplement" parameterType="com.bbyb.joy.bms.domain.dto.dto.BMSAutomaticLogImplementDto">
        update pub_automatic_log_implement
        <trim prefix="SET" suffixOverrides=",">
            <if test="endtime != null">endtime = #{endtime},</if>
            <if test="autossate != null">autossate = #{autossate},</if>
            <if test="resultrek != null">resultrek = #{resultrek},</if>
        </trim>
        where gid = #{gid}
    </update>

    <select id="selectBMSAutomaticLogImplement" parameterType="java.util.Map" resultMap="BMSAutomaticLogImplementDtoResult">
        select  id,gid,starttime,endtime,operator,autossate from pub_automatic_log_implement
        where `module` = #{module}
        order by id desc limit 1
    </select>

</mapper>