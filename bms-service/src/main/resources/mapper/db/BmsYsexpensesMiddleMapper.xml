<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYsexpensesMiddleMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle" id="BmsYsexpensesMiddleResult">
        <result property="id"    column="id"    />
        <result property="ysbillId"    column="ysbill_id"    />
        <result property="ysbilType"    column="ysbil_type"    />
        <result property="expensesId"    column="expenses_id"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="mainExpenseId" column="main_expense_id"/>
    </resultMap>

    <sql id="selectBmsYsexpensesMiddleVo">
        select id, code_id AS ysbill_id, code_type AS ysbil_type, main_code_id, del_flag,main_code_id AS main_expense_id from bms_ysexpenses_middle
    </sql>

    <select id="selectBmsYsexpensesMiddleList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle" resultMap="BmsYsexpensesMiddleResult">
        <include refid="selectBmsYsexpensesMiddleVo"/>
        <where>
            <if test="ysbillId != null  and ysbillId != ''"> and ysbill_id = #{ysbillId}</if>
            <if test="ysbilType != null "> and ysbil_type = #{ysbilType}</if>
            <if test="expensesId != null  and expensesId != ''"> and expenses_id = #{expensesId}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
        </where>
    </select>

    <select id="selectBmsYscostMiddleListByExpensesIds"  resultMap="BmsYsexpensesMiddleResult">
        <include refid="selectBmsYsexpensesMiddleVo"/>
        where del_flag = 0 and main_code_id in
        <foreach item="id" collection="expensesIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectBmsYscostMiddleListByIds"  resultMap="BmsYsexpensesMiddleResult">
        <include refid="selectBmsYsexpensesMiddleVo"/>
        where del_flag = 0
        and ysbill_id in (
            SELECT id from bms_ysbillcodeinfo where 1=1
            and
            <if test="ids!=null and ids.size()>0">
                 code_type =1
                and id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="codes!=null and codes.size()>0">
                code_type in (2,3)
                and relate_code in
                <foreach item="id" collection="codes" open="(" separator="," close=")">
                    #{id}
                </foreach>

                UNION
                SELECT id from bms_ysstockinfo where 1=1
                and stock_code in
                <foreach item="id" collection="codes" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        )

    </select>


    <select id="selectBmsYscostIds"  resultMap="BmsYsexpensesMiddleResult">
        select t1.id, t1.ysbill_id, t1.ysbil_type, t1.expenses_id, t1.oper_by, t1.oper_time, t1.del_flag,t1.main_expense_id
        from bms_ysexpenses_middle t1
        left join bms_ysbillcodeinfo t2 on t1.ysbill_id = t2.id and t2.del_flag=0
        where
        t1.del_flag=0
        and t2.code_type =1
        and t2.relate_code in
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    <select id="selectBmsYsexpensesMiddleById" parameterType="java.lang.String" resultMap="BmsYsexpensesMiddleResult">
        <include refid="selectBmsYsexpensesMiddleVo"/>
        where id = #{id}
    </select>

    <insert id="insertBmsYsexpensesMiddle" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle" useGeneratedKeys="true" keyProperty="id">
        insert into bms_ysexpenses_middle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ysbillId != null">ysbill_id,</if>
            <if test="ysbilType != null">ysbil_type,</if>
            <if test="expensesId != null">expenses_id,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="mainExpenseId != null">main_expense_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ysbillId != null">#{ysbillId},</if>
            <if test="ysbilType != null">#{ysbilType},</if>
            <if test="expensesId != null">#{expensesId},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="mainExpenseId != null">#{mainExpenseId},</if>
        </trim>
    </insert>

    <update id="updateBmsYsexpensesMiddle" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle">
        update bms_ysexpenses_middle
        <trim prefix="SET" suffixOverrides=",">
            <if test="ysbillId != null">ysbill_id = #{ysbillId},</if>
            <if test="ysbilType != null">ysbil_type = #{ysbilType},</if>
            <if test="expensesId != null">expenses_id = #{expensesId},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYsexpensesMiddleById" parameterType="java.lang.String">
        delete from bms_ysexpenses_middle where id = #{id}
    </delete>

    <delete id="deleteBmsYsexpensesMiddleByIds">
        delete from bms_ysexpenses_middle where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYsexpensesMiddleStatusByIds">
        update bms_ysexpenses_middle set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectByOrderId" resultMap="BmsYsexpensesMiddleResult">
        <include refid="selectBmsYsexpensesMiddleVo"/>
        where
        <if test="ids != null and ids.size>0 ">
            ysbill_id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
    </select>


    <select id="selectByCostInfos" resultType="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle">
        <if test="ysbillType==1">
            SELECT
                t1.id,
                t1.ysbill_id AS ysbillId,
                t1.ysbil_type AS ysbilType,
                t1.expenses_id AS expensesId,
                t1.main_expense_id AS mainExpenseId,
                t2.bill_id AS billId,
                t2.expenses_code AS expensesCode,
                t3.expenses_code AS mainExpenseCode
            FROM bms_ysexpenses_middle t1
            LEFT JOIN bms_yscost_info t2 ON t2.id = t1.expenses_id AND t2.del_flag='0'
            LEFT JOIN bms_yscost_main_info t3 ON t3.id = t1.main_expense_id AND t3.del_flag='0'
            LEFT JOIN bms_ysbillcodeinfo t4 ON t4.id = t1.ysbill_id AND t4.del_flag='0'
            WHERE t1.del_flag='0'
            AND t1.ysbil_type = 1
            AND t1.ysbill_id IN
            <foreach item="ysbillId" collection="ysbillIds" open="(" separator="," close=")">
                #{ysbillId}
            </foreach>
        </if>
        <if test="ysbillType==2">
            SELECT
                t1.id,
                t1.ysbill_id AS ysbillId,
                t1.ysbil_type AS ysbilType,
                t1.expenses_id AS expensesId,
                t1.main_expense_id AS mainExpenseId,
                t2.bill_id AS billId,
                t2.expenses_code AS expensesCode,
                t3.expenses_code AS mainExpenseCode
            FROM bms_ysexpenses_middle t1
            LEFT JOIN bms_yscost_info t2 ON t2.id = t1.expenses_id AND t2.del_flag='0'
            LEFT JOIN bms_yscost_main_info t3 ON t3.id = t1.main_expense_id AND t3.del_flag='0'
            LEFT JOIN bms_ysstockinfo t4 ON t4.id = t1.ysbill_id AND t4.del_flag='0'
            WHERE t1.del_flag='0'
            AND t1.ysbil_type = 2
            AND t1.ysbill_id IN
            <foreach item="ysbillId" collection="ysbillIds" open="(" separator="," close=")">
                #{ysbillId}
            </foreach>
        </if>

    </select>

    <select id="queryMiddleInfoByExpenseId" resultType="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle">
        select bym.id,
               bym.share_type                       as shareType,
               bym.share_amount                     as shareAmount,
               bym.freight                          as freight,
               bym.delivery_fee                     as deliveryFee,
               bym.ultrafar_fee                     as ultrafarFee,
               bym.superframes_fee                  as superframesFee,
               bym.excess_fee                       as excessFee,
               bym.reduce_fee                       as reduceFee,
               bym.outboundsorting_fee              as outboundsortingFee,
               bym.shortbarge_fee                   as shortbargeFee,
               bym.return_fee                       as returnFee,
               bym.exception_fee                    as exceptionFee,
               bym.other_cost1                      as otherCost1,
               bym.other_cost2                      as otherCost2,
               bym.other_cost3                      as otherCost3,
               bym.other_cost4                      as otherCost4,
               bym.other_cost5                      as otherCost5,
               bym.other_cost6                      as otherCost6,
               bym.other_cost7                      as otherCost7,
               bym.other_cost8                      as otherCost8,
               bym.other_cost9                      as otherCost9,
               bym.other_cost10                     as otherCost10,
               bym.other_cost11                     as otherCost11,
               bym.other_cost12                     as otherCost12,
               ifnull(byc.total_volume, byt.volume) as totalVolume,
               ifnull(byc.total_weight, byt.weight) as totalWeight
        from bms_ysexpenses_middle bym
                 left join bms_ysbillcodeinfo byc on bym.ysbill_id = byc.id and bym.del_flag = 0 and bym.ysbil_type = 1
                 left join bms_ysstockinfo byt on bym.ysbill_id = byt.id and byt.del_flag = 0 and bym.ysbil_type = 2
        where expenses_id = #{expenseId}
          and bym.del_flag = 0
    </select>
</mapper>