<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfstockCodeinfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYfstockCodeinfo" id="BmsYfstockCodeinfoResult">
        <result property="id"    column="id"    />
        <result property="pkId"    column="pk_id"    />
        <result property="codeType"    column="code_type"    />
        <result property="relateCode"    column="relate_code"    />
        <result property="companyId"    column="company_id"    />
        <result property="networkCode"    column="network_code"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="stockoperTime"    column="stockoper_time"    />
        <result property="trust"    column="trust"    />
        <result property="weight"    column="weight"    />
        <result property="volume"    column="volume"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="warehouseArea"    column="warehouse_area"    />
        <result property="cargoValue"    column="cargo_value"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="costStatus"    column="cost_status"    />
        <result property="billingStatus"    column="billing_status"    />
        <result property="orderType"    column="order_type"    />
        <result property="ifAutarky"    column="if_autarky"    />
        <result property="storageServiceProvider"    column="storage_service_provider"    />
        <result property="clientId"    column="client_id"    />
        <result property="splitTotalNumber"    column="split_total_number"    />
        <result property="skuNumber"    column="sku_number"    />
        <result property="transportTypeApi"    column="transport_type"    />
        <result property="cwFullCases"    column="cw_full_cases"    />
        <result property="cwSplitCases"    column="cw_split_cases"    />
        <result property="ldFullCases"    column="ld_full_cases"    />
        <result property="ldSplitCases"    column="ld_split_cases"    />
        <result property="orderDate"    column="order_date"    />
        <result property="storeCode"    column="store_code"    />
        <result property="receivingStore"    column="receiving_store"    />
        <result property="cwPalletNumber"    column="cw_pallet_number"    />
        <result property="lcPalletNumber"    column="lc_pallet_number"    />
        <result property="ldPalletNumber"    column="ld_pallet_number"    />
        <result property="orderNo"    column="order_no"    />
        <result property="platformCode"    column="platform_code"    />
        <result property="destinationProvince"    column="destination_province"    />
        <result property="destinationCity"    column="destination_city"    />
        <result property="destinationArea"    column="destination_area"    />
    </resultMap>

    <sql id="selectBmsYfstockCodeinfoVo">
        select id, pk_id, code_type, relate_code, company_id, network_code, carrier_code, carrier_name, warehouse_code, warehouse_name, stockoper_time, trust, weight, volume, total_boxes, warehouse_area, cargo_value, oper_dept_id, oper_code, oper_by, oper_time, del_flag, cost_status, billing_status,order_type,if_autarky,storage_service_provider,client_id,split_total_number,sku_number,transport_type from bms_yfstock_codeinfo
    </sql>

    <select id="selectBmsYfstockCodeinfoList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfstockCodeinfo" resultMap="BmsYfstockCodeinfoResult">
        <include refid="selectBmsYfstockCodeinfoVo"/>
        <where>
            <if test="codeType != null "> and code_type = #{codeType}</if>
            <if test="relateCode != null  and relateCode != ''"> and relate_code = #{relateCode}</if>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="networkCode != null  and networkCode != ''"> and network_code = #{networkCode}</if>
            <if test="carrierCode != null  and carrierCode != ''"> and carrier_code = #{carrierCode}</if>
            <if test="carrierName != null  and carrierName != ''"> and carrier_name like concat('%', #{carrierName}, '%')</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="stockoperTime != null "> and stockoper_time = #{stockoperTime}</if>
            <if test="trust != null "> and trust = #{trust}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="volume != null "> and volume = #{volume}</if>
            <if test="totalBoxes != null "> and total_boxes = #{totalBoxes}</if>
            <if test="warehouseArea != null "> and warehouse_area = #{warehouseArea}</if>
            <if test="cargoValue != null "> and cargo_value = #{cargoValue}</if>
            <if test="operDeptId != null "> and oper_dept_id = #{operDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
            <if test="costStatus != null  and costStatus != ''"> and cost_status = #{costStatus}</if>
            <if test="billingStatus != null  and billingStatus != ''"> and billing_status = #{billingStatus}</if>
        </where>
    </select>

    <select id="selectBmsYfstockCodeinfoById" parameterType="java.lang.String" resultMap="BmsYfstockCodeinfoResult">
        <include refid="selectBmsYfstockCodeinfoVo"/>
        where pk_id = #{pkId}
    </select>

    <insert id="insertBmsYfstockCodeinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfstockCodeinfo" useGeneratedKeys="true" keyProperty="pkId">
        insert into bms_yfstock_codeinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="codeType != null">code_type,</if>
            <if test="relateCode != null">relate_code,</if>
            <if test="companyId != null">company_id,</if>
            <if test="networkCode != null">network_code,</if>
            <if test="carrierCode != null">carrier_code,</if>
            <if test="carrierName != null">carrier_name,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="stockoperTime != null">stockoper_time,</if>
            <if test="trust != null">trust,</if>
            <if test="weight != null">weight,</if>
            <if test="volume != null">volume,</if>
            <if test="totalBoxes != null">total_boxes,</if>
            <if test="warehouseArea != null">warehouse_area,</if>
            <if test="cargoValue != null">cargo_value,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="costStatus != null">cost_status,</if>
            <if test="billingStatus != null">billing_status,</if>
            <if test="orderType != null">order_type,</if>
            <if test="ifAutarky != null">if_autarky,</if>
            <if test="storageServiceProvider != null">storage_service_provider,</if>
            <if test="clientId != null">client_id,</if>
            <if test="splitTotalNumber != null">split_total_number,</if>
            <if test="skuNumber != null">sku_number,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="codeType != null">#{codeType},</if>
            <if test="relateCode != null">#{relateCode},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="networkCode != null">#{networkCode},</if>
            <if test="carrierCode != null">#{carrierCode},</if>
            <if test="carrierName != null">#{carrierName},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="stockoperTime != null">#{stockoperTime},</if>
            <if test="trust != null">#{trust},</if>
            <if test="weight != null">#{weight},</if>
            <if test="volume != null">#{volume},</if>
            <if test="totalBoxes != null">#{totalBoxes},</if>
            <if test="warehouseArea != null">#{warehouseArea},</if>
            <if test="cargoValue != null">#{cargoValue},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="costStatus != null">#{costStatus},</if>
            <if test="billingStatus != null">#{billingStatus},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="ifAutarky != null">#{ifAutarky},</if>
            <if test="storageServiceProvider != null">#{storageServiceProvider},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="splitTotalNumber != null">#{splitTotalNumber},</if>
            <if test="skuNumber != null">#{skuNumber},</if>
        </trim>
    </insert>

    <update id="updateBmsYfstockCodeinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfstockCodeinfo">
        update bms_yfstock_codeinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null and id != ''">id = #{id},</if>
            <if test="codeType != null">code_type = #{codeType},</if>
            <if test="relateCode != null">relate_code = #{relateCode},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="networkCode != null">network_code = #{networkCode},</if>
            <if test="carrierCode != null">carrier_code = #{carrierCode},</if>
            <if test="carrierName != null">carrier_name = #{carrierName},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="stockoperTime != null">stockoper_time = #{stockoperTime},</if>
            <if test="trust != null">trust = #{trust},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="volume != null">volume = #{volume},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="warehouseArea != null">warehouse_area = #{warehouseArea},</if>
            <if test="cargoValue != null">cargo_value = #{cargoValue},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="costStatus != null">cost_status = #{costStatus},</if>
            <if test="billingStatus != null">billing_status = #{billingStatus},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="ifAutarky != null">if_autarky = #{ifAutarky},</if>
            <if test="storageServiceProvider != null">storage_service_provider = #{storageServiceProvider},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="splitTotalNumber != null">split_total_number = #{splitTotalNumber},</if>
            <if test="skuNumber != null">sku_number = #{skuNumber},</if>
        </trim>
        where pk_id = #{pkId}
    </update>

    <update id="updateBmsYfstockCodeinfoById" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfstockCodeinfo">
        update bms_yfstock_codeinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="codeType != null">code_type = #{codeType},</if>
            <if test="relateCode != null">relate_code = #{relateCode},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="networkCode != null">network_code = #{networkCode},</if>
            <if test="carrierCode != null">carrier_code = #{carrierCode},</if>
            <if test="carrierName != null">carrier_name = #{carrierName},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="stockoperTime != null">stockoper_time = #{stockoperTime},</if>
            <if test="trust != null">trust = #{trust},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="volume != null">volume = #{volume},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="warehouseArea != null">warehouse_area = #{warehouseArea},</if>
            <if test="cargoValue != null">cargo_value = #{cargoValue},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="costStatus != null">cost_status = #{costStatus},</if>
            <if test="billingStatus != null">billing_status = #{billingStatus},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="ifAutarky != null">if_autarky = #{ifAutarky},</if>
            <if test="storageServiceProvider != null">storage_service_provider = #{storageServiceProvider},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="splitTotalNumber != null">split_total_number = #{splitTotalNumber},</if>
            <if test="skuNumber != null">sku_number = #{skuNumber},</if>
            <if test="palletNumber != null">pallet_number = #{palletNumber},</if>
            <if test="transportTypeApi != null">transport_type = #{transportTypeApi},</if>
            <if test="cwFullCases != null">cw_full_cases = #{cwFullCases},</if>
            <if test="cwSplitCases != null">cw_split_cases = #{cwSplitCases},</if>
            <if test="ldFullCases != null">ld_full_cases = #{ldFullCases},</if>
            <if test="ldSplitCases != null">ld_split_cases = #{ldSplitCases},</if>
            <if test="storeCode != null">store_code = #{storeCode},</if>
            <if test="receivingStore != null">receiving_store = #{receivingStore},</if>
            <if test="cwPalletNumber != null">cw_pallet_number = #{cwPalletNumber},</if>
            <if test="lcPalletNumber != null">lc_pallet_number = #{lcPalletNumber},</if>
            <if test="ldPalletNumber != null">ld_pallet_number = #{ldPalletNumber},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="platformCode != null">platform_code = #{platformCode},</if>
            <if test="destinationProvince != null">destination_province = #{destinationProvince},</if>
            <if test="destinationCity != null">destination_city = #{destinationCity},</if>
            <if test="destinationArea != null">destination_area = #{destinationArea},</if>
            <if test="isTimeout != null">is_timeout = #{isTimeout},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYfstockCodeinfoById" parameterType="java.lang.String">
        delete from bms_yfstock_codeinfo where pk_id = #{pkId}
    </delete>

    <delete id="deleteBmsYfstockCodeinfoByIds">
        delete from bms_yfstock_codeinfo where pk_id in
        <foreach item="pkId" collection="array" open="(" separator="," close=")">
            #{pkId}
        </foreach>
    </delete>

    <update id="updateBmsYfstockCodeinfoStatusByIds">
        update bms_yfstock_codeinfo set status = #{status} where pk_id in
        <foreach item="pkId" collection="array" open="(" separator="," close=")">
            #{pkId}
        </foreach>
    </update>

    <select id="selectByCode" resultMap="BmsYfstockCodeinfoResult">
        <include refid="selectBmsYfstockCodeinfoVo"/>
        where del_flag = '0'
        <if test="codes != null and codes.size>0 ">
            AND  relate_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <select id="selectByCodeType" resultMap="BmsYfstockCodeinfoResult">
        <include refid="selectBmsYfstockCodeinfoVo"/>
        where del_flag = '0'
        <if test="codes != null and codes.size>0 ">
            AND  relate_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
        <if test="type != null and type != ''">
            AND code_type = #{type}
        </if>
    </select>

    <select id="selectIsBillByCode"  resultType="com.bbyb.joy.bms.domain.dto.charginglogic.DispatchBillingBean">
        SELECT
        yi.id AS  yfcostId,
        yi.expenses_code AS expensesCode,
        yi.pk_id AS expensesPkId,
        yi.main_pk_id AS mainExpensesPkId,
        bym.code_pk_id AS yfBillPkId,
        bym.code_id yfbillId,
        bym.relate_code yfbillCode,
        bym.relate_code workCode,
        yi.bill_id billId
        FROM bms_yfcost_info yi
        LEFT JOIN bms_yfexpenses_middle bym ON bym.main_pk_id = yi.main_pk_id AND bym.code_type = #{type}
        WHERE yi.del_flag = 0
        <if test="codes != null and codes.size>0">
            AND bym.relate_code IN
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
        <if test="codeType != null">
            AND yi.expenses_type = #{codeType}
        </if>
    </select>

    <select id="selectIsBillByStockCode"  resultType="com.bbyb.joy.bms.domain.dto.charginglogic.DispatchBillingBean">
        SELECT
        yi.id yfcostId,
        yi.expenses_code expensesCode,
        stk.id yfbillId,
        stk.stock_code yfbillCode,
        ym.business_code workCode,
        yi.bill_id billId
        from bms_yfcost_info yi
        left join bms_yfcost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_yfexpenses_middle bym on bym.expenses_id=yi.id
        left join bms_yfstockinfo stk on bym.yfbill_id = stk.id and IFNULL(ym.code_count,1)=1
        where yi.del_flag=0
        <if test="codes != null and codes.size>0 ">
            AND ym.business_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>


    <insert id="batchSave" parameterType="java.util.List">
        insert into bms_yfstock_codeinfo(id,code_type,relate_code,company_id,network_code,carrier_code,carrier_name,client_id,transport_type,warehouse_code,warehouse_name,stockoper_time,store_code,receiving_store,order_date,trust,weight,volume,total_boxes,split_total_number,cw_full_cases,cw_split_cases,ld_full_cases,ld_split_cases,sku_number,warehouse_area,cargo_value,oper_dept_id,oper_code,oper_by,oper_time,del_flag,cost_status,billing_status,order_type,if_autarky,storage_service_provider,pallet_number,cw_pallet_number,lc_pallet_number,ld_pallet_number,order_no,platform_code,destination_province,destination_city,destination_area,fail_remark,import_time,import_code,import_by,order_source,is_timeout)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id},#{entity.codeType},#{entity.relateCode},#{entity.companyId},#{entity.networkCode},#{entity.carrierCode},#{entity.carrierName},#{entity.clientId},#{entity.transportType},#{entity.warehouseCode},#{entity.warehouseName},#{entity.stockoperTime},#{entity.storeCode},#{entity.receivingStore},#{entity.orderDate},#{entity.trust},#{entity.weight},#{entity.volume},#{entity.totalBoxes},#{entity.splitTotalNumber},#{entity.cwFullCases},#{entity.cwSplitCases},#{entity.ldFullCases},#{entity.ldSplitCases},#{entity.skuNumber},#{entity.warehouseArea},#{entity.cargoValue},#{entity.operDeptId},#{entity.operCode},#{entity.operBy},#{entity.operTime},#{entity.delFlag},#{entity.costStatus},#{entity.billingStatus},#{entity.orderType},#{entity.ifAutarky},#{entity.storageServiceProvider},#{entity.palletNumber},#{entity.cwPalletNumber},#{entity.lcPalletNumber},#{entity.ldPalletNumber},#{entity.orderNo},#{entity.platformCode},#{entity.destinationProvince},#{entity.destinationCity},#{entity.destinationArea},#{entity.failRemark},#{entity.importTime},#{entity.importCode},#{entity.importBy},#{entity.orderSource},#{entity.isTimeout})
        </foreach>
    </insert>

    <update id="deleteStockCodeinfoByCodes" parameterType="java.util.List" >
        update bms_yfstock_codeinfo info
            left join bms_yfstockcode_detailinfo goods
        on info.id = goods.yfstock_id
        set info.del_flag =1 ,
        goods.del_flag = 1
        where
        info.relate_code in
        <foreach collection="codes" item="codes" open="(" separator="," close=")">
            #{codes}
        </foreach>
    </update>

    <update id="deleteStockCodeinfoByCodesAndType" parameterType="java.util.List" >
        update bms_yfstock_codeinfo info
        left join bms_yfstockcode_detailinfo goods
        on info.id = goods.yfstock_id
        set info.del_flag =1 ,
        goods.del_flag = 1
        where
        info.relate_code in
        <foreach collection="codes" item="codes" open="(" separator="," close=")">
            #{codes}
        </foreach>
        <if test="type != null and type != '' ">
            and info.code_type = #{type}
        </if>
    </update>

</mapper>