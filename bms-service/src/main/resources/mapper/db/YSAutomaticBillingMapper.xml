<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.YSAutomaticBillingMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.charginglogic.AutoYsCodeInfo" id="AutoYSCodeInfoResult"
               extends="mapper.db.BmsYsbillcodeinfoMapper.BmsYsbillcodeinfoResult">
        <result property="paymentDays"    column="payment_days"    />
        <result property="oddBoxes"    column="odd_boxes"    />
        <result property="boxType"    column="box_type"    />
        <result property="temperatureType"    column="temperature_type"    />
        <result property="itemId"    column="item_id"    />
        <result property="trust"    column="trust"    />
        <result property="warehouseArea"    column="warehouse_area"    />
        <result property="clientId"    column="client_id"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="aqty"    column="aqty"    />
        <result property="palletNumber"    column="pallet_number"    />
        <result property="carModelStr"    column="car_model"    />
        <result property="splitTotalNumber"    column="split_total_number"    />
        <result property="skuNumber"    column="sku_number"    />
        <result property="lineStoreNum"    column="store_num"    />
        <result property="lineTotalMileage"    column="total_mileage"    />
        <result property="isreject"    column="isreject"    />
        <result property="warehouseCity"    column="warehouse_city"    />
        <result property="cwPalletNumber"    column="cw_pallet_number"    />
        <result property="ldPalletNumber"    column="ld_pallet_number"    />
        <result property="orderNo"    column="order_no"    />
        <result property="feeBelong"    column="fee_belong"    />
        <result property="orderDateWeek"    column="orderDateWeek"    />
        <result property="signDateWeek"    column="signDateWeek"    />
        <result property="unit"    column="unit"    />
        <result property="unitName"    column="unitName"    />
    </resultMap>

<!--    查询应收单据表需要计费的所有客户id以及机构id-->
    <select id="selectYsBillCodeClientIds"  resultMap="AutoYSCodeInfoResult" >
        select t2.id as client_id
        , t1.company_id
        , t1.order_date
        , t1.signing_date
        from bms_ysbillcodeinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code = t3.warehouse_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0 and t2.id&lt;&gt;'' and t1.company_id&lt;&gt;'' and t1.code_type = #{codeType}
        <if test="subjectID != null ">and t2.id = #{subjectID}</if>
        <if test="code != null ">and t1.relate_code =#{code}</if>
        <if test="param.startDate!=null and param.dateType==1">and DATE_FORMAT(t1.signing_date,'%Y-%m-%d') &gt;= #{param.startDate}</if>
        <if test="param.endDate!=null and param.dateType==1">and DATE_FORMAT(t1.signing_date,'%Y-%m-%d') &lt;= #{param.endDate} </if>
        <if test="param.startDate!=null and param.dateType==2">and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &gt;= #{param.startDate}</if>
        <if test="param.endDate!=null and param.dateType==2">and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &lt;= #{param.endDate} </if>
        <if test="param.companyId!=null">
            and t1.company_id in
            <foreach collection="param.companyId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.relateCodeList!=null">
            and t1.relate_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.clientName!=null and param.clientName!=''">and t2.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.receivingStore!=null and param.receivingStore!=''">and t1.receiving_store like concat('%', #{param.receivingStore}, '%')</if>
        <if test="param.warehouseName!=null and param.warehouseName!=''">and t3.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
        GROUP BY t2.id,t1.company_id
    </select>
    <!--    根据客户id以及机构id 查询应收单据表信息-->
    <select id="selectYsBillCodeInfo"  resultMap="AutoYSCodeInfoResult">
        select t1.id ,t2.id as client_id , t1.company_id,t2.payment_days,t1.pk_id,
        t1.code_type,t1.relate_code,t1.scheduling_bill_code,t1.client_code,
        t1.client_name,t1.network_code,t1.warehouse_code,
        t1.total_boxes,t1.total_number,t1.total_weight,t1.total_volume,
        t1.cargo_value,t1.order_date,t1.signing_date,t1.if_autarky,
        t1.storage_service_provider,t1.line_code,t1.line_name,t1.if_base_stores,
        t1.if_Super_base_kilometer,t1.store_distance_kilometer,t1.delivery_code,t1.delivery_name,
        t1.store_code,t1.receiving_store,t1.province_origin,t1.originating_City,t1.originating_area,
        t1.originating_address,t1.destination_Province,t1.destination_city,t1.destination_area,t1.destination_address,
        t1.warehouse_code,t3.warehouse_name,t3.warehouse_city,t1.car_model,t1.delivery_mode,
        t1.split_total_number,t1.sku_number,t1.is_rejected,t1.reject_parties,line.total_mileage,line.store_num,t1.order_type,
        t1.pallet_number,t1.near_store_km,t1.skuString,store.isreject,t1.temperatureTypeStr as temperature_type
        ,t1.transport_type as transportType,
        t1.is_timeout as is_timeout
        ,IFNULL(t1.cw_pallet_number,0) as cw_pallet_number ,IFNULL(t1.lc_pallet_number,0) as lc_pallet_number,IFNULL(t1.ld_pallet_number,0) as ld_pallet_number,t1.order_no
        ,weekday(t1.order_date) orderDateWeek,weekday(t1.signing_date) signDateWeek
        from bms_ysbillcodeinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        left join bms_routeinfo line on t1.line_code=line.route_code and line.del_flag=0
        left join mdm_storeinfo store on t1.store_code=store.store_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0
        and t2.id = #{clientId} and t1.company_id = #{companyId} and t1.code_type = #{codeType}
        <if test="code != null ">and t1.relate_code =#{code}</if>
        <if test="param.dateType==1">
            and DATE_FORMAT(t1.signing_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.signing_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.dateType==2">
            and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.relateCodeList!=null">
            and t1.relate_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.clientName!=null and param.clientName!=''">and t2.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.receivingStore!=null and param.receivingStore!=''">and t1.receiving_store like concat('%', #{param.receivingStore}, '%')</if>
        <if test="param.warehouseName!=null and param.warehouseName!=''">and t3.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
        -- 查询今天之前的数据
        ORDER BY t2.id,t1.company_id,t1.relate_code asc
    </select>
    <!--    查询应收库存表 需要计费的所有客户id以及机构id-->
    <select id="selectYsStockInfoClientIds"  resultMap="AutoYSCodeInfoResult" >
        select t2.id as client_id, t1.company_id
        from bms_ysstockinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code = t3.warehouse_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0 and t2.id&lt;&gt;'' and t1.company_id&lt;&gt;''
        <if test="subjectID != null ">and t2.id = #{subjectID}</if>
        <if test="code != null ">and t1.stock_code =#{code}</if>
        <if test="param.startDate!=null">and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &gt;= #{param.startDate}</if>
        <if test="param.endDate!=null">and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &lt;= #{param.endDate} </if>
        <if test="param.companyId!=null">
            and t1.company_id in
            <foreach collection="param.companyId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.relateCodeList!=null">
            and t1.stock_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.clientName!=null and param.clientName!=''">and t2.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.warehouseName!=null and param.warehouseName!=''">and t3.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
        GROUP BY t2.id,t1.company_id
    </select>
    <select id="selectYSvlueAddedstockinfoClientIDS"  resultMap="AutoYSCodeInfoResult" >
        select t2.id as client_id, t1.company_id
        from bms_ysstockinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_vlaue_status=0 and t2.id&lt;&gt;'' and t1.company_id&lt;&gt;''
        and DATE_SUB(CURDATE(), INTERVAL 30 DAY) &lt;= date(t1.instorage_time)
        <if test="clientIds != null ">
            and t2.id in
            <foreach item="id" collection="clientIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="code != null ">and t1.stock_code =#{code}</if>
        GROUP BY t2.id,t1.company_id
    </select>

    <select id="selectYsAddedServiceOrderChargingInfoClientIds"  resultMap="AutoYSCodeInfoResult" >
        select t2.id as client_id, t1.dept_id as company_id
        from bms_addedfee t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        WHERE t1.del_flag=0 and t1.settle_type = 1 and t2.del_flag=0 and t1.cost_status=0 and t2.id&lt;&gt;'' and t1.dept_id&lt;&gt;''
        <!--and DATE_SUB(CURDATE(), INTERVAL 30 DAY) &lt;= date(t1.instorage_time)-->
        and DATE_FORMAT(t1.creat_date,'%Y-%m-%d') &gt;= #{startDate}
        and DATE_FORMAT(t1.creat_date,'%Y-%m-%d') &lt;= #{endDate}
        <if test="clientIds != null ">
            and t2.id in
            <foreach item="id" collection="clientIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="expensesCode!=null and expensesCode!=''">
            and t1.expenses_code = #{expensesCode}
        </if>
        <if test="code != null ">and t1.relate_code =#{code}</if>
        GROUP BY t2.id,t1.dept_id
    </select>
    <!--    根据客户id以及机构id 查询应收库存表-->
    <select id="selectYSstockInfo"  resultMap="AutoYSCodeInfoResult">
        select t1.id ,t2.id as client_id , t1.company_id,t2.payment_days,
        t1.stock_code as relate_code,t1.client_code,t1.client_name,t1.warehouse_code,
        t1.instorage_time as signing_date,t1.instorage_time as order_date,t1.total_boxes,t1.odd_boxes,t1.box_type,
        t1.trust,t1.warehouse_area,t1.weight as total_weight,t1.volume as total_volume,
        t1.warehouse_code,t3.warehouse_name,t3.warehouse_city,t1.temperature_type,t1.aqty,t1.pallet_number,4 as code_type,
        weekday(t1.instorage_time) orderDateWeek,weekday(t1.instorage_time) signDateWeek
        from bms_ysstockinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0
        and t2.id = #{clientId} and t1.company_id = #{companyId}
        and DATE_FORMAT(t1.instorage_time ,'%Y-%m-%d') &gt;= #{param.startDate}
        and DATE_FORMAT(t1.instorage_time ,'%Y-%m-%d') &lt;= #{param.endDate}

        <if test="code != null ">and t1.stock_code =#{code}</if>
        <if test="param.relateCodeList!=null">
            and t1.stock_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.clientName!=null and param.clientName!=''">and t2.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.warehouseName!=null and param.warehouseName!=''">and t3.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
    </select>


    <!--    根据客户id以及机构id 查询应收库存表-->
    <select id="selectYSstockInfoValue"  resultMap="AutoYSCodeInfoResult">
        select t1.id ,t2.id as client_id , t1.company_id,t2.payment_days,
        t1.stock_code as relate_code,t1.client_code,t1.client_name,t1.warehouse_code,
        t1.instorage_time as signing_date,t1.instorage_time as order_date,t1.total_boxes,t1.odd_boxes,t1.box_type,
        t1.trust,t1.warehouse_area,t1.weight as total_weight,t1.volume as total_volume,
        t1.warehouse_code,t3.warehouse_name,t3.warehouse_city,t1.temperature_type,t1.aqty,t1.pallet_number
        from bms_ysstockinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_vlaue_status=0
        and t2.id = #{clientId} and t1.company_id = #{companyId}
        <if test="code != null ">and t1.stock_code =#{code}</if>
        -- 查询今天之前的数据
        and timestamp(date_add(curdate(), interval - 0 day))>t1.instorage_time
        -- 查询上月到现在的数据
        and PERIOD_DIFF( date_format( now( ) , '%Y%m' ) , date_format( t1.instorage_time, '%Y%m' ) ) &lt;=1
    </select>

    <!--    根据客户id以及机构id 查询增值表-->
    <select id="selectYSstockInfoValueService"  resultMap="AutoYSCodeInfoResult">
        select t1.id ,t2.id as client_id , t1.dept_id as company_id,t2.payment_days,t1.item_id,
        t1.relate_code as relate_code,t1.client_code,t2.client_name,t1.warehouse_code,
        t3.warehouse_name,t3.warehouse_city
        ,t1.number as total_number
        ,t1.number as pallet_number
        ,t1.number as trust
        ,t1.number as sku_number
        ,t1.unit
        ,t1.creat_date as signing_date
        ,t1.fee_belong
        from bms_addedfee t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0
        and t2.id = #{clientId} and t1.dept_id = #{companyId}
        and t1.settle_type = 1
        and DATE_FORMAT(t1.creat_date ,'%Y-%m-%d') &gt;= #{startDate}
        and DATE_FORMAT(t1.creat_date ,'%Y-%m-%d') &lt;= #{endDate}
        <if test="code != null ">and t1.relate_code =#{code}</if>
    </select>





<!--    预估表-->
    <!-- 批量新增应收费用表  bms_yscost_info_estimate  -->
    <insert id="insertBmsyscostInfoEstimateBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostInfo" >
        INSERT INTO bms_yscost_info_estimate
        (
        id, expenses_code, business_type,client_id, institution_id,
        expenses_type, cost_dimension, charge_type,fee_flag,quoterule_id,
        rule_name,remarks,
        freight,Ultrafar_fee,superframes_fee,excess_fee,reduce_fee,delivery_fee,
        outboundsorting_fee,shortbarge_fee,return_fee,exception_fee,
        other_cost1,other_cost2,other_cost3,other_cost4,other_cost5,other_cost6,
        other_cost7,other_cost8,other_cost9,other_cost10,other_cost11,other_cost12,
        oper_code,oper_by,oper_time,del_flag,bill_date,business_time,cost_attribute,
        over_num,over_sendnum
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.expensesCode}, #{item.businessType}, #{item.clientId}, #{item.institutionId},
            #{item.expensesType}, #{item.costDimension}, #{item.chargeType}, #{item.feeFlag}, #{item.quoteruleId},
            #{item.ruleName}, #{item.remarks},
            #{item.freight},#{item.ultrafarFee},#{item.superframesFee},#{item.excessFee},#{item.reduceFee}, #{item.deliveryFee},
            #{item.outboundsortingFee}, #{item.shortbargeFee},#{item.returnFee},#{item.exceptionFee},
            #{item.otherCost1},#{item.otherCost2},#{item.otherCost3},#{item.otherCost4},#{item.otherCost5},#{item.otherCost6},
            #{item.otherCost7},#{item.otherCost8},#{item.otherCost9},#{item.otherCost10},#{item.otherCost11},#{item.otherCost12},
            #{item.operCode}, #{item.operBy}, #{item.operTime}, 0,#{item.billDate},#{item.businessTime},#{item.costAttribute},
            #{item.overNum}, #{item.overSendnum}
            )
        </foreach>
    </insert>
    <!-- 批量作废  bms_yscost_info_estimate  -->
    <update id="delBmsyscostInfoEstimate">
        DELETE FROM  bms_yscost_info_estimate where expenses_type = #{expensesType}
    </update>


    <!-- 批量新增应收费用中间表  bms_ysexpenses_middle_estimate  -->
    <insert id="insertBmsYsexpensesMiddleEstimateBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle" >
        INSERT INTO bms_ysexpenses_middle_estimate
        (
        ysbill_id, ysbil_type,expenses_id, oper_by,oper_time, del_flag,expenses_type
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.ysbillId}, #{item.ysbilType},#{item.expensesId}, #{item.operBy}, #{item.operTime}, 0,#{item.expensesType}
            )
        </foreach>
    </insert>
    <!-- 批量作废  bms_ysexpenses_middle_estimate  -->
    <update id="delBmsYsexpensesMiddeEstimate">
        DELETE FROM bms_ysexpenses_middle_estimate where expenses_type = #{expensesType}
    </update>



    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeDetailinfo" id="AutoYSGoodsResult"
               extends="mapper.db.BmsYsbillcodeDetailinfoMapper.BmsYsbillcodeDetailinfoResult">
        <result property="totalAllBoxes"    column="total_all_boxes"    />
    </resultMap>
    <!--    查询商品信息-->
    <select id="selectYsGoodsByYsbillId"  resultMap="AutoYSGoodsResult" >
        select ysbill_id, sku_code, sku_class, sku_name, temperature_type,
        sum(case when odd_boxes>0 then total_boxes+1 else total_boxes end ) total_all_boxes,
        sum(odd_boxes) odd_boxes,sum(total_boxes) total_boxes, sum(box_type) box_type, sum(contents_number) contents_number,
        max(weight) weight, max(volume) volume,sum(total_weight) total_weight, sum(total_volume) total_volume,sum(total_amount) total_amount,
        max(price) price
        from bms_ysbillcode_detailinfo
        where del_flag = 0 and ysbill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY ysbill_id
        <if test="groupType == 1 ">,sku_class</if>
        <if test="groupType == 2 ">,temperature_type</if>
        <if test="groupType == 3 ">,sku_class,temperature_type</if>
        <if test="groupType == 4 ">,id</if>
    </select>
    <!--    查询商品信息-->
    <select id="selectYsGoodsGroupByYsbillId"  resultMap="AutoYSGoodsResult" >
        select ysbill_id, sku_code, sku_class, sku_name, temperature_type,
        sum(case when odd_boxes>0 then total_boxes+1 else total_boxes end ) total_all_boxes,
        sum(odd_boxes) odd_boxes,sum(total_boxes) total_boxes, sum(box_type) box_type, sum(contents_number) contents_number,
        max(weight) weight, max(volume) volume,sum(total_weight) total_weight, sum(total_volume) total_volume,sum(total_amount) total_amount,
        max(price) price
        from bms_ysbillcode_detailinfo
        where del_flag = 0 and ysbill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        <if test="groupType == 1 ">sku_class</if>
        <if test="groupType == 2 ">temperature_type</if>
        <if test="groupType == 3 ">sku_class,temperature_type</if>
    </select>

    <!--    查询浮动规则信息-->
    <select id="getDoubleRatio"  resultType="com.bbyb.joy.bms.domain.dto.PubFloatfeeRuleInfo" >
        SELECT
        a.rule_type as ruleType,
        a.start_hour as startHour,
        a.end_hour as endHour,
        main.rule_name as ruleName,
        DATE_FORMAT(a.start_date, '%Y-%m-%d %H:%i:%s') as startDate,
        DATE_FORMAT(a.end_date, '%Y-%m-%d %H:%i:%s') as endDate,
        MAX(main.ratio/100) as ratio,
        b.client_code as clientCode,
        cli.id as clientId,
        <!--        c.subject_id as subjectId,-->
        d.item_code as subjectId,
        d.item_name as itemName,
        df.item_code AS itemCode,
        main.order_type AS orderType
        from pub_floatfee_rule_main main
        left join pub_floatfee_rule a
        on main.id=a.rule_main_id
        and a.del_flag=0
        left join pub_floatfee_rule_client b
        on main.id = b.rule_id and b.del_flag=0
        left join pub_floatfee_rule_feetype c
        on c.rule_id = main.id and c.del_flag=0
        left join pub_fee_subject d
        on d.id = c.subject_id
        left join bms_clientinfo cli
        on cli.client_code = b.client_code
        and cli.del_flag =0
        left join pub_fee_subject df
        on df.id = d.father_id
        and df.del_flag = 0
        where main.is_enable = 0
        and main.del_flag=0
        and IFNULL(c.subject_id,'')!=''
        and IFNULL(cli.id,'')!=''
        <if test="instorageTime!=null">
            and a.start_date &lt;= #{instorageTime}
            and a.end_date &gt;= #{instorageTime}
        </if>
        <if test="clientId!=null">
            and IFNULL(cli.id,'')=#{clientId}
        </if>
        <if test="feeType!=null">
            and c.subject_id=#{feeType}
        </if>
        <if test="operType!=null">
            and df.item_code= concat('00', #{operType},'')
        </if>
        GROUP BY
        a.id,
        cli.id,
        df.item_code,
        d.item_code,
        c.subject_id;

    </select>

    <resultMap id="query" type="com.bbyb.joy.bms.domain.dto.PubFloatfeeRuleInfo">
        <result property="endDate" column="end_date"/>
        <result property="endHour" column="end_hour"/>
        <result property="startDate" column="start_date"/>
        <result property="startHour" column="start_hour"/>
        <result property="ratio" column="ration"/>
    </resultMap>


    <!--    库存单查询出库单的总箱数、总件数-->
    <select id="getOutStockInfo"  resultType="com.bbyb.joy.bms.domain.dto.charginglogic.AutoYsCodeInfo" >
        select
        sum(IFNULL(total_boxes,0)) as outTotalBoxes,
        sum(IFNULL(sku_number,0)) as outSkuNumber
        from bms_ysbillcodeinfo ysb
        left join bms_clientinfo cli
        on cli.client_code = ysb.client_code
        and cli.del_flag = 0
        where
        ysb.code_type = 2
        and ysb.del_flag=0
        and cli.id = #{clientId}
        and ysb.company_id = #{companyId}
        and DATE_FORMAT(ysb.signing_date,'%Y-%m') = #{signingDate}
    </select>

    <select id="selectCodeBySchedulingAndMaxSignDate" resultMap="AutoYSCodeInfoResult">
        SELECT
            max(signing_date) as signing_date,
            scheduling_bill_code
        from bms_ysbillcodeinfo
        where del_flag=0
          and code_type=1
          and cost_status=0
          and scheduling_bill_code in
            <foreach collection="scheduleCodes" item="scheduleCode" open="(" separator="," close=")">
                #{scheduleCode}
            </foreach>
        GROUP BY scheduling_bill_code
    </select>

    <select id="selectChargingCodeInfos" resultMap="AutoYSCodeInfoResult">
        select t1.id,t1.relate_code,t2.id as client_id , t1.company_id
        from bms_ysbillcodeinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code = t3.warehouse_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0 and t2.id&lt;&gt;'' and t1.company_id&lt;&gt;'' and t1.code_type = #{codeType}
        <if test="subjectID != null ">and t2.id = #{subjectID}</if>
        <if test="code != null ">and t1.relate_code =#{code}</if>
        <if test="param.startDate!=null and param.dateType==1">and DATE_FORMAT(t1.signing_date,'%Y-%m-%d') &gt;= #{param.startDate}</if>
        <if test="param.endDate!=null and param.dateType==1">and DATE_FORMAT(t1.signing_date,'%Y-%m-%d') &lt;= #{param.endDate} </if>
        <if test="param.startDate!=null and param.dateType==2">and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &gt;= #{param.startDate}</if>
        <if test="param.endDate!=null and param.dateType==2">and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &lt;= #{param.endDate} </if>
        <if test="param.companyId!=null">
            and t1.company_id in
            <foreach collection="param.companyId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.relateCodeList!=null">
            and t1.relate_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.clientName!=null and param.clientName!=''">and t2.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.receivingStore!=null and param.receivingStore!=''">and t1.receiving_store like concat('%', #{param.receivingStore}, '%')</if>
        <if test="param.warehouseName!=null and param.warehouseName!=''">and t3.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
    </select>

    <select id="selectYsCharStockInfos"  resultMap="AutoYSCodeInfoResult" >
        select t1.id,t1.stock_code,t2.id as client_id, t1.company_id
        from bms_ysstockinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code = t3.warehouse_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0 and t2.id&lt;&gt;'' and t1.company_id&lt;&gt;''
        <if test="subjectID != null ">and t2.id = #{subjectID}</if>
        <if test="code != null ">and t1.stock_code =#{code}</if>
        <if test="param.startDate!=null">and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &gt;= #{param.startDate}</if>
        <if test="param.endDate!=null">and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &lt;= #{param.endDate} </if>
        <if test="param.companyId!=null">
            and t1.company_id in
            <foreach collection="param.companyId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.relateCodeList!=null">
            and t1.stock_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.clientName!=null and param.clientName!=''">and t2.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.warehouseName!=null and param.warehouseName!=''">and t3.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
    </select>

    <select id="selectYSBillCodeClientIDSByEstimate" resultType="java.lang.String">
        SELECT DISTINCT
            t1.relation_id as client_id,
            null as company_id
            FROM pub_quoterule t1
        LEFT JOIN pub_quoterule_detail t2 ON t2.quoterule_id = t1.id AND t2.del_flag = 0 AND t2.audit_state IN (1,2)
        LEFT JOIN pub_quoterule_template t3 ON t3.id = t2.quoterule_template_id AND t3.del_flag = 0 AND t3.is_enable = '0' AND t3.bj_type IN (1,2)
        WHERE t1.del_flag = 0
            AND t2.end_time &gt;= NOW() AND t2.start_time &lt;= NOW()
            AND t3.consolidation_rule = 4
            AND t1.rule_type = 1
            AND t2.bill_type = 1
    </select>

    <select id="selectYsStockInfoClientIdsByEstimate" resultType="java.lang.String">
        SELECT DISTINCT
            t1.relation_id as client_id,null as company_id
        FROM pub_quoterule t1
                 LEFT JOIN pub_quoterule_detail t2 ON t2.quoterule_id = t1.id AND t2.del_flag = 0 AND t2.audit_state IN (1,2)
                 LEFT JOIN pub_quoterule_template t3 ON t3.id = t2.quoterule_template_id AND t3.del_flag = 0 AND t3.is_enable = '0' AND t3.bj_type IN (1,2)
        WHERE t1.del_flag = 0
          AND t2.end_time &gt;= NOW() AND t2.start_time &lt;= NOW()
          AND t3.consolidation_rule = 4
          AND t1.rule_type = 1
          AND t2.bill_type IN (2,3,4)
    </select>

    <select id="selectExistYSstockinfoClientIDSByEstimate"
            resultMap="AutoYSCodeInfoResult">
        select DISTINCT t2.id as client_id, t1.company_id
        from bms_ysstockinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t2.id&lt;&gt;'' and t1.company_id&lt;&gt;''
        <if test="subjectIDs != null ">
            and t2.id in
            <foreach item="subjectID" collection="subjectIDs" open="(" separator="," close=")">
                #{subjectID}
            </foreach>
        </if>
        <if test="param.startDate!=null"> and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &gt;= #{param.startDate}</if>
        <if test="param.endDate!=null"> and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &lt;= #{param.endDate} </if>
        GROUP BY t2.id,t1.company_id
    </select>

    <select id="selectExistYSBillCodeClientIDSByEstimate"
            resultMap="AutoYSCodeInfoResult">
        select DISTINCT t2.id as client_id , t1.company_id
        from bms_ysbillcodeinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        WHERE t1.del_flag=0 and t2.id&lt;&gt;'' and t1.company_id&lt;&gt;'' and t1.code_type = #{codeType}
        <if test="subjectIDs != null ">
            and t2.id in
            <foreach item="subjectID" collection="subjectIDs" open="(" separator="," close=")">
                #{subjectID}
            </foreach>
        </if>
        <if test="param.startDate!=null"> and DATE_FORMAT(t1.signing_date,'%Y-%m-%d') &gt;= #{param.startDate}</if>
        <if test="param.endDate!=null"> and DATE_FORMAT(t1.signing_date,'%Y-%m-%d')	&lt;= #{param.endDate} </if>
        GROUP BY t2.id,t1.company_id
    </select>

    <select id="selectYSBillCodeInfoByEstimate" resultMap="AutoYSCodeInfoResult">
        select t1.id ,t2.id as client_id , t1.company_id,t2.payment_days,t1.pk_id,
        t1.code_type,t1.relate_code,t1.scheduling_bill_code,t1.client_code,
        t1.client_name,t1.network_code,t1.warehouse_code,
        t1.total_boxes,t1.total_number,t1.total_weight,t1.total_volume,
        t1.cargo_value,t1.order_date,t1.signing_date,t1.if_autarky,
        t1.storage_service_provider,t1.line_code,t1.line_name,t1.if_base_stores,
        t1.if_Super_base_kilometer,t1.store_distance_kilometer,t1.delivery_code,t1.delivery_name,
        t1.store_code,t1.receiving_store,t1.province_origin,t1.originating_City,t1.originating_area,
        t1.originating_address,t1.destination_Province,t1.destination_city,t1.destination_area,t1.destination_address,
        t1.warehouse_code,t3.warehouse_name,t3.warehouse_city,t1.car_model,t1.delivery_mode,
        t1.split_total_number,t1.sku_number,t1.is_rejected,t1.reject_parties,line.total_mileage,line.store_num,t1.order_type,
        t1.pallet_number,t1.near_store_km,t1.skuString,store.isreject,t1.temperatureTypeStr as temperature_type
        ,t1.transport_type as transportType
        ,IFNULL(t1.cw_pallet_number,0) as cw_pallet_number ,IFNULL(t1.lc_pallet_number,0) as lc_pallet_number,IFNULL(t1.ld_pallet_number,0) as ld_pallet_number,t1.order_no
        from bms_ysbillcodeinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        left join bms_routeinfo line on t1.line_code=line.route_code and line.del_flag=0
        left join mdm_storeinfo store on t1.store_code=store.store_code
        WHERE t1.del_flag=0 and t2.del_flag=0
        and t2.id = #{clientId} and t1.company_id = #{companyId} and t1.code_type = #{codeType}
        <if test="code != null ">and t1.relate_code =#{code}</if>
        and DATE_FORMAT(t1.signing_date,'%Y-%m-%d') &gt;= #{param.startDate}
        and DATE_FORMAT(t1.signing_date,'%Y-%m-%d') &lt;= #{param.endDate}
        -- 查询今天之前的数据
        and timestamp(date_add(curdate(), interval - 0 day))>t1.signing_date
        ORDER BY t2.id,t1.company_id,t1.relate_code asc
    </select>

    <select id="selectYSstockInfoByEstimate" resultType="com.bbyb.joy.bms.domain.dto.charginglogic.AutoYsCodeInfo">
        select t1.id ,t2.id as client_id , t1.company_id,t2.payment_days,
        t1.stock_code as relate_code,t1.client_code,t1.client_name,t1.warehouse_code,
        t1.instorage_time as signing_date,t1.instorage_time as order_date,t1.total_boxes,t1.odd_boxes,t1.box_type,
        t1.trust,t1.warehouse_area,t1.weight as total_weight,t1.volume as total_volume,
        t1.warehouse_code,t3.warehouse_name,t3.warehouse_city,t1.temperature_type,t1.aqty,t1.pallet_number
        from bms_ysstockinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        WHERE t1.del_flag=0 and t2.del_flag=0
        and t2.id = #{clientId} and t1.company_id = #{companyId}
        and DATE_FORMAT(t1.instorage_time ,'%Y-%m-%d') &gt;= #{param.startDate}
        and DATE_FORMAT(t1.instorage_time ,'%Y-%m-%d') &lt;= #{param.endDate}
        -- 查询今天之前的数据
        and timestamp(date_add(curdate(), interval - 0 day))>t1.instorage_time
    </select>

    <update id="truncateEstimate" >
        truncate table bms_yscost_info_estimate_custom;
        truncate table bms_ysexpenses_middle_estimate_customer;
    </update>

    <insert id="insertBmsyscostInfoEstimateBatchByEstimate" parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostInfo">
        INSERT INTO bms_yscost_info_estimate_custom
        (
        id, expenses_code, business_type,client_id, institution_id,
        expenses_type, cost_dimension, charge_type,fee_flag,quoterule_id,
        rule_name,remarks,
        freight,Ultrafar_fee,superframes_fee,excess_fee,reduce_fee,delivery_fee,
        outboundsorting_fee,shortbarge_fee,return_fee,exception_fee,
        other_cost1,other_cost2,other_cost3,other_cost4,other_cost5,other_cost6,
        other_cost7,other_cost8,other_cost9,other_cost10,other_cost11,other_cost12,
        oper_code,oper_by,oper_time,del_flag,bill_date,business_time,cost_attribute,
        over_num,over_sendnum
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.expensesCode}, #{item.businessType}, #{item.clientId}, #{item.institutionId},
            #{item.expensesType}, #{item.costDimension}, #{item.chargeType}, #{item.feeFlag}, #{item.quoteruleId},
            #{item.ruleName}, #{item.remarks},
            #{item.freight},#{item.ultrafarFee},#{item.superframesFee},#{item.excessFee},#{item.reduceFee}, #{item.deliveryFee},
            #{item.outboundsortingFee}, #{item.shortbargeFee},#{item.returnFee},#{item.exceptionFee},
            #{item.otherCost1},#{item.otherCost2},#{item.otherCost3},#{item.otherCost4},#{item.otherCost5},#{item.otherCost6},
            #{item.otherCost7},#{item.otherCost8},#{item.otherCost9},#{item.otherCost10},#{item.otherCost11},#{item.otherCost12},
            #{item.operCode}, #{item.operBy}, #{item.operTime}, 0,#{item.billDate},#{item.businessTime},#{item.costAttribute},
            #{item.overNum}, #{item.overSendnum}
            )
        </foreach>
    </insert>

    <insert id="insertBmsYsexpensesMiddleEstimateBatchByEstimate" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle" >
        INSERT INTO bms_ysexpenses_middle_estimate_customer
        (
        ysbill_id, ysbil_type,expenses_id, oper_by,oper_time, del_flag,expenses_type
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.ysbillId}, #{item.ysbilType},#{item.expensesId}, #{item.operBy}, #{item.operTime}, 0,#{item.expensesType}
            )
        </foreach>
    </insert>


    <select id="selectCodeBySchedulingAndMaxSignDateByEstimate" resultMap="AutoYSCodeInfoResult">
        SELECT
        max(signing_date) as signing_date,
        scheduling_bill_code
        from bms_ysbillcodeinfo
        where del_flag=0
        and code_type=1
        and scheduling_bill_code in
        <foreach collection="scheduleCodes" item="scheduleCode" open="(" separator="," close=")">
            #{scheduleCode}
        </foreach>
        GROUP BY scheduling_bill_code
    </select>

    <select id="selectYsBillCodeInfoForAfterCurDay" resultMap="AutoYSCodeInfoResult">
        select t1.id ,t2.id as client_id , t1.company_id,t2.payment_days,t1.pk_id,
        t1.code_type,t1.relate_code,t1.scheduling_bill_code,t1.client_code,
        t1.client_name,t1.network_code,t1.warehouse_code,
        t1.total_boxes,t1.total_number,t1.total_weight,t1.total_volume,
        t1.cargo_value,t1.order_date,t1.signing_date,t1.if_autarky,
        t1.storage_service_provider,t1.line_code,t1.line_name,t1.if_base_stores,
        t1.if_Super_base_kilometer,t1.store_distance_kilometer,t1.delivery_code,t1.delivery_name,
        t1.store_code,t1.receiving_store,t1.province_origin,t1.originating_City,t1.originating_area,
        t1.originating_address,t1.destination_Province,t1.destination_city,t1.destination_area,t1.destination_address,
        t1.warehouse_code,t3.warehouse_name,t3.warehouse_city,t1.car_model,t1.delivery_mode,
        t1.split_total_number,t1.sku_number,t1.is_rejected,t1.reject_parties,line.total_mileage,line.store_num,t1.order_type,
        t1.pallet_number,t1.near_store_km,t1.skuString,store.isreject,t1.temperatureTypeStr as temperature_type
        ,t1.transport_type as transportType
        ,IFNULL(t1.cw_pallet_number,0) as cw_pallet_number ,IFNULL(t1.lc_pallet_number,0) as lc_pallet_number,IFNULL(t1.ld_pallet_number,0) as ld_pallet_number,t1.order_no
        from bms_ysbillcodeinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        left join bms_routeinfo line on t1.line_code=line.route_code and line.del_flag=0
        left join mdm_storeinfo store on t1.store_code=store.store_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0
        and t2.id = #{clientId} and t1.company_id = #{companyId} and t1.code_type = #{codeType}
        <if test="code != null ">and t1.relate_code =#{code}</if>
        <if test="param.dateType==1">
            and DATE_FORMAT(t1.signing_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.signing_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.dateType==2">
            and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.relateCodeList!=null">
            and t1.relate_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.clientName!=null and param.clientName!=''">and t2.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.receivingStore!=null and param.receivingStore!=''">and t1.receiving_store like concat('%', #{param.receivingStore}, '%')</if>
        <if test="param.warehouseName!=null and param.warehouseName!=''">and t3.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>

        -- 查询今天之前的数据
        and t1.signing_date &gt;= timestamp(date_add(curdate(), interval - 0 day))
        ORDER BY t2.id,t1.company_id,t1.relate_code asc
    </select>

    <select id="selectYSstockInfoForAfterCurDay" resultMap="AutoYSCodeInfoResult">
        select t1.id ,t2.id as client_id , t1.company_id,t2.payment_days,
        t1.stock_code as relate_code,t1.client_code,t1.client_name,t1.warehouse_code,
        t1.instorage_time as signing_date,t1.instorage_time as order_date,t1.total_boxes,t1.odd_boxes,t1.box_type,
        t1.trust,t1.warehouse_area,t1.weight as total_weight,t1.volume as total_volume,
        t1.warehouse_code,t3.warehouse_name,t3.warehouse_city,t1.temperature_type,t1.aqty,t1.pallet_number
        from bms_ysstockinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0
        and t2.id = #{clientId} and t1.company_id = #{companyId}
        and DATE_FORMAT(t1.instorage_time ,'%Y-%m-%d') &gt;= #{param.startDate}
        and DATE_FORMAT(t1.instorage_time ,'%Y-%m-%d') &lt;= #{param.endDate}

        <if test="code != null ">and t1.stock_code =#{code}</if>
        <if test="param.relateCodeList!=null">
            and t1.stock_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.clientName!=null and param.clientName!=''">and t2.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.warehouseName!=null and param.warehouseName!=''">and t3.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
        -- 查询今天之前的数据
        and t1.instorage_time &gt;= timestamp(date_add(curdate(), interval - 0 day))
    </select>

    <select id="selectYsGoodsGroupByYsbillId2" resultMap="AutoYSGoodsResult">
        select
            ysbill_id,
            sku_code,
            sku_class,
            sku_name,
            temperature_type,
            (odd_boxes+total_boxes) AS total_all_boxes,
            odd_boxes AS odd_boxes,
            total_boxes AS total_boxes,
            box_type AS box_type,
            contents_number AS contents_number,
            weight AS weight,
            volume AS volume,
            total_weight AS total_weight,
            total_volume AS total_volume,
            total_amount AS total_amount,
            price AS price
        from bms_ysbillcode_detailinfo
        where del_flag = 0 and ysbill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY sku_code
    </select>




    <select id="selYsbillCodeInfoByAuto" resultType="com.bbyb.joy.bms.domain.dto.charginglogic.AutoYsCodeInfo">
        select
            id,
            relate_code AS relateCode,
            total_boxes AS totalBoxes,
            total_number AS totalNumber,
            total_volume AS totalVolume,
            total_weight AS totalWeight
        from bms_ysbillcodeinfo
        where del_flag = '0'
            and code_type = #{optType}
            <if test="ids!=null and ids.size()>0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="relateCodes!=null and relateCodes.size()>0">
                and relate_code in
                <foreach collection="relateCodes" item="relateCode" open="(" separator="," close=")">
                    #{relateCode}
                </foreach>
            </if>
    </select>



    <select id="selYsStockCodeInfoByAutos" resultType="com.bbyb.joy.bms.domain.dto.charginglogic.AutoYsCodeInfo">
        select
            id,
            stock_code AS relateCode,
            total_boxes AS totalBoxes,
            weight AS totalWeight,
            volume AS totalVolume,
            trust AS totalNumber
        from bms_ysstockinfo
        where del_flag = '0'
            <if test="ids!=null and ids.size()>0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="relateCodes!=null and relateCodes.size()>0">
                and stock_code in
                <foreach collection="relateCodes" item="relateCode" open="(" separator="," close=")">
                    #{relateCode}
                </foreach>
            </if>
    </select>


</mapper>
