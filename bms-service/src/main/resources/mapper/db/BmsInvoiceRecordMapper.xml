<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsInvoiceRecordMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.pay.BmsInvoiceRecord" id="BmsInvoiceRecordResult">
        <result property="id"    column="id"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="collectionCode"    column="collection_code"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="collectionTime"    column="collection_time"    />
        <result property="billAmount"    column="bill_amount"    />
        <result property="amount"    column="amount"    />
        <result property="remark"    column="remark"    />
        <result property="billCode"    column="bill_code"    />
        <result property="billName"    column="bill_name"    />
        <result property="delFlag" column="del_flag" />
        <result property="createCode" column="create_code"/>
        <result property="createBy" column="create_by" />
        <result property="createDeptId" column="create_dept_id"/>
        <result property="createTime" column="create_time" />
        <result property="invoiceNumber" column="invoice_number" />
    </resultMap>

    <resultMap type="com.bbyb.joy.bms.domain.dto.pay.BmsInvoiceBillCodeVo" id="billMain">
        <result property="id"    column="id"    />
        <result property="billCode"    column="bill_code"    />
        <result property="billAmount" column="adjusted_amount"/>
        <result property="ticketState" column="ticket_state" />
        <result property="billState" column="bill_state" />
    </resultMap>
    <resultMap type="com.bbyb.joy.bms.domain.dto.pay.BmsInvoiceCarrierInfoVo" id="carrierMain">
        <result property="id"    column="id"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="paymentDays" column="payment_days" />
    </resultMap>

    <sql id="selectBmsInvoiceRecordVo">
       SELECT
	ir.id,
	ir.carrier_id,
	ir.carrier_name,
	ir.collection_code,
	ir.invoice_type, -- 发票类型
	ir.collection_time,
	ir.bill_amount,
	ir.amount,
	ir.remark,
	ir.del_flag,
	ir.invoice_number,
	ir.create_by,
	ir.create_time,
	ir.create_dept_id,
	yf.bill_code,
	yf.bill_name
FROM
	bms_invoice_record ir
	LEFT JOIN bms_yfbillmain yf ON yf.bill_code = ir.collection_code
    left join bms_carrierinfo ci on yf.carrier_id = ci.id and ci.del_flag = '0'
    </sql>

    <select id="selectBmsInvoiceRecordList" parameterType="com.bbyb.joy.bms.domain.dto.pay.BmsInvoiceRecord" resultMap="BmsInvoiceRecordResult">
        <include refid="selectBmsInvoiceRecordVo"/>
        <where>
            <if test="id != null "> and yf.id = #{id}</if>
            <if test="carrierList != null">
                and  ci.carrier_code in
                <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                    #{carrier}
                </foreach>
            </if>
            <if test="collectionCode != null  and collectionCode != ''"> and ir.collection_code = #{collectionCode}</if>
            and ir.del_flag=0
        </where>
    </select>

    <select id="selectYfCode" parameterType="java.util.Map" resultMap="billMain">
    select id,
    bill_code,
    adjusted_amount,  -- 调整后金额
    bill_state,
    ticket_state
    from bms_yfbillmain
    where bill_code in
    <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
    </foreach>
    and del_flag=0
</select>

    <select id="selectYfById" parameterType="java.util.Map" resultMap="billMain">
        select id,
        bill_code,
        bill_amount,
        bill_state,
        ticket_state
        from bms_yfbillmain
        where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and del_flag=0
    </select>

    <update id="updateYfbillmainByIds">
        UPDATE bms_yfbillmain t1
        LEFT JOIN bms_yfbillmain t2 ON t2.id = t1.id
        SET t1.bill_state =
            CASE
                WHEN t2.bill_state > 5
                THEN t2.bill_state
                ELSE 5
            END,
            t1.ticket_state = 2,
            t1.ticket_amount = #{amount},
            t1.ticket_time = #{ticketTime},
            t1.ticket_user_name = #{createBy},
            t1.ticket_num = COALESCE(t2.ticket_num, 0) +
            CASE
                WHEN #{InvoiceNumber} != null and #{InvoiceNumber} != ''THEN   #{InvoiceNumber}
                ELSE 1
            END
        WHERE t1.bill_code = #{id}
    </update>

    <update id="updateRollBackBill">
        update bms_yfbillmain set ticket_state =1,bill_state=3,ticket_amount=0,ticket_time=NULL,ticket_user_name=NULL,ticket_num=0 where bill_code in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectCarrier" parameterType="java.util.Map" resultMap="carrierMain">
        select id, carrier_name,carrier_code,accountperi as payment_days from bms_carrierinfo
        where carrier_name in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and del_flag=0
    </select>

    <select id="selectCarrierByCode" parameterType="java.util.Map" resultMap="carrierMain">
        select id, carrier_name,carrier_code,accountperi as payment_days from bms_carrierinfo
        where carrier_code in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and del_flag=0
    </select>

    <select id="selectClient" parameterType="java.util.Map" resultMap="carrierMain">
        select id, client_name,client_code,payment_days from bms_clientinfo
        where client_name in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and del_flag=0
    </select>

    <select id="selectClientByCode" parameterType="java.util.Map" resultMap="carrierMain">
        select id, client_name,client_code,payment_days from bms_clientinfo
        where client_code in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and del_flag=0
    </select>

    <insert id="insertBmsInvoiceRecord" parameterType="com.bbyb.joy.bms.domain.dto.pay.BmsInvoiceRecord" useGeneratedKeys="true" keyProperty="id">
        insert into bms_invoice_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="carrierId != null">carrier_id,</if>
            <if test="carrierName != null">carrier_name,</if>
            <if test="collectionCode != null">collection_code,</if>
            <if test="invoiceType != null">invoice_type,</if>
            <if test="collectionTime != null">collection_time,</if>
            <if test="billAmount != null">bill_amount,</if>
            <if test="amount != null">amount,</if>
            <if test="remark != null">remark,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="invoiceNumber!=null">invoice_number,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="carrierId != null">#{carrierId},</if>
            <if test="carrierName != null">#{carrierName},</if>
            <if test="collectionCode != null">#{collectionCode},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="collectionTime != null">#{collectionTime},</if>
            <if test="billAmount != null">#{billAmount},</if>
            <if test="amount != null">#{amount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="invoiceNumber!=null">#{invoiceNumber},</if>
         </trim>
    </insert>

    <update id="updateBmsInvoiceRecord" parameterType="com.bbyb.joy.bms.domain.dto.pay.BmsInvoiceRecord">
        update bms_invoice_record
        <trim prefix="SET" suffixOverrides=",">
<!--            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>-->
<!--            <if test="collectionTime != null">collection_time = #{collectionTime},</if>-->
<!--            <if test="billAmount != null">bill_amount = #{billAmount},</if>-->
<!--            <if test="amount != null">amount = #{amount},</if>-->
<!--            <if test="remark != null">remark = #{remark},</if>-->
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag!=null" >del_flag= #{delFlag} </if>
        </trim>
        where collection_code = #{collectionCode}
    </update>


    <update id="updateBmsInvoiceRecordStatusByIds">
        update bms_invoice_record set del_flag = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <resultMap type="com.bbyb.joy.bms.support.utils.bmsCarrierExistenceVo" id="bmsCarrierExistenceVos">
        <result property="carrierCode"    column="carrier_code"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName" column="client_name"/>
        <result property="carrierName" column="carrier_name"/>
    </resultMap>

    <select id="getCarrierCode" parameterType="java.util.Map" resultMap="bmsCarrierExistenceVos">
        SELECT
        c.carrier_code
        FROM
        <foreach collection="array" item="item" open="(" close=")" separator=" UNION ">
            select #{item} AS carrier_code
        </foreach>
        AS C
        LEFT JOIN bms_carrierinfo A ON A.carrier_code = C.carrier_code AND A.del_flag=0
        AND a.carrier_code IN
        <foreach collection="array" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        WHERE
        A.id IS NULL
    </select>

    <select id="getCarrierName" parameterType="java.util.Map" resultMap="bmsCarrierExistenceVos">
        SELECT
        c.carrier_name
        FROM
        <foreach collection="array" item="item" open="(" close=")" separator=" UNION ">
            select #{item} AS carrier_name
        </foreach>
        AS C
        LEFT JOIN bms_carrierinfo A ON A.carrier_name = C.carrier_name AND A.del_flag=0
        AND a.carrier_name IN
        <foreach collection="array" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        WHERE
        A.id IS NULL
    </select>

    <select id="getClientCode" parameterType="java.util.Map" resultMap="bmsCarrierExistenceVos">
        SELECT
        c.client_code
        FROM
        <foreach collection="array" item="item" open="(" close=")" separator=" UNION ">
            select #{item} AS client_code
        </foreach>
        AS C
        LEFT JOIN bms_clientinfo A ON A.client_code = C.client_code AND A.del_flag=0
        AND a.client_code IN
        <foreach collection="array" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        WHERE
        A.id IS NULL
    </select>

    <select id="getClientName" parameterType="java.util.Map" resultMap="bmsCarrierExistenceVos">
        SELECT
        c.client_name
        FROM
        <foreach collection="array" item="item" open="(" close=")" separator=" UNION ">
            select #{item} AS client_name
        </foreach>
        AS C
        LEFT JOIN bms_clientinfo A ON A.client_name = C.client_name AND A.del_flag=0
        AND a.client_name IN
        <foreach collection="array" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        WHERE
        A.id IS NULL
    </select>
</mapper>