<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.YFChargingBatchMapper">


    <!-- 批量新增应付费用主表  bms_yfcost_main_info  -->
    <insert id="insertBmsYfMainCostInfoBatch" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYfCostMainInfo" >
        insert into bms_yfcost_main_info(id,expenses_code,business_type,carrier_code,carrier_name,company_id,expenses_type,cost_dimension,charge_type,fee_flag,quoterule_id,rule_name,remarks,freight,delivery_fee,superframes_fee,excess_fee,reduce_fee,shortbarge_fee,return_fee,ultrafar_fee,outboundsorting_fee,exception_fee,adjust_fee,adjust_remark,other_cost1,other_cost2,other_cost3,other_cost4,other_cost5,other_cost6,other_cost7,other_cost8,other_cost9,other_cost10,other_cost11,other_cost12,sum_fee,cost_attribute,total_weight,total_volume,total_boxes,total_number,oper_code,oper_by,oper_time,del_flag,bill_id,bill_date,business_time,over_num,over_sendnum,storage_fee_price,disposal_fee_price,other_fee_remark,client_id,fee_type_first,fee_create_ate,is_increment,dispatch_date,finish_date,show_bill_code,extra_field1,warehouse_code_arr,settle_setting,create_code,create_by,create_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id},#{entity.expensesCode},#{entity.businessType},#{entity.carrierCode},#{entity.carrierName},#{entity.companyId},#{entity.expensesType},#{entity.costDimension},#{entity.chargeType},#{entity.feeFlag},#{entity.quoteruleId},#{entity.ruleName},#{entity.remarks},#{entity.freight},#{entity.deliveryFee},#{entity.superframesFee},#{entity.excessFee},#{entity.reduceFee},#{entity.shortbargeFee},#{entity.returnFee},#{entity.ultrafarFee},#{entity.outboundsortingFee},#{entity.exceptionFee},#{entity.adjustFee},#{entity.adjustRemark},#{entity.otherCost1},#{entity.otherCost2},#{entity.otherCost3},#{entity.otherCost4},#{entity.otherCost5},#{entity.otherCost6},#{entity.otherCost7},#{entity.otherCost8},#{entity.otherCost9},#{entity.otherCost10},#{entity.otherCost11},#{entity.otherCost12},#{entity.sumFee},#{entity.costAttribute},#{entity.totalWeight},#{entity.totalVolume},#{entity.totalBoxes},#{entity.totalNumber},#{entity.operCode},#{entity.operBy},#{entity.operTime},#{entity.delFlag},#{entity.billId},#{entity.billDate},#{entity.businessTime},#{entity.overNum},#{entity.overSendnum},#{entity.storageFeePrice},#{entity.disposalFeePrice},#{entity.otherFeeRemark},#{entity.clientId},#{entity.feeTypeFirst},#{entity.feeCreateAte},#{entity.isIncrement},#{entity.dispatchDate},#{entity.finishDate},#{entity.showBillCode},#{entity.extraField1},#{entity.warehouseCodeArr},#{entity.settleSetting},#{entity.createCode},#{entity.createBy},#{entity.createTime})
        </foreach>
    </insert>




    <!-- 批量新增应付费用表  bms_yfcost_info  -->
    <insert id="insertBmsYfCostInfoBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfcostInfo" >
        INSERT INTO bms_yfcost_info
        (
        id, expenses_code, business_type,carrier_code, carrier_name,company_id,
        expenses_type, cost_dimension, charge_type,fee_flag,quoterule_id,
        rule_name,remarks,adjust_remark,
        freight,Ultrafar_fee,superframes_fee,excess_fee,reduce_fee,delivery_fee,
        outboundsorting_fee,shortbarge_fee,return_fee,exception_fee,
        other_cost1,other_cost2,other_cost3,other_cost4,other_cost5,other_cost6,
        other_cost7,other_cost8,other_cost9,other_cost10,other_cost11,other_cost12,
        oper_code,oper_by,oper_time,del_flag,bill_date,business_time,cost_attribute,
        over_num,over_sendnum,bill_id,storage_fee_price,disposal_fee_price,client_id,dispatch_date,finish_date
        ,warehouse_code_arr,settle_type,settle_amount,main_expense_id,settle_main_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.expensesCode}, #{item.businessType}, #{item.carrierCode},#{item.carrierName}, #{item.companyId},
            #{item.expensesType}, #{item.costDimension}, #{item.chargeType}, #{item.feeFlag}, #{item.quoteruleId},
            #{item.ruleName}, #{item.remarks},#{item.adjustRemark},
            #{item.freight},#{item.ultrafarFee},#{item.superframesFee},#{item.excessFee}, #{item.reduceFee},#{item.deliveryFee},
            #{item.outboundsortingFee}, #{item.shortbargeFee},#{item.returnFee},#{item.exceptionFee},
            #{item.otherCost1},#{item.otherCost2},#{item.otherCost3},#{item.otherCost4},#{item.otherCost5},#{item.otherCost6},
            #{item.otherCost7},#{item.otherCost8},#{item.otherCost9},#{item.otherCost10},#{item.otherCost11},#{item.otherCost12},
            #{item.operCode}, #{item.operBy}, #{item.operTime}, 0,#{item.billDate},#{item.businessTime},#{item.costAttribute},
            #{item.overNum}, #{item.overSendnum}, #{item.billId}, #{item.storageFeePrice}, #{item.disposalFeePrice}, #{item.clientId}
            ,#{item.dispatchDate},#{item.finishDate},#{item.warehouseCodeArr},#{item.settleType},#{item.settleAmount},#{item.mainExpenseId},#{item.settleMainId}
            )
        </foreach>
    </insert>
    <!-- 批量作废  bms_yfcost_info  -->
    <update id="delBmsYfCostInfoByIds">
        update bms_yfcost_info set del_flag = 1,oper_time=now(),oper_by=#{operBy} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量新增应收费用拓展表  bms_yfcost_extend  -->
    <insert id="insertBmsYfCostExtendBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfcostExtend" >
        INSERT INTO bms_yfcost_extend
        (
        expenses_id, business_code,warehouse_code, warehouse_name,total_boxes,
        total_number,total_weight,total_volume,cargo_value,del_flag,automatic_billing_remark,code_count,
        tline_code,tline_name,line_code,dispatch_date,finish_date,quotation_information,main_expense_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.expensesId}, #{item.businessCode},#{item.warehouseCode}, #{item.warehouseName}, #{item.totalBoxes},
            #{item.totalNumber}, #{item.totalWeight}, #{item.totalVolume}, #{item.cargoValue}, 0, #{item.automaticBillingRemark}, #{item.codeCount},
            #{item.tlineCode}, #{item.tlineName},#{item.lineCode}, #{item.dispatchDate},#{item.finishDate},#{item.quotationInformation},
            #{item.mainExpenseId}
            )
        </foreach>
    </insert>


    <!-- 批量新增应收费用拓展表  bms_yfexpenses_middle_share  -->
    <insert id="insertBmsYfCostMiddleShareBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfexpensesMiddleShare" >
        INSERT INTO bms_yfexpenses_middle_share(yfbill_id,yfbill_type,expenses_id,main_expense_id,oper_by,oper_time,del_flag,share_type,share_amount,freight,delivery_fee,superframes_fee,excess_fee,reduce_fee,shortbarge_fee,return_fee,ultrafar_fee,outboundsorting_fee,exception_fee,other_cost1,other_cost2,other_cost3,other_cost4,other_cost5,other_cost6,other_cost7,other_cost8,other_cost9,other_cost10,other_cost11,other_cost12)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.yfbillId},#{item.yfbillType},#{item.expensesId},#{item.mainExpenseId},#{item.operBy},#{item.operTime},#{item.delFlag},#{item.shareType},#{item.shareAmount},#{item.freight},#{item.deliveryFee},#{item.superframesFee},#{item.excessFee},#{item.reduceFee},#{item.shortbargeFee},#{item.returnFee},#{item.ultrafarFee},#{item.outboundsortingFee},#{item.exceptionFee},#{item.otherCost1},#{item.otherCost2},#{item.otherCost3},#{item.otherCost4},#{item.otherCost5},#{item.otherCost6},#{item.otherCost7},#{item.otherCost8},#{item.otherCost9},#{item.otherCost10},#{item.otherCost11},#{item.otherCost12})
        </foreach>
    </insert>


    <!-- 批量作废  bms_yfcost_extend  -->
    <update id="delBmsYfCostExtendByExpensesIds">
        update bms_yfcost_extend set del_flag = 1 where expenses_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量新增应收费用中间表  bms_yfexpenses_middle  -->
    <insert id="insertBmsYfExpensesMiddleBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfexpensesMiddle" >
        INSERT INTO bms_yfexpenses_middle
        (
        yfbill_id, yfbill_type,expenses_id, oper_by,oper_time, del_flag, share_type,
        share_amount, freight, delivery_fee, ultrafar_fee, superframes_fee, excess_fee, reduce_fee,
        outboundsorting_fee, shortbarge_fee, return_fee, exception_fee, other_cost1, other_cost2,
        other_cost3, other_cost4, other_cost5, other_cost6, other_cost7, other_cost8, other_cost9,
        other_cost10, other_cost11, other_cost12, main_expense_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.yfbillId}, #{item.yfbillType},#{item.expensesId}, #{item.operBy}, #{item.operTime}, 0, #{item.shareType,jdbcType=INTEGER}, #{item.shareAmount,jdbcType=DECIMAL},
            #{item.freight,jdbcType=DECIMAL}, #{item.deliveryFee,jdbcType=DECIMAL}, #{item.ultrafarFee,jdbcType=DECIMAL},
            #{item.superframesFee,jdbcType=DECIMAL}, #{item.excessFee,jdbcType=DECIMAL}, #{item.reduceFee,jdbcType=DECIMAL},
            #{item.outboundsortingFee,jdbcType=DECIMAL}, #{item.shortbargeFee,jdbcType=DECIMAL}, #{item.returnFee,jdbcType=DECIMAL},
            #{item.exceptionFee,jdbcType=DECIMAL}, #{item.otherCost1,jdbcType=DECIMAL}, #{item.otherCost2,jdbcType=DECIMAL},
            #{item.otherCost3,jdbcType=DECIMAL}, #{item.otherCost4,jdbcType=DECIMAL}, #{item.otherCost5,jdbcType=DECIMAL},
            #{item.otherCost6,jdbcType=DECIMAL}, #{item.otherCost7,jdbcType=DECIMAL}, #{item.otherCost8,jdbcType=DECIMAL},
            #{item.otherCost9,jdbcType=DECIMAL}, #{item.otherCost10,jdbcType=DECIMAL}, #{item.otherCost11,jdbcType=DECIMAL},
            #{item.otherCost12,jdbcType=DECIMAL}, #{item.mainExpenseId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <!-- 批量作废  bms_yfexpenses_middle  -->
    <update id="delBmsYfExpensesMiddleByExpensesIds">
        update bms_yfexpenses_middle set del_flag = 1,oper_time=now(),oper_by=#{operBy} where expenses_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <!-- 批量修改 单据计费状态  -->
    <!--  `cost_status` '计费状态0未计费1已计费',-->
    <update id="updateBmsYfBillCodeInfoStatusByIds">
        update bms_yfbillcodeinfo set cost_status = #{status},fail_remark=null where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <!-- 批量修改 单据计费状态 库存表 -->
    <!--  `cost_status` '计费状态0未计费1已计费',-->
    <update id="updateBmsYfStockInfoStatusByIds">
        update bms_yfstockinfo set cost_status = #{status},fail_remark=null where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <!-- 批量修改 单据计费状态 出入库单表 -->
    <!--  `cost_status` '计费状态0未计费1已计费',-->
    <update id="updateBmsYfStockCodeStatusByIds">
        update bms_yfstock_codeinfo set cost_status = #{status},fail_remark=null where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <!--    查询单据信息-->
    <resultMap type="com.bbyb.joy.bms.domain.dto.charginglogic.YfBillCodeInfo" id="YfbillcodeInfoResult">
        <result property="id"    column="id"    />
        <result property="relateCode"    column="relate_code"    />
        <result property="paymentDays"    column="payment_days"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="businessTime"    column="business_time"    />
        <result property="companyId"    column="company_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="totalNumber"    column="total_number"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="cargoValue"    column="cargo_value"    />
        <result property="clientId"    column="client_id"    />

        <result property="dispatchDate"    column="dispatch_date"    />
        <result property="finishDate"    column="finish_date"    />
        <result property="tlineCode"    column="tline_code"    />
        <result property="tlineName"    column="tline_name"    />
        <result property="lineCode"    column="line_Code"    />
    </resultMap>
    <!--    单据表-->
    <select id="selectYfBillCodeInfoList"  resultMap="YfbillcodeInfoResult">
        select t1.id,t1.scheduling_bill_code relate_code,t2.accountperi payment_days,t2.id carrier_id,
        t1.dispatch_date,t1.finish_date,t1.tline_code,t1.tline_name,t1.line_Code,
        case when setting.date_type=1 then t1.dispatch_date else  t1.finish_date end  business_time,
        t1.company_id,t2.carrier_code,t2.carrier_name,t1.total_boxes,t1.total_number,t1.total_weight,t1.total_volume,t1.cargo_value,t1.client_id
        from bms_yfbillcodeinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join pub_settledate_setting setting on setting.client_id=t1.client_id and bill_type=2
        <where>
            t1.del_flag=0 and t1.id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>
    <!--    库存表-->
    <select id="selectYfStorageList"  resultMap="YfbillcodeInfoResult">
        select t1.id,t1.stock_code as relate_code,t2.accountperi,t2.id carrier_id,t1.instorage_time as business_time,t1.company_id,
        t1.instorage_time as dispatch_date,t1.instorage_time as finish_date,
        t2.carrier_code,t2.carrier_name,t1.warehouse_code,t1.total_boxes,t1.weight as total_weight,t1.volume as total_volume,t3.warehouse_name,t1.client_id
        ,t1.trust as total_number
        from bms_yfstockinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        <where>
            t1.del_flag=0 and t1.id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>
    <!--    查询费用单客户对账日(费用单)-->
    <select id="selectYfCostPaymentDaysList"  resultMap="YfbillcodeInfoResult">
        select t1.id,t2.id carrier_id,t1.business_time,t2.carrier_code,t2.carrier_name,t2.accountperi payment_days
        from bms_yfcost_info t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        <where>
            t1.del_flag=0 and t1.id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>


    <!--    出入库单-->
    <select id="selectYfStockCodeList"  resultMap="YfbillcodeInfoResult">
        select t1.id,t1.relate_code,t1.code_type,t2.accountperi payment_days,t2.id carrier_id,t1.stockoper_time as business_time,t1.company_id,
        t2.carrier_code,t2.carrier_name,t1.warehouse_code,t1.warehouse_name,t1.total_boxes,t1.weight as total_weight,t1.volume as total_volume,t1.client_id
        from bms_yfstock_codeinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        <where>
            t1.del_flag=0 and t1.id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <!--    取消计费-查询单据是否有其他计费-->
    <select id="selectCancelOrderBillId" resultType="java.lang.String">
        select yfbill_id from bms_yfexpenses_middle
        <where>
            del_flag=0 and yfbill_type=#{yfbilType}

            <if test="null != yfcostIds and yfcostIds.size > 0">
                and expenses_id not in
                <foreach item="yfcostid" collection="yfcostIds" open="(" separator="," close=")">
                    #{yfcostid}
                </foreach>
            </if>

            <if test="null != yfbillIds and yfbillIds.size > 0">
                and yfbill_id in
                <foreach item="yfbillId" collection="yfbillIds" open="(" separator="," close=")">
                    #{yfbillId}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateYfBillCostStatus">
        <if test="ids!=null and ids.size()>0">
            update bms_yfbillcodeinfo set cost_status = 0,fail_remark = null
            <where>
                del_flag=0 and id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </where>
            ;
        </if>

        <if test="codes!=null and codes.size()>0">
            update bms_yfstock_codeinfo set cost_status = 0,fail_remark = null
            <where>
                del_flag=0 and relate_code in
                <foreach item="id" collection="codes" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </where>
            ;
            update bms_yfstockinfo set cost_status = 0,fail_remark = null
            <where>
                del_flag=0 and stock_code in
                <foreach item="id" collection="codes" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </where>
        </if>

    </update>


    <update id="updateYfBillCostDelete">
        update bms_yfexpenses_middle_share set del_flag = 1 where main_expense_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yfexpenses_middle set del_flag = 1 where main_expense_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yfcost_info set del_flag = 1 where main_expense_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yfcost_extend set del_flag = 1 where main_expense_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yfcost_main_info set del_flag = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;

    </update>

    <update id="updateBmsAddedFeeCost">
        <if test="list!=null and list.size()>0">
            <foreach collection="list" item="item">
                update bms_addedfee set
                charging_date = now(),
                <if test="item.itemId !=null">
                    item_id = #{item.itemId},
                </if>
                <if test="item.amount !=null">
                    amount = #{item.amount},
                </if>
                <if test="item.remark !=null">
                    remark = #{item.remark},
                </if>
                creat_date = now(),
                cost_status = 1,
                fee_source=1
                where id = #{item.id}
                ;
            </foreach>
        </if>

    </update>

    <update id="modifyCommonBatchCodeFailRemark">
        update bms_yfbillcodeinfo
        set
        fail_remark=#{autoYfCodeInfo.failRemark},
        oper_time=now()
        where del_flag = '0'
        and id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="modifyBatchCodeFailRemark">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update bms_yfbillcodeinfo
            set
            fail_remark=#{item.failRemark},
            oper_time=now()
            where del_flag = '0'
            and id = #{item.id}
        </foreach>
    </update>

    <update id="modifyCommonBatchStockCodeFailRemark">
        update bms_yfstockinfo
        set
        fail_remark=#{autoYfCodeInfo.failRemark},
        oper_time=now()
        where del_flag = '0'
        and id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="modifyBatchStockCodeFailRemark">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update bms_yfstockinfo
            set
            fail_remark=#{item.failRemark},
            oper_time=now()
            where del_flag = '0'
            and id = #{item.id}
        </foreach>
    </update>

    <update id="modifyCommonBatchInOutCodeFailRemark">
        update bms_yfstock_codeinfo
        set
        fail_remark=#{autoYfCodeInfo.failRemark},
        oper_time=now()
        where del_flag = '0'
        and id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="modifyBatchInOutCodeFailRemark">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update bms_yfstock_codeinfo
            set
            fail_remark=#{item.failRemark},
            oper_time=now()
            where del_flag = '0'
            and id = #{item.id}
        </foreach>
    </update>



    <select id="selectYfCostMainByCond" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfCostMainInfo">
        select t1.id,
               t1.pk_id AS pkId,
               t1.expenses_code AS expensesCode,
               t1.business_type AS businessType,
               t1.carrier_code AS carrierCode,
               t1.carrier_name AS carrierName,
               t1.company_id AS companyId,
               t1.expenses_type AS expensesType,
               t1.cost_dimension AS costDimension,
               t1.charge_type AS chargeType,
               t1.fee_flag AS feeFlag,
               t1.quoterule_id AS quoteruleId,
               t1.rule_name AS ruleName,
               t1.remarks,
               t1.freight,
               t1.delivery_fee AS deliveryFee,
               t1.superframes_fee AS superframesFee,
               t1.excess_fee AS excessFee,
               t1.reduce_fee AS reduceFee,
               t1.shortbarge_fee AS shortbargeFee,
               t1.return_fee AS returnFee,
               t1.ultrafar_fee AS ultrafarFee,
               t1.outboundsorting_fee AS outboundsortingFee,
               t1.exception_fee AS exceptionFee,
               t1.adjust_fee AS adjustFee,
               t1.adjust_remark AS adjustRemark,
               t1.other_cost1 AS other_cost1,
               t1.other_cost2 AS other_cost2,
               t1.other_cost3 AS other_cost3,
               t1.other_cost4 AS other_cost4,
               t1.other_cost5 AS other_cost5,
               t1.other_cost6 AS other_cost6,
               t1.other_cost7 AS other_cost7,
               t1.other_cost8 AS other_cost8,
               t1.other_cost9 AS other_cost9,
               t1.other_cost10 AS other_cost10,
               t1.other_cost11 AS other_cost11,
               t1.other_cost12 AS other_cost12,
               t1.sum_fee AS sumFee,
               t1.cost_attribute AS costAttribute,
               t1.total_weight AS totalWeight,
               t1.total_volume AS totalVolume,
               t1.total_boxes AS totalBoxes,
               t1.total_number AS totalNumber,
               t1.oper_code AS operCode,
               t1.oper_by AS operBy,
               t1.oper_time AS operTime,
               t1.del_flag AS delFlag,
               t1.bill_id AS billId,
               t1.bill_date AS billDate,
               t1.business_time AS businessTime,
               t1.over_num AS overNum,
               t1.over_sendnum AS overSendnum,
               t1.storage_fee_price AS storageFeePrice,
               t1.disposal_fee_price AS disposalFeePrice,
               t1.other_fee_remark AS otherFeeRemark,
               t1.client_id AS clientId,
               t1.fee_type_first AS feeTypeFirst,
               t1.fee_create_ate AS feeCreateAte,
               t1.is_increment AS isIncrement,
               t1.dispatch_date AS dispatchDate,
               t1.finish_date AS finishDate,
               t1.show_bill_code AS showBillCode,
               t1.extra_field1 AS extraField1,
               t1.warehouse_code_arr AS warehouseCodeArr,
               t1.settle_setting AS settleSetting,
               t1.create_code AS createCode,
               t1.create_by AS createBy,
               t1.create_time AS createTime,
               IFNULL(t2.share_type,1) AS shareType
        from bms_yfcost_main_info t1
        left join bms_carrierinfo t2 on t2.carrier_code = t1.carrier_code and t2.del_flag = 0
        where t1.del_flag = '0'
        and t1.id in
        <foreach collection="mainCostIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>



    <update id="flushYfMiddleShare">
        <if test="list!=null and list.size()>0">
            <foreach collection="list" item="item">
                update bms_yfexpenses_middle_share
                <set>
                    <if test="item.operBy != null and item.operBy != ''">
                        oper_by = #{item.operBy},
                    </if>
                    <if test="item.operTime != null">
                        oper_time = #{item.operTime},
                    </if>
                    <if test="item.shareAmount != null">
                        share_amount = #{item.shareAmount},
                    </if>
                    <if test="item.freight != null">
                        freight = #{item.freight},
                    </if>
                    <if test="item.deliveryFee != null">
                        delivery_fee = #{item.deliveryFee},
                    </if>
                    <if test="item.superframesFee != null">
                        superframes_fee = #{item.superframesFee},
                    </if>
                    <if test="item.excessFee != null">
                        excess_fee = #{item.excessFee},
                    </if>
                    <if test="item.reduceFee != null">
                        reduce_fee = #{item.reduceFee},
                    </if>
                    <if test="item.shortbargeFee != null">
                        shortbarge_fee = #{item.shortbargeFee},
                    </if>
                    <if test="item.returnFee != null">
                        return_fee = #{item.returnFee},
                    </if>
                    <if test="item.ultrafarFee != null">
                        ultrafar_fee = #{item.ultrafarFee},
                    </if>
                    <if test="item.outboundsortingFee != null">
                        outboundsorting_fee = #{item.outboundsortingFee},
                    </if>
                    <if test="item.exceptionFee != null">
                        exception_fee = #{item.exceptionFee},
                    </if>
                    <if test="item.otherCost1 != null">
                        other_cost1 = #{item.otherCost1},
                    </if>
                    <if test="item.otherCost2 != null">
                        other_cost2 = #{item.otherCost2},
                    </if>
                    <if test="item.otherCost3 != null">
                        other_cost3 = #{item.otherCost3},
                    </if>
                    <if test="item.otherCost4 != null">
                        other_cost4 = #{item.otherCost4},
                    </if>
                    <if test="item.otherCost5 != null">
                        other_cost5 = #{item.otherCost5},
                    </if>
                    <if test="item.otherCost6 != null">
                        other_cost6 = #{item.otherCost6},
                    </if>
                    <if test="item.otherCost7 != null">
                        other_cost7 = #{item.otherCost7},
                    </if>
                    <if test="item.otherCost8 != null">
                        other_cost8 = #{item.otherCost8},
                    </if>
                    <if test="item.otherCost9 != null">
                        other_cost9 = #{item.otherCost9},
                    </if>
                    <if test="item.otherCost10 != null">
                        other_cost10 = #{item.otherCost10},
                    </if>
                    <if test="item.otherCost11 != null">
                        other_cost11 = #{item.otherCost11},
                    </if>
                    <if test="item.otherCost12 != null">
                        other_cost12 = #{item.otherCost12},
                    </if>
                </set>
                where id = #{item.id}
            </foreach>
        </if>
    </update>


    <update id="flushBmsYfFee">
        update bms_yfcost_info
        <set>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            <if test="freight != null">
                freight = #{freight},
            </if>
            <if test="deliveryFee != null">
                delivery_fee = #{deliveryFee},
            </if>
            <if test="superframesFee != null">
                superframes_fee = #{superframesFee},
            </if>
            <if test="excessFee != null">
                excess_fee = #{excessFee},
            </if>
            <if test="reduceFee != null">
                reduce_fee = #{reduceFee},
            </if>
            <if test="shortbargeFee != null">
                shortbarge_fee = #{shortbargeFee},
            </if>
            <if test="returnFee != null">
                return_fee = #{returnFee},
            </if>
            <if test="ultrafarFee != null">
                ultrafar_fee = #{ultrafarFee},
            </if>
            <if test="outboundsortingFee != null">
                outboundsorting_fee = #{outboundsortingFee},
            </if>
            <if test="exceptionFee != null">
                exception_fee = #{exceptionFee},
            </if>
            <if test="adjustFee != null">
                adjust_fee = #{adjustFee},
            </if>
            <if test="adjustRemark != null and adjustRemark != ''">
                adjust_remark = #{adjustRemark},
            </if>
            <if test="otherCost1 != null">
                other_cost1 = #{otherCost1},
            </if>
            <if test="otherCost2 != null">
                other_cost2 = #{otherCost2},
            </if>
            <if test="otherCost3 != null">
                other_cost3 = #{otherCost3},
            </if>
            <if test="otherCost4 != null">
                other_cost4 = #{otherCost4},
            </if>
            <if test="otherCost5 != null">
                other_cost5 = #{otherCost5},
            </if>
            <if test="otherCost6 != null">
                other_cost6 = #{otherCost6},
            </if>
            <if test="otherCost7 != null">
                other_cost7 = #{otherCost7},
            </if>
            <if test="otherCost8 != null">
                other_cost8 = #{otherCost8},
            </if>
            <if test="otherCost9 != null">
                other_cost9 = #{otherCost9},
            </if>
            <if test="otherCost10 != null">
                other_cost10 = #{otherCost10},
            </if>
            <if test="otherCost11 != null">
                other_cost11 = #{otherCost11},
            </if>
            <if test="otherCost12 != null">
                other_cost12 = #{otherCost12},
            </if>
            <if test="operCode != null and operCode != ''">
                oper_code = #{operCode},
            </if>
            <if test="operBy != null and operBy != ''">
                oper_by = #{operBy},
            </if>
            <if test="storageFeePrice != null">
                storage_fee_price = #{storageFeePrice},
            </if>
            <if test="disposalFeePrice != null">
                disposal_fee_price = #{disposalFeePrice},
            </if>
            <if test="otherFeeRemark != null and otherFeeRemark != ''">
                other_fee_remark = #{otherFeeRemark},
            </if>
            <if test="settleType != null">
                settle_type = #{settleType},
            </if>
            <if test="settleAmount != null">
                settle_amount = #{settleAmount},
            </if>
            <if test="billDate != null and billDate != ''">
                bill_date = #{billDate}
            </if>
        </set>
        where id = #{id};


        UPDATE bms_yfcost_info t1
        LEFT JOIN (
            SELECT
                id,
                main_expense_id,
                IFNULL(freight,0) AS freight,
                IFNULL(delivery_fee,0) AS deliveryFee,
                IFNULL(superframes_fee,0) AS superframesFee,
                IFNULL(excess_fee,0) AS excessFee,
                IFNULL(reduce_fee,0) AS reduceFee,
                IFNULL(shortbarge_fee,0) AS shortbargeFee,
                IFNULL(return_fee,0) AS returnFee,
                IFNULL(ultrafar_fee,0) AS ultrafarFee,
                IFNULL(outboundsorting_fee,0) AS outboundsortingFee,
                IFNULL(exception_fee,0) AS exceptionFee,
                IFNULL(other_cost1,0) AS otherCost1,
                IFNULL(other_cost2,0) AS otherCost2,
                IFNULL(other_cost3,0) AS otherCost3,
                IFNULL(other_cost4,0) AS otherCost4,
                IFNULL(other_cost5,0) AS otherCost5,
                IFNULL(other_cost6,0) AS otherCost6,
                IFNULL(other_cost7,0) AS otherCost7,
                IFNULL(other_cost8,0) AS otherCost8,
                IFNULL(other_cost9,0) AS otherCost9,
                IFNULL(other_cost10,0) AS otherCost10,
                IFNULL(other_cost11,0) AS otherCost11,
                IFNULL(other_cost12,0) AS otherCost12,
                IFNULL(freight,0)
                +IFNULL(delivery_fee,0)
                +IFNULL(superframes_fee,0)
                +IFNULL(excess_fee,0)
                +IFNULL(reduce_fee,0)
                +IFNULL(shortbarge_fee,0)
                +IFNULL(return_fee,0)
                +IFNULL(ultrafar_fee,0)
                +IFNULL(outboundsorting_fee,0)
                +IFNULL(exception_fee,0)
                +IFNULL(other_cost1,0)
                +IFNULL(other_cost2,0)
                +IFNULL(other_cost3,0)
                +IFNULL(other_cost4,0)
                +IFNULL(other_cost5,0)
                +IFNULL(other_cost6,0)
                +IFNULL(other_cost7,0)
                +IFNULL(other_cost8,0)
                +IFNULL(other_cost9,0)
                +IFNULL(other_cost10,0)
                +IFNULL(other_cost11,0)
                +IFNULL(other_cost12,0) AS settle_amount
            FROM bms_yfcost_info
            WHERE id = #{id}
        ) t2 ON t2.id = t1.id
        SET t1.settle_amount = t2.settle_amount
        WHERE t1.id = #{id};
        UPDATE bms_yfcost_main_info t1
        LEFT JOIN (
            SELECT
                main_expense_id,
                IFNULL(SUM(freight),0) AS freight,
                IFNULL(SUM(delivery_fee),0) AS delivery_fee,
                IFNULL(SUM(superframes_fee),0) AS superframes_fee,
                IFNULL(SUM(excess_fee),0) AS excess_fee,
                IFNULL(SUM(reduce_fee),0) AS reduce_fee,
                IFNULL(SUM(shortbarge_fee),0) AS shortbarge_fee,
                IFNULL(SUM(return_fee),0) AS return_fee,
                IFNULL(SUM(ultrafar_fee),0) AS ultrafar_fee,
                IFNULL(SUM(outboundsorting_fee),0) AS outboundsorting_fee,
                IFNULL(SUM(exception_fee),0) AS exception_fee,
                IFNULL(SUM(other_cost1),0) AS other_cost1,
                IFNULL(SUM(other_cost2),0) AS other_cost2,
                IFNULL(SUM(other_cost3),0) AS other_cost3,
                IFNULL(SUM(other_cost4),0) AS other_cost4,
                IFNULL(SUM(other_cost5),0) AS other_cost5,
                IFNULL(SUM(other_cost6),0) AS other_cost6,
                IFNULL(SUM(other_cost7),0) AS other_cost7,
                IFNULL(SUM(other_cost8),0) AS other_cost8,
                IFNULL(SUM(other_cost9),0) AS other_cost9,
                IFNULL(SUM(other_cost10),0) AS other_cost10,
                IFNULL(SUM(other_cost11),0) AS other_cost11,
                IFNULL(SUM(other_cost12),0) AS other_cost12,
                IFNULL(SUM(adjust_fee),0) AS adjust_fee,
                IFNULL(SUM(freight),0)
                +IFNULL(SUM(delivery_fee),0)
                +IFNULL(SUM(superframes_fee),0)
                +IFNULL(SUM(excess_fee),0)
                +IFNULL(SUM(reduce_fee),0)
                +IFNULL(SUM(shortbarge_fee),0)
                +IFNULL(SUM(return_fee),0)
                +IFNULL(SUM(ultrafar_fee),0)
                +IFNULL(SUM(outboundsorting_fee),0)
                +IFNULL(SUM(exception_fee),0)
                +IFNULL(SUM(other_cost1),0)
                +IFNULL(SUM(other_cost2),0)
                +IFNULL(SUM(other_cost3),0)
                +IFNULL(SUM(other_cost4),0)
                +IFNULL(SUM(other_cost5),0)
                +IFNULL(SUM(other_cost6),0)
                +IFNULL(SUM(other_cost7),0)
                +IFNULL(SUM(other_cost8),0)
                +IFNULL(SUM(other_cost9),0)
                +IFNULL(SUM(other_cost10),0)
                +IFNULL(SUM(other_cost11),0)
                +IFNULL(SUM(other_cost12),0) AS settle_amount
            FROM bms_yfcost_info
            WHERE del_flag = 0 AND main_expense_id = #{mainExpenseId}
            GROUP BY main_expense_id
        ) t2 ON t2.main_expense_id = t1.id
        SET
            t1.freight = t2.freight
            ,t1.delivery_fee = t2.delivery_fee
            ,t1.superframes_fee = t2.superframes_fee
            ,t1.excess_fee = t2.excess_fee
            ,t1.reduce_fee = t2.reduce_fee
            ,t1.shortbarge_fee = t2.shortbarge_fee
            ,t1.return_fee = t2.return_fee
            ,t1.ultrafar_fee = t2.ultrafar_fee
            ,t1.outboundsorting_fee = t2.outboundsorting_fee
            ,t1.exception_fee = t2.exception_fee
            ,t1.adjust_fee = t2.adjust_fee
            ,t1.other_cost1 = t2.other_cost1
            ,t1.other_cost2 = t2.other_cost2
            ,t1.other_cost3 = t2.other_cost3
            ,t1.other_cost4 = t2.other_cost4
            ,t1.other_cost5 = t2.other_cost5
            ,t1.other_cost6 = t2.other_cost6
            ,t1.other_cost7 = t2.other_cost7
            ,t1.other_cost8 = t2.other_cost8
            ,t1.other_cost9 = t2.other_cost9
            ,t1.other_cost10 = t2.other_cost10
            ,t1.other_cost11 = t2.other_cost11
            ,t1.other_cost12 = t2.other_cost12
            ,t1.sum_fee = t2.settle_amount
        WHERE t1.id = #{mainExpenseId};
    </update>



    <update id="delBmsYfFee">
        update bms_yfcost_info
        set bill_id = null
          ,show_bill_code = null
        where id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")" >
            #{item.id}
        </foreach>
    </update>



</mapper>