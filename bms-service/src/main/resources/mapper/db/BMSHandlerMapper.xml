<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BMSHandlerMapper">


    <select id="selectByCostInfos" resultType="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle">
        <if test="ysbillType==1">
            SELECT
            t1.id,
            t1.ysbill_id AS ysbillId,
            t1.ysbil_type AS ysbilType,
            t1.expenses_id AS expensesId,
            t1.main_expense_id AS mainExpenseId,
            t2.bill_id AS billId,
            t2.expenses_code AS expensesCode,
            t3.expenses_code AS mainExpenseCode
            FROM bms_ysexpenses_middle t1
            LEFT JOIN bms_yscost_info t2 ON t2.id = t1.expenses_id AND t2.del_flag='0'
            LEFT JOIN bms_yscost_main_info t3 ON t3.id = t1.main_expense_id AND t3.del_flag='0'
            LEFT JOIN bms_ysbillcodeinfo t4 ON t4.id = t1.ysbill_id AND t4.del_flag='0'
            WHERE t1.del_flag='0'
            AND t1.ysbil_type = 1
            AND t1.ysbill_id IN
            <foreach item="ysbillId" collection="ysbillIds" open="(" separator="," close=")">
                #{ysbillId}
            </foreach>
        </if>
        <if test="ysbillType==2">
            SELECT
            t1.id,
            t1.ysbill_id AS ysbillId,
            t1.ysbil_type AS ysbilType,
            t1.expenses_id AS expensesId,
            t1.main_expense_id AS mainExpenseId,
            t2.bill_id AS billId,
            t2.expenses_code AS expensesCode,
            t3.expenses_code AS mainExpenseCode
            FROM bms_ysexpenses_middle t1
            LEFT JOIN bms_yscost_info t2 ON t2.id = t1.expenses_id AND t2.del_flag='0'
            LEFT JOIN bms_yscost_main_info t3 ON t3.id = t1.main_expense_id AND t3.del_flag='0'
            LEFT JOIN bms_ysstockinfo t4 ON t4.id = t1.ysbill_id AND t4.del_flag='0'
            WHERE t1.del_flag='0'
            AND t1.ysbil_type = 2
            AND t1.ysbill_id IN
            <foreach item="ysbillId" collection="ysbillIds" open="(" separator="," close=")">
                #{ysbillId}
            </foreach>
        </if>
    </select>

    <!--要按照顺序进行作废 拓展表-中间表-费用明细表 -->
    <update id="cancelFeeDetail">
        UPDATE bms_ysexpenses_middle
        SET del_flag = 1
        WHERE main_pk_id = #{id};
        UPDATE bms_yscost_info
        SET del_flag = 1,
            oper_by = #{operBy},
            oper_code = #{operCode},
            oper_time = NOW()
        WHERE main_pk_id = #{id};
    </update>



    <update id="editCodeCostStatus">
        <if test="codeType != null and codeType == 1">
            UPDATE bms_trans_code_info
                SET cost_status = 1
                ,oper_code = #{operCode}
                ,oper_by = #{operBy}
                ,oper_time=NOW()
            WHERE pk_id IN
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="codeType != null and codeType == 2">
            <if test="costMode !=null and costMode == 1">
                UPDATE bms_storage_code_info
                SET ys_cost_status = 1
                    ,oper_code = #{operCode}
                    ,oper_by = #{operBy}
                    ,oper_time=NOW()
                WHERE pk_id IN
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="costMode !=null and costMode == 2">
                UPDATE bms_storage_code_info
                SET yf_cost_status = 1
                    ,oper_code = #{operCode}
                    ,oper_by = #{operBy}
                    ,oper_time=NOW()
                WHERE pk_id IN
                    <foreach collection="ids" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
            </if>
        </if>
    </update>




    <!--    应收账单-运输对账-更新主费用信息-->
    <update id="reconciliationForYsBindFee">
        UPDATE bms_yscost_info
        SET bill_id = #{billId}
        ,show_bill_id = #{billId}
        ,show_bill_code = #{billCode}
        ,oper_code =  #{operCode}
        ,oper_by = #{operBy}
        ,oper_time=NOW()
        WHERE del_flag = '0'
        AND id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>


    <!--    应收账单-运输对账-更新主费用信息-->
    <update id="reconciliationForYsBindFee2">
        UPDATE bms_yscost_info
        SET bill_id = null
        ,show_bill_id = null
        ,show_bill_code = null
        ,oper_code =  #{operCode}
        ,oper_by = #{operBy}
        ,oper_time=NOW()
        WHERE del_flag = '0'
        AND id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>



    <update id="reconciliationForFlushMainFeeTrans">
        <foreach collection="list" item="item"  separator=";">
            UPDATE bms_yscost_main_info
            SET freight = #{item.freight}
            ,delivery_fee = #{item.deliveryFee}
            ,ultrafar_fee = #{item.ultrafarFee}
            ,superframes_fee = #{item.superframesFee}
            ,excess_fee = #{item.excessFee}
            ,reduce_fee = #{item.reduceFee}
            ,outboundsorting_fee = #{item.outboundsortingFee}
            ,shortbarge_fee = #{item.shortbargeFee}
            ,return_fee = #{item.returnFee}
            ,exception_fee = #{item.exceptionFee}
            ,other_cost1 = #{item.otherCost1}
            ,other_cost2 = #{item.otherCost2}
            ,other_cost3 = #{item.otherCost3}
            ,other_cost4 = #{item.otherCost4}
            ,other_cost5 = #{item.otherCost5}
            ,other_cost6 = #{item.otherCost6}
            ,other_cost7 = #{item.otherCost7}
            ,other_cost8 = #{item.otherCost8}
            ,other_cost9 = #{item.otherCost9}
            ,other_cost10 = #{item.otherCost10}
            ,other_cost11 = #{item.otherCost11}
            ,other_cost12 = #{item.otherCost12}
            ,sum_fee = #{item.sumFee}
            ,adjust_fee = #{item.sumFee}
            ,adjust_remark = #{item.adjustRemark}
            ,remarks = #{item.remarks}
            ,oper_code = #{operCode}
            ,oper_by = #{operBy}
            ,oper_time = NOW()
            WHERE del_flag='0'
            AND id = #{item.id}
        </foreach>
    </update>


    <update id="reconciliationForYsFlushDetailFee">
        <foreach collection="list" item="item"  separator=";">
            UPDATE bms_yscost_info
            <set>
                <if test="item.freight != null">freight = #{item.freight},</if>
                <if test="item.deliveryFee != null">delivery_fee = #{item.deliveryFee},</if>
                <if test="item.ultrafarFee != null">ultrafar_fee = #{item.ultrafarFee},</if>
                <if test="item.superframesFee != null">superframes_fee = #{item.superframesFee},</if>
                <if test="item.excessFee != null">excess_fee = #{item.excessFee},</if>
                <if test="item.reduceFee != null">reduce_fee = #{item.reduceFee},</if>
                <if test="item.outboundsortingFee != null">outboundsorting_fee = #{item.outboundsortingFee},</if>
                <if test="item.shortbargeFee != null">shortbarge_fee = #{item.shortbargeFee},</if>
                <if test="item.returnFee != null">return_fee = #{item.returnFee},</if>
                <if test="item.exceptionFee != null">exception_fee = #{item.exceptionFee},</if>
                <if test="item.otherCost1 != null">other_cost1 = #{item.otherCost1},</if>
                <if test="item.otherCost2 != null">other_cost2 = #{item.otherCost2},</if>
                <if test="item.otherCost3 != null">other_cost3 = #{item.otherCost3},</if>
                <if test="item.otherCost4 != null">other_cost4 = #{item.otherCost4},</if>
                <if test="item.otherCost5 != null">other_cost5 = #{item.otherCost5},</if>
                <if test="item.otherCost6 != null">other_cost6 = #{item.otherCost6},</if>
                <if test="item.otherCost7 != null">other_cost7 = #{item.otherCost7},</if>
                <if test="item.otherCost8 != null">other_cost8 = #{item.otherCost8},</if>
                <if test="item.otherCost9 != null">other_cost9 = #{item.otherCost9},</if>
                <if test="item.otherCost10 != null">other_cost10 = #{item.otherCost10},</if>
                <if test="item.otherCost11 != null">other_cost11 = #{item.otherCost11},</if>
                <if test="item.otherCost12 != null">other_cost12 = #{item.otherCost12},</if>
                <if test="item.settleAmount != null">adjust_fee = #{item.settleAmount},</if>
                <if test="item.adjustRemark != null">adjust_remark = #{item.adjustRemark},</if>
                <if test="item.settleAmount != null">settle_amount = #{item.settleAmount},</if>
                <if test="item.remarks != null">remarks = #{item.remarks},</if>
                <if test="operCode != null">oper_code = #{operCode},</if>
                <if test="operBy != null">oper_by = #{operBy},</if>
                oper_time = NOW()
            </set>
            WHERE del_flag=0
            AND id = #{item.id}
        </foreach>
    </update>



    <update id="reconciliationForFlushMiddleFeeTrans">
        <foreach collection="list" item="item"  separator=";">
            UPDATE bms_ysexpenses_middle
            SET freight = #{item.freight}
            ,delivery_fee = #{item.deliveryFee}
            ,ultrafar_fee = #{item.ultrafarFee}
            ,superframes_fee = #{item.superframesFee}
            ,excess_fee = #{item.excessFee}
            ,reduce_fee = #{item.reduceFee}
            ,outboundsorting_fee = #{item.outboundsortingFee}
            ,shortbarge_fee = #{item.shortbargeFee}
            ,return_fee = #{item.returnFee}
            ,exception_fee = #{item.exceptionFee}
            ,other_cost1 = #{item.otherCost1}
            ,other_cost2 = #{item.otherCost2}
            ,other_cost3 = #{item.otherCost3}
            ,other_cost4 = #{item.otherCost4}
            ,other_cost5 = #{item.otherCost5}
            ,other_cost6 = #{item.otherCost6}
            ,other_cost7 = #{item.otherCost7}
            ,other_cost8 = #{item.otherCost8}
            ,other_cost9 = #{item.otherCost9}
            ,other_cost10 = #{item.otherCost10}
            ,other_cost11 = #{item.otherCost11}
            ,other_cost12 = #{item.otherCost12}
            ,share_type = #{item.shareType}
            ,share_amount = #{item.shareAmount}
            ,oper_by = #{operBy}
            ,oper_time = NOW()
            WHERE del_flag='0'
            AND id = #{item.id}
        </foreach>
    </update>



    <update id="cancelYfFeeDetail">
        UPDATE bms_yfexpenses_middle
            SET del_flag = 1
        WHERE del_flag = 0
            AND main_pk_id = #{pkId};
        UPDATE bms_yfexpenses_middle_share
            SET del_flag = 1
        WHERE del_flag = 0
            AND main_pk_id = #{pkId};
        UPDATE bms_yfcost_info
            SET del_flag = 1
            ,oper_time = NOW()
            ,oper_by = #{operBy}
        WHERE del_flag = 0
            AND main_pk_id = #{pkId};
    </update>



    <update id="editYfCodeCostStatus">
        UPDATE bms_yfbillcodeinfo
            SET cost_status = 1
            ,oper_code = #{operCode}
            ,oper_by = #{operBy}
            ,oper_time=NOW()
        WHERE del_flag='0'
            AND id IN
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
    </update>



    <!--    应付账单-对账-更新主费用信息-->
    <update id="reconciliationForYfBindFee">
        UPDATE bms_yfcost_info
        SET bill_id = #{billId}
        ,show_bill_id = #{billId}
        ,show_bill_code = #{billCode}
        ,oper_code =  #{operCode}
        ,oper_by = #{operBy}
        ,oper_time=NOW()
        WHERE del_flag = '0'
        AND id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>


    <!--    应付账单-对账-更新主费用信息-->
    <update id="reconciliationForYfBindFee2">
        UPDATE bms_yfcost_info
        SET bill_id = null
        ,show_bill_id = null
        ,show_bill_code = null
        ,oper_code =  #{operCode}
        ,oper_by = #{operBy}
        ,oper_time=NOW()
        WHERE del_flag = '0'
        AND id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>


    <update id="reconciliationForYfFlushDetailFee">
        <foreach collection="list" item="item"  separator=";">
            UPDATE bms_yfcost_info
            <set>
                <if test="item.freight != null">freight = #{item.freight},</if>
                <if test="item.deliveryFee != null">delivery_fee = #{item.deliveryFee},</if>
                <if test="item.ultrafarFee != null">ultrafar_fee = #{item.ultrafarFee},</if>
                <if test="item.superframesFee != null">superframes_fee = #{item.superframesFee},</if>
                <if test="item.excessFee != null">excess_fee = #{item.excessFee},</if>
                <if test="item.reduceFee != null">reduce_fee = #{item.reduceFee},</if>
                <if test="item.outboundsortingFee != null">outboundsorting_fee = #{item.outboundsortingFee},</if>
                <if test="item.shortbargeFee != null">shortbarge_fee = #{item.shortbargeFee},</if>
                <if test="item.returnFee != null">return_fee = #{item.returnFee},</if>
                <if test="item.exceptionFee != null">exception_fee = #{item.exceptionFee},</if>
                <if test="item.otherCost1 != null">other_cost1 = #{item.otherCost1},</if>
                <if test="item.otherCost2 != null">other_cost2 = #{item.otherCost2},</if>
                <if test="item.otherCost3 != null">other_cost3 = #{item.otherCost3},</if>
                <if test="item.otherCost4 != null">other_cost4 = #{item.otherCost4},</if>
                <if test="item.otherCost5 != null">other_cost5 = #{item.otherCost5},</if>
                <if test="item.otherCost6 != null">other_cost6 = #{item.otherCost6},</if>
                <if test="item.otherCost7 != null">other_cost7 = #{item.otherCost7},</if>
                <if test="item.otherCost8 != null">other_cost8 = #{item.otherCost8},</if>
                <if test="item.otherCost9 != null">other_cost9 = #{item.otherCost9},</if>
                <if test="item.otherCost10 != null">other_cost10 = #{item.otherCost10},</if>
                <if test="item.otherCost11 != null">other_cost11 = #{item.otherCost11},</if>
                <if test="item.otherCost12 != null">other_cost12 = #{item.otherCost12},</if>
                <if test="item.adjustFee != null">adjust_fee = #{item.adjustFee},</if>
                <if test="item.adjustRemark != null">adjust_remark = #{item.adjustRemark},</if>
                <if test="item.remarks != null">remarks = #{item.remarks},</if>
                <if test="operCode != null">oper_code = #{operCode},</if>
                <if test="operBy != null">oper_by = #{operBy},</if>
                oper_time = NOW()
            </set>
            WHERE del_flag= 0
            AND id = #{item.id}
        </foreach>
    </update>
    
    <update id="cancelYsCostFlushCodeStatus">
        <if test="codeType != null and codeType == 1">
            UPDATE bms_trans_code_info t1
            LEFT JOIN (
                SELECT DISTINCT
                    code_pk_id
                FROM bms_ysexpenses_middle
                WHERE code_type = 1
                AND del_flag = 0
                AND code_pk_id IN
                <foreach collection="pkIds" item="pkId" separator="," open="(" close=")">
                    #{pkId}
                </foreach>
            ) t2 ON t2.code_pk_id = t1.pk_id
            SET
                t1.cost_status = CASE WHEN t2.code_pk_id IS NOT NULL THEN 1 ELSE 0 END
            WHERE t1.del_flag = 0
            AND t1.pk_id IN
            <foreach collection="pkIds" item="pkId" separator="," open="(" close=")">
                #{pkId}
            </foreach>
        </if>
        <if test="codeType != null and codeType == 2">
            UPDATE bms_storage_code_info t1
            LEFT JOIN (
                SELECT DISTINCT
                    code_pk_id
                FROM bms_ysexpenses_middle
                WHERE code_type = 2
                AND del_flag = 0
                AND code_pk_id IN
                <foreach collection="pkIds" item="pkId" separator="," open="(" close=")">
                    #{pkId}
                </foreach>
            ) t2 ON t2.code_pk_id = t1.pk_id
            SET
                t1.ys_cost_status = CASE WHEN t2.code_pk_id IS NOT NULL THEN 1 ELSE 0 END
            WHERE t1.del_flag = 0
            AND t1.cost_mode = 1
            AND t1.pk_id IN
            <foreach collection="pkIds" item="pkId" separator="," open="(" close=")">
                #{pkId}
            </foreach>
        </if>
    </update>


    <update id="cancelYfCostFlushCodeStatus">
        <if test="codeType != null and codeType == 1">
            UPDATE bms_dispatch_code_info t1
            LEFT JOIN bms_yfexpenses_middle t2 ON t2.code_pk_id = t1.pk_id AND t2.code_type = 1 AND t2.del_flag = 0
            SET
                t1.cost_status = CASE WHEN t2.code_pk_id IS NOT NULL THEN 1 ELSE 0 END
            WHERE t1.del_flag = 0
            AND t1.pk_id IN
            <foreach collection="pkIds" item="pkId" separator="," open="(" close=")">
                #{pkId}
            </foreach>
        </if>
        <if test="codeType != null and codeType == 2">
            UPDATE bms_storage_code_info t1
            LEFT JOIN bms_yfexpenses_middle t2 ON t2.code_pk_id = t1.pk_id AND t2.code_type = 2 AND t2.del_flag = 0
            SET
                t1.yf_cost_status = CASE WHEN t2.code_pk_id IS NOT NULL THEN 1 ELSE 0 END
            WHERE t1.cost_mode = 2
            AND t1.del_flag = 0
            AND t1.pk_id IN
            <foreach collection="pkIds" item="pkId" separator="," open="(" close=")">
                #{pkId}
            </foreach>
        </if>
    </update>

</mapper>