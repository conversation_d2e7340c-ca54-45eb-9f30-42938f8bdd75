<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfstockcodeDetailinfoMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYfstockcodeDetailinfo" id="BmsYfstockcodeDetailinfoResult">
        <result property="id"    column="id"    />
        <result property="yfstockId"    column="yfstock_id"    />
        <result property="skuCode"    column="sku_code"    />
        <result property="skuName"    column="sku_name"    />
        <result property="skuClass"    column="sku_class"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="boxType"    column="box_type"    />
        <result property="temperatureType"    column="temperature_type"    />
        <result property="contentsNumber"    column="contents_number"    />
        <result property="weight"    column="weight"    />
        <result property="volume"    column="volume"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="oddBoxes"    column="odd_boxes"    />
        <result property="totalAmount"    column="total_amount"    />

    </resultMap>

    <sql id="selectBmsYfstockcodeDetailinfoVo">
        select id, yfstock_id, sku_code, sku_name,sku_class, total_boxes, box_type, temperature_type, contents_number, weight, volume,total_weight,total_volume, price , del_flag,odd_boxes from bms_yfstockcode_detailinfo
    </sql>

    <select id="selectBmsYfstockcodeDetailinfoList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfstockcodeDetailinfo" resultMap="BmsYfstockcodeDetailinfoResult">
        <include refid="selectBmsYfstockcodeDetailinfoVo"/>
        <where>  
            <if test="yfstockId != null  and yfstockId != ''"> and yfstock_id = #{yfstockId}</if>
            <if test="skuCode != null  and skuCode != ''"> and sku_code = #{skuCode}</if>
            <if test="skuName != null  and skuName != ''"> and sku_name like concat('%', #{skuName}, '%')</if>
            <if test="totalBoxes != null "> and total_boxes = #{totalBoxes}</if>
            <if test="boxType != null  and boxType != ''"> and box_type = #{boxType}</if>
            <if test="temperatureType != null  and temperatureType != ''"> and temperature_type = #{temperatureType}</if>
            <if test="contentsNumber != null "> and contents_number = #{contentsNumber}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="volume != null "> and volume = #{volume}</if>
        </where>
    </select>
    
    <select id="selectBmsYfstockcodeDetailinfoById" parameterType="java.lang.String" resultMap="BmsYfstockcodeDetailinfoResult">
        <include refid="selectBmsYfstockcodeDetailinfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBmsYfstockcodeDetailinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfstockcodeDetailinfo" useGeneratedKeys="true" keyProperty="id">
        insert into bms_yfstockcode_detailinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="yfstockId != null">yfstock_id,</if>
            <if test="skuCode != null">sku_code,</if>
            <if test="skuName != null">sku_name,</if>
            <if test="skuClass != null">sku_class,</if>
            <if test="totalBoxes != null">total_boxes,</if>
            <if test="boxType != null">box_type,</if>
            <if test="temperatureType != null">temperature_type,</if>
            <if test="contentsNumber != null">contents_number,</if>
            <if test="weight != null">weight,</if>
            <if test="volume != null">volume,</if>
            <if test="totalWeight != null">total_weight,</if>
            <if test="totalVolume != null">total_volume,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="oddBoxes != null">odd_boxes,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="yfstockId != null">#{yfstockId},</if>
            <if test="skuCode != null">#{skuCode},</if>
            <if test="skuName != null">#{skuName},</if>
            <if test="skuClass != null">#{skuClass},</if>
            <if test="totalBoxes != null">#{totalBoxes},</if>
            <if test="boxType != null">#{boxType},</if>
            <if test="temperatureType != null">#{temperatureType},</if>
            <if test="contentsNumber != null">#{contentsNumber},</if>
            <if test="weight != null">#{weight},</if>
            <if test="volume != null">#{volume},</if>
            <if test="totalWeight != null">#{totalWeight},</if>
            <if test="totalVolume != null">#{totalVolume},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="oddBoxes != null">#{oddBoxes},</if>
         </trim>
    </insert>

    <update id="updateBmsYfstockcodeDetailinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfstockcodeDetailinfo">
        update bms_yfstockcode_detailinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="yfstockId != null">yfstock_id = #{yfstockId},</if>
            <if test="skuCode != null">sku_code = #{skuCode},</if>
            <if test="skuName != null">sku_name = #{skuName},</if>
            <if test="skuClass != null">sku_class = #{skuClass},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="boxType != null">box_type = #{boxType},</if>
            <if test="temperatureType != null">temperature_type = #{temperatureType},</if>
            <if test="contentsNumber != null">contents_number = #{contentsNumber},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="volume != null">volume = #{volume},</if>
            <if test="totalWeight != null">total_weight = #{totalWeight},</if>
            <if test="totalVolume != null">total_volume = #{totalVolume},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="oddBoxes != null">odd_boxes = #{oddBoxes},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYfstockcodeDetailinfoById" parameterType="java.lang.String">
        delete from bms_yfstockcode_detailinfo where id = #{id}
    </delete>

    <delete id="deleteBmsYfstockcodeDetailinfoByIds">
        delete from bms_yfstockcode_detailinfo where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYfstockcodeDetailinfoStatusByIds">
        update bms_yfstockcode_detailinfo set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchSave" parameterType="java.util.List">
        insert into bms_yfstockcode_detailinfo
        (yfstock_id, sku_code, sku_name,sku_class, total_boxes, box_type, temperature_type, contents_number, weight, volume,total_weight,total_volume, price ,del_flag,odd_boxes,total_amount)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.yfstockId},
            #{item.skuCode},
            #{item.skuName},
            #{item.skuClass},
            #{item.totalBoxes},
            #{item.boxType},
            #{item.temperatureType},
            #{item.contentsNumber},
            #{item.weight},
            #{item.volume},
            #{item.totalWeight},
            #{item.totalVolume},
            #{item.price},
            #{item.delFlag},
            #{item.oddBoxes},
            #{item.totalAmount})
        </foreach>
    </insert>

    <select id="selectBmsYfstockcodeDetailinfoList2" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfstockcodeDetailinfo" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDetailinfoDto" >
        SELECT
        t1.id as id,
        t2.relate_code as relateCode,
        t1.yfstock_id as yfstockId,
        t1.sku_code as skuCode,
        t1.sku_name as skuName,
        t1.sku_class as skuClass,
        t1.total_boxes as totalBoxes,
        t1.box_type as boxType,
        t1.temperature_type as temperatureType,
        t1.contents_number as contentsNumber,
        t1.weight as weight,
        t1.volume as volume,
        t1.total_weight as totalWeight,
        t1.total_volume as totalVolume,
        t1.price as price,
        t1.del_flag as delFlag,
        t1.odd_boxes as oddBoxes,
        t2.cw_pallet_number as cwPalletNumber,
        t2.lc_pallet_number as lcPalletNumber,
        t2.ld_pallet_number as ldPalletNumber,
        t2.order_no as orderNo,
        t2.order_type as orderType,
        t1.total_amount as totalAmount,
        t2.platform_code as platformCode,
        ms.unit,
        ms.specification
        FROM bms_yfstockcode_detailinfo t1
        LEFT JOIN bms_yfstock_codeinfo t2 on t2.id = t1.yfstock_id
        left join mdm_skuinfo ms on ms.sku_code=t1.sku_code and ms.status=0
        <where>
            <if test="yfstockId != null  and yfstockId != ''"> and t1.yfstock_id = #{yfstockId}</if>
            <if test="yfstockIds != null and yfstockIds.size()>0">
                and t1.yfstock_id in
                <foreach collection="yfstockIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="skuCode != null  and skuCode != ''"> and t1.sku_code = #{skuCode}</if>
            <if test="skuName != null  and skuName != ''"> and t1.sku_name like concat('%', #{skuName}, '%')</if>
            <if test="totalBoxes != null "> and t1.total_boxes = #{totalBoxes}</if>
            <if test="boxType != null  and boxType != ''"> and t1.box_type = #{boxType}</if>
            <if test="temperatureType != null  and temperatureType != ''"> and t1.temperature_type = #{temperatureType}</if>
            <if test="contentsNumber != null "> and t1.contents_number = #{contentsNumber}</if>
            <if test="weight != null "> and t1.weight = #{weight}</if>
            <if test="volume != null "> and t1.volume = #{volume}</if>
        </where>
        ORDER BY t2.relate_code
    </select>
</mapper>