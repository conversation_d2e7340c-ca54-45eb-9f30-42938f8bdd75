<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsPubAddedfeeMapper">
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsPubAddedfee" id="BmsPubAddedfeeMap">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="relate_code" jdbcType="VARCHAR" property="relateCode" />
        <result column="expenses_code" jdbcType="VARCHAR" property="expensesCode" />
        <result column="cost_status" jdbcType="CHAR" property="costStatus" />
        <result column="fee_source" jdbcType="CHAR" property="feeSource" />
        <result column="client_id" jdbcType="INTEGER" property="clientId" />
        <result column="client_code" jdbcType="VARCHAR" property="clientCode" />
        <result column="carrier_code" jdbcType="VARCHAR" property="carrierCode" />
        <result column="settle_type" jdbcType="CHAR" property="settleType" />
        <result column="item_id" jdbcType="INTEGER" property="itemId" />
        <result column="amount" jdbcType="DECIMAL" property="amount" />
        <result column="adjust_before_amount" jdbcType="DECIMAL" property="adjustBeforeAmount" />
        <result column="adjust_before_remark" jdbcType="VARCHAR" property="adjustBeforeRemark" />
        <result column="creat_date" jdbcType="TIMESTAMP" property="creatDate" />
        <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
        <result column="number" jdbcType="DECIMAL" property="number" />
        <result column="unit" jdbcType="INTEGER" property="unit" />
        <result column="bill_id" jdbcType="INTEGER" property="billId" />
        <result column="bill_code" jdbcType="VARCHAR" property="billCode" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_code" jdbcType="VARCHAR" property="operCode" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="dept_id" jdbcType="INTEGER" property="deptId" />
        <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
        <result column="fee_belong" jdbcType="SMALLINT" property="feeBelong" />
        <result column="code_type" jdbcType="INTEGER" property="codeType" />
        <result column="item_olevel_id" jdbcType="INTEGER" property="itemOlevelId" />
        <result column="charging_date" jdbcType="TIMESTAMP" property="chargingDate" />
        <result column="order_source" jdbcType="CHAR" property="orderSource" />
        <result column="import_time" jdbcType="TIMESTAMP" property="importTime" />
        <result column="import_code" jdbcType="VARCHAR" property="importCode" />
        <result column="import_by" jdbcType="VARCHAR" property="importBy" />
        <result column="bill_date" jdbcType="VARCHAR" property="billDate" />
        <result column="show_bill_id" jdbcType="INTEGER" property="showBillId" />
        <result column="show_bill_code" jdbcType="VARCHAR" property="showBillCode" />
    </resultMap>

    <sql id="selectBmsPubAddedfeeVo">
        select id, relate_code, expenses_code, cost_status, fee_source, client_id, client_code, carrier_code, settle_type, item_id, amount, adjust_before_amount, adjust_before_remark, creat_date, warehouse_code, number, unit, bill_id, bill_code, remark, oper_by, oper_code, oper_time, del_flag, dept_id, dept_code, fee_belong, code_type, item_olevel_id, charging_date, order_source, import_time, import_code, import_by, bill_date, show_bill_id, show_bill_code
        from bms_addedfee
    </sql>

    <update id="updateBatch" parameterType="java.util.Map">
        update bms_addedfee
        <set>
            <if test="costStatus != null and costStatus != ''">
                cost_status = #{costStatus},
            </if>
            <if test="operBy != null and operBy != ''">
                oper_by = #{operBy},
            </if>
            <if test="operCode != null and operCode != ''">
                oper_code = #{operCode},
            </if>
            <if test="operTime != null and operTime != ''">
                oper_time = #{operTime},
            </if>
            <if test="billDate != null and billDate != ''">
                bill_date = #{billDate},
            </if>
        </set>
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <update id="updateBatchByList">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";" >
            update bms_addedfee
            <set>
                <if test="item.costStatus != null and item.costStatus != ''">
                    cost_status = #{item.costStatus},
                </if>
                <if test="item.amount != null">
                    amount = #{item.amount},
                </if>
                <if test="item.operBy != null and item.operBy != ''">
                    oper_by = #{item.operBy},
                </if>
                <if test="item.operCode != null and item.operCode != ''">
                    oper_code = #{item.operCode},
                </if>
                <if test="item.operTime != null and item.operTime != ''">
                    oper_time = #{item.operTime},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark},
                </if>
                <if test="item.adjustBeforeAmount != null">
                    adjust_before_amount = #{item.adjustBeforeAmount},
                </if>
                <if test="item.adjustBeforeRemark != null and item.adjustBeforeRemark != ''">
                    adjust_before_remark = #{item.adjustBeforeRemark},
                </if>
                <if test="item.billId != null">
                    bill_id = #{item.billId},
                </if>
                <if test="item.billCode !=null and item.billCode !=''">
                    bill_code=#{item.billCode}
                </if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <update id="updateBillIdBatch">
        update bms_addedfee
        set
        bill_id = null,
        bill_code = null,
        oper_time = now()
        where bill_id in
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
    </update>
    <update id="delBatch">
        update bms_addedfee
        set
        del_flag = '1',
        oper_by = #{userInfo.employeename},
        oper_code = #{userInfo.id},
        oper_time = now()
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!--分页查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BmsPubAddedfeeMap">
        select id, relate_code, expenses_code, cost_status, fee_source, client_id, client_code, carrier_code, settle_type, item_id, amount, adjust_before_amount, adjust_before_remark, creat_date, warehouse_code, number, unit, bill_id, bill_code, remark, oper_by, oper_code, oper_time, del_flag, dept_id, dept_code, fee_belong, code_type, item_olevel_id, charging_date, order_source, import_time, import_code, import_by, bill_date, show_bill_id, show_bill_code
        from bms_addedfee
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="relateCode != null and relateCode != ''">
                and relate_code = #{relateCode}
            </if>
            <if test="expensesCode != null and expensesCode != ''">
                and expenses_code = #{expensesCode}
            </if>
            <if test="costStatus != null and costStatus != ''">
                and cost_status = #{costStatus}
            </if>
            <if test="clientCode != null and clientCode != ''">
                and client_code = #{clientCode}
            </if>
            <if test="carrierCode != null and carrierCode != ''">
                and carrier_code = #{carrierCode}
            </if>
            <if test="settleType != null and settleType != ''">
                and settle_type = #{settleType}
            </if>
            <if test="itemId != null and itemId != ''">
                and item_id = #{itemId}
            </if>
            <if test="amount != null and amount != ''">
                and amount = #{amount}
            </if>
            <if test="creatDate != null and creatDate != ''">
                and creat_date = #{creatDate}
            </if>
            <if test="warehouseCode != null and warehouseCode != ''">
                and warehouse_code = #{warehouseCode}
            </if>
            <if test="number != null and number != ''">
                and number = #{number}
            </if>
            <if test="unit != null and unit != ''">
                and unit = #{unit}
            </if>
            <if test="billId != null and billId != ''">
                and bill_id = #{billId}
            </if>
            <if test="billCode != null and billCode != ''">
                and bill_code = #{billCode}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="operBy != null and operBy != ''">
                and oper_by = #{operBy}
            </if>
            <if test="operCode != null and operCode != ''">
                and oper_code = #{operCode}
            </if>
            <if test="operTime != null and operTime != ''">
                and oper_time = #{operTime}
            </if>
            <if test="delFlag != null and delFlag != ''">
                and del_flag = #{delFlag}
            </if>
        </where>
        ORDER BY oper_time DESC
    </select>


    <select id="get" resultType="com.bbyb.joy.bms.domain.dto.BmsPubAddedfee">
        SELECT
            id AS Id,
            relate_code AS relateCode,
            expenses_code AS expensesCode,
            cost_status AS costStatus,
            fee_source AS feeSource,
            client_id AS clientId,
            client_code AS clientCode,
            carrier_code AS carrierCode,
            settle_type AS settleType,
            item_id AS itemId,
            amount AS amount,
            adjust_before_amount AS adjustBeforeAmount,
            adjust_before_remark AS adjustBeforeRemark,
            creat_date AS creatDate,
            warehouse_code AS warehouseCode,
            number AS number,
            unit AS unit,
            bill_id AS billId,
            bill_code AS billCode,
            remark AS remark,
            oper_by AS operBy,
            oper_code AS operCode,
            oper_time AS operTime,
            del_flag AS delFlag,
            dept_id AS deptId,
            dept_code AS deptCode,
            fee_belong AS feeBelong,
            code_type AS codeType,
            item_olevel_id AS itemOlevelId,
            charging_date AS chargingDate,
            order_source AS orderSource,
            import_time AS importTime,
            import_code AS importCode,
            import_by AS importBy,
            bill_date AS billDate,
            show_bill_id AS showBillId,
            show_bill_code AS showBillCode
        FROM bms_addedfee
        where del_flag='0'
          and id = #{id}
    </select>



    <resultMap type="com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto" id="BmsPubAddedfeeMapDto">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="relateCode" column="relate_code" jdbcType="VARCHAR"/>
        <result property="expensesCode" column="expenses_code" jdbcType="VARCHAR"/>
        <result property="costStatus" column="cost_status" />
        <result property="costStatusName" column="cost_status_name" jdbcType="VARCHAR"/>
        <result property="carrierCode" column="carrier_code" jdbcType="VARCHAR"/>
        <result property="carrierName" column="carrier_name" jdbcType="VARCHAR"/>
        <result property="clientCode" column="client_code" jdbcType="VARCHAR"/>
        <result property="clientName" column="client_name" jdbcType="VARCHAR"/>
        <result property="itemId" column="item_id" jdbcType="INTEGER"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="settleType" column="settle_type"/>
        <result property="settleTypeName" column="settle_type_name" jdbcType="VARCHAR"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="creatDate" column="creat_date" jdbcType="DATE"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="billId" column="bill_id" jdbcType="VARCHAR"/>
        <result property="billCode" column="bill_code" jdbcType="VARCHAR"/>
        <result property="billingStatus" column="billing_status" jdbcType="VARCHAR"/>
        <result property="number" column="number" jdbcType="DECIMAL"/>
        <result property="unit" column="unit" jdbcType="INTEGER"/>
        <result property="unitName" column="unit_name" jdbcType="VARCHAR"/>
        <result property="warehouseCode" column="warehouse_code" jdbcType="VARCHAR"/>
        <result property="warehouseName" column="warehouse_name" jdbcType="VARCHAR"/>
        <result property="feeSource"    column="fee_source" jdbcType="INTEGER"   />
        <result property="feeSourceName"    column="fee_source_name" jdbcType="VARCHAR"   />
        <result property="operBy" column="oper_by" jdbcType="VARCHAR"/>
        <result property="operCode" column="oper_code" jdbcType="VARCHAR"/>
        <result property="operTime" column="oper_time" jdbcType="DATE"/>
        <result property="adjustBeforeAmount"    column="adjust_before_amount" jdbcType="DECIMAL"   />
        <result property="adjustBeforeRemark"    column="adjust_before_remark" jdbcType="VARCHAR"   />
        <result property="billDate"    column="billDate" jdbcType="VARCHAR"   />
        <result property="yfBillDate"    column="yfBillDate" jdbcType="VARCHAR"   />
        <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
        <result property="deptCode" column="dept_code" jdbcType="VARCHAR"/>
        <result property="feeBelong" column="fee_belong" jdbcType="INTEGER"/>
        <result property="itemOlevelId" column="item_olevel_id" jdbcType="INTEGER"/>
        <result property="itemOlevelName" column="itemOlevelName" jdbcType="INTEGER"/>
        <result property="chargingDate" column="charging_date" jdbcType="DATE"/>
        <result property="orderSource" column="order_source" jdbcType="CHAR"/>
        <result property="importTime" column="import_time" jdbcType="DATE"/>
        <result property="importCode" column="import_code" jdbcType="VARCHAR"/>
        <result property="importBy" column="import_by" jdbcType="VARCHAR"/>
        <result property="businessType" column="business_type" jdbcType="INTEGER"/>
        <result property="businessTypeDesc" column="businessTypeDesc" jdbcType="VARCHAR"/>
        <result property="feeBelongDesc" column="fee_belong_desc" jdbcType="VARCHAR"/>
        <result property="clientId" column="client_id" jdbcType="INTEGER"/>
        <result property="carrierId" column="carrier_id" jdbcType="INTEGER"/>
        <result property="warehouseProvince" column="warehouse_province" jdbcType="VARCHAR"/>
        <result property="showBillCode" column="show_bill_code" jdbcType="VARCHAR"/>
        <result property="showBillId" column="show_bill_id" jdbcType="INTEGER"/>
    </resultMap>


    <select id="queryAllByDtoLimit" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto" >
        SELECT t1.id                                        AS id,
            t1.relate_code                               AS relateCode,
            t1.expenses_code                             AS expensesCode,
            t1.cost_status                               AS costStatus,
            ''                                           AS costStatusName,
            t4.id                                        AS carrierId,
            t1.carrier_code                              AS carrierCode,
            t4.carrier_name                              AS carrierName,
            t3.id                                        AS clientId,
            t1.client_code                               AS clientCode,
            t3.client_name                               AS clientName,
            t1.item_id                                   AS itemId,
            ''                                           AS itemName,
            t1.settle_type                               AS settleType,
            ''                                           AS settleTypeName,
            t1.amount,
            t1.creat_date                                AS creatDate,
            t1.remark,
            t1.bill_id                                   AS billId,
            t1.bill_code                                 AS billCode,
            t1.bill_date                                 AS billDate,
            IF(t1.bill_code IS NULL, '未生成', '已生成') AS billingStatus,
            t1.number,
            t1.number                                    AS skuNumber,
            t1.unit,
            ''                                           AS unitName,
            t1.warehouse_code                            AS warehouseCode,
            t9.warehouse_name                            AS warehouseName,
            t1.fee_source                                AS feeSource,
            ''                                           AS feeSourceName,
            t1.oper_by                                   AS operBy,
            t1.oper_code                                 AS operCode,
            t1.oper_time                                 AS operTime,
            t1.adjust_before_amount                      AS adjustBeforeAmount,
            t1.adjust_before_remark                      AS adjustBeforeRemark,
            t1.item_olevel_id                            AS itemOlevelId,
            ''                                           AS itemOlevelName,
            t1.dept_id                                   AS deptId,
            t1.dept_code                                 AS deptCode,
            t1.charging_date                             AS chargingDate,
            t1.order_source                              AS orderSource,
            t1.fee_belong                                AS feeBelong,
            t1.import_time                               AS importTime,
            t1.import_code                               AS importCode,
            t1.import_by                                 AS importBy,
            ''                                           AS businessType,
            t9.warehouse_province                        AS warehouseProvince,
            IFNULL(t1.show_bill_code, t1.bill_code)      AS showBillCode,
            IFNULL(t1.show_bill_id, t1.bill_id)          AS showBillId,
            CASE
                WHEN t1.fee_belong = 1 THEN '运输增值'
                WHEN t1.fee_belong = 2 THEN '仓储增值'
                ELSE ''
            END                                      AS feeBelongDesc,
            t1.code_type                             AS codeType
        FROM bms_addedfee t1
        LEFT JOIN bms_clientinfo t3 ON t3.client_code = t1.client_code
        LEFT JOIN bms_carrierinfo t4 ON t4.carrier_code = t1.carrier_code
        LEFT JOIN mdm_warehouseinfo t9 ON t9.warehouse_code = t1.warehouse_code AND t9.del_flag = 0 AND t9.status = 0
        <where>
            <if test="1==1">
                and t1.del_flag = 0
            </if>
            <if test="settleType != null">
                and t1.settle_type = #{settleType}
            </if>
            <if test="startCreatDate != null and startCreatDate != ''">
                and t1.creat_date &gt;= #{startCreatDate}
            </if>
            <if test="endCreatDate != null and endCreatDate != ''">
                and t1.creat_date &lt; #{endCreatDate}
            </if>
            <if test="costStatus != null and costStatus != ''">
                and t1.cost_status = #{costStatus}
            </if>
            <if test="expensesCode != null and expensesCode != ''">
                and t1.expenses_code = #{expensesCode}
            </if>
            <if test="relateCode != null and relateCode != ''">
                and t1.relate_code = #{relateCode}
            </if>
            <if test='billingStatus != null and billingStatus == "0"'>
                and t1.bill_code is null
            </if>
            <if test='billingStatus != null and billingStatus == "1"'>
                and t1.bill_code is not null
            </if>
            <if test="billId != null and billId != ''">
                and t1.bill_id = #{billId}
            </if>
            <if test="billCode != null and billCode != ''">
                and (t1.bill_code = #{billCode} or t1.show_bill_code = #{billCode} )
            </if>
            <if test="showBillCode != null and showBillCode != ''">
                and (t1.bill_code = #{showBillCode} or t1.show_bill_code = #{showBillCode} )
            </if>
            <if test="billIds!=null and billIds.size>0">
                and t1.bill_id IN
                <foreach collection="billIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="clientIds != null and clientIds.size>0">
                and t1.client_code IN
                <foreach collection="clientIds" item="clientId" open="(" separator="," close=")">
                    #{clientId}
                </foreach>
            </if>
            <if test="carrierCodes != null and carrierCodes.size>0">
                and t1.carrier_code IN
                <foreach collection="carrierCodes" item="carrierCode" open="(" separator="," close=")">
                    #{carrierCode}
                </foreach>
            </if>
            <if test="companyArr!=null and companyArr.size>0">
                and t1.dept_id IN
                <foreach collection="companyArr" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="ids !=null and ids.size>0">
                and t1.id in
                <foreach collection="ids" item="ids" open="(" separator="," close=")">
                    #{ids}
                </foreach>
            </if>
            <if test="clientName != null and clientName != ''">
                and t3.client_name like #{clientName}
            </if>
            <if test="carrierName != null and carrierName != ''">
                and t4.carrier_name like #{carrierName}
            </if>
            <if test="feeSource != null">
                and t1.fee_source = #{feeSource}
            </if>
            <if test="billDate != null and billDate != ''">
                and DATE_FORMAT(t1.creat_date,'%Y-%m') = #{billDate}
            </if>
            <if test="deptId != null and deptId != ''">
                and t1.dept_id = #{deptId}
            </if>
            <if test="itemOlevelId != null and itemOlevelId != ''">
                and t1.item_olevel_id = #{itemOlevelId}
            </if>
            <if test="itemId != null and itemId != ''">
                and t1.item_id = #{itemId}
            </if>
            <if test="orderSource != null and orderSource != ''">
                and t1.order_source = #{orderSource}
            </if>
            <if test="feeBelong !=null">
                and t1.fee_belong=#{feeBelong}
            </if>
            <if test="clientCode !=null and clientCode !=''">
                and t1.client_code=#{clientCode}
            </if>
            <if test="charging != null">
                and t1.bill_id is null
            </if>
            <if test="codeType != null">
                and t1.code_type = #{codeType}
            </if>
        </where>
        ORDER BY t1.oper_time DESC
    </select>

    <select id="getAddFeeDto" resultMap="BmsPubAddedfeeMapDto" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto">
        select ba.id,
        ba.relate_code,
        ba.expenses_code,
        ba.fee_source,
        ba.cost_status,
        mc.client_name,
        mc.client_code,
        mc.id,
        ba.carrier_code,
        carrier.carrier_name,
        carrier.id,
        ba.settle_type,
        ba.item_id,
        ba.amount,
        ba.creat_date,
        ba.warehouse_code,
        ba.number,
        ba.unit,
        ba.bill_id,
        ba.bill_code,
        ba.remark,
        ba.oper_by,
        ba.oper_code,
        ba.oper_time,
        ba.del_flag,
        ba.dept_code,
        ba.dept_id,
        warehouse.warehouse_province,
        ba.bill_code,
        ba.show_bill_id,
        ba.show_bill_code
        from bms_addedfee ba
        left join bms_clientinfo mc on mc.client_code = ba.client_code
        left join bms_carrierinfo carrier on carrier.carrier_code = ba.carrier_code
        left join mdm_warehouseinfo warehouse on ba.warehouse_code = warehouse.warehouse_code
        where ba.del_flag = '0'
        and ba.client_code=#{clientCode}
        and ba.bill_id is null
        <if test="ids !=null and ids.size>0">
            and ba.id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
        and  ba.settle_type=#{settleType}
        and DATE_FORMAT(creat_date, '%Y-%m')=#{billDate}
    </select>

    <!--单条根据费用单编码获取增值费信息-->
    <select id="getBmsPubAddedfeeInfoByCode"  resultMap="BmsPubAddedfeeMap">
        <include refid="selectBmsPubAddedfeeVo"/>
        where del_flag = '0' and expenses_code = #{code}
        <if test="type != null ">
            and  settle_type = #{type}
        </if>
    </select>

    <select id="queryDetailByBillId"  resultMap="BmsPubAddedfeeMap">
        <include refid="selectBmsPubAddedfeeVo"/>
        where del_flag = '0'
        and  settle_type = 2
        and bill_id=#{billId}
        limit 1
    </select>

    <select id="queryDetailByBillIdBatch"  resultMap="BmsPubAddedfeeMap">
        <include refid="selectBmsPubAddedfeeVo"/>
        where del_flag = '0'
        and  settle_type = #{settleType}
        and bill_id in
        <foreach collection="billIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryByIds" resultMap="BmsPubAddedfeeMap">
        select id, relate_code, expenses_code, cost_status, fee_source, client_id, client_code, carrier_code, settle_type, item_id, amount, adjust_before_amount, adjust_before_remark, creat_date, warehouse_code, number, unit, bill_id, bill_code, remark, oper_by, oper_code, oper_time, del_flag, dept_id, dept_code, fee_belong, code_type, item_olevel_id, charging_date, order_source, import_time, import_code, import_by, bill_date, show_bill_id, show_bill_code
        from bms_addedfee
        where del_flag = '0'
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="queryByExpensesCodes" resultType="com.bbyb.joy.bms.domain.dto.BmsPubAddedfee">
        SELECT
        id AS Id,
        relate_code AS relateCode,
        expenses_code AS expensesCode,
        cost_status AS costStatus,
        fee_source AS feeSource,
        client_id AS clientId,
        client_code AS clientCode,
        carrier_code AS carrierCode,
        settle_type AS settleType,
        item_id AS itemId,
        amount AS amount,
        adjust_before_amount AS adjustBeforeAmount,
        adjust_before_remark AS adjustBeforeRemark,
        creat_date AS createDate,
        warehouse_code AS warehouseCode,
        number AS number,
        unit AS unit,
        bill_id AS billId,
        bill_code AS billCode,
        remark AS remark,
        oper_by AS operBy,
        oper_code AS operCode,
        oper_time AS operTime,
        del_flag AS delFlag,
        dept_id AS deptId,
        dept_code AS deptCode,
        fee_belong AS feeBelong,
        code_type AS codeType,
        item_olevel_id AS itemOlevelId,
        charging_date AS chargingDate,
        order_source AS orderSource,
        import_time AS importTime,
        import_code AS importCode,
        import_by AS importBy,
        bill_date AS billDate,
        show_bill_id AS showBillId,
        show_bill_code AS showBillCode
        FROM
        bms_addedfee
        WHERE del_flag = '0'
        <if test="settleType!=null and settleType!='' ">
            AND settle_type = #{settleType}
        </if>
        AND expenses_code IN
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>



    <!--单条新增-->
    <insert id="insertBmsPubAddedfee" parameterType="com.bbyb.joy.bms.domain.dto.BmsPubAddedfee">
        insert into bms_addedfee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="relateCode != null">
                relate_code,
            </if>
            <if test="expensesCode != null">
                expenses_code,
            </if>
            <if test="costStatus != null">
                cost_status,
            </if>
            <if test="feeSource != null">
                fee_source,
            </if>
            <if test="clientId != null">
                client_id,
            </if>
            <if test="clientCode != null">
                client_code,
            </if>
            <if test="carrierCode != null">
                carrier_code,
            </if>
            <if test="settleType != null">
                settle_type,
            </if>
            <if test="itemId != null">
                item_id,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="adjustBeforeAmount != null">
                adjust_before_amount,
            </if>
            <if test="adjustBeforeRemark != null">
                adjust_before_remark,
            </if>
            <if test="creatDate != null">
                creat_date,
            </if>
            <if test="warehouseCode != null">
                warehouse_code,
            </if>
            <if test="number != null">
                `number`,
            </if>
            <if test="unit != null">
                unit,
            </if>
            <if test="billId != null">
                bill_id,
            </if>
            <if test="billCode != null">
                bill_code,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="operBy != null">
                oper_by,
            </if>
            <if test="operCode != null">
                oper_code,
            </if>
            <if test="operTime != null">
                oper_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="deptCode != null">
                dept_code,
            </if>
            <if test="feeBelong != null">
                fee_belong,
            </if>
            <if test="codeType != null">
                code_type,
            </if>
            <if test="itemOlevelId != null">
                item_olevel_id,
            </if>
            <if test="chargingDate != null">
                charging_date,
            </if>
            <if test="orderSource != null">
                order_source,
            </if>
            <if test="importTime != null">
                import_time,
            </if>
            <if test="importCode != null">
                import_code,
            </if>
            <if test="importBy != null">
                import_by,
            </if>
            <if test="billDate != null">
                bill_date,
            </if>
            <if test="showBillId != null">
                show_bill_id,
            </if>
            <if test="showBillCode != null">
                show_bill_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="relateCode != null">
                #{relateCode,jdbcType=VARCHAR},
            </if>
            <if test="expensesCode != null">
                #{expensesCode,jdbcType=VARCHAR},
            </if>
            <if test="costStatus != null">
                #{costStatus,jdbcType=CHAR},
            </if>
            <if test="feeSource != null">
                #{feeSource,jdbcType=CHAR},
            </if>
            <if test="clientId != null">
                #{clientId,jdbcType=INTEGER},
            </if>
            <if test="clientCode != null">
                #{clientCode,jdbcType=VARCHAR},
            </if>
            <if test="carrierCode != null">
                #{carrierCode,jdbcType=VARCHAR},
            </if>
            <if test="settleType != null">
                #{settleType,jdbcType=CHAR},
            </if>
            <if test="itemId != null">
                #{itemId,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="adjustBeforeAmount != null">
                #{adjustBeforeAmount,jdbcType=DECIMAL},
            </if>
            <if test="adjustBeforeRemark != null">
                #{adjustBeforeRemark,jdbcType=VARCHAR},
            </if>
            <if test="creatDate != null">
                #{creatDate,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseCode != null">
                #{warehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                #{number,jdbcType=DECIMAL},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=INTEGER},
            </if>
            <if test="billId != null">
                #{billId,jdbcType=INTEGER},
            </if>
            <if test="billCode != null">
                #{billCode,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operBy != null">
                #{operBy,jdbcType=VARCHAR},
            </if>
            <if test="operCode != null">
                #{operCode,jdbcType=VARCHAR},
            </if>
            <if test="operTime != null">
                #{operTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="deptCode != null">
                #{deptCode,jdbcType=VARCHAR},
            </if>
            <if test="feeBelong != null">
                #{feeBelong,jdbcType=SMALLINT},
            </if>
            <if test="codeType != null">
                #{codeType,jdbcType=INTEGER},
            </if>
            <if test="itemOlevelId != null">
                #{itemOlevelId,jdbcType=INTEGER},
            </if>
            <if test="chargingDate != null">
                #{chargingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orderSource != null">
                #{orderSource,jdbcType=CHAR},
            </if>
            <if test="importTime != null">
                #{importTime,jdbcType=TIMESTAMP},
            </if>
            <if test="importCode != null">
                #{importCode,jdbcType=VARCHAR},
            </if>
            <if test="importBy != null">
                #{importBy,jdbcType=VARCHAR},
            </if>
            <if test="billDate != null">
                #{billDate,jdbcType=VARCHAR},
            </if>
            <if test="showBillId != null">
                #{showBillId,jdbcType=INTEGER},
            </if>
            <if test="showBillCode != null">
                #{showBillCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


    <!--单条修改-->
    <update id="updateBmsPubAddedfee" parameterType="com.bbyb.joy.bms.domain.dto.BmsPubAddedfee">
        update bms_addedfee
        <set>
            <if test="relateCode != null">
                relate_code = #{relateCode,jdbcType=VARCHAR},
            </if>
            <if test="expensesCode != null">
                expenses_code = #{expensesCode,jdbcType=VARCHAR},
            </if>
            <if test="costStatus != null">
                cost_status = #{costStatus,jdbcType=CHAR},
            </if>
            <if test="feeSource != null">
                fee_source = #{feeSource,jdbcType=CHAR},
            </if>
            <if test="clientId != null">
                client_id = #{clientId,jdbcType=INTEGER},
            </if>
            <if test="clientCode != null">
                client_code = #{clientCode,jdbcType=VARCHAR},
            </if>
            <if test="carrierCode != null">
                carrier_code = #{carrierCode,jdbcType=VARCHAR},
            </if>
            <if test="settleType != null">
                settle_type = #{settleType,jdbcType=CHAR},
            </if>
            <if test="itemId != null">
                item_id = #{itemId,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="adjustBeforeAmount != null">
                adjust_before_amount = #{adjustBeforeAmount,jdbcType=DECIMAL},
            </if>
            <if test="adjustBeforeRemark != null">
                adjust_before_remark = #{adjustBeforeRemark,jdbcType=VARCHAR},
            </if>
            <if test="creatDate != null">
                creat_date = #{creatDate,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseCode != null">
                warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                `number` = #{number,jdbcType=DECIMAL},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=INTEGER},
            </if>
            <if test="billId != null">
                bill_id = #{billId,jdbcType=INTEGER},
            </if>
            <if test="billCode != null">
                bill_code = #{billCode,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operBy != null">
                oper_by = #{operBy,jdbcType=VARCHAR},
            </if>
            <if test="operCode != null">
                oper_code = #{operCode,jdbcType=VARCHAR},
            </if>
            <if test="operTime != null">
                oper_time = #{operTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="deptCode != null">
                dept_code = #{deptCode,jdbcType=VARCHAR},
            </if>
            <if test="feeBelong != null">
                fee_belong = #{feeBelong,jdbcType=SMALLINT},
            </if>
            <if test="codeType != null">
                code_type = #{codeType,jdbcType=INTEGER},
            </if>
            <if test="itemOlevelId != null">
                item_olevel_id = #{itemOlevelId,jdbcType=INTEGER},
            </if>
            <if test="chargingDate != null">
                charging_date = #{chargingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orderSource != null">
                order_source = #{orderSource,jdbcType=CHAR},
            </if>
            <if test="importTime != null">
                import_time = #{importTime,jdbcType=TIMESTAMP},
            </if>
            <if test="importCode != null">
                import_code = #{importCode,jdbcType=VARCHAR},
            </if>
            <if test="importBy != null">
                import_by = #{importBy,jdbcType=VARCHAR},
            </if>
            <if test="billDate != null">
                bill_date = #{billDate,jdbcType=VARCHAR},
            </if>
            <if test="showBillId != null">
                show_bill_id = #{showBillId,jdbcType=INTEGER},
            </if>
            <if test="showBillCode != null">
                show_bill_code = #{showBillCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id}
    </update>


    <update id="cancelCost">
        update bms_addedfee
        set
        cost_status = 0,
        remark = null,
        charging_date = null,
        oper_time = now(),
        amount = null,
        bill_date = null
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateBmsAddedfee" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto">
        update bms_addedfee
        <set>
            <if test="relateCode != null">
                relate_code = #{relateCode,jdbcType=VARCHAR},
            </if>
            <if test="expensesCode != null">
                expenses_code = #{expensesCode,jdbcType=VARCHAR},
            </if>
            <if test="costStatus != null">
                cost_status = #{costStatus,jdbcType=CHAR},
            </if>
            <if test="feeSource != null">
                fee_source = #{feeSource,jdbcType=CHAR},
            </if>
            <if test="clientId != null">
                client_id = #{clientId,jdbcType=INTEGER},
            </if>
            <if test="clientCode != null">
                client_code = #{clientCode,jdbcType=VARCHAR},
            </if>
            <if test="carrierCode != null">
                carrier_code = #{carrierCode,jdbcType=VARCHAR},
            </if>
            <if test="settleType != null">
                settle_type = #{settleType,jdbcType=CHAR},
            </if>
            <if test="itemId != null">
                item_id = #{itemId,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="adjustBeforeAmount != null">
                adjust_before_amount = #{adjustBeforeAmount,jdbcType=DECIMAL},
            </if>
            <if test="adjustBeforeRemark != null">
                adjust_before_remark = #{adjustBeforeRemark,jdbcType=VARCHAR},
            </if>
            <if test="creatDate != null">
                creat_date = #{creatDate,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseCode != null">
                warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                `number` = #{number,jdbcType=DECIMAL},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=INTEGER},
            </if>
            <if test="billId != null">
                bill_id = #{billId,jdbcType=INTEGER},
            </if>
            <if test="billCode != null">
                bill_code = #{billCode,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operBy != null">
                oper_by = #{operBy,jdbcType=VARCHAR},
            </if>
            <if test="operCode != null">
                oper_code = #{operCode,jdbcType=VARCHAR},
            </if>
            <if test="operTime != null">
                oper_time = #{operTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="deptCode != null">
                dept_code = #{deptCode,jdbcType=VARCHAR},
            </if>
            <if test="feeBelong != null">
                fee_belong = #{feeBelong,jdbcType=SMALLINT},
            </if>
            <if test="codeType != null">
                code_type = #{codeType,jdbcType=INTEGER},
            </if>
            <if test="itemOlevelId != null">
                item_olevel_id = #{itemOlevelId,jdbcType=INTEGER},
            </if>
            <if test="chargingDate != null">
                charging_date = #{chargingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orderSource != null">
                order_source = #{orderSource,jdbcType=CHAR},
            </if>
            <if test="importTime != null">
                import_time = #{importTime,jdbcType=TIMESTAMP},
            </if>
            <if test="importCode != null">
                import_code = #{importCode,jdbcType=VARCHAR},
            </if>
            <if test="importBy != null">
                import_by = #{importBy,jdbcType=VARCHAR},
            </if>
            <if test="billDate != null">
                bill_date = #{billDate,jdbcType=VARCHAR},
            </if>
            <if test="showBillId != null">
                show_bill_id = #{showBillId,jdbcType=INTEGER},
            </if>
            <if test="showBillCode != null">
                show_bill_code = #{showBillCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="insertBatchForDto">
        insert into bms_addedfee(id,relate_code,expenses_code,cost_status,fee_source,client_id,client_code,carrier_code,settle_type,item_id,amount,adjust_before_amount,adjust_before_remark,creat_date,warehouse_code,number,unit,bill_id,bill_code,remark,oper_by,oper_code,oper_time,del_flag,dept_id,dept_code,fee_belong,code_type,item_olevel_id,charging_date,order_source,import_time,import_code,import_by,bill_date,show_bill_id,show_bill_code)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.relateCode},#{entity.expensesCode},#{entity.costStatus},#{entity.feeSource},#{entity.clientId},#{entity.clientCode},#{entity.carrierCode},#{entity.settleType},#{entity.itemId},#{entity.amount},#{entity.adjustBeforeAmount},#{entity.adjustBeforeRemark},#{entity.creatDate},#{entity.warehouseCode},#{entity.number},#{entity.unit},#{entity.billId},#{entity.billCode},#{entity.remark},#{entity.operBy},#{entity.operCode},#{entity.operTime},#{entity.delFlag},#{entity.deptId},#{entity.deptCode},#{entity.feeBelong},#{entity.codeType},#{entity.itemOlevelId},#{entity.chargingDate},#{entity.orderSource},#{entity.importTime},#{entity.importCode},#{entity.importBy},#{entity.billDate},#{entity.showBillId},#{entity.showBillCode})
        </foreach>
    </insert>

    <select id="selSumAmountByBillIdAndSettle" resultMap="BmsPubAddedfeeMap">
        SELECT
        fee_belong,
        SUM(amount) as amount
        FROM bms_addedfee
        WHERE del_flag=0
        AND settle_type = #{settleType}
        AND bill_id IN
        <foreach collection="billInfos" item="info" open="(" separator="," close=")">
            #{info.id}
        </foreach>
        GROUP BY fee_belong
    </select>

    <select id="selSumAmountByBillIdByCur" resultMap="BmsPubAddedfeeMap">
        SELECT
            fee_belong,
            SUM(amount) as amount
        FROM bms_addedfee
        WHERE del_flag=0
          AND settle_type = #{settleType}
          AND bill_id = #{billId}
        GROUP BY fee_belong
    </select>



    <!-- 批量新增数据 -->
    <insert id="insertBatch">
        insert into bms_addedfee(id,relate_code,expenses_code,cost_status,fee_source,client_id,client_code,carrier_code,settle_type,item_id,amount,adjust_before_amount,adjust_before_remark,creat_date,warehouse_code,number,unit,bill_id,bill_code,remark,oper_by,oper_code,oper_time,del_flag,dept_id,dept_code,fee_belong,code_type,item_olevel_id,charging_date,order_source,import_time,import_code,import_by,bill_date,show_bill_id,show_bill_code)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.relateCode},#{entity.expensesCode},#{entity.costStatus},#{entity.feeSource},#{entity.clientId},#{entity.clientCode},#{entity.carrierCode},#{entity.settleType},#{entity.itemId},#{entity.amount},#{entity.adjustBeforeAmount},#{entity.adjustBeforeRemark},#{entity.creatDate},#{entity.warehouseCode},#{entity.number},#{entity.unit},#{entity.billId},#{entity.billCode},#{entity.remark},#{entity.operBy},#{entity.operCode},#{entity.operTime},#{entity.delFlag},#{entity.deptId},#{entity.deptCode},#{entity.feeBelong},#{entity.codeType},#{entity.itemOlevelId},#{entity.chargingDate},#{entity.orderSource},#{entity.importTime},#{entity.importCode},#{entity.importBy},#{entity.billDate},#{entity.showBillId},#{entity.showBillCode})
        </foreach>
    </insert>
</mapper>