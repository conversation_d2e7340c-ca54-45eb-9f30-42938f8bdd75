<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.MdmStoreinfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.MdmStoreinfo" id="MdmStoreinfoResult">
        <result property="id" column="Id"/>
        <result property="storename" column="Store_Name"/>
        <result property="storecode" column="Store_Code"/>
        <result property="shortname" column="Short_Name"/>
        <result property="linkman" column="Link_Man"/>
        <result property="linkphone" column="Link_Phone"/>
        <result property="deliveryregion" column="Delivery_Region"/>
        <result property="remark" column="Remark"/>
        <result property="address" column="Address"/>
        <result property="brandid" column="Brand_ID"/>
        <result property="companyid" column="Company_Id"/>
        <result property="usercompanyid" column="User_Company_ID"/>
        <result property="invalid" column="del_flag"/>
        <result property="overtenkm" column="Over_Ten_Km"/>
        <result property="distance" column="Distance"/>
        <result property="newsttore" column="New_Sttore"/>
        <result property="storestatus" column="Store_Status"/>
        <result property="createname" column="Create_Name"/>
        <result property="createtime" column="Create_Time"/>
        <result property="createunit" column="Create_Unit"/>
        <result property="updatename" column="Update_Name"/>
        <result property="updatetime" column="Update_Time"/>
        <result property="updateunit" column="Update_Unit"/>
        <result property="storetype" column="Store_Type"/>
        <result property="foundtime" column="Found_Time"/>
        <result property="routeid" column="Route_ID"/>
        <result property="deliveryorder" column="Delivery_Order"/>
        <result property="routename" column="Route_Name"/>
        <result property="brandname" column="Client_Name"/>
        <result property="province"  column="province"    />
        <result property="city"    column="city"    />
        <result property="area"    column="area"    />
        <result property="carOverbaseStore"    column="car_overbase_store"    />
        <result property="carOverbaseKm"    column="car_overbase_km"    />
        <result property="carOverKm"    column="car_over_km"    />
        <result property="cusOverbaseStore"    column="cus_overbase_store"    />
        <result property="cusOverbaseKm"    column="cus_overbase_km"    />
        <result property="cusOverKm"    column="cus_over_km"    />
        <result property="customerCode"    column="customer_code"    />
        <result property="deptCode"    column="dept_code"    />
        <result property="lon"    column="lon"    />
        <result property="lat"    column="lat"    />
        <result property="isEnableFence"    column="is_enable_fence"    />
        <result property="fenceRadius"    column="fence_radius"    />
        <result property="customerLines"    column="customer_lines"    />
        <result property="transportRoute"    column="transport_route"    />
        <result property="addrresAttribute"    column="addrres_attribute"    />
        <result property="customerLinesName" column="customerLinesName" />
        <result property="transportRouteName" column="transportRouteName" />
    </resultMap>
    <!--客户-->
    <resultMap type="com.bbyb.joy.bms.domain.dto.MdmClientinfo" id="MdmClientinfoResult">
        <result property="id" column="c_id"/>
        <result property="clientCode" column="Client_Code"/>
        <result property="clientName" column="Client_Name"/>
        <result property="companyId" column="Company_Id"/>
        <result property="userCompanyId" column="User_Company_Id"/>
    </resultMap>
    <!--线路-->
    <resultMap type="com.bbyb.joy.bms.domain.dto.MdmRouteinfo" id="MdmRouteinfoResult">
        <result property="id"    column="r_id"    />
        <result property="routeCode"    column="Route_Code"    />
        <result property="routeName"    column="Route_Name"    />
    </resultMap>
    <sql id="selectMdmStoreinfoVo">
        SELECT
            s.Id,
            s.Store_Name,
            s.Store_Code,
            s.Short_Name,
            s.Link_Man,
            s.Link_Phone,
            s.Delivery_Region,
            s.Remark,
            s.Address,
            s.Brand_ID,
            s.Company_Id,
            s.User_Company_ID,
            s.del_flag,
            s.Store_Status,
            s.Create_Name,
            s.Create_Time,
            s.Create_Unit,
            s.Update_Name,
            s.Update_Time,
            s.Update_Unit,
            s.Store_Type,
            s.Found_Time,
            s.Route_ID,
            s.Delivery_Order,
            s.province,
            s.city,
            s.area,
            s.car_overbase_store,
            s.car_overbase_km,
            s.car_over_km,
            s.cus_overbase_store,
            s.cus_overbase_km,
            s.cus_over_km,
            c.ID as c_id,
            c.Client_Name,
            c.Client_Code,
            c.User_Company_Id,
            c.Company_Id,
            rc.Route_Name customerLinesName,
            rt.Route_Name transportRouteName,
            s.addrres_Attribute
        FROM
            mdm_storeinfo AS s
        LEFT Join bms_clientinfo c on s.Brand_ID=c.ID and c.del_flag=0
        LEFT JOIN bms_routeinfo rc on s.customer_lines=rc.route_code and rc.del_flag=0
        LEFT JOIN bms_routeinfo rt on s.transport_route=rt.route_code and rt.del_flag=0
  </sql>

    <select id="selectMdmStoreinfoList" parameterType="com.bbyb.joy.bms.domain.dto.MdmStoreinfo" resultMap="MdmStoreinfoResult">
        <include refid="selectMdmStoreinfoVo"/>
        <where>
            <if test="storename != null  and storename != ''">and Store_Name like concat('%', #{storename}, '%')</if>
            <if test="storecode != null  and storecode != ''">and Store_Code like concat('%',#{storecode},'%')</if>
            <if test="shortname != null  and shortname != ''">and Short_Name like concat('%', #{shortname}, '%')</if>
            <if test="linkman != null  and linkman != ''">and lin_kman like concat('%', #{linkman}, '%')</if>
            <if test="linkphone != null  and linkphone != ''">and link_phone like concat('%', #{linkphone}, '%')</if>
            <if test="deliveryregion != null  and deliveryregion != ''">and Delivery_Region like
                concat('%',#{deliveryregion},'%')
            </if>
            <if test="clientList != null">
                and  c.client_code in
                <foreach collection="clientList" item="client" open="(" separator="," close=")">
                    #{client}
                </foreach>
            </if>
            <if test="remark != null  and remark != ''">and Remark like concat('%',#{remark},'%')</if>
            <if test="address != null  and address != ''">and s.Address like concat('%',#{address},'%')</if>
            <if test="brandid != null ">and Brand_ID = #{brandid}</if>
            <if test="companyid != null ">and s.Company_Id = #{companyid}</if>
            <if test="companyids != null and companyids.length!=0  ">
            and s.Company_Id in
                <foreach collection="companyids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="usercompanyid != null  and usercompanyid != ''">and User_Company_ID = #{usercompanyid}</if>
            <if test="invalid != null ">and del_flag = #{invalid}</if>
            <if test="overtenkm != null ">and Over_Ten_Km = #{overtenkm}</if>
            <if test="distance != null ">and Distance = #{distance}</if>
            <if test="newsttore != null ">and New_Sttore = #{newsttore}</if>
            <if test="storestatus != null ">and Store_Status = #{storestatus}</if>
            <if test="createname != null  and createname != ''">and Create_Name like concat('%', #{createname}, '%')</if>
            <if test="createtime != null ">and Create_Time = #{createtime}</if>
            <if test="createunit != null ">and Create_Unit = #{createunit}</if>
            <if test="updatename != null  and updatename != ''">and Update_Name like concat('%', #{updatename}, '%')</if>
            <if test="updatetime != null ">and Update_Time = #{updatetime}</if>
            <if test="updateunit != null ">and Update_Unit = #{updateunit}</if>
            <if test="storetype != null ">and Store_Type = #{storetype}</if>
            <if test="params.beginFoundtime != null and params.beginFoundtime != '' and params.endFoundtime != null and params.endFoundtime != ''">
                and Found_Time between #{params.beginFoundtime} and #{params.endFoundtime}
            </if>
            <if test="routeid != null ">and Route_ID = #{routeid}</if>
            <if test="deliveryorder != null  and deliveryorder != ''">and Delivery_Order = #{deliveryorder}</if>
            <if test="routename!=null and routename!=''">and Route_Name like concat('%',#{routename},'%') </if>
            <if test="brandname!=null and brandname!=''">and Client_Name like concat('%',#{brandname},'%') </if>
            <if test="addrresAttribute!=null">
                and addrres_attribute=#{addrresAttribute}
            </if>
            <if test="province!=null and province!=''">
               and (s.province like concat('%',#{province},'%')
               or s.city like concat('%',#{province},'%')
               or s.area like concat('%',#{province},'%') )
            </if>
            and s.del_flag=0
        </where>
        order by s.id desc
    </select>

    <select id="selectMdmStoreinfoById" parameterType="java.lang.String" resultMap="MdmStoreinfoResult">
        <include refid="selectMdmStoreinfoVo"/>
        where s.Id = #{id}
    </select>

    <insert id="insertMdmStoreinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmStoreinfo" useGeneratedKeys="true" keyProperty="id">
        insert into mdm_storeinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="storename != null">store_name,</if>
            <if test="storecode != null">store_code,</if>
            <if test="shortname != null">short_name,</if>
            <if test="linkman != null">link_man,</if>
            <if test="linkphone != null">link_phone,</if>
            <if test="deliveryregion != null">delivery_region,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="remark != null">remark,</if>
            <if test="area != null">area,</if>
            <if test="address != null">address,</if>
            <if test="brandid != null">brand_id,</if>
            <if test="companyid != null">company_id,</if>
            <if test="usercompanyid != null">user_company_id,</if>
            <if test="storestatus != null">store_status,</if>
            <if test="createname != null">create_name,</if>
            <if test="createtime != null">create_time,</if>
            <if test="createunit != null">create_unit,</if>
            <if test="updatename != null">update_name,</if>
            <if test="updatetime != null">update_time,</if>
            <if test="updateunit != null">update_unit,</if>
            <if test="storetype != null">store_type,</if>
            <if test="foundtime != null">found_time,</if>
            <if test="addrresAttribute != null">addrres_attribute,</if>
            <if test="routeid != null">route_id,</if>
            <if test="deliveryorder != null">delivery_order,</if>
            <if test="carOverbaseStore != null">car_overbase_store,</if>
            <if test="carOverbaseKm != null">car_overbase_km,</if>
            <if test="carOverKm != null">car_over_km,</if>
            <if test="cusOverbaseStore != null">cus_overbase_store,</if>
            <if test="cusOverbaseKm != null">cus_overbase_km,</if>
            <if test="cusOverKm != null">cus_over_km,</if>
            <if test="customerCode != null">customer_code,</if>
            <if test="deptCode != null">dept_code,</if>
            <if test="lon != null">lon,</if>
            <if test="lat != null">lat,</if>
            <if test="isEnableFence != null">is_enable_fence,</if>
            <if test="fenceRadius != null">fence_radius,</if>
            <if test="customerLines != null">customer_lines,</if>
            <if test="transportRoute != null">transport_route,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="storename != null">#{storename},</if>
            <if test="storecode != null">#{storecode},</if>
            <if test="shortname != null">#{shortname},</if>
            <if test="linkman != null">#{linkman},</if>
            <if test="linkphone != null">#{linkphone},</if>
            <if test="deliveryregion != null">#{deliveryregion},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="remark != null">#{remark},</if>
            <if test="area != null">#{area},</if>
            <if test="address != null">#{address},</if>
            <if test="brandid != null">#{brandid},</if>
            <if test="companyid != null">#{companyid},</if>
            <if test="usercompanyid != null">#{usercompanyid},</if>
            <if test="storestatus != null">#{storestatus},</if>
            <if test="createname != null">#{createname},</if>
            <if test="createtime != null">#{createtime},</if>
            <if test="createunit != null">#{createunit},</if>
            <if test="updatename != null">#{updatename},</if>
            <if test="updatetime != null">#{updatetime},</if>
            <if test="updateunit != null">#{updateunit},</if>
            <if test="storetype != null">#{storetype},</if>
            <if test="foundtime != null">#{foundtime},</if>
            <if test="addrresAttribute != null">#{addrresAttribute},</if>
            <if test="routeid != null">#{routeid},</if>
            <if test="deliveryorder != null">#{deliveryorder},</if>
            <if test="carOverbaseStore != null">#{carOverbaseStore},</if>
            <if test="carOverbaseKm != null">#{carOverbaseKm},</if>
            <if test="carOverKm != null">#{carOverKm},</if>
            <if test="cusOverbaseStore != null">#{cusOverbaseStore},</if>
            <if test="cusOverbaseKm != null">#{cusOverbaseKm},</if>
            <if test="cusOverKm != null">#{cusOverKm},</if>
            <if test="customerCode != null">#{customerCode},</if>
            <if test="deptCode != null">#{deptCode},</if>
            <if test="lon != null">#{lon},</if>
            <if test="lat != null">#{lat},</if>
            <if test="isEnableFence != null">#{isEnableFence},</if>
            <if test="fenceRadius != null">#{fenceRadius},</if>
            <if test="customerLines != null">#{customerLines},</if>
            <if test="transportRoute != null">#{transportRoute},</if>
        </trim>
    </insert>

    <update id="updateMdmStoreinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmStoreinfo">
        update mdm_storeinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="storename != null">store_name = #{storename},</if>
            <if test="storecode != null">store_code = #{storecode},</if>
            <if test="shortname != null">short_name = #{shortname},</if>
            <if test="linkman != null">link_man = #{linkman},</if>
            <if test="linkphone != null">link_phone = #{linkphone},</if>
            <if test="deliveryregion != null">delivery_region = #{deliveryregion},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="area != null">area = #{area},</if>
            <if test="address != null">address = #{address},</if>
            <if test="brandid != null">brand_id = #{brandid},</if>
            <if test="companyid != null">company_id = #{companyid},</if>
            <if test="usercompanyid != null">user_company_id = #{usercompanyid},</if>
            <if test="invalid != null">del_flag = #{invalid},</if>
            <if test="storestatus != null">store_status = #{storestatus},</if>
            <if test="createname != null">create_name = #{createname},</if>
            <if test="createtime != null">create_time = #{createtime},</if>
            <if test="createunit != null">create_unit = #{createunit},</if>
            <if test="updatename != null">update_name = #{updatename},</if>
            <if test="updatetime != null">update_time = #{updatetime},</if>
            <if test="updateunit != null">update_unit = #{updateunit},</if>
            <if test="storetype != null">store_type = #{storetype},</if>
            <if test="foundtime != null">found_time = #{foundtime},</if>
            <if test="addrresAttribute != null">addrres_attribute = #{addrresAttribute},</if>
            <if test="routeid != null">route_id = #{routeid},</if>
            <if test="deliveryorder != null">delivery_order = #{deliveryorder},</if>
            <if test="carOverbaseStore != null">car_overbase_store = #{carOverbaseStore},</if>
            <if test="carOverbaseKm != null">car_overbase_km = #{carOverbaseKm},</if>
            <if test="carOverKm != null">car_over_km = #{carOverKm},</if>
            <if test="cusOverbaseStore != null">cus_overbase_store = #{cusOverbaseStore},</if>
            <if test="cusOverbaseKm != null">cus_overbase_km = #{cusOverbaseKm},</if>
            <if test="cusOverKm != null">cus_over_km = #{cusOverKm},</if>
            <if test="customerCode != null">customer_code = #{customerCode},</if>
            <if test="deptCode != null">dept_code = #{deptCode},</if>
            <if test="lon != null">lon = #{lon},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="isEnableFence != null">is_enable_fence = #{isEnableFence},</if>
            <if test="fenceRadius != null">fence_radius = #{fenceRadius},</if>
            <if test="customerLines != null">customer_lines = #{customerLines},</if>
            <if test="transportRoute != null">transport_route = #{transportRoute},</if>
        </trim>
        where Id = #{id}
    </update>

    <delete id="deleteMdmStoreinfoById" parameterType="java.lang.String">
        delete from mdm_storeinfo where Id = #{id}
    </delete>

    <delete id="deleteMdmStoreinfoByIds">
        delete from mdm_storeinfo where Id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateMdmStoreinfoStatusByIds">
        update mdm_storeinfo set status = #{status} where Id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="checkStoreCodeUnique" parameterType="java.lang.String" resultMap="MdmStoreinfoResult">
        select id, store_code from mdm_storeinfo where del_flag = 0 and store_code=#{storecode} limit 1
    </select>

    <select id="selectStoreinfoList" resultMap="MdmStoreinfoResult">
        SELECT
        s.Id,
        s.Store_Name,
        s.Store_Code,
        s.Short_Name,
        s.Link_Man,
        s.Link_Phone,
        s.Delivery_Region,
        s.Remark,
        s.Address,
        s.Brand_ID,
        s.Company_Id,
        s.User_Company_ID,
        s.del_flag,
        s.Store_Status,
        s.Create_Name,
        s.Create_Time,
        s.Create_Unit,
        s.Update_Name,
        s.Update_Time,
        s.Update_Unit,
        s.Store_Type,
        s.Found_Time,
        s.Route_ID,
        s.Delivery_Order,
        s.province,
        s.city,
        s.area,
        s.car_overbase_store,
        s.car_overbase_km,
        s.car_over_km,
        s.cus_overbase_store,
        s.cus_overbase_km,
        s.cus_over_km
        FROM
        mdm_storeinfo AS s
        where s.del_flag=0
        <if test="ids != null and ids.size>0 ">
            AND s.id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
        <if test="codes != null and codes.size>0 ">
            AND s.store_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
        <if test="addrresAttribute != null and addrresAttribute!='' ">
            AND s.addrres_attribute = #{addrresAttribute}
        </if>
    </select>

    <select id="selectStoreinfoListByNames" resultMap="MdmStoreinfoResult">
        SELECT
        s.Id,
        s.Store_Name,
        s.Store_Code,
        s.Short_Name,
        s.Link_Man,
        s.Link_Phone,
        s.Delivery_Region,
        s.Remark,
        s.Address,
        s.Brand_ID,
        s.Company_Id,
        s.User_Company_ID,
        s.del_flag,
        s.Store_Status,
        s.Create_Name,
        s.Create_Time,
        s.Create_Unit,
        s.Update_Name,
        s.Update_Time,
        s.Update_Unit,
        s.Store_Type,
        s.Found_Time,
        s.Route_ID,
        s.Delivery_Order,
        s.province,
        s.city,
        s.area,
        s.car_overbase_store,
        s.car_overbase_km,
        s.car_over_km,
        s.cus_overbase_store,
        s.cus_overbase_km,
        s.cus_over_km
        FROM
        mdm_storeinfo AS s
        where s.del_flag=0
        <if test="addrresAttribute != null and addrresAttribute!='' ">
            AND s.addrres_attribute = #{addrresAttribute}
        </if>
        <if test="names != null and names.size>0 ">
            AND s.Store_Name in
            <foreach collection="names" item="names" open="(" separator="," close=")">
                #{names}
            </foreach>
            group by s.Store_Name
        </if>
    </select>


</mapper>