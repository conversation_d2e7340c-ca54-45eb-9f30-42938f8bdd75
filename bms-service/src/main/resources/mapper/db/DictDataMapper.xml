<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.DictDataMapper">
	

	<resultMap type="com.bbyb.joy.bms.support.utils.DictUtilsBean" id="DictUtilsBeanMap">
		<id     property="dictCode"   column="dict_code"   />
		<result property="dictSort"   column="dict_sort"   />
		<result property="dictLabel"  column="dict_label"  />
		<result property="dictValue"  column="dict_value"  />
		<result property="dictType"   column="dict_type"   />
		<result property="dictName" column="dict_name" />
	</resultMap>
	<sql id="selectDictDataVoJoinType">
        select d.dict_code, d.dict_sort, d.dict_label, d.dict_value, d.dict_type, d.is_default, d.status, d.create_by, d.create_time, d.remark
		from sys_dict_data d
		LEFT JOIN sys_dict_type t ON t.dict_type=d.dict_type
    </sql>
<!--关联类型表-->
	<select id="getDictDataByName" parameterType="com.bbyb.joy.bms.support.utils.DictUtilsBean" resultMap="DictUtilsBeanMap">
		<include refid="selectDictDataVoJoinType"/>
		where t.dict_type in (${dictType}) and d.status=0 and t.status=0
	</select>


	
</mapper> 