<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsPubFloatfeeRulerMainMapper">

<insert id="insert" parameterType="com.bbyb.joy.bms.domain.dto.BmsPubFloatfeeRuleMain" >
    insert into
        pub_floatfee_rule_main(id,rule_name,ratio,is_enable,oper_by,oper_code,oper_time,created_by,created_time,updated_by,updated_time,order_type)
    values
    (#{id},#{ruleName},#{ratio},#{isEnable},#{operBy},#{operCode},#{operTime},#{createdBy},#{createdTime},#{updateBy},#{updateTime},#{orderType});
</insert>


    <update id="modify" parameterType="com.bbyb.joy.bms.domain.dto.BmsPubFloatfeeRuleMain">
        update pub_floatfee_rule_main
        <set>
            <if test="ruleName != null and ruleName != ''">
                rule_name = #{ruleName},
            </if>
            <if test="ratio != null and ratio != ''">
                ratio = #{ratio},
            </if>
            <if test="isEnable != null and isEnable != ''">
                is_enable = #{isEnable},
            </if>
            <if test="operBy != null and operBy != ''">
                oper_by = #{operBy},
            </if>
            <if test="operCode != null and operCode != ''">
                oper_code = #{operCode},
            </if>
            <if test="operTime != null">
                oper_time = #{operTime},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime},
            </if>
            <if test="delFlag !=null and delFlag !=''">
                del_flag =#{delFlag}
            </if>
            <if test="orderType !=null ">
                order_type=#{orderType}
            </if>
        </set>
        where id = #{id};
        <if test="orderType !=null and  orderType==2 ">
            update pub_floatfee_rule_main set ratio = NULL where id = #{id};
        </if>
    </update>

    <update id="delBatch" parameterType="java.util.Map">
            update pub_floatfee_rule_main
            set  del_flag =1
            where   id in
         <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
         </foreach>

    </update>

    <select id="queryDetail" resultType="com.bbyb.joy.bms.domain.dto.BmsPubFloatfeeRuleMain">
        select  id,rule_name as ruleName,
                ratio,is_enable as isEnable,oper_by as operBy,oper_code as operCode,oper_time as
                    operTime,created_by as createdBy,created_time as createdTime,updated_by as updateBy,updated_time as updateTime, pfrm.order_type     as orderType,
                case
                    when order_type = 1 then '按比例'
                    when order_type = 2 then '按金额'
                    else ''
                    end as orderTypeDesc
        from
            pub_floatfee_rule_main pfrm
        where id=#{id}
          and del_flag=0;
    </select>

    <select id="queryRatioCount" resultType="String">
        select  main.id from
            pub_floatfee_rule_main main left join pub_floatfee_rule rule on main.id=rule.rule_main_id
        where main.ratio=#{ratio}
         and rule.rule_type=#{ruleType}
        and main.del_flag=0
        and rule.del_flag=0
        and main.is_enable=0
        <if test="id !=null and id !=''">
        and main.id <![CDATA[<>]]> #{id}
        </if>
        <if test="orderType !=null ">
            and main.order_type=#{orderType}
        </if>
        and rule.end_date>now()
    </select>

    <select id="queryDetailByIds" resultType="com.bbyb.joy.bms.domain.dto.BmsPubFloatfeeRuleMain">
        select  id,rule_name as ruleName,
                ratio,is_enable as isEnable,oper_by as operBy,oper_code as operCode,oper_time as
                    operTime,created_by as createdBy,created_time as createdTime,updated_by as updateBy,updated_time as updateTime from
            pub_floatfee_rule_main
        where id in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
          and del_flag=0;
    </select>

</mapper>
