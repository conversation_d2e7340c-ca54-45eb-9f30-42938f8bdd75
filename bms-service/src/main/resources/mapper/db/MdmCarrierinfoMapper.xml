<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.MdmCarrierinfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.MdmCarrierinfo" id="MdmCarrierinfoResult">
        <result property="id"    column="id"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="carrierType"    column="carrier_type"    />
        <result property="costDateDimension"    column="cost_date_dimension"    />
        <result property="carrierPaytype"    column="carrier_paytype"    />
        <result property="transportType"    column="transport_type"    />
        <result property="companyid"    column="companyid"    />
        <result property="linkMan"    column="link_man"    />
        <result property="linkMobile"    column="link_mobile"    />
        <result property="checkName"    column="check_name"    />
        <result property="remark"    column="remark"    />
        <result property="writeuserCode"    column="writeuser_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateuserCode"    column="updateuser_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="updateTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="carrierProvince"    column="carrier_province"    />
        <result property="carrierCity"    column="carrier_city"    />
        <result property="carrierArea"    column="carrier_area"    />
        <result property="carrierAddress"    column="carrier_address"    />
        <result property="isenable"    column="is_enable"    />
        <result property="deliveryfeeComparison"    column="deliveryfee_comparison"    />
        <result property="accountperi"    column="accountperi"    />
        <result property="billingLogic"    column="billing_logic"    />
        <result property="invoiceheader"    column="invoiceheader"    />
        <result property="dutyparagraph"    column="dutyparagraph"    />
        <result property="phone"    column="phone"    />
        <result property="bankofdeposit"    column="bankofdeposit"    />
        <result property="bankaccou"    column="bankaccou"    />
        <result property="taxrate"    column="taxrate"    />
        <result property="address"    column="address"    />
        <result property="area"    column="area"    />
        <result property="deptCode"    column="dept_code"    />
        <result property="defaultTransportType"    column="default_transport_type"    />
        <result property="monthlyCode" column="monthly_code" />
        <result property="settleSetting" column="settle_setting"  />
        <result property="shareType" column="share_type" />
        <result property="isToClient" column="is_to_client" />
    </resultMap>

    <sql id="selectMdmCarrierinfoVo">
        select id, carrier_code, carrier_name, carrier_type,cost_date_dimension,
        carrier_paytype, transport_type, companyid,
        link_man, link_mobile, check_name, remark, writeuser_code,
        create_by, create_dept_id, create_time, updateuser_code, oper_by,
         oper_dept_id, oper_time, del_flag,
        CONCAT(carrier_province,carrier_city,carrier_area) AS Area,
         carrier_address, is_enable, deliveryfee_comparison, accountperi,billing_logic,
         invoiceheader, dutyparagraph, phone, bankofdeposit, bankaccou, taxrate, address,monthly_code,settle_setting,share_type,is_to_client
         from bms_carrierinfo
    </sql>

    <select id="selectMdmCarrierinfoList" parameterType="com.bbyb.joy.bms.domain.dto.MdmCarrierinfo" resultMap="MdmCarrierinfoResult">
        <include refid="selectMdmCarrierinfoVo"/>
        <where>
            <if test="isToClient != null">and is_to_client = #{isToClient}</if>
            <if test="carrierCode  != null  and carrierCode != ''"> and carrier_code  like concat('%', #{carrierCode},'%') </if>
            <if test="carrierName != null  and carrierName != ''"> and carrier_name  like concat('%', #{carrierName}, '%')</if>
            <if test="carrierType != null "> and carrier_type  = #{carrierType}</if>
            <if test="companyIds!=null and companyIds!=''">
               and  companyid in
                <foreach collection="companyIds.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="carrierList != null and carrierList.size!=0 ">
                and  carrier_code in
                <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                    #{carrier}
                </foreach>
            </if>
        </where>
        order by oper_time desc
    </select>

    <select id="selectMdmCarrierinfoById" parameterType="java.lang.String" resultMap="MdmCarrierinfoResult">
        <include refid="selectMdmCarrierinfoVo"/>
        where id = #{id}
    </select>
    <select id="selectMdmCarrierinfoByCode" parameterType="java.lang.String" resultMap="MdmCarrierinfoResult">
        <include refid="selectMdmCarrierinfoVo"/>
        where carrier_code = #{code}
        and IFNULL(del_flag,0)=0
        and IFNULL(is_enable,0)=0
    </select>

    <select id="selectBillDateAndRuleList" parameterType="java.lang.String" resultMap="MdmCarrierinfoResult">
        <include refid="selectMdmCarrierinfoVo"/>
        where
        IFNULL(accountperi,'')!=''
        and IFNULL(billing_logic,0)!=0
        and IFNULL(del_flag,1)=0
        and IFNULL(is_enable,0)=0
    </select>

    <update id="updateMdmCarrierinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmCarrierinfo">
        update bms_carrierinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="carrierCode != null">carrier_code = #{carrierCode},</if>
            <if test="carrierName != null">carrier_name = #{carrierName},</if>
            <if test="carrierType != null">carrier_type = #{carrierType},</if>
            <if test="costDateDimension != null">cost_date_dimension = #{costDateDimension},</if>
            <if test="carrierPaytype != null">carrier_paytype = #{carrierPaytype},</if>
            <if test="transportType != null">transport_type = #{transportType},</if>
            <if test="companyid != null">companyid = #{companyid},</if>
            <if test="linkMan != null">link_man = #{linkMan},</if>
            <if test="linkMobile != null">link_mobile = #{linkMobile},</if>
            <if test="checkName != null">check_name = #{checkName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="writeuserCode != null">writeuser_code = #{writeuserCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateuserCode != null">updateuser_code = #{updateuserCode},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operTime != null">oper_dept_id = #{operDeptId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="carrierProvince != null">carrier_province = #{carrierProvince},</if>
            <if test="carrierCity != null">carrier_city = #{carrierCity},</if>
            <if test="carrierArea != null">carrier_area = #{carrierArea},</if>
            <if test="carrierAddress != null">carrier_address = #{carrierAddress},</if>
            <if test="isenable != null">is_enable = #{isenable},</if>
            <if test="deliveryfeeComparison != null">deliveryfee_comparison = #{deliveryfeeComparison},</if>
            <if test="accountperi != null">accountperi = #{accountperi},</if>
            <if test="billingLogic != null">billing_logic = #{billingLogic},</if>
            <if test="invoiceheader != null">invoiceheader = #{invoiceheader},</if>
            <if test="dutyparagraph != null">dutyparagraph = #{dutyparagraph},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="bankofdeposit != null">bankofdeposit = #{bankofdeposit},</if>
            <if test="bankaccou != null">bankaccou = #{bankaccou},</if>
            <if test="taxrate != null">taxrate = #{taxrate},</if>
            <if test="address != null">address = #{address},</if>
            <if test="deptCode != null">dept_code = #{deptCode},</if>
            <if test="defaultTransportType != null">default_transport_type = #{defaultTransportType},</if>
            <if test="settleSetting!=null">settle_setting=#{settleSetting},</if>
            <if test="shareType!=null">share_type=#{shareType},</if>
            <if test="isToClient != null">is_to_client = #{isToClient},</if>
            oper_time=sysdate()
        </trim>
        where id = #{id}
    </update>

    <insert id="insertMdmCarrierinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmCarrierinfo" useGeneratedKeys="true" keyProperty="id">
        insert into bms_carrierinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="carrierCode != null">carrier_code,</if>
            <if test="carrierName != null">carrier_name,</if>
            <if test="carrierType != null">carrier_type,</if>
            <if test="carrierPaytype != null">carrier_paytype,</if>
            <if test="transportType != null">transport_type,</if>
            <if test="companyid != null">companyid,</if>
            <if test="linkMan != null">link_man,</if>
            <if test="linkMobile != null">link_mobile,</if>
            <if test="checkName != null">check_name,</if>
            <if test="remark != null">remark,</if>
            <if test="writeuserCode != null">writeuser_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateuserCode != null">updateuser_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="carrierProvince != null">carrier_province,</if>
            <if test="carrierCity != null">carrier_city,</if>
            <if test="carrierArea != null">carrier_area,</if>
            <if test="carrierAddress != null">carrier_address,</if>
            <if test="isenable != null">is_enable,</if>
            <if test="deliveryfeeComparison != null">deliveryfee_comparison,</if>
            <if test="billingLogic != null">billing_logic,</if>
            <if test="accountperi != null">accountperi,</if>
            <if test="invoiceheader != null">invoiceheader,</if>
            <if test="dutyparagraph != null">dutyparagraph,</if>
            <if test="phone != null">phone,</if>
            <if test="bankofdeposit != null">bankofdeposit,</if>
            <if test="bankaccou != null">bankaccou,</if>
            <if test="taxrate != null">taxrate,</if>
            <if test="address != null">address,</if>
            <if test="deptCode != null">dept_code,</if>
            <if test="defaultTransportType != null">default_transport_type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="settleSetting!=null">settle_setting,</if>
            <if test="shareType!=null">share_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="carrierCode != null">#{carrierCode},</if>
            <if test="carrierName != null">#{carrierName},</if>
            <if test="carrierType != null">#{carrierType},</if>
            <if test="carrierPaytype != null">#{carrierPaytype},</if>
            <if test="transportType != null">#{transportType},</if>
            <if test="companyid != null">#{companyid},</if>
            <if test="linkMan != null">#{linkMan},</if>
            <if test="linkMobile != null">#{linkMobile},</if>
            <if test="checkName != null">#{checkName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="writeuserCode != null">#{writeuserCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateuserCode != null">#{updateuserCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="carrierProvince != null">#{carrierProvince},</if>
            <if test="carrierCity != null">#{carrierCity},</if>
            <if test="carrierArea != null">#{carrierArea},</if>
            <if test="carrierAddress != null">#{carrierAddress},</if>
            <if test="isenable != null">#{isenable},</if>
            <if test="deliveryfeeComparison != null">#{deliveryfeeComparison},</if>
            <if test="billingLogic != null">#{billingLogic},</if>
            <if test="accountperi != null">#{accountperi},</if>
            <if test="invoiceheader != null">#{invoiceheader},</if>
            <if test="dutyparagraph != null">#{dutyparagraph},</if>
            <if test="phone != null">#{phone},</if>
            <if test="bankofdeposit != null">#{bankofdeposit},</if>
            <if test="bankaccou != null">#{bankaccou},</if>
            <if test="taxrate != null">#{taxrate},</if>
            <if test="address != null">#{address},</if>
            <if test="deptCode != null">#{deptCode},</if>
            <if test="defaultTransportType != null">#{defaultTransportType},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="settleSetting!=null">#{settleSetting},</if>
            <if test="shareType!=null">#{shareType},</if>
        </trim>
    </insert>

    <select id="selectCarrierinfoList" resultMap="MdmCarrierinfoResult">
        <include refid="selectMdmCarrierinfoVo"/>
        where del_flag=0
        <if test="ids != null and ids.size>0 ">
            AND id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
        <if test="codes != null and codes.size>0 ">
            AND carrier_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
        <if test="names != null and names.size>0 ">
            AND carrier_name in
            <foreach collection="names" item="names" open="(" separator="," close=")">
                #{names}
            </foreach>
        </if>
    </select>

    <select id="checkCarrierCodeUnique" parameterType="java.lang.String" resultMap="MdmCarrierinfoResult">
        select id, carrier_code from bms_carrierinfo where carrier_code=#{carrierCode} limit 1
    </select>



    <resultMap type="com.bbyb.joy.bms.domain.dto.model.MdmCarrierinfoModel" id="MdmCarrierModelResult">
        <result property="id"    column="id"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="carrierType"    column="carrier_type"    />
        <result property="carrierPaytype"    column="carrier_paytype"    />
        <result property="transportType"    column="transport_type"    />
        <result property="companyid"    column="companyid"    />
        <result property="linkMan"    column="link_man"    />
        <result property="linkMobile"    column="link_mobile"    />
        <result property="checkName"    column="check_name"    />
        <result property="remark"    column="remark"    />
        <result property="writeuserCode"    column="writeuser_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateuserCode"    column="updateuser_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="updateTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="carrierProvince"    column="carrier_province"    />
        <result property="carrierCity"    column="carrier_city"    />
        <result property="carrierArea"    column="carrier_area"    />
        <result property="carrierAddress"    column="carrier_address"    />
        <result property="isenable"    column="is_enable"    />
        <result property="deliveryfeeComparison"    column="deliveryfee_comparison"    />
        <result property="accountperi"    column="accountperi"    />
        <result property="billingLogic"    column="billing_logic"    />
        <result property="invoiceheader"    column="invoiceheader"    />
        <result property="dutyparagraph"    column="dutyparagraph"    />
        <result property="phone"    column="phone"    />
        <result property="bankofdeposit"    column="bankofdeposit"    />
        <result property="bankaccou"    column="bankaccou"    />
        <result property="taxrate"    column="taxrate"    />
        <result property="address"    column="address"    />
        <result property="area"    column="area"    />
        <result property="deptCode"    column="dept_code"    />
        <result property="defaultTransportType"    column="default_transport_type"    />
        <result property="monthlyCode" column="monthly_code" />
        <result property="settleSetting" column="settle_setting"  />
        <result property="shareType" column="share_type" />
        <result property="isToClient" column="is_to_client" />
    </resultMap>


    <select id="selectMdmCarrierMapsinfoByCarrierForYf" resultMap="MdmCarrierModelResult">
        <if test="codeType!=null and codeType == 1">
            select
            t1.carrier_code,
            t1.carrier_name,
            t1.monthly_code,
            t1.carrier_type,
            t1.carrier_paytype,
            t1.is_to_client,
            t1.transport_type,
            t1.companyid,
            t1.link_man,
            t1.link_mobile,
            t1.check_name,
            t1.remark,
            t1.writeuser_code,
            t1.create_by,
            t1.create_dept_id,
            t1.create_time,
            t1.updateuser_code,
            t1.oper_by,
            t1.oper_dept_id,
            t1.oper_time,
            t1.del_flag,
            t1.carrier_province,
            t1.carrier_city,
            t1.carrier_area,
            t1.carrier_address,
            t1.is_enable,
            t1.deliveryfee_comparison,
            t1.billing_logic,
            t1.accountperi,
            t1.invoiceheader,
            t1.dutyparagraph,
            t1.phone,
            t1.bankofdeposit,
            t1.bankaccou,
            t1.taxrate,
            t1.address,
            t1.dept_code,
            t1.default_transport_type,
            t1.settle_setting,
            t1.share_type,
            t2.id AS workId,
            t2.virtual_order_no AS workCode
            from bms_carrierinfo t1
            join bms_yfbillcodeinfo t2 on t2.carrier_code = t1.carrier_code and t2.del_flag = 0
            <where>
                <if test="codes!=null and codes.size>0">
                    AND t2.virtual_order_no IN
                    <foreach collection="codes" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                </if>
                <if test="ids!=null and ids.size>0">
                    AND t2.id IN
                    <foreach collection="ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </where>
        </if>
        <if test="codeType!=null and codeType in {2,3}">
            select
            t1.carrier_code,
            t1.carrier_name,
            t1.monthly_code,
            t1.carrier_type,
            t1.carrier_paytype,
            t1.is_to_client,
            t1.transport_type,
            t1.companyid,
            t1.link_man,
            t1.link_mobile,
            t1.check_name,
            t1.remark,
            t1.writeuser_code,
            t1.create_by,
            t1.create_dept_id,
            t1.create_time,
            t1.updateuser_code,
            t1.oper_by,
            t1.oper_dept_id,
            t1.oper_time,
            t1.del_flag,
            t1.carrier_province,
            t1.carrier_city,
            t1.carrier_area,
            t1.carrier_address,
            t1.is_enable,
            t1.deliveryfee_comparison,
            t1.billing_logic,
            t1.accountperi,
            t1.invoiceheader,
            t1.dutyparagraph,
            t1.phone,
            t1.bankofdeposit,
            t1.bankaccou,
            t1.taxrate,
            t1.address,
            t1.dept_code,
            t1.default_transport_type,
            t1.settle_setting,
            t1.share_type,
            t2.id AS workId,
            t2.relate_code AS workCode
            from bms_carrierinfo t1
            join bms_yfstock_codeinfo t2 on t2.carrier_code = t1.carrier_code
            <where>
                <if test="codes!=null and codes.size>0">
                    AND t2.relate_code IN
                    <foreach collection="codes" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                </if>
                <if test="ids!=null and ids.size>0">
                    AND t2.id IN
                    <foreach collection="ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </where>
        </if>
        <if test="codeType!=null and codeType == 4">
            select
            t1.carrier_code,
            t1.carrier_name,
            t1.monthly_code,
            t1.carrier_type,
            t1.carrier_paytype,
            t1.is_to_client,
            t1.transport_type,
            t1.companyid,
            t1.link_man,
            t1.link_mobile,
            t1.check_name,
            t1.remark,
            t1.writeuser_code,
            t1.create_by,
            t1.create_dept_id,
            t1.create_time,
            t1.updateuser_code,
            t1.oper_by,
            t1.oper_dept_id,
            t1.oper_time,
            t1.del_flag,
            t1.carrier_province,
            t1.carrier_city,
            t1.carrier_area,
            t1.carrier_address,
            t1.is_enable,
            t1.deliveryfee_comparison,
            t1.billing_logic,
            t1.accountperi,
            t1.invoiceheader,
            t1.dutyparagraph,
            t1.phone,
            t1.bankofdeposit,
            t1.bankaccou,
            t1.taxrate,
            t1.address,
            t1.dept_code,
            t1.default_transport_type,
            t1.settle_setting,
            t1.share_type,
            t2.id AS workId,
            t2.stock_code AS workCode
            from bms_carrierinfo t1
            join bms_yfstockinfo t2 on t2.carrier_code = t1.carrier_code
            <where>
                <if test="codes!=null and codes.size>0">
                    AND t2.stock_code IN
                    <foreach collection="codes" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                </if>
                <if test="ids!=null and ids.size>0">
                    AND t2.id IN
                    <foreach collection="ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </where>
        </if>
    </select>


</mapper>