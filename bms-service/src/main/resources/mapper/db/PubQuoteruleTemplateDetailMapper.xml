<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubQuoteruleTemplateDetailMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.PubQuoteruleTemplateDetail" id="PubQuoteruleTemplatedetailResult">
        <result property="id"    column="id"    />
        <result property="quoteRuleTemplateId"    column="quoterule_template_id"    />
        <result property="filedSetingId"    column="filedseting_id"    />
        <result property="fieldChinese"    column="field_chinese"    />
        <result property="fieldEnglish"    column="field_english"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="templateCode"    column="template_code"    />
        <result property="anotherName"    column="another_name"    />
    </resultMap>

    <select id="selectPubQuoteruleTemplateDetailList" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoteruleTemplateDetail" resultMap="PubQuoteruleTemplatedetailResult">
        select id, quoterule_template_id, filedseting_id, field_chinese, field_english, oper_by, oper_time, del_flag,template_code,another_name
        from pub_quoterule_templatedetail
        where   del_flag='0'
        <if test="id != null "> and id  = #{id} </if>
        <if test="templateCode != null  and templateCode != ''"> and template_code = #{templateCode} </if>
        <if test="quoteRuleTemplateId != null  and quoteRuleTemplateId != ''"> and quoterule_template_id  = #{quoteRuleTemplateId} </if>

    </select>
    <select id="selectTemplateDetailList" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoteruleTemplateDetail" resultMap="PubQuoteruleTemplatedetailResult">
        select id, quoterule_template_id, filedseting_id, field_chinese, field_english, oper_by, oper_time, del_flag,template_code,another_name
        from pub_quoterule_templatedetail
        where   del_flag='0'
        <if test="id != null ">
          and id  = #{id} </if>
        <if test="templateCode != null  and templateCode != ''"> and template_code = #{templateCode} </if>
        <if test="quoteRuleTemplateId != null  and quoteRuleTemplateId != ''"> and quoterule_template_id  = #{quoteRuleTemplateId} </if>
        <if test="IdList != null and IdList.size>0 ">
            and quoterule_template_id in
            <foreach collection="IdList" separator="," item="Id" open="(" close=")">
                #{Id}
            </foreach>
        </if>

    </select>
    <insert id="insertPubQuoteruleTemplateDetailList" parameterType="java.util.List">
        insert into pub_quoterule_templatedetail
        (quoterule_template_id,
        filedseting_id,
        field_chinese,
        field_english,
        oper_by,
        oper_time,
        template_code,
        del_flag,
        another_name)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.quoteRuleTemplateId},
            #{item.filedSetingId},
            #{item.fieldChinese},
            #{item.fieldEnglish},
            #{item.operBy},
            #{item.operTime},
            #{item.templateCode},
            '0',
            #{item.anotherName}
            )
        </foreach>
    </insert>



    <delete id="deletePubQuoteruleTemplateDetail" parameterType="String">
        update pub_quoterule_templatedetail set del_flag = '1' where quoterule_template_id = #{templateId}
    </delete>



</mapper>