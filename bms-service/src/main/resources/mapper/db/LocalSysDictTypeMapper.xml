<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.LocalSysDictTypeMapper">
    <resultMap type="com.bbyb.joy.bms.domain.dto.LocalSysDictType" id="SysDictTypeMap">
        <result property="dictId" column="dict_id" jdbcType="BIGINT"/>
        <result property="dictName" column="dict_name" jdbcType="VARCHAR"/>
        <result property="dictType" column="dict_type" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>


    <!--新增数据-->
    <insert id="insert" keyProperty="dict_id" useGeneratedKeys="true">
        insert into sys_dict_type(dict_id,dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark)
        values (#{dictId},#{dictName},#{dictType},#{status},#{createBy},#{createTime},#{updateBy},#{updateTime},#{remark})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch">
        insert into sys_dict_type(dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.dictName},#{entity.dictType},#{entity.status},#{entity.createBy},#{entity.createTime},#{entity.updateBy},#{entity.updateTime},#{entity.remark})
        </foreach>
    </insert>

    <select id="queryByCond" resultMap="SysDictTypeMap" parameterType="java.util.Map">
        select
        dict_id,dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark
        from sys_dict_type
        <where>
            <if test="dictId != null and dictId != ''">
                and dict_id = #{dictId}
            </if>
            <if test="dictName != null and dictName != ''">
                and dict_name = #{dictName}
            </if>
            <if test="dictType != null and dictType != ''">
                and dict_type = #{dictType}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null and updateTime != ''">
                and update_time = #{updateTime}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="inDictTypes != null">
                and dict_type in
                <foreach collection="inDictTypes"  item="item" open="(" separator="," close=")" >
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


    <!-- 更新数据 -->
    <update id="update">
        update sys_dict_type
        <set>
            <if test="dictId != null and dictId != ''">
                dict_id = #{dictId},
            </if>
            <if test="dictName != null and dictName != ''">
                dict_name = #{dictName},
            </if>
            <if test="dictType != null and dictType != ''">
                dict_type = #{dictType},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="createTime != null and createTime != ''">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null and updateTime != ''">
                update_time = #{updateTime},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </set>
        where dict_id = #{dictId}
    </update>

    <update id="updateBatch">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";" >
            update sys_dict_type
            <set>
                <if test="item.dictName != null and item.dictName != ''">
                    dict_name = #{item.dictName},
                </if>
                <if test="item.status != null and item.status != ''">
                    status = #{item.status},
                </if>
                <if test="item.updateTime != null and item.updateTime != ''">
                    update_time = SYSDATE(),
                </if>
            </set>
            where dict_id = #{item.dictId}
        </foreach>
    </update>

    <!--通过主键作废-->
    <delete id="deleteById">
        delete from sys_dict_type where dict_id = #{dictId}
    </delete>

</mapper>