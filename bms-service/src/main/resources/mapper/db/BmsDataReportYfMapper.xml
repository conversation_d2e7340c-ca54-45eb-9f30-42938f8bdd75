<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsDataReportYfMapper">
    <resultMap type="com.bbyb.joy.bms.domain.dto.datareport.StateYfGroup" id="bmsCarrierExistenceVos">
        <result property="billCode" column="bill_code" />
        <result property="billDate" column="bill_date" />
        <result property="createTime" column="create_time"/>
        <result property="ticketState" column="bill_state"/>
        <result property="companyId"    column="company_id" />
        <result property="companyName" column="company_name" />
        <result property="carrierName" column="carrier_name" />
        <result property="carrierCode"  column="carrier_code" />
        <result property="areaName" column="carrier_area" />
        <result property="total" column="total"/>
    </resultMap>


    <select id="groupList" parameterType="com.bbyb.joy.bms.domain.dto.datareport.StateYfGroup" resultMap="bmsCarrierExistenceVos">
        SELECT
        GROUP_CONCAT(DISTINCT ysm.id SEPARATOR ',') as billIds,
        ysm.bill_state,
        ysm.company_id ,
        sum(ifnull( ysi.freight, 0 ) + ifnull( ysi.ultrafar_fee, 0 ) + ifnull( ysi.superframes_fee, 0 ) + ifnull( ysi.excess_fee, 0 ) + ifnull( ysi.delivery_fee, 0 ) + ifnull( ysi.Outboundsorting_fee, 0 ) + ifnull( ysi.shortbarge_fee, 0 ) + ifnull( ysi.return_fee, 0 ) + ifnull( ysi.exception_fee, 0 ) + ifnull( ysi.adjust_fee, 0 ) + ifnull( ysi.other_cost1, 0 ) + ifnull( ysi.other_cost2, 0 ) + ifnull( ysi.other_cost3, 0 ) + ifnull( ysi.other_cost4, 0 ) + ifnull( ysi.other_cost5, 0 ) + ifnull( ysi.other_cost6, 0 ) + ifnull( ysi.other_cost7, 0 ) + ifnull( ysi.other_cost8, 0 ) + ifnull( ysi.other_cost9, 0 ) + ifnull( ysi.other_cost10, 0 ) + ifnull( ysi.other_cost11, 0 ) + ifnull( ysi.other_cost12, 0 ) ) total
        FROM
        bms_yfbillmain ysm
        LEFT JOIN bms_yfcost_info ysi ON ysi.bill_id = ysm.id AND ysi.del_flag = 0
        left join bms_clientinfo c on ysi.client_id = c.id  and c.del_flag = '0'
        LEFT JOIN bms_carrierinfo carr on carr.id = ysi.carrier_id and carr.del_flag=0
        WHERE
        ysm.del_flag = 0
        <if test="companyIds!=null and companyIds.length>0">
            and ysm.company_id in
            <foreach collection="companyIds"  item="item" open="(" separator="," close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.beginBillData != null and params.beginBillData != '' and params.endBillData != null and params.endBillData != ''">
            and ysm.bill_date between #{params.beginBillData} and #{params.endBillData}
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="carrierList != null and carrierList.size>0">
            and  carr.carrier_code in
            <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                #{carrier}
            </foreach>
        </if>
        GROUP BY
        ysm.bill_state
        <if test="groupType==2">
            , ysm.company_id
        </if>
    </select>


    <select id="detailsList" parameterType="com.bbyb.joy.bms.domain.dto.datareport.StateYfGroup" resultMap="bmsCarrierExistenceVos">
        SELECT
        ysm.id,
        ysm.bill_code,
        ysm.bill_state,
        ysm.bill_date,
        ysm.company_id,
        ysi.expenses_code, -- 费用单号
        c.client_name clientName,
        carr.carrier_code,
        carr.carrier_area,
        carr.carrier_name,
        DATE_FORMAT(ysm.create_time, '%Y-%m-%d %H:%i:%s') AS createTimeStr,
        CAST(ifnull( ysi.freight, 0 ) + ifnull( ysi.ultrafar_fee, 0 ) + ifnull( ysi.superframes_fee, 0 ) + ifnull( ysi.excess_fee, 0 ) + ifnull( ysi.delivery_fee, 0 ) + ifnull( ysi.Outboundsorting_fee, 0 ) + ifnull( ysi.shortbarge_fee, 0 ) + ifnull( ysi.return_fee, 0 ) + ifnull( ysi.exception_fee, 0 ) + ifnull( ysi.adjust_fee, 0 ) + ifnull( ysi.other_cost1, 0 ) + ifnull( ysi.other_cost2, 0 ) + ifnull( ysi.other_cost3, 0 ) + ifnull( ysi.other_cost4, 0 ) + ifnull( ysi.other_cost5, 0 ) + ifnull( ysi.other_cost6, 0 ) + ifnull( ysi.other_cost7, 0 ) + ifnull( ysi.other_cost8, 0 ) + ifnull( ysi.other_cost9, 0 ) + ifnull( ysi.other_cost10, 0 ) + ifnull( ysi.other_cost11, 0 ) + ifnull( ysi.other_cost12, 0 ) as DECIMAL(18,2)) AS total
        FROM bms_yfbillmain ysm
        LEFT JOIN bms_yfcost_info ysi ON ysi.bill_id = ysm.id AND ysi.del_flag = 0
        LEFT JOIN bms_clientinfo c ON ysi.client_id = c.id  AND c.del_flag = 0
        LEFT JOIN bms_carrierinfo carr ON carr.id = ysi.carrier_id AND carr.del_flag=0
        <where>
        ysm.del_flag = 0
        <if test="billIdArr!=null and billIdArr.size>0">
            and ysm.id in
            <foreach collection="billIdArr"  item="item" open="(" separator="," close=")" >
                #{item}
            </foreach>
        </if>
        <if test="ticketState!=null">
            and ysm.bill_state=#{ticketState}
        </if>
        <if test="companyId!=null">
            and ysm.company_id=#{companyId}
        </if>
            <if test="companyIds!=null and companyIds.length>0">
                and ysm.company_id in
                <foreach collection="companyIds"  item="item" open="(" separator="," close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="params.createTimeStart != null and params.createTimeStart != '' and params.createTimeEnd != null and params.createTimeEnd != ''">
                and ysm.bill_date between #{params.createTimeStart} and #{params.createTimeEnd}
            </if>

            <if test="areaName != null and areaName != ''">
                and carr.carrier_area like concat('%',#{areaName},'%')
            </if>
            <if test="carrierName != null and carrierName != ''">
                and carr.carrier_name like concat('%',#{carrierName},'%')
            </if>
            <if test="clientName != null and clientName != ''">
                AND c.client_name like concat('%',trim(#{clientName}),'%')
            </if>
            <if test="carrierList != null">
                and  carr.carrier_code in
                <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                    #{carrier}
                </foreach>
            </if>
        </where>
    </select>


</mapper>