<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsEstimateCostRecordInfoMapper">
  <resultMap id="BaseResultMap" type="com.bbyb.joy.bms.domain.dto.BmsEstimateCostRecordInfo">
    <!--@mbg.generated-->
    <!--@Table bms_estimate_cost_record_info-->
    <id column="id" property="id" />
    <result column="billing_moudle" property="billingMoudle" />
    <result column="billing_fun" property="billingFun" />
    <result column="billing_start_time" property="billingStartTime" />
    <result column="billing_end_time" property="billingEndTime" />
    <result column="estimate_billing_state" property="estimateBillingState" />
    <result column="create_by" property="createBy" />
    <result column="create_code" property="createCode" />
    <result column="create_time" property="createTime" />
    <result column="oper_by" property="operBy" />
    <result column="oper_code" property="operCode" />
    <result column="oper_time" property="operTime" />
    <result column="del_flag" property="delFalg" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, billing_moudle, billing_fun, billing_start_time, billing_end_time, estimate_billing_state,
    create_by, create_code, create_time, oper_by, oper_code, oper_time,del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from bms_estimate_cost_record_info
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from bms_estimate_cost_record_info
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.bbyb.joy.bms.domain.dto.BmsEstimateCostRecordInfo">
    <!--@mbg.generated-->
    insert into bms_estimate_cost_record_info (billing_moudle, billing_fun, billing_start_time, billing_end_time,
      estimate_billing_state, create_by, create_code, create_time, oper_by, 
      oper_code, oper_time,del_flag)
    values ( #{billingMoudle}, #{billingFun}, #{billingStartTime}, #{billingEndTime},
      #{estimateBillingState}, #{createBy}, #{createCode}, #{createTime}, #{operBy}, 
      #{operCode}, #{operTime},#{delFalg})
  </insert>
  <insert id="insertSelective" parameterType="com.bbyb.joy.bms.domain.dto.BmsEstimateCostRecordInfo">
    <!--@mbg.generated-->
    insert into bms_estimate_cost_record_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="billingMoudle != null">
        billing_moudle,
      </if>
      <if test="billingFun != null">
        billing_fun,
      </if>
      <if test="billingStartTime != null">
        billing_start_time,
      </if>
      <if test="billingEndTime != null">
        billing_end_time,
      </if>
      <if test="estimateBillingState != null">
        estimate_billing_state,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createCode != null">
        create_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="operBy != null">
        oper_by,
      </if>
      <if test="operCode != null">
        oper_code,
      </if>
      <if test="operTime != null">
        oper_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="billingMoudle != null">
        #{billingMoudle},
      </if>
      <if test="billingFun != null">
        #{billingFun},
      </if>
      <if test="billingStartTime != null">
        #{billingStartTime},
      </if>
      <if test="billingEndTime != null">
        #{billingEndTime},
      </if>
      <if test="estimateBillingState != null">
        #{estimateBillingState},
      </if>
      <if test="createBy != null">
        #{createBy},
      </if>
      <if test="createCode != null">
        #{createCode},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="operBy != null">
        #{operBy},
      </if>
      <if test="operCode != null">
        #{operCode},
      </if>
      <if test="operTime != null">
        #{operTime},
      </if>
      <if test="delFalg != null">
        #{delFalg}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.bbyb.joy.bms.domain.dto.BmsEstimateCostRecordInfo">
    <!--@mbg.generated-->
    update bms_estimate_cost_record_info
    <set>
      <if test="billingMoudle != null">
        billing_moudle = #{billingMoudle},
      </if>
      <if test="billingFun != null">
        billing_fun = #{billingFun},
      </if>
      <if test="billingStartTime != null">
        billing_start_time = #{billingStartTime},
      </if>
      <if test="billingEndTime != null">
        billing_end_time = #{billingEndTime},
      </if>
      <if test="estimateBillingState != null">
        estimate_billing_state = #{estimateBillingState},
      </if>
      <if test="createBy != null">
        create_by = #{createBy},
      </if>
      <if test="createCode != null">
        create_code = #{createCode},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="operBy != null">
        oper_by = #{operBy},
      </if>
      <if test="operCode != null">
        oper_code = #{operCode},
      </if>
      <if test="operTime != null">
        oper_time = #{operTime},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bbyb.joy.bms.domain.dto.BmsEstimateCostRecordInfo">
    <!--@mbg.generated-->
    update bms_estimate_cost_record_info
    set billing_moudle = #{billingMoudle},
      billing_fun = #{billingFun},
      billing_start_time = #{billingStartTime},
      billing_end_time = #{billingEndTime},
      estimate_billing_state = #{estimateBillingState},
      create_by = #{createBy},
      create_code = #{createCode},
      create_time = #{createTime},
      oper_by = #{operBy},
      oper_code = #{operCode},
      oper_time = #{operTime},
    where id = #{id}
  </update>
</mapper>