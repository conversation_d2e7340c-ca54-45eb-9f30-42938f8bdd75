<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.SysDataGroupMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.sysdatagroup.SysDataGroup" id="SysPermissionsGroupResult">
        <result property="id"    column="id"    />
        <result property="dataGroupName"    column="data_group_name"    />
        <result property="dataGroupId"    column="data_group_code"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag" column="del_flag" />
        <result property="status" column="status" />
    </resultMap>

    <resultMap type="com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo" id="ClietnInfo">
        <result property="id"    column="id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
    </resultMap>


    <resultMap type="com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo" id="CarriderInfo">
        <result property="id"    column="id"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="carrierName"    column="carrier_name"    />
    </resultMap>
    <sql id="selectSysPermissionsGroupVo">
        select id, data_group_name, data_group_code, oper_dept_id, oper_code, oper_by, oper_time, del_flag,status from sys_permissions_group
    </sql>

    <select id="selectSysPermissionsGroupList" parameterType="com.bbyb.joy.bms.domain.dto.SysPermissionsGroup" resultMap="SysPermissionsGroupResult">
        <include refid="selectSysPermissionsGroupVo"/>
        <where>  
            <if test="dataGroupName != null  and dataGroupName != ''"> and data_group_name like concat('%', #{dataGroupName}, '%')</if>
            <if test="dataGroupId != null "> and data_group_code like concat('%',#{dataGroupId},'%') </if>
            <if test="status != null "> and status = #{status} </if>
             and del_flag=0
        </where>
        order by id desc
    </select>
    
    <select id="selectSysPermissionsGroupById" parameterType="java.lang.String" resultMap="SysPermissionsGroupResult">
        <include refid="selectSysPermissionsGroupVo"/>
        where id = #{id}
    </select>
    <select id="getClientInfoById" parameterType="java.lang.String" resultMap="ClietnInfo" >
    select mc.id,mc.client_code,mc.client_name from bms_clientinfo mc
        <where>
            and mc.is_enable = 0 and mc.del_flag = 0
            <if test="id!=null" >
                and mc.id in
                <foreach collection="id.split(',')" separator="," item="item" open="(" close=")">
                                       #{item}
                </foreach>
            </if>
        </where>

    </select>
    <select id="getCarrierInfoById"  parameterType="java.lang.String" resultMap="CarriderInfo">
     select mc.id,mc.carrier_code,mc.carrier_name from bms_carrierinfo mc
        <where>
            and mc.is_enable = 0 and mc.del_flag = 0
            <if test="id!=null" >
                and mc.id in
                <foreach collection="id.split(',')" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>

    <select id="getWarehouseInfoById"  parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.sysdatagroup.WarehouseInfo">
        select
        mc.id,
        mc.warehouse_code as warehouseCode,
        mc.warehouse_name as warehouseName,
        mc.company_id as companyId
        from mdm_warehouseinfo mc
        <where>
            and mc.del_flag = 0
            <if test="id!=null" >
                and mc.id in
                <foreach collection="id.split(',')" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>

    <select id="getDeprtInfoById"  parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.sysdatagroup.DeprtInfo">
        select
        cus.company_id id,
        cus.data_group_id
        from sys_permissions_group_company cus
        <where>
            <if test="id!=null" >
                and cus.data_group_id in
                <foreach collection="id.split(',')" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>

            </if>
            and cus.del_flag=0
        </where>

    </select>

    <select id="getUserInfoById"  parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.sysdatagroup.UserInfo">
        select
        cus.data_group_id,
        cus.user_id as userId
        from sys_permissions_group_user cus
        <where>
            <if test="id!=null" >
                and cus.data_group_id in
                <foreach collection="id.split(',')" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            and cus.del_flag=0
        </where>

    </select>
    <select id="selectDateTypeByUserId" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.PubSettledateSetting">
        select
        date_type as dateType
        FROM pub_settledate_setting
        where client_id =#{userId}
    </select>
    <insert id="insertSysPermissionsGroup" parameterType="com.bbyb.joy.bms.domain.dto.SysPermissionsGroup" useGeneratedKeys="true" keyProperty="id">
        insert into sys_permissions_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataGroupName != null">data_group_name,</if>
            <if test="dataGroupId != null">data_group_code,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataGroupName != null">#{dataGroupName},</if>
            <if test="dataGroupId != null">#{dataGroupId},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>
    <insert id="insertClient">
        insert into sys_permissions_group_customer(data_group_id,customer_code)
        VALUES
        <foreach collection="array"  item="item" separator="," >
            (#{id},#{item})
        </foreach>
    </insert>

    <insert id="insertCaccier" parameterType="java.util.ArrayList">
         insert into sys_permissions_group_carrier(data_group_id,carrier_id)
        VALUES
        <foreach collection="array"  item="item" separator="," >
            (#{id},#{item})
        </foreach>
    </insert>

    <insert id="insertCompany" parameterType="java.util.ArrayList">
        insert into sys_permissions_group_company(data_group_id,company_id)
        VALUES
        <foreach collection="array"  item="item" separator="," >
            (#{id},#{item})
        </foreach>
    </insert>


    <insert id="insertWarehouse" parameterType="java.util.ArrayList">
        insert into sys_permissions_group_warehouse(data_group_id,warehouse_code)
        VALUES
        <foreach collection="array"  item="item" separator="," >
            (#{id},#{item})
        </foreach>
    </insert>

    <insert id="insertUser" parameterType="java.util.ArrayList">
        insert into sys_permissions_group_user(data_group_id,user_id)
        VALUES
        <foreach collection="array"  item="item" separator="," >
            (#{id},#{item})
        </foreach>
    </insert>

    <update id="updateClient" parameterType="java.util.ArrayList">
         update  sys_permissions_group_customer set del_flag=1
        <where>
            <if test="id!=null">
                and  data_group_id=#{id}
            </if>
            <if test="array!=null and array.length>0">
                and customer_code in
                <foreach collection="array" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and del_flag=0
        </where>
    </update>

    <update id="updateCarrier" parameterType="java.util.ArrayList">
        update  sys_permissions_group_carrier set del_flag=1
        <where>
            <if test="id!=null">
                and  data_group_id=#{id}
            </if>
            <if test="array!=null and array.length>0">
                and carrier_id in
                <foreach collection="array" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and del_flag=0
        </where>
    </update>

    <update id="updateWarehouse" parameterType="java.util.ArrayList">
        update  sys_permissions_group_warehouse set del_flag=1
        <where>
            <if test="id!=null">
                and  data_group_id=#{id}
            </if>
            <if test="array!=null and array.length>0">
                and warehouse_code in
                <foreach collection="array" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and del_flag=0
        </where>
    </update>

    <update id="updateCompany" parameterType="java.util.ArrayList">
        update  sys_permissions_group_company set del_flag=1
        <where>
            <if test="id!=null">
              and  data_group_id=#{id}
            </if>
            <if test="array!=null and array.length>0">
                and company_id in
                <foreach collection="array" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and del_flag=0
        </where>
    </update>

    <update id="updateUser" parameterType="java.util.ArrayList">
        update  sys_permissions_group_user set del_flag=1
        <where>
            <if test="id!=null">
                and  data_group_id=#{id}
            </if>
            <if test="array!=null and array.length>0">
                and user_id in
                <foreach collection="array" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and del_flag=0
        </where>
    </update>
    <update id="updateSysPermissionsGroup" parameterType="com.bbyb.joy.bms.domain.dto.SysPermissionsGroup">
        update sys_permissions_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataGroupName != null">data_group_name = #{dataGroupName},</if>
            <if test="dataGroupId != null">data_group_code = #{dataGroupId},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysPermissionsGroupById" parameterType="java.lang.String">
        update  sys_permissions_group where id = #{id}  and del_flag=0
    </delete>

    <delete id="deleteSysPermissionsGroupByIds">
        update  sys_permissions_group where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag=0
    </delete>



    <update id="updateSysPermissionsGroupStatusByIds">
        update sys_permissions_group set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>