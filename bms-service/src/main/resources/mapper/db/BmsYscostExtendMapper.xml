<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYscostExtendMapper">

	<resultMap type="com.bbyb.joy.bms.domain.dto.BmsYscostExtend" id="BmsYscostExtendResult">
		<result property="id"    column="id"    />
		<result property="expensesId"    column="expenses_id"    />
		<result property="businessCode"    column="business_code"    />
		<result property="warehouseCode"    column="warehouse_code"    />
		<result property="warehouseName"    column="warehouse_name"    />
		<result property="totalBoxes"    column="total_boxes"    />
		<result property="totalNumber"    column="total_number"    />
		<result property="totalWeight"    column="total_weight"    />
		<result property="totalVolume"    column="total_volume"    />
		<result property="cargoValue"    column="cargo_value"    />
		<result property="delFlag"    column="del_flag"    />
		<result property="codeCount"    column="code_count"    />
		<result property="signingDate"    column="signing_date"    />
		<result property="orderDate"    column="order_date"    />
		<result property="storeName"    column="store_name"    />
	</resultMap>

	<sql id="selectBmsYscostExtendVo">
		select id, expenses_id, business_code, warehouse_code, warehouse_name, total_boxes, total_number, total_weight, total_volume, cargo_value, del_flag,code_count,store_name,order_date,signing_date from bms_yscost_extend
	</sql>

	<select id="selectBmsYscostExtendList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostExtend" resultMap="BmsYscostExtendResult">
		<include refid="selectBmsYscostExtendVo"/>
		<where>
			<if test="expensesId != null "> and expenses_id = #{expensesId}</if>
			<if test="businessCode != null  and businessCode != ''"> and business_code = #{businessCode}</if>
			<if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
			<if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
			<if test="totalBoxes != null "> and total_boxes = #{totalBoxes}</if>
			<if test="totalNumber != null "> and total_number = #{totalNumber}</if>
			<if test="totalWeight != null "> and total_weight = #{totalWeight}</if>
			<if test="totalVolume != null "> and total_volume = #{totalVolume}</if>
			<if test="cargoValue != null "> and cargo_value = #{cargoValue}</if>
		</where>
	</select>

	<select id="selectBmsYscostExtendById" parameterType="java.lang.String" resultMap="BmsYscostExtendResult">
		<include refid="selectBmsYscostExtendVo"/>
		where id = #{id}
	</select>

	<select id="selectBmsYscostExtendByExpensesId" parameterType="java.lang.String" resultMap="BmsYscostExtendResult">
		<include refid="selectBmsYscostExtendVo"/>
		where expenses_id = #{id}
	</select>

	<select id="selectBmsYscostExtendListByExpensesIds"  resultMap="BmsYscostExtendResult">
		<include refid="selectBmsYscostExtendVo"/>
		where del_flag = 0 and expenses_id in
		<foreach item="id" collection="expensesIds" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

	<select id="selectBmsYscostExtendListBybusinessCodes"
			resultMap="BmsYscostExtendResult">
		<include refid="selectBmsYscostExtendVo"/>
		where del_flag = 0 and business_code in
		<foreach item="id" collection="businessCodes" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

	<insert id="insertBmsYscostExtend" parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostExtend" useGeneratedKeys="true" keyProperty="id">
		insert into bms_yscost_extend
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="expensesId != null">expenses_id,</if>
			<if test="storeName != null and storeName != ''">store_name,</if>
			<if test="orderDate">order_date,</if>
			<if test="signingDate != null">signing_date,</if>
			<if test="businessCode != null">business_code,</if>
			<if test="warehouseCode != null">warehouse_code,</if>
			<if test="warehouseName != null">warehouse_name,</if>
			<if test="totalBoxes != null">total_boxes,</if>
			<if test="totalNumber != null">total_number,</if>
			<if test="totalWeight != null">total_weight,</if>
			<if test="totalVolume != null">total_volume,</if>
			<if test="cargoValue != null">cargo_value,</if>
			<if test="delFlag != null">del_flag,</if>
			<if test="totalQuantity != null">total_quantity,</if>
			<if test="price != null">price,</if>
			<if test="mainExpenseId != null">main_expense_id,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="expensesId != null">#{expensesId},</if>
			<if test="storeName != null and storeName != ''">#{storeName},</if>
			<if test="orderDate != null">#{orderDate},</if>
			<if test="signingDate != null">#{signingDate},</if>
			<if test="businessCode != null and businessCode != ''">#{businessCode},</if>
			<if test="warehouseCode != null">#{warehouseCode},</if>
			<if test="warehouseName != null">#{warehouseName},</if>
			<if test="totalBoxes != null">#{totalBoxes},</if>
			<if test="totalNumber != null">#{totalNumber},</if>
			<if test="totalWeight != null">#{totalWeight},</if>
			<if test="totalVolume != null">#{totalVolume},</if>
			<if test="cargoValue != null">#{cargoValue},</if>
			<if test="delFlag != null">#{delFlag},</if>
			<if test="totalQuantity != null">#{totalQuantity},</if>
			<if test="price != null">#{price},</if>
			<if test="mainExpenseId != null">#{mainExpenseId},</if>
		</trim>
	</insert>

	<update id="updateBmsYscostExtend" parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostExtend">
		update bms_yscost_extend
		<trim prefix="SET" suffixOverrides=",">
			<if test="expensesId != null">expenses_id = #{expensesId},</if>
			<if test="businessCode != null and businessCode != ''">business_code = #{businessCode},</if>
			<if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
			<if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
			<if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
			<if test="totalNumber != null">total_number = #{totalNumber},</if>
			<if test="totalWeight != null">total_weight = #{totalWeight},</if>
			<if test="totalVolume != null">total_volume = #{totalVolume},</if>
			<if test="cargoValue != null">cargo_value = #{cargoValue},</if>
			<if test="delFlag != null">del_flag = #{delFlag},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteBmsYscostExtendById" parameterType="java.lang.String">
		delete from bms_yscost_extend where id = #{id}
	</delete>

	<delete id="deleteBmsYscostExtendByIds">
		delete from bms_yscost_extend where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

	<update id="updateBmsYscostExtendStatusByIds">
		update bms_yscost_extend set status = #{status} where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>
</mapper>