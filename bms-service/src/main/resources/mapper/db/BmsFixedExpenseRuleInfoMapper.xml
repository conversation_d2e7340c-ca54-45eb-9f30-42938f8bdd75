<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsFixedExpenseRuleInfoMapper">
  <resultMap id="BaseResultMap" type="com.bbyb.joy.bms.domain.dto.BmsFixedExpenseRuleInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="client_id" jdbcType="INTEGER" property="clientId" />
    <result column="carrier_code" jdbcType="VARCHAR" property="carrierCode" />
    <result column="item_id" jdbcType="INTEGER" property="itemId" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="frequency" jdbcType="CHAR" property="frequency" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="rule_code" jdbcType="VARCHAR" property="ruleCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_code" jdbcType="VARCHAR" property="createCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
    <result column="oper_code" jdbcType="VARCHAR" property="operCode" />
    <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="is_enable" jdbcType="CHAR" property="isEnable" />
  </resultMap>

  <resultMap id="ExtendsMap" type="com.bbyb.joy.bms.domain.dto.dto.BmsFixedExpenseRuleExtendDto" extends="BaseResultMap">
    <result column="item_name" jdbcType="VARCHAR" property ="itemName"/>
    <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
    <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    id, client_id, carrier_code, item_id, start_date, end_date, frequency, amount,
    remark, create_by, create_code, create_time, oper_by, oper_code, oper_time, del_flag ,is_enable,rule_code
  </sql>

  <insert id="insertSelective" parameterType="com.bbyb.joy.bms.domain.dto.BmsFixedExpenseRuleInfo">
    insert into pub_fixedfee_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="clientId != null">
        client_id,
      </if>
      <if test="carrierCode != null">
        carrier_code,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="frequency != null">
        frequency,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createCode != null">
        create_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="operBy != null">
        oper_by,
      </if>
      <if test="operCode != null">
        oper_code,
      </if>
      <if test="operTime != null">
        oper_time,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="ruleCode !=null and ruleCode !=''">
        rule_code,
      </if>
      <if test="isEnable !=null and isEnable !=''">
        is_enable,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="clientId != null">
        #{clientId,jdbcType=INTEGER},
      </if>
      <if test="carrierCode != null">
        #{carrierCode,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="frequency != null">
        #{frequency,jdbcType=CHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createCode != null">
        #{createCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operBy != null">
        #{operBy,jdbcType=VARCHAR},
      </if>
      <if test="operCode != null">
        #{operCode,jdbcType=VARCHAR},
      </if>
      <if test="operTime != null">
        #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      <if test="ruleCode !=null and ruleCode !=''">
        #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="isEnable !=null and isEnable !=''">
        #{isEnable,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <select id="selectConflictOfTime" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsFixedExpenseRuleDto" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from pub_fixedfee_rule
    where
    del_flag = 0
    and client_id = #{clientId}
    and item_id = #{itemId}
    and NOT (end_date &lt;= #{startDate} OR start_date >= #{endDate})
    <if test="id != null">
      and id != #{id}
    </if>
  </select>

  <update id="updateByPrimaryKeySelective" parameterType="com.bbyb.joy.bms.domain.dto.BmsFixedExpenseRuleInfo">
    update pub_fixedfee_rule
    <set>
      <if test="clientId != null">
        client_id = #{clientId,jdbcType=INTEGER},
      </if>
      <if test="carrierCode != null">
        carrier_code = #{carrierCode,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="frequency != null">
        frequency = #{frequency,jdbcType=CHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createCode != null">
        create_code = #{createCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operBy != null">
        oper_by = #{operBy,jdbcType=VARCHAR},
      </if>
      <if test="operCode != null">
        oper_code = #{operCode,jdbcType=VARCHAR},
      </if>
      <if test="operTime != null">
        oper_time = #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="isEnable != null">
        is_enable = #{isEnable,jdbcType=CHAR},
      </if>    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeySelectiveBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsFixedExpenseRuleInfo">
    update pub_fixedfee_rule
    <set>
      <if test="clientId != null">
        client_id = #{clientId,jdbcType=INTEGER},
      </if>
      <if test="carrierCode != null">
        carrier_code = #{carrierCode,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="frequency != null">
        frequency = #{frequency,jdbcType=CHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createCode != null">
        create_code = #{createCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operBy != null">
        oper_by = #{operBy,jdbcType=VARCHAR},
      </if>
      <if test="operCode != null">
        oper_code = #{operCode,jdbcType=VARCHAR},
      </if>
      <if test="operTime != null">
        oper_time = #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="isEnable != null">
        is_enable = #{isEnable,jdbcType=CHAR},
      </if>    </set>
    where id in
    <foreach collection="ids" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>
  
  <select id="selectByParam" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsFixedExpenseRuleDto" resultMap="ExtendsMap">
    select tb1.id, tb1.client_id, tb1.carrier_code, tb1.item_id, tb1.start_date, tb1.end_date, tb1.frequency, tb1.amount,
    tb1.remark, tb1.create_by, tb1.create_code, tb1.create_time, tb1.oper_by, tb1.oper_code, tb1.oper_time, tb1.del_flag,
    tb3.client_name,tb1.rule_code ,tb1.is_enable
    from pub_fixedfee_rule tb1 left join bms_clientinfo tb3
    on tb1.client_id = tb3.id
    <where>
      tb1.del_flag = 0
      <if test="id != null">
        and tb1.id = #{id}
      </if>
      <if test="clientId != null">
        and tb1.client_id = #{clientId}
      </if>
      <if test="itemId != null">
        and tb1.item_id = #{itemId}
      </if>
      <if test="clientIds != null and clientIds.size()>0">
        and tb1.client_id IN
        <foreach collection="clientIds" item="clientId" open="(" separator="," close=")">
          #{clientId}
        </foreach>
      </if>
      <if test="ruleCode !=null and ruleCode !=''">
        and   tb1.rule_code like concat('%', #{ruleCode}, '%')
      </if>
      <if test="operFromDate !=null and operToDate !=null">
        and    tb1.oper_time &gt;=#{operFromDate}
        and tb1.oper_time &lt;=#{operToDate}
      </if>
      <if test="createFromDate !=null and createToDate !=null">
        and    tb1.create_time &gt;=#{createFromDate}
        and   tb1.create_time &lt;=#{createToDate}
      </if>
      <if test="operBy !=null and operBy !=''">
        and    tb1.oper_By like concat('%', #{operBy}, '%')
      </if>
      <if test="createBy !=null and createBy !=''">
        and   tb1.create_by like concat('%', #{createBy}, '%')
      </if>
    </where>
    order by id desc
  </select>

  <select id="selectForCreateWithLimit" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM pub_fixedfee_rule WHERE del_flag = 0 AND end_date >= #{formatNowDay}
    AND start_date &lt;= #{formatNowDay} ORDER BY id DESC LIMIT #{offSet}, #{size}
  </select>

  <select id="queryDetail" parameterType="java.util.Map" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM pub_fixedfee_rule WHERE del_flag = 0 and  id=#{id}
  </select>

</mapper>