<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsAnalysisReportMapper">


    <select id="queryGrossProfitMarginReport" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsAnalysisDto" >
        SELECT
            main.companyId AS companyId,
            main.clientId AS clientId,
            main.clientCode AS clientCode,
            main.clientName AS clientName,
            main.totalNumber AS totalNumber,
            main.totalWeight AS totalWeight,
            main.totalVolume AS totalVolume,
            main.originatingCity AS originatingCity,
            main.destinationCity AS destinationCity,
            main.businessType AS businessType,
            main.codeType AS codeType,
            SUM(IF(main.costMode = 1 and main.codeType = 1,IFNULL(main.settleAmount,0),0)) AS transIncome,
            SUM(IF(main.costMode = 1 and main.codeType = 2,IFNULL(main.settleAmount,0),0)) AS storageIncome,
            SUM(IF(main.costMode = 1 and main.codeType IN (3,4),IFNULL(main.settleAmount,0),0)) AS addedIncome,
            SUM(IF(main.costMode = 1 and main.codeType IN (5), IFNULL(main.settleAmount, 0), 0)) AS insideIncome,
            SUM(IF(main.costMode = 1 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0)) AS totalIncome,
            SUM(IF(main.costMode = 2 and main.codeType = 1,IFNULL(main.settleAmount,0),0)) AS transCost,
            SUM(IF(main.costMode = 2 and main.codeType = 2,IFNULL(main.settleAmount,0),0)) AS storageCost,
            SUM(IF(main.costMode = 2 and main.codeType IN (3,4),IFNULL(main.settleAmount,0),0)) AS addedCost,
            SUM(IF(main.costMode = 2 and main.codeType IN (5), IFNULL(main.settleAmount, 0), 0)) AS insideCost,
            SUM(IF(main.costMode = 2 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0)) AS totalCost,
            SUM(IF(main.costMode = 1 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0) - IF(main.costMode = 2 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0)) AS profitFee,
            ROUND(SUM(IF(main.costMode = 1 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0) - IF(main.costMode = 2 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0))/SUM(IF(main.costMode = 1 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0)), 2 ) * 100 AS grossMargin
        FROM (
            SELECT
                1 AS costMode,
                t1.company_id AS companyId,
                t1.client_id AS clientId,
                t1.client_code AS clientCode,
                t1.client_name AS clientName,
                SUM(IFNULL(t1.total_number,0)) AS totalNumber,
                SUM(IFNULL(t1.total_weight,0)) AS totalWeight,
                SUM(IFNULL(t1.total_volume,0)) AS totalVolume,
                t1.originating_city AS originatingCity,
                t1.destination_city AS destinationCity,
                1 AS businessType,
                1 AS codeType,
                SUM(IFNULL(t2.settle_amount,0)) AS settleAmount
            FROM bms_trans_code_info t1
            LEFT JOIN bms_ysexpenses_middle t2 ON t2.code_pk_id = t1.pk_id AND t2.code_type = 1 AND t2.del_flag = 0
            WHERE t1.del_flag = 0
            <choose>
                <when test="dateType != null and dateType == 1">
                    AND t1.signing_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 2">
                    AND t1.order_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t1.order_date BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
            <if test="companyIds != null and companyIds.size > 0">
                AND t1.company_id IN
                <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="clientName != null and clientName != ''">
                AND t1.client_name LIKE concat(#{clientName}, '%')
            </if>
            <if test="originatingCity!=null and originatingCity!=''">
                AND t1.originating_city LIKE concat(#{originating_city}, '%')
            </if>
            <if test="destinationCity!=null and destinationCity!=''">
                AND t1.destination_city LIKE concat(#{destinationCity}, '%')
            </if>
            GROUP BY ${groupDimensionConvert}
            UNION ALL
            SELECT
                1 AS costMode,
                t1.company_id AS companyId,
                t1.client_id AS clientId,
                t1.client_code AS clientCode,
                t1.client_name AS clientName,
                SUM(IFNULL(t1.total_number,0)) AS totalNumber,
                SUM(IFNULL(t1.total_weight,0)) AS totalWeight,
                SUM(IFNULL(t1.total_volume,0)) AS totalVolume,
                t1.originating_city AS originatingCity,
                t1.destination_city AS destinationCity,
                2 AS businessType,
                2 AS codeType,
                SUM(IFNULL(t2.settle_amount,0)) AS settleAmount
            FROM bms_storage_code_info t1
            LEFT JOIN bms_ysexpenses_middle t2 ON t2.code_pk_id = t1.pk_id AND t2.code_type = 2 AND t2.del_flag = 0
            WHERE t1.del_flag = 0 AND t1.cost_mode = 1
            <choose>
                <when test="dateType != null and dateType == 1">
                    AND t1.signing_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 2">
                    AND t1.order_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t1.order_date BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
            <if test="companyIds != null and companyIds.size > 0">
                AND t1.company_id IN
                <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="clientName != null and clientName != ''">
                AND t1.client_name LIKE concat(#{clientName}, '%')
            </if>
            <if test="originatingCity!=null and originatingCity!=''">
                AND t1.originating_city LIKE concat(#{originating_city}, '%')
            </if>
            <if test="destinationCity!=null and destinationCity!=''">
                AND t1.destination_city LIKE concat(#{destinationCity}, '%')
            </if>
            GROUP BY ${groupDimensionConvert}
            UNION ALL
            SELECT
                1 AS costMode,
                t1.dept_id AS companyId,
                t1.client_id AS clientId,
                t2.client_code AS clientCode,
                t2.client_name AS clientName,
                t1.number AS totalNumber,
                0 AS totalWeight,
                0 AS totalVolume,
                '' AS originatingCity,
                '' AS destinationCity,
                1 AS businessType,
                3 AS codeType,
                IFNULL(SUM(t1.amount),0) AS settleAmount
            FROM bms_addedfee t1
            LEFT JOIN bms_clientinfo t2 ON t2.id = t1.client_id
            WHERE t1.del_flag = '0'
            AND t1.settle_type = '1'
            AND t1.fee_belong = 1
            <choose>
                <when test="dateType != null and dateType == 1">
                    AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 2">
                    AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
            <if test="companyIds != null and companyIds.size > 0">
                AND t1.dept_id IN
                <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="clientName != null and clientName != ''">
                AND t2.client_name LIKE concat(#{clientName}, '%')
            </if>
            UNION ALL
            SELECT
                1 AS costMode,
                t1.dept_id AS companyId,
                t1.client_id AS clientId,
                t2.client_code AS clientCode,
                t2.client_name AS clientName,
                t1.number AS totalNumber,
                0 AS totalWeight,
                0 AS totalVolume,
                '' AS originatingCity,
                '' AS destinationCity,
                2 AS businessType,
                4 AS codeType,
                IFNULL(SUM(t1.amount),0) AS settleAmount
            FROM bms_addedfee t1
            LEFT JOIN bms_clientinfo t2 ON t2.id = t1.client_id
            WHERE t1.del_flag = '0'
            AND t1.settle_type = '1'
            AND t1.fee_belong = 2
            <choose>
                <when test="dateType != null and dateType == 1">
                    AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 2">
                    AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
            <if test="companyIds != null and companyIds.size > 0">
                AND t1.dept_id IN
                <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="clientName != null and clientName != ''">
                AND t2.client_name LIKE concat(#{clientName}, '%')
            </if>
            GROUP BY ${groupDimensionConvert}
            UNION ALL
            SELECT
                2 AS costMode,
                t1.company_id AS companyId,
                t1.client_id AS clientId,
                t1.client_code AS clientCode,
                t1.client_name AS client_name,
                SUM(IFNULL(t1.total_number,0)) AS totalNumber,
                SUM(IFNULL(t1.total_weight,0)) AS totalWeight,
                SUM(IFNULL(t1.total_volume,0)) AS totalVolume,
                t1.originating_city AS originatingCity,
                t1.destination_city AS destinationCity,
                1 AS businessType,
                1 AS codeType,
                SUM(IFNULL(t3.settle_amount,0)) AS settleAmount
            FROM bms_job_code_info t1
            INNER JOIN bms_trans_code_info t2 ON t2.relate_code = t1.order_code AND t2.del_flag = 0
            LEFT JOIN bms_yfexpenses_middle_share t3 ON t3.code_pk_id = t1.pk_id AND t2.del_flag = 0
            WHERE t1.del_flag = 0
            <choose>
                <when test="dateType != null and dateType == 1">
                    AND t2.signing_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 2">
                    AND t2.order_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t2.order_date BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
            <if test="companyIds != null and companyIds.size > 0">
                AND t1.company_id IN
                <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="clientName != null and clientName != ''">
                AND t1.client_name LIKE concat(#{clientName}, '%')
            </if>
            <if test="originatingCity!=null and originatingCity!=''">
                AND t1.originating_city LIKE concat(#{originating_city}, '%')
            </if>
            <if test="destinationCity!=null and destinationCity!=''">
                AND t1.destination_city LIKE concat(#{destinationCity}, '%')
            </if>
            GROUP BY ${groupDimensionConvert}
            UNION ALL
            SELECT
                2 AS costMode,
                t1.company_id AS companyId,
                t1.client_id AS clientId,
                t1.client_code AS clientCode,
                t1.client_name AS client_name,
                SUM(IFNULL(t1.total_number,0)) AS totalNumber,
                SUM(IFNULL(t1.total_weight,0)) AS totalWeight,
                SUM(IFNULL(t1.total_volume,0)) AS totalVolume,
                t1.originating_city AS originatingCity,
                t1.destination_city AS destinationCity,
                2 AS businessType,
                2 AS codeType,
                SUM(IFNULL(t2.settle_amount,0)) AS settleAmount
            FROM bms_storage_code_info t1
            LEFT JOIN bms_yfexpenses_middle t2 ON t2.code_pk_id = t1.pk_id AND t2.code_type = 2 AND t2.del_flag = 0
            WHERE t1.del_flag = 0 AND t1.cost_mode = 2
            <choose>
                <when test="dateType != null and dateType == 1">
                    AND t1.signing_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 2">
                    AND t1.order_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t1.order_date BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
            <if test="companyIds != null and companyIds.size > 0">
                AND t1.company_id IN
                <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="clientName != null and clientName != ''">
                AND t1.client_name LIKE concat(#{clientName}, '%')
            </if>
            <if test="originatingCity!=null and originatingCity!=''">
                AND t1.originating_city LIKE concat(#{originating_city}, '%')
            </if>
            <if test="destinationCity!=null and destinationCity!=''">
                AND t1.destination_city LIKE concat(#{destinationCity}, '%')
            </if>
            GROUP BY ${groupDimensionConvert}
            UNION ALL
            SELECT
                2 AS costMode,
                t1.dept_id AS companyId,
                t1.client_id AS clientId,
                t2.client_code AS clientCode,
                t2.client_name AS clientName,
                t1.number AS totalNumber,
                0 AS totalWeight,
                0 AS totalVolume,
                '' AS originatingCity,
                '' AS destinationCity,
                1 AS businessType,
                3 AS codeType,
                IFNULL(SUM(t1.amount),0) AS settleAmount
            FROM bms_addedfee t1
            LEFT JOIN bms_clientinfo t2 ON t2.id = t1.client_id
            WHERE t1.del_flag = '0'
            AND t1.settle_type = '2'
            AND t1.fee_belong = 1
            <choose>
                <when test="dateType != null and dateType == 1">
                    AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 2">
                    AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
            <if test="companyIds != null and companyIds.size > 0">
                AND t1.dept_id IN
                <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="clientName != null and clientName != ''">
                AND t2.client_name LIKE concat(#{clientName}, '%')
            </if>
            GROUP BY ${groupDimensionConvert}
            UNION ALL
            SELECT
                2 AS costMode,
                t1.dept_id AS companyId,
                t1.client_id AS clientId,
                t2.client_code AS clientCode,
                t2.client_name AS clientName,
                t1.number AS totalNumber,
                0 AS totalWeight,
                0 AS totalVolume,
                '' AS originatingCity,
                '' AS destinationCity,
                2 AS businessType,
                4 AS codeType,
                IFNULL(SUM(t1.amount),0) AS settleAmount
            FROM bms_addedfee t1
            LEFT JOIN bms_clientinfo t2 ON t2.id = t1.client_id
            WHERE t1.del_flag = '0'
            AND t1.settle_type = '2'
            AND t1.fee_belong = 2
            <choose>
                <when test="dateType != null and dateType == 1">
                    AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 2">
                    AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
            <if test="companyIds != null and companyIds.size > 0">
                AND t1.dept_id IN
                <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="clientName != null and clientName != ''">
                AND t2.client_name LIKE concat(#{clientName}, '%')
            </if>
            GROUP BY ${groupDimensionConvert}
         ) main
        <where>
            <if test="businessType!=null">
                AND main.businessType = #{businessType}
            </if>
        </where>
        GROUP BY ${groupDimensionConvert}
        HAVING companyId IS NOT NULL
    </select>

    <select id="queryProfitAnalysisReport" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsAnalysisDto" >
        SELECT
            main.companyId AS companyId,
            main.clientId AS clientId,
            main.clientCode AS clientCode,
            main.clientName AS clientName,
            main.totalNumber AS totalNumber,
            main.totalWeight AS totalWeight,
            main.totalVolume AS totalVolume,
            main.originatingCity AS originatingCity,
            main.destinationCity AS destinationCity,
            main.businessType AS businessType,
            main.codeType AS codeType,
            SUM(IF(main.costMode = 1 and main.codeType = 1,IFNULL(main.settleAmount,0),0)) AS transIncome,
            SUM(IF(main.costMode = 1 and main.codeType = 2,IFNULL(main.settleAmount,0),0)) AS storageIncome,
            SUM(IF(main.costMode = 1 and main.codeType IN (3,4),IFNULL(main.settleAmount,0),0)) AS addedIncome,
            SUM(IF(main.costMode = 1 and main.codeType IN (5), IFNULL(main.settleAmount, 0), 0)) AS insideIncome,
            SUM(IF(main.costMode = 1 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0)) AS totalIncome,
            SUM(IF(main.costMode = 2 and main.codeType = 1,IFNULL(main.settleAmount,0),0)) AS transCost,
            SUM(IF(main.costMode = 2 and main.codeType = 2,IFNULL(main.settleAmount,0),0)) AS storageCost,
            SUM(IF(main.costMode = 2 and main.codeType IN (3,4),IFNULL(main.settleAmount,0),0)) AS addedCost,
            SUM(IF(main.costMode = 2 and main.codeType IN (5), IFNULL(main.settleAmount, 0), 0)) AS insideCost,
            SUM(IF(main.costMode = 2 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0)) AS totalCost,
            SUM(IF(main.costMode = 1 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0) - IF(main.costMode = 2 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0)) AS profitFee,
            ROUND(SUM(IF(main.costMode = 1 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0) - IF(main.costMode = 2 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0))/SUM(IF(main.costMode = 1 and main.codeType IN (1,2,3,4,5),IFNULL(main.settleAmount,0),0)), 2 ) * 100 AS grossMargin
        FROM (
        SELECT
            1 AS costMode,
            t1.company_id AS companyId,
            t1.client_id AS clientId,
            t1.client_code AS clientCode,
            t1.client_name AS clientName,
            SUM(IFNULL(t1.total_number, 0)) AS totalNumber,
            SUM(IFNULL(t1.total_weight, 0)) AS totalWeight,
            SUM(IFNULL(t1.total_volume, 0)) AS totalVolume,
            t1.originating_city AS originatingCity,
            t1.destination_city AS destinationCity,
            1 AS businessType,
            1 AS codeType,
            SUM(IFNULL(t2.settle_amount, 0)) AS settleAmount
        FROM bms_trans_code_info t1
        LEFT JOIN bms_ysexpenses_middle t2 ON t2.code_pk_id = t1.pk_id AND t2.code_type = 1 AND t2.del_flag = 0
        WHERE t1.del_flag = 0
        <choose>
            <when test="dateType != null and dateType == 1">
                AND t1.signing_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <when test="dateType != null and dateType == 2">
                AND t1.order_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <otherwise>
                AND t1.order_date BETWEEN #{startDate} AND #{endDate}
            </otherwise>
        </choose>
        <if test="companyIds != null and companyIds.size > 0">
            AND t1.company_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND t1.client_name LIKE concat(#{clientName}, '%')
        </if>
        <if test="originatingCity!=null and originatingCity!=''">
            AND t1.originating_city LIKE concat(#{originating_city}, '%')
        </if>
        <if test="destinationCity!=null and destinationCity!=''">
            AND t1.destination_city LIKE concat(#{destinationCity}, '%')
        </if>
        GROUP BY ${groupDimensionConvert}
        UNION ALL
        SELECT
            1 AS costMode,
            t1.company_id AS companyId,
            t1.client_id AS clientId,
            t1.client_code AS clientCode,
            t1.client_name AS clientName,
            SUM(IFNULL(t1.total_number, 0)) AS totalNumber,
            SUM(IFNULL(t1.total_weight, 0)) AS totalWeight,
            SUM(IFNULL(t1.total_volume, 0)) AS totalVolume,
            t1.originating_city AS originatingCity,
            t1.destination_city AS destinationCity,
            2 AS businessType,
            2 AS codeType,
            SUM(IFNULL(t2.settle_amount, 0)) AS settleAmount
        FROM bms_storage_code_info t1
        LEFT JOIN bms_ysexpenses_middle t2 ON t2.code_pk_id = t1.pk_id AND t2.code_type = 2 AND t2.del_flag = 0
        WHERE t1.del_flag = 0 AND t1.cost_mode = 1
        <choose>
            <when test="dateType != null and dateType == 1">
                AND t1.signing_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <when test="dateType != null and dateType == 2">
                AND t1.order_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <otherwise>
                AND t1.order_date BETWEEN #{startDate} AND #{endDate}
            </otherwise>
        </choose>
        <if test="companyIds != null and companyIds.size > 0">
            AND t1.company_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND t1.client_name LIKE concat(#{clientName}, '%')
        </if>
        <if test="originatingCity!=null and originatingCity!=''">
            AND t1.originating_city LIKE concat(#{originating_city}, '%')
        </if>
        <if test="destinationCity!=null and destinationCity!=''">
            AND t1.destination_city LIKE concat(#{destinationCity}, '%')
        </if>
        GROUP BY ${groupDimensionConvert}
        UNION ALL
        SELECT
            1 AS costMode,
            t1.dept_id AS companyId,
            t1.client_id AS clientId,
            t2.client_code AS clientCode,
            t2.client_name AS clientName,
            t1.number AS totalNumber,
            0 AS totalWeight,
            0 AS totalVolume,
            '' AS originatingCity,
            '' AS destinationCity,
            1 AS businessType,
            3 AS codeType,
            IFNULL(SUM(t1.amount),0) AS settleAmount
        FROM bms_addedfee t1
        LEFT JOIN bms_clientinfo t2 ON t2.id = t1.client_id
        WHERE t1.del_flag = '0'
        AND t1.settle_type = '1'
        AND t1.fee_belong = 1
        <choose>
            <when test="dateType != null and dateType == 1">
                AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <when test="dateType != null and dateType == 2">
                AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <otherwise>
                AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
            </otherwise>
        </choose>
        <if test="companyIds != null and companyIds.size > 0">
            AND t1.dept_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND t2.client_name LIKE concat(#{clientName}, '%')
        </if>
        GROUP BY ${groupDimensionConvert}
        UNION ALL
        SELECT
            1 AS costMode,
            t1.dept_id AS companyId,
            t1.client_id AS clientId,
            t2.client_code AS clientCode,
            t2.client_name AS clientName,
            t1.number AS totalNumber,
            0 AS totalWeight,
            0 AS totalVolume,
            '' AS originatingCity,
            '' AS destinationCity,
            2 AS businessType,
            4 AS codeType,
            IFNULL(SUM(t1.amount),0) AS settleAmount
        FROM bms_addedfee t1
        LEFT JOIN bms_clientinfo t2 ON t2.id = t1.client_id
        WHERE t1.del_flag = '0'
        AND t1.settle_type = '1'
        AND t1.fee_belong = 2
        <choose>
            <when test="dateType != null and dateType == 1">
                AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <when test="dateType != null and dateType == 2">
                AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <otherwise>
                AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
            </otherwise>
        </choose>
        <if test="companyIds != null and companyIds.size > 0">
            AND t1.dept_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND t2.client_name LIKE concat(#{clientName}, '%')
        </if>
        GROUP BY ${groupDimensionConvert}
        UNION ALL
        SELECT
            1 AS costMode,
            t1.originating_company_id AS companyId,
            t1.client_id AS clientId,
            t1.client_code AS client_code,
            t1.client_name AS clientName,
            SUM(IFNULL(t1.total_number, 0)) AS totalNumber,
            SUM(IFNULL(t1.total_weight, 0)) AS totalWeight,
            SUM(IFNULL(t1.total_volume, 0)) AS totalVolume,
            t1.originating_city AS originatingCity,
            t1.destination_city AS destinationCity,
            1 AS businessType,
            5 AS codeType,
            SUM(IFNULL(t3.freight+t3.delivery_fee+t3.handling_fee+t3.pick_fee+t3.ultrafar_fee+t3.add_points_fee+t3.reduce_points_fee+t3.hort_haul_fee+t3.excess_basket_fee+t3.abnormal_fee+t3.other_cost1+t3.other_cost2+t3.other_cost3+t3.other_cost4+t3.other_cost5+t3.other_cost6+t3.other_cost7+t3.other_cost8+t3.other_cost9+t3.other_cost10+t3.other_cost11+t3.other_cost12,0)) AS settleAmount
        FROM bms_job_code_info t1
        INNER JOIN bms_trans_code_info t2 ON t2.relate_code = t1.order_code AND t2.del_flag = 0
        LEFT JOIN bms_insidereceivefee t3 ON t3.job_id = t1.id AND t2.del_flag = '0'
        WHERE t1.del_flag = 0
        <choose>
            <when test="dateType != null and dateType == 1">
                AND t2.signing_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <when test="dateType != null and dateType == 2">
                AND t2.order_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <otherwise>
                AND t2.order_date BETWEEN #{startDate} AND #{endDate}
            </otherwise>
        </choose>
        <if test="companyIds != null and companyIds.size > 0">
            AND t1.originating_company_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND t1.client_name LIKE concat(#{clientName}, '%')
        </if>
        GROUP BY ${groupDimensionConvert}
        UNION ALL
        SELECT
            2 AS costMode,
            t1.company_id AS companyId,
            t1.client_id AS clientId,
            t1.client_code AS clientCode,
            t1.client_name AS client_name,
            SUM(IFNULL(t1.total_number, 0)) AS totalNumber,
            SUM(IFNULL(t1.total_weight, 0)) AS totalWeight,
            SUM(IFNULL(t1.total_volume, 0)) AS totalVolume,
            t1.originating_city AS originatingCity,
            t1.destination_city AS destinationCity,
            1 AS businessType,
            1 AS codeType,
            SUM(IFNULL(t3.settle_amount, 0)) AS settleAmount
        FROM bms_job_code_info t1
        INNER JOIN bms_trans_code_info t2 ON t2.relate_code = t1.order_code AND t2.del_flag = 0
        LEFT JOIN bms_yfexpenses_middle_share t3 ON t3.code_pk_id = t1.pk_id AND t2.del_flag = 0
        WHERE t1.del_flag = 0
        <choose>
            <when test="dateType != null and dateType == 1">
                AND t2.signing_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <when test="dateType != null and dateType == 2">
                AND t2.order_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <otherwise>
                AND t2.order_date BETWEEN #{startDate} AND #{endDate}
            </otherwise>
        </choose>
        <if test="companyIds != null and companyIds.size > 0">
            AND t1.company_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND t1.client_name LIKE concat(#{clientName}, '%')
        </if>
        <if test="originatingCity!=null and originatingCity!=''">
            AND t1.originating_city LIKE concat(#{originating_city}, '%')
        </if>
        <if test="destinationCity!=null and destinationCity!=''">
            AND t1.destination_city LIKE concat(#{destinationCity}, '%')
        </if>
        GROUP BY ${groupDimensionConvert}
        UNION ALL
        SELECT
            2 AS costMode,
            t1.company_id AS companyId,
            t1.client_id AS clientId,
            t1.client_code AS clientCode,
            t1.client_name AS client_name,
            SUM(IFNULL(t1.total_number, 0)) AS totalNumber,
            SUM(IFNULL(t1.total_weight, 0)) AS totalWeight,
            SUM(IFNULL(t1.total_volume, 0)) AS totalVolume,
            t1.originating_city AS originatingCity,
            t1.destination_city AS destinationCity,
            2 AS businessType,
            2 AS codeType,
            SUM(IFNULL(t2.settle_amount, 0)) AS settleAmount
        FROM bms_storage_code_info t1
        LEFT JOIN bms_yfexpenses_middle t2 ON t2.code_pk_id = t1.pk_id AND t2.code_type = 2 AND t2.del_flag = 0
        WHERE t1.del_flag = 0 AND t1.cost_mode = 2
        <choose>
            <when test="dateType != null and dateType == 1">
                AND t1.signing_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <when test="dateType != null and dateType == 2">
                AND t1.order_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <otherwise>
                AND t1.order_date BETWEEN #{startDate} AND #{endDate}
            </otherwise>
        </choose>
        <if test="companyIds != null and companyIds.size > 0">
            AND t1.company_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND t1.client_name LIKE concat(#{clientName}, '%')
        </if>
        <if test="originatingCity!=null and originatingCity!=''">
            AND t1.originating_city LIKE concat(#{originating_city}, '%')
        </if>
        <if test="destinationCity!=null and destinationCity!=''">
            AND t1.destination_city LIKE concat(#{destinationCity}, '%')
        </if>
        GROUP BY ${groupDimensionConvert}
        UNION ALL
        SELECT
            2 AS costMode,
            t1.dept_id AS companyId,
            t1.client_id AS clientId,
            t2.client_code AS clientCode,
            t2.client_name AS clientName,
            t1.number AS totalNumber,
            0 AS totalWeight,
            0 AS totalVolume,
            '' AS originatingCity,
            '' AS destinationCity,
            1 AS businessType,
            3 AS codeType,
            IFNULL(SUM(t1.amount),0) AS settleAmount
        FROM bms_addedfee t1
        LEFT JOIN bms_clientinfo t2 ON t2.id = t1.client_id
        WHERE t1.del_flag = '0'
        AND t1.settle_type = '2'
        AND t1.fee_belong = 1
        <choose>
            <when test="dateType != null and dateType == 1">
                AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <when test="dateType != null and dateType == 2">
                AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <otherwise>
                AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
            </otherwise>
        </choose>
        <if test="companyIds != null and companyIds.size > 0">
            AND t1.dept_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND t2.client_name LIKE concat(#{clientName}, '%')
        </if>
        GROUP BY ${groupDimensionConvert}
        UNION ALL
        SELECT
            2 AS costMode,
            t1.dept_id AS companyId,
            t1.client_id AS clientId,
            t2.client_code AS clientCode,
            t2.client_name AS clientName,
            t1.number AS totalNumber,
            0 AS totalWeight,
            0 AS totalVolume,
            '' AS originatingCity,
            '' AS destinationCity,
            2 AS businessType,
            4 AS codeType,
            IFNULL(SUM(t1.amount),0) AS settleAmount
        FROM bms_addedfee t1
        LEFT JOIN bms_clientinfo t2 ON t2.id = t1.client_id
        WHERE t1.del_flag = '0'
        AND t1.settle_type = '2'
        AND t1.fee_belong = 2
        <choose>
            <when test="dateType != null and dateType == 1">
                AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <when test="dateType != null and dateType == 2">
                AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <otherwise>
                AND t1.creat_date BETWEEN #{startDate} AND #{endDate}
            </otherwise>
        </choose>
        <if test="companyIds != null and companyIds.size > 0">
            AND t1.dept_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND t2.client_name LIKE concat(#{clientName}, '%')
        </if>
        GROUP BY ${groupDimensionConvert}
        UNION ALL
        SELECT
            2 AS costMode,
            t1.pay_company_id AS companyId,
            t1.client_id AS clientId,
            t1.client_code AS client_code,
            t1.client_name AS clientName,
            SUM(IFNULL(t1.total_number, 0)) AS totalNumber,
            SUM(IFNULL(t1.total_weight, 0)) AS totalWeight,
            SUM(IFNULL(t1.total_volume, 0)) AS totalVolume,
            t1.originating_city AS originatingCity,
            t1.destination_city AS destinationCity,
            1 AS businessType,
            5 AS codeType,
            SUM(IFNULL(t3.freight+t3.delivery_fee+t3.handling_fee+t3.pick_fee+t3.ultrafar_fee+t3.add_points_fee+t3.reduce_points_fee+t3.hort_haul_fee+t3.excess_basket_fee+t3.abnormal_fee+t3.other_cost1+t3.other_cost2+t3.other_cost3+t3.other_cost4+t3.other_cost5+t3.other_cost6+t3.other_cost7+t3.other_cost8+t3.other_cost9+t3.other_cost10+t3.other_cost11+t3.other_cost12,0)) AS settleAmount
        FROM bms_job_code_info t1
        INNER JOIN bms_trans_code_info t2 ON t2.relate_code = t1.order_code AND t2.del_flag = 0
        LEFT JOIN bms_insidereceivefee t3 ON t3.job_id = t1.id AND t2.del_flag = '0'
        WHERE t1.del_flag = 0
        <choose>
            <when test="dateType != null and dateType == 1">
                AND t2.signing_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <when test="dateType != null and dateType == 2">
                AND t2.order_date BETWEEN #{startDate} AND #{endDate}
            </when>
            <otherwise>
                AND t2.order_date BETWEEN #{startDate} AND #{endDate}
            </otherwise>
        </choose>
        <if test="companyIds != null and companyIds.size > 0">
            AND t1.pay_company_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND t1.client_name LIKE concat(#{clientName}, '%')
        </if>
        GROUP BY ${groupDimensionConvert}
        ) main
        <where>
            <if test="businessType!=null">
                AND main.businessType = #{businessType}
            </if>
        </where>
        GROUP BY ${groupDimensionConvert}
        HAVING companyId IS NOT NULL
    </select>



    <!-- 毛利率报表主查询 -->
    <select id="queryGrossMarginReport" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsProfitAnalysisDynamicDto">
        <choose>
            <when test="businessType == 1">
                SELECT
                    t1.code_pk_id AS codePkId,
                    t1.code_id AS codeId,
                    t1.relate_code AS relateCode,
                    1 AS codeType,
                    t2.client_id AS clientId,
                    t2.client_code AS clientCode,
                    t2.client_name AS clientName,
                    t4.client_group AS clientGroup,
                    t2.company_id AS companyId,
                    t2.originating_province AS originatingProvince,
                    t2.originating_city AS originatingCity,
                    t2.originating_area AS originatingArea,
                    t2.originating_address AS originatingAddress,
                    t2.destination_province AS destinationProvince,
                    t2.destination_city AS destinationCity,
                    t2.destination_area AS destinationArea,
                    t2.destination_address AS destinationAddress,
                    t2.total_number AS totalNumber,
                    t2.total_weight AS totalWeight,
                    t2.total_volume AS totalVolume,
                    SUM(IFNULL(t1.freight,0)) AS freight,
                    SUM(IFNULL(t1.delivery_fee,0)) AS deliveryFee,
                    SUM(IFNULL(t1.ultrafar_fee,0)) AS ultrafarFee,
                    SUM(IFNULL(t1.superframes_fee,0)) AS superframesFee,
                    SUM(IFNULL(t1.excess_fee,0)) AS excessFee,
                    SUM(IFNULL(t1.reduce_fee,0)) AS reduceFee,
                    SUM(IFNULL(t1.outboundsorting_fee,0)) AS outboundsortingFee,
                    SUM(IFNULL(t1.shortbarge_fee,0)) AS shortbargeFee,
                    SUM(IFNULL(t1.return_fee,0)) AS returnFee,
                    SUM(IFNULL(t1.exception_fee,0)) AS exceptionFee,
                    SUM(IFNULL(t1.other_cost1,0)+IFNULL(t1.other_cost2,0)+IFNULL(t1.other_cost3,0)+IFNULL(t1.other_cost4,0)+IFNULL(t1.other_cost5,0)+IFNULL(t1.other_cost6,0)+IFNULL(t1.other_cost7,0)+IFNULL(t1.other_cost8,0)+IFNULL(t1.other_cost9,0)+IFNULL(t1.other_cost10,0)+IFNULL(t1.other_cost11,0)+IFNULL(t1.other_cost12,0)) AS totalOtherFee,
                    IFNULL(SUM(t3.amount),0) AS totalAddedFee,
                    IFNULL(SUM(t1.settle_amount),0) AS totalIncome
                FROM bms_ysexpenses_middle t1
                LEFT JOIN bms_trans_code_info t2 ON t2.pk_id = t1.code_pk_id AND t2.del_flag = 0
                LEFT JOIN bms_addedfee t3 ON t3.relate_code = t2.relate_code AND t3.del_flag = '0' AND t3.settle_type = '1' AND t3.fee_belong = 1 AND t3.code_type = 1
                LEFT JOIN bms_clientinfo t4 ON t4.id = t2.client_id
                WHERE t1.del_flag = 0
                AND t1.code_type = 1
                <if test="companyIds != null and companyIds.size > 0">
                    AND t2.company_id IN
                    <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                        #{companyId}
                    </foreach>
                </if>
                <choose>
                    <when test="dateType != null and dateType == 1">
                        AND t2.signing_date BETWEEN #{startDate} AND #{endDate}
                    </when>
                    <when test="dateType != null and dateType == 2">
                        AND t2.order_date BETWEEN #{startDate} AND #{endDate}
                    </when>
                    <otherwise>
                        AND t2.order_date BETWEEN #{startDate} AND #{endDate}
                    </otherwise>
                </choose>
                <if test="clientName !=null and clientName!=''">
                    AND t2.client_name LIKE CONCAT( #{clientName}, '%')
                </if>
                <if test="clientIds !=null and clientIds.size > 0">
                    AND t2.client_id IN
                    <foreach item="clientId" collection="clientIds" open="(" separator="," close=")">
                        #{clientId}
                    </foreach>
                </if>
                <if test="originatingCity != null and originatingCity != ''">
                    AND t2.originating_city = #{originatingCity}
                </if>
                <if test="destinationCity != null and destinationCity != ''">
                    AND t2.destination_city = #{destinationCity}
                </if>
                <if test="relateCode!=null and relateCode!=''">
                    AND t2.relate_code = #{relateCode}
                </if>
                GROUP BY relateCode
            </when>
            <otherwise>
                SELECT
                    t1.code_pk_id AS codePkId,
                    t1.code_id AS codeId,
                    t1.relate_code AS relateCode,
                    t2.code_type AS codeType,
                    t2.client_id AS clientId,
                    t2.client_code AS clientCode,
                    t2.client_name AS clientName,
                    t4.client_group AS clientGroup,
                    t2.company_id AS companyId,
                    t2.warehouse_code AS warehouseCode,
                    t5.warehouse_name AS warehouseName,
                    t2.originating_province AS originatingProvince,
                    t2.originating_city AS originatingCity,
                    t2.originating_area AS originatingArea,
                    t2.originating_address AS originatingAddress,
                    t2.destination_province AS destinationProvince,
                    t2.destination_city AS destinationCity,
                    t2.destination_area AS destinationArea,
                    t2.destination_address AS destinationAddress,
                    t2.total_number AS totalNumber,
                    t2.total_weight AS totalWeight,
                    t2.total_volume AS totalVolume,
                    SUM(IFNULL(t1.freight,0)) AS freight,
                    SUM(IFNULL(t1.delivery_fee,0)) AS deliveryFee,
                    SUM(IFNULL(t1.ultrafar_fee,0)) AS ultrafarFee,
                    SUM(IFNULL(t1.superframes_fee,0)) AS superframesFee,
                    SUM(IFNULL(t1.excess_fee,0)) AS excessFee,
                    SUM(IFNULL(t1.reduce_fee,0)) AS reduceFee,
                    SUM(IFNULL(t1.outboundsorting_fee,0)) AS outboundsortingFee,
                    SUM(IFNULL(t1.shortbarge_fee,0)) AS shortbargeFee,
                    SUM(IFNULL(t1.return_fee,0)) AS returnFee,
                    SUM(IFNULL(t1.exception_fee,0)) AS exceptionFee,
                    SUM(IFNULL(t1.other_cost1,0) + IFNULL(t1.other_cost2,0) + IFNULL(t1.other_cost3,0) + IFNULL(t1.other_cost4,0) + IFNULL(t1.other_cost5,0) + IFNULL(t1.other_cost6,0) + IFNULL(t1.other_cost7,0) + IFNULL(t1.other_cost8,0) + IFNULL(t1.other_cost9,0) + IFNULL(t1.other_cost10,0) + IFNULL(t1.other_cost11,0) + IFNULL(t1.other_cost12,0)) AS totalOtherFee,
                    IFNULL(SUM(t3.amount),0) AS totalAddedFee,
                    IFNULL(SUM(t1.settle_amount),0) AS totalIncome
                FROM bms_ysexpenses_middle t1
                LEFT JOIN bms_storage_code_info t2 ON t2.pk_id = t1.code_pk_id AND t2.del_flag = 0 AND t2.cost_mode = 1
                LEFT JOIN bms_addedfee t3 ON t3.relate_code = t2.relate_code AND t3.del_flag = '0' AND t3.settle_type = '1' AND t3.fee_belong = 2 AND t3.code_type = t2.code_type
                LEFT JOIN bms_clientinfo t4 ON t4.id = t2.client_id
                LEFT JOIN mdm_warehouseinfo t5 ON t5.warehouse_code = t2.warehouse_code AND t5.del_flag = 0
                WHERE t1.del_flag = 0
                AND t1.code_type = 2
                <if test="companyIds != null and companyIds.size>0">
                    AND t2.company_id IN
                    <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                        #{companyId}
                    </foreach>
                </if>
                <choose>
                    <when test="dateType != null and dateType == 1">
                        AND t2.signing_date BETWEEN #{startDate} AND #{endDate}
                    </when>
                    <when test="dateType != null and dateType == 2">
                        AND t2.order_date BETWEEN #{startDate} AND #{endDate}
                    </when>
                    <otherwise>
                        AND t2.order_date BETWEEN #{startDate} AND #{endDate}
                    </otherwise>
                </choose>
                <if test="clientName !=null and clientName!=''">
                    AND t2.client_name LIKE CONCAT( #{clientName}, '%')
                </if>
                <if test="clientIds !=null and clientIds.size > 0">
                    AND t2.client_id IN
                    <foreach item="clientId" collection="clientIds" open="(" separator="," close=")">
                        #{clientId}
                    </foreach>
                </if>
                <if test="originatingCity != null and originatingCity != ''">
                    AND t2.originating_city = #{originatingCity}
                </if>
                <if test="destinationCity != null and destinationCity != ''">
                    AND t2.destination_city = #{destinationCity}
                </if>
                <if test="relateCode!=null and relateCode!=''">
                    AND t2.relate_code = #{relateCode}
                </if>
                <if test="warehouseCodes!=null and warehouseCodes.size>0">
                    AND t2.warehouse_code IN
                    <foreach item="warehouseCode" collection="warehouseCodes" open="(" separator="," close=")">
                        #{warehouseCode}
                    </foreach>
                </if>
                <if test="warehouseCode!=null and warehouseCode!=''">
                    AND t2.warehouse_code LIKE CONCAT( #{warehouseCode}, '%')
                </if>
                <if test="warehouseName!=null and warehouseName!=''">
                    AND t5.warehouse_name LIKE CONCAT( #{warehouseName}, '%')
                </if>
                GROUP BY relateCode,codeType
            </otherwise>
        </choose>
    </select>


    <!-- 毛利率报表支出明细查询 -->
    <select id="queryGrossMarginExpenseDetailReport" resultType="com.bbyb.joy.bms.domain.dto.reportdto.BmsProfitAnalysisDynamicExpenseDto">
        <choose>
        <when test="businessType == 1">
            SELECT
                t2.order_code AS orderCode,
                t1.code_pk_id AS codePkId,
                t2.relate_code AS relateCode,
                1 AS codeType,
                t3.virtual_scheduling_code AS virtualSchedulingCode,
                t3.scheduling_code AS schedulingCode,
                t3.original_scheduling_code AS originalSchedulingCode,
                t3.carrier_id AS carrierId,
                t3.carrier_code AS carrierCode,
                t3.carrier_name AS carrierName,
                SUM(IFNULL(t1.freight,0) + IFNULL(t1.delivery_fee,0) + IFNULL(t1.ultrafar_fee,0) + IFNULL(t1.superframes_fee,0) + IFNULL(t1.excess_fee,0) + IFNULL(t1.reduce_fee,0) + IFNULL(t1.outboundsorting_fee,0) + IFNULL(t1.shortbarge_fee,0) + IFNULL(t1.return_fee,0) + IFNULL(t1.exception_fee,0)) AS totalBaseFee,
                SUM(IFNULL(t1.other_cost1,0) + IFNULL(t1.other_cost2,0) + IFNULL(t1.other_cost3,0) + IFNULL(t1.other_cost4,0) + IFNULL(t1.other_cost5,0) + IFNULL(t1.other_cost6,0) + IFNULL(t1.other_cost7,0) + IFNULL(t1.other_cost8,0) + IFNULL(t1.other_cost9,0) + IFNULL(t1.other_cost10,0) + IFNULL(t1.other_cost11,0) + IFNULL(t1.other_cost12,0)) AS totalOtherFee,
                SUM(t1.settle_amount) AS totalFee,
                IFNULL(SUM(t4.amount),0) AS totalAddedFee,
                IFNULL(SUM(t4.amount),0) + SUM(t1.settle_amount) AS totalCost
            FROM bms_yfexpenses_middle_share t1
            LEFT JOIN bms_job_code_info t2 ON t2.relate_code = t1.relate_code AND t2.del_flag = 0
            LEFT JOIN bms_dispatch_code_info t3 ON t3.pk_id = t2.main_pk_id AND t3.del_flag = 0
            LEFT JOIN bms_addedfee t4 ON t4.relate_code = t2.order_code AND t4.del_flag = '0' AND t4.settle_type = '2' AND t4.code_type = 1
            WHERE t1.del_flag = 0
            <if test="relateCodes != null and relateCodes.size>0">
                AND t2.order_code IN
                <foreach item="relateCode" collection="relateCodes" open="(" separator="," close=")">
                    #{relateCode}
                </foreach>
            </if>
            GROUP BY relateCode,codePkId
        </when>
        <otherwise>
            SELECT
                t1.code_pk_id AS codePkId,
                t1.relate_code AS orderCode,
                t1.relate_code AS relateCode,
                t2.code_type AS codeType,
                t2.carrier_id AS carrierId,
                t2.carrier_code AS carrierCode,
                t2.carrier_name AS carrierName,
                SUM(IFNULL(t1.freight,0) + IFNULL(t1.delivery_fee,0) + IFNULL(t1.ultrafar_fee,0) + IFNULL(t1.superframes_fee,0) + IFNULL(t1.excess_fee,0) + IFNULL(t1.reduce_fee,0) + IFNULL(t1.outboundsorting_fee,0) + IFNULL(t1.shortbarge_fee,0) + IFNULL(t1.return_fee,0) + IFNULL(t1.exception_fee,0)) AS totalBaseFee,
                SUM(IFNULL(t1.other_cost1,0) + IFNULL(t1.other_cost2,0) + IFNULL(t1.other_cost3,0) + IFNULL(t1.other_cost4,0) + IFNULL(t1.other_cost5,0) + IFNULL(t1.other_cost6,0) + IFNULL(t1.other_cost7,0) + IFNULL(t1.other_cost8,0) + IFNULL(t1.other_cost9,0) + IFNULL(t1.other_cost10,0) + IFNULL(t1.other_cost11,0) + IFNULL(t1.other_cost12,0)) AS totalOtherFee,
                SUM(t1.settle_amount) AS totalFee,
                IFNULL(SUM(t3.amount),0) AS totalAddedFee,
                IFNULL(SUM(t3.amount),0) + SUM(t1.settle_amount) AS totalCost
            FROM bms_yfexpenses_middle t1
            LEFT JOIN bms_storage_code_info t2 ON t2.relate_code = t1.relate_code AND t2.del_flag = 0
            LEFT JOIN bms_addedfee t3 ON t3.relate_code = t2.relate_code AND t3.del_flag = '0' AND t3.settle_type = '2' AND t3.code_type = t2.code_type
            WHERE t1.del_flag = 0 AND t1.code_type = 2
            <if test="relateCodes != null and relateCodes.size>0">
                AND t2.relate_code IN
                <foreach item="relateCode" collection="relateCodes" open="(" separator="," close=")">
                    #{relateCode}
                </foreach>
            </if>
            GROUP BY relateCode,codePkId;
        </otherwise>
        </choose>
    </select>






</mapper>
