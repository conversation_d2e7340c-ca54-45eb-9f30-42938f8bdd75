<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYsbillcodeinfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeinfo" id="BmsYsbillcodeinfoResult">
        <result property="id"    column="id"    />
        <result property="pkId"    column="pk_id"    />
        <result property="codeType"    column="code_type"    />
        <result property="relateCode"    column="relate_code"    />
        <result property="schedulingBillCode"    column="scheduling_bill_code"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="companyId"    column="company_id"    />
        <result property="networkCode"    column="network_code"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="totalNumber"    column="total_number"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="cargoValue"    column="cargo_value"    />
        <result property="orderDate"    column="order_date"    />
        <result property="signingDate"    column="signing_date"    />
        <result property="ifAutarky"    column="if_autarky"    />
        <result property="storageServiceProvider"    column="storage_service_provider"    />
        <result property="lineCode"    column="line_code"    />
        <result property="lineName"    column="line_name"    />
        <result property="ifBaseStores"    column="if_base_stores"    />
        <result property="ifSuperBaseKilometer"    column="if_Super_base_kilometer"    />
        <result property="storeDistanceKilometer"    column="store_distance_kilometer"    />
        <result property="deliveryCode"    column="delivery_code"    />
        <result property="deliveryName"    column="delivery_name"    />
        <result property="storeCode"    column="store_code"    />
        <result property="receivingStore"    column="receiving_store"    />
        <result property="provinceOrigin"    column="province_origin"    />
        <result property="originatingCity"    column="originating_city"    />
        <result property="originatingArea"    column="originating_area"    />
        <result property="originatingAddress"    column="originating_address"    />
        <result property="destinationProvince"    column="destination_province"    />
        <result property="destinationCity"    column="destination_city"    />
        <result property="destinationArea"    column="destination_area"    />
        <result property="destinationAddress"    column="destination_address"    />
        <result property="costStatus"    column="cost_status"    />
        <result property="billingStatus"    column="billing_status"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="deliveryMode"    column="delivery_mode"    />
        <result property="isRejected"    column="is_rejected"    />
        <result property="rejectParties"    column="reject_parties"    />
        <result property="orderType"    column="order_type"    />
        <result property="nearStoreKm"    column="near_store_km"    />
        <result property="carType"    column="car_type"    />
        <result property="carModel"    column="car_model"    />
        <result property="cwFullCases"    column="cw_full_cases"    />
        <result property="cwSplitCases"    column="cw_split_cases"    />
        <result property="ldFullCases"    column="ld_full_cases"    />
        <result property="ldSplitCases"    column="ld_split_cases"    />
        <result property="lcFullCases"    column="lc_full_cases"    />
        <result property="lcSplitCases"    column="lc_split_cases"    />
        <result property="splitTotalNumber"    column="split_total_number"    />
        <result property="skuNumber"    column="sku_number"    />
        <result property="palletNumber"    column="pallet_number"    />
        <result property="transportType"    column="transport_type"    />
        <result property="transportTypeApi"    column="transport_type"    />
        <result property="skuString"    column="skuString"    />
        <result property="temperatureTypeStr"    column="temperatureTypeStr"    />
        <result property="cwPalletNumber"    column="cw_pallet_number"    />
        <result property="lcPalletNumber"    column="lc_pallet_number"    />
        <result property="ldPalletNumber"    column="ld_pallet_number"    />
        <result property="orderNo"    column="order_no"    />
        <result property="platformCode"    column="platform_code"    />
        <result property="isTimeout"    column="is_timeout"    />
        <result property="skuNumber"    column="sku_number"    />
        <result property="settleSetting"    column="settle_setting"    />
        <result property="carrierpayType"    column="carrierpay_type"    />

    </resultMap>

    <sql id="selectBmsYsbillcodeinfoVo">
        select id, code_type, relate_code, scheduling_bill_code,
               client_code, client_name, company_id, network_code,
               warehouse_code, total_boxes, total_number, total_weight,
               total_volume, cargo_value, order_date, signing_date,
               if_autarky, storage_service_provider, line_code, line_name,
               if_base_stores, if_Super_base_kilometer, store_distance_kilometer,
               delivery_code, delivery_name, store_code, receiving_store,
               province_origin, originating_City, originating_area,
               originating_address, destination_Province, destination_city,
               destination_area, destination_address, cost_status,
               billing_status, create_code, create_by, create_dept_id,
               create_time, oper_code, oper_by, oper_dept_id, oper_time,
               del_flag, delivery_mode, is_rejected, reject_parties,order_type,
               near_store_km,car_type,car_model,cw_full_cases,cw_split_cases,ld_full_cases,ld_split_cases,lc_full_cases,lc_split_cases,pallet_number, transport_type,skuString
        from bms_ysbillcodeinfo
    </sql>


    <select id="getcodeListGroupRelation" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        select * from (
        select
        ext.warehouse_code as warehouseCode,
        a.expenses_type as ysbilType,
        b.client_code as clientCode,
        b.client_name as clientName,
        a.institution_id as companyId,
        a.bill_date as billDate,
        b.id as clientId,
        GROUP_CONCAT(DISTINCT cn.bill_name) as belongRegion,
        GROUP_CONCAT(a.id) as idsStr
        from  bms_yscost_info a
        inner join bms_clientinfo b
        on a.client_id = b.id
        left join bms_yscost_extend ext
        on ext.expenses_id = a.id
        LEFT JOIN company_network cn ON FIND_IN_SET(cn.warehouse_code ,ext.warehouse_code)
        <where>
            a.del_flag = 0
            and IFNULL(a.bill_id,0)=0
            AND a.id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </where>
        group by b.client_code,a.bill_date,a.institution_id,cn.bill_name
        union all
        select
        '' as warehouseCode,
        '3' as ysbilType,
        a.relation_code as clientCode,
        d.client_name as clientName,
        a.company_id as companyId,
        a.bill_date as billDate,
        d.id as clientId,
        '' as belongRegion,
        null as idsStr
        from bms_yspurchase_feeinfo a
        inner join bms_clientinfo d
        on a.relation_code = d.client_code
        <where>
            a.del_flag = 0
            and IFNULL(a.ysbill_id,0)=0
            AND a.id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </where>
        group by a.relation_code,a.bill_date,a.company_id
        ) a
    </select>





    <select id="getcodeListGroupRelationByNot" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        select * from (
        select
        d.client_code as clientCode,
        c.expenses_code as expensesCode,
        d.id as clientId
        from  bms_yscost_info c
        inner join bms_clientinfo d
        on c.client_id = d.id
        <where>
            and IFNULL(c.bill_id,0)!=0
            AND c.id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </where>
        group by d.client_code
        union all
        select
        a.relation_code as clientCode,
        a.ys_feecode as expensesCode,
        d.id as clientId
        from bms_yspurchase_feeinfo a
        inner join bms_clientinfo d
        on a.relation_code = d.client_code
        <where>
            a.del_flag = 0
            and IFNULL(a.ysbill_id,0)!=0
            AND CONCAT(a.id,'') in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </where>
        group by a.relation_code
        ) a
    </select>



    <select id="countCustomDocument" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        select * from (
        select
        d.client_code as clientCode,
        c.expenses_type as ysbilType,
        d.id as clientId
        from  bms_yscost_info c
        inner join bms_clientinfo d
        on a.client_code = d.client_code
        <where>
            and IFNULL(c.bill_id,0)=0
            <if test="ids == null or ids.size==0">
                and 1!=1
            </if>
            <if test="ids != null and ids.size>0 ">
                AND a.id in
                <foreach collection="ids" item="ids" open="(" separator="," close=")">
                    #{ids}
                </foreach>
            </if>
        </where>
        group by a.client_code

        union all

        select
        a.relation_code as clientCode,
        '3' as ysbilType,
        d.id as clientId
        from bms_yspurchase_feeinfo a
        inner join bms_clientinfo d
        on a.relation_code = d.client_code
        <where>
            a.del_flag = 0
            and IFNULL(a.ysbill_id,0)=0
            <if test="ids == null or ids.size==0">
                and 1!=1
            </if>
            <if test="ids != null and ids.size>0 ">
                AND CONCAT(a.id,'') in
                <foreach collection="ids" item="ids" open="(" separator="," close=")">
                    #{ids}
                </foreach>
            </if>
        </where>
        group by a.relation_code
        ) a
    </select>


    <select id="countCustomDocumentAmtSumByFeeFlag" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        select * from (
        select
        IFNULL(c.freight,'0') as freight,
        IFNULL(c.delivery_fee,'0') as deliveryFee,
        IFNULL(c.Outboundsorting_fee,'0') as OutboundsortingFee,
        IFNULL(c.shortbarge_fee,'0') as shortbargeFee,
        IFNULL(c.superframes_fee,'0') as superframesFee,
        IFNULL(c.excess_fee,'0') as excessFee,
        IFNULL(c.reduce_fee,'0') as reduceFee,
        IFNULL(c.Ultrafar_fee,'0') as UltrafarFee,
        IFNULL(c.exception_fee,'0') as exceptionFee,
        IFNULL(c.adjust_fee,'0') as adjustFee,
        IFNULL(c.return_fee,0) as returnFee,
        IFNULL(c.other_cost1,'0') as otherCost1,
        IFNULL(c.other_cost2,'0') as otherCost2,
        IFNULL(c.other_cost3,'0') as otherCost3,
        IFNULL(c.other_cost4,'0') as otherCost4,
        IFNULL(c.other_cost5,'0') as otherCost5,
        IFNULL(c.other_cost6,'0') as otherCost6,
        IFNULL(c.other_cost7,'0') as otherCost7,
        IFNULL(c.other_cost8,'0') as otherCost8,
        IFNULL(c.other_cost9,'0') as otherCost9,
        IFNULL(c.other_cost10,'0') as otherCost10,
        IFNULL(c.other_cost11,'0') as otherCost11,
        IFNULL(c.other_cost12,'0') as otherCost12,
        #IFNULL(c.upstairs_fee,'0') as upstairsFee,
        #IFNULL(c.counting_fee,'0') as countingFee,
        #IFNULL(c.overtime_pay,'0') as overtimePay,
        #IFNULL(c.express_fee,'0') as expressFee,
        #IFNULL(c.other_cost_one,'0') as otherCostOne,
        #IFNULL(c.other_cost_two,'0') as otherCostTwo,
        #IFNULL(c.other_cost_three,'0') as otherCostThree,
        #IFNULL(c.other_cost_four,'0') as otherCostFour,
        #IFNULL(c.other_cost_five,'0') as otherCostFive,
        d.client_code as clientCode,
        d.client_name as clientName,
        c.institution_id as companyId,
        null as ysbillId,
        null as codeInfoId,
        c.pk_id as codeInfoPkId,
        d.payment_days as paymentDays,
        c.expenses_type as ysbilType
        from bms_yscost_info c
        inner join bms_clientinfo d on c.client_id = d.id
        inner join bms_ysexpenses_middle e on c.main_expense_id = e.main_expense_id and e.del_flag = '0'
        <where>
            IFNULL(c.del_flag,0) = 0
            and IFNULL(c.bill_id,0) = 0
            and IFNULL(c.fee_flag,1) =2
            AND c.id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
            <if test="ysbilType != null and ysbilType!=''">
                and c.expenses_type in
                <foreach collection="ysbilType" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="ids == null or ids.size==0">
                and 1!=1
            </if>
        </where>
        union all
        select
        IFNULL(a.total_amount,'0') as freight,
        '0' as deliveryFee,
        '0' as OutboundsortingFee,
        '0' as shortbargeFee,
        '0' as superframesFee,
        '0' as excessFee,
        '0' as reduceFee,
        '0' as UltrafarFee,
        '0' as exceptionFee,
        '0'  as adjustFee,
        '0' as returnFee,
        '0' as otherCost1,
        '0' as otherCost2,
        '0' as otherCost3,
        '0' as otherCost4,
        '0' as otherCost5,
        '0' as otherCost6,
        '0' as otherCost7,
        '0' as otherCost8,
        '0' as otherCost9,
        '0'  as otherCost10,
        '0'  as otherCost11,
        '0'  as otherCost12,
        #'0' as upstairsFee,
        #'0' as countingFee,
        #'0' as overtimePay,
        #'0' as expressFee,
        #'0' as otherCostOne,
        #'0' as otherCostTwo,
        #'0' as otherCostThree,
        #'0' as otherCostFour,
        #'0' as otherCostFive,
        a.relation_code as clientCode,
        a.relation_name as clientName,
        a.company_id as companyId,
        a.id as ysbillId,
        '' as codeInfoId,
        null as codeInfoPkId,
        '' as paymentDays,
        '3' as ysbilType
        from bms_yspurchase_feeinfo a
        left join bms_clientinfo b
        on a.relation_code = b.id
        <where>
            IFNULL(a.del_flag,0) = 0
            and IFNULL(a.creat_flag,0) = 0
            and IFNULL(a.fee_flag,1) = 2
            <if test="ids == null or ids.size==0">
                and 1!=1
            </if>
            AND CONCAT(a.id,'') in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </where>
        ) a
    </select>



    <select id="selectOrderBill1" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        select * FROM
        (SELECT
        y.id ysbillId
        ,null expensesId
        ,null yscostId
        ,y.relate_code relateCode
        ,IF(y.fail_remark is not null and y.fail_remark!='',3,y.cost_status) as costStatus
        ,null chargeType
        ,null expensesCode
        ,DATE_FORMAT(y.signing_date,'%Y-%m-%d %H:%i:%s') signingDates
        ,y.company_id companyId
        ,mc.id as clientId
        ,y.client_name clientName
        ,y.client_code clientCode
        ,null ruleName
        ,y.receiving_store receivingStore
        ,0 freight
        ,0 deliveryFee
        ,0 outboundsortingFee
        ,0 shortbargeFee
        ,0 superframesFee
        ,0 excessFee
        ,0 reduceFee
        ,0 ultrafarFee
        ,0 exceptionFee
        ,0 returnFee
        ,0 adjustFee
        ,0 otherCost1
        ,0 otherCost2
        ,0 otherCost3
        ,0 otherCost4
        ,0 otherCost5
        ,0 otherCost6
        ,0 otherCost7
        ,0 otherCost8
        ,0 otherCost9
        ,0 otherCost10
        ,0 otherCost11
        ,0 otherCost12
        ,IFNULL(y.total_boxes,0) totalBoxes
        ,IFNULL(y.total_number,0) totalNumber
        ,IFNULL(y.sku_number,0) skuNumber
        ,IFNULL(y.total_weight,0) totalWeight
        ,IFNULL(y.total_volume,0) totalVolume
        ,IFNULL(y.cargo_value,0) cargoValue
        ,null feeFlag
        ,null remarks
        ,null automaticBillingRemark
        ,null adjustRemark
        ,null billDate
        ,IFNULL(y.create_by,y.import_by) expensesBy
        ,DATE_FORMAT(y.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,y.billing_status billingStatus
        ,null billCode
        ,y.del_flag delFlag
        ,y.order_date orderDate
        ,DATE_FORMAT(y.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,y.signing_date signingDate
        ,y.oper_time as operTime
        ,y.code_type codeType
        ,y.warehouse_code warehouseCode
        ,y.create_time createTime
        ,null costDimension
        ,y.near_store_km nearStoreKm
        ,y.car_type carType
        ,y.car_model carModel
        ,y.cw_full_cases cwFullCases
        ,y.cw_split_cases cwSplitCases
        ,y.ld_full_cases ldFullCases
        ,y.ld_split_cases ldSplitCases
        ,y.lc_full_cases lcFullCases
        ,y.lc_split_cases lcSplitCases
        ,y.province_origin provinceOrigin
        ,y.originating_city originatingCity
        ,y.originating_area originatingArea
        ,y.destination_province destinationProvince
        ,y.destination_city destinationCity
        ,y.destination_area destinationArea
        ,y.delivery_mode deliveryMode
        ,y.delivery_code deliveryCode
        ,0 baseCostTotal
        ,0 otherCostTotal
        ,0 totalFee
        ,null costClientId
        ,DATE_FORMAT(y.create_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,null billId
        ,null billType
        ,y.transport_type transportTypeApi
        ,0 isIncrement -- 是否为增值费0否1是
        ,null feeTypeFirst -- 计费大类
        ,null totalQuantity -- 附加费总数量
        ,null price -- 附加费单价
        ,y.store_code AS storeCode
        ,y.receiving_store AS storeName
        ,y.transport_type AS transportType
        ,y.order_type AS orderType
        ,y.cw_pallet_number AS cwPalletNumber
        ,y.lc_pallet_number AS lcPalletNumber
        ,y.ld_pallet_number AS ldPalletNumber
        ,y.order_no AS orderNo
        ,y.platform_code AS platformCode
        ,y.import_time AS importTime
        ,y.import_code AS importCode
        ,IFNULL(y.import_by,y.create_by) AS importBy
        ,y.order_source AS orderSource
        ,y.is_timeout AS isTimeOut
        ,CASE y.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        ,y.fail_remark AS failRemark
        ,null AS showBillCode
        ,null AS showBillId,
        null AS extraField1,
         1 AS ysbilType,
        null AS mainExpenseCode,
        null AS mainExpenseId
        FROM bms_ysbillcodeinfo y
        LEFT JOIN mdm_warehouseinfo mw ON y.warehouse_code=mw.warehouse_code
        LEFT JOIN bms_clientinfo mc ON mc.client_code=y.client_code
        WHERE y.del_flag=0 AND y.cost_status!='1'
        <if test="isIncrement != null">
            AND 0 = #{isIncrement}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="importBy != null and importBy != ''">
            and IFNULL(y.import_by,y.create_by) like concat('%',trim(#{importBy}),'%')
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="warehouseName !=null and warehouseName !=''">
            and mw.warehouse_name like concat('%', #{warehouseName}, '%')
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND y.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != '' and costStatus !=3">
            AND y.cost_status = #{costStatus} AND y.fail_remark is null
        </if>
        <if test="costStatus != null and costStatus == 3">
            AND y.fail_remark is not null
        </if>
        <if test="clientName != null and clientName != ''">
            AND y.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND y.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND y.receiving_store like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND y.delivery_mode = #{deliveryMode}
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND 1 = 2
        </if>

        union all
        SELECT
        GROUP_CONCAT(bym.ysbill_id SEPARATOR ',') ysbillId
        ,bymi.id expensesId
        ,yi.id yscostId
        ,ym.business_code relateCode
        ,1 costStatus
        ,bymi.charge_type chargeType
        ,bymi.expenses_code expensesCode
        ,DATE_FORMAT(IFNULL(ym.signing_date,bymi.business_time),'%Y-%m-%d %H:%i:%s') signingDates
        ,bymi.institution_id companyId
        ,mc.id as clientId
        ,mc.client_name clientName
        ,mc.client_code clientCode
        ,bymi.rule_name ruleName
        ,IFNULL(ym.store_name,ys.receiving_store)  receivingStore
        ,IFNULL(bymi.freight,0) freight
        ,IFNULL(bymi.delivery_fee,0) deliveryFee
        ,IFNULL(bymi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(bymi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(bymi.superframes_fee,0) superframesFee
        ,IFNULL(bymi.excess_fee,0) excessFee
        ,IFNULL(bymi.reduce_fee,0) reduceFee
        ,IFNULL(bymi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(bymi.exception_fee,0) exceptionFee
        ,IFNULL(bymi.return_fee,0) returnFee
        ,IFNULL(bymi.adjust_fee,0) adjustFee
        ,IFNULL(bymi.other_cost1,0) otherCost1
        ,IFNULL(bymi.other_cost2,0) otherCost2
        ,IFNULL(bymi.other_cost3,0) otherCost3
        ,IFNULL(bymi.other_cost4,0) otherCost4
        ,IFNULL(bymi.other_cost5,0) otherCost5
        ,IFNULL(bymi.other_cost6,0) otherCost6
        ,IFNULL(bymi.other_cost7,0) otherCost7
        ,IFNULL(bymi.other_cost8,0) otherCost8
        ,IFNULL(bymi.other_cost9,0) otherCost9
        ,IFNULL(bymi.other_cost10,0) otherCost10
        ,IFNULL(bymi.other_cost11,0) otherCost11
        ,IFNULL(bymi.other_cost12,0) otherCost12
        ,IFNULL(bymi.total_boxes,0) totalBoxes
        ,IFNULL(bymi.total_number,0) totalNumber
        ,IFNULL(ym.sku_number,0) skuNumber
        ,IFNULL(bymi.total_weight,0) totalWeight
        ,IFNULL(bymi.total_volume,0) totalVolume
        ,IFNULL(ym.cargo_value,0) cargoValue
        ,yi.fee_flag feeFlag
        ,case when bymi.is_increment =1 then yi.other_fee_remark
        else bymi.remarks end as  remarks
        ,ym.automatic_billing_remark automaticBillingRemark
        ,yi.adjust_remark adjustRemark
        ,bymi.bill_date billDate
        ,bymi.oper_by expensesBy
        ,DATE_FORMAT(bymi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when bymi.bill_id is not null then 1 else 0 end billingStatus
        ,IFNULL(ybm2.bill_code,ybm.bill_code) billCode
        ,bymi.del_flag delFlag
        ,ym.order_date orderDate
        ,DATE_FORMAT(ym.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,IFNULL(ym.signing_date,bymi.business_time) signingDate
        ,case when bymi.is_increment =1 then bymi.fee_create_ate
        else ys.oper_time end as  operTime
        ,bymi.expenses_type codeType
        ,ym.warehouse_code warehouseCode
        ,ys.create_time createTime
        ,bymi.cost_dimension costDimension
        ,ys.near_store_km nearStoreKm
        ,ys.car_type carType
        ,ys.car_model carModel
        ,ys.cw_full_cases cwFullCases
        ,ys.cw_split_cases cwSplitCases
        ,ys.ld_full_cases ldFullCases
        ,ys.ld_split_cases ldSplitCases
        ,ys.lc_full_cases lcFullCases
        ,ys.lc_split_cases lcSplitCases
        ,ys.province_origin provinceOrigin
        ,ys.originating_city originatingCity
        ,ys.originating_area originatingArea
        ,ys.destination_province destinationProvince
        ,ys.destination_city destinationCity
        ,ys.destination_area destinationArea
        ,ys.delivery_mode deliveryMode
        ,ys.delivery_code deliveryCode
        ,(IFNULL(bymi.freight,
        0) +IFNULL(bymi.delivery_fee,
        0) +IFNULL(bymi.outboundsorting_fee,
        0) +IFNULL(bymi.shortbarge_fee,
        0) +IFNULL(bymi.superframes_fee,
        0) +IFNULL(bymi.excess_fee,
        0) +IFNULL(bymi.ultrafar_fee,
        0) +IFNULL(bymi.exception_fee,
        0) +IFNULL(bymi.return_fee,
        0) +IFNULL(bymi.reduce_fee,
        0)) baseCostTotal
        ,(IFNULL(bymi.other_cost1,0)
        +IFNULL(bymi.other_cost2,0)
        +IFNULL(bymi.other_cost3,0)
        +IFNULL(bymi.other_cost4,0)
        +IFNULL(bymi.other_cost5,0)
        +IFNULL(bymi.other_cost6,0)
        +IFNULL(bymi.other_cost7,0)
        +IFNULL(bymi.other_cost8,0)
        +IFNULL(bymi.other_cost9,0)
        +IFNULL(bymi.other_cost10,0)
        +IFNULL(bymi.other_cost11,0)
        +IFNULL(bymi.other_cost12,0)  ) otherCostTotal
        ,(IFNULL(bymi.freight,
        0) +IFNULL(bymi.delivery_fee,
        0) +IFNULL(bymi.outboundsorting_fee,
        0) +IFNULL(bymi.shortbarge_fee,
        0) +IFNULL(bymi.superframes_fee,
        0) +IFNULL(bymi.excess_fee,
        0) +IFNULL(bymi.ultrafar_fee,
        0) +IFNULL(bymi.exception_fee,
        0) +IFNULL(bymi.return_fee,
        0) +IFNULL(bymi.reduce_fee,
        0))+
        (IFNULL(bymi.other_cost1,0)
        +IFNULL(bymi.other_cost2,0)
        +IFNULL(bymi.other_cost3,0)
        +IFNULL(bymi.other_cost4,0)
        +IFNULL(bymi.other_cost5,0)
        +IFNULL(bymi.other_cost6,0)
        +IFNULL(bymi.other_cost7,0)
        +IFNULL(bymi.other_cost8,0)
        +IFNULL(bymi.other_cost9,0)
        +IFNULL(bymi.other_cost10,0)
        +IFNULL(bymi.other_cost11,0)
        +IFNULL(bymi.other_cost12,0)  ) as totalFee
        ,bymi.client_id costClientId
        ,DATE_FORMAT(ys.create_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,bymi.bill_id billId
        ,ybm.bill_type billType
        ,ys.transport_type transportTypeApi
        ,IFNULL(yi.is_increment,0) isIncrement -- 是否为增值费0否1是
        ,bymi.fee_type_first feeTypeFirst -- 计费大类
        ,ym.total_quantity totalQuantity -- 附加费总数量
        ,ym.price price -- 附加费单价
        ,ys.store_code as storeCode
        ,ys.receiving_store as storeName
        ,ys.transport_type as transportType
        ,ys.order_type as orderType
        ,ys.cw_pallet_number as cwPalletNumber
        ,ys.lc_pallet_number as lcPalletNumber
        ,ys.ld_pallet_number as ldPalletNumber
        ,ys.order_no as orderNo
        ,ys.platform_code as platformCode
        ,ys.import_time as importTime
        ,ys.import_code as importCode
        ,IFNULL(bymi.create_by,bymi.oper_by)  as importBy
        ,ys.order_source as orderSource
        ,ys.is_timeout as isTimeOut
        ,CASE ys.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        ,null as failRemark
        ,ifnull(bymi.show_bill_code,IFNULL(ybm2.bill_code,ybm.bill_code)) as show_bill_code
        ,ifnull(bymi.show_bill_id,bymi.bill_id) as show_bill_id,
        IF(yi.extra_Field1 IS NULL, SUM(ys.total_boxes), yi.extra_Field1) AS extraField1,
         1 as ysbilType,
        bymi.expenses_code AS mainExpenseCode,
        yi.main_expense_id AS mainExpenseId
        from bms_yscost_main_info bymi
        left join bms_yscost_info yi ON yi.main_expense_id = bymi.id AND bymi.del_flag = '0'
        left join bms_yscost_extend ym ON ym.main_expense_id = bymi.id AND ym.del_flag = 0
        left join bms_ysexpenses_middle bym ON bym.main_expense_id = bymi.id AND bym.ysbil_type = 1
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_ysbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.id=yi.client_id
        left join bms_ysbillcodeinfo ys on ys.id=bym.ysbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="clientId != null and clientId.size>0 ">
            AND bymi.client_id in
            <foreach collection="clientId" item="clientId" open="(" separator="," close=")">
                #{clientId}
            </foreach>
        </if>
        <if test="importBy != null and importBy != ''">
            and IFNULL(bymi.create_by,bymi.oper_by) like concat('%',trim(#{importBy}),'%')
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="or" close=")">
                bymi.warehouse_code_arr like concat('%',trim(#{warehouseCode}),'%')
            </foreach>
            or bymi.is_increment=1
            )
        </if>
        <if test="warehouseName !=null and warehouseName !=''">
            and ym.warehouse_name like concat('%', #{warehouseName}, '%')
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND bymi.expenses_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="isIncrement != null">
            AND IFNULL(bymi.is_increment,0) = #{isIncrement}
        </if>

        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND bymi.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND bymi.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND bymi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND bymi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND bymi.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND bymi.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND bymi.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND bymi.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND bymi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND bymi.charge_type = #{chargeType}
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND bymi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND bymi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND bymi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND IFNULL(ym.store_name,ys.receiving_store) like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND bymi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND bymi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND bymi.cost_dimension = #{costDimension}
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND ys.delivery_mode = #{deliveryMode}
        </if>
        group by bymi.id
        ) a
        order by createTime desc,relateCode desc
    </select>
    <select id="selectOrderBilled" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        SELECT
        bym.ysbill_id ysbillId
        ,yi.id expensesId
        ,ym.business_code relateCode
        ,1 costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,DATE_FORMAT(IFNULL(ym.signing_date,yi.business_time),'%Y-%m-%d %H:%i:%s') signingDates
        ,yi.institution_id companyId
        ,mc.client_name clientName
        ,mc.client_code clientCode
        ,yi.rule_name ruleName
        ,IFNULL(ym.store_name,ys.receiving_store) receivingStore
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.reduce_fee,0) reduceFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.adjust_fee,0) adjustFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,IFNULL(ym.total_boxes,0) totalBoxes
        ,IFNULL(ym.total_number,0) totalNumber
        ,IFNULL(ym.sku_number,0) skuNumber
        ,IFNULL(ym.total_weight,0) totalWeight
        ,IFNULL(ym.total_volume,0) totalVolume
        ,IFNULL(ym.cargo_value,0) cargoValue
        ,yi.fee_flag feeFlag
        ,case when yi.is_increment =1 then yi.other_fee_remark
        else yi.remarks end as remarks
        ,ym.automatic_billing_remark automaticBillingRemark
        ,yi.adjust_remark adjustRemark
        ,yi.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,IFNULL(ybm2.bill_code,ybm.bill_code) billCode
        ,yi.del_flag delFlag
        ,ym.order_date orderDate
        ,DATE_FORMAT(ym.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,IFNULL(ym.signing_date,yi.business_time) signingDate
        ,case when yi.is_increment =1 then yi.fee_create_ate
        else yi.oper_time end as operTime
        ,yi.expenses_type codeType
        ,ym.warehouse_code warehouseCode
        ,ys.create_time createTime
        ,yi.cost_dimension costDimension
        ,ys.near_store_km nearStoreKm
        ,ys.car_type carType
        ,ys.car_model carModel
        ,ys.cw_full_cases cwFullCases
        ,ys.cw_split_cases cwSplitCases
        ,ys.ld_full_cases ldFullCases
        ,ys.ld_split_cases ldSplitCases
        ,ys.lc_full_cases lcFullCases
        ,ys.lc_split_cases lcSplitCases
        ,ys.province_origin provinceOrigin
        ,ys.originating_city originatingCity
        ,ys.originating_area originatingArea
        ,ys.destination_province destinationProvince
        ,ys.destination_city destinationCity
        ,ys.destination_area destinationArea
        ,ys.delivery_mode deliveryMode
        ,ys.delivery_code deliveryCode
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.reduce_fee,0)) baseCostTotal
        ,(IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0) ) otherCostTotal
        ,(IFNULL(yi.freight,
        0) +IFNULL(yi.delivery_fee,
        0) +IFNULL(yi.outboundsorting_fee,
        0) +IFNULL(yi.shortbarge_fee,
        0) +IFNULL(yi.superframes_fee,
        0) +IFNULL(yi.excess_fee,
        0) +IFNULL(yi.ultrafar_fee,
        0) +IFNULL(yi.exception_fee,
        0) +IFNULL(yi.return_fee,
        0) +IFNULL(yi.reduce_fee,
        0))+
        (IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0) ) as totalFee
        ,yi.client_id costClientId
        ,DATE_FORMAT(ys.create_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,yi.bill_id billId
        ,ybm.bill_type billType
        ,ys.transport_type transportTypeApi
        ,IFNULL(yi.is_increment,0) isIncrement -- 是否为增值费0否1是
        ,yi.fee_type_first feeTypeFirst -- 计费大类
        ,ym.total_quantity totalQuantity -- 附加费总数量
        ,ym.price price -- 附加费单价
        ,ys.store_code as storeCode
        ,ys.receiving_store as storeName
        ,ys.transport_type as transportType
        ,ys.order_type as orderType
        ,ys.cw_pallet_number as cwPalletNumber
        ,ys.lc_pallet_number as lcPalletNumber
        ,ys.ld_pallet_number as ldPalletNumber
        ,ys.order_no as orderNo
        ,ys.platform_code as platformCode
        ,ys.import_time as importTime
        ,ys.import_code as importCode
        ,CASE
        ys.cost_status
        WHEN 0 THEN IFNULL(ys.import_by,ys.oper_by)
        ELSE IFNULL(bymi.create_by,bymi.oper_by) END AS importBy
        ,ys.order_source as orderSource
        ,ys.is_timeout as isTimeOut
        ,CASE ys.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        ,null as failRemark
        ,ifnull(yi.show_bill_code,IFNULL(ybm2.bill_code,ybm.bill_code)) as show_bill_code
        ,ifnull(yi.show_bill_id,yi.bill_id) as show_bill_id
        ,bymi.expenses_code as mainExpenseCode
        ,bymi.id AS mainExpenseId
        ,yi.settle_type as settleType
        ,yi.settle_amount as settleAmount
        ,yi.settle_main_id as settleMainId
        ,mc2.client_name AS settleMainName
        from bms_yscost_main_info bymi
        left join bms_yscost_info yi on yi.main_expense_id = bymi.id and yi.del_flag = '0'
        left join bms_yscost_extend ym on ym.main_expense_id=yi.main_expense_id and ym.del_flag=0
        left join bms_ysexpenses_middle bym on bym.main_expense_id=yi.main_expense_id and bym.ysbil_type = 1
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_ysbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.id=yi.client_id
        left join bms_clientinfo mc2 on mc2.id=yi.settle_main_id
        left join bms_ysbillcodeinfo ys on ys.id=bym.ysbill_id and ym.code_count=1
        where bymi.del_flag=0
        <if test="clientId != null and clientId.size>0 ">
            AND yi.client_id in
            <foreach collection="clientId" item="clientId" open="(" separator="," close=")">
                #{clientId}
            </foreach>
        </if>
        <if test="importBy != null and importBy != ''">
            and (CASE
            ys.cost_status
            WHEN 0 THEN IFNULL(ys.import_by,ys.oper_by)
            ELSE IFNULL(bymi.create_by,bymi.oper_by) END ) like concat('%',trim(#{importBy}),'%')
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="or" close=")">
                ym.warehouse_code like concat('%',trim(#{warehouseCode}),'%')
            </foreach>
            or yi.is_increment=1
            )
        </if>
        <if test="warehouseName !=null and warehouseName !=''">
            and ym.warehouse_name like concat('%', #{warehouseName}, '%')
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="isIncrement != null">
            AND IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND yi.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND yi.signing_date &lt;=
            #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;=
            #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND yi.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND yi.order_date &lt;=
            #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND yi.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND yi.order_date &lt;=
            #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="clientCodeList != null and clientCodeList.size>0 ">
            AND mc.client_code in
            <foreach collection="clientCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND IFNULL(ym.store_name,ys.receiving_store) like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="mainExpenseCode != null and mainExpenseCode != ''">
            and bymi.expenses_code like concat('%',trim(#{mainExpenseCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND ys.delivery_mode = #{deliveryMode}
        </if>
        <if test="settleType !=null and settleType != ''">
            AND yi.settle_type = #{settleType}
        </if>
        order by yi.oper_time desc,yi.main_expense_id desc
    </select>

    <select id="selectOrderBill1AndCostStatusZERO" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        SELECT
        y.id ysbillId
        ,null expensesId
        ,y.relate_code relateCode
        ,y.cost_status costStatus
        ,null chargeType
        ,null expensesCode
        ,DATE_FORMAT(y.signing_date,'%Y-%m-%d %H:%i:%s') signingDates
        ,y.company_id companyId
        ,mc.client_name clientName
        ,y.client_code clientCode
        ,null ruleName
        ,y.receiving_store receivingStore
        ,0 freight
        ,0 deliveryFee
        ,0 outboundsortingFee
        ,0 shortbargeFee
        ,0 superframesFee
        ,0 excessFee
        ,0 reduceFee
        ,0 ultrafarFee
        ,0 exceptionFee
        ,0 returnFee
        ,0 adjustFee
        ,0 otherCost1
        ,0 otherCost2
        ,0 otherCost3
        ,0 otherCost4
        ,0 otherCost5
        ,0 otherCost6
        ,0 otherCost7
        ,0 otherCost8
        ,0 otherCost9
        ,0 otherCost10
        ,0 otherCost11
        ,0 otherCost12
        ,IFNULL(y.total_boxes,0) totalBoxes
        ,IFNULL(y.total_number,0) totalNumber
        ,IFNULL(y.sku_number,0) skuNumber
        ,IFNULL(y.total_weight,0) totalWeight
        ,IFNULL(y.total_volume,0) totalVolume
        ,IFNULL(y.cargo_value,0) cargoValue
        ,null feeFlag
        ,null remarks
        ,null automaticBillingRemark
        ,null adjustRemark
        ,null billDate
        ,null expensesBy
        ,null expensesTimes
        ,y.billing_status billingStatus
        ,null billCode
        ,y.del_flag delFlag
        ,y.order_date orderDate
        ,DATE_FORMAT(y.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,y.signing_date signingDate
        ,null operTime
        ,y.code_type codeType
        ,y.warehouse_code warehouseCode
        ,y.create_time createTime
        ,null costDimension
        ,y.near_store_km nearStoreKm
        ,y.car_type carType
        ,y.car_model carModel
        ,y.cw_full_cases cwFullCases
        ,y.cw_split_cases cwSplitCases
        ,y.ld_full_cases ldFullCases
        ,y.ld_split_cases ldSplitCases
        ,y.lc_full_cases lcFullCases
        ,y.lc_split_cases lcSplitCases
        ,y.province_origin provinceOrigin
        ,y.originating_city originatingCity
        ,y.originating_area originatingArea
        ,y.destination_province destinationProvince
        ,y.destination_city destinationCity
        ,y.destination_area destinationArea
        ,y.delivery_mode deliveryMode
        ,y.delivery_code deliveryCode
        ,0 baseCostTotal
        ,0 otherCostTotal
        ,0 totalFee
        ,null costClientId
        ,DATE_FORMAT(y.create_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,null billId
        ,null billType
        ,y.transport_type transportTypeApi
        ,0 isIncrement -- 是否为增值费0否1是
        ,null feeTypeFirst -- 计费大类
        ,null totalQuantity -- 附加费总数量
        ,null price -- 附加费单价
        ,y.store_code as storeCode
        ,y.receiving_store as storeName
        ,y.transport_type as transportType
        ,y.order_type as orderType
        ,y.cw_pallet_number as cwPalletNumber
        ,y.lc_pallet_number as lcPalletNumber
        ,y.ld_pallet_number as ldPalletNumber
        ,y.order_no as orderNo
        ,y.import_time as importTime
        ,y.import_code as importCode
        ,y.import_by as importBy
        ,y.order_source as orderSource
        ,CASE y.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        FROM bms_ysbillcodeinfo y
        left join bms_clientinfo mc on mc.client_code=y.client_code and mc.del_flag = 0
        where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null">
            AND 0 = #{isIncrement}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND y.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND y.receiving_store like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND y.delivery_mode = #{deliveryMode}
        </if>
        order by y.create_time desc,y.relate_code desc
    </select>


    <select id="selectOrderBill1AndCostStatusONE" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">

        SELECT
        GROUP_CONCAT(bym.ysbill_id SEPARATOR ',') ysbillId
        ,yi.id expensesId
        ,ym.business_code relateCode
        ,1 costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,DATE_FORMAT(IFNULL(ym.signing_date,yi.business_time),'%Y-%m-%d %H:%i:%s') signingDates
        ,yi.institution_id companyId
        ,mc.client_name clientName
        ,mc.client_code clientCode
        ,yi.rule_name ruleName
        ,IFNULL(ym.store_name,ys.receiving_store)  receivingStore
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.reduce_fee,0) reduceFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.adjust_fee,0) adjustFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,IFNULL(ym.total_boxes,0) totalBoxes
        ,IFNULL(ym.total_number,0) totalNumber
        ,IFNULL(ym.sku_number,0) skuNumber
        ,IFNULL(ym.total_weight,0) totalWeight
        ,IFNULL(ym.total_volume,0) totalVolume
        ,IFNULL(ym.cargo_value,0) cargoValue
        ,yi.fee_flag feeFlag
        ,case when yi.is_increment =1 then yi.other_fee_remark
        else yi.remarks end as  remarks
        ,ym.automatic_billing_remark automaticBillingRemark
        ,yi.adjust_remark adjustRemark
        ,yi.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,IFNULL(ybm2.bill_code,ybm.bill_code) billCode
        ,yi.del_flag delFlag
        ,ym.order_date orderDate
        ,DATE_FORMAT(ym.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,IFNULL(ym.signing_date,yi.business_time) signingDate
        ,case when yi.is_increment =1 then yi.fee_create_ate
        else yi.oper_time end as  operTime
        ,yi.expenses_type codeType
        ,ym.warehouse_code warehouseCode
        ,ys.create_time createTime
        ,yi.cost_dimension costDimension
        ,ys.near_store_km nearStoreKm
        ,ys.car_type carType
        ,ys.car_model carModel
        ,ys.cw_full_cases cwFullCases
        ,ys.cw_split_cases cwSplitCases
        ,ys.ld_full_cases ldFullCases
        ,ys.ld_split_cases ldSplitCases
        ,ys.lc_full_cases lcFullCases
        ,ys.lc_split_cases lcSplitCases
        ,ys.province_origin provinceOrigin
        ,ys.originating_city originatingCity
        ,ys.originating_area originatingArea
        ,ys.destination_province destinationProvince
        ,ys.destination_city destinationCity
        ,ys.destination_area destinationArea
        ,ys.delivery_mode deliveryMode
        ,ys.delivery_code deliveryCode
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.adjust_fee,0)
        +IFNULL(yi.reduce_fee,0)) baseCostTotal
        ,(IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  ) otherCostTotal
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.adjust_fee,0)
        +IFNULL(yi.reduce_fee,0))+
        (IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  ) as totalFee
        ,yi.client_id costClientId
        ,DATE_FORMAT(ys.create_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,yi.bill_id billId
        ,ybm.bill_type billType
        ,ys.transport_type transportTypeApi
        ,IFNULL(yi.is_increment,0) isIncrement -- 是否为增值费0否1是
        ,yi.fee_type_first feeTypeFirst -- 计费大类
        ,ym.total_quantity totalQuantity -- 附加费总数量
        ,ym.price price -- 附加费单价
        ,ys.store_code as storeCode
        ,ys.receiving_store as storeName
        ,ys.transport_type as transportType
        ,ys.order_type as orderType
        ,ys.cw_pallet_number as cwPalletNumber
        ,ys.lc_pallet_number as lcPalletNumber
        ,ys.ld_pallet_number as ldPalletNumber
        ,ys.order_no as orderNo
        ,ys.import_time as importTime
        ,ys.import_code as importCode
        ,ys.import_by as importBy
        ,ys.order_source as orderSource
        ,CASE ys.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        from bms_yscost_info yi
        left join bms_yscost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_ysexpenses_middle bym on bym.expenses_id=yi.id and bym.ysbil_type = 1
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_ysbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.id=yi.client_id
        left join bms_ysbillcodeinfo ys on ys.id=bym.ysbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="clientCode != null and clientCode.size>0 ">
            AND mc.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="or" close=")">
                ym.warehouse_code like concat('%',trim(#{warehouseCode}),'%')
            </foreach>
            or yi.is_increment=1
            )
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="isIncrement != null">
            AND IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>

        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND IFNULL(ym.signing_date,yi.business_time) &gt;= #{signingDateStart,jdbcType=VARCHAR} AND IFNULL(ym.signing_date,yi.business_time) &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND ym.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND ym.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND ym.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND ym.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND IFNULL(ym.store_name,ys.receiving_store) like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND ys.delivery_mode = #{deliveryMode}
        </if>
        group by yi.id
        order by ys.create_time desc,ym.business_code desc
    </select>


    <select id="selectOrderBill2" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        select * from
        (SELECT
        y.id ysbillId
        ,null expensesId
        ,null yscostId
        ,y.relate_code relateCode
        ,IF(y.fail_remark is not null and y.fail_remark!='',3,y.cost_status) as costStatus
        ,null chargeType
        ,null expensesCode
        ,DATE_FORMAT(y.signing_date,'%Y-%m-%d %H:%i:%s') signingDates
        ,y.company_id companyId
        ,y.client_name clientName
        ,y.client_code clientCode
        ,mc.id as clientId
        ,null ruleName
        ,y.receiving_store receivingStore
        ,0 freight
        ,0 deliveryFee
        ,0 outboundsortingFee
        ,0 shortbargeFee
        ,0 superframesFee
        ,0 excessFee
        ,0 reduceFee
        ,0 ultrafarFee
        ,0 exceptionFee
        ,0 returnFee
        ,0 adjustFee
        ,0 otherCost1
        ,0 otherCost2
        ,0 otherCost3
        ,0 otherCost4
        ,0 otherCost5
        ,0 otherCost6
        ,0 otherCost7
        ,0 otherCost8
        ,0 otherCost9
        ,0 otherCost10
        ,0 otherCost11
        ,0 otherCost12
        ,IFNULL(y.total_boxes,0) totalBoxes
        ,IFNULL(y.total_number,0) totalNumber
        ,IFNULL(y.sku_number,0) skuNumber
        ,IFNULL(y.total_weight,0) totalWeight
        ,IFNULL(y.total_volume,0) totalVolume
        ,IFNULL(y.cargo_value,0) cargoValue
        ,null feeFlag
        ,null remarks
        ,null automaticBillingRemark
        ,null adjustRemark
        ,null billDate
        ,null expensesBy
        ,DATE_FORMAT(y.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,y.billing_status billingStatus
        ,null billCode
        ,y.del_flag delFlag
        ,y.order_date orderDate
        ,DATE_FORMAT(y.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,y.signing_date signingDate
        ,y.oper_time operTime
        ,y.code_type codeType
        ,y.warehouse_code warehouseCode
        ,y.create_time createTime
        ,null instorageTime
        ,null instorageTimes
        ,null skuCode
        ,null oddBoxes
        ,null boxType
        ,null warehouseArea
        ,null temperatureType
        ,null aqty
        ,y.pallet_number palletNumber
        ,null costDimension
        ,y.near_store_km nearStoreKm
        ,y.car_type carType
        ,y.car_model carModel
        ,y.cw_full_cases cwFullCases
        ,y.cw_split_cases cwSplitCases
        ,y.ld_full_cases ldFullCases
        ,y.ld_split_cases ldSplitCases
        ,y.lc_full_cases lcFullCases
        ,y.lc_split_cases lcSplitCases
        ,0 baseCostTotal
        ,0 otherCostTotal
        ,0 totalFee
        ,DATE_FORMAT(y.create_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,null billId
        ,null billType
        ,y.transport_type transportTypeApi

        ,0 isIncrement -- 是否为增值费0否1是
        ,null feeTypeFirst -- 计费大类
        ,null totalQuantity -- 附加费总数量
        ,null price -- 附加费单价
        ,y.store_code as storeCode
        ,y.receiving_store as storeName
        ,y.transport_type as transportType
        ,y.order_type as orderType
        ,y.cw_pallet_number as cwPalletNumber
        ,y.lc_pallet_number as lcPalletNumber
        ,y.ld_pallet_number as ldPalletNumber
        ,y.order_no as orderNo
        ,y.platform_code as platformCode
        ,y.import_time as importTime
        ,y.import_code as importCode
        ,CASE  order_source WHEN 1 THEN IFNULL( y.import_by, y.create_by ) ELSE null END importBy
        ,y.is_timeout as isTimeOut
        ,y.order_source as orderSource
        ,CASE y.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        ,y.destination_Province as destinationProvince
        ,y.destination_city as destinationCity
        ,y.destination_area as destinationArea
        ,y.fail_remark as failRemark
        ,null as showBillCode
        ,null as showBillId
        ,null as extraField1
        ,null as mainExpenseId
        ,null as mainExpenseCode
        FROM bms_ysbillcodeinfo y left join bms_clientinfo mc on y.client_code=mc.id
        where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null">
            AND 0 = #{isIncrement}
        </if>
        <if test="importBy != null and importBy != ''">
            and (CASE  order_source WHEN 1 THEN IFNULL( y.import_by, y.create_by ) ELSE null END)  like concat('%',trim(#{importBy}),'%')
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND y.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.create_time &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.create_time &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != '' and costStatus!=3 ">
            AND y.cost_status = #{costStatus} AND y.fail_remark is null
        </if>
        <if test="costStatus != null and costStatus == 3 ">
            AND y.fail_remark is not null
        </if>
        <if test="clientName != null and clientName != ''">
            AND y.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND y.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND y.receiving_store like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or
        (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or
        (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(bym.ysbill_id SEPARATOR ',') ysbillId
        ,yi.id expensesId
        ,yi.id yscostId
        ,ym.business_code relateCode
        ,1 costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,DATE_FORMAT(IFNULL(ym.signing_date,yi.business_time),'%Y-%m-%d %H:%i:%s') signingDates
        ,yi.institution_id companyId
        ,mc.client_name clientName
        ,mc.client_code clientCode
        ,mc.id as clientId
        ,yi.rule_name ruleName
        ,null receivingStore
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.reduce_fee,0) reduceFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.adjust_fee,0) adjustFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,IFNULL(ym.total_boxes,0) totalBoxes
        ,IFNULL(ym.total_number,0) totalNumber
        ,IFNULL(ym.sku_number,0) skuNumber
        ,IFNULL(ym.total_weight,0) totalWeight
        ,IFNULL(ym.total_volume,0) totalVolume
        ,IFNULL(ym.cargo_value,0) cargoValue
        ,yi.fee_flag feeFlag
        ,case when yi.is_increment=1 then yi.other_fee_remark
        else yi.remarks end as  remarks
        ,ym.automatic_billing_remark automaticBillingRemark
        ,yi.adjust_remark adjustRemark
        ,yi.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,IFNULL(ybm2.bill_code,ybm.bill_code) billCode
        ,yi.del_flag delFlag
        ,byc.order_date orderDate
        ,DATE_FORMAT(byc.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,IFNULL(ym.signing_date,yi.business_time) signingDate
        ,case when yi.is_increment=1 then yi.fee_create_ate
        else yi.oper_time end as  operTime
        ,yi.expenses_type codeType
        ,ym.warehouse_code warehouseCode
        ,DATE_FORMAT(IFNULL(stk.oper_time,byc.create_time),'%Y-%m-%d %H:%i:%s') createTime
        ,null instorageTime
        ,null instorageTimes
        ,null skuCode
        ,null oddBoxes
        ,null boxType
        ,null warehouseArea
        ,stk.temperature_type as  temperatureType
        ,stk.aqty aqty
        ,IFNULL(stk.pallet_number,byc.pallet_number) palletNumber
        ,yi.cost_dimension costDimension
        ,null nearStoreKm
        ,null carType
        ,null carModel
        ,byc.cw_full_cases cwFullCases
        ,byc.cw_split_cases cwSplitCases
        ,byc.ld_full_cases ldFullCases
        ,byc.ld_split_cases ldSplitCases
        ,byc.lc_full_cases lcFullCases
        ,byc.lc_split_cases lcSplitCases
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.reduce_fee,0)) baseCostTotal
        ,(IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  ) otherCostTotal
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.adjust_fee,0)
        +IFNULL(yi.reduce_fee,0))+
        (IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  )  as totalFee
        ,DATE_FORMAT(IFNULL(stk.oper_time,byc.create_time),'%Y-%m-%d %H:%i:%s') createTimes
        ,yi.bill_id billId
        ,ybm.bill_type billType
        ,byc.transport_type transportTypeApi
        ,IFNULL(yi.is_increment,0) isIncrement -- 是否为增值费0否1是
        ,yi.fee_type_first feeTypeFirst -- 计费大类
        ,ym.total_quantity totalQuantity -- 附加费总数量
        ,ym.price price -- 附加费单价
        ,byc.store_code as storeCode
        ,byc.receiving_store as storeName
        ,byc.transport_type as transportType
        ,byc.order_type as orderType
        ,byc.cw_pallet_number as cwPalletNumber
        ,byc.lc_pallet_number as lcPalletNumber
        ,byc.ld_pallet_number as ldPalletNumber
        ,byc.order_no as orderNo
        ,byc.platform_code as platformCode
        ,ifnull( byc.import_time,stk.import_time) as importTime ,
        ifnull(byc.import_code,stk.import_code) as importCode ,
        ifnull( bymi.oper_by, byc.import_by ) AS importBy ,
        byc.is_timeout as isTimeOut,
        ifnull(byc.order_source,stk.order_source) as orderSource ,
        case ifnull(byc.order_source,stk.order_source )
        WHEN 0 THEN '对接产生'
        WHEN 1 THEN '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        ,byc.destination_Province as destinationProvince
        ,byc.destination_city as destinationCity
        ,byc.destination_area as destinationArea
        ,null as failRemark
        ,ifnull(yi.show_bill_code,ybm.bill_code) as showBillCode
        ,ifnull(yi.show_bill_id,IFNULL(ybm2.id,ybm.id)) as showBillId
        ,yi.extra_field1 as extraField1
        ,bymi.id as mainExpenseId
        ,bymi.expenses_code as mainExpenseCode
        from bms_yscost_main_info bymi
        left join bms_yscost_info yi on yi.main_expense_id=bymi.id and yi.del_flag=0
        left join bms_yscost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_ysexpenses_middle bym on bym.expenses_id=yi.id and bym.del_flag=0
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_ysbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.id=yi.client_id and mc.del_flag=0
        left join bms_ysstockinfo stk on bym.ysbill_id = stk.id and IFNULL(ym.code_count,1)=1
        left join bms_ysbillcodeinfo byc on byc.id=bym.ysbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="isIncrement != null">
            AND IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>

        <if test="importBy != null and importBy != ''">
            and ifnull( bymi.oper_by, byc.import_by ) like concat('%',trim(#{importBy}),'%')
        </if>
        <if test="clientId != null and clientId.size>0 ">
            AND yi.client_id in
            <foreach collection="clientId" item="clientId" open="(" separator="," close=")">
                #{clientId}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="or" close=")">
                yi.warehouse_code_arr like concat('%',trim(#{warehouseCode}),'%')
            </foreach>
            or yi.is_increment=1
            )
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND yi.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND yi.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND stk.oper_time &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND stk.oper_time &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND yi.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND yi.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND 1=2
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        group by bymi.id
        union
        SELECT
        y.id ysbillId
        ,yi.id expensesId
        ,yi.id yscostId
        ,y.stock_code relateCode
        ,IF(y.fail_remark is not null and y.fail_remark!='',3,y.cost_status) as costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,DATE_FORMAT(y.instorage_time,'%Y-%m-%d %H:%i:%s') signingDates
        ,y.company_id companyId
        ,mc.client_name clientName
        ,y.client_code clientCode
        ,mc.id as clientId
        ,yi.rule_name ruleName
        ,null receivingStore
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.reduce_fee,0) reduceFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.adjust_fee,0) adjustFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,IFNULL(y.total_boxes,0) totalBoxes
        ,IFNULL(y.trust,0) totalNumber
        ,IFNULL(y.trust,0) skuNumber
        ,IFNULL(y.weight,0) totalWeight
        ,IFNULL(y.volume,0) totalVolume
        ,null cargoValue
        ,yi.fee_flag feeFlag
        ,case when yi.is_increment=1 then yi.other_fee_remark
        else yi.remarks end as  remarks
        ,ye.automatic_billing_remark automaticBillingRemark
        ,yi.adjust_remark adjustRemark
        ,yi.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,ybm.bill_code billCode
        ,y.del_flag delFlag
        ,null orderDate
        ,null orderDates
        ,y.instorage_time signingDate
        ,case when yi.is_increment=1 then yi.fee_create_ate
        else yi.oper_time end as operTime
        ,4 codeType
        ,y.warehouse_code warehouseCode
        ,y.oper_time createTime
        ,y.instorage_time instorageTime
        ,DATE_FORMAT(y.instorage_time,'%Y-%m-%d %H:%i:%s') instorageTimes
        ,y.sku_code skuCode
        ,y.odd_boxes oddBoxes
        ,y.box_type boxType
        ,y.warehouse_area warehouseArea
        ,y.temperature_type temperatureType
        ,y.aqty
        ,y.pallet_number palletNumber
        ,yi.cost_dimension costDimension
        ,null nearStoreKm
        ,null carType
        ,null carModel
        ,null cwFullCases
        ,null cwSplitCases
        ,null ldFullCases
        ,null ldSplitCases
        ,null lcFullCases
        ,null lcSplitCases
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.reduce_fee,0)) baseCostTotal
        ,(IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  ) otherCostTotal
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.adjust_fee,0)
        +IFNULL(yi.reduce_fee,0)) +
        (IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  ) as totalFee
        ,DATE_FORMAT(y.oper_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,null billId
        ,null billType
        ,null transportTypeApi
        ,IFNULL(yi.is_increment,0) isIncrement -- 是否为增值费0否1是
        ,yi.fee_type_first feeTypeFirst -- 计费大类
        ,ye.total_quantity totalQuantity -- 附加费总数量
        ,ye.price price -- 附加费单价
        ,null as storeCode
        ,null as storeName
        ,null as transportType
        ,null as orderType
        ,null as cwPalletNumber
        ,null as lcPalletNumber
        ,null as ldPalletNumber
        ,null as orderNo
        ,null as platformCode
        ,y.import_time as importTime
        ,y.import_code as importCode
        ,CASE  y.order_source WHEN 1 THEN IFNULL( y.import_by, y.oper_by ) ELSE null END importBy
        ,null as isTimeout
        ,y.order_source as orderSource
        ,CASE y.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        ,null as destinationProvince
        ,null as destinationCity
        ,null as destinationArea
        ,y.fail_remark as failRemark
        ,yi.show_bill_code as showBillCode
        ,yi.show_bill_id as showBillId
        ,yi.extra_field1 as extraField1
        ,null as mainExpenseId
        ,null as mainExpenseCode
        FROM bms_ysstockinfo y
        left join bms_ysexpenses_middle ym on y.id=ym.ysbill_id and ym.del_flag=0
        left join bms_yscost_info yi on yi.main_expense_id=ym.main_expense_id and yi.del_flag=0
        left join bms_yscost_extend ye on yi.main_expense_id=ye.main_expense_id and ye.del_flag=0
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.client_code=y.client_code and mc.del_flag = 0
        where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null ">
            AND IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>
        <if test="importBy != null and importBy != ''">
            and (CASE  y.order_source WHEN 1 THEN IFNULL( y.import_by, y.oper_by ) ELSE null END) like concat('%',trim(#{importBy}),'%')
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND 4 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.instorage_time &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.instorage_time &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.oper_time &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.oper_time &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != '' and costStatus != 3">
            AND y.cost_status = #{costStatus} AND y.fail_remark is null
        </if>
        <if test="costStatus != null and costStatus == 3">
            AND y.fail_remark is not null
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND y.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND y.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND 1=2
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.stock_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND 1=2
        </if>
        ) a
        order by createTime desc,relateCode desc
    </select>

    <select id="selectOrderBill2AndCostStatusZERO" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        select * from
        (SELECT
        y.id ysbillId
        ,null expensesId
        ,y.relate_code relateCode
        ,y.cost_status costStatus
        ,null chargeType
        ,null expensesCode
        ,DATE_FORMAT(y.signing_date,'%Y-%m-%d %H:%i:%s') signingDates
        ,y.company_id companyId
        ,mc.client_name clientName
        ,y.client_code clientCode
        ,null ruleName
        ,y.receiving_store receivingStore
        ,0 freight
        ,0 deliveryFee
        ,0 outboundsortingFee
        ,0 shortbargeFee
        ,0 superframesFee
        ,0 excessFee
        ,0 reduceFee
        ,0 ultrafarFee
        ,0 exceptionFee
        ,0 returnFee
        ,0 adjustFee
        ,0 otherCost1
        ,0 otherCost2
        ,0 otherCost3
        ,0 otherCost4
        ,0 otherCost5
        ,0 otherCost6
        ,0 otherCost7
        ,0 otherCost8
        ,0 otherCost9
        ,0 otherCost10
        ,0 otherCost11
        ,0 otherCost12
        ,IFNULL(y.total_boxes,0) totalBoxes
        ,IFNULL(y.total_number,0) totalNumber
        ,IFNULL(y.sku_number,0) skuNumber
        ,IFNULL(y.total_weight,0) totalWeight
        ,IFNULL(y.total_volume,0) totalVolume
        ,IFNULL(y.cargo_value,0) cargoValue
        ,null feeFlag
        ,null remarks
        ,null automaticBillingRemark
        ,null adjustRemark
        ,null billDate
        ,null expensesBy
        ,null expensesTimes
        ,y.billing_status billingStatus
        ,null billCode
        ,y.del_flag delFlag
        ,y.order_date orderDate
        ,DATE_FORMAT(y.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,y.signing_date signingDate
        ,null operTime
        ,y.code_type codeType
        ,y.warehouse_code warehouseCode
        ,y.create_time createTime
        ,null instorageTime
        ,null instorageTimes
        ,null skuCode
        ,null oddBoxes
        ,null boxType
        ,null warehouseArea
        ,null temperatureType
        ,null aqty
        ,y.pallet_number palletNumber
        ,null costDimension
        ,y.near_store_km nearStoreKm
        ,y.car_type carType
        ,y.car_model carModel
        ,y.cw_full_cases cwFullCases
        ,y.cw_split_cases cwSplitCases
        ,y.ld_full_cases ldFullCases
        ,y.ld_split_cases ldSplitCases
        ,y.lc_full_cases lcFullCases
        ,y.lc_split_cases lcSplitCases
        ,0 baseCostTotal
        ,0 otherCostTotal
        ,0 totalFee
        ,DATE_FORMAT(y.create_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,null billId
        ,null billType
        ,y.transport_type transportTypeApi

        ,0 isIncrement -- 是否为增值费0否1是
        ,null feeTypeFirst -- 计费大类
        ,null totalQuantity -- 附加费总数量
        ,null price -- 附加费单价
        ,y.store_code as storeCode
        ,y.receiving_store as storeName
        ,y.transport_type as transportType
        ,y.order_type as orderType
        ,y.cw_pallet_number as cwPalletNumber
        ,y.lc_pallet_number as lcPalletNumber
        ,y.ld_pallet_number as ldPalletNumber
        ,y.order_no as orderNo
        ,y.import_time as importTime
        ,y.import_code as importCode
        ,y.import_by as importBy
        ,y.order_source as orderSource
        ,CASE y.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        FROM bms_ysbillcodeinfo y
        left join bms_clientinfo mc on mc.client_code=y.client_code and mc.del_flag = 0
        where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null">
            AND 0 = #{isIncrement}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND y.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.create_time &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.create_time &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND y.receiving_store like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or
        (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or
        (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(bym.ysbill_id SEPARATOR ',') ysbillId
        ,yi.id expensesId
        ,ym.business_code relateCode
        ,1 costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,DATE_FORMAT(IFNULL(ym.signing_date,yi.business_time),'%Y-%m-%d %H:%i:%s') signingDates
        ,yi.institution_id companyId
        ,mc.client_name clientName
        ,mc.client_code clientCode
        ,yi.rule_name ruleName
        ,null receivingStore
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.reduce_fee,0) reduceFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.adjust_fee,0) adjustFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,IFNULL(ym.total_boxes,0) totalBoxes
        ,IFNULL(ym.total_number,0) totalNumber
        ,IFNULL(ym.sku_number,0) skuNumber
        ,IFNULL(ym.total_weight,0) totalWeight
        ,IFNULL(ym.total_volume,0) totalVolume
        ,IFNULL(ym.cargo_value,0) cargoValue
        ,yi.fee_flag feeFlag
        ,case when yi.is_increment=1 then yi.other_fee_remark
        else yi.remarks end as  remarks
        ,ym.automatic_billing_remark automaticBillingRemark
        ,yi.adjust_remark adjustRemark
        ,yi.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,IFNULL(ybm2.bill_code,ybm.bill_code) billCode
        ,yi.del_flag delFlag
        ,byc.order_date orderDate
        ,DATE_FORMAT(byc.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,IFNULL(ym.signing_date,yi.business_time) signingDate
        ,case when yi.is_increment=1 then yi.fee_create_ate
        else yi.oper_time end as  operTime
        ,yi.expenses_type codeType
        ,ym.warehouse_code warehouseCode
        ,stk.oper_time createTime
        ,null instorageTime
        ,null instorageTimes
        ,null skuCode
        ,null oddBoxes
        ,null boxType
        ,null warehouseArea
        ,stk.temperature_type as  temperatureType
        ,stk.aqty aqty
        ,IFNULL(stk.pallet_number,byc.pallet_number) palletNumber
        ,yi.cost_dimension costDimension
        ,null nearStoreKm
        ,null carType
        ,null carModel
        ,byc.cw_full_cases cwFullCases
        ,byc.cw_split_cases cwSplitCases
        ,byc.ld_full_cases ldFullCases
        ,byc.ld_split_cases ldSplitCases
        ,byc.lc_full_cases lcFullCases
        ,byc.lc_split_cases lcSplitCases
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.adjust_fee,0)
        +IFNULL(yi.reduce_fee,0)) baseCostTotal
        ,(IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  ) otherCostTotal
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.adjust_fee,0)
        +IFNULL(yi.reduce_fee,0))+
        (IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  )  as totalFee
        ,DATE_FORMAT(IFNULL(stk.oper_time,byc.create_time),'%Y-%m-%d %H:%i:%s') createTimes
        ,yi.bill_id billId
        ,ybm.bill_type billType
        ,byc.transport_type transportTypeApi

        ,IFNULL(yi.is_increment,0) isIncrement -- 是否为增值费0否1是
        ,yi.fee_type_first feeTypeFirst -- 计费大类
        ,ym.total_quantity totalQuantity -- 附加费总数量
        ,ym.price price -- 附加费单价
        ,byc.store_code as storeCode
        ,byc.receiving_store as storeName
        ,byc.transport_type as transportType
        ,byc.order_type as orderType
        ,byc.cw_pallet_number as cwPalletNumber
        ,byc.lc_pallet_number as lcPalletNumber
        ,byc.ld_pallet_number as ldPalletNumber
        ,byc.order_no as orderNo
        ,byc.import_time as importTime
        ,byc.import_code as importCode
        ,byc.import_by as importBy
        ,byc.order_source as orderSource
        ,CASE byc.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        from bms_yscost_info yi
        left join bms_yscost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_ysexpenses_middle bym on bym.expenses_id=yi.id
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_ysbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.id=yi.client_id and mc.del_flag=0
        left join bms_ysstockinfo stk on bym.ysbill_id = stk.id and IFNULL(ym.code_count,1)=1
        left join bms_ysbillcodeinfo byc on byc.id=bym.ysbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="isIncrement != null">
            AND IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND mc.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="or" close=")">
                ym.warehouse_code like concat('%',trim(#{warehouseCode}),'%')
            </foreach>
            or yi.is_increment=1
            )
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND IFNULL(ym.signing_date,yi.business_time) &gt;= #{signingDateStart,jdbcType=VARCHAR} AND IFNULL(ym.signing_date,yi.business_time) &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND stk.oper_time &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND stk.oper_time &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND byc.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND byc.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND 1=2
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        group by yi.id
        union
        SELECT
        y.id ysbillId
        ,yi.id expensesId
        ,y.stock_code relateCode
        ,y.cost_status costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,DATE_FORMAT(y.instorage_time,'%Y-%m-%d %H:%i:%s') signingDates
        ,y.company_id companyId
        ,mc.client_name clientName
        ,y.client_code clientCode
        ,yi.rule_name ruleName
        ,null receivingStore
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.reduce_fee,0) reduceFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.adjust_fee,0) adjustFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,IFNULL(y.total_boxes,0) totalBoxes
        ,IFNULL(y.trust,0) totalNumber
        ,IFNULL(y.trust,0) skuNumber
        ,IFNULL(y.weight,0) totalWeight
        ,IFNULL(y.volume,0) totalVolume
        ,null cargoValue
        ,yi.fee_flag feeFlag
        ,case when yi.is_increment=1 then yi.other_fee_remark
        else yi.remarks end as  remarks
        ,ye.automatic_billing_remark automaticBillingRemark
        ,yi.adjust_remark adjustRemark
        ,yi.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,ybm.bill_code billCode
        ,y.del_flag delFlag
        ,null orderDate
        ,null orderDates
        ,y.instorage_time signingDate
        ,case when yi.is_increment=1 then yi.fee_create_ate
        else yi.oper_time end as operTime
        ,4 codeType
        ,y.warehouse_code warehouseCode
        ,y.oper_time createTime
        ,y.instorage_time instorageTime
        ,DATE_FORMAT(y.instorage_time,'%Y-%m-%d %H:%i:%s') instorageTimes
        ,y.sku_code skuCode
        ,y.odd_boxes oddBoxes
        ,y.box_type boxType
        ,y.warehouse_area warehouseArea
        ,y.temperature_type temperatureType
        ,y.aqty
        ,y.pallet_number palletNumber
        ,yi.cost_dimension costDimension
        ,null nearStoreKm
        ,null carType
        ,null carModel
        ,null cwFullCases
        ,null cwSplitCases
        ,null ldFullCases
        ,null ldSplitCases
        ,null lcFullCases
        ,null lcSplitCases
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.adjust_fee,0)
        +IFNULL(yi.reduce_fee,0)) baseCostTotal
        ,(IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  ) otherCostTotal
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.adjust_fee,0)
        +IFNULL(yi.reduce_fee,0)) +
        (IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  ) as totalFee
        ,DATE_FORMAT(y.oper_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,null billId
        ,null billType
        ,null transportTypeApi
        ,IFNULL(yi.is_increment,0) isIncrement -- 是否为增值费0否1是
        ,yi.fee_type_first feeTypeFirst -- 计费大类
        ,ye.total_quantity totalQuantity -- 附加费总数量
        ,ye.price price -- 附加费单价
        ,null as storeCode
        ,null as storeName
        ,null as transportType
        ,null as orderType
        ,null as cwPalletNumber
        ,null as lcPalletNumber
        ,null as ldPalletNumber
        ,null as orderNo
        ,y.import_time as importTime
        ,y.import_code as importCode
        ,y.import_by as importBy
        ,y.order_source as orderSource
        ,CASE y.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        FROM bms_ysstockinfo y
        left join bms_ysexpenses_middle ym on y.id=ym.ysbill_id and ym.del_flag=0
        left join bms_yscost_info yi on yi.id=ym.expenses_id and yi.del_flag=0
        left join bms_yscost_extend ye on yi.id=ye.expenses_id and ye.del_flag=0
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.client_code=y.client_code and mc.del_flag = 0
        where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null ">
            AND IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND 4 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.instorage_time &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.instorage_time &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.oper_time &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.oper_time &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND 1=2
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.stock_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND 1=2
        </if>
        ) a
        order by createTime desc,relateCode desc
    </select>

    <select id="selectOrderBill2AndCostStatusONE" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        select * from
        (SELECT
        y.id ysbillId
        ,null expensesId
        ,y.relate_code relateCode
        ,y.cost_status costStatus
        ,null chargeType
        ,null expensesCode
        ,DATE_FORMAT(y.signing_date,'%Y-%m-%d %H:%i:%s') signingDates
        ,y.company_id companyId
        ,mc.client_name clientName
        ,y.client_code clientCode
        ,null ruleName
        ,y.receiving_store receivingStore
        ,0 freight
        ,0 deliveryFee
        ,0 outboundsortingFee
        ,0 shortbargeFee
        ,0 superframesFee
        ,0 excessFee
        ,0 reduceFee
        ,0 ultrafarFee
        ,0 exceptionFee
        ,0 returnFee
        ,0 adjustFee
        ,0 otherCost1
        ,0 otherCost2
        ,0 otherCost3
        ,0 otherCost4
        ,0 otherCost5
        ,0 otherCost6
        ,0 otherCost7
        ,0 otherCost8
        ,0 otherCost9
        ,0 otherCost10
        ,0 otherCost11
        ,0 otherCost12
        ,IFNULL(y.total_boxes,0) totalBoxes
        ,IFNULL(y.total_number,0) totalNumber
        ,IFNULL(y.sku_number,0) skuNumber
        ,IFNULL(y.total_weight,0) totalWeight
        ,IFNULL(y.total_volume,0) totalVolume
        ,IFNULL(y.cargo_value,0) cargoValue
        ,null feeFlag
        ,null remarks
        ,null automaticBillingRemark
        ,null adjustRemark
        ,null billDate
        ,null expensesBy
        ,null expensesTimes
        ,y.billing_status billingStatus
        ,null billCode
        ,y.del_flag delFlag
        ,y.order_date orderDate
        ,DATE_FORMAT(y.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,y.signing_date signingDate
        ,null operTime
        ,y.code_type codeType
        ,y.warehouse_code warehouseCode
        ,y.create_time createTime
        ,null instorageTime
        ,null instorageTimes
        ,null skuCode
        ,null oddBoxes
        ,null boxType
        ,null warehouseArea
        ,null temperatureType
        ,null aqty
        ,y.pallet_number palletNumber
        ,null costDimension
        ,y.near_store_km nearStoreKm
        ,y.car_type carType
        ,y.car_model carModel
        ,y.cw_full_cases cwFullCases
        ,y.cw_split_cases cwSplitCases
        ,y.ld_full_cases ldFullCases
        ,y.ld_split_cases ldSplitCases
        ,y.lc_full_cases lcFullCases
        ,y.lc_split_cases lcSplitCases
        ,0 baseCostTotal
        ,0 otherCostTotal
        ,0 totalFee
        ,DATE_FORMAT(y.create_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,null billId
        ,null billType
        ,y.transport_type transportTypeApi

        ,0 isIncrement -- 是否为增值费0否1是
        ,null feeTypeFirst -- 计费大类
        ,null totalQuantity -- 附加费总数量
        ,null price -- 附加费单价
        ,y.store_code as storeCode
        ,y.receiving_store as storeName
        ,y.transport_type as transportType
        ,y.order_type as orderType
        ,y.cw_pallet_number as cwPalletNumber
        ,y.lc_pallet_number as lcPalletNumber
        ,y.ld_pallet_number as ldPalletNumber
        ,y.order_no as orderNo
        ,y.import_time as importTime
        ,y.import_code as importCode
        ,y.import_by as importBy
        ,y.order_source as orderSource
        ,CASE y.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        FROM bms_ysbillcodeinfo y
        left join bms_clientinfo mc on mc.client_code=y.client_code and mc.del_flag = 0
        where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null">
            AND 0 = #{isIncrement}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND y.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.create_time &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.create_time &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND y.receiving_store like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or
        (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or
        (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(bym.ysbill_id SEPARATOR ',') ysbillId
        ,yi.id expensesId
        ,ym.business_code relateCode
        ,1 costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,DATE_FORMAT(IFNULL(ym.signing_date,yi.business_time),'%Y-%m-%d %H:%i:%s') signingDates
        ,yi.institution_id companyId
        ,mc.client_name clientName
        ,mc.client_code clientCode
        ,yi.rule_name ruleName
        ,null receivingStore
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.reduce_fee,0) reduceFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.adjust_fee,0) adjustFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,IFNULL(ym.total_boxes,0) totalBoxes
        ,IFNULL(ym.total_number,0) totalNumber
        ,IFNULL(ym.sku_number,0) skuNumber
        ,IFNULL(ym.total_weight,0) totalWeight
        ,IFNULL(ym.total_volume,0) totalVolume
        ,IFNULL(ym.cargo_value,0) cargoValue
        ,yi.fee_flag feeFlag
        ,case when yi.is_increment=1 then yi.other_fee_remark
        else yi.remarks end as  remarks
        ,ym.automatic_billing_remark automaticBillingRemark
        ,yi.adjust_remark adjustRemark
        ,yi.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,IFNULL(ybm2.bill_code,ybm.bill_code) billCode
        ,yi.del_flag delFlag
        ,byc.order_date orderDate
        ,DATE_FORMAT(byc.order_date,'%Y-%m-%d %H:%i:%s') orderDates
        ,IFNULL(ym.signing_date,yi.business_time) signingDate
        ,case when yi.is_increment=1 then yi.fee_create_ate
        else yi.oper_time end as  operTime
        ,yi.expenses_type codeType
        ,ym.warehouse_code warehouseCode
        ,DATE_FORMAT(IFNULL(stk.oper_time,byc.create_time),'%Y-%m-%d %H:%i:%s') createTime
        ,null instorageTime
        ,null instorageTimes
        ,null skuCode
        ,null oddBoxes
        ,null boxType
        ,null warehouseArea
        ,stk.temperature_type as  temperatureType
        ,stk.aqty aqty
        ,IFNULL(stk.pallet_number,byc.pallet_number) palletNumber
        ,yi.cost_dimension costDimension
        ,null nearStoreKm
        ,null carType
        ,null carModel
        ,byc.cw_full_cases cwFullCases
        ,byc.cw_split_cases cwSplitCases
        ,byc.ld_full_cases ldFullCases
        ,byc.ld_split_cases ldSplitCases
        ,byc.lc_full_cases lcFullCases
        ,byc.lc_split_cases lcSplitCases
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.adjust_fee,0)
        +IFNULL(yi.reduce_fee,0)) baseCostTotal
        ,(IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  ) otherCostTotal
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.adjust_fee,0)
        +IFNULL(yi.reduce_fee,0))+
        (IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  )  as totalFee
        ,DATE_FORMAT(IFNULL(stk.oper_time,byc.create_time),'%Y-%m-%d %H:%i:%s') createTimes
        ,yi.bill_id billId
        ,ybm.bill_type billType
        ,byc.transport_type transportTypeApi

        ,IFNULL(yi.is_increment,0) isIncrement -- 是否为增值费0否1是
        ,yi.fee_type_first feeTypeFirst -- 计费大类
        ,ym.total_quantity totalQuantity -- 附加费总数量
        ,ym.price price -- 附加费单价
        ,byc.store_code as storeCode
        ,byc.receiving_store as storeName
        ,byc.transport_type as transportType
        ,byc.order_type as orderType
        ,byc.cw_pallet_number as cwPalletNumber
        ,byc.lc_pallet_number as lcPalletNumber
        ,byc.ld_pallet_number as ldPalletNumber
        ,byc.order_no as orderNo
        ,byc.import_time as importTime
        ,byc.import_code as importCode
        ,byc.import_by as importBy
        ,byc.order_source as orderSource
        ,CASE byc.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        from bms_yscost_info yi
        left join bms_yscost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_ysexpenses_middle bym on bym.expenses_id=yi.id
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_ysbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.id=yi.client_id and mc.del_flag=0
        left join bms_ysstockinfo stk on bym.ysbill_id = stk.id and IFNULL(ym.code_count,1)=1
        left join bms_ysbillcodeinfo byc on byc.id=bym.ysbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="isIncrement != null">
            AND IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND mc.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="or" close=")">
                ym.warehouse_code like concat('%',trim(#{warehouseCode}),'%')
            </foreach>
            or yi.is_increment=1
            )
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND IFNULL(ym.signing_date,yi.business_time) &gt;= #{signingDateStart,jdbcType=VARCHAR} AND IFNULL(ym.signing_date,yi.business_time) &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND stk.oper_time &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND stk.oper_time &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND byc.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND byc.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND 1=2
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        group by yi.id
        union
        SELECT
        y.id ysbillId
        ,yi.id expensesId
        ,y.stock_code relateCode
        ,y.cost_status costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,DATE_FORMAT(y.instorage_time,'%Y-%m-%d %H:%i:%s') signingDates
        ,y.company_id companyId
        ,mc.client_name clientName
        ,y.client_code clientCode
        ,yi.rule_name ruleName
        ,null receivingStore
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.reduce_fee,0) reduceFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.adjust_fee,0) adjustFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,IFNULL(y.total_boxes,0) totalBoxes
        ,IFNULL(y.trust,0) totalNumber
        ,IFNULL(y.trust,0) skuNumber
        ,IFNULL(y.weight,0) totalWeight
        ,IFNULL(y.volume,0) totalVolume
        ,null cargoValue
        ,yi.fee_flag feeFlag
        ,case when yi.is_increment=1 then yi.other_fee_remark
        else yi.remarks end as  remarks
        ,ye.automatic_billing_remark automaticBillingRemark
        ,yi.adjust_remark adjustRemark
        ,yi.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,ybm.bill_code billCode
        ,y.del_flag delFlag
        ,null orderDate
        ,null orderDates
        ,y.instorage_time signingDate
        ,case when yi.is_increment=1 then yi.fee_create_ate
        else yi.oper_time end as operTime
        ,4 codeType
        ,y.warehouse_code warehouseCode
        ,y.oper_time createTime
        ,y.instorage_time instorageTime
        ,DATE_FORMAT(y.instorage_time,'%Y-%m-%d %H:%i:%s') instorageTimes
        ,y.sku_code skuCode
        ,y.odd_boxes oddBoxes
        ,y.box_type boxType
        ,y.warehouse_area warehouseArea
        ,y.temperature_type temperatureType
        ,y.aqty
        ,y.pallet_number palletNumber
        ,yi.cost_dimension costDimension
        ,null nearStoreKm
        ,null carType
        ,null carModel
        ,null cwFullCases
        ,null cwSplitCases
        ,null ldFullCases
        ,null ldSplitCases
        ,null lcFullCases
        ,null lcSplitCases
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.adjust_fee,0)
        +IFNULL(yi.reduce_fee,0)) baseCostTotal
        ,(IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  ) otherCostTotal
        ,(IFNULL(yi.freight,0)
        +IFNULL(yi.delivery_fee,0)
        +IFNULL(yi.outboundsorting_fee,0)
        +IFNULL(yi.shortbarge_fee,0)
        +IFNULL(yi.superframes_fee,0)
        +IFNULL(yi.excess_fee,0)
        +IFNULL(yi.ultrafar_fee,0)
        +IFNULL(yi.exception_fee,0)
        +IFNULL(yi.return_fee,0)
        +IFNULL(yi.adjust_fee,0)
        +IFNULL(yi.reduce_fee,0)) +
        (IFNULL(yi.other_cost1,0)
        +IFNULL(yi.other_cost2,0)
        +IFNULL(yi.other_cost3,0)
        +IFNULL(yi.other_cost4,0)
        +IFNULL(yi.other_cost5,0)
        +IFNULL(yi.other_cost6,0)
        +IFNULL(yi.other_cost7,0)
        +IFNULL(yi.other_cost8,0)
        +IFNULL(yi.other_cost9,0)
        +IFNULL(yi.other_cost10,0)
        +IFNULL(yi.other_cost11,0)
        +IFNULL(yi.other_cost12,0)  ) as totalFee
        ,DATE_FORMAT(y.oper_time,'%Y-%m-%d %H:%i:%s') createTimes
        ,null billId
        ,null billType
        ,null transportTypeApi
        ,IFNULL(yi.is_increment,0) isIncrement -- 是否为增值费0否1是
        ,yi.fee_type_first feeTypeFirst -- 计费大类
        ,ye.total_quantity totalQuantity -- 附加费总数量
        ,ye.price price -- 附加费单价
        ,null as storeCode
        ,null as storeName
        ,null as transportType
        ,null as orderType
        ,null as cwPalletNumber
        ,null as lcPalletNumber
        ,null as ldPalletNumber
        ,null as orderNo
        ,y.import_time as importTime
        ,y.import_code as importCode
        ,y.import_by as importBy
        ,y.order_source as orderSource
        ,CASE y.order_source
        WHEN 0 THEN
        '对接产生'
        WHEN 1 THEN
        '导入产生'
        ELSE '对接产生'
        END AS orderSourceName
        FROM bms_ysstockinfo y
        left join bms_ysexpenses_middle ym on y.id=ym.ysbill_id and ym.del_flag=0
        left join bms_yscost_info yi on yi.id=ym.expenses_id and yi.del_flag=0
        left join bms_yscost_extend ye on yi.id=ye.expenses_id and ye.del_flag=0
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.client_code=y.client_code and mc.del_flag = 0
        where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null ">
            AND IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND 4 in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.instorage_time &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.instorage_time &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.oper_time &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.oper_time &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND 1=2
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.stock_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND 1=2
        </if>
        ) a
        order by createTime desc,relateCode desc
    </select>



    <update id="updatemanualGenerationpurchaseNewByleef">
        update bms_yspurchase_feeinfo set ysbill_id =#{mainId},creat_flag= '1'
        where
        CONCAT(id,'') in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(fee_flag,1)=2
        and relation_code = #{clientCode}
        and company_id =  #{companyId}
    </update>


    <update id="updatemanualGenerationpurchaseNew">
        update bms_yspurchase_feeinfo set ysbill_id =#{mainId},creat_flag= '1'
        where
        CONCAT(id,'') in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(fee_flag,1)=1
        and relation_code = #{clientCode}
        and company_id = #{companyId}
    </update>

    <update id="refreshYsCostBillDate">
        <if test="billDate!=null and billDate!=''">
            update bms_yscost_info a
            set a.bill_date =#{billDate}
            where
            a.del_flag=0
            and IFNULL(a.bill_id,0)=0
            and a.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="billDate==null or billDate==''">
            update bms_yscost_info a
            inner join (
            select
            bysc.id,
            bysc.expenses_code,
            bysc.bill_date,
            bysco.order_date as '订单日期',
            bysco.signing_date as '出入库日期',
            byt.instorage_time as'库存日期',
            case
            when bysc.expenses_type = 1 and pss.date_type =1  then DATE_FORMAT(bysco.order_date,'%Y-%m')
            when bysc.expenses_type = 1 and pss.date_type =2  then DATE_FORMAT(bysco.signing_date,'%Y-%m')
            when bysc.expenses_type = 1 then DATE_FORMAT(bysco.signing_date,'%Y-%m')
            when bysc.expenses_type = 2 then DATE_FORMAT(bysco.signing_date,'%Y-%m')
            when bysc.expenses_type = 3 then DATE_FORMAT(bysco.signing_date,'%Y-%m')
            when bysc.expenses_type = 4 then DATE_FORMAT(byt.instorage_time,'%Y-%m')
            else '' end as billDate ,
            bysc.expenses_type,
            case when bym.ysbil_type = 1 then bysco.client_code
            when bym.ysbil_type = 2 then byt.client_code
            else '' end as '客户编码',
            case when bym.ysbil_type = 1 then bysco.client_name
            when bym.ysbil_type = 2 then byt.client_name
            else '' end as '客户名称',
            byt.stock_code,
            bysco.relate_code
            from bms_yscost_info bysc
            left join bms_ysexpenses_middle bym
            on  bym.expenses_id = bysc.id
            left join bms_ysstockinfo byt
            on byt.id = bym.ysbill_id
            and bym.ysbil_type = 2
            left join bms_ysbillcodeinfo bysco
            on bysco.id = bym.ysbill_id
            and bym.ysbil_type = 1
            left join bms_clientinfo cli
            on cli.id = bysc.client_id
            left join pub_settledate_setting pss
            on pss.client_id = bysc.client_id
            and pss.bill_type =1
            and cli.del_flag=0
            where
            1=1
            and bysc.del_flag=0
            and bysc.bill_date > (case when bysc.expenses_type = 1 and pss.date_type =1  then DATE_FORMAT(bysco.order_date,'%Y-%m')
            when bysc.expenses_type = 1 and pss.date_type =2  then DATE_FORMAT(bysco.signing_date,'%Y-%m')
            when bysc.expenses_type = 2 then DATE_FORMAT(bysco.signing_date,'%Y-%m')
            when bysc.expenses_type = 3 then DATE_FORMAT(bysco.signing_date,'%Y-%m')
            when bysc.expenses_type = 4 then DATE_FORMAT(byt.instorage_time,'%Y-%m')
            else '' end)
            and bysc.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>

            group by bysc.id
            ) b
            on a.id = b.id
            and a.expenses_code = b.expenses_code

            set a.bill_date = (case when IFNULL(b.billDate,'')='' then a.bill_date else b.billDate end)

            where a.del_flag=0
            and IFNULL(a.bill_id,0)=0
            and IFNULL(b.billDate,'')!=''
            and IFNULL(a.bill_id,0)=0
            and a.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

    </update>

    <update id="updatemanualGenerationNewByleef">
        update bms_yscost_info a
        inner join
        (
        select a.id,b.client_code from bms_yscost_info a
        inner join bms_clientinfo b
        on a.client_id = b.id
        where
        a.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and b.client_code = #{clientCode}
        and IFNULL(a.fee_flag,1) = 2
        and a.institution_id = #{companyId}
        and a.bill_date = #{billDate}
        and IFNULL(a.bill_id,0)=0
        ) b on a.id = b.id
        set a.bill_id = #{mainId}
        ,a.show_bill_code = #{billCode}
        ,a.show_bill_id = #{mainId}
        ;
        insert into billmain_cost(
        bill_id,
        cost_id,
        create_time
        )
        values
        <foreach collection="ids" item="id"  separator=",">
            (
            #{mainId},
            #{id},
            now()
            )
        </foreach>

    </update>

    <update id="updatemanualGenerationNew">
        update bms_yscost_info a
        inner join
        (
            select
                a.id,
                b.client_code
            from bms_yscost_info a
            inner join bms_clientinfo b on a.client_id = b.id
            inner join bms_yscost_extend ext on ext.main_expense_id = a.main_expense_id and ext.del_flag = '0'
            where a.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            and b.client_code = #{clientCode}
            and IFNULL(a.fee_flag,1) = 1
            and a.institution_id = #{companyId}
            and a.bill_date = #{billDate}
            and IFNULL(a.bill_id,0)=0
            and a.del_flag =0
        ) b on a.id = b.id
        set a.bill_id = #{mainId},
        a.show_bill_id = #{mainId},
        a.show_bill_code = #{billCode}
        ;
        insert into billmain_cost(
        bill_id,
        cost_id,
        create_time
        )
        values
        <foreach collection="ids" item="id"  separator=",">
            (
            #{mainId},
            #{id},
            now()
            )
        </foreach>
    </update>

    <select id="selectStockinfo" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        SELECT
        y.id ysbillId
        ,yi.id expensesId
        ,y.stock_code relateCode
        ,y.sku_code skuCode
        ,y.sku_name skuName
        ,DATE_FORMAT(y.instorage_time,'%Y-%m-%d %H:%i:%s') instorageTimes
        ,y.total_boxes totalBoxes
        ,y.odd_boxes oddBoxes
        ,y.box_type boxType
        ,y.temperature_type temperatureType
        ,y.trust
        ,y.weight
        ,y.volume
        ,y.warehouse_area warehouseArea
        ,y.cost_status costStatus
        ,yi.charge_type chargeType
        ,yi.expenses_code expensesCode
        ,y.company_id companyId
        ,y.client_code clientCode
        ,y.client_name clientName
        ,yi.rule_name ruleName
        ,IFNULL(yi.freight,0) freight
        ,IFNULL(yi.delivery_fee,0) deliveryFee
        ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
        ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
        ,IFNULL(yi.superframes_fee,0) superframesFee
        ,IFNULL(yi.excess_fee,0) excessFee
        ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
        ,IFNULL(yi.exception_fee,0) exceptionFee
        ,IFNULL(yi.return_fee,0) returnFee
        ,IFNULL(yi.other_cost1,0) otherCost1
        ,IFNULL(yi.other_cost2,0) otherCost2
        ,IFNULL(yi.other_cost3,0) otherCost3
        ,IFNULL(yi.other_cost4,0) otherCost4
        ,IFNULL(yi.other_cost5,0) otherCost5
        ,IFNULL(yi.other_cost6,0) otherCost6
        ,IFNULL(yi.other_cost7,0) otherCost7
        ,IFNULL(yi.other_cost8,0) otherCost8
        ,IFNULL(yi.other_cost9,0) otherCost9
        ,IFNULL(yi.other_cost10,0) otherCost10
        ,IFNULL(yi.other_cost11,0) otherCost11
        ,IFNULL(yi.other_cost12,0) otherCost12
        ,yi.fee_flag feeFlag
        ,case when yi.is_increment =1 then yi.other_fee_remark
        else yi.remarks end as   remarks
        ,ybm.bill_date billDate
        ,yi.oper_by expensesBy
        ,DATE_FORMAT(yi.oper_time,'%Y-%m-%d %H:%i:%s') expensesTimes
        ,case when yi.bill_id is not null then 1 else 0 end billingStatus
        ,ybm.bill_code billCode
        ,yi.oper_time operTime
        ,y.warehouse_code warehouseCode
        ,4 codeType
        FROM bms_ysstockinfo y left join bms_ysexpenses_middle ym on CONCAT(y.id,'')=ym.ysbill_id and ym.del_flag=0
        left join bms_yscost_info yi on yi.id=ym.expenses_id and yi.del_flag=0
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        where y.del_flag=0
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="instorageTimeStart != null and instorageTimeStart != '' and instorageTimeEnd != null and instorageTimeEnd!=''">
            AND  y.instorage_time &gt;= #{instorageTimeStart,jdbcType=VARCHAR} AND y.instorage_time &lt;= #{instorageTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND y.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="relateCode != null and relateCode != ''">
            AND y.stock_code like concat('%',trim(#{relateCode}),'%')
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        order by y.oper_time desc,relateCode desc
    </select>

    <insert id="batchSaveAndReturnId" parameterType="java.util.List">
        insert into bms_ysbillcodeinfo
        (id,code_type, relate_code, scheduling_bill_code, client_code, client_name, company_id, network_code, warehouse_code, total_boxes, total_number,split_total_number,sku_number, total_weight, total_volume, cargo_value, order_date, signing_date, if_autarky, storage_service_provider, line_code, line_name, if_base_stores, if_Super_base_kilometer, store_distance_kilometer,delivery_code, delivery_name, store_code, receiving_store, province_origin, originating_City, originating_area, originating_address, destination_Province, destination_city, destination_area, destination_address, cost_status, billing_status, create_code, create_by, create_dept_id, create_time, oper_code, oper_by, oper_dept_id, oper_time, del_flag, delivery_mode, is_rejected, reject_parties,order_type,
        near_store_km,car_type,car_model,cw_full_cases,cw_split_cases,ld_full_cases,ld_split_cases,lc_full_cases,lc_split_cases,pallet_number,transport_type,skuString,temperatureTypeStr,cw_pallet_number,lc_pallet_number,ld_pallet_number,order_no,platform_code,import_time,import_code,import_by,order_source,is_timeout)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},
            #{item.codeType},
            #{item.relateCode},
            #{item.schedulingBillCode},
            #{item.clientCode},
            #{item.clientName},
            #{item.companyId},
            #{item.networkCode},
            #{item.warehouseCode},
            #{item.totalBoxes},
            #{item.totalNumber},
            #{item.splitTotalNumber},
            #{item.skuNumber},
            #{item.totalWeight},
            #{item.totalVolume},
            #{item.cargoValue},
            #{item.orderDate},
            #{item.signingDate},
            #{item.ifAutarky},
            #{item.storageServiceProvider},
            #{item.lineCode},
            #{item.lineName},
            #{item.ifBaseStores},
            #{item.ifSuperBaseKilometer},
            #{item.storeDistanceKilometer},
            #{item.deliveryCode},
            #{item.deliveryName},
            #{item.storeCode},
            #{item.receivingStore},
            #{item.provinceOrigin},
            #{item.originatingCity},
            #{item.originatingArea},
            #{item.originatingAddress},
            #{item.destinationProvince},
            #{item.destinationCity},
            #{item.destinationArea},
            #{item.destinationAddress},
            #{item.costStatus},
            #{item.billingStatus},
            #{item.createCode},
            #{item.createBy},
            #{item.createDeptId},
            #{item.createTime},
            #{item.operCode},
            #{item.operBy},
            #{item.operDeptId},
            #{item.operTime},
            #{item.delFlag},
            #{item.deliveryMode},
            #{item.isRejected},
            #{item.rejectParties},
            #{item.orderType},
            #{item.nearStoreKm},
            #{item.carType},
            #{item.carModel},
            #{item.cwFullCases},
            #{item.cwSplitCases},
            #{item.ldFullCases},
            #{item.ldSplitCases},
            #{item.lcFullCases},
            #{item.lcSplitCases},
            #{item.palletNumber},
            #{item.transportTypeApi},
            #{item.skuString},
            #{item.temperatureTypeStr},
            #{item.cwPalletNumber},
            #{item.lcPalletNumber},
            #{item.ldPalletNumber},
            #{item.orderNo},
            #{item.platformCode},
            #{item.importTime},
            #{item.importCode},
            #{item.importBy},
            #{item.orderSource},
            #{item.isTimeout}
            )
        </foreach>
    </insert>

    <update id="updateBmsYsbillcodeinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeinfo">
        update bms_ysbillcodeinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="codeType != null">code_type = #{codeType},</if>
            <if test="relateCode != null">relate_code = #{relateCode},</if>
            <if test="schedulingBillCode != null">scheduling_bill_code = #{schedulingBillCode},</if>
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="networkCode != null">network_code = #{networkCode},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="totalNumber != null">total_number = #{totalNumber},</if>
            <if test="totalWeight != null">total_weight = #{totalWeight},</if>
            <if test="splitTotalNumber != null">split_total_number = #{splitTotalNumber},</if>
            <if test="skuNumber != null">sku_number = #{skuNumber},</if>
            <if test="totalVolume != null">total_volume = #{totalVolume},</if>
            <if test="cargoValue != null">cargo_value = #{cargoValue},</if>
            <if test="orderDate != null">order_date = #{orderDate},</if>
            <if test="signingDate != null">signing_date = #{signingDate},</if>
            <if test="ifAutarky != null">if_autarky = #{ifAutarky},</if>
            <if test="storageServiceProvider != null">storage_service_provider = #{storageServiceProvider},</if>
            <if test="lineCode != null">line_code = #{lineCode},</if>
            <if test="lineName != null">line_name = #{lineName},</if>
            <if test="ifBaseStores != null">if_base_stores = #{ifBaseStores},</if>
            <if test="ifSuperBaseKilometer != null">if_Super_base_kilometer = #{ifSuperBaseKilometer},</if>
            <if test="storeDistanceKilometer != null">store_distance_kilometer = #{storeDistanceKilometer},</if>
            <if test="deliveryCode != null">delivery_code = #{deliveryCode},</if>
            <if test="deliveryName != null">delivery_name = #{deliveryName},</if>
            <if test="storeCode != null">store_code = #{storeCode},</if>
            <if test="receivingStore != null">receiving_store = #{receivingStore},</if>
            <if test="provinceOrigin != null">province_origin = #{provinceOrigin},</if>
            <if test="originatingCity != null">originating_City = #{originatingCity},</if>
            <if test="originatingArea != null">originating_area = #{originatingArea},</if>
            <if test="originatingAddress != null">originating_address = #{originatingAddress},</if>
            <if test="destinationProvince != null">destination_Province = #{destinationProvince},</if>
            <if test="destinationCity != null">destination_city = #{destinationCity},</if>
            <if test="destinationArea != null">destination_area = #{destinationArea},</if>
            <if test="destinationAddress != null">destination_address = #{destinationAddress},</if>
            <if test="costStatus != null">cost_status = #{costStatus},</if>
            <if test="billingStatus != null">billing_status = #{billingStatus},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="deliveryMode != null">delivery_mode = #{deliveryMode},</if>
            <if test="isRejected != null">is_rejected = #{isRejected},</if>
            <if test="rejectParties != null">reject_parties = #{rejectParties},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="nearStoreKm != null">near_store_km = #{nearStoreKm},</if>
            <if test="cwFullCases != null">cw_full_cases = #{cwFullCases},</if>
            <if test="cwSplitCases != null">cw_split_cases = #{cwSplitCases},</if>
            <if test="ldFullCases != null">ld_full_cases = #{ldFullCases},</if>
            <if test="ldSplitCases != null">ld_split_cases = #{ldSplitCases},</if>
            <if test="lcFullCases != null">lc_full_cases = #{lcFullCases},</if>
            <if test="lcSplitCases != null">lc_split_cases = #{lcSplitCases},</if>
            <if test="carType != null">car_type = #{carType},</if>
            <if test="carModel != null">car_model = #{carModel},</if>
            <if test="palletNumber != null">pallet_number = #{palletNumber},</if>
            <if test="transportTypeApi != null">transport_type = #{transportTypeApi},</if>
            <if test="skuString != null">skuString = #{skuString},</if>
            <if test="temperatureTypeStr != null">temperatureTypeStr = #{temperatureTypeStr},</if>
            <if test="cwPalletNumber != null">cw_pallet_number = #{cwPalletNumber},</if>
            <if test="lcPalletNumber != null">lc_pallet_number = #{lcPalletNumber},</if>
            <if test="ldPalletNumber != null">ld_pallet_number = #{ldPalletNumber},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="platformCode != null">platform_code = #{platformCode},</if>
            <if test="isTimeout != null">is_timeout = #{isTimeout},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectByCode" resultMap="BmsYsbillcodeinfoResult">
        <include refid="selectBmsYsbillcodeinfoVo"/>
        where 1=1 and del_flag = '0'
        <if test="codes != null and codes.size>0 ">
            AND relate_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
        <if test="type != null and type != ''">
            AND code_type = #{type}
        </if>
    </select>

    <select id="selectIsBillByCode"  resultType="com.bbyb.joy.bms.domain.dto.charginglogic.OrderBillingBean">
        SELECT yi.id            AS yscostId,
            yi.expenses_code AS expensesCode,
            yi.pk_id         AS expensesPkId,
            yi.main_pk_id    AS mainExpensesPkId,
            bym.code_pk_id   AS ysbillPkId,
            bym.code_id      AS ysbillId,
            bym.relate_code  AS ysbillCode,
            bym.relate_code  AS workCode,
            yi.bill_id       AS billId
        FROM bms_yscost_info yi
        LEFT JOIN bms_ysexpenses_middle bym ON bym.main_pk_id = yi.main_pk_id AND bym.del_flag = 0 AND bym.code_type = #{type}
        WHERE yi.del_flag = 0
        <if test="codes != null and codes.size>0 ">
            AND bym.relate_code IN
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
        <if test="codeType != null and codeType != ''">
            AND yi.expenses_type = #{codeType}
        </if>
    </select>



    <resultMap id="BmsYsbillcodeinfoBeanResultMap" type="com.bbyb.joy.bms.domain.extend.BmsYsbillcodeinfoExtend">
        <!--@mbg.generated-->
        <!--@Table bms_ysbillcodeinfo-->
        <id column="pk_id" property="pkId" />
        <result column="id" property="id" />
        <result column="code_type" property="codeType" />
        <result column="relate_code" property="relateCode" />
        <result column="scheduling_bill_code" property="schedulingBillCode" />
        <result column="client_code" property="clientCode" />
        <result column="client_name" property="clientName" />
        <result column="company_id"  property="companyId" />
        <result column="network_code" property="networkCode" />
        <result column="warehouse_code" property="warehouseCode" />
        <result column="total_boxes"  property="totalBoxes" />
        <result column="total_number"  property="totalNumber" />
        <result column="split_total_number"  property="splitTotalNumber" />
        <result column="sku_number"  property="skuNumber" />
        <result column="total_weight"  property="totalWeight" />
        <result column="total_volume"  property="totalVolume" />
        <result column="cargo_value"  property="cargoValue" />
        <result column="order_date"  property="orderDate" />
        <result column="signing_date"  property="signingDate" />
        <result column="if_autarky"  property="ifAutarky" />
        <result column="storage_service_provider" property="storageServiceProvider" />
        <result column="line_code" property="lineCode" />
        <result column="line_name" property="lineName" />
        <result column="if_base_stores"  property="ifBaseStores" />
        <result column="if_Super_base_kilometer"  property="ifSuperBaseKilometer" />
        <result column="store_distance_kilometer"  property="storeDistanceKilometer" />
        <result column="delivery_code" property="deliveryCode" />
        <result column="delivery_name" property="deliveryName" />
        <result column="store_code" property="storeCode" />
        <result column="receiving_store" property="receivingStore" />
        <result column="province_origin" property="provinceOrigin" />
        <result column="originating_city" property="originatingCity" />
        <result column="originating_area" property="originatingArea" />
        <result column="originating_address" property="originatingAddress" />
        <result column="destination_Province" property="destinationProvince" />
        <result column="destination_city" property="destinationCity" />
        <result column="destination_area" property="destinationArea" />
        <result column="destination_address" property="destinationAddress" />
        <result column="cost_status"  property="costStatus" />
        <result column="billing_status"  property="billingStatus" />
        <result column="create_code" property="createCode" />
        <result column="create_by" property="createBy" />
        <result column="create_dept_id"  property="createDeptId" />
        <result column="create_time"  property="createTime" />
        <result column="oper_code" property="operCode" />
        <result column="oper_by" property="operBy" />
        <result column="oper_dept_id"  property="operDeptId" />
        <result column="oper_time"  property="operTime" />
        <result column="del_flag"  property="delFlag" />
        <result column="delivery_mode" property="deliveryMode" />
        <result column="transport_type" property="transportType" />
        <result column="is_rejected"  property="isRejected" />
        <result column="reject_parties" property="rejectParties" />
        <result column="order_type" property="orderType" />
        <result column="near_store_km"  property="nearStoreKm" />
        <result column="car_type" property="carType" />
        <result column="car_model" property="carModel" />
        <result column="cw_full_cases"  property="cwFullCases" />
        <result column="cw_split_cases"  property="cwSplitCases" />
        <result column="ld_full_cases"  property="ldFullCases" />
        <result column="ld_split_cases"  property="ldSplitCases" />
        <result column="lc_full_cases"  property="lcFullCases" />
        <result column="lc_split_cases"  property="lcSplitCases" />
        <result column="pallet_number"  property="palletNumber" />
        <result column="skuString"  property="skuString" />
        <result column="temperatureTypeStr"  property="temperatureTypeStr" />
        <result column="cw_pallet_number"  property="cwPalletNumber" />
        <result column="lc_pallet_number"  property="lcPalletNumber" />
        <result column="ld_pallet_number"  property="ldPalletNumber" />
        <result column="order_no" property="orderNo" />
        <result column="platform_code" property="platformCode" />
        <result column="import_time"  property="importTime" />
        <result column="import_code" property="importCode" />
        <result column="import_by" property="importBy" />
        <result column="order_source"  property="orderSource" />
        <result column="returnr_over_num"  property="returnrOverNum" />
        <result column="fail_remark" property="failRemark" />
        <result column="is_timeout"  property="isTimeout" />
        <result column="workCode"  property="workCode" />
        <result column="billId"  property="billId" />
        <result column="mainExpenseCode"  property="mainExpenseCode" />
        <result column="mainExpenseId"  property="mainExpenseId" />
    </resultMap>



    <select id="selectBillAndCostByRelateCode"  resultMap="BmsYsbillcodeinfoBeanResultMap">
        SELECT
            t1.id,
            t1.pk_id,
            t1.code_type,
            t1.relate_code,
            t1.scheduling_bill_code,
            t1.client_code,
            t1.client_name,
            t1.company_id,
            t1.network_code,
            t1.warehouse_code,
            t1.total_boxes,
            t1.total_number,
            t1.split_total_number,
            t1.sku_number,
            t1.total_weight,
            t1.total_volume,
            t1.cargo_value,
            t1.order_date,
            t1.signing_date,
            t1.if_autarky,
            t1.storage_service_provider,
            t1.line_code,
            t1.line_name,
            t1.if_base_stores,
            t1.if_Super_base_kilometer,
            t1.store_distance_kilometer,
            t1.delivery_code,
            t1.delivery_name,
            t1.store_code,
            t1.receiving_store,
            t1.province_origin,
            t1.originating_city,
            t1.originating_area,
            t1.originating_address,
            t1.destination_Province,
            t1.destination_city,
            t1.destination_area,
            t1.destination_address,
            t1.cost_status,
            t1.billing_status,
            t1.create_code,
            t1.create_by,
            t1.create_dept_id,
            t1.create_time,
            t1.oper_code,
            t1.oper_by,
            t1.oper_dept_id,
            t1.oper_time,
            t1.del_flag,
            t1.delivery_mode,
            t1.transport_type,
            t1.is_rejected,
            t1.reject_parties,
            t1.order_type,
            t1.near_store_km,
            t1.car_type,
            t1.car_model,
            t1.cw_full_cases,
            t1.cw_split_cases,
            t1.ld_full_cases,
            t1.ld_split_cases,
            t1.lc_full_cases,
            t1.lc_split_cases,
            t1.pallet_number,
            t1.skuString,
            t1.temperatureTypeStr,
            t1.cw_pallet_number,
            t1.lc_pallet_number,
            t1.ld_pallet_number,
            t1.order_no,
            t1.platform_code,
            t1.import_time,
            t1.import_code,
            t1.import_by,
            t1.order_source,
            t1.returnr_over_num,
            t1.fail_remark,
            t1.is_timeout,
            t1.relate_code AS workCode,
            GROUP_CONCAT(DISTINCT t3.bill_id) billId,
            t4.expenses_code AS mainExpenseCode,
            t4.id AS mainExpenseId
        FROM bms_ysbillcodeinfo t1
        LEFT JOIN bms_ysexpenses_middle t2 ON t2.ysbil_type = 1 AND t2.ysbill_id = t1.id AND t2.del_flag = 0
        LEFT JOIN bms_yscost_info t3 ON t3.main_expense_id = t2.main_expense_id AND t3.del_flag = 0
        LEFT JOIN bms_yscost_main_info t4 ON t4.id = t2.main_expense_id AND t4.del_flag = 0
        WHERE t1.del_flag = 0
        <if test="codes != null and codes.size>0 ">
            AND t1.relate_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
        <if test="type != null and type != ''">
            AND t1.code_type = #{type}
        </if>
        GROUP BY workCode
    </select>

    <select id="selectIsBillByStockCode"  resultType="com.bbyb.joy.bms.domain.dto.charginglogic.OrderBillingBean">
        SELECT
        yi.id yscostId,
        yi.expenses_code expensesCode,
        stk.id ysbillId,
        stk.stock_code ysbillCode,
        bym.expenses_id,
        ym.business_code workCode,
        yi.bill_id billId
        from bms_yscost_info yi
        left join bms_yscost_extend ym on yi.main_expense_id=ym.main_expense_id and ym.del_flag=0
        left join bms_ysexpenses_middle bym on bym.main_expense_id=yi.main_expense_id and bym.ysbil_type = 2 and bym.del_flag = 0
        left join bms_ysstockinfo stk on bym.ysbill_id = stk.id and stk.del_flag=0
        where yi.del_flag = 0
        <if test="codes != null and codes.size>0 ">
            AND stk.stock_code  in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>





    <resultMap id="BmsYsstockinfoBeanResultMap" type="com.bbyb.joy.bms.domain.extend.BmsYsstockinfoExtend">
        <!--@mbg.generated-->
        <!--@Table bms_ysstockinfo-->
        <id column="id"  property="id" />
        <result column="company_id"  property="companyId" />
        <result column="client_code"  property="clientCode" />
        <result column="client_name"  property="clientName" />
        <result column="warehouse_code"  property="warehouseCode" />
        <result column="stock_code"  property="stockCode" />
        <result column="ysbill_id"  property="ysbillId" />
        <result column="sku_code"  property="skuCode" />
        <result column="sku_name"  property="skuName" />
        <result column="instorage_time"  property="instorageTime" />
        <result column="total_boxes"  property="totalBoxes" />
        <result column="odd_boxes"  property="oddBoxes" />
        <result column="box_type"  property="boxType" />
        <result column="temperature_type"  property="temperatureType" />
        <result column="trust"  property="trust" />
        <result column="weight"  property="weight" />
        <result column="volume"  property="volume" />
        <result column="warehouse_area"  property="warehouseArea" />
        <result column="remark"  property="remark" />
        <result column="oper_dept_id"  property="operDeptId" />
        <result column="oper_code"  property="operCode" />
        <result column="oper_by"  property="operBy" />
        <result column="oper_time"  property="operTime" />
        <result column="del_flag"  property="delFlag" />
        <result column="cost_status"  property="costStatus" />
        <result column="billing_status"  property="billingStatus" />
        <result column="aqty"  property="aqty" />
        <result column="pallet_number"  property="palletNumber" />
        <result column="if_autarky"  property="ifAutarky" />
        <result column="storage_service_provider"  property="storageServiceProvider" />
        <result column="pallet_ruler"  property="palletRuler" />
        <result column="unit"  property="unit" />
        <result column="cost_vlaue_status"  property="costVlaueStatus" />
        <result column="import_time"  property="importTime" />
        <result column="import_code"  property="importCode" />
        <result column="import_by"  property="importBy" />
        <result column="order_source"  property="orderSource" />
        <result column="fail_remark"  property="failRemark" />
        <result column="workCode"  property="workCode" />
        <result column="billId"  property="billId" />
        <result column="mainExpenseCode"  property="mainExpenseCode" />
        <result column="mainExpenseId"  property="mainExpenseId" />
    </resultMap>


    <select id="selectBillAndCostByStockCode"  resultMap="BmsYsstockinfoBeanResultMap">
        SELECT
            t1.id,
            t1.company_id,
            t1.client_code,
            t1.client_name,
            t1.warehouse_code,
            t1.stock_code,
            t1.ysbill_id,
            t1.sku_code,
            t1.sku_name,
            t1.instorage_time,
            t1.total_boxes,
            t1.odd_boxes,
            t1.box_type,
            t1.temperature_type,
            t1.trust,
            t1.weight,
            t1.volume,
            t1.warehouse_area,
            t1.remark,
            t1.oper_dept_id,
            t1.oper_code,
            t1.oper_by,
            t1.oper_time,
            t1.del_flag,
            t1.cost_status,
            t1.billing_status,
            t1.aqty,
            t1.pallet_number,
            t1.if_autarky,
            t1.storage_service_provider,
            t1.pallet_ruler,
            t1.unit,
            t1.cost_vlaue_status,
            t1.import_time,
            t1.import_code,
            t1.import_by,
            t1.order_source,
            t1.fail_remark,
            t1.stock_code workCode,
            GROUP_CONCATE(DISTINCT t3.bill_id) AS billId,
            4 AS codeType,
            t4.expenses_code AS mainExpenseCode,
            t4.id AS mainExpenseId
        FROM bms_ysstockinfo t1
        LEFT JOIN bms_ysexpenses_middle t2 ON t2.ysbil_type = 1 AND t2.ysbill_id = t1.id AND t2.del_flag = 0
        LEFT JOIN bms_yscost_info t3 ON t3.main_expense_id = t2.main_expense_id AND t3.del_flag = 0
        LEFT JOIN bms_yscost_main_info t4 ON t4.id = t2.main_expense_id AND t4.del_flag = 0
        WHERE t1.del_flag = 0
        <if test="codes != null and codes.size>0 ">
            AND t1.stock_code  in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>




    <select id="selectByschedulingBillCode" resultMap="BmsYsbillcodeinfoResult">
        <include refid="selectBmsYsbillcodeinfoVo"/>
        where 1=1
        <if test="codes != null and codes.size>0 ">
            AND scheduling_bill_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <select id="selectByCodeInfoId" resultMap="BmsYsbillcodeinfoResult">
        select byc.id,
            byc.pk_id,
            byc.code_type,
            byc.relate_code,
            byc.scheduling_bill_code,
            byc.client_code,
            byc.client_name,
            byc.company_id,
            byc.network_code,
            byc.warehouse_code,
            byc.total_boxes,
            byc.total_number,
            byc.split_total_number,
            byc.sku_number,
            byc.total_weight,
            byc.total_volume,
            byc.cargo_value,
            byc.order_date,
            byc.signing_date,
            byc.if_autarky,
            byc.storage_service_provider,
            byc.line_code,
            byc.line_name,
            byc.if_base_stores,
            byc.if_Super_base_kilometer,
            byc.store_distance_kilometer,
            byc.delivery_code,
            byc.delivery_name,
            byc.store_code,
            byc.receiving_store,
            byc.province_origin,
            byc.originating_city,
            byc.originating_area,
            byc.originating_address,
            byc.destination_Province,
            byc.destination_city,
            byc.destination_area,
            byc.destination_address,
            byc.cost_status,
            byc.billing_status,
            byc.create_code,
            byc.create_by,
            byc.create_dept_id,
            byc.create_time,
            byc.oper_code,
            byc.oper_by,
            byc.oper_dept_id,
            byc.oper_time,
            byc.del_flag,
            byc.delivery_mode,
            byc.transport_type,
            byc.is_rejected,
            byc.reject_parties,
            byc.order_type,
            byc.near_store_km,
            byc.car_type,
            byc.car_model,
            byc.cw_full_cases,
            byc.cw_split_cases,
            byc.ld_full_cases,
            byc.ld_split_cases,
            byc.lc_full_cases,
            byc.lc_split_cases,
            byc.pallet_number,
            byc.skuString,
            byc.temperatureTypeStr,
            byc.cw_pallet_number,
            byc.lc_pallet_number,
            byc.ld_pallet_number,
            byc.order_no,
            byc.platform_code,
            byc.import_time,
            byc.import_code,
            byc.import_by,
            byc.order_source,
            byc.returnr_over_num,
            byc.fail_remark,
            byc.is_timeout,
            mc.client_code,
            mc.client_name,
            mc.settle_setting,
            mc.carrierpay_type
        from bms_ysbillcodeinfo byc
        left join bms_clientinfo mc on byc.client_code = mc.client_code
        where byc.id = #{id}
    </select>


    <select id="selectOrderByExpensesIdIfFee" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        select * from (
                          SELECT
                              y.id ysbillId
                          FROM bms_ysbillcodeinfo y
                          where y.del_flag = '0'  and y.id = #{expensesId}

                          union
                          SELECT
                              y.id ysbillId
                          FROM bms_ysstockinfo y
                          where y.del_flag = '0'  and y.id = #{expensesId}
                      ) a group by a.ysbillId
    </select>

    <select id="selectOrderByExpensesId2" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        select * from (
                          SELECT
                              y.id ysbillId
                               ,y.code_type codeType
                               ,'' expensesId
                               ,y.relate_code relateCode
                               ,y.scheduling_bill_code schedulingBillCode
                               ,y.company_id companyId
                               ,mc.id as clientId
                               ,y.client_code clientCode
                               ,y.client_name clientName
                               ,DATE_FORMAT(y.order_date,'%Y-%m-%d %H:%i:%s') orderDates
                               ,DATE_FORMAT(y.signing_date,'%Y-%m-%d %H:%i:%s') signingDates
                               ,y.delivery_code deliveryCode
                               ,y.delivery_name deliveryName
                               ,y.province_origin provinceOrigin
                               ,y.originating_City originatingCity
                               ,y.originating_area originatingArea
                               ,y.originating_address originatingAddress
                               ,y.store_code storeCode
                               ,y.receiving_store storeName
                               ,y.destination_Province destinationProvince
                               ,y.destination_city destinationCity
                               ,y.destination_area destinationArea
                               ,y.destination_address destinationAddress
                               ,y.is_rejected isRejected
                               ,y.reject_parties rejectParties
                               ,delivery_mode deliveryMode
                               ,y.del_flag delFlag
                               ,IFNULL(y.total_boxes,0) totalBoxes
                               ,IFNULL(y.split_total_number,0) splitTotalNumber
                               ,IFNULL(y.sku_number,0) skuNumber
                               ,IFNULL(y.total_number,0) totalNumber
                               ,IFNULL(y.total_weight,0) totalWeight
                               ,IFNULL(y.total_volume,0) totalVolume
                               ,IFNULL(y.cargo_value,0) cargoValue
                               ,y.line_code lineCode
                               ,y.line_name lineName
                               ,y.near_store_km nearStoreKm
                               ,y.car_type carType
                               ,y.car_model carModel
                               ,y.cw_full_cases cwFullCases
                               ,y.cw_split_cases cwSplitCases
                               ,y.ld_full_cases ldFullCases
                               ,y.ld_split_cases ldSplitCases
                               ,y.lc_full_cases lcFullCases
                               ,y.lc_split_cases lcSplitCases
                               ,IFNULL(ym.freight,0) freight
                               ,IFNULL(ym.delivery_fee,0) deliveryFee
                               ,IFNULL(ym.outboundsorting_fee,0) outboundsortingFee
                               ,IFNULL(ym.shortbarge_fee,0) shortbargeFee
                               ,IFNULL(ym.superframes_fee,0) superframesFee
                               ,IFNULL(ym.excess_fee,0) excessFee
                               ,IFNULL(ym.ultrafar_fee,0) ultrafarFee
                               ,IFNULL(ym.exception_fee,0) exceptionFee
                               ,IFNULL(ym.return_fee,0) returnFee
                               ,IFNULL(yi.adjust_fee,0) adjustFee
                               ,IFNULL(ym.other_cost1,0) otherCost1
                               ,IFNULL(ym.other_cost2,0) otherCost2
                               ,IFNULL(ym.other_cost3,0) otherCost3
                               ,IFNULL(ym.other_cost4,0) otherCost4
                               ,IFNULL(ym.other_cost5,0) otherCost5
                               ,IFNULL(ym.other_cost6,0) otherCost6
                               ,IFNULL(ym.other_cost7,0) otherCost7
                               ,IFNULL(ym.other_cost8,0) otherCost8
                               ,IFNULL(ym.other_cost9,0) otherCost9
                               ,IFNULL(ym.other_cost10,0) otherCost10
                               ,IFNULL(ym.other_cost11,0) otherCost11
                               ,IFNULL(ym.other_cost12,0) otherCost12
                               ,bymi.expenses_code expensesCode
                               ,bymi.institution_id institutionId
                               ,bymi.client_id  costClientId
                               ,y.if_autarky ifAutarky
                               ,y.if_base_stores ifBaseStores
                               ,y.if_Super_base_kilometer ifSuperBaseKilometer
                               ,y.store_distance_kilometer storeDistanceKilometer
                               ,y.warehouse_code warehouseCode
                               ,mr.total_mileage totalMileage
                               ,mr.store_num storeNum
                               ,null warmZoneType
                               ,null aqty
                               ,ye.price price
                               ,IFNULL(bymi.is_increment,0) as isIncrement
                               ,y.pallet_number palletNumber
                               ,case when bymi.is_increment =1 then bymi.other_fee_remark
                                     else bymi.remarks end as   remarks
                               ,ye.automatic_billing_remark automaticBillingRemark
                               ,y.transport_type transportType
                               ,y.order_type as orderType
                               ,y.cw_pallet_number as cwPalletNumber
                               ,y.lc_pallet_number as lcPalletNumber
                               ,y.ld_pallet_number as ldPalletNumber
                               ,y.order_no as orderNo
                               ,y.platform_code as platformCode
                            FROM bms_ysbillcodeinfo y
                            LEFT JOIN bms_ysexpenses_middle ym on y.id=ym.ysbill_id and ym.del_flag=0
                            LEFT JOIN bms_yscost_info yi on yi.main_expense_id=ym.main_expense_id and yi.del_flag=0
                            LEFT JOIN bms_yscost_extend ye on yi.main_expense_id=ye.main_expense_id and ye.del_flag=0
                            LEFT JOIN bms_routeinfo mr on mr.route_code=y.line_code
                            LEFT JOIN bms_yscost_main_info bymi on ym.main_expense_id=bymi.id
                            LEFT JOIN bms_clientinfo mc on mc.client_code=y.client_code
                          where y.del_flag = '0'  AND  y.id = #{expensesId}
                          union
                          SELECT
                              y.id ysbillId
                               ,4 codeTypeg
                               ,'' expensesId
                               ,y.stock_code relateCode
                               ,null schedulingBillCode
                               ,y.company_id companyId
                               ,mc.id as clientId
                               ,y.client_code clientCode
                               ,y.client_name clientName
                               ,null orderDates
                               ,DATE_FORMAT(y.instorage_time,'%Y-%m-%d %H:%i:%s') signingDates
                               ,null deliveryCode
                               ,null deliveryName
                               ,null provinceOrigin
                               ,null originatingCity
                               ,null originatingArea
                               ,null originatingAddress
                               ,null storeCode
                               ,null storeName
                               ,null destinationProvince
                               ,null destinationCity
                               ,null destinationArea
                               ,null destinationAddress
                               ,null isRejected
                               ,null rejectParties
                               ,null deliveryMode
                               ,y.del_flag delFlag
                               ,IFNULL(y.total_boxes,0) totalBoxes
                               ,IFNULL(y.odd_boxes,0) splitTotalNumber
                               ,IFNULL(y.trust,0) skuNumber
                               ,IFNULL(y.total_boxes,0) totalNumber
                               ,IFNULL(y.weight,0) totalWeight
                               ,IFNULL(y.volume,0) totalVolume
                               ,null cargoValue
                               ,null lineCode
                               ,null lineName
                               ,null nearStoreKm
                               ,null carType
                               ,null carModel
                               ,null cwFullCases
                               ,null cwSplitCases
                               ,null ldFullCases
                               ,null ldSplitCases
                               ,null lcFullCases
                               ,null lcSplitCases
                               ,IFNULL(yi.freight,0) freight
                               ,IFNULL(yi.delivery_fee,0) deliveryFee
                               ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
                               ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
                               ,IFNULL(yi.superframes_fee,0) superframesFee
                               ,IFNULL(yi.excess_fee,0) excessFee
                               ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
                               ,IFNULL(yi.exception_fee,0) exceptionFee
                               ,IFNULL(yi.return_fee,0) returnFee
                               ,IFNULL(yi.adjust_fee,0) adjustFee
                               ,IFNULL(yi.other_cost1,0) otherCost1
                               ,IFNULL(yi.other_cost2,0) otherCost2
                               ,IFNULL(yi.other_cost3,0) otherCost3
                               ,IFNULL(yi.other_cost4,0) otherCost4
                               ,IFNULL(yi.other_cost5,0) otherCost5
                               ,IFNULL(yi.other_cost6,0) otherCost6
                               ,IFNULL(yi.other_cost7,0) otherCost7
                               ,IFNULL(yi.other_cost8,0) otherCost8
                               ,IFNULL(yi.other_cost9,0) otherCost9
                               ,IFNULL(yi.other_cost10,0) otherCost10
                               ,IFNULL(yi.other_cost11,0) otherCost11
                               ,IFNULL(yi.other_cost12,0) otherCost12
                               ,yi.expenses_code expensesCode
                               ,yi.institution_id institutionId
                               ,yi.client_id  costClientId
                               ,null ifAutarky
                               ,null ifBaseStores
                               ,null ifSuperBaseKilometer
                               ,null storeDistanceKilometer
                               ,y.warehouse_code warehouseCode
                               ,null totalMileage
                               ,null storeNum
                               ,y.temperature_type warmZoneType
                               ,y.aqty aqty
                               ,ye.price price
                               ,IFNULL(bymi.is_increment,0) as isIncrement
                               ,y.pallet_number palletNumber
                               ,case when bymi.is_increment =1 then bymi.other_fee_remark
                                     else bymi.remarks end as   remarks
                               ,ye.automatic_billing_remark automaticBillingRemark
                               ,null transportType
                               ,null as orderType
                               ,null as cwPalletNumber
                               ,null as lcPalletNumber
                               ,null as ldPalletNumber
                               ,null as orderNo
                               ,null as platformCode
                            FROM bms_ysstockinfo y
                            LEFT JOIN bms_ysexpenses_middle ym on y.id=ym.ysbill_id and ym.del_flag=0
                            LEFT JOIN bms_yscost_info yi on yi.main_expense_id=ym.main_expense_id and yi.del_flag=0
                            LEFT JOIN bms_yscost_extend ye on yi.main_expense_id=ye.main_expense_id and ye.del_flag=0
                            LEFT JOIN bms_yscost_main_info bymi on ym.main_expense_id=bymi.id
                            LEFT JOIN bms_clientinfo mc on mc.client_code=y.client_code
                          where y.del_flag = '0'  AND  y.id = #{expensesId}
                      ) a where a.ysbillId is not null group by a.expensesCode,a.ysbillId  ORDER BY  relateCode  asc
    </select>

    <select id="selectOrderByExpensesId" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        select * from (
              SELECT
                  y.id ysbillId
                   ,y.code_type codeType
                   ,y.relate_code relateCode
                   ,y.scheduling_bill_code schedulingBillCode
                   ,y.company_id companyId
                   ,mc.id as clientId
                   ,y.client_code clientCode
                   ,y.client_name clientName
                   ,DATE_FORMAT(y.order_date,'%Y-%m-%d %H:%i:%s') orderDates
                   ,DATE_FORMAT(y.signing_date,'%Y-%m-%d %H:%i:%s') signingDates
                   ,y.delivery_code deliveryCode
                   ,y.delivery_name deliveryName
                   ,y.province_origin provinceOrigin
                   ,y.originating_City originatingCity
                   ,y.originating_area originatingArea
                   ,y.originating_address originatingAddress
                   ,y.store_code storeCode
                   ,y.receiving_store storeName
                   ,y.destination_Province destinationProvince
                   ,y.destination_city destinationCity
                   ,y.destination_area destinationArea
                   ,y.destination_address destinationAddress
                   ,y.is_rejected isRejected
                   ,y.reject_parties rejectParties
                   ,delivery_mode deliveryMode
                   ,y.del_flag delFlag
                   ,IFNULL(y.total_boxes,0) totalBoxes
                   ,IFNULL(y.split_total_number,0) splitTotalNumber
                   ,IFNULL(y.sku_number,0) skuNumber
                   ,IFNULL(y.total_number,0) totalNumber
                   ,IFNULL(y.total_weight,0) totalWeight
                   ,IFNULL(y.total_volume,0) totalVolume
                   ,IFNULL(y.cargo_value,0) cargoValue
                   ,y.line_code lineCode
                   ,y.line_name lineName
                   ,y.near_store_km nearStoreKm
                   ,y.car_type carType
                   ,y.car_model carModel
                   ,y.cw_full_cases cwFullCases
                   ,y.cw_split_cases cwSplitCases
                   ,y.ld_full_cases ldFullCases
                   ,y.ld_split_cases ldSplitCases
                   ,y.lc_full_cases lcFullCases
                   ,y.lc_split_cases lcSplitCases
                    ,IFNULL(yi.freight,0) freight
                    ,IFNULL(yi.delivery_fee,0) deliveryFee
                    ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
                    ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
                    ,IFNULL(yi.superframes_fee,0) superframesFee
                    ,IFNULL(yi.excess_fee,0) excessFee
                    ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
                    ,IFNULL(yi.exception_fee,0) exceptionFee
                    ,IFNULL(yi.return_fee,0) returnFee
                    ,IFNULL(yi.adjust_fee,0) adjustFee
                    ,IFNULL(yi.other_cost1,0) otherCost1
                    ,IFNULL(yi.other_cost2,0) otherCost2
                    ,IFNULL(yi.other_cost3,0) otherCost3
                    ,IFNULL(yi.other_cost4,0) otherCost4
                    ,IFNULL(yi.other_cost5,0) otherCost5
                    ,IFNULL(yi.other_cost6,0) otherCost6
                    ,IFNULL(yi.other_cost7,0) otherCost7
                    ,IFNULL(yi.other_cost8,0) otherCost8
                    ,IFNULL(yi.other_cost9,0) otherCost9
                    ,IFNULL(yi.other_cost10,0) otherCost10
                    ,IFNULL(yi.other_cost11,0) otherCost11
                    ,IFNULL(yi.other_cost12,0) otherCost12
                   ,yi.expenses_code expensesCode
                   ,yi.institution_id institutionId
                   ,yi.client_id  costClientId
                   ,y.if_autarky ifAutarky
                   ,y.if_base_stores ifBaseStores
                   ,y.if_Super_base_kilometer ifSuperBaseKilometer
                   ,y.store_distance_kilometer storeDistanceKilometer
                   ,y.warehouse_code warehouseCode
                   ,mr.total_mileage totalMileage
                   ,mr.store_num storeNum
                   ,null warmZoneType
                   ,ye.total_quantity aqty
                   ,ye.price price
                   ,IFNULL(yi.is_increment,0) as isIncrement
                   ,y.pallet_number palletNumber
                   ,case when yi.is_increment =1 then yi.other_fee_remark
                         else yi.remarks end as   remarks
                   ,IFNULL(ye.automatic_billing_remark,yi.other_fee_remark) automaticBillingRemark
                   ,y.transport_type transportType
                   ,y.order_type as orderType
                   ,y.cw_pallet_number as cwPalletNumber
                   ,y.lc_pallet_number as lcPalletNumber
                   ,y.ld_pallet_number as ldPalletNumber
                   ,y.order_no as orderNo
                   ,y.platform_code as platformCode
                   ,yi.id as expensesId
                   ,yi.id as yscostId
                   ,bymi.id as mainExpenseId
                   ,bymi.expenses_code as mainExpenseCode
            FROM bms_yscost_main_info bymi
            LEFT JOIN bms_yscost_info yi on yi.main_expense_id=bymi.id and yi.del_flag = '0'
            LEFT JOIN bms_ysexpenses_middle ym on yi.main_expense_id=ym.main_expense_id and ym.del_flag=0
            LEFT JOIN bms_ysbillcodeinfo y on y.id=ym.ysbill_id  and y.del_flag=0
            LEFT JOIN bms_yscost_extend ye on yi.main_expense_id=ye.main_expense_id and ye.del_flag=0
            LEFT JOIN bms_routeinfo mr on mr.route_code=y.line_code
            LEFT JOIN bms_clientinfo mc on mc.client_code=y.client_code
            WHERE yi.id = #{idParam} group by ym.ysbill_id
            UNION
              SELECT
                  y.id ysbillId
                   ,4 codeTypeg
                   ,y.stock_code relateCode
                   ,null schedulingBillCode
                   ,y.company_id companyId
                   ,mc.id as clientId
                   ,y.client_code clientCode
                   ,y.client_name clientName
                   ,null orderDates
                   ,DATE_FORMAT(y.instorage_time,'%Y-%m-%d %H:%i:%s') signingDates
                   ,null deliveryCode
                   ,null deliveryName
                   ,null provinceOrigin
                   ,null originatingCity
                   ,null originatingArea
                   ,null originatingAddress
                   ,null storeCode
                   ,null storeName
                   ,null destinationProvince
                   ,null destinationCity
                   ,null destinationArea
                   ,null destinationAddress
                   ,null isRejected
                   ,null rejectParties
                   ,null deliveryMode
                   ,y.del_flag delFlag
                   ,IFNULL(y.total_boxes,0) totalBoxes
                   ,IFNULL(y.odd_boxes,0) splitTotalNumber
                   ,IFNULL(y.trust,0) skuNumber
                   ,IFNULL(y.total_boxes,0) totalNumber
                   ,IFNULL(y.weight,0) totalWeight
                   ,IFNULL(y.volume,0) totalVolume
                   ,null cargoValue
                   ,null lineCode
                   ,null lineName
                   ,null nearStoreKm
                   ,null carType
                   ,null carModel
                   ,null cwFullCases
                   ,null cwSplitCases
                   ,null ldFullCases
                   ,null ldSplitCases
                   ,null lcFullCases
                   ,null lcSplitCases
                   ,IFNULL(yi.freight,0) freight
                   ,IFNULL(yi.delivery_fee,0) deliveryFee
                   ,IFNULL(yi.outboundsorting_fee,0) outboundsortingFee
                   ,IFNULL(yi.shortbarge_fee,0) shortbargeFee
                   ,IFNULL(yi.superframes_fee,0) superframesFee
                   ,IFNULL(yi.excess_fee,0) excessFee
                   ,IFNULL(yi.ultrafar_fee,0) ultrafarFee
                   ,IFNULL(yi.exception_fee,0) exceptionFee
                   ,IFNULL(yi.return_fee,0) returnFee
                   ,IFNULL(yi.adjust_fee,0) adjustFee
                   ,IFNULL(yi.other_cost1,0) otherCost1
                   ,IFNULL(yi.other_cost2,0) otherCost2
                   ,IFNULL(yi.other_cost3,0) otherCost3
                   ,IFNULL(yi.other_cost4,0) otherCost4
                   ,IFNULL(yi.other_cost5,0) otherCost5
                   ,IFNULL(yi.other_cost6,0) otherCost6
                   ,IFNULL(yi.other_cost7,0) otherCost7
                   ,IFNULL(yi.other_cost8,0) otherCost8
                   ,IFNULL(yi.other_cost9,0) otherCost9
                   ,IFNULL(yi.other_cost10,0) otherCost10
                   ,IFNULL(yi.other_cost11,0) otherCost11
                   ,IFNULL(yi.other_cost12,0) otherCost12
                   ,yi.expenses_code expensesCode
                   ,yi.institution_id institutionId
                   ,yi.client_id  costClientId
                   ,null ifAutarky
                   ,null ifBaseStores
                   ,null ifSuperBaseKilometer
                   ,null storeDistanceKilometer
                   ,y.warehouse_code warehouseCode
                   ,null totalMileage
                   ,null storeNum
                   ,y.temperature_type warmZoneType
                   ,IFNULL(y.aqty,ye.total_quantity) aqty
                   ,ye.price price
                   ,IFNULL(yi.is_increment,0) as isIncrement
                   ,y.pallet_number palletNumber
                   ,case when yi.is_increment =1 then yi.other_fee_remark
                         else yi.remarks end as   remarks
                   ,IFNULL(ye.automatic_billing_remark,yi.other_fee_remark) automaticBillingRemark
                   ,null transportType
                   ,null as orderType
                   ,null as cwPalletNumber
                   ,null as lcPalletNumber
                   ,null as ldPalletNumber
                   ,null as orderNo
                   ,null as platformCode
                   ,yi.id as expensesId
                   ,yi.id as yscostId
                   ,bymi.id as mainExpenseId
                   ,bymi.expenses_code as mainExpenseCode
                FROM bms_yscost_main_info bymi
                LEFT JOIN bms_yscost_info yi on yi.main_expense_id=bymi.id and yi.del_flag = '0'
                LEFT JOIN bms_ysexpenses_middle ym  on yi.main_expense_id=ym.main_expense_id  and ym.del_flag=0
                LEFT JOIN bms_ysstockinfo y on y.id=ym.ysbill_id and y.del_flag=0
                LEFT JOIN bms_yscost_extend ye on yi.main_expense_id=ye.main_expense_id and ye.del_flag=0
                LEFT JOIN bms_clientinfo mc on mc.client_code=y.client_code
              where  yi.id = #{idParam} group by ym.ysbill_id
          ) a where a.ysbillId is not null group by a.expensesCode,a.ysbillId ORDER BY  relateCode  asc
    </select>


    <select id="getOrderinfoDetailByIds" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.export.BmsYsbillcodeExport">
        select
        info.relate_code as relateCode,
        info.scheduling_bill_code as schedulingBillCode,
        info.company_id as companyId,
        info.client_code as clientCode,
        info.client_name as clientName,
        info.order_date as orderDate,
        info.signing_date as signingDate,
        IFNULL(info.sku_number,0) as totalNumber,
        IFNULL(info.total_weight,0) as totalWeight,
        IFNULL(info.total_volume,0) as totalVolume,
        IFNULL(info.cargo_value,0) as cargoValue,
        IFNULL(info.total_boxes,0) as totalBoxes,
        IFNULL(info.cw_full_cases,0) as cwFullCases,
        IFNULL(info.cw_split_cases,0) as cwSplitCases,
        IFNULL(info.ld_full_cases,0) as ldFullCases,
        IFNULL(info.ld_split_cases,0) as ldSplitCases,
        IFNULL(info.lc_full_cases,0) as lcFullCases,
        IFNULL(info.lc_split_cases,0) as lcSplitCases,
        info.line_code as lineCode,
        info.delivery_mode as deliveryMode,
        info.if_autarky as ifAutarky,
        IFNULL(ro.total_mileage,0) as totalMileage,
        IFNULL(ro.store_num,0) as storeNum,
        info.if_base_stores as ifBaseStores,
        info.if_Super_base_kilometer as ifSuperBaseKilometer,
        IFNULL(info.store_distance_kilometer,0) as storeDistanceKilometer,
        IFNULL(info.near_store_km,0) as nearStoreKm,
        info.is_rejected as isRejected,
        info.reject_parties as rejectParties,
        info.car_model as carModel,
        ware.warehouse_name as warehouseName,
        info.delivery_code as deliveryCode,
        info.province_origin as provinceOrigin,
        info.originating_city as originatingCity,
        info.originating_area as originatingArea,
        info.originating_address as originatingAddress,
        info.receiving_store as receivingStore,
        info.transport_type as transportType,
        info.store_code as storeCode,
        info.receiving_store as storeName,
        info.destination_Province as destinationProvince,
        info.destination_city as destinationCity,
        info.destination_area as destinationArea,
        info.destination_address as destinationAddress,
        info.order_no as orderNo,
        info.platform_code as platformCode,
        bym.share_type as shareType,
        bym.share_amount as shareAmount,
        bym.freight,
        bym.delivery_fee as deliveryFee,
        bym.ultrafar_fee as ultrafarFee ,
        bym.superframes_fee as superframesFee,
        bym.excess_fee as excessFee,
        bym.reduce_fee as reduceFee ,
        bym.outboundsorting_fee as outboundsortingFee,
        bym.shortbarge_fee as shortbargeFee,
        bym.return_fee as returnFee,
        bym.exception_fee as exceptionFee,
        bym.other_cost1 as otherCost1,
        bym.other_cost2 as otherCost2,
        bym.other_cost3 as otherCost3,
        bym.other_cost4 as otherCost4,
        bym.other_cost5 as otherCost5,
        bym.other_cost6 as otherCost6,
        bym.other_cost7 as otherCost7,
        bym.other_cost8 as otherCost8,
        bym.other_cost9 as otherCost9,
        bym.other_cost10 as otherCost10,
        bym.other_cost11 as otherCost11,
        bym.other_cost12 as otherCost12
        from bms_ysbillcodeinfo info
        left join bms_routeinfo ro
        on info.line_code = ro.route_code
        left join mdm_warehouseinfo ware
        on ware.warehouse_code = info.warehouse_code
        left join bms_ysexpenses_middle bym on info.id =bym.ysbill_id and bym.ysbil_type=1
        where
        bym.del_flag=0
        and info.id in (
        select ysbillId from
        (SELECT
        y.id ysbillId

        FROM bms_ysbillcodeinfo y where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null">
            and 0 = #{isIncrement}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND y.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="clientName != null and clientName != ''">
            AND y.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND y.receiving_store like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND y.delivery_mode = #{deliveryMode}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or
            (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or
            (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(bym.ysbill_id SEPARATOR ',') ysbillId
        from bms_yscost_info yi
        left join bms_yscost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_ysexpenses_middle bym on bym.expenses_id=yi.id and bym.ysbil_type = 1
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.id=yi.client_id
        left join bms_ysbillcodeinfo ys on ys.id=bym.ysbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="isIncrement != null">
            and IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND mc.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            ym.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
            or yi.is_increment=1
            )
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND ys.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND ys.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND yi.business_time &gt;= #{signingDateStart,jdbcType=VARCHAR} AND yi.business_time &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND ym.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND ym.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND IFNULL(ym.store_name,ys.receiving_store) like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND ys.delivery_mode = #{deliveryMode}
        </if>
        group by yi.id
        ) a
        )
        order by info.relate_code,info.create_time



    </select>
    <select id="getOrderinfoDetailByIds2" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.export.BmsYsbillcodeExport">
        select
        info.relate_code as relateCode,
        info.scheduling_bill_code as schedulingBillCode,
        info.company_id as companyId,
        info.client_code as clientCode,
        info.client_name as clientName,
        info.order_date as orderDate,
        info.signing_date as signingDate,
        IFNULL(info.sku_number,0) as totalNumber,
        IFNULL(info.total_weight,0) as totalWeight,
        IFNULL(info.total_volume,0) as totalVolume,
        IFNULL(info.cargo_value,0) as cargoValue,
        IFNULL(info.total_boxes,0) as totalBoxes,
        IFNULL(info.cw_full_cases,0) as cwFullCases,
        IFNULL(info.cw_split_cases,0) as cwSplitCases,
        IFNULL(info.ld_full_cases,0) as ldFullCases,
        IFNULL(info.ld_split_cases,0) as ldSplitCases,
        IFNULL(info.lc_full_cases,0) as lcFullCases,
        IFNULL(info.lc_split_cases,0) as lcSplitCases,
        info.line_code as lineCode,
        info.line_name as lineName,
        info.delivery_mode as deliveryMode,
        info.if_autarky as ifAutarky,
        IFNULL(ro.total_mileage,0) as totalMileage,
        IFNULL(ro.store_num,0) as storeNum,
        info.if_base_stores as ifBaseStores,
        info.if_Super_base_kilometer as ifSuperBaseKilometer,
        IFNULL(info.store_distance_kilometer,0) as storeDistanceKilometer,
        IFNULL(info.near_store_km,0) as nearStoreKm,
        info.is_rejected as isRejected,
        info.reject_parties as rejectParties,
        info.car_model as carModel,
        ware.warehouse_name as warehouseName,
        info.delivery_code as deliveryCode,
        info.province_origin as provinceOrigin,
        info.originating_city as originatingCity,
        info.originating_area as originatingArea,
        info.originating_address as originatingAddress,
        info.receiving_store as receivingStore,
        info.transport_type as transportType,
        info.store_code as storeCode,
        info.receiving_store as storeName,
        info.destination_Province as destinationProvince,
        info.destination_city as destinationCity,
        info.destination_area as destinationArea,
        info.destination_address as destinationAddress,
        info.order_no as orderNo,
        info.platform_code as platformCode,
        bym.share_type as shareType,
        bym.share_amount as shareAmount,
        bym.freight,
        bym.delivery_fee as deliveryFee,
        bym.ultrafar_fee as ultrafarFee ,
        bym.superframes_fee as superframesFee,
        bym.excess_fee as excessFee,
        bym.reduce_fee as reduceFee ,
        bym.outboundsorting_fee as outboundsortingFee,
        bym.shortbarge_fee as shortbargeFee,
        bym.return_fee as returnFee,
        bym.exception_fee as exceptionFee,
        bym.other_cost1 as otherCost1,
        bym.other_cost2 as otherCost2,
        bym.other_cost3 as otherCost3,
        bym.other_cost4 as otherCost4,
        bym.other_cost5 as otherCost5,
        bym.other_cost6 as otherCost6,
        bym.other_cost7 as otherCost7,
        bym.other_cost8 as otherCost8,
        bym.other_cost9 as otherCost9,
        bym.other_cost10 as otherCost10,
        bym.other_cost11 as otherCost11,
        bym.other_cost12 as otherCost12
        from bms_ysbillcodeinfo info
        left join bms_routeinfo ro
        on info.line_code = ro.route_code
        left join mdm_warehouseinfo ware
        on ware.warehouse_code = info.warehouse_code
        left join bms_ysexpenses_middle bym on info.id =bym.ysbill_id and bym.ysbil_type=1
        where
        bym.del_flag=0
        and info.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by info.relate_code,info.create_time



    </select>

    <select id="getGoodsinfoDetailByIds" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.export.BmsYsbillcodeExport">
        SELECT
        info.relate_code AS relateCode,
        info.order_date as orderDate,
        info.store_code as storeCode,
        info.receiving_store as storeName,
        sku.sku_type_name as skuTypeName,
        sku.sku_code as skuCode,
        sku.sku_name as skuName,
        sku.specification,
        sku.unit,
        detail.temperature_type  as temperatureType,
        detail.box_type as boxType,
        IFNULL(detail.contents_number,0) as contentsNumber,
        case when IFNULL(detail.odd_boxes,0)>0 then IFNULL(detail.total_boxes,0)+1 else IFNULL(detail.total_boxes,0) end totalNumber,
        IFNULL(detail.total_boxes,0) as totalBoxes,
        IFNULL(detail.odd_boxes,0) as oddBoxes,
        IFNULL(detail.total_weight,0) as totalWeight,
        IFNULL(detail.total_volume,0) as totalVolume,
        IFNULL(detail.weight,0) as weight,
        IFNULL(detail.volume,0) as volume,
        IFNULL(detail.total_amount,0) as totalAmount
        FROM
        bms_ysbillcode_detailinfo detail
        inner JOIN  bms_ysbillcodeinfo info
        ON info.id = detail.ysbill_id
        LEFT JOIN mdm_skuinfo sku ON detail.sku_code = sku.sku_code
        where
        1=1
        and info.id  in (
        select ysbillId from
        (SELECT
        y.id ysbillId

        FROM bms_ysbillcodeinfo y where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null">
            and 0 = #{isIncrement}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND y.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="clientName != null and clientName != ''">
            AND y.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND y.receiving_store like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND y.delivery_mode = #{deliveryMode}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or
            (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or
            (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(bym.ysbill_id SEPARATOR ',') ysbillId
        from bms_yscost_info yi
        left join bms_yscost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_ysexpenses_middle bym on bym.expenses_id=yi.id and bym.ysbil_type = 1
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.id=yi.client_id
        left join bms_ysbillcodeinfo ys on ys.id=bym.ysbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="isIncrement != null">
            and IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND mc.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            ym.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
            or yi.is_increment=1
            )
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND ys.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND ys.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND yi.business_time &gt;= #{signingDateStart,jdbcType=VARCHAR} AND yi.business_time &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND ym.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND ym.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND IFNULL(ym.store_name,ys.receiving_store) like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND ys.delivery_mode = #{deliveryMode}
        </if>
        group by yi.id
        ) a
        )
        group by detail.id
        order by info.relate_code,info.create_time

    </select>
    <select id="getGoodsinfoDetailByIds2" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.export.BmsYsbillcodeExport">
        SELECT
        info.relate_code AS relateCode,
        info.order_date as orderDate,
        info.store_code as storeCode,
        info.receiving_store as storeName,
        sku.sku_type_name as skuTypeName,
        sku.sku_code as skuCode,
        sku.sku_name as skuName,
        sku.specification,
        sku.unit,
        detail.temperature_type  as temperatureType,
        detail.box_type as boxType,
        IFNULL(detail.contents_number,0) as contentsNumber,
        case when IFNULL(detail.odd_boxes,0)>0 then IFNULL(detail.total_boxes,0)+1 else IFNULL(detail.total_boxes,0) end totalNumber,
        IFNULL(detail.total_boxes,0) as totalBoxes,
        IFNULL(detail.odd_boxes,0) as oddBoxes,
        IFNULL(detail.total_weight,0) as totalWeight,
        IFNULL(detail.total_volume,0) as totalVolume,
        IFNULL(detail.weight,0) as weight,
        IFNULL(detail.volume,0) as volume,
        IFNULL(detail.total_amount,0) as totalAmount
        FROM
        bms_ysbillcode_detailinfo detail
        inner JOIN  bms_ysbillcodeinfo info
        ON info.id = detail.ysbill_id
        LEFT JOIN mdm_skuinfo sku ON detail.sku_code = sku.sku_code
        where
        1=1
        and info.id  in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by detail.id
        order by info.relate_code,info.create_time

    </select>

    <select id="getWareOrderInfoDetail" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.export.BmsYsbillcodeExport">
        select
        ext.business_code as businessCode,
        cos.business_time as businessTime,
        info.company_id as companyId,
        info.storage_service_provider as carrierName,
        car.carrier_code as carrierCode,
        info.client_code as clientCode,
        info.client_name as clientName,
        info.warehouse_code as warehouseCode,
        war.warehouse_name as warehouseName,
        info.trust as trust,
        info.weight as weight,
        info.volume as volume,
        null as cargoValue,
        info.total_boxes as totalBoxes,
        info.aqty as aqty,
        IFNULL(info.pallet_number,0) as palletNumber,
        null as fFullBoxes,
        null as oddBoxes
        ,"" as storeCode
        ,"" as storeName
        ,null as orderType
        ,null as cwPalletNumber
        ,null as lcPalletNumber
        ,null as ldPalletNumber
        ,null as orderNo
        ,null as platformCode
        ,null as destinationProvince
        ,null as destinationCity
        ,null as destinationArea
        ,mid.share_type as shareType
        ,mid.share_amount as shareAmount
        from
        bms_ysstockinfo info
        left join bms_ysexpenses_middle mid
        on info.id =mid.ysbill_id
        left join bms_yscost_info cos
        on mid.expenses_id = cos.id
        left join bms_yscost_extend ext
        on ext.expenses_id = cos.id
        left join bms_carrierinfo car
        on car.carrier_name = info.storage_service_provider
        left join mdm_warehouseinfo war
        on war.warehouse_code = info.warehouse_code
        where
        1=1
        and info.id in
        (
        select ysbillId from
        (SELECT
        y.id ysbillId
        FROM bms_ysbillcodeinfo y
        left join bms_clientinfo mc on mc.client_code=y.client_code and mc.del_flag = 0
        where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null">
            AND 0 = #{isIncrement}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND y.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND y.receiving_store like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND y.delivery_mode = #{deliveryMode}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or
        (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or
        (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(bym.ysbill_id SEPARATOR ',') ysbillId
        from bms_yscost_info yi
        left join bms_yscost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_ysexpenses_middle bym on bym.expenses_id=yi.id and bym.ysbil_type = 1
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_ysbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.id=yi.client_id
        left join bms_ysbillcodeinfo ys on ys.id=bym.ysbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="clientCode != null and clientCode.size>0 ">
            AND mc.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="or" close=")">
                ym.warehouse_code like concat('%',trim(#{warehouseCode}),'%')
            </foreach>
            or yi.is_increment=1
            )
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="isIncrement != null">
            AND IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>

        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND IFNULL(ym.signing_date,yi.business_time) &gt;= #{signingDateStart,jdbcType=VARCHAR} AND IFNULL(ym.signing_date,yi.business_time) &lt;=
            #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND ym.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND ym.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND ym.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND ym.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND IFNULL(ym.store_name,ys.receiving_store) like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND ys.delivery_mode = #{deliveryMode}
        </if>
        group by yi.id
        ) a
        )
        group by info.id
        UNION ALL
        select
        ext.business_code as businessCode,
        cos.business_time as businessTime,
        info.company_id as companyId,
        info.storage_service_provider as carrierName,
        car.carrier_code as carrierCode,
        info.client_code as clientCode,
        info.client_name as clientName,
        info.warehouse_code as warehouseCode,
        war.warehouse_name as warehouseName,
        info.sku_number as trust,
        info.total_weight as weight,
        info.total_volume as volume,
        info.cargo_value as cargoValue,
        info.total_boxes as totalBoxes,
        null as aqty,
        IFNULL(info.pallet_number,0) palletNumber,
        info.total_boxes as fFullBoxes,
        info.split_total_number as oddBoxes
        ,info.store_code as storeCode
        ,info.receiving_store as storeName
        ,info.order_type as orderType
        ,info.cw_pallet_number as cwPalletNumber
        ,info.lc_pallet_number as lcPalletNumber
        ,info.ld_pallet_number as ldPalletNumber
        ,info.order_no as orderNo
        ,info.platform_code as platformCode
        ,info.destination_Province as destinationProvince
        ,info.destination_city as destinationCity
        ,info.destination_area as destinationArea
        ,mid.share_type as shareType
        ,mid.share_amount as shareAmount
        from
        bms_ysbillcodeinfo info
        left join bms_ysbillcode_detailinfo infod
        on info.id = infod.ysbill_id
        and infod.del_flag = 0
        left join bms_ysexpenses_middle mid
        on info.id =mid.ysbill_id
        left join bms_yscost_info cos
        on mid.expenses_id = cos.id
        left join bms_yscost_extend ext
        on ext.expenses_id = cos.id
        left join bms_carrierinfo car
        on car.carrier_name = info.storage_service_provider
        left join mdm_warehouseinfo war
        on war.warehouse_code = info.warehouse_code
        where
        1=1
        and info.code_type in (2,3)
        and info.id in (
        select ysbillId from
        (SELECT
        y.id ysbillId
        FROM bms_ysbillcodeinfo y
        left join bms_clientinfo mc on mc.client_code=y.client_code and mc.del_flag = 0
        where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null">
            AND 0 = #{isIncrement}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND y.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND y.receiving_store like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND y.delivery_mode = #{deliveryMode}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or
        (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or
        (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(bym.ysbill_id SEPARATOR ',') ysbillId
        from bms_yscost_info yi
        left join bms_yscost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_ysexpenses_middle bym on bym.expenses_id=yi.id and bym.ysbil_type = 1
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_ysbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.id=yi.client_id
        left join bms_ysbillcodeinfo ys on ys.id=bym.ysbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="clientCode != null and clientCode.size>0 ">
            AND mc.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="or" close=")">
                ym.warehouse_code like concat('%',trim(#{warehouseCode}),'%')
            </foreach>
            or yi.is_increment=1
            )
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="isIncrement != null">
            AND IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>

        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND IFNULL(ym.signing_date,yi.business_time) &gt;= #{signingDateStart,jdbcType=VARCHAR} AND IFNULL(ym.signing_date,yi.business_time) &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND ym.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND ym.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND ym.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND ym.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND IFNULL(ym.store_name,ys.receiving_store) like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND ys.delivery_mode = #{deliveryMode}
        </if>
        group by yi.id
        ) a

        )
        group by info.id

    </select>

    <select id="getWareOrderInfoDetail2" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.export.BmsYsbillcodeExport">

        select
        IFNULL(ext.business_code,info.stock_code) as businessCode,
        IFNULL(cos.business_time,info.instorage_time) as businessTime,
        info.company_id as companyId,
        info.storage_service_provider as carrierName,
        car.carrier_code as carrierCode,
        info.client_code as clientCode,
        info.client_name as clientName,
        info.warehouse_code as warehouseCode,
        war.warehouse_name as warehouseName,
        info.trust as trust,
        info.weight as weight,
        info.volume as volume,
        null as cargoValue,
        info.total_boxes as totalBoxes,
        info.aqty as aqty,
        IFNULL(info.pallet_number,0) as palletNumber,
        null as fFullBoxes,
        null as oddBoxes
        ,"" as storeCode
        ,"" as storeName
        ,null as orderType
        ,null as cwPalletNumber
        ,null as lcPalletNumber
        ,null as ldPalletNumber
        ,IFNULL(ext.business_code,info.stock_code) as relateCode
        ,null as orderNo
        ,null as platformCode
        ,null as destinationProvince
        ,null as destinationCity
        ,null as destinationArea
        ,mid.share_type as shareType
        ,mid.share_amount as shareAmount
        from
        bms_ysstockinfo info
        left join bms_ysexpenses_middle mid
        on info.id =mid.ysbill_id
        left join bms_yscost_info cos
        on mid.expenses_id = cos.id
        left join bms_yscost_extend ext
        on ext.expenses_id = cos.id
        left join bms_carrierinfo car
        on car.carrier_name = info.storage_service_provider
        left join mdm_warehouseinfo war
        on war.warehouse_code = info.warehouse_code
        where info.id  in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by info.id
        UNION ALL
        select
        IFNULL(ext.business_code,info.relate_code) as businessCode,
        IFNULL(cos.business_time,info.signing_date) as businessTime,
        info.company_id as companyId,
        info.storage_service_provider as carrierName,
        car.carrier_code as carrierCode,
        info.client_code as clientCode,
        info.client_name as clientName,
        info.warehouse_code as warehouseCode,
        war.warehouse_name as warehouseName,
        info.sku_number as trust,
        info.total_weight as weight,
        info.total_volume as volume,
        info.cargo_value as cargoValue,
        info.total_boxes as totalBoxes,
        null as aqty,
        IFNULL(info.pallet_number,0) palletNumber,
        info.total_number as fFullBoxes,
        info.split_total_number as oddBoxes
        ,info.store_code as storeCode
        ,info.receiving_store as storeName
        ,info.order_type as orderType
        ,info.cw_pallet_number as cwPalletNumber
        ,info.lc_pallet_number as lcPalletNumber
        ,info.ld_pallet_number as ldPalletNumber
        ,info.relate_code as relateCode
        ,info.order_no as orderNo
        ,info.platform_code as platformCode
        ,info.destination_Province as destinationProvince
        ,info.destination_city as destinationCity
        ,info.destination_area as destinationArea
        ,mid.share_type as shareType
        ,mid.share_amount as shareAmount
        from
        bms_ysbillcodeinfo info
        left join bms_ysbillcode_detailinfo infod
        on info.id  = infod.ysbill_id
        and infod.del_flag = 0
        left join bms_ysexpenses_middle mid
        on info.id =mid.ysbill_id
        left join bms_yscost_info cos
        on mid.expenses_id = cos.id
        left join bms_yscost_extend ext
        on ext.expenses_id = cos.id
        left join bms_carrierinfo car
        on car.carrier_name = info.storage_service_provider
        left join mdm_warehouseinfo war
        on war.warehouse_code = info.warehouse_code
        where
        info.code_type in (2,3)
        and info.id  in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by info.id
    </select>

    <select id="getWareGoodsinfoDetailByIds" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.export.BmsYsbillcodeExport">


        select
        info.stock_code as businessCode,
        ext.order_date as orderDate,
        null as storeCode,
        null as storeName,
        sku.sku_code as skuCode,
        sku.sku_name as skuName,
        sku.unit,
        sku.specification,
        goods.temperature_type as temperatureType,
        sku.sku_type_name as skuTypeName,
        goods.box_type as boxType,
        info.total_boxes as totalBoxes,
        info.odd_boxes as oddBoxes,
        null as cargoValue,
        sku.weight as weight,
        sku.volume as volume,
        info.weight as totalWeight,
        info.volume  as totalVolume,
        null as totalAmount
        from
        bms_ysstockinfo info
        inner join bms_ysstock_goodsinfo goods
        on info.id = goods.ysstock_id
        left join mdm_skuinfo sku
        on sku.sku_code = goods.sku_code
        left join bms_ysexpenses_middle mid
        on info.id =mid.ysbill_id
        left join bms_yscost_info cos
        on mid.expenses_id = cos.id
        left join bms_yscost_extend ext
        on ext.expenses_id = cos.id
        where
        goods.del_flag =0
        and ext.del_flag =0
        and mid.del_flag =0
        and cos.del_flag =0
        1=1
        and info.id  in (
        select ysbillId from
        (SELECT
        y.id ysbillId
        FROM bms_ysbillcodeinfo y
        left join bms_clientinfo mc on mc.client_code=y.client_code and mc.del_flag = 0
        where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null">
            AND 0 = #{isIncrement}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND y.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND y.receiving_store like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND y.delivery_mode = #{deliveryMode}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or
        (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or
        (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(bym.ysbill_id SEPARATOR ',') ysbillId
        from bms_yscost_info yi
        left join bms_yscost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_ysexpenses_middle bym on bym.expenses_id=yi.id and bym.ysbil_type = 1
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_ysbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.id=yi.client_id
        left join bms_ysbillcodeinfo ys on ys.id=bym.ysbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="clientCode != null and clientCode.size>0 ">
            AND mc.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="or" close=")">
                ym.warehouse_code like concat('%',trim(#{warehouseCode}),'%')
            </foreach>
            or yi.is_increment=1
            )
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="isIncrement != null">
            AND IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>

        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND IFNULL(ym.signing_date,yi.business_time) &gt;= #{signingDateStart,jdbcType=VARCHAR} AND IFNULL(ym.signing_date,yi.business_time) &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND ym.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND ym.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND ym.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND ym.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND IFNULL(ym.store_name,ys.receiving_store) like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND ys.delivery_mode = #{deliveryMode}
        </if>
        ) a

        )
        group by goods.sku_code,info.id

        UNION ALL
        select
        info.relate_code as businessCode,
        info.order_date as orderDate,
        info.store_code as storeCode,
        info.receiving_store as storeName,
        sku.sku_code as skuCode,
        sku.sku_name as skuName,
        sku.unit,
        sku.specification,
        goods.temperature_type as temperatureType,
        sku.sku_type_name as skuTypeName,
        goods.box_type as boxType,
        goods.total_boxes as totalBoxes,
        goods.odd_boxes as oddBoxes,
        goods.price as cargoValue,
        goods.weight as weight,
        goods.volume as volume,
        goods.total_weight as totalWeight,
        goods.total_volume as totalVolume,
        goods.total_amount as totalAmount
        from
        bms_ysbillcodeinfo info
        inner join bms_ysbillcode_detailinfo goods
        on info.id = goods.ysbill_id
        left join mdm_skuinfo sku
        on sku.sku_code = goods.sku_code
        left join bms_ysexpenses_middle mid
        on info.id =mid.ysbill_id
        left join bms_yscost_info cos
        on mid.expenses_id = cos.id
        left join bms_yscost_extend ext
        on ext.expenses_id  = cos.id
        where
        goods.del_flag =0
        and ext.del_flag =0
        and mid.del_flag =0
        and cos.del_flag =0
        and info.code_type in (2,3)
        and info.id  in (
        select ysbillId from
        (SELECT
        y.id ysbillId
        FROM bms_ysbillcodeinfo y
        left join bms_clientinfo mc on mc.client_code=y.client_code and mc.del_flag = 0
        where y.del_flag=0 and y.cost_status!='1'
        <if test="isIncrement != null">
            AND 0 = #{isIncrement}
        </if>
        <if test="billingStatus != null and billingStatus != ''">
            and y.billing_status = #{billingStatus}
        </if>
        <if test="clientCode != null and clientCode.size>0 ">
            AND y.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND y.warehouse_code in
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND y.code_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND y.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND y.signing_date &gt;= #{signingDateStart,jdbcType=VARCHAR} AND y.signing_date &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND y.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND y.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND y.cost_status = #{costStatus}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND y.receiving_store like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND y.relate_code in
            <foreach collection="relateCode" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND y.delivery_mode = #{deliveryMode}
        </if>
        <if test="(expensesDateStart != null and expensesDateStart !='')  or (expensesDateEnd != null and expensesDateEnd!='') or
        (feeFlag != null and feeFlag!='') or (expensesCode != null and expensesCode!='') or (billDate != null and billDate!='') or
        (costDimension != null and costDimension!='') or (chargeType != null and chargeType != '')">
            AND 1=2
        </if>
        union
        SELECT
        GROUP_CONCAT(bym.ysbill_id SEPARATOR ',') ysbillId
        from bms_yscost_info yi
        left join bms_yscost_extend ym on yi.id=ym.expenses_id and ym.del_flag=0
        left join bms_ysexpenses_middle bym on bym.expenses_id=yi.id and bym.ysbil_type = 1
        left join bms_ysbillmain ybm on yi.bill_id=ybm.id and ybm.del_flag=0
        left join bms_ysbillmain ybm2 on ybm.fatherid=ybm2.id and ybm.del_flag=0
        left join bms_clientinfo mc on mc.id=yi.client_id
        left join bms_ysbillcodeinfo ys on ys.id=bym.ysbill_id and ym.code_count=1
        where yi.del_flag=0
        <if test="clientCode != null and clientCode.size>0 ">
            AND mc.client_code in
            <foreach collection="clientCode" item="clientCode" open="(" separator="," close=")">
                #{clientCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode.size>0 ">
            AND (
            <foreach collection="warehouseCode" item="warehouseCode" open="(" separator="or" close=")">
                ym.warehouse_code like concat('%',trim(#{warehouseCode}),'%')
            </foreach>
            or yi.is_increment=1
            )
        </if>
        <if test="codeType != null and codeType.size>0 ">
            AND yi.expenses_type in
            <foreach collection="codeType" item="codeType" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="isIncrement != null">
            AND IFNULL(yi.is_increment,0) = #{isIncrement}
        </if>

        <if test="signingDateStart != null and signingDateStart != '' and signingDateEnd != null and signingDateEnd!=''">
            AND IFNULL(ym.signing_date,yi.business_time) &gt;= #{signingDateStart,jdbcType=VARCHAR} AND IFNULL(ym.signing_date,yi.business_time) &lt;= #{signingDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="expensesDateStart != null and expensesDateStart != '' and expensesDateEnd != null and expensesDateEnd!=''">
            AND yi.oper_time &gt;= #{expensesDateStart,jdbcType=VARCHAR} AND yi.oper_time &lt;= #{expensesDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="insertTimeStart != null and insertTimeStart != '' and insertTimeEnd != null and insertTimeEnd!=''">
            AND ym.order_date &gt;= #{insertTimeStart,jdbcType=VARCHAR} AND ym.order_date &lt;= #{insertTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="orderDateStart != null and orderDateStart != '' and orderDateEnd != null and orderDateEnd!=''">
            AND ym.order_date &gt;= #{orderDateStart,jdbcType=VARCHAR} AND ym.order_date &lt;= #{orderDateEnd,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND yi.institution_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="chargeType != null and chargeType != ''">
            AND yi.charge_type = #{chargeType}
        </if>
        <if test="costStatus != null and costStatus != ''">
            AND 1 = #{costStatus}
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '0'.toString()">
            AND yi.bill_id is null
        </if>
        <if test="billingStatus != null and billingStatus != '' and billingStatus == '1'.toString()">
            AND yi.bill_id is not null
        </if>
        <if test="feeFlag != null and feeFlag != ''">
            AND yi.fee_flag = #{feeFlag}
        </if>
        <if test="clientName != null and clientName != ''">
            AND mc.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="clientNameList != null and clientNameList.size>0 ">
            AND mc.client_name in
            <foreach collection="clientNameList" item="clientNameList" open="(" separator="," close=")">
                #{clientNameList}
            </foreach>
        </if>
        <if test="receivingStore != null and receivingStore != ''">
            AND IFNULL(ym.store_name,ys.receiving_store) like concat('%',trim(#{receivingStore}),'%')
        </if>
        <if test="relateCode != null and relateCode.size>0 ">
            AND (
            <foreach collection="relateCode" item="relateCode" open="(" separator="or" close=")">
                ym.business_code like concat('%',trim(#{relateCode}),'%')
            </foreach>
            )
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            AND yi.expenses_code like concat('%',trim(#{expensesCode}),'%')
        </if>
        <if test="billDate != null and billDate != ''">
            AND yi.bill_date = #{billDate}
        </if>
        <if test="costDimension != null ">
            AND yi.cost_dimension = #{costDimension}
        </if>
        <if test="deliveryMode != null and deliveryMode != ''">
            AND ys.delivery_mode = #{deliveryMode}
        </if>
        ) a

        )

    </select>

    <select id="getWareGoodsinfoDetailByIds2" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.export.BmsYsbillcodeExport">


        select
        info.stock_code as businessCode,
        ext.order_date as orderDate,
        null as storeCode,
        null as storeName,
        sku.sku_code as skuCode,
        sku.sku_name as skuName,
        sku.unit,
        sku.specification,
        goods.temperature_type as temperatureType,
        sku.sku_type_name as skuTypeName,
        goods.box_type as boxType,
        info.total_boxes as totalBoxes,
        info.odd_boxes as oddBoxes,
        null as cargoValue,
        sku.weight as weight,
        sku.volume as volume,
        info.weight as totalWeight,
        info.volume as totalVolume,
        null as totalAmount
        from
        bms_ysstockinfo info
        inner join bms_ysstock_goodsinfo goods
        on info.id = goods.ysstock_id
        left join mdm_skuinfo sku
        on sku.sku_code = goods.sku_code
        left join bms_ysexpenses_middle mid
        on info.id =mid.ysbill_id
        left join bms_yscost_info cos
        on mid.expenses_id = cos.id
        left join bms_yscost_extend ext
        on ext.expenses_id = cos.id
        where
        goods.del_flag =0
        and ext.del_flag =0
        and mid.del_flag =0
        and cos.del_flag =0
        and info.id  in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        UNION ALL
        select
        info.relate_code as businessCode,
        info.order_date as orderDate,
        info.store_code as storeCode,
        info.receiving_store as storeName,
        sku.sku_code as skuCode,
        sku.sku_name as skuName,
        sku.unit,
        sku.specification,
        goods.temperature_type as temperatureType,
        sku.sku_type_name as skuTypeName,
        goods.box_type as boxType,
        goods.total_boxes as totalBoxes,
        goods.odd_boxes as oddBoxes,
        goods.price as cargoValue,
        goods.weight as weight,
        goods.volume as volume,
        goods.total_weight as totalWeight,
        goods.total_volume as totalVolume,
        goods.total_amount as totalAmount
        from
        bms_ysbillcodeinfo info
        inner join bms_ysbillcode_detailinfo goods
        on info.id = goods.ysbill_id
        left join mdm_skuinfo sku
        on sku.sku_code = goods.sku_code
        left join bms_ysexpenses_middle mid
        on info.id =mid.ysbill_id
        left join bms_yscost_info cos
        on mid.expenses_id = cos.id
        left join bms_yscost_extend ext
        on ext.expenses_id  = cos.id
        where
        1=1
        and info.code_type in (2,3)
        and goods.del_flag =0
        and ext.del_flag =0
        and mid.del_flag =0
        and cos.del_flag =0
        and info.id  in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </select>

    <select id="getPkIds" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.export.BmsYsbillcodeExport">


        select
        info.pk_id as pkId
        from
        bms_ysbillcodeinfo info

        where
        1=1
        and info.code_type in (2,3)
        <if test="ids!=null and ids.size()>0">
            and info.id  in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>

    </select>

    <select id="getIds" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.export.BmsYsbillcodeExport">

        select
        info.id
        from
        bms_ysstockinfo info
        where
        1=1
        <if test="ids!=null and ids.size()>0">
            and info.id  in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>

    </select>


    <update id="deleteOrderByCodes" parameterType="java.util.List">
        update bms_ysbillcodeinfo info
        left join bms_ysbillcode_detailinfo infod
        on info.id = infod.ysbill_id
        set info.del_flag = 1
        ,infod.del_flag =1
        where
        1=1
        <if test="relateCodes==null and schedulingBillCodes==null">
            and 1!=1
        </if>
        <if test="relateCodes!=null and relateCodes.size()>0">
            and info.relate_code in
            <foreach collection="relateCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="schedulingBillCodes!=null and schedulingBillCodes.size()>0">
            and info.scheduling_bill_code in
            <foreach collection="schedulingBillCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>


    </update>


    <update id="deleteOrderByCodesAndType" parameterType="java.util.List">
        update bms_ysbillcodeinfo info
        left join bms_ysbillcode_detailinfo infod
        on info.id = infod.ysbill_id
        set info.del_flag = 1
        ,infod.del_flag =1
        ,info.oper_code='接口对接'
        ,info.oper_by='接口对接'
        ,info.oper_time=NOW()
        where
        1=1
        <if test="relateCodes==null">
            and 1!=1
        </if>
        <if test="relateCodes!=null and relateCodes.size()>0">
            and info.relate_code in
            <foreach collection="relateCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="type != null and type != '' ">
            and info.code_type = #{type}
        </if>


    </update>



    <select id="selectImportOrderByCodeType" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        SELECT
        t1.id,
        t1.code_type as codeType,
        t1.relate_code as relateCode,
        t1.cost_status as costStatus,
        t1.billing_status as billingStatus,
        t1.order_no as orderNo,
        t1.platform_code as platformCode,
        t1.import_time as importTime,
        t1.import_code as importCode,
        t1.import_by as importBy,
        t1.order_source as orderSource,
        t3.bill_id as billId
        FROM bms_ysbillcodeinfo t1
        LEFT JOIN bms_ysexpenses_middle t2 ON t2.ysbill_id = t1.id AND t2.del_flag = 0
        LEFT JOIN bms_yscost_info t3 ON t3.id = t2.expenses_id AND t3.del_flag = 0
        WHERE t1.del_flag = 0
        and t1.code_type = #{codeType}
        and t1.relate_code in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectImportOrderByStock" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto">
        SELECT
        t1.id,
        null as codeType,
        t1.stock_code as relateCode,
        t1.cost_status as costStatus,
        t1.billing_status as billingStatus,
        null as orderNo,
        null as platformCode,
        t1.import_time as importTime,
        t1.import_code as importCode,
        t1.import_by as importBy,
        t1.order_source as orderSource,
        t3.bill_id as billId
        FROM bms_ysstockinfo t1
        LEFT JOIN bms_ysexpenses_middle t2 ON t2.ysbill_id = t1.id AND t2.del_flag = 0
        LEFT JOIN bms_yscost_info t3 ON t3.id = t2.expenses_id AND t3.del_flag = 0
        WHERE t1.del_flag = 0
        and t1.stock_code in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>




    <update id="deleteImportOrder2">
        <if test="expensesIds!=null and expensesIds.size()>0">
            UPDATE bms_ysexpenses_middle
            set del_flag=1
            ,oper_by=#{loginUserInfo.userName}
            ,oper_time = now()
            WHERE expenses_id IN
            <foreach collection="expensesIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
            UPDATE bms_yscost_info
            set del_flag=1
            ,oper_by=#{loginUserInfo.userName}
            ,oper_time = now()
            WHERE id IN
            <foreach collection="expensesIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
            UPDATE bms_yscost_extend
            SET del_flag=1
            WHERE expenses_id IN
            <foreach collection="expensesIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
        </if>
        <if test="yscodeInfoIds!=null and yscodeInfoIds.size()>0 and codeType!=null and codeType in {1,2,3} ">
            UPDATE bms_ysbillcode_detailinfo
            SET del_flag=1
            WHERE ysbill_id IN
            <foreach collection="yscodeInfoIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
        </if>
        <if test="yscodeInfoIds!=null and yscodeInfoIds.size()>0 and codeType!=null and codeType==4">
            UPDATE bms_ysstock_goodsinfo
            SET del_flag=1
            WHERE ysstock_id IN
            <foreach collection="yscodeInfoIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
        </if>
        <if test="relateCodes!=null and relateCodes.size()>0 and codeType!=null and codeType in {1,2,3} ">
            UPDATE bms_ysbillcodeinfo
            SET del_flag=1
            ,oper_by = #{loginUserInfo.userName}
            ,oper_time = now()
            WHERE code_type = #{codeType}
            AND relate_code IN
            <foreach collection="relateCodes" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
        </if>
        <if test="relateCodes!=null and relateCodes.size()>0 and codeType!=null and codeType==4">
            UPDATE bms_ysstockinfo
            SET del_flag=1
            ,oper_by = #{loginUserInfo.userName}
            ,oper_time = now()
            WHERE stock_code IN
            <foreach collection="relateCodes" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>;
        </if>
    </update>




    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeinfo" id="SelectByRelateCodeMap">
        <result property="id"    column="id"    />
        <result property="pkId"    column="pk_id"    />
        <result property="codeType"    column="code_type"    />
        <result property="relateCode"    column="relate_code"    />
        <result property="schedulingBillCode"    column="scheduling_bill_code"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="companyId"    column="company_id"    />
        <result property="networkCode"    column="network_code"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="totalNumber"    column="total_number"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="cargoValue"    column="cargo_value"    />
        <result property="orderDate"    column="order_date"    />
        <result property="signingDate"    column="signing_date"    />
        <result property="ifAutarky"    column="if_autarky"    />
        <result property="storageServiceProvider"    column="storage_service_provider"    />
        <result property="lineCode"    column="line_code"    />
        <result property="lineName"    column="line_name"    />
        <result property="ifBaseStores"    column="if_base_stores"    />
        <result property="ifSuperBaseKilometer"    column="if_Super_base_kilometer"    />
        <result property="storeDistanceKilometer"    column="store_distance_kilometer"    />
        <result property="deliveryCode"    column="delivery_code"    />
        <result property="deliveryName"    column="delivery_name"    />
        <result property="storeCode"    column="store_code"    />
        <result property="receivingStore"    column="receiving_store"    />
        <result property="provinceOrigin"    column="province_origin"    />
        <result property="originatingCity"    column="originating_City"    />
        <result property="originatingArea"    column="originating_area"    />
        <result property="originatingAddress"    column="originating_address"    />
        <result property="destinationProvince"    column="destination_Province"    />
        <result property="destinationCity"    column="destination_city"    />
        <result property="destinationArea"    column="destination_area"    />
        <result property="destinationAddress"    column="destination_address"    />
        <result property="costStatus"    column="cost_status"    />
        <result property="billingStatus"    column="billing_status"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="deliveryMode"    column="delivery_mode"    />
        <result property="isRejected"    column="is_rejected"    />
        <result property="rejectParties"    column="reject_parties"    />
        <result property="orderType"    column="order_type"    />
        <result property="nearStoreKm"    column="near_store_km"    />
        <result property="carType"    column="car_type"    />
        <result property="carModel"    column="car_model"    />
        <result property="cwFullCases"    column="cw_full_cases"    />
        <result property="cwSplitCases"    column="cw_split_cases"    />
        <result property="ldFullCases"    column="ld_full_cases"    />
        <result property="ldSplitCases"    column="ld_split_cases"    />
        <result property="lcFullCases"    column="lc_full_cases"    />
        <result property="lcSplitCases"    column="lc_split_cases"    />
        <result property="splitTotalNumber"    column="split_total_number"    />
        <result property="skuNumber"    column="sku_number"    />
        <result property="palletNumber"    column="pallet_number"    />
        <result property="transportTypeApi"    column="transport_type"    />
        <result property="skuString"    column="skuString"    />
        <result property="temperatureTypeStr"    column="temperatureTypeStr"    />
        <result property="cwPalletNumber"    column="cw_pallet_number"    />
        <result property="lcPalletNumber"    column="lc_pallet_number"    />
        <result property="ldPalletNumber"    column="ld_pallet_number"    />
        <result property="orderNo"    column="order_no"    />
        <result property="platformCode"    column="platform_code"    />
    </resultMap>

    <select id="selectByRelateCode" resultMap="SelectByRelateCodeMap">
        select id,
        code_type, relate_code, scheduling_bill_code,
        client_code, client_name, company_id, network_code,
        warehouse_code, total_boxes, total_number, total_weight,
        total_volume, cargo_value, order_date, signing_date,
        if_autarky, storage_service_provider, line_code, line_name,
        if_base_stores, if_Super_base_kilometer, store_distance_kilometer,
        delivery_code, delivery_name, store_code, receiving_store,
        province_origin, originating_City, originating_area,
        originating_address, destination_Province, destination_city,
        destination_area, destination_address, cost_status,
        billing_status, create_code, create_by, create_dept_id,
        create_time, oper_code, oper_by, oper_dept_id, oper_time,
        del_flag, delivery_mode, is_rejected, reject_parties,order_type,
        near_store_km,car_type,car_model,cw_full_cases,cw_split_cases,ld_full_cases,ld_split_cases,lc_full_cases,lc_split_cases,pallet_number, transport_type,skuString
        from bms_ysbillcodeinfo
        where 1=1 and del_flag = '0'
        <if test="codes != null and codes.size>0 ">
            AND relate_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <select id="queryMainPoById" resultMap="BaseMainPOResultMap">
        select bymi.pk_id,
               bymi.id,
               bymi.expenses_code,
               bymi.business_type,
               bymi.client_id,
               bymi.institution_id,
               bymi.expenses_type,
               bymi.cost_dimension,
               bymi.charge_type,
               bymi.fee_flag,
               bymi.quoterule_id,
               bymi.rule_name,
               bymi.remarks,
               bymi.freight,
               bymi.delivery_fee,
               bymi.ultrafar_fee,
               bymi.superframes_fee,
               bymi.excess_fee,
               bymi.reduce_fee,
               bymi.outboundsorting_fee,
               bymi.shortbarge_fee,
               bymi.return_fee,
               bymi.exception_fee,
               bymi.adjust_fee,
               bymi.adjust_remark,
               bymi.other_cost1,
               bymi.other_cost2,
               bymi.other_cost3,
               bymi.other_cost4,
               bymi.other_cost5,
               bymi.other_cost6,
               bymi.other_cost7,
               bymi.other_cost8,
               bymi.other_cost9,
               bymi.other_cost10,
               bymi.other_cost11,
               bymi.other_cost12,
               bymi.sum_fee,
               bymi.cost_attribute,
               bymi.total_weight,
               bymi.total_volume,
               bymi.total_boxes,
               bymi.total_number,
               oper_code,
               oper_by,
               bymi.oper_time,
               bymi.create_code,
               bymi.create_by,
               bymi.create_time,
               bymi.del_flag,
               bymi.bill_id,
               bymi.bill_date,
               bymi.business_time,
               bymi.over_num,
               bymi.over_sendnum,
               bymi.storage_fee_price,
               bymi.disposal_fee_price,
               bymi.other_fee_remark,
               fee_type_first,
               fee_create_ate,
               bymi.is_increment,
               bymi.order_date,
               bymi.signing_date,
               bymi.quoteruledetail_id,
               bymi.show_bill_id,
               bymi.show_bill_code,
               bymi.extra_field1,
               bymi.warehouse_code_arr,
               mc.settle_setting,
               mc.client_code,
               mc.client_name,
               mc.carrierpay_type
        from bms_yscost_main_info bymi
                 left join bms_clientinfo mc on bymi.client_id = mc.id
        where bymi.id = #{id}
    </select>

    <select id="queryMiddleByMainId" resultMap="BaseMiddleResultMap">
        select bym.id,
               bym.ysbill_id,
               bym.ysbil_type,
               bym.expenses_id,
               bym.main_expense_id,
               bym.oper_by,
               bym.oper_time,
               bym.del_flag,
               bym.share_type,
               IFNULL(bym.share_amount, 0.00)        as share_amount,
               IFNULL(bym.freight, 0.00)             as freight,
               IFNULL(bym.delivery_fee, 0.00)        as delivery_fee,
               IFNULL(bym.ultrafar_fee, 0.00)        as ultrafar_fee,
               IFNULL(bym.superframes_fee, 0.00)     as superframes_fee,
               IFNULL(bym.excess_fee, 0.00)          as excess_fee,
               IFNULL(bym.reduce_fee, 0.00)          as reduce_fee,
               IFNULL(bym.outboundsorting_fee, 0.00) as outboundsorting_fee,
               IFNULL(bym.shortbarge_fee, 0.00)      as shortbarge_fee,
               IFNULL(bym.return_fee, 0.00)          as return_fee,
               IFNULL(bym.exception_fee, 0.00)       as exception_fee,
               IFNULL(bym.other_cost1, 0.00)         as other_cost1,
               IFNULL(bym.other_cost2, 0.00)         as other_cost2,
               IFNULL(bym.other_cost3, 0.00)         as other_cost3,
               IFNULL(bym.other_cost4, 0.00)         as other_cost4,
               IFNULL(bym.other_cost5, 0.00)         as other_cost5,
               IFNULL(bym.other_cost6, 0.00)         as other_cost6,
               IFNULL(bym.other_cost7, 0.00)         as other_cost7,
               IFNULL(bym.other_cost8, 0.00)         as other_cost8,
               IFNULL(bym.other_cost9, 0.00)         as other_cost9,
               IFNULL(bym.other_cost10, 0.00)        as other_cost10,
               IFNULL(bym.other_cost11, 0.00)        as other_cost11,
               IFNULL(bym.other_cost12, 0.00)        as other_cost12,
               byc.relate_code,
               byc.total_weight,
               byc.total_volume,
               byc.total_boxes,
               byc.sku_number
        from bms_ysexpenses_middle bym
        left join bms_ysbillcodeinfo byc on bym.ysbill_id = byc.id
        where bym.del_flag = 0
          and bym.main_expense_id = #{mainId}
        group by bym.ysbill_id
    </select>


    <resultMap id="BaseMiddleResultMap" type="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="ysbill_id" property="ysbillId" jdbcType="VARCHAR" />
        <result column="ysbil_type" property="ysbilType" jdbcType="INTEGER" />
        <result column="expenses_id" property="expensesId" jdbcType="VARCHAR" />
        <result column="main_expense_id" property="mainExpenseId" jdbcType="VARCHAR" />
        <result column="oper_by" property="operBy" jdbcType="VARCHAR" />
        <result column="oper_time" property="operTime" jdbcType="TIMESTAMP" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
        <result column="share_type" property="shareType" jdbcType="INTEGER" />
        <result column="share_amount" property="shareAmount" jdbcType="DECIMAL" />
        <result column="freight" property="freight" jdbcType="DECIMAL" />
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL" />
        <result column="ultrafar_fee" property="ultrafarFee" jdbcType="DECIMAL" />
        <result column="superframes_fee" property="superframesFee" jdbcType="DECIMAL" />
        <result column="excess_fee" property="excessFee" jdbcType="DECIMAL" />
        <result column="reduce_fee" property="reduceFee" jdbcType="DECIMAL" />
        <result column="outboundsorting_fee" property="outboundsortingFee" jdbcType="DECIMAL" />
        <result column="shortbarge_fee" property="shortbargeFee" jdbcType="DECIMAL" />
        <result column="return_fee" property="returnFee" jdbcType="DECIMAL" />
        <result column="exception_fee" property="exceptionFee" jdbcType="DECIMAL" />
        <result column="other_cost1" property="otherCost1" jdbcType="DECIMAL" />
        <result column="other_cost2" property="otherCost2" jdbcType="DECIMAL" />
        <result column="other_cost3" property="otherCost3" jdbcType="DECIMAL" />
        <result column="other_cost4" property="otherCost4" jdbcType="DECIMAL" />
        <result column="other_cost5" property="otherCost5" jdbcType="DECIMAL" />
        <result column="other_cost6" property="otherCost6" jdbcType="DECIMAL" />
        <result column="other_cost7" property="otherCost7" jdbcType="DECIMAL" />
        <result column="other_cost8" property="otherCost8" jdbcType="DECIMAL" />
        <result column="other_cost9" property="otherCost9" jdbcType="DECIMAL" />
        <result column="other_cost10" property="otherCost10" jdbcType="DECIMAL" />
        <result column="other_cost11" property="otherCost11" jdbcType="DECIMAL" />
        <result column="other_cost12" property="otherCost12" jdbcType="DECIMAL" />
        <result column="relate_code" property="relateCode" jdbcType="VARCHAR" />
        <result column="total_weight" property="totalWeight" jdbcType="DECIMAL" />
        <result column="total_volume" property="totalVolume" jdbcType="DECIMAL" />
        <result column="total_boxes" property="totalBoxes" jdbcType="DECIMAL" />
        <result column="sku_number" property="totalNumber" jdbcType="DECIMAL" />
    </resultMap>

    <resultMap id="BaseMainPOResultMap" type="com.bbyb.joy.bms.domain.dto.BmsYscostMainInfo" >
        <result column="id" property="id" jdbcType="VARCHAR" />
        <result column="expenses_code" property="expensesCode" jdbcType="VARCHAR" />
        <result column="business_type" property="businessType" jdbcType="SMALLINT" />
        <result column="client_id" property="clientId" jdbcType="INTEGER" />
        <result column="institution_id" property="institutionId" jdbcType="INTEGER" />
        <result column="expenses_type" property="expensesType" jdbcType="SMALLINT" />
        <result column="cost_dimension" property="costDimension" jdbcType="SMALLINT" />
        <result column="charge_type" property="chargeType" jdbcType="SMALLINT" />
        <result column="fee_flag" property="feeFlag" jdbcType="SMALLINT" />
        <result column="quoterule_id" property="quoteruleId" jdbcType="VARCHAR" />
        <result column="rule_name" property="ruleName" jdbcType="VARCHAR" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="freight" property="freight" jdbcType="DECIMAL" />
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL" />
        <result column="ultrafar_fee" property="ultrafarFee" jdbcType="DECIMAL" />
        <result column="superframes_fee" property="superframesFee" jdbcType="DECIMAL" />
        <result column="excess_fee" property="excessFee" jdbcType="DECIMAL" />
        <result column="reduce_fee" property="reduceFee" jdbcType="DECIMAL" />
        <result column="outboundsorting_fee" property="outboundsortingFee" jdbcType="DECIMAL" />
        <result column="shortbarge_fee" property="shortbargeFee" jdbcType="DECIMAL" />
        <result column="return_fee" property="returnFee" jdbcType="DECIMAL" />
        <result column="exception_fee" property="exceptionFee" jdbcType="DECIMAL" />
        <result column="adjust_fee" property="adjustFee" jdbcType="DECIMAL" />
        <result column="adjust_remark" property="adjustRemark" jdbcType="VARCHAR" />
        <result column="other_cost1" property="otherCost1" jdbcType="DECIMAL" />
        <result column="other_cost2" property="otherCost2" jdbcType="DECIMAL" />
        <result column="other_cost3" property="otherCost3" jdbcType="DECIMAL" />
        <result column="other_cost4" property="otherCost4" jdbcType="DECIMAL" />
        <result column="other_cost5" property="otherCost5" jdbcType="DECIMAL" />
        <result column="other_cost6" property="otherCost6" jdbcType="DECIMAL" />
        <result column="other_cost7" property="otherCost7" jdbcType="DECIMAL" />
        <result column="other_cost8" property="otherCost8" jdbcType="DECIMAL" />
        <result column="other_cost9" property="otherCost9" jdbcType="DECIMAL" />
        <result column="other_cost10" property="otherCost10" jdbcType="DECIMAL" />
        <result column="other_cost11" property="otherCost11" jdbcType="DECIMAL" />
        <result column="other_cost12" property="otherCost12" jdbcType="DECIMAL" />
        <result column="sum_fee" property="sumFee" jdbcType="DECIMAL" />
        <result column="cost_attribute" property="costAttribute" jdbcType="SMALLINT" />
        <result column="total_weight" property="totalWeight" jdbcType="DECIMAL" />
        <result column="total_volume" property="totalVolume" jdbcType="DECIMAL" />
        <result column="total_boxes" property="totalBoxes" jdbcType="DECIMAL" />
        <result column="total_number" property="totalNumber" jdbcType="DECIMAL" />
        <result column="oper_code" property="operCode" jdbcType="VARCHAR" />
        <result column="oper_by" property="operBy" jdbcType="VARCHAR" />
        <result column="oper_time" property="operTime" jdbcType="TIMESTAMP" />
        <result column="create_code" property="createCode" jdbcType="VARCHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
        <result column="bill_id" property="billId" jdbcType="BIGINT" />
        <result column="bill_date" property="billDate" jdbcType="VARCHAR" />
        <result column="business_time" property="businessTime" jdbcType="TIMESTAMP" />
        <result column="over_num" property="overNum" jdbcType="DECIMAL" />
        <result column="over_sendnum" property="overSendnum" jdbcType="INTEGER" />
        <result column="storage_fee_price" property="storageFeePrice" jdbcType="DECIMAL" />
        <result column="disposal_fee_price" property="disposalFeePrice" jdbcType="DECIMAL" />
        <result column="other_fee_remark" property="otherFeeRemark" jdbcType="VARCHAR" />
        <result column="fee_type_first" property="feeTypeFirst" jdbcType="VARCHAR" />
        <result column="fee_create_ate" property="feeCreateAte" jdbcType="VARCHAR" />
        <result column="is_increment" property="isIncrement" jdbcType="TINYINT" />
        <result column="order_date" property="orderDate" jdbcType="TIMESTAMP" />
        <result column="signing_date" property="signingDate" jdbcType="TIMESTAMP" />
        <result column="quoteruledetail_id" property="quoteruledetailId" jdbcType="VARCHAR" />
        <result column="show_bill_id" property="showBillId" jdbcType="INTEGER" />
        <result column="show_bill_code" property="showBillCode" jdbcType="VARCHAR" />
        <result column="extra_field1" property="extraField1" jdbcType="DECIMAL" />
        <result column="warehouse_code_arr" property="warehouseCodeArr" jdbcType="VARCHAR" />
        <result column="settle_setting" property="settleSetting" jdbcType="SMALLINT" />
        <result column="client_code" property="clientCode" jdbcType="VARCHAR" />
        <result column="client_name" property="clientName" jdbcType="VARCHAR" />
        <result column="carrierpay_type" property="carrierpayType" jdbcType="INTEGER" />
    </resultMap>
</mapper>