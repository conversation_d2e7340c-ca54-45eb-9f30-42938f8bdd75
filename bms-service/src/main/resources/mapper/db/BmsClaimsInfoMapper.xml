<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsClaimsInfoMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsClaimsInfo" id="BmsClaimsInfoResult">
        <result property="id"    column="id"    />
        <result property="workType"    column="work_type"    />
        <result property="workOrderCode"    column="work_order_code"    />
        <result property="clientId"    column="client_id"    />
        <result property="relatedOrderCode"    column="related_order_code"    />
        <result property="billCode"    column="bill_code"    />
        <result property="billDate"    column="bill_date"    />
        <result property="primaryClassificationName"    column="primary_classification_name"    />
        <result property="secondaryClassificationName"    column="secondary_classification_name"    />
        <result property="complaintContent"    column="complaint_content"    />
        <result property="responsibleMoney"    column="responsible_money"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="complaintTime"    column="complaint_time"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="workCreateTime"    column="work_create_time"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="handleUserName"    column="handle_user_name"    />
        <result property="workCloseTime"    column="work_close_time"    />
        <result property="finishUserName"    column="finish_user_name"    />
        <result property="closeUserName"    column="close_user_name"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="claimsType"    column="claims_type"    />
        <result property="billStatus"    column="bill_status"    />
        <result property="finishTime"    column="finish_time"    />
        <result property="clientName"    column="client_name"    />
        <result property="clientCode"    column="client_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="claimsStatus"    column="claims_status"    />
        <result property="adjustRemark"    column="adjust_remark"    />
        <result property="province"    column="province"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="shopCode"    column="shop_code"    />
        <result property="shopName"    column="shop_name"    />
        <result property="area"    column="area"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="companyId"    column="company_id"    />
        <result property="oldResponsibleMoney"    column="old_responsible_money"    />
        <result property="oldBillDate"    column="old_bill_date"    />
        <result property="splitStatus"    column="split_status"    />
        <result property="showBillCode"    column="show_bill_code"    />
        <result property="showBillId"    column="show_bill_id"    />
    </resultMap>

    <sql id="selectBmsClaimsInfoVo">
        select id, work_type, work_order_code,claims_type, adjust_remark,client_id,province, related_order_code, bill_status,claims_status,bill_code, bill_date, primary_classification_name, secondary_classification_name, complaint_content, responsible_money, warehouse_id, warehouse_code, carrier_id, carrier_code, complaint_time, create_user_name, work_create_time, handle_time, handle_user_name, work_close_time, finish_user_name, close_user_name, create_code, create_by, create_time, create_dept_id, oper_code, oper_by, oper_time, oper_dept_id, del_flag,finish_time from bms_claims_info
    </sql>

    <select id="selectBmsClaimsInfoList" resultMap="BmsClaimsInfoResult">
        SELECT bci.id
        , bci.work_type
        , bci.work_order_code
        , bci.adjust_remark
        , bci.claims_type
        , bci.claims_status
        , bci.client_id
        , bci.province
        , bci.related_order_code
        , bci.bill_status
        , bill_code
        , bci.bill_date
        , bci.primary_classification_name
        , bci.secondary_classification_name
        , bci.complaint_content
        , bci.responsible_money
        , bci.warehouse_id
        , bci.warehouse_code
        , bci.carrier_id
        , bci.carrier_code
        , bci.complaint_time
        , bci.create_user_name
        , bci.work_create_time
        , bci.handle_time
        , bci.handle_user_name
        , bci.work_close_time
        , bci.finish_user_name
        , bci.close_user_name
        , bci.create_code
        , bci.finish_time
        , bci.shop_code
        , bci.shop_name
        , bci.area
        , bci.city
        , bci.district
        , c.client_name
        , c.client_code
        , w.warehouse_name
        , mc.carrier_name
        , bci.split_status
        , bci.old_responsible_money
        , bci.old_bill_date
        , ifnull(bci.show_bill_code, bci.bill_code) AS show_bill_code
        , ifnull(bci.show_bill_id, bci.bill_id) AS show_bill_id
        FROM bms_claims_info bci
        LEFT JOIN bms_clientinfo c ON bci.client_id = c.id AND c.del_flag = 0
        LEFT JOIN mdm_warehouseinfo w ON bci.warehouse_id = w.id AND w.del_flag = 0
        LEFT JOIN bms_carrierinfo mc ON bci.carrier_id = mc.id AND mc.del_flag = 0
        <where>
            bci.del_flag=0
            <if test="ids != null and ids.size>0">
                AND bci.id IN
                <foreach collection="ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="workType != null "> AND bci.work_type = #{workType}</if>
            <if test="claimsStatus != null "> AND bci.claims_status = #{claimsStatus}</if>
            <if test="billStatus != null "> AND bci.bill_status = #{billStatus}</if>
            <if test="workOrderCode != null and workOrderCode != ''"> AND bci.work_order_code = #{workOrderCode}</if>
            <if test="workOrderCodes != null and workOrderCodes != ''">
                AND bci.work_order_code in
                <foreach collection="workOrderCodes.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="clientId != null and clientId != ''"> AND bci.client_id = #{clientId}</if>
            <if test="claimsType != null"> AND bci.claims_type = #{claimsType}</if>
            <if test="relatedOrderCode != null and relatedOrderCode != ''"> AND bci.related_order_code = #{relatedOrderCode}</if>
            <if test="billCode != null and billCode != ''">
                AND bci.bill_code IN
                <foreach item="item" collection="billCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="billDate != null  and billDate != ''"> AND bci.bill_date = #{billDate}</if>
            <if test="primaryClassificationName != null and primaryClassificationName != ''"> AND bci.primary_classification_name LIKE concat('%', #{primaryClassificationName}, '%')</if>
            <if test="secondaryClassificationName != null and secondaryClassificationName != ''"> AND bci.secondary_classification_name LIKE concat('%', #{secondaryClassificationName}, '%')</if>
            <if test="complaintContent != null and complaintContent != ''"> AND bci.complaint_content = #{complaintContent}</if>
            <if test="responsibleMoney != null "> AND bci.responsible_money = #{responsibleMoney}</if>
            <if test="warehouseId != null "> AND bci.warehouse_id = #{warehouseId}</if>
            <if test="warehouseCode != null and warehouseCode != ''"> AND bci.warehouse_code = #{warehouseCode}</if>
            <if test="carrierId != null "> AND bci.carrier_id = #{carrierId}</if>
            <if test="carrierCode != null and carrierCode != ''"> AND bci.carrier_code = #{carrierCode}</if>
            <if test="complaintTime != null "> AND bci.complaint_time = #{complaintTime}</if>
            <if test="createUserName != null and createUserName != ''"> AND bci.create_user_name like concat('%', #{createUserName}, '%')</if>
            <if test="workCreateTime != null "> AND bci.work_create_time = #{workCreateTime}</if>
            <if test="workCreateTimeStart != null and workCreateTimeStart!=''"> AND bci.work_create_time &gt;= #{workCreateTimeStart} AND bci.work_create_time&lt;=#{workCreateTimeEnd}</if>
            <if test="handleTime != null "> AND bci.handle_time = #{handleTime}</if>
            <if test="handleUserName != null and handleUserName != ''"> AND bci.handle_user_name like concat('%', #{handleUserName}, '%')</if>
            <if test="workCloseTime != null "> AND bci.work_close_time = #{workCloseTime}</if>
            <if test="finishUserName != null and finishUserName != ''"> AND bci.finish_user_name like concat('%', #{finishUserName}, '%')</if>
            <if test="closeUserName != null and closeUserName != ''"> AND bci.close_user_name like concat('%', #{closeUserName}, '%')</if>
            <if test="createCode != null and createCode != ''"> AND bci.create_code = #{createCode}</if>
            <if test="createDeptId != null "> AND bci.create_dept_id = #{createDeptId}</if>
            <if test="operCode != null and operCode != ''"> AND bci.oper_code = #{operCode}</if>
            <if test="operBy != null and operBy != ''"> AND bci.oper_by = #{operBy}</if>
            <if test="operTime != null "> AND bci.oper_time = #{operTime}</if>
            <if test="operDeptId != null "> AND bci.oper_dept_id = #{operDeptId}</if>
        </where>
        ORDER BY bci.id DESC
    </select>
    
    <select id="selectBmsClaimsInfoById" parameterType="java.lang.String" resultMap="BmsClaimsInfoResult">
        <include refid="selectBmsClaimsInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBmsClaimsInfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsClaimsInfo" useGeneratedKeys="true" keyProperty="id">
        insert into bms_claims_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workType != null">work_type,</if>
            <if test="workOrderCode != null">work_order_code,</if>
            <if test="clientId != null">client_id,</if>
            <if test="relatedOrderCode != null">related_order_code,</if>
            <if test="billCode != null">bill_code,</if>
            <if test="billDate != null">bill_date,</if>
            <if test="primaryClassificationName != null">primary_classification_name,</if>
            <if test="secondaryClassificationName != null">secondary_classification_name,</if>
            <if test="complaintContent != null">complaint_content,</if>
            <if test="responsibleMoney != null">responsible_money,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="carrierId != null">carrier_id,</if>
            <if test="carrierCode != null">carrier_code,</if>
            <if test="complaintTime != null">complaint_time,</if>
            <if test="createUserName != null">create_user_name,</if>
            <if test="workCreateTime != null">work_create_time,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="handleUserName != null">handle_user_name,</if>
            <if test="workCloseTime != null">work_close_time,</if>
            <if test="finishUserName != null">finish_user_name,</if>
            <if test="closeUserName != null">close_user_name,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workType != null">#{workType},</if>
            <if test="workOrderCode != null">#{workOrderCode},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="relatedOrderCode != null">#{relatedOrderCode},</if>
            <if test="billCode != null">#{billCode},</if>
            <if test="billDate != null">#{billDate},</if>
            <if test="primaryClassification != null">#{primaryClassification},</if>
            <if test="primaryClassificationName != null">#{primaryClassificationName},</if>
            <if test="secondaryClassification != null">#{secondaryClassification},</if>
            <if test="secondaryClassificationName != null">#{secondaryClassificationName},</if>
            <if test="complaintContent != null">#{complaintContent},</if>
            <if test="responsibleMoney != null">#{responsibleMoney},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="carrierId != null">#{carrierId},</if>
            <if test="carrierCode != null">#{carrierCode},</if>
            <if test="complaintTime != null">#{complaintTime},</if>
            <if test="createuserName != null">#{createuserName},</if>
            <if test="workCreateTime != null">#{workCreateTime},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="handleUserId != null">#{handleUserId},</if>
            <if test="handleUserName != null">#{handleUserName},</if>
            <if test="workCloseTime != null">#{workCloseTime},</if>
            <if test="finishUserId != null">#{finishUserId},</if>
            <if test="finishUserName != null">#{finishUserName},</if>
            <if test="closeUserId != null">#{closeUserId},</if>
            <if test="closeUserName != null">#{closeUserName},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateBmsClaimsInfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsClaimsInfo">
        update bms_claims_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="workType != null">work_type = #{workType},</if>
            <if test="workOrderCode != null">work_order_code = #{workOrderCode},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="relatedOrderCode != null">related_order_code = #{relatedOrderCode},</if>
            <if test="billCode != null">bill_code = #{billCode},</if>
            <if test="billDate != null">bill_date = #{billDate},</if>
            <if test="primaryClassificationName != null">primary_classification_name = #{primaryClassificationName},</if>
            <if test="secondaryClassificationName != null">secondary_classification_name = #{secondaryClassificationName},</if>
            <if test="complaintContent != null">complaint_content = #{complaintContent},</if>
            <if test="responsibleMoney != null">responsible_money = #{responsibleMoney},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="carrierId != null">carrier_id = #{carrierId},</if>
            <if test="carrierCode != null">carrier_code = #{carrierCode},</if>
            <if test="complaintTime != null">complaint_time = #{complaintTime},</if>
            <if test="createUserName != null">create_user_name = #{createUserName},</if>
            <if test="workCreateTime != null">work_create_time = #{workCreateTime},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleUserName != null">handle_user_name = #{handleUserName},</if>
            <if test="workCloseTime != null">work_close_time = #{workCloseTime},</if>
            <if test="finishUserName != null">finish_user_name = #{finishUserName},</if>
            <if test="closeUserName != null">close_user_name = #{closeUserName},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsClaimsInfoById" parameterType="java.lang.String">
        delete from bms_claims_info where id = #{id}
    </delete>

    <delete id="deleteBmsClaimsInfoByIds">
        delete from bms_claims_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsClaimsInfoStatusByIds">
        update bms_claims_info set status = #{status} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectBmsClaimsByClientIds" parameterType="java.util.Map" resultMap="BmsClaimsInfoResult">
        select t1.id, t1.work_type, t1.work_order_code,t1.claims_type, t1.adjust_remark,t1.client_id,t1.province, t1.related_order_code, t1.bill_status,t1.claims_status,t1.bill_code, t1.bill_date, t1.primary_classification_name, t1.secondary_classification_name, t1.complaint_content, t1.responsible_money, t1.warehouse_id, t1.warehouse_code, t1.carrier_id, t1.carrier_code, t1.complaint_time, t1.create_user_name, t1.work_create_time, t1.handle_time, t1.handle_user_name, t1.work_close_time, t1.finish_user_name, t1.close_user_name, t1.create_code, t1.create_by, t1.create_time, t1.create_dept_id, t1.oper_code, t1.oper_by, t1.oper_time, t1.oper_dept_id, t1.del_flag,t1.finish_time
        ,t2.company_id
        from bms_claims_info t1
        left join mdm_warehouseinfo t2
        on t1.warehouse_id=t2.id
        where t1.del_flag=0
        and t1.bill_status=0
        and ifnull(t1.responsible_money,0)!=0
        and t1.work_type=#{workType}
        and t1.bill_date=#{billDate}
        <if test="companyId!=null">
            and t2.company_id=#{companyId}
        </if>
        <if test="claimsType!=null">
            and t1.claims_type=#{claimsType}
        </if>
        <if test="claimsStatus!=null and claimsStatus!=''">
            and t1.claims_status=#{claimsStatus}
        </if>
        <if test="carrierId!=null">
            and t1.carrier_id=#{carrierId}
        </if>
        and t1.client_id in
        <foreach item="id" collection="clientIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectBmsClaimsByClientIds2" parameterType="java.util.Map" resultMap="BmsClaimsInfoResult">
        select t1.id, t1.work_type, t1.work_order_code,t1.claims_type, t1.adjust_remark, t1.client_id, t1.shop_code, t1.shop_name, t1.area, t1.province, t1.city, t1.district, t1.related_order_code, t1.bill_status,t1.claims_status,t1.bill_code, t1.bill_date, t1.primary_classification_name, t1.secondary_classification_name, t1.complaint_content, t1.responsible_money, t1.warehouse_id, t1.warehouse_code, t1.carrier_id, t1.carrier_code, t1.complaint_time, t1.create_user_name, t1.work_create_time, t1.handle_time, t1.handle_user_name, t1.work_close_time, t1.finish_user_name, t1.close_user_name, t1.create_code, t1.create_by, t1.create_time, t1.create_dept_id, t1.oper_code, t1.oper_by, t1.oper_time, t1.oper_dept_id, t1.del_flag,t1.finish_time
        ,t2.company_id
        ,t1.old_bill_date
        ,t1.old_responsible_money
        from bms_claims_info t1
        left join mdm_warehouseinfo t2
        on t1.warehouse_id=t2.id
        where t1.del_flag=0
        and t1.bill_status=0
        and ifnull(t1.responsible_money,0)!=0
        and t1.work_type=#{workType}
        <if test="billDate!='' and billDate!=null">
            and t1.bill_date &lt;= #{billDate}
        </if>
        <if test="companyId!=null">
            and t2.company_id=#{companyId}
        </if>
        <if test="claimsType!=null">
            and t1.claims_type=#{claimsType}
        </if>
        <if test="claimsStatus!=null and claimsStatus!=''">
            and t1.claims_status=#{claimsStatus}
        </if>
        <if test="carrierId!=null">
            and t1.carrier_id=#{carrierId}
        </if>
        and t1.client_id in
        <foreach item="id" collection="clientIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by t1.id
    </select>

    <update id="batchUpdateBmsClaimsInfo">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";" >
            UPDATE bms_claims_info
            <set>
                <if test="item.workOrderCode != null and item.workOrderCode!=''">work_order_code = #{item.workOrderCode},</if>
                <if test="item.adjustRemark != null and item.adjustRemark!=''">adjust_remark = #{item.adjustRemark},</if>
                <if test="item.billStatus != null">bill_status = #{item.billStatus},</if>
                <if test="item.billId != null">bill_id = #{item.billId},</if>
                <if test="item.billCode != null and item.billCode!=''">bill_code = #{item.billCode},</if>
                <if test="item.billDate != null and item.billDate!=''">bill_date = #{item.billDate},</if>
                <if test="item.responsibleMoney != null">responsible_money = #{item.responsibleMoney},</if>
                <if test="item.complaintTime != null">complaint_time = #{item.complaintTime},</if>
                <if test="item.createUserName != null">create_user_name = #{item.createUserName},</if>
                <if test="item.workCreateTime != null">work_create_time = #{item.workCreateTime},</if>
                <if test="item.handleTime != null">handle_time = #{item.handleTime},</if>
                <if test="item.operCode != null">oper_code = #{item.operCode},</if>
                <if test="item.operBy != null">oper_by = #{item.operBy},</if>
                <if test="item.operTime != null">oper_time = #{item.operTime},</if>
                <if test="item.operDeptId != null">oper_dept_id = #{item.operDeptId},</if>
                <if test="item.splitStatus != null">split_status = #{item.splitStatus},</if>
                <if test="item.oldResponsibleMoney != null">old_responsible_money = #{item.oldResponsibleMoney},</if>
                <if test="item.oldBillDate != null and item.oldBillDate!=''">old_bill_date = #{item.oldBillDate},</if>
                <if test="item.showBillCode != null">show_bill_code = #{item.showBillCode},</if>
                <if test="item.showBillId != null">show_bill_id = #{item.showBillId},</if>
                <if test="item.delFlag != null">del_flag = #{item.delFlag},</if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <update id="cancelBillClaims">
        UPDATE bms_claims_info
        SET bill_status=0
        ,bill_id=null
        ,bill_code=null
        ,show_bill_code=null
        ,show_bill_id=null
        ,oper_code=#{operCode}
        ,oper_by=#{operBy}
        ,oper_time=NOW()
        ,oper_dept_id=#{operDeptId}
        WHERE del_flag=0
        AND work_type=#{workType}
        AND bill_code IN
        <foreach item="item" collection="billCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <insert id="batchInsetBmsClaimsInfo" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";" >
            insert into bms_claims_info
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.workType != null">work_type,</if>
                <if test="item.workOrderCode != null">work_order_code,</if>
                <if test="item.claimsType != null">claims_type,</if>
                <if test="item.claimsStatus != null">claims_status,</if>
                <if test="item.province != null and item.province!=''">province,</if>
                <if test="item.city != null and item.city!=''">city,</if>
                <if test="item.district != null and item.district!=''">district,</if>
                <if test="item.area != null and item.area!=''">area,</if>
                <if test="item.shopCode != null and item.shopCode!=''">shop_code,</if>
                <if test="item.shopName != null and item.shopName!=''">shop_name,</if>
                <if test="item.clientId != null">client_id,</if>
                <if test="item.relatedOrderCode != null and item.relatedOrderCode!=''">related_order_code,</if>
                <if test="item.billStatus != null">bill_status,</if>
                <if test="item.billCode != null">bill_code,</if>
                <if test="item.billDate != null">bill_date,</if>
                <if test="item.primaryClassificationName != null">primary_classification_name,</if>
                <if test="item.secondaryClassificationName != null">secondary_classification_name,</if>
                <if test="item.complaintContent != null">complaint_content,</if>
                <if test="item.responsibleMoney != null">responsible_money,</if>
                <if test="item.warehouseId != null">warehouse_id,</if>
                <if test="item.warehouseCode != null">warehouse_code,</if>
                <if test="item.carrierId != null">carrier_id,</if>
                <if test="item.carrierCode != null">carrier_code,</if>
                <if test="item.complaintTime != null">complaint_time,</if>
                <if test="item.createUserName != null">create_user_name,</if>
                <if test="item.workCreateTime != null">work_create_time,</if>
                <if test="item.handleTime != null">handle_time,</if>
                <if test="item.handleUserName != null">handle_user_name,</if>
                <if test="item.workCloseTime != null">work_close_time,</if>
                <if test="item.finishTime != null">finish_time,</if>
                <if test="item.finishUserName != null">finish_user_name,</if>
                <if test="item.closeUserName != null">close_user_name,</if>
                <if test="item.createCode != null">create_code,</if>
                <if test="item.createBy != null">create_by,</if>
                <if test="item.createTime != null">create_time,</if>
                <if test="item.createDeptId != null">create_dept_id,</if>
                <if test="item.operCode != null">oper_code,</if>
                <if test="item.operBy != null">oper_by,</if>
                <if test="item.operTime != null">oper_time,</if>
                <if test="item.operDeptId != null">oper_dept_id,</if>
                <if test="item.splitStatus != null">split_status,</if>
                <if test="item.oldResponsibleMoney != null">old_responsible_money,</if>
                <if test="item.oldBillDate != null and item.oldBillDate!=''">old_bill_date,</if>
                <if test="item.showBillId != null">show_bill_id,</if>
                <if test="item.showBillCode != null">show_bill_code,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.workType != null">#{item.workType},</if>
                <if test="item.workOrderCode != null">#{item.workOrderCode},</if>
                <if test="item.claimsType != null">#{item.claimsType},</if>
                <if test="item.claimsStatus != null">#{item.claimsStatus},</if>
                <if test="item.province != null and item.province!=''">#{item.province},</if>
                <if test="item.city != null and item.city!=''">#{item.city},</if>
                <if test="item.district != null and item.district!=''">#{item.district},</if>
                <if test="item.area != null and item.area!=''">#{item.area},</if>
                <if test="item.shopCode != null and item.shopCode!=''">#{item.shopCode},</if>
                <if test="item.shopName != null and item.shopName!=''">#{item.shopName},</if>
                <if test="item.clientId != null">#{item.clientId},</if>
                <if test="item.relatedOrderCode != null and item.relatedOrderCode!=''">#{item.relatedOrderCode},</if>
                <if test="item.billStatus != null">#{item.billStatus},</if>
                <if test="item.billCode != null">#{item.billCode},</if>
                <if test="item.billDate != null">#{item.billDate},</if>
                <if test="item.primaryClassificationName != null">#{item.primaryClassificationName},</if>
                <if test="item.secondaryClassificationName != null">#{item.secondaryClassificationName},</if>
                <if test="item.complaintContent != null">#{item.complaintContent},</if>
                <if test="item.responsibleMoney != null">#{item.responsibleMoney},</if>
                <if test="item.warehouseId != null">#{item.warehouseId},</if>
                <if test="item.warehouseCode != null">#{item.warehouseCode},</if>
                <if test="item.carrierId != null">#{item.carrierId},</if>
                <if test="item.carrierCode != null">#{item.carrierCode},</if>
                <if test="item.complaintTime != null">#{item.complaintTime},</if>
                <if test="item.createUserName != null">#{item.createUserName},</if>
                <if test="item.workCreateTime != null">#{item.workCreateTime},</if>
                <if test="item.handleTime != null">#{item.handleTime},</if>
                <if test="item.handleUserName != null">#{item.handleUserName},</if>
                <if test="item.workCloseTime != null">#{item.workCloseTime},</if>
                <if test="item.finishTime != null">#{item.finishTime},</if>
                <if test="item.finishUserName != null">#{item.finishUserName},</if>
                <if test="item.closeUserName != null">#{item.closeUserName},</if>
                <if test="item.createCode != null">#{item.createCode},</if>
                <if test="item.createBy != null">#{item.createBy},</if>
                <if test="item.createTime != null">#{item.createTime},</if>
                <if test="item.createDeptId != null">#{item.createDeptId},</if>
                <if test="item.operCode != null">#{item.operCode},</if>
                <if test="item.operBy != null">#{item.operBy},</if>
                <if test="item.operTime != null">#{item.operTime},</if>
                <if test="item.operDeptId != null">#{item.operDeptId},</if>
                <if test="item.splitStatus != null">#{item.splitStatus},</if>
                <if test="item.oldResponsibleMoney != null">#{item.oldResponsibleMoney},</if>
                <if test="item.oldBillDate != null and item.oldBillDate!=''">#{item.oldBillDate},</if>
                <if test="item.showBillId != null">#{item.showBillId},</if>
                <if test="item.showBillCode != null">#{item.showBillCode},</if>
            </trim>
        </foreach>
    </insert>
</mapper>