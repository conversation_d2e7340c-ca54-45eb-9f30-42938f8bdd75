<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYsstockinfoMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYsstockinfo" id="BmsYsstockinfoResult">
        <result property="id"    column="id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="stockCode"    column="stock_code"    />
        <result property="ysbillId"    column="ysbill_id"    />
        <result property="skuCode"    column="sku_code"    />
        <result property="skuName"    column="sku_name"    />
        <result property="instorageTime"    column="instorage_time"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="oddBoxes"    column="odd_boxes"    />
        <result property="boxType"    column="box_type"    />
        <result property="temperatureType"    column="temperature_type"    />
        <result property="trust"    column="trust"    />
        <result property="weight"    column="weight"    />
        <result property="volume"    column="volume"    />
        <result property="warehouseArea"    column="warehouse_area"    />
        <result property="remark"    column="remark"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="costStatus"    column="cost_status"    />
        <result property="billingStatus"    column="billing_status"    />
        <result property="companyId"    column="company_id"    />
        <result property="clientName"    column="client_name"    />
        <result property="aqty"    column="aqty"    />
        <result property="palletNumber"    column="pallet_number"    />
        <result property="ifAutarky"    column="if_autarky"    />
        <result property="storageServiceProvider"    column="storage_service_provider"    />
        <result property="palletRuler"    column="pallet_ruler"    />
        <result property="unit"    column="unit"    />
    </resultMap>

    <sql id="selectBmsYsstockinfoVo">
        select id, client_code, warehouse_code, stock_code, ysbill_id, sku_code, sku_name, instorage_time, total_boxes, odd_boxes, box_type, temperature_type, trust, weight, volume, warehouse_area, remark, oper_dept_id, oper_code, oper_by, oper_time, del_flag, cost_status, billing_status, company_id, client_name,aqty,pallet_number,if_autarky,storage_service_provider,pallet_ruler,unit from bms_ysstockinfo
    </sql>

    <select id="selectBmsYsstockinfoList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsstockinfo" resultMap="BmsYsstockinfoResult">
        <include refid="selectBmsYsstockinfoVo"/>
        <where>  
            <if test="clientCode != null  and clientCode != ''"> and client_code = #{clientCode}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="stockCode != null  and stockCode != ''"> and stock_code = #{stockCode}</if>
            <if test="ysbillId != null "> and ysbill_id = #{ysbillId}</if>
            <if test="skuCode != null  and skuCode != ''"> and sku_code = #{skuCode}</if>
            <if test="skuName != null  and skuName != ''"> and sku_name like concat('%', #{skuName}, '%')</if>
            <if test="instorageTime != null "> and instorage_time = #{instorageTime}</if>
            <if test="totalBoxes != null "> and total_boxes = #{totalBoxes}</if>
            <if test="oddBoxes != null "> and odd_boxes = #{oddBoxes}</if>
            <if test="boxType != null  and boxType != ''"> and box_type = #{boxType}</if>
            <if test="temperatureType != null  and temperatureType != ''"> and temperature_type = #{temperatureType}</if>
            <if test="trust != null "> and trust = #{trust}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="volume != null "> and volume = #{volume}</if>
            <if test="warehouseArea != null "> and warehouse_area = #{warehouseArea}</if>
            <if test="operDeptId != null "> and oper_dept_id = #{operDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
            <if test="costStatus != null  and costStatus != ''"> and cost_status = #{costStatus}</if>
            <if test="billingStatus != null  and billingStatus != ''"> and billing_status = #{billingStatus}</if>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="delFlag != null and delFlag != ''">and del_flag = #{delFlag}</if>
        </where>
    </select>
    
    <select id="selectBmsYsstockinfoById" parameterType="java.lang.String" resultMap="BmsYsstockinfoResult">
        <include refid="selectBmsYsstockinfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBmsYsstockinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsstockinfo" useGeneratedKeys="true" keyProperty="id">
        insert into bms_ysstockinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="clientCode != null">client_code,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="stockCode != null">stock_code,</if>
            <if test="ysbillId != null">ysbill_id,</if>
            <if test="skuCode != null">sku_code,</if>
            <if test="skuName != null">sku_name,</if>
            <if test="instorageTime != null">instorage_time,</if>
            <if test="totalBoxes != null">total_boxes,</if>
            <if test="oddBoxes != null">odd_boxes,</if>
            <if test="boxType != null">box_type,</if>
            <if test="temperatureType != null">temperature_type,</if>
            <if test="trust != null">trust,</if>
            <if test="weight != null">weight,</if>
            <if test="volume != null">volume,</if>
            <if test="warehouseArea != null">warehouse_area,</if>
            <if test="remark != null">remark,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="costStatus != null">cost_status,</if>
            <if test="billingStatus != null">billing_status,</if>
            <if test="companyId != null">company_id,</if>
            <if test="clientName != null">client_name,</if>
            <if test="aqty != null">aqty,</if>
            <if test="palletNumber != null">pallet_number,</if>
            <if test="ifAutarky != null">if_autarky,</if>
            <if test="storageServiceProvider != null">storage_service_provider,</if>
            <if test="palletRuler != null">pallet_ruler,</if>
            <if test="unit != null">unit,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="stockCode != null">#{stockCode},</if>
            <if test="ysbillId != null">#{ysbillId},</if>
            <if test="skuCode != null">#{skuCode},</if>
            <if test="skuName != null">#{skuName},</if>
            <if test="instorageTime != null">#{instorageTime},</if>
            <if test="totalBoxes != null">#{totalBoxes},</if>
            <if test="oddBoxes != null">#{oddBoxes},</if>
            <if test="boxType != null">#{boxType},</if>
            <if test="temperatureType != null">#{temperatureType},</if>
            <if test="trust != null">#{trust},</if>
            <if test="weight != null">#{weight},</if>
            <if test="volume != null">#{volume},</if>
            <if test="warehouseArea != null">#{warehouseArea},</if>
            <if test="remark != null">#{remark},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="costStatus != null">#{costStatus},</if>
            <if test="billingStatus != null">#{billingStatus},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="aqty != null">#{aqty},</if>
            <if test="palletNumber != null">#{palletNumber},</if>
            <if test="ifAutarky != null">#{ifAutarky},</if>
            <if test="storageServiceProvider != null">#{storageServiceProvider},</if>
            <if test="palletRuler != null">#{palletRuler},</if>
            <if test="unit != null">#{unit},</if>
         </trim>
    </insert>

    <update id="updateBmsYsstockinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsstockinfo">
        update bms_ysstockinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="stockCode != null">stock_code = #{stockCode},</if>
            <if test="ysbillId != null">ysbill_id = #{ysbillId},</if>
            <if test="skuCode != null">sku_code = #{skuCode},</if>
            <if test="skuName != null">sku_name = #{skuName},</if>
            <if test="instorageTime != null">instorage_time = #{instorageTime},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="oddBoxes != null">odd_boxes = #{oddBoxes},</if>
            <if test="boxType != null">box_type = #{boxType},</if>
            <if test="temperatureType != null">temperature_type = #{temperatureType},</if>
            <if test="trust != null">trust = #{trust},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="volume != null">volume = #{volume},</if>
            <if test="warehouseArea != null">warehouse_area = #{warehouseArea},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="costStatus != null">cost_status = #{costStatus},</if>
            <if test="billingStatus != null">billing_status = #{billingStatus},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="aqty != null">aqty = #{aqty},</if>
            <if test="palletNumber != null">pallet_number = #{palletNumber},</if>
            <if test="ifAutarky != null">if_autarky = #{ifAutarky},</if>
            <if test="storageServiceProvider != null">storage_service_provider = #{storageServiceProvider},</if>
            <if test="palletRuler != null">pallet_ruler = #{palletRuler},</if>
            <if test="unit != null">unit = #{unit},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYsstockinfoById" parameterType="java.lang.String">
        delete from bms_ysstockinfo where id = #{id}
    </delete>

    <delete id="deleteBmsYsstockinfoByIds">
        delete from bms_ysstockinfo where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYsstockinfoStatusByIds">
        update bms_ysstockinfo set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchSave" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into bms_ysstockinfo
        (id,company_id,client_code,client_name, warehouse_code, stock_code, ysbill_id, sku_code, sku_name, instorage_time, total_boxes, odd_boxes, box_type, temperature_type,
        trust, weight, volume, warehouse_area, remark, oper_dept_id, oper_code, oper_by, oper_time, del_flag, cost_status, billing_status,aqty,pallet_number,if_autarky,storage_service_provider,
        pallet_ruler,unit,import_time,import_code,import_by,order_source )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},
            #{item.companyId},
            #{item.clientCode},
            #{item.clientName},
            #{item.warehouseCode},
            #{item.stockCode},
            #{item.ysbillId},
            #{item.skuCode},
            #{item.skuName},
            #{item.instorageTime},
            #{item.totalBoxes},
            #{item.oddBoxes},
            #{item.boxType},
            #{item.temperatureType},
            #{item.trust},
            #{item.weight},
            #{item.volume},
            #{item.warehouseArea},
            #{item.remark},
            #{item.operDeptId},
            #{item.operCode},
            #{item.operBy},
            #{item.operTime},
            #{item.delFlag},
            #{item.costStatus},
            #{item.billingStatus},
            #{item.aqty},
            #{item.palletNumber},
            #{item.ifAutarky},
            #{item.storageServiceProvider},
            #{item.palletRuler},
            #{item.unit},
            #{item.importTime},
            #{item.importCode},
            #{item.importBy},
            #{item.orderSource})
        </foreach>
    </insert>

    <select id="selectByCode" resultMap="BmsYsstockinfoResult">
        <include refid="selectBmsYsstockinfoVo"/>
        where del_flag = 0
        <if test="codes != null and codes.size>0 ">
           and stock_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <select id="approveOrNot" resultMap="BmsYsstockinfoResult">
        <include refid="selectBmsYsstockinfoVo"/>
        where
        id = #{codeInfoId}
    </select>

    <update id="deleteStockInfoByCodes" parameterType="java.util.List">
        update bms_ysstockinfo st
        left join bms_ysstock_goodsinfo goods
        on goods.ysstock_id = st.id
        set st.del_flag =1 ,goods.del_flag = 1
        where
        1=1  and st.stock_code  in
        <foreach collection="codes" item="codes" open="(" separator="," close=")">
            #{codes}
        </foreach>
    </update>
</mapper>