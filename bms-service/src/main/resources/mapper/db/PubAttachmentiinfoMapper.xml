<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubAttachmentiinfoMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.PubAttachmentiinfo" id="PubAttachmentiinfoResult">
        <result property="id"    column="id"    />
        <result property="attachmentType"    column="attachment_type"    />
        <result property="relationIde"    column="relation_ide"    />
        <result property="attachmentName"    column="attachment_name"    />
        <result property="attachmentPath"    column="attachment_path"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>


    <select id="selectPubAttachmentiinfoList"  resultMap="PubAttachmentiinfoResult">
        select id, attachment_type, relation_ide, attachment_name, attachment_path,
                create_code, create_by, create_time, create_dept_id, oper_dept_id,
               oper_code, oper_by, oper_time, del_flag
        from pub_attachmentiinfo
        where del_flag = '0'and  relation_ide = #{relationIdes}
        <if test="attachmentType != null "> and attachment_type = #{attachmentType}</if>
    </select>

        
    <insert id="insertPubAttachmentiinfo"   parameterType="java.util.List">
        insert into pub_attachmentiinfo
        (
        attachment_type,
        relation_ide,
        attachment_name,
        attachment_path,
        create_code,
        create_by,
        create_time,
        create_dept_id,
        oper_dept_id,
        oper_code,
        oper_by,
        oper_time,
        del_flag
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.attachmentType},
            #{item.relationIde},
            #{item.attachmentName},
            #{item.attachmentPath},
            #{item.createCode},
            #{item.createBy},
            #{item.createTime},
            #{item.createDeptId},
            #{item.operDeptId},
            #{item.operCode},
            #{item.operBy},
            #{item.operTime},
            '0'
            )
        </foreach>

    </insert>


    <update id="deletePubAttachmentiinfoByRelationIde">
        update pub_attachmentiinfo set del_flag = '1' where relation_ide = #{relationIde}
        <if test="attachmentType != null "> and attachment_type = #{attachmentType}</if>
    </update>



    <update id="updatePubAttachmentiinfoByIds" >
        update pub_attachmentiinfo set del_flag = '1'
        where relation_ide in
        <foreach item="relationIdes" collection="relationIdes" open="(" separator="," close=")">
            #{relationIdes}
        </foreach>
        <if test="attachmentType != null "> and attachment_type = #{attachmentType}</if>
    </update>

    <select id="selectDownloadUrl" resultType="java.lang.String">
        select attachment_path
        from pub_attachmentiinfo
        where del_flag = '0'and  relation_ide = #{relationIdes}
        <if test="attachmentType != null "> and attachment_type = #{attachmentType}</if>
    </select>

    <select id="selectPubAttachmentiinfoListByRelIds" resultMap="PubAttachmentiinfoResult">
        select id, attachment_type, relation_ide, attachment_name, attachment_path,
        create_code, create_by, create_time, create_dept_id, oper_dept_id,
        oper_code, oper_by, oper_time, del_flag
        from pub_attachmentiinfo
        where del_flag = '0'
        and relation_ide in
        <foreach collection="relationIdes" item="relationId" open="(" separator="," close=")">
            #{relationId}
        </foreach>
        <if test="attachmentType != null "> and attachment_type = #{attachmentType}</if>
    </select>
</mapper>