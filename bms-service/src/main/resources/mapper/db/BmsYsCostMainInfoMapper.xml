<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYsCostMainInfoMapper">

    <update id="batchUpdateById" parameterType="java.util.List" >
        <foreach collection="list" index="index" item="item" separator=";" >
            UPDATE bms_yscost_main_info
            <set>
                <if test="item.pkId !=null " >
                    pk_id=#{item.pkId,jdbcType=BIGINT},
                </if>
                <if test=" item.expensesCode !=null " >
                    expenses_code=#{item.expensesCode,jdbcType=VARCHAR},
                </if>
                <if test=" item.businessType !=null " >
                    business_type=#{item.businessType,jdbcType=SMALLINT},
                </if>
                <if test="item.clientId !=null " >
                    client_id=#{item.clientId,jdbcType=INTEGER},
                </if>
                <if test=" item.institutionId !=null " >
                    institution_id=#{item.institutionId,jdbcType=INTEGER},
                </if>
                <if test="item.expensesType !=null " >
                    expenses_type=#{item.expensesType,jdbcType=SMALLINT},
                </if>
                <if test=" item.costDimension !=null " >
                    cost_dimension=#{item.costDimension,jdbcType=SMALLINT},
                </if>
                <if test="item.chargeType !=null " >
                    charge_type=#{item.chargeType,jdbcType=SMALLINT},
                </if>
                <if test=" item.feeFlag !=null " >
                    fee_flag=#{item.feeFlag,jdbcType=SMALLINT},
                </if>
                <if test=" item.quoteruleId !=null " >
                    quoterule_id=#{item.quoteruleId,jdbcType=VARCHAR},
                </if>
                <if test="item.ruleName !=null " >
                    rule_name=#{item.ruleName,jdbcType=VARCHAR},
                </if>
                <if test=" item.remarks !=null " >
                    remarks=#{item.remarks,jdbcType=VARCHAR},
                </if>
                <if test=" item.freight !=null " >
                    freight=#{item.freight,jdbcType=DECIMAL},
                </if>
                <if test=" item.deliveryFee !=null " >
                    delivery_fee=#{item.deliveryFee,jdbcType=DECIMAL},
                </if>
                <if test=" item.ultrafarFee !=null " >
                    ultrafar_fee=#{item.ultrafarFee,jdbcType=DECIMAL},
                </if>
                <if test=" item.superframesFee !=null " >
                    superframes_fee=#{item.superframesFee,jdbcType=DECIMAL},
                </if>
                <if test="item.excessFee !=null " >
                    excess_fee=#{item.excessFee,jdbcType=DECIMAL},
                </if>
                <if test=" item.reduceFee !=null " >
                    reduce_fee=#{item.reduceFee,jdbcType=DECIMAL},
                </if>
                <if test=" item.outboundsortingFee !=null " >
                    outboundsorting_fee=#{item.outboundsortingFee,jdbcType=DECIMAL},
                </if>
                <if test=" item.shortbargeFee !=null " >
                    shortbarge_fee=#{item.shortbargeFee,jdbcType=DECIMAL},
                </if>
                <if test="item.returnFee !=null " >
                    return_fee=#{item.returnFee,jdbcType=DECIMAL},
                </if>
                <if test="item.exceptionFee !=null " >
                    exception_fee=#{item.exceptionFee,jdbcType=DECIMAL},
                </if>
                <if test="item.adjustFee !=null " >
                    adjust_fee=#{item.adjustFee,jdbcType=DECIMAL},
                </if>
                <if test="item.adjustRemark !=null " >
                    adjust_remark=#{item.adjustRemark,jdbcType=VARCHAR},
                </if>
                <if test="item.otherCost1 !=null " >
                    other_cost1=#{item.otherCost1,jdbcType=DECIMAL},
                </if>
                <if test="item.otherCost2 !=null " >
                    other_cost2=#{item.otherCost2,jdbcType=DECIMAL},
                </if>
                <if test="item.otherCost3 !=null " >
                    other_cost3=#{item.otherCost3,jdbcType=DECIMAL},
                </if>
                <if test="item.otherCost4 !=null " >
                    other_cost4=#{item.otherCost4,jdbcType=DECIMAL},
                </if>
                <if test="item.otherCost5 !=null " >
                    other_cost5=#{item.otherCost5,jdbcType=DECIMAL},
                </if>
                <if test="item.otherCost6 !=null " >
                    other_cost6=#{item.otherCost6,jdbcType=DECIMAL},
                </if>
                <if test="item.otherCost7 !=null " >
                    other_cost7=#{item.otherCost7,jdbcType=DECIMAL},
                </if>
                <if test="item.otherCost8 !=null " >
                    other_cost8=#{item.otherCost8,jdbcType=DECIMAL},
                </if>
                <if test="item.otherCost9 !=null " >
                    other_cost9=#{item.otherCost9,jdbcType=DECIMAL},
                </if>
                <if test="item.otherCost10 !=null " >
                    other_cost10=#{item.otherCost10,jdbcType=DECIMAL},
                </if>
                <if test="item.otherCost11 !=null " >
                    other_cost11=#{item.otherCost11,jdbcType=DECIMAL},
                </if>
                <if test="item.otherCost12 !=null " >
                    other_cost12=#{item.otherCost12,jdbcType=DECIMAL},
                </if>
                <if test="item.sumFee !=null " >
                    sum_fee=#{item.sumFee,jdbcType=DECIMAL},
                </if>
                <if test="item.costAttribute !=null " >
                    cost_attribute=#{item.costAttribute,jdbcType=SMALLINT},
                </if>
                <if test="item.totalWeight !=null " >
                    total_weight=#{item.totalWeight,jdbcType=DECIMAL},
                </if>
                <if test="item.totalVolume !=null " >
                    total_volume=#{item.totalVolume,jdbcType=DECIMAL},
                </if>
                <if test="item.totalBoxes !=null " >
                    total_boxes=#{item.totalBoxes,jdbcType=DECIMAL},
                </if>
                <if test="item.totalNumber !=null " >
                    total_number=#{item.totalNumber,jdbcType=DECIMAL},
                </if>
                <if test="item.operCode !=null " >
                    oper_code=#{item.operCode,jdbcType=VARCHAR},
                </if>
                <if test="item.operBy !=null " >
                    oper_by=#{item.operBy,jdbcType=VARCHAR},
                </if>
                <if test="item.operTime !=null " >
                    oper_time=#{item.operTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createCode !=null " >
                    create_code=#{item.createCode,jdbcType=VARCHAR},
                </if>
                <if test="item.createBy !=null " >
                    create_by=#{item.createBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime !=null " >
                    create_time=#{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.delFlag !=null " >
                    del_flag=#{item.delFlag,jdbcType=CHAR},
                </if>
                <if test="item.billId !=null " >
                    bill_id=#{item.billId,jdbcType=BIGINT},
                </if>
                <if test="item.billDate !=null " >
                    bill_date=#{item.billDate,jdbcType=VARCHAR},
                </if>
                <if test="item.businessTime !=null " >
                    business_time=#{item.businessTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.overNum !=null " >
                    over_num=#{item.overNum,jdbcType=DECIMAL},
                </if>
                <if test="item.overSendnum !=null " >
                    over_sendnum=#{item.overSendnum,jdbcType=INTEGER},
                </if>
                <if test="item.storageFeePrice !=null " >
                    storage_fee_price=#{item.storageFeePrice,jdbcType=DECIMAL},
                </if>
                <if test="item.disposalFeePrice !=null " >
                    disposal_fee_price=#{item.disposalFeePrice,jdbcType=DECIMAL},
                </if>
                <if test="item.otherFeeRemark !=null " >
                    other_fee_remark=#{item.otherFeeRemark,jdbcType=VARCHAR},
                </if>
                <if test="item.feeTypeFirst !=null " >
                    fee_type_first=#{item.feeTypeFirst,jdbcType=VARCHAR},
                </if>
                <if test="item.feeCreateAte !=null " >
                    fee_create_ate=#{item.feeCreateAte,jdbcType=VARCHAR},
                </if>
                <if test="item.isIncrement !=null " >
                    is_increment=#{item.isIncrement,jdbcType=TINYINT},
                </if>
                <if test="item.orderDate !=null " >
                    order_date=#{item.orderDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.signingDate !=null " >
                    signing_date=#{item.signingDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.quoteruledetailId !=null " >
                    quoteruledetail_id=#{item.quoteruledetailId,jdbcType=VARCHAR},
                </if>
                <if test="item.showBillId !=null " >
                    show_bill_id=#{item.showBillId,jdbcType=INTEGER},
                </if>
                <if test="item.showBillCode !=null " >
                    show_bill_code=#{item.showBillCode,jdbcType=VARCHAR},
                </if>
                <if test="item.extraField1 !=null " >
                    extra_field1=#{item.extraField1,jdbcType=DECIMAL},
                </if>
                <if test="item.warehouseCodeArr !=null " >
                    warehouse_code_arr=#{item.warehouseCodeArr,jdbcType=VARCHAR},
                </if>
                <if test="item.settleSetting !=null " >
                    settle_setting=#{item.settleSetting,jdbcType=SMALLINT},
                </if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>
</mapper>