<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfstockinfoMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYfstockinfo" id="BmsYfstockinfoResult">
        <result property="id"    column="id"    />
        <result property="companyId"    column="company_id"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="clientId"    column="client_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="stockCode"    column="stock_code"    />
        <result property="yfbillId"    column="yfbill_id"    />
        <result property="skuCode"    column="sku_code"    />
        <result property="skuName"    column="sku_name"    />
        <result property="instorageTime"    column="instorage_time"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="oddBoxes"    column="odd_boxes"    />
        <result property="boxType"    column="box_type"    />
        <result property="temperatureType"    column="temperature_type"    />
        <result property="trust"    column="trust"    />
        <result property="weight"    column="weight"    />
        <result property="volume"    column="volume"    />
        <result property="warehouseArea"    column="warehouse_area"    />
        <result property="remark"    column="remark"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="costStatus"    column="cost_status"    />
        <result property="billingStatus"    column="billing_status"    />
        <result property="aqty"    column="aqty"    />
        <result property="palletNumber"    column="pallet_number"    />
        <result property="ifAutarky"    column="if_autarky"    />
        <result property="storageServiceProvider"    column="storage_service_provider"    />
        <result property="palletRuler"    column="pallet_ruler"    />
        <result property="unit"    column="unit"    />
    </resultMap>

    <sql id="selectBmsYfstockinfoVo">
        select id, company_id, carrier_code, carrier_name,client_id, warehouse_code, stock_code, yfbill_id, sku_code, sku_name, instorage_time, total_boxes, odd_boxes, box_type, temperature_type, trust, weight, volume, warehouse_area, remark, oper_dept_id, oper_code, oper_by, oper_time, del_flag, cost_status, billing_status,aqty,pallet_number,if_autarky,storage_service_provider,pallet_ruler,unit from bms_yfstockinfo
    </sql>

    <select id="selectBmsYfstockinfoList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfstockinfo" resultMap="BmsYfstockinfoResult">
        <include refid="selectBmsYfstockinfoVo"/>
        <where>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="carrierCode != null  and carrierCode != ''"> and carrier_code = #{carrierCode}</if>
            <if test="carrierName != null  and carrierName != ''"> and carrier_name like concat('%', #{carrierName}, '%')</if>
            <if test="clientId != null  and clientId != ''"> and client_id = #{clientId}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="stockCode != null  and stockCode != ''"> and stock_code = #{stockCode}</if>
            <if test="yfbillId != null "> and yfbill_id = #{yfbillId}</if>
            <if test="skuCode != null  and skuCode != ''"> and sku_code = #{skuCode}</if>
            <if test="skuName != null  and skuName != ''"> and sku_name like concat('%', #{skuName}, '%')</if>
            <if test="instorageTime != null "> and instorage_time = #{instorageTime}</if>
            <if test="totalBoxes != null "> and total_boxes = #{totalBoxes}</if>
            <if test="oddBoxes != null "> and odd_boxes = #{oddBoxes}</if>
            <if test="boxType != null  and boxType != ''"> and box_type = #{boxType}</if>
            <if test="temperatureType != null  and temperatureType != ''"> and temperature_type = #{temperatureType}</if>
            <if test="trust != null "> and trust = #{trust}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="volume != null "> and volume = #{volume}</if>
            <if test="warehouseArea != null "> and warehouse_area = #{warehouseArea}</if>
            <if test="operDeptId != null "> and oper_dept_id = #{operDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
            <if test="costStatus != null  and costStatus != ''"> and cost_status = #{costStatus}</if>
            <if test="billingStatus != null  and billingStatus != ''"> and billing_status = #{billingStatus}</if>
            <if test="delFlag != null and delFlag != ''">and del_flag = #{delFlag}</if>
        </where>
    </select>
    
    <select id="selectBmsYfstockinfoById" parameterType="java.lang.String" resultMap="BmsYfstockinfoResult">
        <include refid="selectBmsYfstockinfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBmsYfstockinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfstockinfo" useGeneratedKeys="true" keyProperty="id">
        insert into bms_yfstockinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyId != null">company_id,</if>
            <if test="carrierCode != null">carrier_code,</if>
            <if test="carrierName != null">carrier_name,</if>
            <if test="clientId != null">client_id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="stockCode != null">stock_code,</if>
            <if test="yfbillId != null">yfbill_id,</if>
            <if test="skuCode != null">sku_code,</if>
            <if test="skuName != null">sku_name,</if>
            <if test="instorageTime != null">instorage_time,</if>
            <if test="totalBoxes != null">total_boxes,</if>
            <if test="oddBoxes != null">odd_boxes,</if>
            <if test="boxType != null">box_type,</if>
            <if test="temperatureType != null">temperature_type,</if>
            <if test="trust != null">trust,</if>
            <if test="weight != null">weight,</if>
            <if test="volume != null">volume,</if>
            <if test="warehouseArea != null">warehouse_area,</if>
            <if test="remark != null">remark,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="costStatus != null">cost_status,</if>
            <if test="billingStatus != null">billing_status,</if>
            <if test="aqty != null">aqty,</if>
            <if test="palletNumber != null">pallet_number,</if>
            <if test="ifAutarky != null">if_autarky,</if>
            <if test="storageServiceProvider != null">storage_service_provider,</if>
            <if test="palletRuler != null">pallet_ruler,</if>
            <if test="unit != null">unit,</if>
            <if test="importTime != null">import_time,</if>
            <if test="importCode != null">import_code,</if>
            <if test="importBy != null">import_by,</if>
            <if test="orderSource != null">order_source,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="carrierCode != null">#{carrierCode},</if>
            <if test="carrierName != null">#{carrierName},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="stockCode != null">#{stockCode},</if>
            <if test="yfbillId != null">#{yfbillId},</if>
            <if test="skuCode != null">#{skuCode},</if>
            <if test="skuName != null">#{skuName},</if>
            <if test="instorageTime != null">#{instorageTime},</if>
            <if test="totalBoxes != null">#{totalBoxes},</if>
            <if test="oddBoxes != null">#{oddBoxes},</if>
            <if test="boxType != null">#{boxType},</if>
            <if test="temperatureType != null">#{temperatureType},</if>
            <if test="trust != null">#{trust},</if>
            <if test="weight != null">#{weight},</if>
            <if test="volume != null">#{volume},</if>
            <if test="warehouseArea != null">#{warehouseArea},</if>
            <if test="remark != null">#{remark},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="costStatus != null">#{costStatus},</if>
            <if test="billingStatus != null">#{billingStatus},</if>
            <if test="aqty != null">#{aqty},</if>
            <if test="palletNumber != null">#{palletNumber},</if>
            <if test="ifAutarky != null">#{ifAutarky},</if>
            <if test="storageServiceProvider != null">#{storageServiceProvider},</if>
            <if test="palletRuler != null">#{palletRuler},</if>
            <if test="unit != null">#{unit},</if>
            <if test="importTime != null">#{importTime},</if>
            <if test="importCode != null">#{importCode},</if>
            <if test="importBy != null">#{importBy},</if>
            <if test="orderSource != null">#{orderSource},</if>
        </trim>
    </insert>

    <update id="updateBmsYfstockinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfstockinfo">
        update bms_yfstockinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="carrierCode != null">carrier_code = #{carrierCode},</if>
            <if test="carrierName != null">carrier_name = #{carrierName},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="stockCode != null">stock_code = #{stockCode},</if>
            <if test="yfbillId != null">yfbill_id = #{yfbillId},</if>
            <if test="skuCode != null">sku_code = #{skuCode},</if>
            <if test="skuName != null">sku_name = #{skuName},</if>
            <if test="instorageTime != null">instorage_time = #{instorageTime},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="oddBoxes != null">odd_boxes = #{oddBoxes},</if>
            <if test="boxType != null">box_type = #{boxType},</if>
            <if test="temperatureType != null">temperature_type = #{temperatureType},</if>
            <if test="trust != null">trust = #{trust},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="volume != null">volume = #{volume},</if>
            <if test="warehouseArea != null">warehouse_area = #{warehouseArea},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="costStatus != null">cost_status = #{costStatus},</if>
            <if test="billingStatus != null">billing_status = #{billingStatus},</if>
            <if test="aqty != null">aqty = #{aqty},</if>
            <if test="palletNumber != null">pallet_number = #{palletNumber},</if>
            <if test="ifAutarky != null">if_autarky = #{ifAutarky},</if>
            <if test="storageServiceProvider != null">storage_service_provider = #{storageServiceProvider},</if>
            <if test="palletRuler != null">pallet_ruler = #{palletRuler},</if>
            <if test="unit != null">unit = #{unit},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYfstockinfoById" parameterType="java.lang.String">
        delete from bms_yfstockinfo where id = #{id}
    </delete>

    <delete id="deleteBmsYfstockinfoByIds">
        delete from bms_yfstockinfo where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYfstockinfoStatusByIds">
        update bms_yfstockinfo set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <update id="updateBmsYfbillcodeinfoAndStockByIds">
        update bms_yfstockinfo set billing_status = #{billingStatus}
        where
        id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>

        ;

        update bms_yfstock_codeinfo set billing_status = #{billingStatus}
        where
        id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectStorageBillGroupByIds" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDto">

        select
        a.carrier_code as carrierCode,
        a.bill_date as billDate,
        a.company_id as companyId,
        a.carrier_name as carrierName,
        a.client_id as clientId,
        c.client_name as clientName,
        d.warehouse_code as warehouseCode
        from bms_yfcost_info a
        inner join bms_yfexpenses_middle b
        on a.id = b.expenses_id
        left join bms_clientinfo c
        on c.id = a.client_id
        left join bms_yfcost_extend d on a.id=d.expenses_id
        where a.id  in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and a.expenses_type in
        <foreach item="type" collection="ysbillType" open="(" separator="," close=")">
            #{type}
        </foreach>
        and IFNULL(a.fee_flag,1)=1
        group by a.carrier_code,a.bill_date,a.client_id,a.company_id

    </select>

    <select id="selectStorageBillByIdsAndFeeFlag" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDto">

        select
        a.carrier_code as carrierCode,
        a.bill_date as billDate,
        a.company_id as companyId,
        c.client_name as clientName
        from bms_yfcost_info a
        inner join bms_yfexpenses_middle b
        on a.id = b.expenses_id
        left join bms_clientinfo c
        on a.client_id = c.id
        where a.id  in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and a.expenses_type in
        <foreach item="type" collection="ysbillType" open="(" separator="," close=")">
            #{type}
        </foreach>
        and IFNULL(a.fee_flag,1)=2
        group by a.carrier_code


    </select>

    <select id="selectByCode" resultMap="BmsYfstockinfoResult">
        <include refid="selectBmsYfstockinfoVo"/>
        where  del_flag = '0'
        <if test="codes != null and codes.size>0 ">
            and  stock_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <insert id="batchSave" parameterType="java.util.List">
        insert into bms_yfstockinfo
        (id,company_id,carrier_code,carrier_name,client_id, warehouse_code, stock_code, yfbill_id, sku_code, sku_name, instorage_time, total_boxes, odd_boxes, box_type, temperature_type,
        trust, weight, volume, warehouse_area, remark, oper_dept_id, oper_code, oper_by, oper_time, del_flag, cost_status, billing_status,aqty,pallet_number,if_autarky,storage_service_provider,
        pallet_ruler,unit,import_time,import_code,import_by,order_source )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},
            #{item.companyId},
            #{item.carrierCode},
            #{item.carrierName},
            #{item.clientId},
            #{item.warehouseCode},
            #{item.stockCode},
            #{item.yfbillId},
            #{item.skuCode},
            #{item.skuName},
            #{item.instorageTime},
            #{item.totalBoxes},
            #{item.oddBoxes},
            #{item.boxType},
            #{item.temperatureType},
            #{item.trust},
            #{item.weight},
            #{item.volume},
            #{item.warehouseArea},
            #{item.remark},
            #{item.operDeptId},
            #{item.operCode},
            #{item.operBy},
            #{item.operTime},
            #{item.delFlag},
            #{item.costStatus},
            #{item.billingStatus},
            #{item.aqty},
            #{item.palletNumber},
            #{item.ifAutarky},
            #{item.storageServiceProvider},
            #{item.palletRuler},
            #{item.unit},
            #{item.importTime},
            #{item.importCode},
            #{item.importBy},
            #{item.orderSource})
        </foreach>
    </insert>

    <update id="deleteStockinfoByCodes" parameterType="java.util.List" >
        update bms_yfstockinfo info
        left join bms_yfstock_goodsinfo goods
        on info.id = goods.yfstock_id
        set info.del_flag = 1 ,
        goods.del_flag = 1
        where
        info.stock_code in
        <foreach collection="codes" item="codes" open="(" separator="," close=")">
            #{codes}
        </foreach>
    </update>
</mapper>