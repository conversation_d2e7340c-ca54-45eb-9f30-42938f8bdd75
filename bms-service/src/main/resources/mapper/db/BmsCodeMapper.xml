<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsCodeMapper">


    <update id="flushTransDetailPkId">
        UPDATE bms_trans_code_detail_info t1
        INNER JOIN bms_trans_code_info t2 ON t2.id = t1.main_code_id AND t2.del_flag = 0
        SET
            t1.main_pk_id = t2.pk_id
        WHERE t1.del_flag = 0
        AND t1.main_code_id IN
        <foreach item="id" collection="mainIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="flushStorageDetailPkId">
        UPDATE bms_storage_code_detail_info t1
        INNER JOIN bms_storage_code_info t2 ON t2.id = t1.main_code_id AND t2.del_flag = 0
        SET
            t1.main_pk_id = t2.pk_id
        WHERE t1.del_flag = 0
        AND t1.main_code_id IN
        <foreach item="id" collection="mainIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="flushJobDetailPkId">
        UPDATE bms_job_code_detail_info t1
        INNER JOIN bms_job_code_info t2 ON t2.id = t1.main_code_id AND t2.del_flag = 0
        SET
            t1.main_pk_id = t2.pk_id,
            t1.scheduling_pk_id = t2.main_pk_id
        WHERE t1.del_flag = 0
        AND t1.main_code_id IN
        <foreach item="id" collection="mainIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="flushJobPkId">
        UPDATE bms_job_code_info t1
        INNER JOIN bms_dispatch_code_info t2 ON t2.id = t1.main_code_id AND t2.del_flag = 0
        SET
            t1.main_pk_id = t2.pk_id
        WHERE t1.del_flag = 0
        AND t1.main_code_id IN
        <foreach item="id" collection="mainIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>




    <!-- 运输单据查询 -->
    <select id="queryTrans" resultType="com.bbyb.joy.bms.code.domain.dto.BmsTransCodeInfoDto">
        SELECT
            t1.id AS id,
            t1.pk_id AS pkId,
            t1.relate_code AS relateCode,
            t1.order_code AS orderCode,
            t1.transaction_code AS transactionCode,
            t1.scheduling_code AS schedulingCode,
            t1.code_type AS codeType,
            t1.business_type AS businessType,
            t1.order_source AS orderSource,
            t1.client_id AS clientId,
            t1.client_code AS clientCode,
            t1.client_name AS clientName,
            t1.cost_status AS costStatus,
            t1.cost_fail_remark AS costFailRemark,
            t1.billing_status AS billingStatus,
            t1.is_timeout AS isTimeout,
            t1.is_autarky AS isAutarky,
            t1.is_rejected AS isRejected,
            t1.rejected_responsible_party AS rejectedResponsibleParty,
            t1.delivery_mode AS deliveryMode,
            t1.transport_type AS transportType,
            t1.company_id AS companyId,
            t1.warehouse_code AS warehouseCode,
            t1.storage_service_provider AS storageServiceProvider,
            t1.network_code AS networkCode,
            t1.line_code AS lineCode,
            t1.line_name AS lineName,
            IFNULL(t1.total_boxes,0) AS totalBoxes,
            IFNULL(t1.total_split_boxes,0) AS totalSplitBoxes,
            IFNULL(t1.total_normal_full_boxes,0) AS totalNormalFullBoxes,
            IFNULL(t1.total_normal_split_boxes,0) AS totalNormalSplitBoxes,
            IFNULL(t1.total_unnormal_full_boxes,0) AS totalUnnormalFullBoxes,
            IFNULL(t1.total_unnormal_split_boxes,0) AS totalUnnormalSplitBoxes,
            IFNULL(t1.total_number,0) AS totalNumber,
            IFNULL(t1.total_split_number,0) AS totalSplitNumber,
            IFNULL(t1.total_over_number,0) AS totalOverNumber,
            IFNULL(t1.total_day_number,0) AS totalDayNumber,
            IFNULL(t1.total_weight,0) AS totalWeight,
            IFNULL(t1.total_volume,0) AS totalVolume,
            IFNULL(t1.total_sku_number,0) AS totalSkuNumber,
            IFNULL(t1.total_pallet_number,0) AS totalPalletNumber,
            IFNULL(t1.total_day_pallet_number,0) AS totalDayPalletNumber,
            IFNULL(t1.total_cw_pallet_number,0) AS totalCwPalletNumber,
            IFNULL(t1.total_ld_pallet_number,0) AS totalLdPalletNumber,
            IFNULL(t1.total_lc_pallet_number,0) AS totalLcPalletNumber,
            IFNULL(t1.total_cargo_value,0) AS totalCargoValue,
            t1.pallet_rule AS palletRule,
            t1.unit AS unit,
            t1.order_date AS orderDate,
            t1.signing_date AS signingDate,
            t1.delivery_code AS deliveryCode,
            t1.delivery_name AS deliveryName,
            t1.receiving_store_code AS receivingStoreCode,
            t1.receiving_store_name AS receivingStoreName,
            t1.originating_province AS originatingProvince,
            t1.originating_city AS originatingCity,
            t1.originating_area AS originatingArea,
            t1.originating_address AS originatingAddress,
            t1.destination_province AS destinationProvince,
            t1.destination_city AS destinationCity,
            t1.destination_area AS destinationArea,
            t1.destination_address AS destinationAddress,
            t1.over_mileage AS overMileage,
            t1.base_store_status AS baseStoreStatus,
            t1.base_mileage_status AS baseMileageStatus,
            t1.distance_warehouse_mileage AS distanceWarehouseMileage,
            t1.car_type AS carType,
            t1.car_model AS carModel,
            t1.remark AS remark,
            t1.create_code AS createCode,
            t1.create_by AS createBy,
            t1.create_dept_id AS createDeptId,
            t1.create_time AS createTime,
            t1.oper_code AS operCode,
            t1.oper_by AS operBy,
            t1.oper_dept_id AS operDeptId,
            t1.oper_time AS operTime,
            t1.del_flag AS delFlag,
            t1.opt_month AS optMonth,
            t1.opt_day AS optDay
        FROM bms_trans_code_info t1
        WHERE t1.del_flag = 0
        <choose>
            <when test="relateCode != null and relateCode!='' and relateCode.indexOf(',') != -1">
                AND t1.relate_code IN
                <foreach item="item" collection="relateCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <when test="relateCode != null  and relateCode!=''">
                AND t1.relate_code LIKE CONCAT( #{relateCode}, '%')
            </when>
        </choose>
        <if test="relateCodes != null and relateCodes.size>0">
            AND t1.relate_code IN
            <foreach item="relateCode" collection="relateCodes" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="receivingStoreName != null and receivingStoreName != ''">
            AND t1.receiving_store_name = CONCAT( #{receivingStoreName}, '%')
        </if>
        <if test="warehouseCodes != null and warehouseCodes.size>0">
            AND t1.warehouse_code IN
            <foreach item="warehouseCode" collection="warehouseCodes" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="deliveryModes != null and deliveryModes.size>0">
            AND t1.delivery_mode IN
            <foreach item="deliveryMode" collection="deliveryModes" open="(" separator="," close=")">
                #{deliveryMode}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size>0">
            AND t1.company_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            AND t1.id = #{id}
        </if>
        <if test="pkId != null ">
            AND t1.pk_id = #{pkId}
        </if>
        <if test="pkIds != null and pkIds.size>0">
            AND t1.pk_id IN
            <foreach item="pkId" collection="pkIds" open="(" separator="," close=")">
                #{pkId}
            </foreach>
        </if>
        <if test="startDate!=null and startDate!='' and endDate!=null and endDate!='' ">
            <choose>
                <when test="dateType != null and dateType == 1">
                    AND t1.signing_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 2">
                    AND t1.order_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 3">
                    AND t1.oper_time BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t1.oper_time BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
        </if>
        <if test="clientId != null">
            AND t1.client_id = #{clientId}
        </if>
        <if test="clientIds != null and clientIds.size>0">
            AND t1.client_id IN
            <foreach item="clientId" collection="clientIds" open="(" separator="," close=")">
                #{clientId}
            </foreach>
        </if>
        <if test="costStatus!=null and costStatus.size>0 ">
            AND t1.cost_status IN
            <foreach item="costState" collection="costStatus" open="(" separator="," close=")">
                #{costState}
            </foreach>
        </if>
        <if test="pageQueryFlag != null and pageQueryFlag == true">
            ORDER BY
            <choose>
                <when test="dateType != null and dateType == 1">
                    t1.signing_date DESC
                </when>
                <when test="dateType != null and dateType == 2">
                    t1.order_date DESC
                </when>
                <when test="dateType != null and dateType == 3">
                    t1.oper_time DESC
                </when>
                <otherwise>
                    t1.oper_time DESC
                </otherwise>
            </choose>
        </if>
    </select>

    <!-- 仓储应收单据查询 -->
    <select id="queryYsStorage" resultType="com.bbyb.joy.bms.code.domain.dto.BmsStorageCodeInfoDto">
        SELECT
            t1.id AS id,
            t1.pk_id AS pkId,
            t1.relate_code AS relateCode,
            t1.order_code AS orderCode,
            t1.transaction_code AS transactionCode,
            t1.code_type AS codeType,
            t1.business_type AS codeBusinessType,
            t1.order_source AS orderSource,
            t1.client_id AS clientId,
            t1.client_code AS clientCode,
            t1.client_name AS clientName,
            t1.carrier_id AS carrierId,
            t1.carrier_code AS carrierCode,
            t1.carrier_name AS carrierName,
            t1.company_id AS companyId,
            t1.cost_mode AS costMode,
            t1.ys_cost_status AS ysCostStatus,
            t1.ys_cost_fail_remark AS ysCostFailRemark,
            t1.ys_billing_status AS ysBillingStatus,
            t1.yf_cost_status AS yfCostStatus,
            t1.yf_cost_fail_remark AS yfCostFailRemark,
            t1.yf_billing_status AS yfBillingStatus,
            t1.is_timeout AS isTimeout,
            t1.is_autarky AS isAutarky,
            t1.is_rejected AS isRejected,
            t1.rejected_responsible_party AS rejectedResponsibleParty,
            t1.delivery_mode AS deliveryMode,
            t1.transport_type AS transportType,
            t1.storage_service_provider AS storageServiceProvider,
            t1.warehouse_code AS warehouseCode,
            t1.network_code AS networkCode,
            IFNULL(t1.total_boxes,0) AS totalBoxes,
            IFNULL(t1.total_split_boxes,0) AS totalSplitBoxes,
            IFNULL(t1.total_normal_full_boxes,0) AS totalNormalFullBoxes,
            IFNULL(t1.total_normal_split_boxes,0) AS totalNormalSplitBoxes,
            IFNULL(t1.total_unnormal_full_boxes,0) AS totalUnnormalFullBoxes,
            IFNULL(t1.total_unnormal_split_boxes,0) AS totalUnnormalSplitBoxes,
            IFNULL(t1.total_number,0) AS totalNumber,
            IFNULL(t1.total_split_number,0) AS totalSplitNumber,
            IFNULL(t1.total_over_number,0) AS totalOverNumber,
            IFNULL(t1.total_day_number,0) AS totalDayNumber,
            IFNULL(t1.total_weight,0) AS totalWeight,
            IFNULL(t1.total_volume,0) AS totalVolume,
            IFNULL(t1.total_sku_number,0) AS totalSkuNumber,
            IFNULL(t1.total_pallet_number,0) AS totalPalletNumber,
            IFNULL(t1.total_day_pallet_number,0) AS totalDayPalletNumber,
            IFNULL(t1.total_cw_pallet_number,0) AS totalCwPalletNumber,
            IFNULL(t1.total_ld_pallet_number,0) AS totalLdPalletNumber,
            IFNULL(t1.total_lc_pallet_number,0) AS totalLcPalletNumber,
            IFNULL(t1.total_cargo_value,0) AS totalCargoValue,
            t1.pallet_rule AS palletRule,
            t1.box_rule_code AS boxRuleCode,
            t1.box_rule AS boxRule,
            t1.temperature_type_id AS temperatureTypeId,
            t1.temperature_type AS temperatureType,
            t1.sku_code AS skuCode,
            t1.sku_name AS skuName,
            t1.unit AS unit,
            t1.order_date AS orderDate,
            t1.signing_date AS signingDate,
            t1.delivery_code AS deliveryCode,
            t1.delivery_name AS deliveryName,
            t1.receiving_store_code AS receivingStoreCode,
            t1.receiving_store_name AS receivingStoreName,
            t1.originating_province AS originatingProvince,
            t1.originating_city AS originatingCity,
            t1.originating_area AS originatingArea,
            t1.originating_address AS originatingAddress,
            t1.destination_province AS destinationProvince,
            t1.destination_city AS destinationCity,
            t1.destination_area AS destinationArea,
            t1.destination_address AS destinationAddress,
            t1.over_mileage AS overMileage,
            t1.base_store_status AS baseStoreStatus,
            t1.base_mileage_status AS baseMileageStatus,
            t1.distance_warehouse_mileage AS distanceWarehouseMileage,
            t1.car_type AS carType,
            t1.car_model AS carModel,
            t1.remark AS remark,
            t1.create_code AS createCode,
            t1.create_by AS createBy,
            t1.create_dept_id AS createDeptId,
            t1.create_time AS createTime,
            t1.oper_code AS operCode,
            t1.oper_by AS operBy,
            t1.oper_dept_id AS operDeptId,
            t1.oper_time AS operTime,
            t1.del_flag AS delFlag,
            t1.opt_month AS optMonth,
            t1.opt_day AS optDay
        FROM bms_storage_code_info t1
        WHERE t1.del_flag = 0 AND t1.cost_mode = 1
        <choose>
            <when test="relateCode != null and relateCode.indexOf(',') != -1">
                AND t1.relate_code IN
                <foreach item="item" collection="relateCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <when test="relateCode != null and relateCode != ''">
                AND t1.relate_code LIKE CONCAT( #{relateCode}, '%')
            </when>
        </choose>
        <if test="relateCodes != null and relateCodes.size>0">
            AND t1.relate_code IN
            <foreach item="relateCode" collection="relateCodes" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="codeTypes != null and codeTypes.size>0">
            AND t1.code_type IN
            <foreach item="codeType" collection="codeTypes" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="codeType!=null">
            AND t1.code_type = #{codeType}
        </if>
        <if test="companyIds != null and companyIds.size>0">
            AND t1.company_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="warehouseCodes != null and warehouseCodes.size>0">
            AND t1.warehouse_code IN
            <foreach item="warehouseCode" collection="warehouseCodes" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND t1.warehouse_code = #{warehouseCode}
        </if>
        <if test="receivingStoreName != null and receivingStoreName != ''">
            AND t1.receiving_store_name LIKE CONCAT( #{receivingStoreName}, '%')
        </if>
        <if test="id != null and id != ''">
            AND t1.id = #{id}
        </if>
        <if test="pkId != null ">
            AND t1.pk_id = #{pkId}
        </if>
        <if test="pkIds != null and pkIds.size>0">
            AND t1.pk_id IN
            <foreach item="pkId" collection="pkIds" open="(" separator="," close=")">
                #{pkId}
            </foreach>
        </if>
        <if test="startDate!=null and startDate!='' and endDate!=null and endDate!='' ">
            <choose>
                <when test="dateType != null and dateType == 1">
                    AND t1.signing_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 2">
                    AND t1.order_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 3">
                    AND t1.oper_time BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t1.oper_time BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
        </if>
        <if test="clientId != null">
            AND t1.client_id = #{clientId}
        </if>
        <if test="clientIds != null and clientIds.size>0">
            AND t1.client_id IN
            <foreach item="clientId" collection="clientIds" open="(" separator="," close=")">
                #{clientId}
            </foreach>
        </if>
        <if test="costStatus!=null and costStatus.size>0 ">
            AND t1.ys_cost_status IN
            <foreach item="costState" collection="costStatus" open="(" separator="," close=")">
                #{costState}
            </foreach>
        </if>
        <if test="pageQueryFlag != null and pageQueryFlag == true">
            ORDER BY
            <choose>
                <when test="dateType != null and dateType == 1">
                    t1.signing_date DESC
                </when>
                <when test="dateType != null and dateType == 2">
                    t1.order_date DESC
                </when>
                <when test="dateType != null and dateType == 3">
                    t1.oper_time DESC
                </when>
                <otherwise>
                    t1.oper_time DESC
                </otherwise>
            </choose>
        </if>
    </select>

    <!-- 仓储应付单据查询 -->
    <select id="queryYfStorage" resultType="com.bbyb.joy.bms.code.domain.dto.BmsStorageCodeInfoDto">
        SELECT
            t1.id AS id,
            t1.pk_id AS pkId,
            t1.relate_code AS relateCode,
            t1.order_code AS orderCode,
            t1.transaction_code AS transactionCode,
            t1.code_type AS codeType,
            t1.business_type AS businessType,
            t1.order_source AS orderSource,
            t1.client_id AS clientId,
            t1.client_code AS clientCode,
            t1.client_name AS clientName,
            t1.carrier_id AS carrierId,
            t1.carrier_code AS carrierCode,
            t1.carrier_name AS carrierName,
            t1.company_id AS companyId,
            t1.cost_mode AS costMode,
            t1.ys_cost_status AS ysCostStatus,
            t1.ys_cost_fail_remark AS ysCostFailRemark,
            t1.ys_billing_status AS ysBillingStatus,
            t1.yf_cost_status AS yfCostStatus,
            t1.yf_cost_fail_remark AS yfCostFailRemark,
            t1.yf_billing_status AS yfBillingStatus,
            t1.is_timeout AS isTimeout,
            t1.is_autarky AS isAutarky,
            t1.is_rejected AS isRejected,
            t1.rejected_responsible_party AS rejectedResponsibleParty,
            t1.delivery_mode AS deliveryMode,
            t1.transport_type AS transportType,
            t1.storage_service_provider AS storageServiceProvider,
            t1.warehouse_code AS warehouseCode,
            t1.network_code AS networkCode,
            IFNULL(t1.total_boxes,0) AS totalBoxes,
            IFNULL(t1.total_split_boxes,0) AS totalSplitBoxes,
            IFNULL(t1.total_normal_full_boxes,0) AS totalNormalFullBoxes,
            IFNULL(t1.total_normal_split_boxes,0) AS totalNormalSplitBoxes,
            IFNULL(t1.total_unnormal_full_boxes,0) AS totalUnnormalFullBoxes,
            IFNULL(t1.total_unnormal_split_boxes,0) AS totalUnnormalSplitBoxes,
            IFNULL(t1.total_number,0) AS totalNumber,
            IFNULL(t1.total_split_number,0) AS totalSplitNumber,
            IFNULL(t1.total_over_number,0) AS totalOverNumber,
            IFNULL(t1.total_day_number,0) AS totalDayNumber,
            IFNULL(t1.total_weight,0) AS totalWeight,
            IFNULL(t1.total_volume,0) AS totalVolume,
            IFNULL(t1.total_sku_number,0) AS totalSkuNumber,
            IFNULL(t1.total_pallet_number,0) AS totalPalletNumber,
            IFNULL(t1.total_day_pallet_number,0) AS totalDayPalletNumber,
            IFNULL(t1.total_cw_pallet_number,0) AS totalCwPalletNumber,
            IFNULL(t1.total_ld_pallet_number,0) AS totalLdPalletNumber,
            IFNULL(t1.total_lc_pallet_number,0) AS totalLcPalletNumber,
            IFNULL(t1.total_cargo_value,0) AS totalCargoValue,
            t1.pallet_rule AS palletRule,
            t1.box_rule_code AS boxRuleCode,
            t1.box_rule AS boxRule,
            t1.temperature_type_id AS temperatureTypeId,
            t1.temperature_type AS temperatureType,
            t1.sku_code AS skuCode,
            t1.sku_name AS skuName,
            t1.unit AS unit,
            t1.order_date AS orderDate,
            t1.signing_date AS signingDate,
            t1.delivery_code AS deliveryCode,
            t1.delivery_name AS deliveryName,
            t1.receiving_store_code AS receivingStoreCode,
            t1.receiving_store_name AS receivingStoreName,
            t1.originating_province AS originatingProvince,
            t1.originating_city AS originatingCity,
            t1.originating_area AS originatingArea,
            t1.originating_address AS originatingAddress,
            t1.destination_province AS destinationProvince,
            t1.destination_city AS destinationCity,
            t1.destination_area AS destinationArea,
            t1.destination_address AS destinationAddress,
            t1.over_mileage AS overMileage,
            t1.base_store_status AS baseStoreStatus,
            t1.base_mileage_status AS baseMileageStatus,
            t1.distance_warehouse_mileage AS distanceWarehouseMileage,
            t1.car_type AS carType,
            t1.car_model AS carModel,
            t1.remark AS remark,
            t1.create_code AS createCode,
            t1.create_by AS createBy,
            t1.create_dept_id AS createDeptId,
            t1.create_time AS createTime,
            t1.oper_code AS operCode,
            t1.oper_by AS operBy,
            t1.oper_dept_id AS operDeptId,
            t1.oper_time AS operTime,
            t1.del_flag AS delFlag,
            t1.opt_month AS optMonth,
            t1.opt_day AS optDay
            FROM bms_storage_code_info t1
        WHERE t1.del_flag = 0 AND t1.cost_mode = 2
        <choose>
            <when test="relateCode != null and relateCode.indexOf(',') != -1">
                AND t1.relate_code IN
                <foreach item="item" collection="relateCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <when test="relateCode != null and relateCode!=''">
                AND t1.relate_code LIKE CONCAT( #{relateCode}, '%')
            </when>
        </choose>
        <if test="relateCodes != null and relateCodes.size > 0">
            AND t1.relate_code IN
            <foreach item="relateCode" collection="relateCodes" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="receivingStoreName != null and receivingStoreName != ''">
            AND t1.receiving_store_name LIKE CONCAT( #{receivingStoreName}, '%')
        </if>
        <if test="codeTypes != null and codeTypes.size>0">
            AND t1.code_type IN
            <foreach item="codeType" collection="codeTypes" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="warehouseCodes != null and warehouseCodes.size>0">
            AND t1.warehouse_code IN
            <foreach item="warehouseCode" collection="warehouseCodes" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND t1.warehouse_code LIKE CONCAT( #{warehouseCode}, '%')
        </if>
        <if test="codeType!=null">
            AND t1.code_type = #{codeType}
        </if>
        <if test="companyIds != null and companyIds.size > 0">
            AND t1.company_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            AND t1.id = #{id}
        </if>
        <if test="pkId != null ">
            AND t1.pk_id = #{pkId}
        </if>
        <if test="pkIds != null and pkIds.size > 0">
            AND t1.pk_id IN
            <foreach item="pkId" collection="pkIds" open="(" separator="," close=")">
                #{pkId}
            </foreach>
        </if>
        <if test="startDate!=null and startDate!='' and endDate!=null and endDate!='' ">
            <choose>
                <when test="dateType != null and dateType == 1">
                    AND t1.signing_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 2">
                    AND t1.order_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 3">
                    AND t1.oper_time BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t1.oper_time BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
        </if>
        <if test="clientId != null">
            AND t1.client_id = #{clientId}
        </if>
        <if test="clientIds != null and clientIds.size > 0">
            AND t1.client_id IN
            <foreach item="clientId" collection="clientIds" open="(" separator="," close=")">
                #{clientId}
            </foreach>
        </if>
        <if test="carrierId != null">
            AND t1.carrier_id = #{carrierId}
        </if>
        <if test="carrierIds != null and carrierIds.size > 0">
            AND t1.carrier_id IN
            <foreach item="carrierId" collection="carrierIds" open="(" separator="," close=")">
                #{carrierId}
            </foreach>
        </if>
        <if test="costStatus!=null and costStatus.size > 0 ">
            AND t1.yf_cost_status IN
            <foreach item="costState" collection="costStatus" open="(" separator="," close=")">
                #{costState}
            </foreach>
        </if>
        <if test="pageQueryFlag != null and pageQueryFlag == true">
            ORDER BY
            <choose>
                <when test="dateType != null and dateType == 1">
                    t1.signing_date DESC
                </when>
                <when test="dateType != null and dateType == 2">
                    t1.order_date DESC
                </when>
                <when test="dateType != null and dateType == 3">
                    t1.oper_time DESC
                </when>
                <otherwise>
                    t1.oper_time DESC
                </otherwise>
            </choose>
        </if>
    </select>

    <!-- 调度单据查询 -->
    <select id="queryDispatch" resultType="com.bbyb.joy.bms.code.domain.dto.BmsDispatchCodeInfoDto">
        SELECT
            t1.id AS id,
            t1.pk_id AS pkId,
            t1.scheduling_code AS schedulingCode,
            t1.virtual_scheduling_code AS virtualSchedulingCode,
            t1.original_scheduling_code AS originalSchedulingCode,
            t1.original_order_type AS originalOrderType,
            t1.express_no AS expressNo,
            t1.order_source AS orderSource,
            t1.split_status AS splitStatus,
            t1.clients_dimension AS clientsDimension,
            t1.transport_type AS transportType,
            t1.transport_mode AS transportMode,
            t1.delivery_mode AS deliveryMode,
            t1.carrier_id AS carrierId,
            t1.carrier_code AS carrierCode,
            t1.carrier_name AS carrierName,
            t1.client_id AS clientId,
            t1.client_code AS clientCode,
            t1.client_name AS clientName,
            t1.company_id AS companyId,
            t1.cost_status AS costStatus,
            t1.cost_fail_remark AS costFailRemark,
            t1.billing_status AS billingStatus,
            t1.network_code AS networkCode,
            t1.base_stores AS baseStores,
            t1.total_store_times AS totalStoreTimes,
            t1.total_in_office_times AS totalInOfficeTimes,
            t1.total_out_office_times AS totalOutOfficeTimes,
            t1.base_kilometer AS baseKilometer,
            t1.total_kilometer AS totalKilometer,
            t1.line_code AS lineCode,
            t1.line_name AS lineName,
            t1.tms_line_code AS tmsLineCode,
            t1.tms_line_name AS tmsLineName,
            t1.dispatch_date AS dispatchDate,
            t1.start_date AS startDate,
            t1.finish_date AS finishDate,
            IFNULL(t1.total_boxes,0) AS totalBoxes,
            IFNULL(t1.total_number,0) AS totalNumber,
            IFNULL(t1.total_weight,0) AS totalWeight,
            IFNULL(t1.total_volume,0) AS totalVolume,
            IFNULL(t1.total_cargo_value,0) AS totalCargoValue,
            IFNULL(t1.total_votes,0) AS totalVotes,
            t1.originating_province AS originatingProvince,
            t1.originating_city AS originatingCity,
            t1.originating_area AS originatingArea,
            t1.originating_address AS originatingAddress,
            t1.destination_province AS destinationProvince,
            t1.destination_city AS destinationCity,
            t1.destination_area AS destinationArea,
            t1.destination_address AS destinationAddress,
            t1.loading_points_number AS loadingPointsNumber,
            t1.unloading_points_number AS unloadingPointsNumber,
            t1.driver AS driver,
            t1.car_number AS carNumber,
            t1.car_type AS carType,
            t1.car_model AS carModel,
            t1.remark AS remark,
            t1.create_code AS createCode,
            t1.create_by AS createBy,
            t1.create_dept_id AS createDeptId,
            t1.create_time AS createTime,
            t1.oper_code AS operCode,
            t1.oper_by AS operBy,
            t1.oper_dept_id AS operDeptId,
            t1.oper_time AS operTime,
            t1.del_flag AS delFlag,
            t1.opt_month AS optMonth,
            t1.opt_day AS optDay
        FROM bms_dispatch_code_info t1
        WHERE t1.del_flag = 0
        <if test="companyIds != null and companyIds.size > 0">
            AND t1.company_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            AND t1.id = #{id}
        </if>
        <if test="pkId != null ">
            AND t1.pk_id = #{pkId}
        </if>
        <if test="pkIds != null and pkIds.size > 0">
            AND t1.pk_id IN
            <foreach item="pkId" collection="pkIds" open="(" separator="," close=")">
                #{pkId}
            </foreach>
        </if>
        <choose>
            <when test="schedulingCode != null and schedulingCode != '' and schedulingCode.indexOf(',') != -1">
                AND t1.scheduling_code IN
                <foreach item="item" collection="schedulingCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <when test="schedulingCode != null and schedulingCode != '' and !schedulingCode.indexOf(',') != -1">
                AND t1.scheduling_code LIKE CONCAT( #{schedulingCode}, '%')
            </when>
        </choose>
        <if test="schedulingCodes != null and schedulingCodes.size > 0">
            AND t1.scheduling_code IN
            <foreach item="schedulingCode" collection="schedulingCodes" open="(" separator="," close=")">
                #{schedulingCode}
            </foreach>
        </if>
        <choose>
            <when test="virtualSchedulingCode != null and virtualSchedulingCode.indexOf(',') != -1">
                AND t1.virtual_scheduling_code IN
                <foreach item="item" collection="virtualSchedulingCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <when test="virtualSchedulingCode != null and !virtualSchedulingCode.indexOf(',') != -1">
                AND t1.virtual_scheduling_code LIKE CONCAT( #{virtualSchedulingCode}, '%')
            </when>
        </choose>
        <if test="virtualSchedulingCodes != null and virtualSchedulingCodes.size>0">
            AND t1.virtual_scheduling_code IN
            <foreach item="virtualSchedulingCode" collection="virtualSchedulingCodes" open="(" separator="," close=")">
                #{virtualSchedulingCode}
            </foreach>
        </if>
        <choose>
            <when test="originalSchedulingCode != null and originalSchedulingCode != '' and originalSchedulingCode.indexOf(',') != -1">
                AND t1.original_scheduling_code IN
                <foreach item="item" collection="originalSchedulingCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <when test="originalSchedulingCode != null and originalSchedulingCode != '' and !originalSchedulingCode.indexOf(',') != -1">
                AND t1.original_scheduling_code LIKE CONCAT( #{originalSchedulingCode}, '%')
            </when>
        </choose>
        <if test="originalSchedulingCodes != null and originalSchedulingCodes.size>0">
            AND t1.original_scheduling_code IN
            <foreach item="originalSchedulingCode" collection="originalSchedulingCodes" open="(" separator="," close=")">
                #{originalSchedulingCode}
            </foreach>
        </if>
        <choose>
            <when test="expressNo != null and expressNo != '' and expressNo.indexOf(',') != -1">
                AND t1.express_no IN
                <foreach item="item" collection="expressNo.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <when test="expressNo != null and expressNo != '' and !expressNo.indexOf(',') != -1">
                AND t1.express_no = #{expressNo}
            </when>
        </choose>
        <if test="expressNos != null and expressNos.size > 0">
            AND t1.express_no IN
            <foreach item="expressNo" collection="expressNos" open="(" separator="," close=")">
                #{expressNo}
            </foreach>
        </if>
        <if test="originalOrderTypes != null and originalOrderTypes.size > 0">
            AND t1.original_order_type IN
            <foreach item="originalOrderType" collection="originalOrderTypes" open="(" separator="," close=")">
                #{originalOrderType}
            </foreach>
        </if>
        <if test="originalOrderType != null">
            AND t1.original_order_type = #{originalOrderType}
        </if>
        <if test="deliveryModes != null and deliveryModes.size > 0">
            AND t1.delivery_mode IN
            <foreach item="deliveryMode" collection="deliveryModes" open="(" separator="," close=")">
                #{deliveryMode}
            </foreach>
        </if>
        <if test="transportTypes != null and transportTypes.size > 0">
            AND t1.transport_type IN
            <foreach item="transportType" collection="transportTypes" open="(" separator="," close=")">
                #{transportType}
            </foreach>
        </if>
        <if test="tmsLineCode!=null and tmsLineCode!=''">
            AND t1.tms_line_code = #{tmsLineCode}
        </if>
        <if test="tmsLineName!=null and tmsLineName!=''">
            AND t1.tms_line_name = #{tmsLineName}
        </if>
        <if test="startDate!=null and startDate!='' and endDate!=null and endDate!='' ">
            <choose>
                <when test="dateType != null and dateType == 1">
                    AND t1.finish_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 2">
                    AND t1.dispatch_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 3">
                    AND t1.oper_time BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t1.oper_time BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
        </if>
        <if test="clientId != null">
            AND t1.client_id = #{clientId}
        </if>
        <if test="clientIds != null and clientIds.size>0">
            AND t1.client_id IN
            <foreach item="clientId" collection="clientIds" open="(" separator="," close=")">
                #{clientId}
            </foreach>
        </if>
        <if test="carrierId != null">
            AND t1.carrier_id = #{carrierId}
        </if>
        <if test="carrierIds != null and carrierIds.size > 0">
            AND t1.carrier_id IN
            <foreach item="carrierId" collection="carrierIds" open="(" separator="," close=")">
                #{carrierId}
            </foreach>
        </if>
        <if test="costStatus!=null and costStatus.size > 0 ">
            AND t1.cost_status IN
            <foreach item="costState" collection="costStatus" open="(" separator="," close=")">
                #{costState}
            </foreach>
        </if>
        <if test="pageQueryFlag != null and pageQueryFlag == true">
            ORDER BY
            <choose>
                <when test="dateType != null and dateType == 1">
                    t1.finish_date DESC
                </when>
                <when test="dateType != null and dateType == 2">
                    t1.dispatch_date DESC
                </when>
                <when test="dateType != null and dateType == 3">
                    t1.oper_time DESC
                </when>
                <otherwise>
                    t1.oper_time DESC
                </otherwise>
            </choose>
        </if>
    </select>



</mapper>