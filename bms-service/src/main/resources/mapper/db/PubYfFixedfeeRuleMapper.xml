<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubYfFixedfeeRuleMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.PubYfFixedfeeRule" id="PubYfFixedfeeRuleResult">
        <result property="id"    column="id"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="ruleCode"    column="rule_code"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="companyId"    column="company_id"    />
        <result property="clientId"    column="client_id"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="itemId"    column="item_id"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="frequency"    column="frequency"    />
        <result property="amount"    column="amount"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createCode"    column="create_code"    />
        <result property="createTime"    column="create_time"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectPubYfFixedfeeRuleVo">
        select id,
               rule_type,
               warehouse_code,
               company_id,
               client_id,
               carrier_id,
               carrier_code,
               item_id,
               start_date,
               end_date,
               frequency,
               amount,
               remark,
               create_by,
               create_code,
               create_time,
               oper_by,
               oper_code,
               oper_time,
               del_flag from pub_yf_fixedfee_rule
    </sql>

    <sql id="Base_Column_List">
        id,
       rule_type,
       rule_code,
       warehouse_code,
       company_id,
       client_id,
       carrier_id,
       carrier_code,
       item_id,
       start_date,
       end_date,
       frequency,
       amount,
       remark,
       create_by,
       create_code,
       create_time,
       oper_by,
       oper_code,
       oper_time,
       del_flag
    </sql>

    <select id="selectPubYfFixedfeeRuleList" parameterType="com.bbyb.joy.bms.domain.dto.PubYfFixedfeeRule" resultMap="PubYfFixedfeeRuleResult">
        <include refid="selectPubYfFixedfeeRuleVo"/>
        <where>
            <if test="ruleType != null "> and rule_type = #{ruleType}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="clientId != null "> and client_id = #{clientId}</if>
            <if test="carrierId != null "> and carrier_id = #{carrierId}</if>
            <if test="carrierCode != null  and carrierCode != ''"> and carrier_code = #{carrierCode}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="frequency != null "> and frequency = #{frequency}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="createCode != null  and createCode != ''"> and create_code = #{createCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
        </where>
    </select>


    <select id="selectPubYfFixedfeeRuleById" parameterType="java.lang.String" resultMap="PubYfFixedfeeRuleResult">
        select <include refid="selectPubYfFixedfeeRuleVo"/> from pub_yf_fixedfee_rule
        where id = #{id}
    </select>
    <select id="selectConflictOfTime" resultType="com.bbyb.joy.bms.domain.dto.dto.PubYfFixedfeeRuleDto"
            resultMap="PubYfFixedfeeRuleResult">
        select
        <include refid="Base_Column_List"/>
        from pub_yf_fixedfee_rule
        where del_flag = 0
        and rule_type = #{ruleType}
        and carrier_id = #{carrierId}
        and client_id = #{clientId}
        and item_id = #{itemId}
        AND (
        (
        rule_type = 1 AND
        <if test="companyIds != null and companyIds.size() > 0">
            company_id in
            <foreach collection="companyIds" item="companyId" open="(" separator="," close=")">#{companyId}</foreach>
        </if>
        <if test="companyIds == null or companyIds.size() == 0">
            1=1
        </if>
        )
        OR
        (
        rule_type = 2 AND
        <if test="warehouseCodes != null and warehouseCodes.size() > 0">
            warehouse_code in
            <foreach collection="warehouseCodes" item="warehouseCode" open="(" separator="," close=")">
                #{warehouseCode}
            </foreach>
        </if>
        <if test="warehouseCodes == null or warehouseCodes.size() == 0">
            1=1
        </if>
        )
        )
        and NOT (end_date &lt;= #{startDate} OR start_date >= #{endDate})
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

<resultMap id="PubYfFixedfeeRuleExtendResult" type="com.bbyb.joy.bms.domain.dto.dto.PubYfFixedfeeRuleExtendDto" extends="PubYfFixedfeeRuleResult">
    <result property="warehouseName" column="warehouse_name"/>
    <result property="itemName" column="item_name"/>
</resultMap>


    <select id="selectByCondition" parameterType="com.bbyb.joy.bms.domain.dto.dto.PubYfFixedfeeRuleDto" resultMap="PubYfFixedfeeRuleExtendResult">
        select
            r.id,
            r.rule_type,
            r.rule_code,
            case when r.rule_type = 1 then '运输'
                when r.rule_type = 2 then '仓储'
            end as ruleTypeName,
            mw.warehouse_code,
            mw.warehouse_name,
            r.company_id,
            r.client_id,
            mc.client_name as clientName,
            r.carrier_id,
            r.carrier_code,
            m.carrier_name as carrierName,
            r.item_id,
            r.start_date,
            r.end_date,
            r.frequency,
            case when r.frequency = 1 then '按月'
                 when r.frequency = 2 then '按季'
                end as frequencyName,
            r.amount,
            r.remark,
            r.create_by,
            r.create_code,
            r.create_time,
            r.oper_by,
            r.oper_code,
            r.oper_time
        from pub_yf_fixedfee_rule r
                 left join mdm_warehouseinfo mw on r.warehouse_code = mw.warehouse_code
                 left join bms_clientinfo mc on r.client_id = mc.id
                 left join bms_carrierinfo m on r.carrier_id = m.id
        <where>
                r.del_flag =0
            <if test="id != null">
                and r.id = #{id}
            </if>
            <if test="ruleType != null">
                and r.rule_type = #{ruleType}
            </if>
            <if test="clientId != null">
                and r.client_id = #{clientId}
            </if>
            <if test="carrierId != null">
                and r.carrier_id = #{carrierId}
            </if>
            <if test="itemId != null">
                and r.item_id = #{itemId}
            </if>
            <if test="warehouseCode != null and warehouseCode != ''">
                and (mw.warehouse_name = #{warehouseCode} or mw.warehouse_code =#{warehouseCode})
            </if>
            <if test="companyIds != null and companyIds.size() > 0">
                and r.company_id in <foreach collection="companyIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            </if>
            <if test="ruleCode != null and ruleCode != ''">
                and r.rule_code = #{ruleCode}
            </if>
            <if test="createBy != null and createBy != ''">
                and r.create_by = #{createBy}
            </if>
            <if test="operBy != null and operBy != ''">
                and r.oper_by = #{operBy}
            </if>
            <if test="beginCreateTime != null and beginCreateTime != '' and endCreateTime != null and endCreateTime != ''">
                and r.create_time BETWEEN #{beginCreateTime} and  #{endCreateTime}
            </if>
            <if test="beginOperTime != null and beginOperTime != '' and endOperTime != null and endOperTime != ''">
                and r.oper_time BETWEEN #{beginOperTime} and #{endOperTime}
            </if>
        </where>
     order by  r.id desc
    </select>

    <select id="queryDetail"  resultMap="PubYfFixedfeeRuleExtendResult">
        select
        r.id,
        r.rule_type,
        r.rule_code,
        case when r.rule_type = 1 then '运输'
        when r.rule_type = 2 then '仓储'
        end as ruleTypeName,
        mw.warehouse_code,
        mw.warehouse_name,
        r.company_id,
        r.client_id,
        mc.client_name as clientName,
        r.carrier_id,
        r.carrier_code,
        m.carrier_name as carrierName,
        r.item_id,
        pfs.item_name,
        r.start_date,
        r.end_date,
        r.frequency,
        case when r.frequency = 1 then '按月'
        when r.frequency = 2 then '按季'
        end as frequencyName,
        r.amount,
        r.remark,
        r.create_by,
        r.create_code,
        r.create_time,
        r.oper_by,
        r.oper_code,
        r.oper_time
        from pub_yf_fixedfee_rule r
        left join pub_fee_subject pfs on r.item_id = pfs.id
        left join mdm_warehouseinfo mw on r.warehouse_code = mw.warehouse_code
        left join bms_clientinfo mc on r.client_id = mc.id
        left join bms_carrierinfo m on r.carrier_id = m.id
         where r.del_flag =0
            <if test="id != null">
                and r.id = #{id}
            </if>
    </select>

    <select id="selectYfRuleByids" resultMap="PubYfFixedfeeRuleResult">
        select * from pub_yf_fixedfee_rule where del_flag = 0 and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </select>
    <select id="selectById" resultMap="PubYfFixedfeeRuleResult" >
        select * from pub_yf_fixedfee_rule where id = #{id} and del_flag = 0
    </select>


    <insert id="insertPubYfFixedfeeRule" parameterType="com.bbyb.joy.bms.domain.dto.PubYfFixedfeeRule">
        insert into pub_yf_fixedfee_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="ruleCode != null">rule_code,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="companyId != null">company_id,</if>
            <if test="clientId != null">client_id,</if>
            <if test="carrierId != null">carrier_id,</if>
            <if test="carrierCode != null">carrier_code,</if>
            <if test="itemId != null">item_id,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="frequency != null">frequency,</if>
            <if test="amount != null">amount,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createTime != null">create_time,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="ruleCode != null">#{ruleCode},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="carrierId != null">#{carrierId},</if>
            <if test="carrierCode != null">#{carrierCode},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="amount != null">#{amount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updatePubYfFixedfeeRule" parameterType="com.bbyb.joy.bms.domain.dto.PubYfFixedfeeRule">
        update pub_yf_fixedfee_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleType != null">rule_type = #{ruleType},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="carrierId != null">carrier_id = #{carrierId},</if>
            <if test="carrierCode != null">carrier_code = #{carrierCode},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePubYfFixedfeeRuleById" parameterType="java.lang.String">
        delete from pub_yf_fixedfee_rule where id = #{id}
    </delete>

    <delete id="deletePubYfFixedfeeRuleByIds">
        delete from pub_yf_fixedfee_rule where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="del" parameterType="com.bbyb.joy.bms.domain.dto.PubYfFixedfeeRule">
        update pub_yf_fixedfee_rule set del_flag = 1 , oper_by = #{operBy}, oper_code = #{operCode}, oper_time = #{operTime} where id = #{id}
    </update>

    <update id="updatePubYfFixedfeeRuleStatusByIds">
        update pub_yf_fixedfee_rule set status = #{status} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="update" parameterType="com.bbyb.joy.bms.domain.dto.PubYfFixedfeeRule">
        update pub_yf_fixedfee_rule
        <set>
            <if test="ruleType != null">rule_type = #{ruleType},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="carrierId != null">carrier_id = #{carrierId},</if>
            <if test="carrierCode != null">carrier_code = #{carrierCode},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
        </set>
            where id = #{id}
    </update>

    <select id="selectForCreateWithLimit" resultMap="PubYfFixedfeeRuleResult">
        select <include refid="Base_Column_List"/> from pub_yf_fixedfee_rule where del_flag = 0
        and end_date &gt;= #{formatDate} and start_date &lt;= #{formatDate} order by id desc limit #{offSet},#{size}
    </select>


    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from pub_yf_fixedfee_rule where del_flag = 0
        and end_date &gt;= #{formatDate} and start_date &lt;= #{formatDate}
    </select>
</mapper>
