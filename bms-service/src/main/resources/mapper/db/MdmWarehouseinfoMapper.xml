<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.MdmWarehouseinfoMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.MdmWarehouseinfo" id="MdmWarehouseinfoResult">
        <result property="id"    column="id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="network"    column="network"    />
        <result property="warehouseType"    column="warehouse_type"    />
        <result property="serviceCode"    column="service_code"    />
        <result property="warehouseProvince"    column="warehouse_province"    />
        <result property="warehouseCity"    column="warehouse_city"    />
        <result property="warehouseArea"    column="warehouse_area"    />
        <result property="address"    column="address"    />
        <result property="companyId"    column="company_id"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="status"    column="status"    />
        <result property="deptCode"    column="dept_code"    />
    </resultMap>

    <sql id="selectMdmWarehouseinfoVo">
        select id, warehouse_code, warehouse_name, network, warehouse_type, service_code, warehouse_province, warehouse_city, warehouse_area, address, company_id, oper_by, oper_dept_id, oper_time, del_flag from mdm_warehouseinfo
    </sql>

    <select id="selectMdmWarehouseinfoList" parameterType="com.bbyb.joy.bms.domain.dto.MdmWarehouseinfo" resultMap="MdmWarehouseinfoResult">
        select
            war.id,
            war.warehouse_code,
            war.warehouse_name,
            war.network,
            war.warehouse_type,
            war.service_code,
            war.warehouse_province,
            war.warehouse_city,
            war.warehouse_area,
            war.address,
            war.company_id,
            war.oper_by,
            war.oper_dept_id,
            war.oper_time,
            war.del_flag,
            wor.bill_name as billName
        from mdm_warehouseinfo war
        left join company_network wor
        on war.warehouse_code = wor.warehouse_code
        <where>  
            <if test="warehouseCode != null  and warehouseCode != ''"> and war.warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and war.warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="network != null  and network != ''"> and wor.network = #{network}</if>
            <if test="warehouseType != null "> and war.warehouse_type = #{warehouseType}</if>
            <if test="serviceCode != null  and serviceCode != ''"> and war.service_code = #{serviceCode}</if>
            <if test="warehouseProvince != null  and warehouseProvince != ''"> and war.warehouse_province = #{warehouseProvince}</if>
            <if test="warehouseCity != null  and warehouseCity != ''"> and war.warehouse_city = #{warehouseCity}</if>
            <if test="warehouseArea != null  and warehouseArea != ''"> and war.warehouse_area = #{warehouseArea}</if>
            <if test="address != null  and address != ''"> and war.address = #{address}</if>
            <if test="operBy != null  and operBy != ''"> and war.oper_by = #{operBy}</if>
            <if test="operDeptId != null "> and war.oper_dept_id = #{operDeptId}</if>
            <if test="operTime != null "> and war.oper_time = #{operTime}</if>
            <if test="status != null "> and war.status = #{status}</if>
            <if test="companyIds != null and companyIds.size > 0  ">
                and war.company_id in
                <foreach collection="companyIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by war.warehouse_code
        order by war.oper_time DESC
    </select>
    
    <select id="selectMdmWarehouseinfoById" parameterType="java.lang.String" resultMap="MdmWarehouseinfoResult">
        <include refid="selectMdmWarehouseinfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMdmWarehouseinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmWarehouseinfo" useGeneratedKeys="true" keyProperty="id">
        insert into mdm_warehouseinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="network != null">network,</if>
            <if test="warehouseType != null">warehouse_type,</if>
            <if test="serviceCode != null">service_code,</if>
            <if test="warehouseProvince != null">warehouse_province,</if>
            <if test="warehouseCity != null">warehouse_city,</if>
            <if test="warehouseArea != null">warehouse_area,</if>
            <if test="address != null">address,</if>
            <if test="companyId != null">company_id,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="status != null">status,</if>
            <if test="deptCode != null">dept_code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="network != null">#{network},</if>
            <if test="warehouseType != null">#{warehouseType},</if>
            <if test="serviceCode != null">#{serviceCode},</if>
            <if test="warehouseProvince != null">#{warehouseProvince},</if>
            <if test="warehouseCity != null">#{warehouseCity},</if>
            <if test="warehouseArea != null">#{warehouseArea},</if>
            <if test="address != null">#{address},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="status != null">#{status},</if>
            <if test="deptCode != null">#{deptCode},</if>
         </trim>
    </insert>

    <update id="updateMdmWarehouseinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmWarehouseinfo">
        update mdm_warehouseinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="network != null">network = #{network},</if>
            <if test="warehouseType != null">warehouse_type = #{warehouseType},</if>
            <if test="serviceCode != null">service_code = #{serviceCode},</if>
            <if test="warehouseProvince != null">warehouse_province = #{warehouseProvince},</if>
            <if test="warehouseCity != null">warehouse_city = #{warehouseCity},</if>
            <if test="warehouseArea != null">warehouse_area = #{warehouseArea},</if>
            <if test="address != null">address = #{address},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="status != null">status = #{status},</if>
            <if test="deptCode != null">dept_code = #{deptCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMdmWarehouseinfoById" parameterType="java.lang.String">
        delete from mdm_warehouseinfo where id = #{id}
    </delete>

    <delete id="deleteMdmWarehouseinfoByIds" parameterType="java.util.Map">
        delete from mdm_warehouseinfo where id in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateMdmWarehouseinfoStatusByIds">
        update mdm_warehouseinfo set status = #{status} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectWarehouseList" resultMap="MdmWarehouseinfoResult">
        <include refid="selectMdmWarehouseinfoVo"/>
        where del_flag=0
        <if test="ids != null and ids.size>0 ">
            AND id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
        <if test="codes != null and codes.size>0 ">
            AND warehouse_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
        <if test="name != null and name != ''">
            AND warehouse_name like concat('%',trim(#{name}),'%')
        </if>
    </select>

    <select id="selectWarehouseMapByCodeNames" resultMap="MdmWarehouseinfoResult">
        <include refid="selectMdmWarehouseinfoVo"/>
        where del_flag=0
        <if test="names != null and names.size>0 ">
            AND warehouse_name in
            <foreach collection="names" item="names" open="(" separator="," close=")">
                #{names}
            </foreach>
        </if>
        <if test="codes != null and codes.size>0 ">
            AND warehouse_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>


    <select id="checkWarehouseCodeUnique" parameterType="java.lang.String" resultMap="MdmWarehouseinfoResult">
        select id, warehouse_code from mdm_warehouseinfo where del_flag = 0 and warehouse_code=#{warehouseCode} limit 1
    </select>

    <select id="selectMdmWarehouseinfoByCode" parameterType="java.lang.String" resultMap="MdmWarehouseinfoResult">
        <include refid="selectMdmWarehouseinfoVo"/>
        where del_flag =0 and warehouse_code = #{warehouseCode}
    </select>
</mapper>