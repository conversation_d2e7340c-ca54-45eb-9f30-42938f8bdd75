<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsCalculateRuleMapper">

    <select id="queryValidList" resultType="com.bbyb.joy.bms.calculate.domain.dto.rule.CalculateRuleInfoDto">
        SELECT
            t1.pk_id AS mainPkId,
            t1.id AS mainId,
            t1.rule_code AS mainRuleCode,
            t1.rule_name AS mainRuleName,
            t1.relation_id AS relationId,
            t1.user_company_id AS userCompanyId,
            t1.project_quotation AS projectQuotation,
            t1.client_idstr AS clientIdStr,
            t1.chargeby_weight AS chargeByWeight,
            t1.chargeby_volume AS chargeByVolume,
            t1.decimal_point AS decimalPoint,
            t1.rule_type AS ruleType,
            t2.id AS id,
            t2.pk_id AS pkId,
            t2.rulecode AS ruleCode,
            t2.rulecode AS ruleName,
            t2.fee_type AS feeType,
            t2.fee_type_str AS feeTypeName,
            t2.quoterule_template_id AS templateId,
            t2.client_id AS clientId,
            t2.start_time AS startTime,
            t2.end_time AS endTime,
            t2.bill_type AS billType,
            t2.fee_type_str AS feeTypeStr,
            t2.audit_state AS auditState,
            t3.rule_code AS templateRuleCode,
            t3.rule_name AS templateRuleName,
            t3.consolidation_rule AS consolidationRule,
            t3.group_rule AS groupRule,
            t3.calculation_formula_code AS calculateCode,
            t3.matching_conditions_code AS matchingCode,
            t3.calculation_process_code AS processCode,
            CASE
                WHEN t1.rule_type = 1
                    THEN t4.carrierpay_type
                WHEN t1.rule_type = 2
                    THEN t5.carrier_paytype
                END AS settleType,
            CASE
                WHEN t1.rule_type = 1
                    THEN t4.settle_setting
                WHEN t1.rule_type = 2
                    THEN t5.settle_setting
                END AS settleSetting,
            CASE
                WHEN t1.rule_type = 1
                    THEN t4.share_type
                WHEN t1.rule_type = 2
                    THEN t5.share_type
                END AS shareType
        FROM pub_quoterule t1
        LEFT JOIN pub_quoterule_detail t2 ON t1.id=t2.quoterule_id AND t2.del_flag=0 AND t2.is_enable=0
        LEFT JOIN pub_quoterule_template t3 ON t3.id=t2.quoterule_template_id AND t3.del_flag=0 AND t3.is_enable=0
        LEFT JOIN bms_clientinfo t4 ON t4.id = t1.relation_id
        LEFT JOIN bms_carrierinfo t5 ON t5.id = t1.relation_id
        WHERE t1.del_flag=0
        AND t1.is_enable=0
        AND t2.audit_state = 2
        AND t1.business_type IN
        <foreach collection="businessTypes" item="businessType" open="(" separator="," close=")">
            #{businessType}
        </foreach>
        AND COALESCE(t3.calculation_formula_code, '') &lt;&gt; ''
        AND COALESCE(t3.matching_conditions_code, '') &lt;&gt; ''
        AND t1.rule_type = #{ruleType}
        <if test="relationIds != null and relationIds.size>0">
            AND t1.relation_id IN
            <foreach item="id" collection="relationIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="ruleType !=null and ruleType==1">
            AND DATE_FORMAT(t2.end_time,'%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="ruleType !=null and ruleType==2">
            AND DATE_FORMAT(t2.end_time,'%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="codeTypes != null and codeTypes.size>0">
            AND t2.bill_type IN
            <foreach item="codeType" collection="codeTypes" open="(" separator="," close=")">
                #{codeType}
            </foreach>
        </if>
        <if test="ruleCode!=null and ruleCode!=''">
            AND t2.rulecode = #{ruleCode}
        </if>
    </select>


    <select id="queryFactorsList" resultType="com.bbyb.joy.bms.calculate.domain.dto.rule.CalculateFactorInfoDto">
        SELECT
            t1.id AS id,
            t1.quoteruledetail_id AS quoteRuleDetailId,
            t1.store_code AS storeCode,
            t1.store_name AS storeName,
            IFNULL(NULLIF(t1.deliver_province, ''), '全国') AS deliverProvince,
            IFNULL(t1.deliver_area,'') AS deliverArea,
            IFNULL(t1.deliver_city,'') AS deliverCity,
            IFNULL(NULLIF(t1.receive_province, ''), '全国') AS receiveProvince,
            IFNULL(t1.receive_area,'') AS receiveArea,
            IFNULL(t1.receive_city,'') AS receiveCity,
            IFNULL(t1.carmodel,'') AS carModel,
            t1.carlong AS carLong,
            IFNULL(t1.route_code,'') AS routeCode,
            IFNULL(t1.warehouse_name,'') AS warehouseName,
            t1.bill_type AS billType,
            IFNULL(t1.temperature_zone,'') AS temperatureZone,
            t1.specifications AS specifications,
            t1.category AS category,
            t1.is_removezero AS isRemoveZero,
            t1.business_type AS businessType,
            t1.minimum_charge AS minimumCharge,
            t1.unit_price AS unitPrice,
            t1.step AS step,
            t1.singular_ladder1 AS singularLadder1,
            t1.singular_ladder2 AS singularLadder2,
            t1.singular_ladder3 AS singularLadder3,
            t1.singular_ladder4 AS singularLadder4,
            t1.singular_ladder5 AS singularLadder5,
            t1.singular_ladder6 AS singularLadder6,
            t1.box_ladder1 AS boxLadder1,
            t1.box_ladder2 AS boxLadder2,
            t1.box_ladder3 AS boxLadder3,
            t1.box_ladder4 AS boxLadder4,
            t1.box_ladder5 AS boxLadder5,
            t1.box_ladder6 AS boxLadder6,
            t1.weight_ladder1 AS weightLadder1,
            t1.weight_ladder2 AS weightLadder2,
            t1.weight_ladder3 AS weightLadder3,
            t1.weight_ladder4 AS weightLadder4,
            t1.weight_ladder5 AS weightLadder5,
            t1.weight_ladder6 AS weightLadder6,
            t1.volume_ladder1 AS volumeLadder1,
            t1.volume_ladder2 AS volumeLadder2,
            t1.volume_ladder3 AS volumeLadder3,
            t1.volume_ladder4 AS volumeLadder4,
            t1.volume_ladder5 AS volumeLadder5,
            t1.volume_ladder6 AS volumeLadder6,
            t1.kilometre_ladder1 AS kilometreLadder1,
            t1.kilometre_ladder2 AS kilometreLadder2,
            t1.kilometre_ladder3 AS kilometreLadder3,
            t1.kilometre_ladder4 AS kilometreLadder4,
            t1.kilometre_ladder5 AS kilometreLadder5,
            t1.kilometre_ladder6 AS kilometreLadder6,
            t1.ladder_price1 AS ladderPrice1,
            t1.ladder_price2 AS ladderPrice2,
            t1.ladder_price3 AS ladderPrice3,
            t1.ladder_price4 AS ladderPrice4,
            t1.ladder_price5 AS ladderPrice5,
            t1.ladder_price6 AS ladderPrice6,
            t1.percentage AS percentage,
            t1.tornum_ladder1 AS tornumLadder1,
            t1.tornum_ladder2 AS tornumLadder2,
            t1.tornum_ladder3 AS tornumLadder3,
            t1.tornum_ladder4 AS tornumLadder4,
            t1.tornum_ladder5 AS tornumLadder5,
            t1.tornum_ladder6 AS tornumLadder6,
            t1.number_ladder1 AS numberLadder1,
            t1.number_ladder2 AS numberLadder2,
            t1.number_ladder3 AS numberLadder3,
            t1.number_ladder4 AS numberLadder4,
            t1.number_ladder5 AS numberLadder5,
            t1.number_ladder6 AS numberLadder6,
            t1.base_quantity AS baseQuantity,
            t1.category1 AS category1,
            t1.category2 AS category2,
            t1.category3 AS category3,
            t1.category4 AS category4,
            t1.category5 AS category5,
            t1.category6 AS category6,
            t1.item_id AS itemId,
            t1.item_id1 AS itemId1,
            t1.cost_unit AS costUnit
        FROM pub_quotation_classificationdetail t1
        WHERE t1.quoteruledetail_id IN
        <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </select>

</mapper>