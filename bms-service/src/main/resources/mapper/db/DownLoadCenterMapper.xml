<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="mapper.db.DownLoadCenterMapper">
    <!--下载中心查询-->
    <select id="query" resultType="com.bbyb.joy.mdm.dto.DownLoadCenterdto" parameterType="java.util.Map">
        select
        pdc.id id,
        pdc.user_id as userId,
        pdc.type_name as typeName,
        pdc.download_url as downloadUrl,
        pdc.state_name as stateName,
        pdc.error_log as errorLog,
        pdc.file_size as fileSize,
        DATE_FORMAT(pdc.completeed,'%Y-%m-%d %H:%i:%s') as completeeds,
        pdc.creater as creater,
        DATE_FORMAT(pdc.created,'%Y-%m-%d %H:%i:%s') as createds
        from free_download_center pdc
        where pdc.is_valid='0'
        <if test="id != null and id!=''">
            and pdc.id ${id}
        </if>
        <if test="userId != null and userId!=''">
            and pdc.user_id ${userId}
        </if>
        <if test="typeName != null and typeName!=''">
            and pdc.type_name ${typeName}
        </if>
        <if test="downloadUrl != null and downloadUrl!=''">
            and pdc.download_url ${downloadUrl}
        </if>
        <if test="stateName != null and stateName!=''">
            and pdc.state_name ${stateName}
        </if>
        <if test="createdStart != null and createdStart!=''">
            and pdc.created ${createdStart}
        </if>
        <if test="createdEnd != null and createdEnd!=''">
            and pdc.created ${createdEnd}
        </if>
        <if test="order != null and order!=''">
            order by pdc.${order} ${sort}
        </if>
        <if test="sort == null or sort==''">
            order by pdc.created desc
        </if>
    </select>
</mapper>