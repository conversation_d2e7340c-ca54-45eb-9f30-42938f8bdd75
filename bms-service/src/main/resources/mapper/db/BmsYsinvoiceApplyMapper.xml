<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYsinvoiceApplyMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.bill.BmsYsinvoiceApply" id="BmsYsinvoiceApplyResult">
        <result property="id"    column="id"    />
        <result property="billid"    column="billid"    />
        <result property="invoiceProject"    column="invoice_project"    />
        <result property="applyFee"    column="apply_fee"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="openingName"    column="opening_name"    />
        <result property="taxpayerNum"    column="taxpayer_num"    />
        <result property="openingBank"    column="opening_bank"    />
        <result property="cardNumber"    column="card_number"    />
        <result property="linkPhone"    column="link_phone"    />
        <result property="tacAddress"    column="tac_address"    />
        <result property="remark"    column="remark"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="applyRemark"    column="apply_remark"    />
        <result property="email"    column="email"    />
        <result property="area"    column="area"    />
    </resultMap>

    <sql id="selectBmsYsinvoiceApplyVo">
        select id, billid, invoice_project, apply_fee, invoice_type, opening_name, taxpayer_num, opening_bank, card_number, link_phone, tac_address, remark, oper_dept_id, oper_code, oper_by, oper_time, del_flag,apply_remark,email,area from bms_ysinvoice_apply
    </sql>

    <select id="selectBmsYsinvoiceApplyList" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsinvoiceApply" resultMap="BmsYsinvoiceApplyResult">
        <include refid="selectBmsYsinvoiceApplyVo"/>
        <where>  
            <if test="billid != null "> and billid = #{billid}</if>
            <if test="invoiceProject != null "> and invoice_project = #{invoiceProject}</if>
            <if test="applyFee != null "> and apply_fee = #{applyFee}</if>
            <if test="invoiceType != null "> and invoice_type = #{invoiceType}</if>
            <if test="openingName != null  and openingName != ''"> and opening_name like concat('%', #{openingName}, '%')</if>
            <if test="taxpayerNum != null  and taxpayerNum != ''"> and taxpayer_num = #{taxpayerNum}</if>
            <if test="openingBank != null  and openingBank != ''"> and opening_bank = #{openingBank}</if>
            <if test="cardNumber != null  and cardNumber != ''"> and card_number = #{cardNumber}</if>
            <if test="linkPhone != null  and linkPhone != ''"> and link_phone = #{linkPhone}</if>
            <if test="tacAddress != null  and tacAddress != ''"> and tac_address = #{tacAddress}</if>
            <if test="operDeptId != null "> and oper_dept_id = #{operDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
        </where>
    </select>
    
    <select id="selectBmsYsinvoiceApplyById" parameterType="java.lang.String" resultMap="BmsYsinvoiceApplyResult">
        <include refid="selectBmsYsinvoiceApplyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBmsYsinvoiceApply" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsinvoiceApply" useGeneratedKeys="true" keyProperty="id">
        insert into bms_ysinvoice_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billid != null">billid,</if>
            <if test="applyCode != null">apply_code,</if>
            <if test="invoiceProject != null">invoice_project,</if>
            <if test="applyFee != null">apply_fee,</if>
            <if test="invoiceType != null">invoice_type,</if>
            <if test="openingName != null">opening_name,</if>
            <if test="taxpayerNum != null">taxpayer_num,</if>
            <if test="openingBank != null">opening_bank,</if>
            <if test="cardNumber != null">card_number,</if>
            <if test="linkPhone != null">link_phone,</if>
            <if test="tacAddress != null">tac_address,</if>
            <if test="remark != null">remark,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="ticketState != null">ticket_state,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="email != null">email,</if>
            <if test="area != null">area,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billid != null">#{billid},</if>
            <if test="applyCode != null">#{applyCode},</if>
            <if test="invoiceProject != null">#{invoiceProject},</if>
            <if test="applyFee != null">#{applyFee},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="openingName != null">#{openingName},</if>
            <if test="taxpayerNum != null">#{taxpayerNum},</if>
            <if test="openingBank != null">#{openingBank},</if>
            <if test="cardNumber != null">#{cardNumber},</if>
            <if test="linkPhone != null">#{linkPhone},</if>
            <if test="tacAddress != null">#{tacAddress},</if>
            <if test="remark != null">#{remark},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="ticketState != null">#{ticketState},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="email != null">#{email},</if>
            <if test="area != null">#{area},</if>
         </trim>
    </insert>

    <update id="updateBmsYsinvoiceApply" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsinvoiceApply">
        update bms_ysinvoice_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="billid != null">billid = #{billid},</if>
            <if test="invoiceProject != null">invoice_project = #{invoiceProject},</if>
            <if test="applyFee != null">apply_fee = #{applyFee},</if>
            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>
            <if test="openingName != null">opening_name = #{openingName},</if>
            <if test="taxpayerNum != null">taxpayer_num = #{taxpayerNum},</if>
            <if test="openingBank != null">opening_bank = #{openingBank},</if>
            <if test="cardNumber != null">card_number = #{cardNumber},</if>
            <if test="linkPhone != null">link_phone = #{linkPhone},</if>
            <if test="tacAddress != null">tac_address = #{tacAddress},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYsinvoiceApplyById" parameterType="java.lang.String">
        delete from bms_ysinvoice_apply where id = #{id}
    </delete>

    <delete id="deleteBmsYsinvoiceApplyByIds">
        delete from bms_ysinvoice_apply where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYsinvoiceApplyStatusByIds">
        update bms_ysinvoice_apply set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateYsApplyAuditStateByBillid" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsinvoiceApply">
        update bms_ysinvoice_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="invoiceProject != null">invoice_project = #{invoiceProject},</if>
            <if test="applyFee != null">apply_fee = #{applyFee},</if>
            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>
            <if test="openingName != null">opening_name = #{openingName},</if>
            <if test="taxpayerNum != null">taxpayer_num = #{taxpayerNum},</if>
            <if test="openingBank != null">opening_bank = #{openingBank},</if>
            <if test="cardNumber != null">card_number = #{cardNumber},</if>
            <if test="linkPhone != null">link_phone = #{linkPhone},</if>
            <if test="tacAddress != null">tac_address = #{tacAddress},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="applyAuditState != null">apply_audit_state = #{applyAuditState},</if>
        </trim>
        where del_flag=0 and billid = #{billid}
    </update>


    <!-- 批量新增数据 -->
    <insert id="insertBatch">
        insert into bms_ysinvoice_apply(apply_code,billid,invoice_project,apply_fee,remark,apply_remark,invoice_type,invoice_fee,opening_name,taxpayer_num,opening_bank,card_number,link_phone,tac_address,oper_dept_id,oper_code,oper_by,oper_time,del_flag,ticket_state,transtool_type,car_no,place_dispatch,place_destination,trans_goods_name,email,area)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.applyCode},#{entity.billid},#{entity.invoiceProject},#{entity.applyFee},#{entity.remark},#{entity.applyRemark},#{entity.invoiceType},#{entity.invoiceFee},#{entity.openingName},#{entity.taxpayerNum},#{entity.openingBank},#{entity.cardNumber},#{entity.linkPhone},#{entity.tacAddress},#{entity.operDeptId},#{entity.operCode},#{entity.operBy},#{entity.operTime},#{entity.delFlag},#{entity.ticketState}
            ,#{entity.transtoolType},#{entity.carNo},#{entity.placeDispatch},#{entity.placeDestination},#{entity.transGoodsName},#{entity.email},#{entity.area})
        </foreach>
    </insert>

    <select id="selectBmsYsinvoiceApplyByBillId" resultMap="BmsYsinvoiceApplyResult" >
        <include refid="selectBmsYsinvoiceApplyVo"/>
        where del_flag = '0'
        and billid in
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
    </select>

    <select id="selectBmsYsinvoiceApplyByBillId2" resultMap="BmsYsinvoiceApplyResult" >
        SELECT
        t1.billid AS billId,
        t2.bill_code AS billCode,
        CASE
        WHEN FIND_IN_SET(7,GROUP_CONCAT(DISTINCT t1.invoice_project))>0
        THEN 1
        WHEN FIND_IN_SET(8,GROUP_CONCAT(DISTINCT t1.invoice_project))>0
        THEN 1
        ELSE 2
        END AS invoiceMode
        FROM bms_ysinvoice_apply t1
        LEFT JOIN bms_ysbillmain t2 ON t2.id = t1.billid
        WHERE t1.del_flag = '0'
        AND t2.bill_code in
        <foreach collection="billCodes" item="billCode" open="(" separator="," close=")">
            #{billCode}
        </foreach>
        GROUP BY t1.billId,t2.bill_code;
    </select>
</mapper>