<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYscostInfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYscostInfo" id="BmsYscostInfoResult">
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="expenses_code" jdbcType="VARCHAR" property="expensesCode" />
        <result column="business_type" jdbcType="SMALLINT" property="businessType" />
        <result column="client_id" jdbcType="INTEGER" property="clientId" />
        <result column="institution_id" jdbcType="INTEGER" property="institutionId" />
        <result column="company_id" jdbcType="INTEGER" property="companyId" />
        <result column="expenses_type" jdbcType="SMALLINT" property="expensesType" />
        <result column="cost_dimension" jdbcType="SMALLINT" property="costDimension" />
        <result column="charge_type" jdbcType="SMALLINT" property="chargeType" />
        <result column="expenses_mark" jdbcType="SMALLINT" property="feeFlag" />
        <result column="quoterule_id" jdbcType="VARCHAR" property="quoteruleId" />
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="freight" jdbcType="DECIMAL" property="freight" />
        <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
        <result column="ultrafar_fee" jdbcType="DECIMAL" property="ultrafarFee" />
        <result column="superframes_fee" jdbcType="DECIMAL" property="superframesFee" />
        <result column="excess_fee" jdbcType="DECIMAL" property="excessFee" />
        <result column="reduce_fee" jdbcType="DECIMAL" property="reduceFee" />
        <result column="outboundsorting_fee" jdbcType="DECIMAL" property="outboundsortingFee" />
        <result column="shortbarge_fee" jdbcType="DECIMAL" property="shortbargeFee" />
        <result column="return_fee" jdbcType="DECIMAL" property="returnFee" />
        <result column="exception_fee" jdbcType="DECIMAL" property="exceptionFee" />
        <result column="adjust_fee" jdbcType="DECIMAL" property="adjustFee" />
        <result column="adjust_remark" jdbcType="VARCHAR" property="adjustRemark" />
        <result column="other_cost1" jdbcType="DECIMAL" property="otherCost1" />
        <result column="other_cost2" jdbcType="DECIMAL" property="otherCost2" />
        <result column="other_cost3" jdbcType="DECIMAL" property="otherCost3" />
        <result column="other_cost4" jdbcType="DECIMAL" property="otherCost4" />
        <result column="other_cost5" jdbcType="DECIMAL" property="otherCost5" />
        <result column="other_cost6" jdbcType="DECIMAL" property="otherCost6" />
        <result column="other_cost7" jdbcType="DECIMAL" property="otherCost7" />
        <result column="other_cost8" jdbcType="DECIMAL" property="otherCost8" />
        <result column="other_cost9" jdbcType="DECIMAL" property="otherCost9" />
        <result column="other_cost10" jdbcType="DECIMAL" property="otherCost10" />
        <result column="other_cost11" jdbcType="DECIMAL" property="otherCost11" />
        <result column="other_cost12" jdbcType="DECIMAL" property="otherCost12" />
        <result column="cost_attribute" jdbcType="SMALLINT" property="costAttribute" />
        <result column="oper_code" jdbcType="VARCHAR" property="operCode" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="bill_id" jdbcType="BIGINT" property="billId" />
        <result column="bill_date" jdbcType="VARCHAR" property="billDate" />
        <result column="business_time" jdbcType="TIMESTAMP" property="businessTime" />
        <result column="over_num" jdbcType="DECIMAL" property="overNum" />
        <result column="over_sendnum" jdbcType="INTEGER" property="overSendnum" />
        <result column="storage_fee_price" jdbcType="DECIMAL" property="storageFeePrice" />
        <result column="disposal_fee_price" jdbcType="DECIMAL" property="disposalFeePrice" />
        <result column="other_fee_remark" jdbcType="VARCHAR" property="otherFeeRemark" />
        <result column="fee_type_first" jdbcType="VARCHAR" property="feeTypeFirst" />
        <result column="fee_create_ate" jdbcType="VARCHAR" property="feeCreateAte" />
        <result column="is_increment" jdbcType="TINYINT" property="isIncrement" />
        <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
        <result column="signing_date" jdbcType="TIMESTAMP" property="signingDate" />
        <result column="quoteruledetail_id" jdbcType="VARCHAR" property="quoteruledetailId" />
        <result column="show_bill_id" jdbcType="INTEGER" property="showBillId" />
        <result column="show_bill_code" jdbcType="VARCHAR" property="showBillCode" />
        <result column="extra_field1" jdbcType="DECIMAL" property="extraField1" />
        <result column="warehouse_code_arr" jdbcType="VARCHAR" property="warehouseCodeArr" />
        <result column="settle_type" jdbcType="SMALLINT" property="settleType" />
        <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount" />
        <result column="main_expense_id" jdbcType="VARCHAR" property="mainExpenseId" />
        <result column="settle_main_id" jdbcType="INTEGER" property="settleMainId" />
        <result column="automatic_billing_remark" jdbcType="INTEGER" property="automaticBillingRemark" />
    </resultMap>



    <resultMap id="MainCostBaseResultMap" type="com.bbyb.joy.bms.domain.dto.BmsYscostMainInfo">
        <!--@mbg.generated-->
        <!--@Table bms_yscost_main_info-->
        <id column="pk_id" jdbcType="BIGINT" property="pkId" />
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="expenses_code" jdbcType="VARCHAR" property="expensesCode" />
        <result column="business_type" jdbcType="SMALLINT" property="businessType" />
        <result column="client_id" jdbcType="INTEGER" property="clientId" />
        <result column="company_id" jdbcType="INTEGER" property="companyId" />
        <result column="expenses_type" jdbcType="SMALLINT" property="expensesType" />
        <result column="cost_dimension" jdbcType="SMALLINT" property="costDimension" />
        <result column="charge_type" jdbcType="SMALLINT" property="chargeType" />
        <result column="expenses_mark" jdbcType="SMALLINT" property="feeFlag" />
        <result column="quoterule_id" jdbcType="VARCHAR" property="quoteruleId" />
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="freight" jdbcType="DECIMAL" property="freight" />
        <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
        <result column="ultrafar_fee" jdbcType="DECIMAL" property="ultrafarFee" />
        <result column="superframes_fee" jdbcType="DECIMAL" property="superframesFee" />
        <result column="excess_fee" jdbcType="DECIMAL" property="excessFee" />
        <result column="reduce_fee" jdbcType="DECIMAL" property="reduceFee" />
        <result column="outboundsorting_fee" jdbcType="DECIMAL" property="outboundsortingFee" />
        <result column="shortbarge_fee" jdbcType="DECIMAL" property="shortbargeFee" />
        <result column="return_fee" jdbcType="DECIMAL" property="returnFee" />
        <result column="exception_fee" jdbcType="DECIMAL" property="exceptionFee" />
        <result column="adjust_fee" jdbcType="DECIMAL" property="adjustFee" />
        <result column="adjust_remark" jdbcType="VARCHAR" property="adjustRemark" />
        <result column="other_cost1" jdbcType="DECIMAL" property="otherCost1" />
        <result column="other_cost2" jdbcType="DECIMAL" property="otherCost2" />
        <result column="other_cost3" jdbcType="DECIMAL" property="otherCost3" />
        <result column="other_cost4" jdbcType="DECIMAL" property="otherCost4" />
        <result column="other_cost5" jdbcType="DECIMAL" property="otherCost5" />
        <result column="other_cost6" jdbcType="DECIMAL" property="otherCost6" />
        <result column="other_cost7" jdbcType="DECIMAL" property="otherCost7" />
        <result column="other_cost8" jdbcType="DECIMAL" property="otherCost8" />
        <result column="other_cost9" jdbcType="DECIMAL" property="otherCost9" />
        <result column="other_cost10" jdbcType="DECIMAL" property="otherCost10" />
        <result column="other_cost11" jdbcType="DECIMAL" property="otherCost11" />
        <result column="other_cost12" jdbcType="DECIMAL" property="otherCost12" />
        <result column="sum_fee" jdbcType="DECIMAL" property="sumFee" />
        <result column="cost_attribute" jdbcType="SMALLINT" property="costAttribute" />
        <result column="total_weight" jdbcType="DECIMAL" property="totalWeight" />
        <result column="total_volume" jdbcType="DECIMAL" property="totalVolume" />
        <result column="total_boxes" jdbcType="DECIMAL" property="totalBoxes" />
        <result column="total_number" jdbcType="DECIMAL" property="totalNumber" />
        <result column="oper_code" jdbcType="VARCHAR" property="operCode" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="create_code" jdbcType="VARCHAR" property="createCode" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="bill_id" jdbcType="BIGINT" property="billId" />
        <result column="bill_date" jdbcType="VARCHAR" property="billDate" />
        <result column="business_time" jdbcType="TIMESTAMP" property="businessTime" />
        <result column="over_num" jdbcType="DECIMAL" property="overNum" />
        <result column="over_sendnum" jdbcType="INTEGER" property="overSendnum" />
        <result column="storage_fee_price" jdbcType="DECIMAL" property="storageFeePrice" />
        <result column="disposal_fee_price" jdbcType="DECIMAL" property="disposalFeePrice" />
        <result column="other_fee_remark" jdbcType="VARCHAR" property="otherFeeRemark" />
        <result column="fee_type_first" jdbcType="VARCHAR" property="feeTypeFirst" />
        <result column="fee_create_ate" jdbcType="VARCHAR" property="feeCreateAte" />
        <result column="is_increment" jdbcType="TINYINT" property="isIncrement" />
        <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
        <result column="signing_date" jdbcType="TIMESTAMP" property="signingDate" />
        <result column="quoteruledetail_id" jdbcType="VARCHAR" property="quoteruledetailId" />
        <result column="show_bill_id" jdbcType="INTEGER" property="showBillId" />
        <result column="show_bill_code" jdbcType="VARCHAR" property="showBillCode" />
        <result column="extra_field1" jdbcType="DECIMAL" property="extraField1" />
        <result column="warehouse_code_arr" jdbcType="VARCHAR" property="warehouseCodeArr" />
        <result column="settle_setting" jdbcType="SMALLINT" property="settleSetting" />
    </resultMap>


    <sql id="selectBmsYscostInfoVo">
        select pk_id,
               id,
               expenses_code,
               client_id,
               company_id         as institution_id,
               company_id         ,
               expenses_type,
               expenses_dimension as cost_dimension,
               charge_type,
               expenses_mark      as expenses_mark,
               quoter_rule_id as quoterule_id,
               quoter_rule_name as rule_name,
               remarks,
               freight,
               delivery_fee,
               ultrafar_fee,
               superframes_fee,
               excess_fee,
               reduce_fee,
               outboundsorting_fee,
               shortbarge_fee,
               return_fee,
               exception_fee,
               adjust_fee,
               adjust_remark,
               other_cost1,
               other_cost2,
               other_cost3,
               other_cost4,
               other_cost5,
               other_cost6,
               other_cost7,
               other_cost8,
               other_cost9,
               other_cost10,
               other_cost11,
               other_cost12,
               oper_code,
               oper_by,
               oper_time,
               del_flag,
               bill_id,
               bill_date,
               business_time,
               fee_type_first,
               is_increment,
               order_date,
               signing_date,
               quoter_rule_detail_id as quoteruledetail_id,
               show_bill_id,
               show_bill_code,
               extra_field1,
               warehouse_name as warehouse_code_arr,
               settle_type,
               settle_amount,
               main_code_id as main_expense_id,
               settle_main_id
        from bms_yscost_info
    </sql>

    <select id="selectBmsYscostInfoList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostInfo" resultMap="BmsYscostInfoResult">
        <include refid="selectBmsYscostInfoVo"/>
        <where>
            <if test="expensesCode != null  and expensesCode != ''"> and expenses_code = #{expensesCode}</if>
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="clientId != null "> and client_id = #{clientId}</if>
            <if test="institutionId != null "> and company_id = #{institutionId}</if>
            <if test="expensesType != null "> and expenses_type = #{expensesType}</if>
            <if test="costDimension != null "> and cost_dimension = #{costDimension}</if>
            <if test="chargeType != null "> and charge_type = #{chargeType}</if>
            <if test="feeFlag != null "> and expenses_mark = #{feeFlag}</if>
            <if test="quoteruleId != null  and quoteruleId != ''"> and quoterule_id = #{quoteruleId}</if>
            <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="freight != null "> and freight = #{freight}</if>
            <if test="ultrafarFee != null "> and ultrafar_fee = #{ultrafarFee}</if>
            <if test="superframesFee != null "> and superframes_fee = #{superframesFee}</if>
            <if test="excessFee != null "> and excess_fee = #{excessFee}</if>
            <if test="deliveryFee != null "> and delivery_fee = #{deliveryFee}</if>
            <if test="outboundsortingFee != null "> and outboundsorting_fee = #{outboundsortingFee}</if>
            <if test="shortbargeFee != null "> and shortbarge_fee = #{shortbargeFee}</if>
            <if test="returnFee != null "> and return_fee = #{returnFee}</if>
            <if test="exceptionFee != null "> and exception_fee = #{exceptionFee}</if>
            <if test="otherCost1 != null "> and other_cost1 = #{otherCost1}</if>
            <if test="otherCost2 != null "> and other_cost2 = #{otherCost2}</if>
            <if test="otherCost3 != null "> and other_cost3 = #{otherCost3}</if>
            <if test="otherCost4 != null "> and other_cost4 = #{otherCost4}</if>
            <if test="otherCost5 != null "> and other_cost5 = #{otherCost5}</if>
            <if test="otherCost6 != null "> and other_cost6 = #{otherCost6}</if>
            <if test="otherCost7 != null "> and other_cost7 = #{otherCost7}</if>
            <if test="otherCost8 != null "> and other_cost8 = #{otherCost8}</if>
            <if test="otherCost9 != null "> and other_cost9 = #{otherCost9}</if>
            <if test="otherCost10 != null "> and other_cost10 = #{otherCost10}</if>
            <if test="otherCost11 != null "> and other_cost11 = #{otherCost11}</if>
            <if test="otherCost12 != null "> and other_cost12 = #{otherCost12}</if>
            <if test="costAttribute != null "> and cost_attribute = #{costAttribute}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
            <if test="billId != null "> and bill_id = #{billId}</if>
            <if test="billDate != null  and billDate != ''"> and bill_date = #{billDate}</if>
            <if test="businessTime != null "> and business_time = #{businessTime}</if>
            <if test="settleType != null">and settle_type = #{settleType}}</if>
            <if test="mainExpenseId!=null">and main_expense_id = #{mainExpenseId}</if>
            <if test="settleMainId!=null">and settle_main_id = #{settleMainId}</if>
        </where>
    </select>

    <select id="selectBmsYsCostInfoListByID"  resultMap="BmsYscostInfoResult">
        <include refid="selectBmsYscostInfoVo"/>
        where del_flag = 0 and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="selectBmsYscostInfoListByMainID"  resultMap="BmsYscostInfoResult">
        <include refid="selectBmsYscostInfoVo"/>
        where del_flag = 0 and main_expense_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="selectBmsYscostMainInfoListByID"  resultMap="MainCostBaseResultMap">
        select
        pk_id, id, expenses_code, business_type, client_id, company_id, expenses_type,
        cost_dimension, charge_type, expenses_mark, quoterule_id, rule_name, remarks, freight,
        delivery_fee, ultrafar_fee, superframes_fee, excess_fee, reduce_fee, outboundsorting_fee,
        shortbarge_fee, return_fee, exception_fee, adjust_fee, adjust_remark, other_cost1,
        other_cost2, other_cost3, other_cost4, other_cost5, other_cost6, other_cost7, other_cost8,
        other_cost9, other_cost10, other_cost11, other_cost12, sum_fee, cost_attribute, total_weight,
        total_volume, total_boxes, total_number, oper_code, oper_by, oper_time, create_code,
        create_by, create_time, del_flag, bill_id, bill_date, business_time, over_num, over_sendnum,
        storage_fee_price, disposal_fee_price, other_fee_remark, fee_type_first, fee_create_ate,
        is_increment, order_date, signing_date, quoteruledetail_id, show_bill_id, show_bill_code,
        extra_field1, warehouse_code_arr, settle_setting
        from bms_yscost_main_info
        where del_flag = 0 and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="getCostInfoByCode"  resultMap="BmsYscostInfoResult">
        <include refid="selectBmsYscostInfoVo"/>
        where del_flag = 0 and expenses_code = #{code}
    </select>

    <select id="getPaymentDays" parameterType="java.lang.Integer"  resultType="java.lang.String">
        select  payment_days
        from bms_clientinfo
        where
        id = #{id}
    </select>
    <select id="getPaymentDaysByCode" parameterType="java.lang.String"  resultType="java.lang.String">
        select  payment_days
        from bms_clientinfo
        where
        client_code = #{clientCode}
    </select>

    <select id="getcodeListGroupcompanyByClientIds" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
         SUM(
         IFNULL(freight,0) +
         IFNULL(ultrafar_fee,0) +
         IFNULL(superframes_fee,0) +
         IFNULL(excess_fee,0) +
         IFNULL(delivery_fee,0) +
         IFNULL(outboundsorting_fee,0) +
         IFNULL(shortbarge_fee,0) +
         IFNULL(return_fee,0) +
         IFNULL(exception_fee,0) +
         IFNULL(adjust_fee,0) +
         IFNULL(other_cost1,0) +
         IFNULL(other_cost2,0) +
         IFNULL(other_cost3,0) +
         IFNULL(other_cost4,0) +
         IFNULL(other_cost5,0) +
         IFNULL(other_cost6,0) +
         IFNULL(other_cost7,0) +
         IFNULL(other_cost8,0) +
         IFNULL(other_cost9,0) +
         IFNULL(other_cost10,0) +
         IFNULL(other_cost11,0) +
         IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        company_id as companyId
         from bms_yscost_info
        where
        client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        group by company_id,bill_date
    </select>

    <select id="getcodeListGroupcompanyByClientAndStockIds" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        "1" as billType,
        company_id as companyId
        from bms_yscost_info
        where
        client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        and expenses_type = 1
        group by company_id,bill_date

        union all

        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        2 as billType,
        company_id as companyId
        from bms_yscost_info
        where client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND expenses_mark = 1
        AND del_flag = 0
        AND bill_id IS NULL
        AND expenses_type IN (2,3,4)
        GROUP BY company_id,bill_date
    </select>

    <select id="getcodeListGroupWarehousByClientAndStockIds" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        "1" as billType,
        company_id as companyId,
        '' as warehouseCode,
        '' as warehouseName
        from bms_yscost_info
        where
        client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        and expenses_type = 1
        group by company_id,bill_date

        union all

        select
        SUM(
            IFNULL(a.freight,0) +
            IFNULL(a.ultrafar_fee,0) +
            IFNULL(a.superframes_fee,0) +
            IFNULL(a.excess_fee,0) +
            IFNULL(a.delivery_fee,0) +
            IFNULL(a.outboundsorting_fee,0) +
            IFNULL(a.shortbarge_fee,0) +
            IFNULL(a.return_fee,0) +
            IFNULL(a.exception_fee,0) +
            IFNULL(a.adjust_fee,0) +
            IFNULL(a.other_cost1,0) +
            IFNULL(a.other_cost2,0) +
            IFNULL(a.other_cost3,0) +
            IFNULL(a.other_cost4,0) +
            IFNULL(a.other_cost5,0) +
            IFNULL(a.other_cost6,0) +
            IFNULL(a.other_cost7,0) +
            IFNULL(a.other_cost8,0) +
            IFNULL(a.other_cost9,0) +
            IFNULL(a.other_cost10,0) +
            IFNULL(a.other_cost11,0) +
            IFNULL(a.other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        a.bill_date as billDate,
        2 as billType,
        a.company_id as companyId,
        b.warehouse_code as warehouseCode,
        b.warehouse_name as warehouseName
        from bms_yscost_info a
        left join bms_yscost_extend b
        on a.id = b.expenses_id
        where
        a.client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(a.expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(a.bill_date,'') =#{billDate}
        </if>
        and IFNULL(a.del_flag,'0') = '0'
        and IFNULL(a.bill_id,0)=0
        and a.expenses_type in(2,3,4)
        group by a.company_id,a.bill_date,b.warehouse_code

    </select>


    <select id="getcodeListGroupcompanyByClientAndCompany" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        company_id as companyId
        from bms_yscost_info
        where
        client_id =#{id}
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        group by company_id,bill_date
    </select>

    <select id="getcodeListGroupcompanyByClientAndCompanyAndStock" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        "1" as billType,
        company_id as companyId
        from bms_yscost_info
        where
        client_id =#{id}
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        and expenses_type = 1
        group by company_id,bill_date

        union all

        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        2 as billType,
        company_id as companyId
        from bms_yscost_info
        where
        client_id =#{id}
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>

        AND del_flag = 0
        AND bill_id IS NULL
        and expenses_type in(2,3,4)
        group by company_id,bill_date
    </select>

    <select id="getcodeListGroupWareByClientAndCompanyAndStock" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        "1" as billType,
        company_id as companyId,
        "" as warehouseCode,
        "" as warehouseName
        from bms_yscost_info
        where
        client_id =#{id}
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        and expenses_type = 1
        group by company_id,bill_date

        union all

        select
        SUM(
            IFNULL(a.freight,0) +
            IFNULL(a.ultrafar_fee,0) +
            IFNULL(a.superframes_fee,0) +
            IFNULL(a.excess_fee,0) +
            IFNULL(a.delivery_fee,0) +
            IFNULL(a.outboundsorting_fee,0) +
            IFNULL(a.shortbarge_fee,0) +
            IFNULL(a.return_fee,0) +
            IFNULL(a.exception_fee,0) +
            IFNULL(a.adjust_fee,0) +
            IFNULL(a.other_cost1,0) +
            IFNULL(a.other_cost2,0) +
            IFNULL(a.other_cost3,0) +
            IFNULL(a.other_cost4,0) +
            IFNULL(a.other_cost5,0) +
            IFNULL(a.other_cost6,0) +
            IFNULL(a.other_cost7,0) +
            IFNULL(a.other_cost8,0) +
            IFNULL(a.other_cost9,0) +
            IFNULL(a.other_cost10,0) +
            IFNULL(a.other_cost11,0) +
            IFNULL(a.other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        a.bill_date as billDate,
        2 as billType,
        a.company_id as companyId,
        b.warehouse_code as warehouseCode,
        b.warehouse_name as warehouseName
        from bms_yscost_info a
        left join bms_yscost_extend  b
        on a.id = b.expenses_id
        where
        a.client_id =#{id}
        and IFNULL(a.expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(a.bill_date,'') =#{billDate}
        </if>

        and IFNULL(a.del_flag,'0') = '0'
        and IFNULL(a.bill_id,0)=0
        and a.expenses_type in(2,3,4)
        group by a.company_id,a.bill_date,b.warehouse_code
    </select>



    <select id="getcodeListGroupcompanyByClient" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        /*institution_id as companyId*/
        100 as companyId
        from bms_yscost_info
        where
        client_id =#{id}
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        group by bill_date
    </select>

    <select id="getcodeListGroupcompanyAndStockByClient" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        "1" as billType,
        100 as companyId
        from bms_yscost_info
        where
        client_id =#{id}
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        and expenses_type = 1
        group by bill_date

        union all

        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        2 as billType,
        100 as companyId
        from bms_yscost_info
        where
        client_id =#{id}
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        and expenses_type in (2,3,4)
        group by bill_date
    </select>

    <select id="getcodeListGroupcompanyAndWareByClient" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        "1" as billType,
        /*institution_id as companyId*/
        100 as companyId,
        "" as warehouseCode,
        "" as warehouseName
        from bms_yscost_info
        where
        client_id =#{id}
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        and expenses_type = 1
        group by bill_date

        union all

        select
        SUM(
            IFNULL(a.freight,0) +
            IFNULL(a.ultrafar_fee,0) +
            IFNULL(a.superframes_fee,0) +
            IFNULL(a.excess_fee,0) +
            IFNULL(a.delivery_fee,0) +
            IFNULL(a.outboundsorting_fee,0) +
            IFNULL(a.shortbarge_fee,0) +
            IFNULL(a.return_fee,0) +
            IFNULL(a.exception_fee,0) +
            IFNULL(a.adjust_fee,0) +
            IFNULL(a.other_cost1,0) +
            IFNULL(a.other_cost2,0) +
            IFNULL(a.other_cost3,0) +
            IFNULL(a.other_cost4,0) +
            IFNULL(a.other_cost5,0) +
            IFNULL(a.other_cost6,0) +
            IFNULL(a.other_cost7,0) +
            IFNULL(a.other_cost8,0) +
            IFNULL(a.other_cost9,0) +
            IFNULL(a.other_cost10,0) +
            IFNULL(a.other_cost11,0) +
            IFNULL(a.other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        a.bill_date as billDate,
        2 as billType,
        100 as companyId,
        b.warehouse_code as warehouseCode,
        b.warehouse_name as warehouseName
        from bms_yscost_info a
        left join bms_yscost_extend  b
        on a.id = b.expenses_id
        where
        a.client_id =#{id}
        and IFNULL(a.expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(a.bill_date,'') =#{billDate}
        </if>
        and IFNULL(a.del_flag,'0') = '0'
        and IFNULL(a.bill_id,0)=0
        and a.expenses_type in (2,3,4)
        group by a.bill_date,b.warehouse_code
    </select>


    <select id="getcodeListGroupByIds" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        "1" as billType,
        /*institution_id as companyId*/
        100 as companyId
        from bms_yscost_info
        where
        client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        group by bill_date
    </select>

    <select id="getcodeListGroupAndSotckByIds" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        100 as companyId,
        1 as billType
        from bms_yscost_info
        where
        client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        and expenses_type = 1
        group by bill_date
        union all
        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        100 as companyId,
        2 as billType
        from bms_yscost_info
        where
        client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        and expenses_type in (2,3,4)
        group by bill_date
    </select>

    <select id="getcodeListGroupAndWarehouseByIds" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
        SUM(
        IFNULL(freight,0) +
        IFNULL(ultrafar_fee,0) +
        IFNULL(superframes_fee,0) +
        IFNULL(excess_fee,0) +
        IFNULL(delivery_fee,0) +
        IFNULL(outboundsorting_fee,0) +
        IFNULL(shortbarge_fee,0) +
        IFNULL(return_fee,0) +
        IFNULL(exception_fee,0) +
        IFNULL(adjust_fee,0) +
        IFNULL(other_cost1,0) +
        IFNULL(other_cost2,0) +
        IFNULL(other_cost3,0) +
        IFNULL(other_cost4,0) +
        IFNULL(other_cost5,0) +
        IFNULL(other_cost6,0) +
        IFNULL(other_cost7,0) +
        IFNULL(other_cost8,0) +
        IFNULL(other_cost9,0) +
        IFNULL(other_cost10,0) +
        IFNULL(other_cost11,0) +
        IFNULL(other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        bill_date as billDate,
        100 as companyId,
        1 as billType,
        "" as warehouseCode,
        "" as warehouseName
        from bms_yscost_info
        where
        client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(bill_date,'') =#{billDate}
        </if>
        AND del_flag = 0
        AND bill_id IS NULL
        and expenses_type = 1
        group by bill_date

        union all

        select
            SUM(
            IFNULL(a.freight,0) +
            IFNULL(a.ultrafar_fee,0) +
            IFNULL(a.superframes_fee,0) +
            IFNULL(a.excess_fee,0) +
            IFNULL(a.delivery_fee,0) +
            IFNULL(a.outboundsorting_fee,0) +
            IFNULL(a.shortbarge_fee,0) +
            IFNULL(a.return_fee,0) +
            IFNULL(a.exception_fee,0) +
            IFNULL(a.adjust_fee,0) +
            IFNULL(a.other_cost1,0) +
            IFNULL(a.other_cost2,0) +
            IFNULL(a.other_cost3,0) +
            IFNULL(a.other_cost4,0) +
            IFNULL(a.other_cost5,0) +
            IFNULL(a.other_cost6,0) +
            IFNULL(a.other_cost7,0) +
            IFNULL(a.other_cost8,0) +
            IFNULL(a.other_cost9,0) +
            IFNULL(a.other_cost10,0) +
            IFNULL(a.other_cost11,0) +
            IFNULL(a.other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        a.bill_date as billDate,
        100 as companyId,
        2 as billType,
        b.warehouse_code as warehouseCode,
        b.warehouse_name as warehouseName
        from bms_yscost_info a
        left join bms_yscost_extend  b
        on a.id = b.expenses_id
        where
        a.client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IFNULL(a.expenses_mark,1) = 1
        <if test="billDate!=null">
            and IFNULL(a.bill_date,'') =#{billDate}
        </if>
        and IFNULL(a.del_flag,'0') = '0'
        and IFNULL(a.bill_id,0)=0
        and a.expenses_type in (2,3,4)
        group by a.bill_date,b.warehouse_code
    </select>


    <select id="selectBmsYscostInfoById" parameterType="java.lang.String" resultMap="BmsYscostInfoResult">
        select
        a.id,
        a.pk_id,
        a.expenses_code,
        a.business_type,
        a.client_id,
        a.company_id,
        a.expenses_type,
        a.cost_dimension,
        a.charge_type,
        a.expenses_mark,
        a.quoterule_id,
        a.rule_name,
        a.remarks,
        IFNULL(a.freight,0) as freight,
        IFNULL(a.ultrafar_fee,0) as ultrafar_fee,
        IFNULL(a.superframes_fee,0) as superframes_fee,
        IFNULL(a.excess_fee,0) as excess_fee,
        IFNULL(a.delivery_fee,0) as delivery_fee,
        IFNULL(a.outboundsorting_fee,0) as outboundsorting_fee,
        IFNULL(a.shortbarge_fee,0) as shortbarge_fee,
        IFNULL(a.return_fee,0) as return_fee,
        IFNULL(a.exception_fee,0) as exception_fee,
        IFNULL(a.other_cost1,0) as other_cost1,
        IFNULL(a.other_cost2,0) as other_cost2,
        IFNULL(a.other_cost3,0) as other_cost3,
        IFNULL(a.other_cost4,0) as other_cost4,
        IFNULL(a.other_cost5,0) as other_cost5,
        IFNULL(a.other_cost6,0) as other_cost6,
        IFNULL(a.other_cost7,0) as other_cost7,
        IFNULL(a.other_cost8,0) as other_cost8,
        IFNULL(a.other_cost9,0) as other_cost9,
        IFNULL(a.other_cost10,0) as other_cost10,
        IFNULL(a.other_cost11,0) as other_cost11,
        IFNULL(a.other_cost12,0) as other_cost12,
        a.cost_attribute,
        a.oper_code,
        a.oper_by,
        a.oper_time,
        a.del_flag,
        a.bill_id,
        a.bill_date,
        a.business_time AS businessTime
        from bms_yscost_info a
        left join bms_ysbillmain b
        on a.bill_id = b.id

        where a.id = #{id}
    </select>

    <insert id="insertBmsYscostInfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostInfo" useGeneratedKeys="true" keyProperty="pkId">
        insert into bms_yscost_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="expensesCode != null">expenses_code,</if>
            <if test="businessType != null">business_type,</if>
            <if test="clientId != null">client_id,</if>
            <if test="institutionId != null">company_id,</if>
            <if test="expensesType != null">expenses_type,</if>
            <if test="costDimension != null">cost_dimension,</if>
            <if test="chargeType != null">charge_type,</if>
            <if test="feeFlag != null">expenses_mark,</if>
            <if test="quoteruleId != null">quoterule_id,</if>
            <if test="ruleName != null">rule_name,</if>
            <if test="remarks != null">remarks,</if>
            <if test="adjustRemark != null">adjust_remark,</if>
            <if test="freight != null">freight,</if>
            <if test="ultrafarFee != null">ultrafar_fee,</if>
            <if test="superframesFee != null">superframes_fee,</if>
            <if test="excessFee != null">excess_fee,</if>
            <if test="deliveryFee != null">delivery_fee,</if>
            <if test="outboundsortingFee != null">outboundsorting_fee,</if>
            <if test="shortbargeFee != null">shortbarge_fee,</if>
            <if test="returnFee != null">return_fee,</if>
            <if test="exceptionFee != null">exception_fee,</if>
            <if test="adjustFee != null">adjust_fee,</if>
            <if test="otherCost1 != null">other_cost1,</if>
            <if test="otherCost2 != null">other_cost2,</if>
            <if test="otherCost3 != null">other_cost3,</if>
            <if test="otherCost4 != null">other_cost4,</if>
            <if test="otherCost5 != null">other_cost5,</if>
            <if test="otherCost6 != null">other_cost6,</if>
            <if test="otherCost7 != null">other_cost7,</if>
            <if test="otherCost8 != null">other_cost8,</if>
            <if test="otherCost9 != null">other_cost9,</if>
            <if test="otherCost10 != null">other_cost10,</if>
            <if test="otherCost11 != null">other_cost11,</if>
            <if test="otherCost12 != null">other_cost12,</if>
            <if test="costAttribute != null">cost_attribute,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="billId != null">bill_id,</if>
            <if test="billDate != null">bill_date,</if>
            <if test="businessTime != null">business_time,</if>
            <if test="reduceFee != null">reduce_fee,</if>
            <if test="overNum != null">over_num,</if>
            <if test="overSendnum != null">over_sendnum,</if>
            <if test="storageFeePrice != null">storage_fee_price,</if>
            <if test="disposalFeePrice != null">disposal_fee_price,</if>
            <if test="feeTypeFirst != null">fee_type_first,</if>
            <if test="feeCreateDate != null">fee_create_ate,</if>
            <if test="isIncrement != null">is_increment,</if>
            <if test="otherFeeRemark != null">other_fee_remark,</if>
            <if test="orderDate != null">order_date,</if>
            <if test="signingDate != null">signing_date,</if>
            <if test="mainExpenseId != null">main_expense_id,</if>
            <if test="settleAmount != null">settle_amount,</if>
            <if test="warehouseCodeArr != null">warehouse_code_arr,</if>
            <if test="settleMainId != null">settle_main_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="expensesCode != null">#{expensesCode},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="institutionId != null">#{institutionId},</if>
            <if test="expensesType != null">#{expensesType},</if>
            <if test="costDimension != null">#{costDimension},</if>
            <if test="chargeType != null">#{chargeType},</if>
            <if test="feeFlag != null">#{feeFlag},</if>
            <if test="quoteruleId != null">#{quoteruleId},</if>
            <if test="ruleName != null">#{ruleName},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="adjustRemark != null">#{adjustRemark},</if>
            <if test="freight != null">#{freight},</if>
            <if test="ultrafarFee != null">#{ultrafarFee},</if>
            <if test="superframesFee != null">#{superframesFee},</if>
            <if test="excessFee != null">#{excessFee},</if>
            <if test="deliveryFee != null">#{deliveryFee},</if>
            <if test="outboundsortingFee != null">#{outboundsortingFee},</if>
            <if test="shortbargeFee != null">#{shortbargeFee},</if>
            <if test="returnFee != null">#{returnFee},</if>
            <if test="exceptionFee != null">#{exceptionFee},</if>
            <if test="adjustFee != null">#{adjustFee},</if>
            <if test="otherCost1 != null">#{otherCost1},</if>
            <if test="otherCost2 != null">#{otherCost2},</if>
            <if test="otherCost3 != null">#{otherCost3},</if>
            <if test="otherCost4 != null">#{otherCost4},</if>
            <if test="otherCost5 != null">#{otherCost5},</if>
            <if test="otherCost6 != null">#{otherCost6},</if>
            <if test="otherCost7 != null">#{otherCost7},</if>
            <if test="otherCost8 != null">#{otherCost8},</if>
            <if test="otherCost9 != null">#{otherCost9},</if>
            <if test="otherCost10 != null">#{otherCost10},</if>
            <if test="otherCost11 != null">#{otherCost11},</if>
            <if test="otherCost12 != null">#{otherCost12},</if>
            <if test="costAttribute != null">#{costAttribute},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="billId != null">#{billId},</if>
            <if test="billDate != null">#{billDate},</if>
            <if test="businessTime != null">#{businessTime},</if>
            <if test="reduceFee != null">#{reduceFee},</if>
            <if test="overNum != null">#{overNum},</if>
            <if test="overSendnum != null">#{overSendnum},</if>
            <if test="storageFeePrice != null">#{storageFeePrice},</if>
            <if test="disposalFeePrice != null">#{disposalFeePrice},</if>
            <if test="feeTypeFirst != null">#{feeTypeFirst},</if>
            <if test="feeCreateDate != null">#{feeCreateDate},</if>
            <if test="isIncrement != null">#{isIncrement},</if>
            <if test="otherFeeRemark != null">#{otherFeeRemark},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="signingDate != null">#{signingDate},</if>
            <if test="mainExpenseId != null">#{mainExpenseId},</if>
            <if test="settleAmount != null">#{settleAmount},</if>
            <if test="warehouseCodeArr != null">#{warehouseCodeArr},</if>
            <if test="settleMainId != null">#{settleMainId},</if>
        </trim>
    </insert>

    <update id="updateBmsYscostInfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostInfo">
        update bms_yscost_info
        <set>
            <if test="id != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="expensesCode != null">
                expenses_code = #{expensesCode,jdbcType=VARCHAR},
            </if>
            <if test="mainCodeId != null">
                main_code_id = #{mainCodeId,jdbcType=VARCHAR},
            </if>
            <if test="mainPkId != null">
                main_pk_id = #{mainPkId,jdbcType=INTEGER},
            </if>
            <if test="expensesType != null">
                expenses_type = #{expensesType,jdbcType=INTEGER},
            </if>
            <if test="expensesDimension != null">
                expenses_dimension = #{expensesDimension,jdbcType=INTEGER},
            </if>
            <if test="expensesMark != null">
                expenses_mark = #{expensesMark,jdbcType=INTEGER},
            </if>
            <if test="expensesAttribute != null">
                expenses_attribute = #{expensesAttribute,jdbcType=INTEGER},
            </if>
            <if test="feeTypeFirst != null">
                fee_type_first = #{feeTypeFirst,jdbcType=INTEGER},
            </if>
            <if test="isIncrement != null">
                is_increment = #{isIncrement,jdbcType=INTEGER},
            </if>
            <if test="chargeType != null">
                charge_type = #{chargeType,jdbcType=INTEGER},
            </if>
            <if test="businessTime != null">
                business_time = #{businessTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderDate != null">
                order_date = #{orderDate,jdbcType=TIMESTAMP},
            </if>
            <if test="signingDate != null">
                signing_date = #{signingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="clientId != null">
                client_id = #{clientId,jdbcType=INTEGER},
            </if>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="warehouseCode != null">
                warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="warehouseName != null">
                warehouse_name = #{warehouseName,jdbcType=VARCHAR},
            </if>
            <if test="billId != null">
                bill_id = #{billId,jdbcType=INTEGER},
            </if>
            <if test="billDate != null">
                bill_date = #{billDate,jdbcType=VARCHAR},
            </if>
            <if test="showBillId != null">
                show_bill_id = #{showBillId,jdbcType=INTEGER},
            </if>
            <if test="showBillCode != null">
                show_bill_code = #{showBillCode,jdbcType=VARCHAR},
            </if>
            <if test="quoterRuleId != null">
                quoter_rule_id = #{quoterRuleId,jdbcType=VARCHAR},
            </if>
            <if test="quoterRuleName != null">
                quoter_rule_name = #{quoterRuleName,jdbcType=VARCHAR},
            </if>
            <if test="quoterRuleDetailId != null">
                quoter_rule_detail_id = #{quoterRuleDetailId,jdbcType=VARCHAR},
            </if>
            <if test="quoterRuleDetailName != null">
                quoter_rule_detail_name = #{quoterRuleDetailName,jdbcType=VARCHAR},
            </if>
            <if test="totalCodeNumber != null">
                total_code_number = #{totalCodeNumber,jdbcType=INTEGER},
            </if>
            <if test="totalSkuNumber != null">
                total_sku_number = #{totalSkuNumber,jdbcType=INTEGER},
            </if>
            <if test="totalBoxes != null">
                total_boxes = #{totalBoxes,jdbcType=DECIMAL},
            </if>
            <if test="totalNumber != null">
                total_number = #{totalNumber,jdbcType=DECIMAL},
            </if>
            <if test="totalWeight != null">
                total_weight = #{totalWeight,jdbcType=DECIMAL},
            </if>
            <if test="totalVolume != null">
                total_volume = #{totalVolume,jdbcType=DECIMAL},
            </if>
            <if test="totalCargoValue != null">
                total_cargo_value = #{totalCargoValue,jdbcType=DECIMAL},
            </if>
            <if test="totalExtraFeeAmount != null">
                total_extra_fee_amount = #{totalExtraFeeAmount,jdbcType=DECIMAL},
            </if>
            <if test="totalExtraFeeNumber != null">
                total_extra_fee_number = #{totalExtraFeeNumber,jdbcType=DECIMAL},
            </if>
            <if test="totalExtraFeePrice != null">
                total_extra_fee_price = #{totalExtraFeePrice,jdbcType=DECIMAL},
            </if>
            <if test="extraFeeRemark != null">
                extra_fee_remark = #{extraFeeRemark,jdbcType=VARCHAR},
            </if>
            <if test="totalOverNum != null">
                total_over_num = #{totalOverNum,jdbcType=DECIMAL},
            </if>
            <if test="totalOverStoreNum != null">
                total_over_store_num = #{totalOverStoreNum,jdbcType=INTEGER},
            </if>
            <if test="extraField1 != null">
                extra_field1 = #{extraField1,jdbcType=DECIMAL},
            </if>
            <if test="settleType != null">
                settle_type = #{settleType,jdbcType=INTEGER},
            </if>
            <if test="settleEntityType != null">
                settle_entity_type = #{settleEntityType,jdbcType=INTEGER},
            </if>
            <if test="settleSetting != null">
                settle_setting = #{settleSetting,jdbcType=INTEGER},
            </if>
            <if test="settleMainId != null">
                settle_main_id = #{settleMainId,jdbcType=INTEGER},
            </if>
            <if test="settleAmount != null">
                settle_amount = #{settleAmount,jdbcType=DECIMAL},
            </if>
            <if test="freight != null">
                freight = #{freight,jdbcType=DECIMAL},
            </if>
            <if test="deliveryFee != null">
                delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
            </if>
            <if test="ultrafarFee != null">
                ultrafar_fee = #{ultrafarFee,jdbcType=DECIMAL},
            </if>
            <if test="superframesFee != null">
                superframes_fee = #{superframesFee,jdbcType=DECIMAL},
            </if>
            <if test="excessFee != null">
                excess_fee = #{excessFee,jdbcType=DECIMAL},
            </if>
            <if test="reduceFee != null">
                reduce_fee = #{reduceFee,jdbcType=DECIMAL},
            </if>
            <if test="outboundsortingFee != null">
                outboundsorting_fee = #{outboundsortingFee,jdbcType=DECIMAL},
            </if>
            <if test="shortbargeFee != null">
                shortbarge_fee = #{shortbargeFee,jdbcType=DECIMAL},
            </if>
            <if test="returnFee != null">
                return_fee = #{returnFee,jdbcType=DECIMAL},
            </if>
            <if test="exceptionFee != null">
                exception_fee = #{exceptionFee,jdbcType=DECIMAL},
            </if>
            <if test="adjustFee != null">
                adjust_fee = #{adjustFee,jdbcType=DECIMAL},
            </if>
            <if test="adjustRemark != null">
                adjust_remark = #{adjustRemark,jdbcType=VARCHAR},
            </if>
            <if test="otherCost1 != null">
                other_cost1 = #{otherCost1,jdbcType=DECIMAL},
            </if>
            <if test="otherCost2 != null">
                other_cost2 = #{otherCost2,jdbcType=DECIMAL},
            </if>
            <if test="otherCost3 != null">
                other_cost3 = #{otherCost3,jdbcType=DECIMAL},
            </if>
            <if test="otherCost4 != null">
                other_cost4 = #{otherCost4,jdbcType=DECIMAL},
            </if>
            <if test="otherCost5 != null">
                other_cost5 = #{otherCost5,jdbcType=DECIMAL},
            </if>
            <if test="otherCost6 != null">
                other_cost6 = #{otherCost6,jdbcType=DECIMAL},
            </if>
            <if test="otherCost7 != null">
                other_cost7 = #{otherCost7,jdbcType=DECIMAL},
            </if>
            <if test="otherCost8 != null">
                other_cost8 = #{otherCost8,jdbcType=DECIMAL},
            </if>
            <if test="otherCost9 != null">
                other_cost9 = #{otherCost9,jdbcType=DECIMAL},
            </if>
            <if test="otherCost10 != null">
                other_cost10 = #{otherCost10,jdbcType=DECIMAL},
            </if>
            <if test="otherCost11 != null">
                other_cost11 = #{otherCost11,jdbcType=DECIMAL},
            </if>
            <if test="otherCost12 != null">
                other_cost12 = #{otherCost12,jdbcType=DECIMAL},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="createCode != null">
                create_code = #{createCode,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operCode != null">
                oper_code = #{operCode,jdbcType=VARCHAR},
            </if>
            <if test="operBy != null">
                oper_by = #{operBy,jdbcType=VARCHAR},
            </if>
            <if test="operTime != null">
                oper_time = #{operTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="optMonth != null">
                opt_month = #{optMonth,jdbcType=INTEGER},
            </if>
            <if test="optDay != null">
                opt_day = #{optDay,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id}
    </update>



    <update id="flushBmsYsFee" parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostInfo">
        update bms_yscost_info
        <set>
            <if test="id != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="expensesCode != null">
                expenses_code = #{expensesCode,jdbcType=VARCHAR},
            </if>
            <if test="mainCodeId != null">
                main_code_id = #{mainCodeId,jdbcType=VARCHAR},
            </if>
            <if test="mainPkId != null">
                main_pk_id = #{mainPkId,jdbcType=INTEGER},
            </if>
            <if test="expensesType != null">
                expenses_type = #{expensesType,jdbcType=INTEGER},
            </if>
            <if test="expensesDimension != null">
                expenses_dimension = #{expensesDimension,jdbcType=INTEGER},
            </if>
            <if test="expensesMark != null">
                expenses_mark = #{expensesMark,jdbcType=INTEGER},
            </if>
            <if test="expensesAttribute != null">
                expenses_attribute = #{expensesAttribute,jdbcType=INTEGER},
            </if>
            <if test="feeTypeFirst != null">
                fee_type_first = #{feeTypeFirst,jdbcType=INTEGER},
            </if>
            <if test="isIncrement != null">
                is_increment = #{isIncrement,jdbcType=INTEGER},
            </if>
            <if test="chargeType != null">
                charge_type = #{chargeType,jdbcType=INTEGER},
            </if>
            <if test="businessTime != null">
                business_time = #{businessTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderDate != null">
                order_date = #{orderDate,jdbcType=TIMESTAMP},
            </if>
            <if test="signingDate != null">
                signing_date = #{signingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="clientId != null">
                client_id = #{clientId,jdbcType=INTEGER},
            </if>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="warehouseCode != null">
                warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="warehouseName != null">
                warehouse_name = #{warehouseName,jdbcType=VARCHAR},
            </if>
            <if test="billId != null">
                bill_id = #{billId,jdbcType=INTEGER},
            </if>
            <if test="billDate != null">
                bill_date = #{billDate,jdbcType=VARCHAR},
            </if>
            <if test="showBillId != null">
                show_bill_id = #{showBillId,jdbcType=INTEGER},
            </if>
            <if test="showBillCode != null">
                show_bill_code = #{showBillCode,jdbcType=VARCHAR},
            </if>
            <if test="quoterRuleId != null">
                quoter_rule_id = #{quoterRuleId,jdbcType=VARCHAR},
            </if>
            <if test="quoterRuleName != null">
                quoter_rule_name = #{quoterRuleName,jdbcType=VARCHAR},
            </if>
            <if test="quoterRuleDetailId != null">
                quoter_rule_detail_id = #{quoterRuleDetailId,jdbcType=VARCHAR},
            </if>
            <if test="quoterRuleDetailName != null">
                quoter_rule_detail_name = #{quoterRuleDetailName,jdbcType=VARCHAR},
            </if>
            <if test="totalCodeNumber != null">
                total_code_number = #{totalCodeNumber,jdbcType=INTEGER},
            </if>
            <if test="totalSkuNumber != null">
                total_sku_number = #{totalSkuNumber,jdbcType=INTEGER},
            </if>
            <if test="totalBoxes != null">
                total_boxes = #{totalBoxes,jdbcType=DECIMAL},
            </if>
            <if test="totalNumber != null">
                total_number = #{totalNumber,jdbcType=DECIMAL},
            </if>
            <if test="totalWeight != null">
                total_weight = #{totalWeight,jdbcType=DECIMAL},
            </if>
            <if test="totalVolume != null">
                total_volume = #{totalVolume,jdbcType=DECIMAL},
            </if>
            <if test="totalCargoValue != null">
                total_cargo_value = #{totalCargoValue,jdbcType=DECIMAL},
            </if>
            <if test="totalExtraFeeAmount != null">
                total_extra_fee_amount = #{totalExtraFeeAmount,jdbcType=DECIMAL},
            </if>
            <if test="totalExtraFeeNumber != null">
                total_extra_fee_number = #{totalExtraFeeNumber,jdbcType=DECIMAL},
            </if>
            <if test="totalExtraFeePrice != null">
                total_extra_fee_price = #{totalExtraFeePrice,jdbcType=DECIMAL},
            </if>
            <if test="extraFeeRemark != null">
                extra_fee_remark = #{extraFeeRemark,jdbcType=VARCHAR},
            </if>
            <if test="totalOverNum != null">
                total_over_num = #{totalOverNum,jdbcType=DECIMAL},
            </if>
            <if test="totalOverStoreNum != null">
                total_over_store_num = #{totalOverStoreNum,jdbcType=INTEGER},
            </if>
            <if test="extraField1 != null">
                extra_field1 = #{extraField1,jdbcType=DECIMAL},
            </if>
            <if test="settleType != null">
                settle_type = #{settleType,jdbcType=INTEGER},
            </if>
            <if test="settleEntityType != null">
                settle_entity_type = #{settleEntityType,jdbcType=INTEGER},
            </if>
            <if test="settleSetting != null">
                settle_setting = #{settleSetting,jdbcType=INTEGER},
            </if>
            <if test="settleMainId != null">
                settle_main_id = #{settleMainId,jdbcType=INTEGER},
            </if>
            <if test="settleAmount != null">
                settle_amount = #{settleAmount,jdbcType=DECIMAL},
            </if>
            <if test="freight != null">
                freight = #{freight,jdbcType=DECIMAL},
            </if>
            <if test="deliveryFee != null">
                delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
            </if>
            <if test="ultrafarFee != null">
                ultrafar_fee = #{ultrafarFee,jdbcType=DECIMAL},
            </if>
            <if test="superframesFee != null">
                superframes_fee = #{superframesFee,jdbcType=DECIMAL},
            </if>
            <if test="excessFee != null">
                excess_fee = #{excessFee,jdbcType=DECIMAL},
            </if>
            <if test="reduceFee != null">
                reduce_fee = #{reduceFee,jdbcType=DECIMAL},
            </if>
            <if test="outboundsortingFee != null">
                outboundsorting_fee = #{outboundsortingFee,jdbcType=DECIMAL},
            </if>
            <if test="shortbargeFee != null">
                shortbarge_fee = #{shortbargeFee,jdbcType=DECIMAL},
            </if>
            <if test="returnFee != null">
                return_fee = #{returnFee,jdbcType=DECIMAL},
            </if>
            <if test="exceptionFee != null">
                exception_fee = #{exceptionFee,jdbcType=DECIMAL},
            </if>
            <if test="adjustFee != null">
                adjust_fee = #{adjustFee,jdbcType=DECIMAL},
            </if>
            <if test="adjustRemark != null">
                adjust_remark = #{adjustRemark,jdbcType=VARCHAR},
            </if>
            <if test="otherCost1 != null">
                other_cost1 = #{otherCost1,jdbcType=DECIMAL},
            </if>
            <if test="otherCost2 != null">
                other_cost2 = #{otherCost2,jdbcType=DECIMAL},
            </if>
            <if test="otherCost3 != null">
                other_cost3 = #{otherCost3,jdbcType=DECIMAL},
            </if>
            <if test="otherCost4 != null">
                other_cost4 = #{otherCost4,jdbcType=DECIMAL},
            </if>
            <if test="otherCost5 != null">
                other_cost5 = #{otherCost5,jdbcType=DECIMAL},
            </if>
            <if test="otherCost6 != null">
                other_cost6 = #{otherCost6,jdbcType=DECIMAL},
            </if>
            <if test="otherCost7 != null">
                other_cost7 = #{otherCost7,jdbcType=DECIMAL},
            </if>
            <if test="otherCost8 != null">
                other_cost8 = #{otherCost8,jdbcType=DECIMAL},
            </if>
            <if test="otherCost9 != null">
                other_cost9 = #{otherCost9,jdbcType=DECIMAL},
            </if>
            <if test="otherCost10 != null">
                other_cost10 = #{otherCost10,jdbcType=DECIMAL},
            </if>
            <if test="otherCost11 != null">
                other_cost11 = #{otherCost11,jdbcType=DECIMAL},
            </if>
            <if test="otherCost12 != null">
                other_cost12 = #{otherCost12,jdbcType=DECIMAL},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="createCode != null">
                create_code = #{createCode,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operCode != null">
                oper_code = #{operCode,jdbcType=VARCHAR},
            </if>
            <if test="operBy != null">
                oper_by = #{operBy,jdbcType=VARCHAR},
            </if>
            <if test="operTime != null">
                oper_time = #{operTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where del_flag = 0 and main_code_id = #{mainExpenseId};
    </update>


    <delete id="deleteBmsYscostInfoById" parameterType="java.lang.String">
        delete from bms_yscost_info where pk_id = #{pkId}
    </delete>

    <delete id="deleteBmsYscostInfoByIds">
        delete from bms_yscost_info where pk_id in
        <foreach item="pkId" collection="array" open="(" separator="," close=")">
            #{pkId}
        </foreach>
    </delete>

    <update id="updateBmsYscostInfoStatusByIds">
        update bms_yscost_info set status = #{status} where pk_id in
        <foreach item="pkId" collection="array" open="(" separator="," close=")">
            #{pkId}
        </foreach>
    </update>

    <update id="updateBmsYscostInfoMainInfoByIds" parameterType="java.util.Map">
        UPDATE bms_yscost_info SET bill_id = null,show_bill_code=null,show_bill_id=null
        WHERE bill_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>;
        UPDATE bms_yscost_info SET bill_id = null,show_bill_code=null,show_bill_id=null
        WHERE bill_id IN (
            SELECT id
            FROM bms_ysbillmain
            WHERE fatherid IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        );
        UPDATE bms_ysbillmain SET del_flag =1
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>;
        UPDATE bms_addedfee SET bill_id =null,bill_code=null,show_bill_code=null,show_bill_id=null
        WHERE del_flag = 0 AND settle_type = 1 AND bill_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>;
        UPDATE bms_fixedfee set bill_id =null,bill_code=null,show_bill_code=null,show_bill_id=null
        WHERE del_flag = 0 AND settle_type = 1 AND  bill_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>;
        UPDATE pub_file_export SET del_flag =1 WHERE bill_code IN (
            SELECT bill_code
            FROM bms_ysbillmain
            WHERE id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        )
    </update>

    <update id="updateBillIdByIds" >
        update bms_yscost_info
        set bill_id = #{billId},
        show_bill_id = #{billId},
        show_bill_code = #{billCode}
        where
        client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="companyId!=null">
            and company_id = #{companyId}
        </if>
        AND bill_id IS NULL
        and IFNULL(expenses_mark,1) = 1
        and IFNULL(bill_date,'') =#{billDate}
        AND del_flag = 0
    </update>

    <update id="updateBillIdYSByIds" >
        update bms_yscost_info
        set bill_id = #{billId}
        where
        client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="companyId!=null">
            and company_id = #{companyId}
        </if>
        AND bill_id IS NULL
        and IFNULL(expenses_mark,1) = 1
        and IFNULL(bill_date,'') =#{billDate}
        AND del_flag = 0
        and IFNULL(expenses_type,'0') in (1)
    </update>

    <update id="updateBillIdCCByIds" >
        update bms_yscost_info a
        set a.bill_id = #{billId}
        where
        a.client_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="companyId!=null">
            and a.company_id = #{companyId}
        </if>
        <if test="warehouseCode!=null and warehouseCode!=''">
            and a.warehouse_code = #{warehouseCode}
        </if>
        and IFNULL(a.bill_id,0)=0
        and IFNULL(a.expenses_mark,1) = 1
        and IFNULL(a.bill_date,'') =#{billDate}
        and IFNULL(a.del_flag,'0') = '0'
        and IFNULL(a.expenses_type,'0') in (2,3,4)
    </update>

    <update id="updateBillIdById" >
        update bms_yscost_info
        set bill_id = #{billId}
        where
        client_id =#{id}
        and company_id = #{companyId}
        AND bill_id IS NULL
        and IFNULL(expenses_mark,1) = 1
        and IFNULL(bill_date,'') =#{billDate}
        AND del_flag = 0
    </update>

    <update id="updateBillIdFeeFlagById" >
        update bms_yscost_info
        set bill_id = #{billId}
        where
        client_id =#{id}
        and company_id = #{companyId}
        AND bill_id IS NULL
        and IFNULL(expenses_mark,1) = 2
        and IFNULL(bill_date,'') =#{billDate}
        AND del_flag = 0
    </update>

    <select id="getcodeFeeFlagList" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
        SUM(
        IFNULL(a.freight,0) +
        IFNULL(a.ultrafar_fee,0) +
        IFNULL(a.superframes_fee,0) +
        IFNULL(a.excess_fee,0) +
        IFNULL(a.delivery_fee,0) +
        IFNULL(a.outboundsorting_fee,0) +
        IFNULL(a.shortbarge_fee,0) +
        IFNULL(a.return_fee,0) +
        IFNULL(a.exception_fee,0) +
        IFNULL(a.adjust_fee,0) +
        IFNULL(a.other_cost1,0) +
        IFNULL(a.other_cost2,0) +
        IFNULL(a.other_cost3,0) +
        IFNULL(a.other_cost4,0) +
        IFNULL(a.other_cost5,0) +
        IFNULL(a.other_cost6,0) +
        IFNULL(a.other_cost7,0) +
        IFNULL(a.other_cost8,0) +
        IFNULL(a.other_cost9,0) +
        IFNULL(a.other_cost10,0) +
        IFNULL(a.other_cost11,0) +
        IFNULL(a.other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        a.bill_date as billDate,
        a.company_id as companyId,
        case when a.expenses_type=1 then '1'
             when a.expenses_type in (2,3,4) then '2'
             else '' end as billType,
        case when a.expenses_type=1 then ''
             when a.expenses_type in (2,3,4) then b.warehouse_code
             else '' end as warehouseCode,
        case when a.expenses_type=1 then ''
             when a.expenses_type in (2,3,4) then b.warehouse_name
             else '' end as warehouseName

        from bms_yscost_info a
        left join bms_yscost_extend  b
        on a.id = b.expenses_id
        WHERE a.client_id =#{id}
        AND a.expenses_mark = 2
        <if test="billDate!=null">
            AND IFNULL(a.bill_date,'') =#{billDate}
        </if>
        AND a.del_flag = 0
        AND a.bill_id IS NULL
    </select>

    <select id="selListByYsbillId" resultMap="BmsYscostInfoResult" >
        <include refid="selectBmsYscostInfoVo" />
        WHERE del_flag = 0
        and bill_id IN
        <foreach item="id" collection="billIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryCostByMainCostId" resultMap="BmsYscostInfoResult" >
        SELECT
        t1.pk_id,
        t1.id,
        t1.expenses_code,
        t1.business_type,
        t1.client_id,
        t1.company_id,
        t1.expenses_type,
        t1.cost_dimension,
        t1.charge_type,
        t1.expenses_mark,
        t1.quoterule_id,
        t1.rule_name,
        t1.remarks,
        t1.freight,
        t1.delivery_fee,
        t1.ultrafar_fee,
        t1.superframes_fee,
        t1.excess_fee,
        t1.reduce_fee,
        t1.outboundsorting_fee,
        t1.shortbarge_fee,
        t1.return_fee,
        t1.exception_fee,
        t1.adjust_fee,
        t1.adjust_remark,
        t1.other_cost1,
        t1.other_cost2,
        t1.other_cost3,
        t1.other_cost4,
        t1.other_cost5,
        t1.other_cost6,
        t1.other_cost7,
        t1.other_cost8,
        t1.other_cost9,
        t1.other_cost10,
        t1.other_cost11,
        t1.other_cost12,
        t1.cost_attribute,
        t1.oper_code,
        t1.oper_by,
        t1.oper_time,
        t1.del_flag,
        t1.bill_id,
        t1.bill_date,
        t1.business_time,
        t1.over_num,
        t1.over_sendnum,
        t1.storage_fee_price,
        t1.disposal_fee_price,
        t1.other_fee_remark,
        t1.fee_type_first,
        t1.fee_create_ate,
        t1.is_increment,
        t1.order_date,
        t1.signing_date,
        t1.quoteruledetail_id,
        t1.show_bill_id,
        t1.show_bill_code,
        t1.extra_field1,
        t1.warehouse_code_arr,
        t1.settle_type,
        t1.settle_amount,
        t1.main_expense_id,
        t1.settle_main_id,
        t2.automatic_billing_remark
        FROM bms_yscost_info t1
        left join bms_yscost_extend t2 on t1.main_expense_id = t2.main_expense_id and t2.del_flag = 0
        WHERE t1.del_flag = 0
        and t1.main_expense_id  IN
        <foreach item="id" collection="mainCostIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="updateShowBillCode">
        UPDATE bms_yscost_info
        SET show_bill_code = #{showBillCode}
        WHERE del_flag = 0
        AND bill_id IN
        <foreach item="item" collection="entitys" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>


    <select id="selectCostInfoListByMap">

    </select>

</mapper>