<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfpurchaseFeeinfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.pay.BmsYfpurchaseFeeinfo" id="BmsYfpurchaseFeeinfoResult">
        <result property="id"    column="id"    />
        <result property="yfFeecode"    column="yf_feecode"    />
        <result property="registerTime"    column="register_time"    />
        <result property="billId"    column="bill_id"    />
        <result property="creatFlag"    column="creat_flag"    />
        <result property="registerrCompanyId"    column="registerr_company_id"    />
        <result property="feeFlag"    column="fee_flag"    />
        <result property="registerrCompanyname"    column="registerr_companyname"    />
        <result property="ysFeecode"    column="ys_feecode"    />
        <result property="companyId"    column="company_id"    />
        <result property="skuName"    column="sku_name"    />
        <result property="num"    column="num"    />
        <result property="price"    column="price"    />
        <result property="unit"    column="unit"    />
        <result property="feeFlag" column="fee_Flag" />
        <result property="companyId"    column="company_id"    />
        <result property="spec"    column="spec"    />
        <result property="relationCode"    column="client_name"    />
        <result property="relationName"    column="client_code"    />
        <result property="suppleCode"    column="carrier_name"    />
        <result property="suppleName"    column="carrier_code"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="carrierCode"    column="yfrcode"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="clientCode"    column="ysrcode"    />
        <result property="clientName"    column="client_name"    />
        <result property="totalAmountYs"    column="ystotal_amount"    />
        <result property="totalAmountYf"    column="yftotal_amount"    />
        <result property="remark"    column="remark"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="billDate"    column="bill_date"    />
        <result property="yfBillCode" column="yfbillcode" />
        <result property="relationCode"    column="relation_code"    />
        <result property="relationName" column="relation_name" />
        <result property="adjustRemark" column="adjust_remark" />
    </resultMap>

    <sql id="selectBmsYfpurchaseFeeinfoVo">
        select id, yf_feecode, register_time, bill_id, creat_flag, registerr_company_id, fee_flag, registerr_companyname, ys_feecode, sku_name, num, price, unit, company_id, spec, relation_code, relation_name, total_amount, remark, create_code, create_by, create_time, create_dept_id, oper_dept_id, oper_code, oper_by, oper_time, del_flag, bill_date from bms_yfpurchase_feeinfo
    </sql>


    <select id="selectBmsYfpurchaseFeeinfoByIds" parameterType="java.util.List" resultMap="BmsYfpurchaseFeeinfoResult">
       select
       a.id,
       a.yf_feecode,
       a.register_time,
       a.bill_id,
       a.creat_flag,
       a.registerr_company_id,
       a.fee_flag,
       a.registerr_companyname,
       a.ys_feecode,
       a.sku_name,
       a.num,
       a.price,
       a.unit,
       a.company_id,
       a.spec,
       a.relation_code,
       a.relation_name,
       a.total_amount,
       a.remark,
       a.create_code,
       a.create_by,
       a.create_time,
       a.create_dept_id,
       a.oper_dept_id,
       a.oper_code,
       a.oper_by,
       a.oper_time,
       a.del_flag,
       IFNULL(a.bill_date,b.bill_date) as bill_date
       from bms_yfpurchase_feeinfo a
       left join bms_yfbillmain b on a.bill_id = b.id
       where
       a.id in
       <foreach collection="ids" item="id" open="(" separator="," close=")">
           #{id}
       </foreach>

    </select>

    <select id="selectBmsYfpurchaseFeeinfoById" parameterType="java.lang.String" resultMap="BmsYfpurchaseFeeinfoResult">
       select
       a.id,
       a.yf_feecode,
       a.register_time,
       a.bill_id,
       a.creat_flag,
       a.registerr_company_id,
       a.fee_flag,
       a.registerr_companyname,
       a.ys_feecode,
       a.sku_name,
       a.num,
       a.price,
       a.unit,
       a.company_id,
       a.spec,
       a.relation_code,
       a.relation_name,
       a.total_amount,
       a.remark,
       a.create_code,
       a.create_by,
       a.create_time,
       a.create_dept_id,
       a.oper_dept_id,
       a.oper_code,
       a.oper_by,
       a.oper_time,
       a.del_flag,
       IFNULL(a.bill_date,b.bill_date) as bill_date
       from bms_yfpurchase_feeinfo a
       left join bms_yfbillmain b on a.bill_id = b.id
       where
       a.id = #{id}

    </select>

    <select id="selectBmsYfpurchaseFeeinfoList" parameterType="com.bbyb.joy.bms.domain.dto.pay.BmsYfpurchaseFeeinfo" resultMap="BmsYfpurchaseFeeinfoResult">
        <include refid="selectBmsYfpurchaseFeeinfoVo"/>
        <where>
            and del_flag=0
            <if test="yfFeecode != null  and yfFeecode != ''"> and yf_feecode = #{yfFeecode}</if>
            <if test="params.beginRegisterTime != null and params.beginRegisterTime != '' and params.endRegisterTime != null and params.endRegisterTime != ''"> and register_time between #{params.beginRegisterTime} and #{params.endRegisterTime}</if>
            <if test="registerrCompanyId != null "> and registerr_company_id = #{registerrCompanyId}</if>
            <if test="registerrCompanyname != null  and registerrCompanyname != ''"> and registerr_companyname like concat('%', #{registerrCompanyname}, '%')</if>
            <if test="ysFeecode != null  and ysFeecode != ''"> and ys_feecode = #{ysFeecode}</if>
            <if test="skuName != null  and skuName != ''"> and sku_name like concat('%', #{skuName}, '%')</if>
            <if test="num != null "> and num = #{num}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="spec != null  and spec != ''"> and spec = #{spec}</if>
            <if test="carrierCode != null  and carrierCode != ''"> and relation_code like concat('%', #{carrierCode}, '%')</if>
            <if test="carrierName != null  and carrierName != ''"> and relation_name like concat('%', #{carrierName}, '%')</if>
            <if test="totalAmountYf != null "> and total_amount = #{totalAmountYf}</if>
            <if test="createCode != null  and createCode != ''"> and create_code = #{createCode}</if>
            <if test="createDeptId != null "> and create_dept_id = #{createDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
        </where>
    </select>

    <select id="selectBmsJoinYS" parameterType="com.bbyb.joy.bms.domain.dto.pay.BmsYfpurchaseFeeinfo" resultMap="BmsYfpurchaseFeeinfoResult" >
        select
            yf.id,
            yf.yf_feecode,
            yf.remark,
            yf.register_time,
            yf.relation_code yfrcode,
            carrier.carrier_name,
            yf.total_amount yftotal_amount,
            ys.total_amount ystotal_amount,
            yf.bill_date,
            yfbill.bill_code yfbillcode,
            yf.creat_flag,
            ysbill.bill_code ysbillcode,
            ys.ys_feecode,
            ys.relation_code ysrcode,
            ys.registerr_companyname,
            client.client_name,
            ysbill.audit_state,
            yf.fee_flag,
            yf.create_time,
            yf.create_by,
            yf.create_time,
            yf.create_dept_id,
            yf.oper_dept_id,
            yf.oper_code,
            yf.oper_by,
            yf.oper_time,
            yf.sku_name,
            yf.create_code,
            yf.num,
            yf.price,
            yf.unit,
            yf.spec,
            yf.adjust_remark
        from
        bms_yfpurchase_feeinfo yf
        LEFT JOIN bms_yspurchase_feeinfo ys on yf.yf_feecode=ys.yf_feecode             AND ys.del_flag = 0
        LEFT JOIN bms_ysbillmain ysbill on ys.ysbill_id=ysbill.id             AND ys.del_flag = 0
        LEFT JOIN bms_yfbillmain yfbill on yf.bill_id=yfbill.id
        LEFT JOIN bms_clientinfo client on client.client_code=ys.relation_code
        LEFT JOIN bms_carrierinfo carrier on carrier.carrier_code=yf.relation_code
        <where>
            <if test="id!=null">and yf.id=#{id}</if>
            <if test="params.beginRegisterTime != null and params.beginRegisterTime != '' and params.endRegisterTime != null and params.endRegisterTime != ''"> and register_time between #{params.beginRegisterTime} and #{params.endRegisterTime}</if>
            <if test="companyId != null "> and yf.company_id = #{companyId}</if>
            <if test="carrierCode != null  and carrierCode != ''"> and  carrier.carrier_code like concat('%', #{carrierCode}, '%')</if>
            <if test="carrierName != null  and carrierName != ''"> and  carrier.carrier_name like concat('%', #{carrierName}, '%')</if>
            <if test="operTime != null "> and yf.oper_time = #{operTime}</if>
            <if test="feeFlag!=null"> and yf.fee_Flag=#{feeFlag} </if>
            <if test="remark!=null" > and yf.remark like concat('%',#{remark},'%')</if>
             <if test="yfFeecode!=null and yfFeecode!=''">
                 and  yf.yf_feecode like concat('%',#{yfFeecode},'%')
             </if>
            <if test="companyIds != null and companyIds.length>0 ">
                and  yf.create_dept_id in
                <foreach collection="companyIds" item="value" separator="," open="(" close=")">
                    #{value}
                </foreach>
            </if>
            <if test="carrierList != null">
                and  carrier.carrier_code in
                <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                    #{carrier}
                </foreach>
            </if>
            <if test="billDate!=null and billDate!=''">
                and yf.bill_date=#{billDate}
            </if>
            <if test="creatFlag!=null and creatFlag!=''">
                and yf.creat_flag=#{creatFlag}
            </if>
            and yf.del_Flag=0
        </where>
        order by yf.id desc
    </select>

    <insert id="insertBmsYfpurchaseFeeinfo" parameterType="com.bbyb.joy.bms.domain.dto.pay.BmsYfpurchaseFeeinfo" useGeneratedKeys="true" keyProperty="id">
        insert into bms_yfpurchase_feeinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="yfFeecode != null">yf_feecode,</if>
            <if test="registerTime != null">register_time,</if>
            <if test="billId != null">bill_id,</if>
            <if test="creatFlag != null">creat_flag,</if>
            <if test="registerrCompanyId != null">registerr_company_id,</if>
            <if test="feeFlag != null">fee_flag,</if>
            <if test="registerrCompanyname != null">registerr_companyname,</if>
            <if test="ysFeecode != null">ys_feecode,</if>
            <if test="skuName != null">sku_name,</if>
            <if test="num != null">num,</if>
            <if test="price != null">price,</if>
            <if test="unit != null">unit,</if>
            <if test="companyId != null">company_id,</if>
            <if test="spec != null">spec,</if>
            <if test="relationCode != null">relation_code,</if>
            <if test="relationName != null">relation_name,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="remark != null">remark,</if>
            <if test="adjustRemark != null">adjust_remark,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="yfBillDate != null">bill_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="yfFeecode != null">#{yfFeecode},</if>
            <if test="registerTime != null">#{registerTime},</if>
            <if test="billId != null">#{billId},</if>
            <if test="creatFlag != null">#{creatFlag},</if>
            <if test="registerrCompanyId != null">#{registerrCompanyId},</if>
            <if test="feeFlag != null">#{feeFlag},</if>
            <if test="registerrCompanyname != null">#{registerrCompanyname},</if>
            <if test="ysFeecode != null">#{ysFeecode},</if>
            <if test="skuName != null">#{skuName},</if>
            <if test="num != null">#{num},</if>
            <if test="price != null">#{price},</if>
            <if test="unit != null">#{unit},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="spec != null">#{spec},</if>
            <if test="relationCode != null">#{relationCode},</if>
            <if test="relationName != null">#{relationName},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="adjustRemark != null">#{adjustRemark},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="yfBillDate != null">#{yfBillDate},</if>
        </trim>
    </insert>

    <update id="updateBmsYfpurchaseFeeinfo" parameterType="com.bbyb.joy.bms.domain.dto.pay.BmsYfpurchaseFeeinfo">
        update bms_yfpurchase_feeinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="yfFeecode != null">yf_feecode = #{yfFeecode},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
            <if test="billId != null">bill_id = #{billId},</if>
            <if test="creatFlag != null">creat_flag = #{creatFlag},</if>
            <if test="registerrCompanyId != null">registerr_company_id = #{registerrCompanyId},</if>
            <if test="feeFlag != null">fee_flag = #{feeFlag},</if>
            <if test="registerrCompanyname != null">registerr_companyname = #{registerrCompanyname},</if>
            <if test="ysFeecode != null">ys_feecode = #{ysFeecode},</if>
            <if test="skuName != null">sku_name = #{skuName},</if>
            <if test="num != null">num = #{num},</if>
            <if test="price != null">price = #{price},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="spec != null">spec = #{spec},</if>
            <if test="carrierCode != null">relation_code = #{carrierCode},</if>
            <if test="carrierName != null">relation_name = #{carrierName},</if>
            <if test="totalAmountYf != null">total_amount = #{totalAmountYf},</if>
            <if test="relationCode != null">relation_code = #{relationCode},</if>
            <if test="relationName != null">relation_name = #{relationName},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="billDate != null">bill_date = #{billDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYfpurchaseFeeinfoById" parameterType="java.lang.String">
        update  bms_yfpurchase_feeinfo  set del_flag=1 where id = #{id}
    </delete>

    <delete id="deleteBmsYfpurchaseFeeinfoByIds">
        update  bms_yfpurchase_feeinfo set del_flag=1  where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYfpurchaseFeeinfoStatusByIds">
        update bms_yfpurchase_feeinfo set del_flag = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>



    <update id="updateBmsYfpurchaseFeeinfoStatusByCode">
        update bms_yfpurchase_feeinfo set del_flag = #{status} where ys_feecode=#{ysFeecode}
    </update>
</mapper>