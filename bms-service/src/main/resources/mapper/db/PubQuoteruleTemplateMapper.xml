<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubQuoteruleTemplateMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.PubQuoteruleTemplate" id="PubQuoteruleTemplateResult">
        <result property="id"    column="id"    />
        <result property="ruleCode"    column="rule_code"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="consolidationRule"    column="consolidation_rule"    />
        <result property="groupRule"    column="group_rule"    />
        <result property="remark"    column="remark"    />
        <result property="calculationFormula"    column="calculation_formula"    />
        <result property="matchingConditions"    column="matching_conditions"    />
        <result property="isEnable"    column="is_enable"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="updateBy"    column="oper_by"    />
        <result property="updateTime"    column="oper_time"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="calculationFormulaCode"    column="calculation_formula_code"    />
        <result property="matchingConditionsCode"    column="matching_conditions_code"    />
        <result property="calculationProcess"    column="calculation_process"    />
        <result property="calculationProcessCode"    column="calculation_process_code"    />
        <result property="bjType"    column="bj_type"    />
        <result property="orderType"    column="order_type"    />
        <result property="orderTypeList"    column="order_type_list"    />
        <result property="extraFormula"    column="extra_formula"    />
        <result property="extraFormulaCode"    column="extra_formula_code"    />
        <result property="extraCharging"    column="extra_charging"    />
    </resultMap>

    <sql id="selectPubQuoteruleTemplateVo">
        select id, rule_code, rule_name, rule_type, consolidation_rule,group_rule,
        remark, calculation_formula, matching_conditions, is_enable,create_by,
        create_time, create_dept_id, oper_by, oper_time, oper_dept_id, del_flag,
        calculation_formula_code,matching_conditions_code,calculation_process,calculation_process_code,bj_type,order_type,order_type_list,extra_formula,extra_formula_code,extra_charging
        from pub_quoterule_template
    </sql>

    <select id="selectPubQuoteruleTemplateList" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoteruleTemplate" resultMap="PubQuoteruleTemplateResult">
        <include refid="selectPubQuoteruleTemplateVo"/>
        <where>
            del_flag='0'
            <if test="ruleCode != null  and ruleCode != ''"> and rule_code like concat('%', #{ruleCode}, '%')</if>
            <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="ruleType != null "> and rule_type = #{ruleType}</if>
            <if test="bjType != null "> and bj_type = #{bjType}</if>
        </where>
        ORDER BY  create_time desc
    </select>

    <select id="selectPubQuoteruleTemplateById" parameterType="String" resultMap="PubQuoteruleTemplateResult">
        <include refid="selectPubQuoteruleTemplateVo"/>
        where    del_flag='0'  and  id = #{id}
    </select>

    <select id="selectPubQuoteruleTemplateListInfo"   resultType="com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleTemplateDto">
        select id, rule_code ruleCode, rule_name ruleName ,remark tempRemark,extra_charging as extraCharging
        from pub_quoterule_template
        where    del_flag='0' and is_enable = '0'
        <if test="ruleType == 1  "> and bj_type in(1,2)</if>
        <if test="ruleType == 2  "> and bj_type in(1,3)</if>
        <if test="ruleName !=null and ruleName !=''">
         and rule_name like concat('%',#{ruleName},'%')
        </if>
        <if test="orderType !=null"> and (
            IFNULL(order_type,-1) = #{orderType}
            or IFNULL(order_type_list,'') like concat('%',#{orderType},'%')
            )</if>

    </select>


    <select id="checkRuleCodeUnique" parameterType="String" resultMap="PubQuoteruleTemplateResult">
        <include refid="selectPubQuoteruleTemplateVo"/>
        where  del_flag='0' and rule_code=#{ruleCode} limit 1
    </select>

    <insert id="insertPubQuoteruleTemplate" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoteruleTemplate">
        insert into pub_quoterule_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="ruleCode != null">rule_code,</if>
            <if test="ruleName != null">rule_name,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="consolidationRule != null">consolidation_rule,</if>
            <if test="groupRule != null">group_rule,</if>
            <if test="remark != null">remark,</if>
            <if test="calculationFormula != null">calculation_formula,</if>
            <if test="matchingConditions != null">matching_conditions,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="updateBy != null">oper_by,</if>
            <if test="updateTime != null">oper_time,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="calculationFormulaCode != null">calculation_formula_code,</if>
            <if test="matchingConditionsCode != null">matching_conditions_code,</if>
            <if test="calculationProcess != null">calculation_process,</if>
            <if test="calculationProcessCode != null">calculation_process_code,</if>
            <if test="bjType != null">bj_type,</if>
            <if test="orderType != null">order_type,</if>
            <if test="orderTypeList != null">order_type_list,</if>
            <if test="extraFormula != null">extra_formula,</if>
            <if test="extraFormulaCode != null">extra_formula_code,</if>
            <if test="extraCharging != null">extra_charging,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="ruleCode != null">#{ruleCode},</if>
            <if test="ruleName != null">#{ruleName},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="consolidationRule != null">#{consolidationRule},</if>
            <if test="groupRule != null">#{groupRule},</if>
            <if test="remark != null">#{remark},</if>
            <if test="calculationFormula != null">#{calculationFormula},</if>
            <if test="matchingConditions != null">#{matchingConditions},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="calculationFormulaCode != null">#{calculationFormulaCode},</if>
            <if test="matchingConditionsCode != null">#{matchingConditionsCode},</if>
            <if test="calculationProcess != null">#{calculationProcess},</if>
            <if test="calculationProcessCode != null">#{calculationProcessCode},</if>
            <if test="bjType != null">#{bjType},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="orderTypeList != null">#{orderTypeList},</if>
            <if test="extraFormula != null">#{extraFormula},</if>
            <if test="extraFormulaCode != null">#{extraFormulaCode},</if>
            <if test="extraCharging != null">#{extraCharging},</if>
        </trim>
    </insert>

    <update id="updatePubQuoteruleTemplate" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoteruleTemplate">
        update pub_quoterule_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleCode != null">rule_code = #{ruleCode},</if>
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            <if test="ruleType != null">rule_type = #{ruleType},</if>
            <if test="consolidationRule != null">consolidation_rule = #{consolidationRule},</if>
            <if test="groupRule != null">group_rule = #{groupRule},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="calculationFormula != null">calculation_formula = #{calculationFormula},</if>
            <if test="matchingConditions != null">matching_conditions = #{matchingConditions},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="updateBy != null">oper_by = #{updateBy},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="calculationFormulaCode != null">calculation_formula_code = #{calculationFormulaCode},</if>
            <if test="matchingConditionsCode != null">matching_conditions_code = #{matchingConditionsCode},</if>
            <if test="calculationProcess != null">calculation_process = #{calculationProcess},</if>
            <if test="calculationProcessCode != null">calculation_process_code = #{calculationProcessCode},</if>
            <if test="bjType != null">bj_type = #{bjType},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="orderTypeList != null">order_type_list = #{orderTypeList},</if>
            <if test="extraFormula != null">extra_formula=#{extraFormula},</if>
            <if test="extraFormulaCode != null">extra_formula_code=#{extraFormulaCode},</if>
            <if test="extraCharging != null">extra_charging=#{extraCharging},</if>
            oper_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePubQuoteruleTemplateByIds">
        update pub_quoterule_template
        set del_flag= 1
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updatePubQuoteruleByIds" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoteruleTemplate">
        update pub_quoterule_template
        set is_enable= #{isEnable}
        where id  =   #{id}
    </update>

    <select id="selectPubQuoteruleTemplateDtoById" parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleTemplateDto">
        select id, rule_code ruleCode, rule_name ruleName ,remark tempRemark
        from pub_quoterule_template
        where del_flag='0' and is_enable = '0'
          and id = #{id}
    </select>
</mapper>