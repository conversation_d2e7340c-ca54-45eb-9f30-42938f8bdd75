<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYsbillinvoiceMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillinvoice" id="BmsYsbillinvoiceResult">
        <result property="id"    column="id"    />
        <result property="invoicecode"    column="invoicecode"    />
        <result property="invoiceWord"    column="invoice_word"    />
        <result property="ticketMode"    column="ticket_mode"    />
        <result property="billCodes"    column="bill_codes"    />
        <result property="billid"    column="billid"    />
        <result property="invoicefee"    column="invoicefee"    />
        <result property="taxRate"    column="tax_rate"    />
        <result property="taxFee"    column="tax_fee"    />
        <result property="invoiceIncome"    column="invoice_income"    />
        <result property="companyid"    column="companyid"    />
        <result property="invoiceTime"    column="invoice_time"    />
        <result property="openingName"    column="opening_name"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="openingBank"    column="opening_bank"    />
        <result property="cardNumber"    column="card_number"    />
        <result property="linkPhone"    column="link_phone"    />
        <result property="taxpayerNum"    column="taxpayer_num"    />
        <result property="tacAddress"    column="tac_address"    />
        <result property="expressCode"    column="express_code"    />
        <result property="remark"    column="remark"    />
        <result property="receiver"    column="receiver"    />
        <result property="invoiceState"    column="invoice_state"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="invoiceProject"    column="invoice_project"    />
        <result property="createByNames"    column="createByNames"    />
        <result property="createByTimes"    column="createByTimes"    />
    </resultMap>

    <sql id="selectBmsYsbillinvoiceVo">
        select id, invoicecode, ticket_mode, bill_codes,billid,
               invoicefee, tax_rate, tax_fee, invoice_income,
               companyid, invoice_time, opening_name, invoice_type,
               opening_bank, card_number, link_phone, taxpayer_num,
               tac_address, express_code,  remark, receiver,
               invoice_state, create_code, create_by, create_time, create_dept_id,
               oper_dept_id, oper_code, oper_by, oper_time, del_flag from bms_ysbillinvoice
    </sql>




    <select id="selectBmsYsbillinvoiceList" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillinvoice" resultMap="BmsYsbillinvoiceResult">
        select ysi.id, ysi.invoicecode, ysi.ticket_mode, ysi.bill_codes,
               ysi.invoicefee, ysi.tax_rate, ysi.tax_fee, ysi.invoice_income,
               ysi.companyid, ysi.invoice_time, ysi.opening_name, ysi.invoice_type,
               ysi.opening_bank, ysi.card_number, ysi.link_phone, ysi.taxpayer_num,
               ysi.tac_address, ysi.express_code, ysi.remark, ysi.receiver,
               ysi.invoice_state, ysi.create_code, ysi.create_by, ysi.create_time, ysi.create_dept_id,
               ysi.oper_dept_id, ysi.oper_code, ysi.oper_by, ysi.oper_time, ysi.del_flag,ysi.create_by createByNames,
               ysi.create_time  createByTimes,ysi.invoice_project invoice_project
        from bms_ysbillinvoice ysi
        left join bms_ysbillmain ys on ysi.billid = ys.id and ys.del_flag = '0'
        where  ysi.del_flag = '0'
        <if test="invoicecode != null  and invoicecode != ''">
         and ysi.invoicecode like concat('%', #{invoicecode}, '%')
        </if>
        <if test="expressCode != null  and expressCode != ''">
         and ysi.express_code like concat('%', #{expressCode}, '%')
        </if>
        <if test="billid != null ">
          and ysi.billid = #{billid}
        </if>
        order by ysi.create_time desc
    </select>


    <select id="selectBmsYsbillinvoiceByIds" resultMap="BmsYsbillinvoiceResult">
        select ysi.bill_codes, ysi.invoicefee, ysi.billid,ys.ticket_amount ticketAmount,ys.ticket_num ticketNum
        ,ys.ticket_apply_amount ticketApplyAmount,ys.hx_state hxState,ysi.invoice_state,
        ysi.invoicecode
        from bms_ysbillinvoice ysi
        left join bms_ysbillmain ys on ysi.billid = ys.id and ys.del_flag = '0'
        where  ysi.del_flag = '0' and ysi.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="selectYsmainAndInvoiceList" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsmainAndInvoiceDto">
        select
        ys.id,
        ysap.id as applyId,
        ysap.apply_code as applyCode,/** 开票申请单号 */
        IFNULL(ysap.ticket_state,1) ticketState,/** 开票状态 */
        cli.client_code clientCode,/** 客户编码 */
        cli.client_name clientName,/** 客户名称 */
        ys.bill_code billCode,/** 账单编号 */
        ys.bill_name billName,/** 账单名称 */
        ys.bill_date billDate,/** 账期 */
        ifnull( ys.adjusted_amount, ys.bill_amount )-IFNULL(ys.exception_fee,0) receivableAmount,/** 应收金额 */
        IFNULL(ys.exception_fee,0) as exceptionFee,/** 账单客诉金额 */
        ifnull( ys.adjusted_amount, ys.bill_amount ) ysAmount,/** 实收金额 */
        ysap.invoice_type as invoiceType,
        di2.dict_label as invoiceTypeName,/** 开票类型 */
        ysap.invoice_project as invoiceProject,
        di1.dict_label as invoiceProjectName,/** 开票项目 */
        ysap.apply_fee as ticketApplyAmount,/** 开票申请金额 */
        ysi.remark as ticketApplyRemark,/** 开票备注 */
        SUM(IFNULL(ysi.invoicefee,0)) as ticketAmount,/** 开票金额 */
        IFNULL(ysap.apply_fee,0)-SUM(IFNULL(ysi.invoicefee,0)) as noTicketAmount,/** 未开票金额 */
        count(ysi.id) as ticketNum,/** 开票次数 */
        ysap.opening_name as openingName,/** 开票抬头 */
        ysap.taxpayer_num as taxpayerNum,/** 税号 */
        ysap.tac_address as tacAddress,/** 地址 */
        ysap.link_phone as linkPhone,/** 联系电话 */
        ysap.opening_bank as openingBank,/** 开户行 */
        ysap.card_number as cardNumber,/** 银行卡号 */
        ys.company_id companyId, /** 所属机构 */
        DATE_FORMAT( ys.ticket_apply_time, '%Y-%m-%d %H:%i:%s') as ticketApplyTime, /** 开票申请时间 */
        wareInfo.warehouse_province as provinceOrigin,
        wareInfo.warehouse_city as originatingCity,
        wareInfo.warehouse_area as originatingArea,
        wareInfo.warehouse_province as destinationProvince,
        wareInfo.warehouse_city as destinationCity,
        wareInfo.warehouse_area as destinationArea,
        ysap.transtool_type as transtoolType,
        ysap.car_no as carNo,
        ysap.place_dispatch as placeDispatch,
        ysap.place_destination as placeDestination,
        ysap.trans_goods_name as transGoodsName
        from bms_ysbillmain ys
        left join bms_ysinvoice_apply ysap on ysap.billid = ys.id and ysap.del_flag=0
        left join bms_clientinfo  cli  on ys.client_id = cli.id and cli.del_flag = '0'
        left join bms_ysbillinvoice ysi on ysap.billid = ysi.billid and ysap.invoice_project=ysi.invoice_project and ysi.del_flag = '0' and IFNULL(ysi.invoice_state,0) =0 and ysi.invoice_type = ysap.invoice_type
        left join sys_dict_data di1 on di1.dict_value =ysap.invoice_project  and di1.dict_type = 'bms_invoice_project'
        left join sys_dict_data di2 on di2.dict_value =ysap.invoice_type  and di2.dict_type = 'bms_invoice_type'
        left join mdm_warehouseinfo wareInfo ON wareInfo.warehouse_code = ys.warehouse_code
        where ys.del_flag='0' and ys.ticket_apply_state = 2
        and ys.apply_audit_state=1
        <if test="ticketState != null and ticketState.size>0 ">
            AND ysap.ticket_state in
            <foreach collection="ticketState" item="ticketState" open="(" separator="," close=")">
                #{ticketState}
            </foreach>
        </if>
        <if test="beginTicketApplyTime != null and beginTicketApplyTime != '' and endTicketApplyTime != null and endTicketApplyTime!=''">
            AND ys.ticket_apply_time &gt;= #{beginTicketApplyTime,jdbcType=VARCHAR} AND ys.ticket_apply_time &lt;= #{endTicketApplyTime,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND ys.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="clientList != null">
            and  cli.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND cli.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="billCode != null and billCode != ''">
            AND ys.bill_code like concat('%',trim(#{billCode}),'%')
        </if>
        group by ysap.id
        order by ys.ticket_apply_time desc,ysap.ticket_state asc
    </select>




    <insert id="insertBmsYsbillinvoice" parameterType="java.util.List">
        insert into bms_ysbillinvoice
            (invoicecode,
            invoice_word,
            ticket_mode,
            billid,
            bill_codes,
            invoicefee,
            tax_rate,
            tax_fee,
            invoice_income,
            companyid,
            invoice_time,
            opening_name,
            invoice_type,
            opening_bank,
            card_number,
            link_phone,
            taxpayer_num,
            tac_address,
            express_code,
            remark,
            receiver,
            invoice_state,
            create_code,
            create_by,
            create_time,
            create_dept_id,
            oper_dept_id,
            oper_code,
            oper_by,
            oper_time,
            del_flag,
            freight_fee,
            storage_fee,
            information_service_fee,
            platform_fee,
            brand_fee)
            VALUES
             <foreach collection="list" item="item" index="index" separator=",">
             (
                 #{item.invoicecode},
                 #{item.invoiceWord},
                 #{item.ticketMode},
                 #{item.billid},
                 #{item.billCodes},
                 #{item.invoicefee},
                 #{item.taxRate},
                 #{item.taxFee},
                 #{item.invoiceIncome},
                 #{item.companyid},
                 #{item.invoiceTime},
                 #{item.openingName},
                 #{item.invoiceType},
                 #{item.openingBank},
                 #{item.cardNumber},
                 #{item.linkPhone},
                 #{item.taxpayerNum},
                 #{item.tacAddress},
                 #{item.expressCode},
                 #{item.remark},
                 #{item.receiver},
                 #{item.invoiceState},
                 #{item.createCode},
                 #{item.createBy},
                 sysdate(),
                 #{item.createDeptId},
                 #{item.operDeptId},
                 #{item.operCode},
                 #{item.operBy},
                 #{item.operTime},
                 '0',
                 #{item.freightFee},
                 #{item.storageFee},
                 #{item.informationServiceFee},
                 #{item.platformFee},
                 #{item.brandFee}
                 )
             </foreach>
    </insert>

    <update id="updateBmsYsbillinvoiceStatusByIds">
        update bms_ysbillinvoice set invoice_state = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="selectBmsYsbillinvoiceByInvoiceCode" parameterType="java.util.List" resultType="java.lang.String">
        select  invoicecode from bms_ysbillinvoice
        where del_flag = '0'
        and invoice_state = 0
        and invoicecode in
        <foreach item="invoicecode" collection="list" open="(" separator="," close=")">
            #{invoicecode}
        </foreach>
    </select>





    <select id="selectBmsYsbillinvoiceListBybillInfo" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillinvoice">
        SELECT
            id,
            billid,
            invoicecode,
            ticket_mode as ticketMode,
            bill_codes as billCodes,
            invoice_type as invoiceType,
            invoice_project as invoiceProject,
            invoice_word as invoiceWord,
            sum(invoicefee) as invoicefee,
            tax_rate as taxRate,
            tax_fee as taxFee,
            freight_fee as freightFee,
            storage_fee as storageFee,
            information_service_fee as informationServiceFee,
            platform_fee as platformFee,
            brand_fee as brandFee,
            invoice_income as invoiceIncome,
            companyid,
            invoice_time as invoiceTime,
            opening_name as openingName,
            opening_bank as openingBank,
            card_number as cardNumber,
            link_phone as linkPhone,
            taxpayer_num as taxpayerNum,
            tac_address as tacAddress,
            express_code as expressCode,
            remark,
            receiver,
            invoice_state as invoiceState,
            void_type as voidType,
            create_code as createCode,
            create_by as createBy,
            create_time as createTime,
            create_dept_id as createDeptId,
            oper_dept_id as operDeptId,
            oper_code as operCode,
            oper_by as operBy,
            oper_time as operTime,
            del_flag as delFlag,
            ppstorage_fee as ppstorageFee,
            zpstorage_fee as zpstorageFee,
            ppfreight_fee as ppfreightFee,
            zpfreight_fee as zpfreightFee
        FROM bms_ysbillinvoice
        where del_flag = '0'
        and invoice_state=0
        <if test="billIds != null and billIds.size>0 ">
            AND billid in
            <foreach collection="billIds" item="billId" open="(" separator="," close=")">
                #{billId}
            </foreach>
        </if>
        <if test="billCodes != null and billCodes.size>0 ">
            AND bill_codes in
            <foreach collection="billCodes" item="billCode" open="(" separator="," close=")">
                #{billCode}
            </foreach>
        </if>
        GROUP BY invoice_project,apply_id,billid
    </select>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" >
        insert into bms_ysbillinvoice(billid,invoicecode,ticket_mode,bill_codes,invoice_type,invoice_project,invoice_word,invoicefee,tax_rate,tax_fee,freight_fee,storage_fee,information_service_fee,platform_fee,brand_fee,invoice_income,companyid,invoice_time,opening_name,opening_bank,card_number,link_phone,taxpayer_num,tac_address,express_code,remark,receiver,invoice_state,void_type,create_code,create_by,create_time,create_dept_id,oper_dept_id,oper_code,oper_by,oper_time,del_flag,ppstorage_fee,zpstorage_fee,ppfreight_fee,zpfreight_fee)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.billid},#{entity.invoicecode},#{entity.ticketMode},#{entity.billCodes},#{entity.invoiceType},#{entity.invoiceProject},#{entity.invoiceWord},#{entity.invoicefee},#{entity.taxRate},#{entity.taxFee},#{entity.freightFee},#{entity.storageFee},#{entity.informationServiceFee},#{entity.platformFee},#{entity.brandFee},#{entity.invoiceIncome},#{entity.companyid},#{entity.invoiceTime},#{entity.openingName},#{entity.openingBank},#{entity.cardNumber},#{entity.linkPhone},#{entity.taxpayerNum},#{entity.tacAddress},#{entity.expressCode},#{entity.remark},#{entity.receiver},#{entity.invoiceState},#{entity.voidType},#{entity.createCode},#{entity.createBy},#{entity.createTime},#{entity.createDeptId},#{entity.operDeptId},#{entity.operCode},#{entity.operBy},#{entity.operTime},#{entity.delFlag},#{entity.ppstorageFee},#{entity.zpstorageFee},#{entity.ppfreightFee},#{entity.zpfreightFee})
        </foreach>
    </insert>

    <update id="updateBill">
        update bms_ysbillmain
        set ticket_state =
        (case when ticket_amount =0 then 1
        when ticket_amount=ticket_apply_amount then 2
        when ticket_amount &lt; ticket_apply_amount then 3
        else 3 end),
        ticket_time =
        (case when ticket_amount =0 then null
        else ticket_time end),
        ticket_user_name =
        (case when ticket_amount =0 then null
        else ticket_user_name end)
        where bill_code in
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
        ;
        update  bms_ysinvoice_apply t1
        left join (
            SELECT billid,invoice_project,invoice_type,SUM(invoicefee) as invoicefee,opening_name from bms_ysbillinvoice where IFNULL(invoice_project,'')!='' and  invoice_state =0 and del_flag =0 GROUP BY billid,invoice_project,invoice_type,opening_name
        ) t2 on t1.billid = t2.billid and t1.invoice_project = t2.invoice_project and t1.invoice_type=t2.invoice_type and t1.opening_name=t2.opening_name
        left join bms_ysbillmain t3 on t1.billid = t3.id
        set t1.invoice_fee = t2.invoicefee
        where t3.bill_code in
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
        and t1.del_flag=0
        and t3.del_flag=0
        ;
        update bms_ysinvoice_apply t1
        left join (
        SELECT billid,invoice_project,invoice_type,SUM(invoicefee) as invoicefee,opening_name from bms_ysbillinvoice where IFNULL(invoice_project,'')!='' and  invoice_state =0 and del_flag =0 GROUP BY billid,invoice_project,invoice_type,opening_name
        ) t2 on t1.billid = t2.billid and t1.invoice_project = t2.invoice_project and t1.invoice_type=t2.invoice_type and t1.opening_name=t2.opening_name
        left join bms_ysbillmain t3 on t1.billid = t3.id
        set t1.ticket_state = (
            case
                when t1.apply_fee = t1.invoice_fee then 2
                when t1.apply_fee > t1.invoice_fee and t1.invoice_fee>0 then 3
            else 1
            end
        )
        where t3.bill_code in
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
        and t1.del_flag=0
        and t3.del_flag=0
        ;
    </update>


    <select id="selectBmsYsbillinvoiceListByOver" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillinvoice">
        SELECT
        id,
        bill_code as billCodes,
        ticket_state
        FROM bms_ysbillmain
        WHERE del_flag=0 AND bill_code IN
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
        AND ticket_state IN (2,4)
        UNION ALL
        SELECT
        id,
        bill_code as billCodes,
        ticket_state
        FROM bms_ysbillmain
        WHERE del_flag=0 AND bill_code IN
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
        AND ticket_state=1 AND apply_audit_state IN (0,2)
    </select>
</mapper>