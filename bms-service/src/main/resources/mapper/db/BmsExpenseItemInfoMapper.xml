<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsExpenseItemInfoMapper">
  <resultMap id="BaseResultMap" type="com.bbyb.joy.bms.domain.dto.BmsExpenseItemInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="father_id" jdbcType="INTEGER" property="fatherId" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="show_type" jdbcType="INTEGER" property="showType" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
    <result column="oper_code" jdbcType="VARCHAR" property="operCode" />
    <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, item_code, item_name, father_id, level, del_flag, oper_by, oper_code, oper_time, show_type,business_type
  </sql>

  <select id="selectByInfo" resultMap="BaseResultMap" parameterType="com.bbyb.joy.bms.domain.dto.BmsExpenseItemInfo">
    select <include refid="Base_Column_List"/> from pub_fee_subject
    <where>
      del_flag = 0
      <if test="level != null">
        and `level` = #{level}
      </if>
      <if test="fatherId != null">
        and father_id = #{fatherId}
      </if>
      <if test="showType != null">
        and show_type = #{showType}
      </if>
      <if test="businessType !=null ">
        and business_type=#{businessType}
      </if>
    </where>
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from pub_fee_subject where
    id in
    <foreach collection="ids" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="getFatherList" resultType="java.util.Map" >
    SELECT
      @r AS _id,
      (SELECT @r := father_id FROM pub_fee_subject WHERE id = _id) AS father_id
    FROM
      (SELECT @r := #{id}) vars, pub_fee_subject AS h
    WHERE @r is NOT NULL
  </select>
  <select id="getExpenseItemBypubYf"  resultMap="BaseResultMap" parameterType="com.bbyb.joy.bms.domain.dto.BmsExpenseItemInfo">
    select <include refid="Base_Column_List"/> from pub_fee_subject
    <where>
      del_flag = 0
      <if test="level != null">
        and `level` = #{level}
      </if>
      <if test="fatherId != null">
        and father_id = #{fatherId}
      </if>
      <if test="showType != null">
        and show_type = #{showType}
      </if>
      <if test="businessType != null">
        and business_type = #{businessType}
      </if>
    </where>
  </select>
</mapper>