<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsCalculateMapper">

    <select id="calculateDataDalibration">
        <choose>
            <when test="handlerType == 1 ">
                UPDATE bms_ysexpenses_middle t1
                INNER JOIN bms_trans_code_info t2 ON t2.pk_id = t1.code_pk_id
                SET
                t1.relate_code = t2.relate_code,
                t1.code_id = t2.id,
                t1.total_weight = t2.total_weight,
                t1.total_volume = t2.total_volume,
                t1.total_number = t2.total_number
                WHERE t1.del_flag = 0 AND t1.code_type = 1
                AND t1.code_pk_id IN
                <foreach collection="codePkIds" item="codePkId" open="(" separator="," close=")">
                    #{codePkId}
                </foreach>
                ;
                UPDATE bms_ys_cost_record t1
                INNER JOIN bms_ysexpenses_middle t2 ON t2.main_code_id = t1.main_code_id
                AND t2.del_flag = 0 AND t2.code_type = 1
                SET t1.main_pk_id = t2.main_pk_id
                WHERE t1.del_flag = 0 AND t1.code_type = 1
                AND t2.code_pk_id IN
                <foreach collection="codePkIds" item="codePkId" open="(" separator="," close=")">
                    #{codePkId}
                </foreach>
            </when>
            <when test="handlerType == 21 ">
                UPDATE bms_ysexpenses_middle t1
                INNER JOIN bms_storage_code_info t2 ON t2.pk_id = t1.code_pk_id AND t2.cost_mode = 1
                SET
                t1.relate_code = t2.relate_code,
                t1.code_id = t2.id,
                t1.total_weight = t2.total_weight,
                t1.total_volume = t2.total_volume,
                t1.total_number = t2.total_number
                WHERE t1.del_flag = 0 AND t1.code_type = 2
                AND t1.code_pk_id IN
                <foreach collection="codePkIds" item="codePkId" open="(" separator="," close=")">
                    #{codePkId}
                </foreach>
                ;
                UPDATE bms_ys_cost_record t1
                INNER JOIN bms_ysexpenses_middle t2 ON t2.main_code_id = t1.main_code_id
                AND t2.del_flag = 0 AND t2.code_type = 2
                SET t1.main_pk_id = t2.main_pk_id
                WHERE t1.del_flag = 0 AND t1.code_type = 2
                AND t2.code_pk_id IN
                <foreach collection="codePkIds" item="codePkId" open="(" separator="," close=")">
                    #{codePkId}
                </foreach>
            </when>
            <when test="handlerType == 22 ">
                UPDATE bms_yfexpenses_middle t1
                INNER JOIN bms_storage_code_info t2 ON t2.pk_id = t1.code_pk_id AND t2.cost_mode = 2
                SET
                t1.relate_code = t2.relate_code,
                t1.code_id = t2.id,
                t1.total_weight = t2.total_weight,
                t1.total_volume = t2.total_volume,
                t1.total_number = t2.total_number
                WHERE t1.del_flag = 0 AND t1.code_type = 2
                AND t1.code_pk_id IN
                <foreach collection="codePkIds" item="codePkId" open="(" separator="," close=")">
                    #{codePkId}
                </foreach>
                ;
                UPDATE bms_yf_cost_record t1
                INNER JOIN bms_yfexpenses_middle t2 ON t2.main_code_id = t1.main_code_id
                AND t2.del_flag = 0 AND t2.code_type = 2
                SET
                t1.main_pk_id = t2.main_pk_id
                WHERE t1.del_flag = 0 AND t1.code_type = 2
                AND t2.code_pk_id IN
                <foreach collection="codePkIds" item="codePkId" open="(" separator="," close=")">
                    #{codePkId}
                </foreach>
            </when>
            <when test="handlerType == 3 ">
                UPDATE bms_yfexpenses_middle t1
                INNER JOIN bms_dispatch_code_info t2 ON t2.pk_id = t1.code_pk_id
                SET
                t1.relate_code = t2.virtual_scheduling_code,
                t1.code_id = t2.id,
                t1.total_weight = t2.total_weight,
                t1.total_volume = t2.total_volume,
                t1.total_number = t2.total_number
                WHERE t1.del_flag = 0 AND t1.code_type = 1
                AND t1.code_pk_id IN
                <foreach collection="codePkIds" item="codePkId" open="(" separator="," close=")">
                    #{codePkId}
                </foreach>
                ;
                UPDATE bms_yfexpenses_middle_share t1
                INNER JOIN bms_job_code_info t2 ON t2.pk_id = t1.code_pk_id
                SET
                t1.relate_code = t2.relate_code,
                t1.code_id = t2.id,
                t1.total_weight = t2.total_weight,
                t1.total_volume = t2.total_volume,
                t1.total_number = t2.total_number,
                t1.code_main_id = t2.main_code_id,
                t1.code_main_pk_id = t2.main_pk_id
                WHERE t1.del_flag = 0
                AND t2.main_pk_id IN
                <foreach collection="codePkIds" item="codePkId" open="(" separator="," close=")">
                    #{codePkId}
                </foreach>
                ;
                UPDATE bms_yf_cost_record t1
                INNER JOIN bms_yfexpenses_middle t2 ON t2.main_code_id = t1.main_code_id
                AND t2.del_flag = 0 AND t2.code_type = 1
                SET
                t1.main_pk_id = t2.main_pk_id
                WHERE t1.del_flag = 0 AND t1.code_type = 1
                AND t2.code_pk_id IN
                <foreach collection="codePkIds" item="codePkId" open="(" separator="," close=")">
                    #{codePkId}
                </foreach>
            </when>
        </choose>
    </select>

</mapper>