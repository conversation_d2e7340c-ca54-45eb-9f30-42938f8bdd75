<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubOperlogMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.PubOperlog" id="PubOperlogResult">
        <result property="id"    column="id"    />
        <result property="userCode"    column="user_code"    />
        <result property="userName"    column="user_name"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="operModule"    column="oper_module"    />
        <result property="operNode"    column="oper_node"    />
        <result property="operContent"    column="oper_content"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="operOrder"    column="oper_order"    />
        <result property="operParam1"    column="oper_param1"    />
        <result property="operParam2"    column="oper_param2"    />
        <result property="operParam3"    column="oper_param3"    />
    </resultMap>

    <sql id="selectPubOperlogVo">
        select id, user_code, user_name, oper_dept_id, oper_time, oper_module, oper_node, oper_content, del_flag, oper_order,oper_param1,oper_param2,oper_param3 from pub_operlog
    </sql>

    <select id="selectPubOperlogList" parameterType="com.bbyb.joy.bms.domain.dto.PubOperlog" resultMap="PubOperlogResult">
        <include refid="selectPubOperlogVo"/>
        <where>

            <if test="userCode != null "> and user_code like concat('%', #{userCode}, '%')</if>
            <if test="userName != null  and userName != '' and nikeName != null  and nikeName != ''"> and (user_name like concat('%', #{userName}, '%') or user_name like concat('%', #{nikeName}, '%') )</if>
            <if test="operDeptId != null "> and oper_dept_id = #{operDeptId}</if>
            <if test="params.beginOperTime != null and params.beginOperTime != '' and params.endOperTime != null and params.endOperTime != ''"> and oper_time between #{params.beginOperTime} and #{params.endOperTime}</if>
            <if test="operModule != null  and operModule != ''"> and oper_module like concat('%',#{operModule},'%') </if>
            <if test="operNode != null  and operNode != ''"> and oper_node like concat('%', #{operNode}, '%')</if>
            <if test="operContent != null  and operContent != ''"> and oper_content like concat('%', #{operContent}, '%')</if>
            <if test="operOrder != null  and operOrder != ''"> and oper_order like concat('%', #{operOrder}, '%')</if>
            and del_flag=0
        </where>
        order by oper_time desc
    </select>
    
    <select id="selectPubOperlogById" parameterType="java.lang.String" resultMap="PubOperlogResult">
        <include refid="selectPubOperlogVo"/>
        where id = #{id}  and del_flag=0
    </select>
        
    <insert id="insertPubOperlog" parameterType="com.bbyb.joy.bms.domain.dto.PubOperlog" useGeneratedKeys="true" keyProperty="id">
        insert into pub_operlog
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userCode != null">user_code,</if>
            <if test="userName != null">user_name,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="operModule != null">oper_module,</if>
            <if test="operNode != null">oper_node,</if>
            <if test="operContent != null">oper_content,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="operOrder != null">oper_order,</if>
            <if test="operParam1 != null">oper_param1,</if>
            <if test="operParam2 != null">oper_param2,</if>
            <if test="operParam3 != null">oper_param3,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userCode != null">#{userCode},</if>
            <if test="userName != null">#{userName},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="operModule != null">#{operModule},</if>
            <if test="operNode != null">#{operNode},</if>
            <if test="operContent != null">#{operContent},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="operOrder != null">#{operOrder},</if>
            <if test="operParam1 != null">#{operParam1},</if>
            <if test="operParam2 != null">#{operParam2},</if>
            <if test="operParam3 != null">#{operParam3},</if>
         </trim>
    </insert>

    <update id="updatePubOperlog" parameterType="com.bbyb.joy.bms.domain.dto.PubOperlog">
        update pub_operlog
        <trim prefix="SET" suffixOverrides=",">
            <if test="userCode != null">user_code = #{userCode},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="operModule != null">oper_module = #{operModule},</if>
            <if test="operNode != null">oper_node = #{operNode},</if>
            <if test="operContent != null">oper_content = #{operContent},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="operOrder != null">oper_order = #{operOrder},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePubOperlogById" parameterType="java.lang.String">
        delete from pub_operlog where id = #{id}
    </delete>

    <delete id="deletePubOperlogByIds">
        delete from pub_operlog where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updatePubOperlogStatusByIds">
        update pub_operlog set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <insert id="insertPubOperlogList" parameterType="com.bbyb.joy.bms.domain.dto.PubOperlog" >
        INSERT INTO pub_operlog
        (
        user_code, user_name, oper_dept_id,oper_time, oper_module,
         oper_node, oper_content, del_flag,oper_order
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.userCode}, #{item.userName}, #{item.operDeptId}, #{item.operTime}, #{item.operModule},
             #{item.operNode}, #{item.operContent}, #{item.delFlag}, #{item.operOrder}
            )
        </foreach>
    </insert>

    <select id="getYsBillCode" resultType="java.lang.String" parameterType="java.lang.Integer">
        select bill_code as BillCode from bms_ysbillmain where id = #{id}
    </select>
    <select id="getYfBillCode" resultType="java.lang.String" parameterType="java.lang.Integer">
        select bill_code as BillCode from bms_yfbillmain where id = #{id}
    </select>

    <select id="getYsBillCodeList" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" parameterType="java.util.List">
        SELECT bill_code AS billCode FROM bms_ysbillmain WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getYfBillCodeList" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain" parameterType="java.util.List">
        select bill_code as BillCode from bms_yfbillmain
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


</mapper>