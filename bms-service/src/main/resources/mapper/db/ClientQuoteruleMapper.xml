<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.ClientQuoteruleMapper">
    <!--主合同新增-->
    <resultMap id="getMainContractResultMap" type="com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleDto">
        <result column="id" property="id"/>
        <result column="rule_code" property="ruleCode"/>
        <result column="rule_name" property="ruleName"/>
        <result column="rule_edit" property="ruleEdit"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="warning_time" property="warningTime"/>
        <result column="rule_type" property="ruleType"/>
        <result column="ht_type" property="htType"/>
        <result column="relation_id" property="relationId"/>
        <result column="business_type" property="businessType"/>
        <result column="user_company_id" property="userCompanyId"/>
        <result column="remark" property="remark"/>
        <result column="project_quotation" property="projectQuotation"/>
        <result column="client_idstr" property="clientIdstr"/>
        <result column="chargeby_weight" property="chargebyWeight" />
        <result column="chargeby_volume" property="chargebyVolume" />
        <result column="decimal_point" property="decimalPoint"/>
        <result column="is_enable" property="isEnable"/>
        <result column="create_by" property="createBy"/>
        <result column="create_code" property="createCode"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept_id" property="createDeptId"/>
        <result column="oper_by" property="operBy"/>
        <result column="oper_code" property="operCode"/>
        <result column="oper_time" property="operTime"/>
        <result column="oper_dept_id" property="operDeptId"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <select id="getMainContract" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleDto">
        SELECT
        t1.id as id,
        t1.rule_code as ruleCode,
        t1.rule_name as ruleName,
        t1.rule_type as ruleType,
        t1.ht_type as htType,
        t1.business_type as businessType,
        t1.relation_id as relationId,
        CASE
        WHEN t1.rule_type = 1 THEN t3.client_name
        WHEN t1.rule_type = 2 THEN t4.carrier_name
        END AS relationName,
        DATE_FORMAT(t1.start_time,'%Y-%m-%d %H:%i:%s') as startTime,
        DATE_FORMAT(t1.end_time,'%Y-%m-%d %H:%i:%s') as endTime,
        DATE_FORMAT(t1.warning_time,'%Y-%m-%d %H:%i:%s') as warningTime,
        t1.is_enable as isEnable,
        CASE
        WHEN (LOCATE(1,IFNULL(GROUP_CONCAT(IFNULL(t2.audit_state,1),','),1))>0 OR LOCATE(3,IFNULL(GROUP_CONCAT(IFNULL(t2.audit_state,1),','),1))>0) AND LOCATE(2,IFNULL(GROUP_CONCAT(IFNULL(t2.audit_state,1),','),1))>0 THEN 3
        WHEN LOCATE(1,IFNULL(GROUP_CONCAT(IFNULL(t2.audit_state,1),','),1))=0 AND LOCATE(3,IFNULL(GROUP_CONCAT(IFNULL(t2.audit_state,1),','),1))=0 AND LOCATE(2,IFNULL(GROUP_CONCAT(IFNULL(t2.audit_state,1),','),1))>0 THEN 2
        WHEN (LOCATE(1,IFNULL(GROUP_CONCAT(IFNULL(t2.audit_state,1),','),1))>0 OR LOCATE(3,IFNULL(GROUP_CONCAT(IFNULL(t2.audit_state,1),','),1))>0) AND LOCATE(2,IFNULL(GROUP_CONCAT(IFNULL(t2.audit_state,1),','),1))=0 THEN 1
        END AS auditState,
        t1.oper_code as operCode,
        t1.oper_by as operBy,
        DATE_FORMAT(t1.oper_time,'%Y-%m-%d %H:%i:%s') as operTime,
        t1.user_company_id as userCompanyId,
        t1.client_idstr as clientIdstr,
        t1.remark,
        CASE
        WHEN t1.rule_type = 1 THEN t3.company_id
        WHEN t1.rule_type = 2 THEN t4.companyid
        END AS companyid,
        CASE
        WHEN now() BETWEEN t1.start_time AND t1.warning_time THEN 1
        WHEN now() BETWEEN t1.warning_time AND t1.end_time THEN 2
        WHEN now() > t1.end_time THEN 3
        WHEN t1.is_enable = 1 then 4
        END AS htState,
        t1.create_by as createBy,
        t1.create_code as createCode,
        t1.create_time as createTime
        FROM pub_quoterule t1
        LEFT JOIN pub_quoterule_detail t2 ON t2.quoterule_id = t1.id AND t2.del_flag = 0
        LEFT JOIN bms_clientinfo t3 ON t1.relation_id = t3.id AND t3.del_flag = 0
        LEFT JOIN bms_carrierinfo t4 ON t1.relation_id = t4.id AND t4.del_flag = 0
        WHERE t1.del_flag = 0
        <if test="id!=null and id!=''">
            and t1.id=#{id}
        </if>
        GROUP BY t1.id
        ORDER BY t1.oper_time DESC
    </select>

    <select id="selectPubQuoteruleDetailList" parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.PubQuoteruleDetail">
        SELECT
        qd.id,
        qd.quoterule_id,
        qd.quoterule_template_id quoteruleTemplateId,
        qd.rulecode,
        q.rule_code AS offerCode,
        q.rule_name AS ruleName,
        qt.rule_name AS ruleTemplate,
        qd.is_enable,
        qd.audit_state,
        qd.fee_type,
        ( CASE q.rule_type WHEN 1 THEN c.client_name END ) AS clientName,
        ( CASE q.rule_type WHEN 2 THEN cr.carrier_name END ) AS carrierName,
        qd.bill_type,
        qd.start_time as start_time,
        qd.end_time as end_time,
        qd.warning_time as warning_time,
        case when  q.rule_type = 1 then c.client_name else  cr.carrier_name end clientOrCarrieName,
        qd.bill_type billType,
        qd.fee_type feeType,
        qd.fee_type_str feeTypeStr
        FROM
        pub_quoterule_detail qd
        LEFT JOIN pub_quoterule q ON q.id = qd.quoterule_id
        AND q.del_flag = 0
        LEFT JOIN pub_quoterule_template qt ON qt.id = qd.quoterule_template_id
        AND qt.del_flag = 0
        LEFT JOIN bms_clientinfo c ON q.relation_id = c.id
        AND c.del_flag = 0
        LEFT JOIN bms_carrierinfo cr ON q.relation_id = cr.id
        AND cr.del_flag = 0
        WHERE qd.del_flag=0
        and qd.rule_type=1
        <if test="id != null and ruleType != ''"> and q.id = #{id}</if>
        GROUP BY qd.id
        ORDER BY qd.oper_time desc
    </select>


    <sql id="selectPubQuotationClassificationdetailVo">
        select id, quoteruledetail_id, store_code, store_name, IFNULL(deliver_province,'') deliver_province,
               IFNULL(deliver_area,'') deliver_area, IFNULL(deliver_city,'') deliver_city,
               IFNULL(receive_province,'') receive_province, IFNULL(receive_area,'') receive_area,
               IFNULL(receive_city,'') receive_city, carmodel, carlong, IFNULL(route_code,'') route_code, warehouse_name,
               bill_type, temperature_zone, specifications, category, is_removezero, business_type,
               minimum_charge, unit_price, step, singular_ladder1, singular_ladder2, singular_ladder3,
               singular_ladder4, singular_ladder5, singular_ladder6, box_ladder1, box_ladder2, box_ladder3,
               box_ladder4, box_ladder5, box_ladder6, weight_ladder1, weight_ladder2, weight_ladder3,
               weight_ladder4, weight_ladder5, weight_ladder6, volume_ladder1, volume_ladder2, volume_ladder3,
               volume_ladder4, volume_ladder5, volume_ladder6, kilometre_ladder1, kilometre_ladder2,
               kilometre_ladder3, kilometre_ladder4, kilometre_ladder5, kilometre_ladder6, ladder_price1,
               ladder_price2, ladder_price3, ladder_price4, ladder_price5, ladder_price6, percentage,
               tornum_ladder1, tornum_ladder2, tornum_ladder3, tornum_ladder4, tornum_ladder5, tornum_ladder6,
               number_ladder1, number_ladder2, number_ladder3, number_ladder4, number_ladder5, number_ladder6,
               base_quantity,
               category1, category2, category3, category4, category5, category6,item_id,item_id1
        from pub_quotation_classificationdetail
    </sql>

    <resultMap id="PubQuotationClassificationdetailResult" type="com.bbyb.joy.bms.domain.dto.PubQuotationClassificationdetail" >
        <result property="id"    column="id"    />
        <result property="quoteruledetailId"    column="quoteruledetail_id"    />
        <result property="storeCode"    column="store_code"    />
        <result property="storeName"    column="store_name"    />
        <result property="deliverProvince"    column="deliver_province"    />
        <result property="deliverArea"    column="deliver_area"    />
        <result property="deliverCity"    column="deliver_city"    />
        <result property="receiveProvince"    column="receive_province"    />
        <result property="receiveArea"    column="receive_area"    />
        <result property="receiveCity"    column="receive_city"    />
        <result property="carmodel"    column="carmodel"    />
        <result property="carlong"    column="carlong"    />
        <result property="routeCode"    column="route_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="billType"    column="bill_type"    />
        <result property="temperatureZone"    column="temperature_zone"    />
        <result property="specifications"    column="specifications"    />
        <result property="category"    column="category"    />
        <result property="isRemovezero"    column="is_removezero"    />
        <result property="businessType"    column="business_type"    />
        <result property="minimumCharge"    column="minimum_charge"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="step"    column="step"    />
        <result property="singularLadder1"    column="singular_ladder1"    />
        <result property="singularLadder2"    column="singular_ladder2"    />
        <result property="singularLadder3"    column="singular_ladder3"    />
        <result property="singularLadder4"    column="singular_ladder4"    />
        <result property="singularLadder5"    column="singular_ladder5"    />
        <result property="singularLadder6"    column="singular_ladder6"    />
        <result property="boxLadder1"    column="box_ladder1"    />
        <result property="boxLadder2"    column="box_ladder2"    />
        <result property="boxLadder3"    column="box_ladder3"    />
        <result property="boxLadder4"    column="box_ladder4"    />
        <result property="boxLadder5"    column="box_ladder5"    />
        <result property="boxLadder6"    column="box_ladder6"    />
        <result property="weightLadder1"    column="weight_ladder1"    />
        <result property="weightLadder2"    column="weight_ladder2"    />
        <result property="weightLadder3"    column="weight_ladder3"    />
        <result property="weightLadder4"    column="weight_ladder4"    />
        <result property="weightLadder5"    column="weight_ladder5"    />
        <result property="weightLadder6"    column="weight_ladder6"    />
        <result property="volumeLadder1"    column="volume_ladder1"    />
        <result property="volumeLadder2"    column="volume_ladder2"    />
        <result property="volumeLadder3"    column="volume_ladder3"    />
        <result property="volumeLadder4"    column="volume_ladder4"    />
        <result property="volumeLadder5"    column="volume_ladder5"    />
        <result property="volumeLadder6"    column="volume_ladder6"    />
        <result property="kilometreLadder1"    column="kilometre_ladder1"    />
        <result property="kilometreLadder2"    column="kilometre_ladder2"    />
        <result property="kilometreLadder3"    column="kilometre_ladder3"    />
        <result property="kilometreLadder4"    column="kilometre_ladder4"    />
        <result property="kilometreLadder5"    column="kilometre_ladder5"    />
        <result property="kilometreLadder6"    column="kilometre_ladder6"    />
        <result property="ladderPrice1"    column="ladder_price1"    />
        <result property="ladderPrice2"    column="ladder_price2"    />
        <result property="ladderPrice3"    column="ladder_price3"    />
        <result property="ladderPrice4"    column="ladder_price4"    />
        <result property="ladderPrice5"    column="ladder_price5"    />
        <result property="ladderPrice6"    column="ladder_price6"    />
        <result property="percentage"    column="percentage"    />
        <result property="tornumLadder1"    column="tornum_ladder1"    />
        <result property="tornumLadder2"    column="tornum_ladder2"    />
        <result property="tornumLadder3"    column="tornum_ladder3"    />
        <result property="tornumLadder4"    column="tornum_ladder4"    />
        <result property="tornumLadder5"    column="tornum_ladder5"    />
        <result property="tornumLadder6"    column="tornum_ladder6"    />
        <result property="numberLadder1"    column="number_ladder1"    />
        <result property="numberLadder2"    column="number_ladder2"    />
        <result property="numberLadder3"    column="number_ladder3"    />
        <result property="numberLadder4"    column="number_ladder4"    />
        <result property="numberLadder5"    column="number_ladder5"    />
        <result property="numberLadder6"    column="number_ladder6"    />
        <result property="baseQuantity"    column="base_quantity"    />
        <result property="category1"    column="category1"    />
        <result property="category2"    column="category2"    />
        <result property="category3"    column="category3"    />
        <result property="category4"    column="category4"    />
        <result property="category5"    column="category5"    />
        <result property="category6"    column="category6"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemId1"    column="item_id1"    />
    </resultMap>
    <select id="getPubClass" resultMap="PubQuotationClassificationdetailResult">
        <include refid="selectPubQuotationClassificationdetailVo"/>
        where
        quoteruledetail_id in
        <foreach item="item" collection="quoteruledetailIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <resultMap id="PubQuoteruleTemplateResultMap" type="com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleTemplateDto">
        <!--@mbg.generated-->
        <!--@Table pub_quoterule_template-->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="rule_code" jdbcType="VARCHAR" property="ruleCode" />
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
        <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
        <result column="consolidation_rule" jdbcType="INTEGER" property="consolidationRule" />
        <result column="group_rule" jdbcType="VARCHAR" property="groupRule" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="calculation_formula" jdbcType="LONGVARCHAR" property="calculationFormula" />
        <result column="calculation_formula_code" jdbcType="LONGVARCHAR" property="calculationFormulaCode" />
        <result column="matching_conditions" jdbcType="LONGVARCHAR" property="matchingConditions" />
        <result column="matching_conditions_code" jdbcType="LONGVARCHAR" property="matchingConditionsCode" />
        <result column="is_enable" jdbcType="INTEGER" property="isEnable" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_dept_id" jdbcType="INTEGER" property="createDeptId" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="oper_dept_id" jdbcType="INTEGER" property="operDeptId" />
        <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
        <result column="calculation_process" jdbcType="LONGVARCHAR" property="calculationProcess" />
        <result column="calculation_process_code" jdbcType="LONGVARCHAR" property="calculationProcessCode" />
        <result column="bj_type" jdbcType="INTEGER" property="bjType" />
        <result column="order_type" jdbcType="INTEGER" property="orderType" />
        <result column="order_type_list" jdbcType="VARCHAR" property="orderTypeList" />
        <collection property="templateDetails" column="id" select="getTemplateDetails">
        </collection>
    </resultMap>
    <resultMap id="getTemplateDetails" type="com.bbyb.joy.bms.domain.dto.PubQuoteruleTemplateDetail">
        
    </resultMap>
    <select id="getQuoteruleTemplate" resultMap="PubQuoteruleTemplateResultMap">
        select
            id,rule_code,rule_name,rule_type,consolidation_rule,group_rule,remark,calculation_formula,calculation_formula_code,matching_conditions,matching_conditions_code,is_enable,create_by,create_time,create_dept_id,oper_by,oper_time,oper_dept_id,del_flag,calculation_process,calculation_process_code,bj_type,order_type,order_type_list
        from pub_quoterule_template
        where del_flag=0
        and id in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    
    <select id="getTemplateDetails" resultMap="getTemplateDetails" parameterType="java.util.Map">
        select
            id,
            template_code templateCode,
            filedseting_id filedsetingId,
            field_chinese fieldChinese,
            field_english fieldEnglish,
            another_name anotherName
        from pub_quoterule_templatedetail
        where del_flag=0
        and quoterule_template_id=#{id}
    </select>

    <select id="getQuoteruleFiledseting" resultType="com.bbyb.joy.bms.domain.dto.PubQuoteruleFiledseting">
        select
            id,
            column_chinese columnChinese,
            column_english columnEnglish
        from pub_quoterule_filedseting
        where id in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>