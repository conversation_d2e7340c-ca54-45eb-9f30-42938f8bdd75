<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubFileExportMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.PubFileExport" id="PubFileExportResult">
        <result property="id"    column="id"    />
        <result property="gid"    column="gid"    />
        <result property="fileName"    column="file_name"    />
        <result property="actionName"    column="action_name"    />
        <result property="physicalPath"    column="physical_path"    />
        <result property="serverVirtualPath"    column="server_virtual_path"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="downloadName"    column="download_name"    />
        <result property="billCode"    column="bill_code"    />
        <result property="isHide"    column="is_hide"    />


    </resultMap>

    <sql id="selectPubFileExportVo">
        select id,gid, file_name, action_name, physical_path, server_virtual_path, status, remark, oper_dept_id, oper_code, oper_by, oper_time, del_flag, download_name from pub_file_export
    </sql>

    <select id="selectPubFileExportList" parameterType="java.util.Map" resultMap="PubFileExportResult">
        <include refid="selectPubFileExportVo"/>
        where  1 = 1
            <if test="operTimeStart != null and operTimeStart != '' and operTimeEnd != null and operTimeEnd!=''">
                AND oper_time >= #{operTimeStart,jdbcType=VARCHAR} AND oper_time &lt;= #{operTimeEnd,jdbcType=VARCHAR}
            </if>
            <if test="companyIds != null and companyIds.size > 0 ">
                AND oper_dept_id in
                <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                    #{companyIds}
                </foreach>
            </if>
        <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
        <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode} </if>
        <if test="status != null  "> and status = #{status} </if>
        <if test="billCode != null  and billCode != ''"> and bill_code = #{billCode} </if>
        <if test="isHide != null  "> and is_hide = #{isHide} </if>

        <if test="gid != null  "> and gid = #{gid} </if>
        and del_flag = '0'
        order by oper_time desc
    </select>
    

    <insert id="insertPubFileExport" parameterType="com.bbyb.joy.bms.domain.dto.PubFileExport">
        insert into pub_file_export
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="gid != null">gid,</if>
            <if test="fileName != null">file_name,</if>
            <if test="billCode != null">bill_code,</if>
            <if test="actionName != null">action_name,</if>
            <if test="physicalPath != null">physical_path,</if>
            <if test="serverVirtualPath != null">server_virtual_path,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="downloadName != null">download_name,</if>
            <if test="isHide != null">is_hide,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="gid != null">#{gid},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="billCode != null">#{billCode},</if>
            <if test="actionName != null">#{actionName},</if>
            <if test="physicalPath != null">#{physicalPath},</if>
            <if test="serverVirtualPath != null">#{serverVirtualPath},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="downloadName != null">#{downloadName},</if>
            <if test="isHide != null">#{isHide},</if>
         </trim>
    </insert>

    <update id="updatePubFileExportStatus" parameterType="com.bbyb.joy.bms.domain.dto.PubFileExport">
        update pub_file_export
        <trim prefix="SET" suffixOverrides=",">
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updatePubFileExportStatusByGid"  parameterType="com.bbyb.joy.bms.domain.dto.PubFileExport">
        update pub_file_export
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="actionName != null">action_name = #{actionName},</if>
            <if test="physicalPath != null">physical_path = #{physicalPath},</if>
            <if test="serverVirtualPath != null">server_virtual_path = #{serverVirtualPath},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="downloadName != null">download_name = #{downloadName},</if>
        </trim>
        where gid = #{gid}
    </update>

</mapper>