<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsQuoteruleMapper">

    <!--主合同新增-->
    <resultMap id="getMainContractResultMap" type="com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleDto">
        <result column="id" property="id"/>
        <result column="rule_code" property="ruleCode"/>
        <result column="rule_name" property="ruleName"/>
        <result column="rule_edit" property="ruleEdit"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="warning_time" property="warningTime"/>
        <result column="rule_type" property="ruleType"/>
        <result column="ht_type" property="htType"/>
        <result column="relation_id" property="relationId"/>
        <result column="business_type" property="businessType"/>
        <result column="user_company_id" property="userCompanyId"/>
        <result column="remark" property="remark"/>
        <result column="project_quotation" property="projectQuotation"/>
        <result column="client_idstr" property="clientIdstr"/>
        <result column="chargeby_weight" property="chargebyWeight" />
        <result column="chargeby_volume" property="chargebyVolume" />
        <result column="decimal_point" property="decimalPoint"/>
        <result column="is_enable" property="isEnable"/>
        <result column="create_by" property="createBy"/>
        <result column="create_code" property="createCode"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept_id" property="createDeptId"/>
        <result column="oper_by" property="operBy"/>
        <result column="oper_code" property="operCode"/>
        <result column="oper_time" property="operTime"/>
        <result column="oper_dept_id" property="operDeptId"/>
        <result column="del_flag" property="delFlag"/>
        <result column="share_type" property="shareType" />
        <result column="weight_value" property="weightValue"/>
    </resultMap>



    <insert id="insertMainContract" parameterType="com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleDto">
        insert into pub_quoterule(id,rule_code,rule_name,rule_edit,start_time,end_time,warning_time,rule_type,relation_id,business_type,user_company_id,remark,project_quotation,client_idstr,chargeby_weight,chargeby_volume,decimal_point,is_enable,create_by,create_code,create_time,create_dept_id,oper_by,oper_code,oper_time,oper_dept_id,del_flag,ht_type,share_type,weight_value)
        values
        (#{id},#{ruleCode},#{ruleName},#{ruleEdit},#{startTime},#{endTime},#{warningTime},#{ruleType},#{relationId},#{businessType},#{userCompanyId},#{remark},#{projectQuotation},#{clientIdstr},#{chargebyWeight},#{chargebyVolume},#{decimalPoint},#{isEnable},#{createBy},#{createCode},#{createTime},#{createDeptId},#{operBy},#{operCode},#{operTime},#{operDeptId},#{delFlag},#{htType},#{shareType},#{weightValue});
    </insert>


    <select id="getMainContract" resultMap="getMainContractResultMap">
        select
            id,rule_code,rule_name,rule_edit,start_time,end_time,warning_time,rule_type,ht_type,relation_id,business_type,user_company_id,remark,project_quotation,client_idstr,chargeby_weight,chargeby_volume,decimal_point,is_enable,create_by,create_code,create_time,create_dept_id,oper_by,oper_code,oper_time,oper_dept_id,del_flag,share_type,weight_value
        from pub_quoterule
        where del_flag = 0 and id = #{id}
    </select>

    <!--主合同修改-->
    <update id="updateMainContract">
        update pub_quoterule
        <set>
            <if test="ruleEdit != null">
                rule_edit = #{ruleEdit},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="warningTime != null">
                warning_time = #{warningTime},
            </if>
            <if test="ruleType != null">
                rule_type = #{ruleType},
            </if>
            <if test="relationId != null">
                relation_id = #{relationId},
            </if>
            <if test="businessType != null">
                business_type = #{businessType},
            </if>
            <if test="userCompanyId != null">
                user_company_id = #{userCompanyId},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="projectQuotation != null">
                project_quotation = #{projectQuotation},
            </if>
            <if test="clientIdstr != null">
                client_idstr = #{clientIdstr},
            </if>
            <if test="decimalPoint != null">
                decimal_point = #{decimalPoint},
            </if>
            <if test="isEnable != null">
                is_enable = #{isEnable},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createCode != null">
                create_code = #{createCode},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="createDeptId != null">
                create_dept_id = #{createDeptId},
            </if>
            <if test="operBy != null">
                oper_by = #{operBy},
            </if>
            <if test="operCode != null">
                oper_code = #{operCode},
            </if>
            <if test="operTime != null">
                oper_time = #{operTime},
            </if>
            <if test="operDeptId != null">
                oper_dept_id = #{operDeptId},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="htType != null">
                ht_type = #{htType},
            </if>
            <if test="shareType!=null">
                share_type = #{shareType},
            </if>
            <if test="weightValue!=null">
                weight_value = #{weightValue},
            </if>
        </set>
        where id = #{id}
    </update>


    <resultMap id="SubContractResultMap" type="com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleDetailDto">
        <!--@mbg.generated-->
        <!--@Table pub_quoterule_detail-->
        <id column="pk_id" jdbcType="BIGINT" property="pkId" />
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="quoterule_id" jdbcType="VARCHAR" property="quoteruleId" />
        <result column="rulecode" jdbcType="VARCHAR" property="rulecode" />
        <result column="fee_type" jdbcType="INTEGER" property="feeType" />
        <result column="fee_type_str" jdbcType="VARCHAR" property="feeTypeStr" />
        <result column="item_id" jdbcType="INTEGER" property="itemId" />
        <result column="is_calculated" jdbcType="INTEGER" property="isCalculated" />
        <result column="quoterule_template_id" jdbcType="VARCHAR" property="quoteruleTemplateId" />
        <result column="is_enable" jdbcType="INTEGER" property="isEnable" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_code" jdbcType="VARCHAR" property="operCode" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="oper_dept_id" jdbcType="INTEGER" property="operDeptId" />
        <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
        <result column="audit_state" jdbcType="INTEGER" property="auditState" />
        <result column="audit_user" jdbcType="VARCHAR" property="auditUser" />
        <result column="audit_user_name" jdbcType="VARCHAR" property="auditUserName" />
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
        <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
        <result column="bill_type" jdbcType="INTEGER" property="billType" />
        <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="warning_time" jdbcType="TIMESTAMP" property="warningTime" />
        <result column="client_id" jdbcType="INTEGER" property="clientId" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_code" property="createCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="getSubContract" resultMap="SubContractResultMap">
        select
            id,pk_id,quoterule_id,rulecode,fee_type,fee_type_str,item_id,is_calculated,quoterule_template_id,is_enable,remark,oper_by,oper_code,oper_time,oper_dept_id,del_flag,audit_state,audit_user,audit_user_name,audit_time,rule_type,bill_type,audit_remark,start_time,end_time,warning_time,client_id,create_by,create_code,create_time
        from pub_quoterule_detail
        where del_flag = 0 and id = #{id}
    </select>

    <!--子合同新增-->
    <insert id="insertSubContract" parameterType="com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleDetailDto">
        insert into pub_quoterule_detail(id,quoterule_id,rulecode,fee_type,fee_type_str,item_id,is_calculated,quoterule_template_id,is_enable,remark,oper_by,oper_code,oper_time,oper_dept_id,del_flag,audit_state,audit_user,audit_user_name,audit_time,rule_type,bill_type,audit_remark,start_time,end_time,warning_time,client_id,create_by,create_code,create_time)
        values (#{id},#{quoteruleId},#{rulecode},#{feeType},#{feeTypeStr},#{itemId},#{isCalculated},#{quoteruleTemplateId},#{isEnable},#{remark},#{operBy},#{operCode},#{operTime},#{operDeptId},#{delFlag},#{auditState},#{auditUser},#{auditUserName},#{auditTime},#{ruleType},#{billType},#{auditRemark},#{startTime},#{endTime},#{warningTime},#{clientId},#{createBy},#{createCode},#{createTime})
    </insert>


    <!-- 批量新增数据 -->
    <insert id="insertBatchSubContract">
        insert into
        pub_quoterule_detail(id,quoterule_id,rulecode,fee_type,fee_type_str,item_id,is_calculated,quoterule_template_id,is_enable,remark,oper_by,oper_code,oper_time,oper_dept_id,del_flag,audit_state,audit_user,audit_user_name,audit_time,rule_type,bill_type,audit_remark,start_time,end_time,warning_time,client_id,create_by,create_code,create_time,extra_field)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.quoteruleId},#{entity.rulecode},#{entity.feeType},#{entity.feeTypeStr},#{entity.itemId},#{entity.isCalculated},#{entity.quoteruleTemplateId},#{entity.isEnable},#{entity.remark},#{entity.operBy},#{entity.operCode},#{entity.operTime},#{entity.operDeptId},#{entity.delFlag},#{entity.auditState},#{entity.auditUser},#{entity.auditUserName},#{entity.auditTime},#{entity.ruleType},#{entity.billType},#{entity.auditRemark},#{entity.startTime},#{entity.endTime},#{entity.warningTime},#{entity.clientId},#{entity.createBy},#{entity.createCode},#{entity.createTime},#{entity.extraField})
        </foreach>
    </insert>

    <update id="updateSubContract">
        update pub_quoterule_detail
        <set>
            <if test="rulecode != null and rulecode != ''">
                rulecode = #{rulecode},
            </if>
            <if test="feeType != null">
                fee_type = #{feeType},
            </if>
            <if test="feeTypeStr != null and feeTypeStr != ''">
                fee_type_str = #{feeTypeStr},
            </if>
            <if test="itemId != null">
                item_id = #{itemId},
            </if>
            <if test="isCalculated != null">
                is_calculated = #{isCalculated},
            </if>
            <if test="quoteruleTemplateId != null and quoteruleTemplateId != ''">
                quoterule_template_id = #{quoteruleTemplateId},
            </if>
            <if test="isEnable != null">
                is_enable = #{isEnable},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="operBy != null and operBy != ''">
                oper_by = #{operBy},
            </if>
            <if test="operCode != null and operCode != ''">
                oper_code = #{operCode},
            </if>
            <if test="operTime != null">
                oper_time = #{operTime},
            </if>
            <if test="operDeptId != null">
                oper_dept_id = #{operDeptId},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="auditState != null">
                audit_state = #{auditState},
            </if>
            <if test="auditUser != null and auditUser != ''">
                audit_user = #{auditUser},
            </if>
            <if test="auditUserName != null and auditUserName != ''">
                audit_user_name = #{auditUserName},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime},
            </if>
            <if test="ruleType != null">
                rule_type = #{ruleType},
            </if>
            <if test="billType != null">
                bill_type = #{billType},
            </if>
            <if test="auditRemark != null and auditRemark != ''">
                audit_remark = #{auditRemark},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="warningTime != null">
                warning_time = #{warningTime},
            </if>
            <if test="clientId != null">
                client_id = #{clientId},
            </if>
        </set>
        where id = #{id}
    </update>



    <update id="updateBatchSubContract">
        <foreach item="entitie" collection="entities" index="index" open="" close="" separator=";" >
            update pub_quoterule_detail
            <set>
                <if test="entitie.rulecode != null and entitie.rulecode != ''">
                    rulecode = #{entitie.rulecode},
                </if>
                <if test="entitie.feeType != null">
                    fee_type = #{entitie.feeType},
                </if>
                <if test="entitie.feeTypeStr != null and entitie.feeTypeStr != ''">
                    fee_type_str = #{entitie.feeTypeStr},
                </if>
                <if test="entitie.itemId != null">
                    item_id = #{entitie.itemId},
                </if>
                <if test="entitie.isCalculated != null">
                    is_calculated = #{entitie.isCalculated},
                </if>
                <if test="entitie.quoteruleTemplateId != null and entitie.quoteruleTemplateId != ''">
                    quoterule_template_id = #{entitie.quoteruleTemplateId},
                </if>
                <if test="entitie.isEnable != null">
                    is_enable = #{entitie.isEnable},
                </if>
                <if test="entitie.remark != null and entitie.remark != ''">
                    remark = #{entitie.remark},
                </if>
                <if test="entitie.operBy != null and entitie.operBy != ''">
                    oper_by = #{entitie.operBy},
                </if>
                <if test="entitie.operCode != null and entitie.operCode != ''">
                    oper_code = #{entitie.operCode},
                </if>
                <if test="entitie.operTime != null">
                    oper_time = #{entitie.operTime},
                </if>
                <if test="entitie.operDeptId != null">
                    oper_dept_id = #{entitie.operDeptId},
                </if>
                <if test="entitie.delFlag != null">
                    del_flag = #{entitie.delFlag},
                </if>
                <if test="entitie.auditState != null">
                    audit_state = #{entitie.auditState},
                </if>
                <if test="entitie.auditUser != null and entitie.auditUser != ''">
                    audit_user = #{entitie.auditUser},
                </if>
                <if test="entitie.auditUserName != null and entitie.auditUserName != ''">
                    audit_user_name = #{entitie.auditUserName},
                </if>
                <if test="entitie.auditTime != null">
                    audit_time = #{entitie.auditTime},
                </if>
                <if test="entitie.ruleType != null">
                    rule_type = #{entitie.ruleType},
                </if>
                <if test="entitie.billType != null">
                    bill_type = #{entitie.billType},
                </if>
                <if test="entitie.auditRemark != null">
                    audit_remark = #{entitie.auditRemark},
                </if>
                <if test="entitie.startTime != null">
                    start_time = #{entitie.startTime},
                </if>
                <if test="entitie.endTime != null">
                    end_time = #{entitie.endTime},
                </if>
                <if test="entitie.warningTime != null">
                    warning_time = #{entitie.warningTime},
                </if>
                <if test="entitie.clientId != null">
                    client_id = #{entitie.clientId},
                </if>
                <if test="entitie.extraField !=null">
                    extra_field = #{entitie.extraField}
                </if>
            </set>
            where id = #{entitie.id}
        </foreach>
    </update>



    <resultMap id="PubQuotationClassificationdetailResultMap" type="com.bbyb.joy.bms.domain.dto.dto.PubQuotationClassificationdetailDto">
        <!--@mbg.generated-->
        <!--@Table pub_quotation_classificationdetail-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="quoteruledetail_id" jdbcType="VARCHAR" property="quoteruledetailId" />
        <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
        <result column="store_name" jdbcType="VARCHAR" property="storeName" />
        <result column="deliver_province" jdbcType="VARCHAR" property="deliverProvince" />
        <result column="deliver_area" jdbcType="VARCHAR" property="deliverArea" />
        <result column="deliver_city" jdbcType="VARCHAR" property="deliverCity" />
        <result column="receive_province" jdbcType="VARCHAR" property="receiveProvince" />
        <result column="receive_area" jdbcType="VARCHAR" property="receiveArea" />
        <result column="receive_city" jdbcType="VARCHAR" property="receiveCity" />
        <result column="carmodel" jdbcType="VARCHAR" property="carmodel" />
        <result column="carlong" jdbcType="VARCHAR" property="carlong" />
        <result column="route_code" jdbcType="VARCHAR" property="routeCode" />
        <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
        <result column="bill_type" jdbcType="INTEGER" property="billType" />
        <result column="temperature_zone" jdbcType="VARCHAR" property="temperatureZone" />
        <result column="specifications" jdbcType="VARCHAR" property="specifications" />
        <result column="category" jdbcType="VARCHAR" property="category" />
        <result column="is_removezero" jdbcType="VARCHAR" property="isRemovezero" />
        <result column="business_type" jdbcType="INTEGER" property="businessType" />
        <result column="minimum_charge" jdbcType="DECIMAL" property="minimumCharge" />
        <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
        <result column="step" jdbcType="DECIMAL" property="step" />
        <result column="singular_ladder1" jdbcType="DECIMAL" property="singularLadder1" />
        <result column="singular_ladder2" jdbcType="DECIMAL" property="singularLadder2" />
        <result column="singular_ladder3" jdbcType="DECIMAL" property="singularLadder3" />
        <result column="singular_ladder4" jdbcType="DECIMAL" property="singularLadder4" />
        <result column="singular_ladder5" jdbcType="DECIMAL" property="singularLadder5" />
        <result column="singular_ladder6" jdbcType="DECIMAL" property="singularLadder6" />
        <result column="box_ladder1" jdbcType="DECIMAL" property="boxLadder1" />
        <result column="box_ladder2" jdbcType="DECIMAL" property="boxLadder2" />
        <result column="box_ladder3" jdbcType="DECIMAL" property="boxLadder3" />
        <result column="box_ladder4" jdbcType="DECIMAL" property="boxLadder4" />
        <result column="box_ladder5" jdbcType="DECIMAL" property="boxLadder5" />
        <result column="box_ladder6" jdbcType="DECIMAL" property="boxLadder6" />
        <result column="weight_ladder1" jdbcType="DECIMAL" property="weightLadder1" />
        <result column="weight_ladder2" jdbcType="DECIMAL" property="weightLadder2" />
        <result column="weight_ladder3" jdbcType="DECIMAL" property="weightLadder3" />
        <result column="weight_ladder4" jdbcType="DECIMAL" property="weightLadder4" />
        <result column="weight_ladder5" jdbcType="DECIMAL" property="weightLadder5" />
        <result column="weight_ladder6" jdbcType="DECIMAL" property="weightLadder6" />
        <result column="volume_ladder1" jdbcType="DECIMAL" property="volumeLadder1" />
        <result column="volume_ladder2" jdbcType="DECIMAL" property="volumeLadder2" />
        <result column="volume_ladder3" jdbcType="DECIMAL" property="volumeLadder3" />
        <result column="volume_ladder4" jdbcType="DECIMAL" property="volumeLadder4" />
        <result column="volume_ladder5" jdbcType="DECIMAL" property="volumeLadder5" />
        <result column="volume_ladder6" jdbcType="DECIMAL" property="volumeLadder6" />
        <result column="kilometre_ladder1" jdbcType="DECIMAL" property="kilometreLadder1" />
        <result column="kilometre_ladder2" jdbcType="DECIMAL" property="kilometreLadder2" />
        <result column="kilometre_ladder3" jdbcType="DECIMAL" property="kilometreLadder3" />
        <result column="kilometre_ladder4" jdbcType="DECIMAL" property="kilometreLadder4" />
        <result column="kilometre_ladder5" jdbcType="DECIMAL" property="kilometreLadder5" />
        <result column="kilometre_ladder6" jdbcType="DECIMAL" property="kilometreLadder6" />
        <result column="ladder_price1" jdbcType="DECIMAL" property="ladderPrice1" />
        <result column="ladder_price2" jdbcType="DECIMAL" property="ladderPrice2" />
        <result column="ladder_price3" jdbcType="DECIMAL" property="ladderPrice3" />
        <result column="ladder_price4" jdbcType="DECIMAL" property="ladderPrice4" />
        <result column="ladder_price5" jdbcType="DECIMAL" property="ladderPrice5" />
        <result column="ladder_price6" jdbcType="DECIMAL" property="ladderPrice6" />
        <result column="percentage" jdbcType="DECIMAL" property="percentage" />
        <result column="tornum_ladder1" jdbcType="DECIMAL" property="tornumLadder1" />
        <result column="tornum_ladder2" jdbcType="DECIMAL" property="tornumLadder2" />
        <result column="tornum_ladder3" jdbcType="DECIMAL" property="tornumLadder3" />
        <result column="tornum_ladder4" jdbcType="DECIMAL" property="tornumLadder4" />
        <result column="tornum_ladder5" jdbcType="DECIMAL" property="tornumLadder5" />
        <result column="tornum_ladder6" jdbcType="DECIMAL" property="tornumLadder6" />
        <result column="number_ladder1" jdbcType="VARCHAR" property="numberLadder1" />
        <result column="number_ladder2" jdbcType="VARCHAR" property="numberLadder2" />
        <result column="number_ladder3" jdbcType="VARCHAR" property="numberLadder3" />
        <result column="number_ladder4" jdbcType="VARCHAR" property="numberLadder4" />
        <result column="number_ladder5" jdbcType="VARCHAR" property="numberLadder5" />
        <result column="number_ladder6" jdbcType="VARCHAR" property="numberLadder6" />
        <result column="base_quantity" jdbcType="INTEGER" property="baseQuantity" />
        <result column="category1" jdbcType="VARCHAR" property="category1" />
        <result column="category2" jdbcType="VARCHAR" property="category2" />
        <result column="category3" jdbcType="VARCHAR" property="category3" />
        <result column="category4" jdbcType="VARCHAR" property="category4" />
        <result column="category5" jdbcType="VARCHAR" property="category5" />
        <result column="category6" jdbcType="VARCHAR" property="category6" />
        <result column="item_id" jdbcType="INTEGER" property="itemId" />
        <result column="item_id1" jdbcType="INTEGER" property="itemId1" />
        <result column="itemIdName" jdbcType="VARCHAR" property="itemIdName" />
        <result column="itemId1Name" jdbcType="VARCHAR" property="itemId1Name" />
        <result column="costUnitName" jdbcType="VARCHAR" property="costUnitName" />
        <result column="costUnit" jdbcType="VARCHAR" property="costUnit" />
    </resultMap>

    <select id="selClassification" resultMap="PubQuotationClassificationdetailResultMap">
        select
        id,quoteruledetail_id,store_code,store_name,deliver_province,deliver_area,deliver_city,receive_province,receive_area,receive_city,carmodel,carlong,route_code,warehouse_name,bill_type,temperature_zone,specifications,category,is_removezero,business_type,minimum_charge,unit_price,step,singular_ladder1,singular_ladder2,singular_ladder3,singular_ladder4,singular_ladder5,singular_ladder6,box_ladder1,box_ladder2,box_ladder3,box_ladder4,box_ladder5,box_ladder6,weight_ladder1,weight_ladder2,weight_ladder3,weight_ladder4,weight_ladder5,weight_ladder6,volume_ladder1,volume_ladder2,volume_ladder3,volume_ladder4,volume_ladder5,volume_ladder6,kilometre_ladder1,kilometre_ladder2,kilometre_ladder3,kilometre_ladder4,kilometre_ladder5,kilometre_ladder6,ladder_price1,ladder_price2,ladder_price3,ladder_price4,ladder_price5,ladder_price6,percentage,tornum_ladder1,tornum_ladder2,tornum_ladder3,tornum_ladder4,tornum_ladder5,tornum_ladder6,number_ladder1,number_ladder2,number_ladder3,number_ladder4,number_ladder5,number_ladder6,base_quantity,category1,category2,category3,category4,category5,category6,item_id,item_id1
        from pub_quotation_classificationdetail
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="quoteruledetailId != null and quoteruledetailId != ''">
                and quoteruledetail_id = #{quoteruledetailId}
            </if>
            <if test="storeCode != null and storeCode != ''">
                and store_code = #{storeCode}
            </if>
            <if test="storeName != null and storeName != ''">
                and store_name = #{storeName}
            </if>
            <if test="deliverProvince != null and deliverProvince != ''">
                and deliver_province = #{deliverProvince}
            </if>
            <if test="deliverArea != null and deliverArea != ''">
                and deliver_area = #{deliverArea}
            </if>
            <if test="deliverCity != null and deliverCity != ''">
                and deliver_city = #{deliverCity}
            </if>
            <if test="receiveProvince != null and receiveProvince != ''">
                and receive_province = #{receiveProvince}
            </if>
            <if test="receiveArea != null and receiveArea != ''">
                and receive_area = #{receiveArea}
            </if>
            <if test="receiveCity != null and receiveCity != ''">
                and receive_city = #{receiveCity}
            </if>
            <if test="carmodel != null and carmodel != ''">
                and carmodel = #{carmodel}
            </if>
            <if test="carlong != null and carlong != ''">
                and carlong = #{carlong}
            </if>
            <if test="routeCode != null and routeCode != ''">
                and route_code = #{routeCode}
            </if>
            <if test="warehouseName != null and warehouseName != ''">
                and warehouse_name = #{warehouseName}
            </if>
            <if test="billType != null and billType != ''">
                and bill_type = #{billType}
            </if>
            <if test="temperatureZone != null and temperatureZone != ''">
                and temperature_zone = #{temperatureZone}
            </if>
            <if test="specifications != null and specifications != ''">
                and specifications = #{specifications}
            </if>
            <if test="category != null and category != ''">
                and category = #{category}
            </if>
            <if test="isRemovezero != null and isRemovezero != ''">
                and is_removezero = #{isRemovezero}
            </if>
            <if test="businessType != null and businessType != ''">
                and business_type = #{businessType}
            </if>
            <if test="minimumCharge != null and minimumCharge != ''">
                and minimum_charge = #{minimumCharge}
            </if>
            <if test="unitPrice != null and unitPrice != ''">
                and unit_price = #{unitPrice}
            </if>
            <if test="step != null and step != ''">
                and step = #{step}
            </if>
            <if test="singularLadder1 != null and singularLadder1 != ''">
                and singular_ladder1 = #{singularLadder1}
            </if>
            <if test="singularLadder2 != null and singularLadder2 != ''">
                and singular_ladder2 = #{singularLadder2}
            </if>
            <if test="singularLadder3 != null and singularLadder3 != ''">
                and singular_ladder3 = #{singularLadder3}
            </if>
            <if test="singularLadder4 != null and singularLadder4 != ''">
                and singular_ladder4 = #{singularLadder4}
            </if>
            <if test="singularLadder5 != null and singularLadder5 != ''">
                and singular_ladder5 = #{singularLadder5}
            </if>
            <if test="singularLadder6 != null and singularLadder6 != ''">
                and singular_ladder6 = #{singularLadder6}
            </if>
            <if test="boxLadder1 != null and boxLadder1 != ''">
                and box_ladder1 = #{boxLadder1}
            </if>
            <if test="boxLadder2 != null and boxLadder2 != ''">
                and box_ladder2 = #{boxLadder2}
            </if>
            <if test="boxLadder3 != null and boxLadder3 != ''">
                and box_ladder3 = #{boxLadder3}
            </if>
            <if test="boxLadder4 != null and boxLadder4 != ''">
                and box_ladder4 = #{boxLadder4}
            </if>
            <if test="boxLadder5 != null and boxLadder5 != ''">
                and box_ladder5 = #{boxLadder5}
            </if>
            <if test="boxLadder6 != null and boxLadder6 != ''">
                and box_ladder6 = #{boxLadder6}
            </if>
            <if test="weightLadder1 != null and weightLadder1 != ''">
                and weight_ladder1 = #{weightLadder1}
            </if>
            <if test="weightLadder2 != null and weightLadder2 != ''">
                and weight_ladder2 = #{weightLadder2}
            </if>
            <if test="weightLadder3 != null and weightLadder3 != ''">
                and weight_ladder3 = #{weightLadder3}
            </if>
            <if test="weightLadder4 != null and weightLadder4 != ''">
                and weight_ladder4 = #{weightLadder4}
            </if>
            <if test="weightLadder5 != null and weightLadder5 != ''">
                and weight_ladder5 = #{weightLadder5}
            </if>
            <if test="weightLadder6 != null and weightLadder6 != ''">
                and weight_ladder6 = #{weightLadder6}
            </if>
            <if test="volumeLadder1 != null and volumeLadder1 != ''">
                and volume_ladder1 = #{volumeLadder1}
            </if>
            <if test="volumeLadder2 != null and volumeLadder2 != ''">
                and volume_ladder2 = #{volumeLadder2}
            </if>
            <if test="volumeLadder3 != null and volumeLadder3 != ''">
                and volume_ladder3 = #{volumeLadder3}
            </if>
            <if test="volumeLadder4 != null and volumeLadder4 != ''">
                and volume_ladder4 = #{volumeLadder4}
            </if>
            <if test="volumeLadder5 != null and volumeLadder5 != ''">
                and volume_ladder5 = #{volumeLadder5}
            </if>
            <if test="volumeLadder6 != null and volumeLadder6 != ''">
                and volume_ladder6 = #{volumeLadder6}
            </if>
            <if test="kilometreLadder1 != null and kilometreLadder1 != ''">
                and kilometre_ladder1 = #{kilometreLadder1}
            </if>
            <if test="kilometreLadder2 != null and kilometreLadder2 != ''">
                and kilometre_ladder2 = #{kilometreLadder2}
            </if>
            <if test="kilometreLadder3 != null and kilometreLadder3 != ''">
                and kilometre_ladder3 = #{kilometreLadder3}
            </if>
            <if test="kilometreLadder4 != null and kilometreLadder4 != ''">
                and kilometre_ladder4 = #{kilometreLadder4}
            </if>
            <if test="kilometreLadder5 != null and kilometreLadder5 != ''">
                and kilometre_ladder5 = #{kilometreLadder5}
            </if>
            <if test="kilometreLadder6 != null and kilometreLadder6 != ''">
                and kilometre_ladder6 = #{kilometreLadder6}
            </if>
            <if test="ladderPrice1 != null and ladderPrice1 != ''">
                and ladder_price1 = #{ladderPrice1}
            </if>
            <if test="ladderPrice2 != null and ladderPrice2 != ''">
                and ladder_price2 = #{ladderPrice2}
            </if>
            <if test="ladderPrice3 != null and ladderPrice3 != ''">
                and ladder_price3 = #{ladderPrice3}
            </if>
            <if test="ladderPrice4 != null and ladderPrice4 != ''">
                and ladder_price4 = #{ladderPrice4}
            </if>
            <if test="ladderPrice5 != null and ladderPrice5 != ''">
                and ladder_price5 = #{ladderPrice5}
            </if>
            <if test="ladderPrice6 != null and ladderPrice6 != ''">
                and ladder_price6 = #{ladderPrice6}
            </if>
            <if test="percentage != null and percentage != ''">
                and percentage = #{percentage}
            </if>
            <if test="tornumLadder1 != null and tornumLadder1 != ''">
                and tornum_ladder1 = #{tornumLadder1}
            </if>
            <if test="tornumLadder2 != null and tornumLadder2 != ''">
                and tornum_ladder2 = #{tornumLadder2}
            </if>
            <if test="tornumLadder3 != null and tornumLadder3 != ''">
                and tornum_ladder3 = #{tornumLadder3}
            </if>
            <if test="tornumLadder4 != null and tornumLadder4 != ''">
                and tornum_ladder4 = #{tornumLadder4}
            </if>
            <if test="tornumLadder5 != null and tornumLadder5 != ''">
                and tornum_ladder5 = #{tornumLadder5}
            </if>
            <if test="tornumLadder6 != null and tornumLadder6 != ''">
                and tornum_ladder6 = #{tornumLadder6}
            </if>
            <if test="numberLadder1 != null and numberLadder1 != ''">
                and number_ladder1 = #{numberLadder1}
            </if>
            <if test="numberLadder2 != null and numberLadder2 != ''">
                and number_ladder2 = #{numberLadder2}
            </if>
            <if test="numberLadder3 != null and numberLadder3 != ''">
                and number_ladder3 = #{numberLadder3}
            </if>
            <if test="numberLadder4 != null and numberLadder4 != ''">
                and number_ladder4 = #{numberLadder4}
            </if>
            <if test="numberLadder5 != null and numberLadder5 != ''">
                and number_ladder5 = #{numberLadder5}
            </if>
            <if test="numberLadder6 != null and numberLadder6 != ''">
                and number_ladder6 = #{numberLadder6}
            </if>
            <if test="baseQuantity != null and baseQuantity != ''">
                and base_quantity = #{baseQuantity}
            </if>
            <if test="category1 != null and category1 != ''">
                and category1 = #{category1}
            </if>
            <if test="category2 != null and category2 != ''">
                and category2 = #{category2}
            </if>
            <if test="category3 != null and category3 != ''">
                and category3 = #{category3}
            </if>
            <if test="category4 != null and category4 != ''">
                and category4 = #{category4}
            </if>
            <if test="category5 != null and category5 != ''">
                and category5 = #{category5}
            </if>
            <if test="category6 != null and category6 != ''">
                and category6 = #{category6}
            </if>
            <if test="itemId != null and itemId != ''">
                and item_id = #{itemId}
            </if>
            <if test="itemId1 != null and itemId1 != ''">
                and item_id1 = #{itemId1}
            </if>
        </where>
    </select>

    <insert id="insertBatchClassification">
        insert into
        pub_quotation_classificationdetail(quoteruledetail_id,store_code,store_name,deliver_province,deliver_area,deliver_city,receive_province,receive_area,receive_city,carmodel,carlong,route_code,warehouse_name,bill_type,temperature_zone,specifications,category,is_removezero,business_type,minimum_charge,unit_price,step,singular_ladder1,singular_ladder2,singular_ladder3,singular_ladder4,singular_ladder5,singular_ladder6,box_ladder1,box_ladder2,box_ladder3,box_ladder4,box_ladder5,box_ladder6,weight_ladder1,weight_ladder2,weight_ladder3,weight_ladder4,weight_ladder5,weight_ladder6,volume_ladder1,volume_ladder2,volume_ladder3,volume_ladder4,volume_ladder5,volume_ladder6,kilometre_ladder1,kilometre_ladder2,kilometre_ladder3,kilometre_ladder4,kilometre_ladder5,kilometre_ladder6,ladder_price1,ladder_price2,ladder_price3,ladder_price4,ladder_price5,ladder_price6,percentage,tornum_ladder1,tornum_ladder2,tornum_ladder3,tornum_ladder4,tornum_ladder5,tornum_ladder6,number_ladder1,number_ladder2,number_ladder3,number_ladder4,number_ladder5,number_ladder6,base_quantity,category1,category2,category3,category4,category5,category6,item_id,item_id1,cost_unit)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.quoteruledetailId},#{entity.storeCode},#{entity.storeName},#{entity.deliverProvince},#{entity.deliverArea},#{entity.deliverCity},#{entity.receiveProvince},#{entity.receiveArea},#{entity.receiveCity},#{entity.carmodel},#{entity.carlong},#{entity.routeCode},#{entity.warehouseName},#{entity.billType},#{entity.temperatureZone},#{entity.specifications},#{entity.category},#{entity.isRemovezero},#{entity.businessType},#{entity.minimumCharge},#{entity.unitPrice},#{entity.step},#{entity.singularLadder1},#{entity.singularLadder2},#{entity.singularLadder3},#{entity.singularLadder4},#{entity.singularLadder5},#{entity.singularLadder6},#{entity.boxLadder1},#{entity.boxLadder2},#{entity.boxLadder3},#{entity.boxLadder4},#{entity.boxLadder5},#{entity.boxLadder6},#{entity.weightLadder1},#{entity.weightLadder2},#{entity.weightLadder3},#{entity.weightLadder4},#{entity.weightLadder5},#{entity.weightLadder6},#{entity.volumeLadder1},#{entity.volumeLadder2},#{entity.volumeLadder3},#{entity.volumeLadder4},#{entity.volumeLadder5},#{entity.volumeLadder6},#{entity.kilometreLadder1},#{entity.kilometreLadder2},#{entity.kilometreLadder3},#{entity.kilometreLadder4},#{entity.kilometreLadder5},#{entity.kilometreLadder6},#{entity.ladderPrice1},#{entity.ladderPrice2},#{entity.ladderPrice3},#{entity.ladderPrice4},#{entity.ladderPrice5},#{entity.ladderPrice6},#{entity.percentage},#{entity.tornumLadder1},#{entity.tornumLadder2},#{entity.tornumLadder3},#{entity.tornumLadder4},#{entity.tornumLadder5},#{entity.tornumLadder6},#{entity.numberLadder1},#{entity.numberLadder2},#{entity.numberLadder3},#{entity.numberLadder4},#{entity.numberLadder5},#{entity.numberLadder6},#{entity.baseQuantity},#{entity.category1},#{entity.category2},#{entity.category3},#{entity.category4},#{entity.category5},#{entity.category6},#{entity.itemId},#{entity.itemId1},#{entity.costUnit})
        </foreach>
    </insert>

    <delete id="deleteBatchClassification">
        delete from pub_quotation_classificationdetail
        where quoteruledetail_id in
        <foreach item="entitie" collection="entities" open="(" separator="," close=")">
            #{entitie}
        </foreach>
    </delete>

    <!--新增报价模板数据-->
    <insert id="insertQuoteruleTemplate">
        insert into pub_quoterule_template(id,rule_code,rule_name,rule_type,consolidation_rule,group_rule,remark,calculation_formula,calculation_formula_code,matching_conditions,matching_conditions_code,is_enable,create_by,create_time,create_dept_id,oper_by,oper_time,oper_dept_id,del_flag,calculation_process,calculation_process_code,bj_type,order_type,order_type_list)
        values (#{id},#{ruleCode},#{ruleName},#{ruleType},#{consolidationRule},#{groupRule},#{remark},#{calculationFormula},#{calculationFormulaCode},#{matchingConditions},#{matchingConditionsCode},#{isEnable},#{createBy},#{createTime},#{createDeptId},#{operBy},#{operTime},#{operDeptId},#{delFlag},#{calculationProcess},#{calculationProcessCode},#{bjType},#{orderType},#{orderTypeList})
    </insert>

    <!--批量新增报价模板数据-->
    <insert id="insertBatchQuoteruleTemplate">
        insert into pub_quoterule_template(id,rule_code,rule_name,rule_type,consolidation_rule,group_rule,remark,calculation_formula,calculation_formula_code,matching_conditions,matching_conditions_code,is_enable,create_by,create_time,create_dept_id,oper_by,oper_time,oper_dept_id,del_flag,calculation_process,calculation_process_code,bj_type,order_type,order_type_list)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.ruleCode},#{entity.ruleName},#{entity.ruleType},#{entity.consolidationRule},#{entity.groupRule},#{entity.remark},#{entity.calculationFormula},#{entity.calculationFormulaCode},#{entity.matchingConditions},#{entity.matchingConditionsCode},#{entity.isEnable},#{entity.createBy},#{entity.createTime},#{entity.createDeptId},#{entity.operBy},#{entity.operTime},#{entity.operDeptId},#{entity.delFlag},#{entity.calculationProcess},#{entity.calculationProcessCode},#{entity.bjType},#{entity.orderType},#{entity.orderTypeList})
        </foreach>
    </insert>


    <resultMap id="PubQuoteruleTemplateResultMap" type="com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleTemplateDto">
        <!--@mbg.generated-->
        <!--@Table pub_quoterule_template-->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="rule_code" jdbcType="VARCHAR" property="ruleCode" />
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
        <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
        <result column="consolidation_rule" jdbcType="INTEGER" property="consolidationRule" />
        <result column="group_rule" jdbcType="VARCHAR" property="groupRule" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="calculation_formula" jdbcType="LONGVARCHAR" property="calculationFormula" />
        <result column="calculation_formula_code" jdbcType="LONGVARCHAR" property="calculationFormulaCode" />
        <result column="matching_conditions" jdbcType="LONGVARCHAR" property="matchingConditions" />
        <result column="matching_conditions_code" jdbcType="LONGVARCHAR" property="matchingConditionsCode" />
        <result column="is_enable" jdbcType="INTEGER" property="isEnable" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_dept_id" jdbcType="INTEGER" property="createDeptId" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="oper_dept_id" jdbcType="INTEGER" property="operDeptId" />
        <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
        <result column="calculation_process" jdbcType="LONGVARCHAR" property="calculationProcess" />
        <result column="calculation_process_code" jdbcType="LONGVARCHAR" property="calculationProcessCode" />
        <result column="bj_type" jdbcType="INTEGER" property="bjType" />
        <result column="order_type" jdbcType="INTEGER" property="orderType" />
        <result column="order_type_list" jdbcType="VARCHAR" property="orderTypeList" />
    </resultMap>

    <select id="selQuoteruleTemplate" resultMap="PubQuoteruleTemplateResultMap" >
        select
            id,rule_code,rule_name,rule_type,consolidation_rule,group_rule,remark,calculation_formula,calculation_formula_code,matching_conditions,matching_conditions_code,is_enable,create_by,create_time,create_dept_id,oper_by,oper_time,oper_dept_id,del_flag,calculation_process,calculation_process_code,bj_type,order_type,order_type_list
        from pub_quoterule_template
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="ruleCode != null and ruleCode != ''">
                and rule_code = #{ruleCode}
            </if>
            <if test="ruleName != null and ruleName != ''">
                and rule_name = #{ruleName}
            </if>
            <if test="ruleType != null and ruleType != ''">
                and rule_type = #{ruleType}
            </if>
            <if test="consolidationRule != null and consolidationRule != ''">
                and consolidation_rule = #{consolidationRule}
            </if>
            <if test="groupRule != null and groupRule != ''">
                and group_rule = #{groupRule}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="calculationFormula != null and calculationFormula != ''">
                and calculation_formula = #{calculationFormula}
            </if>
            <if test="calculationFormulaCode != null and calculationFormulaCode != ''">
                and calculation_formula_code = #{calculationFormulaCode}
            </if>
            <if test="matchingConditions != null and matchingConditions != ''">
                and matching_conditions = #{matchingConditions}
            </if>
            <if test="matchingConditionsCode != null and matchingConditionsCode != ''">
                and matching_conditions_code = #{matchingConditionsCode}
            </if>
            <if test="isEnable != null and isEnable != ''">
                and is_enable = #{isEnable}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="createDeptId != null and createDeptId != ''">
                and create_dept_id = #{createDeptId}
            </if>
            <if test="operBy != null and operBy != ''">
                and oper_by = #{operBy}
            </if>
            <if test="operTime != null and operTime != ''">
                and oper_time = #{operTime}
            </if>
            <if test="operDeptId != null and operDeptId != ''">
                and oper_dept_id = #{operDeptId}
            </if>
            <if test="delFlag != null and delFlag != ''">
                and del_flag = #{delFlag}
            </if>
            <if test="calculationProcess != null and calculationProcess != ''">
                and calculation_process = #{calculationProcess}
            </if>
            <if test="calculationProcessCode != null and calculationProcessCode != ''">
                and calculation_process_code = #{calculationProcessCode}
            </if>
            <if test="bjType != null and bjType != ''">
                and bj_type = #{bjType}
            </if>
            <if test="orderType != null and orderType != ''">
                and order_type = #{orderType}
            </if>
            <if test="orderTypeList != null and orderTypeList != ''">
                and order_type_list = #{orderTypeList}
            </if>
        </where>
    </select>

    <select id="getQuoteruleTemplate" resultMap="PubQuoteruleTemplateResultMap">
        select
        id,rule_code,rule_name,rule_type,consolidation_rule,group_rule,remark,calculation_formula,calculation_formula_code,matching_conditions,matching_conditions_code,is_enable,create_by,create_time,create_dept_id,oper_by,oper_time,oper_dept_id,del_flag,calculation_process,calculation_process_code,bj_type,order_type,order_type_list
        from pub_quoterule_template
        where id = #{id}
    </select>


    <resultMap id="quoteruleDetailResultMap" type="com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleTemplatedetailDto">
        <!--@mbg.generated-->
        <!--@Table pub_quoterule_templatedetail-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="quoterule_template_id" jdbcType="VARCHAR" property="quoteruleTemplateId" />
        <result column="template_code" jdbcType="VARCHAR" property="templateCode" />
        <result column="filedseting_id" jdbcType="INTEGER" property="filedsetingId" />
        <result column="field_chinese" jdbcType="VARCHAR" property="fieldChinese" />
        <result column="field_english" jdbcType="VARCHAR" property="fieldEnglish" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
        <result column="another_name" jdbcType="VARCHAR" property="anotherName" />
    </resultMap>


    <select id="selQuoteruleTemplatedetailList" resultMap="quoteruleDetailResultMap">
        select
            id,quoterule_template_id,template_code,filedseting_id,field_chinese,field_english,oper_by,oper_time,del_flag,another_name
        from pub_quoterule_templatedetail
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="quoteruleTemplateId != null and quoteruleTemplateId != ''">
                and quoterule_template_id = #{quoteruleTemplateId}
            </if>
            <if test="templateCode != null and templateCode != ''">
                and template_code = #{templateCode}
            </if>
            <if test="filedsetingId != null and filedsetingId != ''">
                and filedseting_id = #{filedsetingId}
            </if>
            <if test="fieldChinese != null and fieldChinese != ''">
                and field_chinese = #{fieldChinese}
            </if>
            <if test="fieldEnglish != null and fieldEnglish != ''">
                and field_english = #{fieldEnglish}
            </if>
            <if test="operBy != null and operBy != ''">
                and oper_by = #{operBy}
            </if>
            <if test="operTime != null and operTime != ''">
                and oper_time = #{operTime}
            </if>
            <if test="delFlag != null and delFlag != ''">
                and del_flag = #{delFlag}
            </if>
            <if test="anotherName != null and anotherName != ''">
                and another_name = #{anotherName}
            </if>
            <if test="tempIds != null and tempIds.size() != 0">
                and quoterule_template_id in
                <foreach item="id" collection="tempIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selQuoteruleTemplateList" resultMap="PubQuoteruleTemplateResultMap">
        select
            id,rule_code,rule_name,rule_type,consolidation_rule,group_rule,remark,calculation_formula,calculation_formula_code,matching_conditions,matching_conditions_code,is_enable,create_by,create_time,create_dept_id,oper_by,oper_time,oper_dept_id,del_flag,calculation_process,calculation_process_code,bj_type,order_type,order_type_list
        from pub_quoterule_template
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="ruleCode != null and ruleCode != ''">
                and rule_code = #{ruleCode}
            </if>
            <if test="ruleName != null and ruleName != ''">
                and rule_name = #{ruleName}
            </if>
            <if test="ruleType != null and ruleType != ''">
                and rule_type = #{ruleType}
            </if>
            <if test="consolidationRule != null and consolidationRule != ''">
                and consolidation_rule = #{consolidationRule}
            </if>
            <if test="groupRule != null and groupRule != ''">
                and group_rule = #{groupRule}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="calculationFormula != null and calculationFormula != ''">
                and calculation_formula = #{calculationFormula}
            </if>
            <if test="calculationFormulaCode != null and calculationFormulaCode != ''">
                and calculation_formula_code = #{calculationFormulaCode}
            </if>
            <if test="matchingConditions != null and matchingConditions != ''">
                and matching_conditions = #{matchingConditions}
            </if>
            <if test="matchingConditionsCode != null and matchingConditionsCode != ''">
                and matching_conditions_code = #{matchingConditionsCode}
            </if>
            <if test="isEnable != null and isEnable != ''">
                and is_enable = #{isEnable}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="createDeptId != null and createDeptId != ''">
                and create_dept_id = #{createDeptId}
            </if>
            <if test="operBy != null and operBy != ''">
                and oper_by = #{operBy}
            </if>
            <if test="operTime != null and operTime != ''">
                and oper_time = #{operTime}
            </if>
            <if test="operDeptId != null and operDeptId != ''">
                and oper_dept_id = #{operDeptId}
            </if>
            <if test="delFlag != null and delFlag != ''">
                and del_flag = #{delFlag}
            </if>
            <if test="calculationProcess != null and calculationProcess != ''">
                and calculation_process = #{calculationProcess}
            </if>
            <if test="calculationProcessCode != null and calculationProcessCode != ''">
                and calculation_process_code = #{calculationProcessCode}
            </if>
            <if test="bjType != null and bjType != ''">
                and bj_type = #{bjType}
            </if>
            <if test="orderType != null and orderType != ''">
                and order_type = #{orderType}
            </if>
            <if test="orderTypeList != null and orderTypeList != ''">
                and order_type_list = #{orderTypeList}
            </if>
            <if test="tempIds != null and tempIds.size() != 0">
                and id in
                <foreach item="id" collection="tempIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selMainContractList" resultType="com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleDto">
        SELECT
            t1.id as id,
            t1.rule_code as ruleCode,
            t1.rule_name as ruleName,
            t1.rule_type as ruleType,
            t1.ht_type as htType,
            t1.business_type as businessType,
            t1.relation_id as relationId,
            CASE
                WHEN t1.rule_type = 1 THEN t3.client_name
                WHEN t1.rule_type = 2 THEN t4.carrier_name
            END AS relationName,
            DATE_FORMAT(t1.start_time,'%Y-%m-%d %H:%i:%s') as startTime,
            DATE_FORMAT(t1.end_time,'%Y-%m-%d %H:%i:%s') as endTime,
            DATE_FORMAT(t1.warning_time,'%Y-%m-%d %H:%i:%s') as warningTime,
            t1.is_enable as isEnable,
            CASE
                WHEN GROUP_CONCAT(DISTINCT IFNULL(t2.audit_state,1)) = '2'
                    THEN 2
                WHEN GROUP_CONCAT(DISTINCT IFNULL(t2.audit_state,1)) = '1'
                    THEN 1
                WHEN GROUP_CONCAT(DISTINCT IFNULL(t2.audit_state,1)) = '3'
                    THEN 4
                ELSE 3
            END AS auditState,
            t1.oper_code as operCode,
            t1.oper_by as operBy,
            DATE_FORMAT(t1.oper_time,'%Y-%m-%d %H:%i:%s') as operTime,
            t1.user_company_id as userCompanyId,
            t1.client_idstr as clientIdstr,
            t1.remark,
            CASE
                WHEN t1.rule_type = 1 THEN t3.company_id
                WHEN t1.rule_type = 2 THEN t4.companyid
            END AS companyid,
            CASE
                WHEN now() BETWEEN t1.start_time AND t1.warning_time THEN 1
                WHEN now() BETWEEN t1.warning_time AND t1.end_time THEN 3
                WHEN now() > t1.end_time THEN 2
            END AS htState,
        t1.create_by as createBy,
        t1.create_code as createCode,
        t1.create_time as createTime,
        t1.share_type as shareType,
        t1.weight_value as weightValue
        FROM pub_quoterule t1
        LEFT JOIN pub_quoterule_detail t2 ON t2.quoterule_id = t1.id AND t2.del_flag = 0
        LEFT JOIN bms_clientinfo t3 ON t1.relation_id = t3.id AND t3.del_flag = 0
        LEFT JOIN bms_carrierinfo t4 ON t1.relation_id = t4.id AND t4.del_flag = 0
        WHERE t1.del_flag = 0
        <if test="beginOperTime != null and beginOperTime != ''">
            and t1.oper_time &gt;= #{beginOperTime}
        </if>
        <if test="endOperTime != null and endOperTime != ''">
            and t1.oper_time &lt;= #{endOperTime}
        </if>
        <if test="beginCreateTime != null and beginCreateTime != ''">
            and t1.create_time &gt;= #{beginCreateTime}
        </if>
        <if test="endCreateTime != null and endCreateTime != ''">
            and t1.create_time &lt;= #{endCreateTime}
        </if>
        <if test="ruleCode != null"> and t1.rule_code like concat('%', #{ruleCode}, '%')</if>
        <if test="ruleName != null"> and t1.rule_name like concat('%', #{ruleName}, '%')</if>
        <if test="ruleType != null"> and t1.rule_type = #{ruleType}</if>
        <if test="htType != null"> and t1.ht_type = #{htType}</if>
        <if test="businessType !=null and businessType !=0"> and t1.business_type =#{businessType}</if>
        <if test="relationId !=null"> and t1.relation_id = #{relationId}</if>
        <if test="relationName !=null and relationName !='' and ruleType==1"> and t3.client_name like concat('%',#{relationName},'%')</if>
        <if test="relationName !=null and relationName !='' and ruleType==2"> and t4.carrier_name like concat('%',#{relationName},'%')</if>
        <if test="auditState!=null and auditState!=0 "> and IFNULL(t2.audit_state,0) = #{auditState} </if>
        <if test="createBy != null and createBy !='' "> and t1.create_by = #{createBy} </if>
        <if test="createCode != null and createCode !='' "> and t1.create_code = #{createCode} </if>
        <if test="operBy != null and operBy !='' "> and t1.oper_by = #{operBy} </if>
        <if test="operCode != null and operCode !='' "> and t1.oper_code = #{operCode} </if>
        <if test="isEnable != null and isEnable!=2 "> and t1.is_enable = #{isEnable}</if>
        <if test="companyIds != null and companyIds.size > 0 and ruleType==1 ">
            and t3.company_id in
            <foreach collection="companyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="companyIds != null and companyIds.size > 0 and ruleType==2 ">
            and t4.companyid in
            <foreach collection="companyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="clientIds != null and clientIds.size > 0 ">
            and  t1.relation_id in
            <foreach collection="clientIds" item="clientId" open="(" separator="," close=")">
                #{clientId}
            </foreach>
        </if>
        <if test="carrierIds != null  and carrierIds.size > 0 ">
            and  t1.relation_id in
            <foreach collection="carrierIds" item="carrierId" open="(" separator="," close=")">
                #{carrierId}
            </foreach>
        </if>
        GROUP BY t1.id
        <trim prefix="HAVING" prefixOverrides="AND">
            <if test="auditState !=null and auditState!=0">
                AND auditState = #{auditState}
            </if>
            <if test="htState != null and htState!=0">
                AND htState = #{htState}
            </if>
        </trim>
        ORDER BY t1.oper_time DESC
    </select>



    <select id="selClassificationList" resultMap="PubQuotationClassificationdetailResultMap">
        SELECT
            t1.id,
            t1.quoteruledetail_id,
            t1.store_code,
            t1.store_name,
            t1.deliver_province,
            t1.deliver_area,
            t1.deliver_city,
            t1.receive_province,
            t1.receive_area,
            t1.receive_city,
            t1.carmodel,
            t1.carlong,
            t1.route_code,
            t1.warehouse_name,
            t1.bill_type,
            t1.temperature_zone,
            t1.specifications,
            t1.category,
            t1.is_removezero,
            t1.business_type,
            t1.minimum_charge,
            t1.unit_price,
            t1.step,
            t1.singular_ladder1,
            t1.singular_ladder2,
            t1.singular_ladder3,
            t1.singular_ladder4,
            t1.singular_ladder5,
            t1.singular_ladder6,
            t1.box_ladder1,
            t1.box_ladder2,
            t1.box_ladder3,
            t1.box_ladder4,
            t1.box_ladder5,
            t1.box_ladder6,
            t1.weight_ladder1,
            t1.weight_ladder2,
            t1.weight_ladder3,
            t1.weight_ladder4,
            t1.weight_ladder5,
            t1.weight_ladder6,
            t1.volume_ladder1,
            t1.volume_ladder2,
            t1.volume_ladder3,
            t1.volume_ladder4,
            t1.volume_ladder5,
            t1.volume_ladder6,
            t1.kilometre_ladder1,
            t1.kilometre_ladder2,
            t1.kilometre_ladder3,
            t1.kilometre_ladder4,
            t1.kilometre_ladder5,
            t1.kilometre_ladder6,
            t1.ladder_price1,
            t1.ladder_price2,
            t1.ladder_price3,
            t1.ladder_price4,
            t1.ladder_price5,
            t1.ladder_price6,
            t1.percentage,
            t1.tornum_ladder1,
            t1.tornum_ladder2,
            t1.tornum_ladder3,
            t1.tornum_ladder4,
            t1.tornum_ladder5,
            t1.tornum_ladder6,
            t1.number_ladder1,
            t1.number_ladder2,
            t1.number_ladder3,
            t1.number_ladder4,
            t1.number_ladder5,
            t1.number_ladder6,
            t1.base_quantity,
            t1.category1,
            t1.category2,
            t1.category3,
            t1.category4,
            t1.category5,
            t1.category6,
            t1.item_id,
            '' as itemIdName,
            t1.item_id1,
            '' as itemId1Name,
            t1.cost_unit as costUnit,
            '' as costUnitName
        FROM pub_quotation_classificationdetail t1
        <where>
            <if test="id != null and id != ''">
                and t1.id = #{id}
            </if>
            <if test="quoteruledetailId != null and quoteruledetailId != ''">
                and t1.quoteruledetail_id = #{quoteruledetailId}
            </if>
            <if test="storeCode != null and storeCode != ''">
                and t1.store_code = #{storeCode}
            </if>
            <if test="storeName != null and storeName != ''">
                and t1.store_name = #{storeName}
            </if>
            <if test="deliverProvince != null and deliverProvince != ''">
                and t1.deliver_province = #{deliverProvince}
            </if>
            <if test="deliverArea != null and deliverArea != ''">
                and t1.deliver_area = #{deliverArea}
            </if>
            <if test="deliverCity != null and deliverCity != ''">
                and t1.deliver_city = #{deliverCity}
            </if>
            <if test="receiveProvince != null and receiveProvince != ''">
                and t1.receive_province = #{receiveProvince}
            </if>
            <if test="receiveArea != null and receiveArea != ''">
                and t1.receive_area = #{receiveArea}
            </if>
            <if test="receiveCity != null and receiveCity != ''">
                and t1.receive_city = #{receiveCity}
            </if>
            <if test="carmodel != null and carmodel != ''">
                and t1.carmodel = #{carmodel}
            </if>
            <if test="carlong != null and carlong != ''">
                and t1.carlong = #{carlong}
            </if>
            <if test="routeCode != null and routeCode != ''">
                and t1.route_code = #{routeCode}
            </if>
            <if test="warehouseName != null and warehouseName != ''">
                and t1.warehouse_name = #{warehouseName}
            </if>
            <if test="billType != null and billType != ''">
                and t1.bill_type = #{billType}
            </if>
            <if test="temperatureZone != null and temperatureZone != ''">
                and t1.temperature_zone = #{temperatureZone}
            </if>
            <if test="specifications != null and specifications != ''">
                and t1.specifications = #{specifications}
            </if>
            <if test="category != null and category != ''">
                and t1.category = #{category}
            </if>
            <if test="isRemovezero != null and isRemovezero != ''">
                and t1.is_removezero = #{isRemovezero}
            </if>
            <if test="businessType != null and businessType != ''">
                and t1.business_type = #{businessType}
            </if>
            <if test="minimumCharge != null and minimumCharge != ''">
                and t1.minimum_charge = #{minimumCharge}
            </if>
            <if test="unitPrice != null and unitPrice != ''">
                and t1.unit_price = #{unitPrice}
            </if>
            <if test="step != null and step != ''">
                and t1.step = #{step}
            </if>
            <if test="singularLadder1 != null and singularLadder1 != ''">
                and t1.singular_ladder1 = #{singularLadder1}
            </if>
            <if test="singularLadder2 != null and singularLadder2 != ''">
                and t1.singular_ladder2 = #{singularLadder2}
            </if>
            <if test="singularLadder3 != null and singularLadder3 != ''">
                and t1.singular_ladder3 = #{singularLadder3}
            </if>
            <if test="singularLadder4 != null and singularLadder4 != ''">
                and t1.singular_ladder4 = #{singularLadder4}
            </if>
            <if test="singularLadder5 != null and singularLadder5 != ''">
                and t1.singular_ladder5 = #{singularLadder5}
            </if>
            <if test="singularLadder6 != null and singularLadder6 != ''">
                and t1.singular_ladder6 = #{singularLadder6}
            </if>
            <if test="boxLadder1 != null and boxLadder1 != ''">
                and t1.box_ladder1 = #{boxLadder1}
            </if>
            <if test="boxLadder2 != null and boxLadder2 != ''">
                and t1.box_ladder2 = #{boxLadder2}
            </if>
            <if test="boxLadder3 != null and boxLadder3 != ''">
                and t1.box_ladder3 = #{boxLadder3}
            </if>
            <if test="boxLadder4 != null and boxLadder4 != ''">
                and t1.box_ladder4 = #{boxLadder4}
            </if>
            <if test="boxLadder5 != null and boxLadder5 != ''">
                and t1.box_ladder5 = #{boxLadder5}
            </if>
            <if test="boxLadder6 != null and boxLadder6 != ''">
                and t1.box_ladder6 = #{boxLadder6}
            </if>
            <if test="weightLadder1 != null and weightLadder1 != ''">
                and t1.weight_ladder1 = #{weightLadder1}
            </if>
            <if test="weightLadder2 != null and weightLadder2 != ''">
                and t1.weight_ladder2 = #{weightLadder2}
            </if>
            <if test="weightLadder3 != null and weightLadder3 != ''">
                and t1.weight_ladder3 = #{weightLadder3}
            </if>
            <if test="weightLadder4 != null and weightLadder4 != ''">
                and t1.weight_ladder4 = #{weightLadder4}
            </if>
            <if test="weightLadder5 != null and weightLadder5 != ''">
                and t1.weight_ladder5 = #{weightLadder5}
            </if>
            <if test="weightLadder6 != null and weightLadder6 != ''">
                and t1.weight_ladder6 = #{weightLadder6}
            </if>
            <if test="volumeLadder1 != null and volumeLadder1 != ''">
                and t1.volume_ladder1 = #{volumeLadder1}
            </if>
            <if test="volumeLadder2 != null and volumeLadder2 != ''">
                and t1.volume_ladder2 = #{volumeLadder2}
            </if>
            <if test="volumeLadder3 != null and volumeLadder3 != ''">
                and t1.volume_ladder3 = #{volumeLadder3}
            </if>
            <if test="volumeLadder4 != null and volumeLadder4 != ''">
                and t1.volume_ladder4 = #{volumeLadder4}
            </if>
            <if test="volumeLadder5 != null and volumeLadder5 != ''">
                and t1.volume_ladder5 = #{volumeLadder5}
            </if>
            <if test="volumeLadder6 != null and volumeLadder6 != ''">
                and t1.volume_ladder6 = #{volumeLadder6}
            </if>
            <if test="kilometreLadder1 != null and kilometreLadder1 != ''">
                and t1.kilometre_ladder1 = #{kilometreLadder1}
            </if>
            <if test="kilometreLadder2 != null and kilometreLadder2 != ''">
                and t1.kilometre_ladder2 = #{kilometreLadder2}
            </if>
            <if test="kilometreLadder3 != null and kilometreLadder3 != ''">
                and t1.kilometre_ladder3 = #{kilometreLadder3}
            </if>
            <if test="kilometreLadder4 != null and kilometreLadder4 != ''">
                and t1.kilometre_ladder4 = #{kilometreLadder4}
            </if>
            <if test="kilometreLadder5 != null and kilometreLadder5 != ''">
                and t1.kilometre_ladder5 = #{kilometreLadder5}
            </if>
            <if test="kilometreLadder6 != null and kilometreLadder6 != ''">
                and t1.kilometre_ladder6 = #{kilometreLadder6}
            </if>
            <if test="ladderPrice1 != null and ladderPrice1 != ''">
                and t1.ladder_price1 = #{ladderPrice1}
            </if>
            <if test="ladderPrice2 != null and ladderPrice2 != ''">
                and t1.ladder_price2 = #{ladderPrice2}
            </if>
            <if test="ladderPrice3 != null and ladderPrice3 != ''">
                and t1.ladder_price3 = #{ladderPrice3}
            </if>
            <if test="ladderPrice4 != null and ladderPrice4 != ''">
                and t1.ladder_price4 = #{ladderPrice4}
            </if>
            <if test="ladderPrice5 != null and ladderPrice5 != ''">
                and t1.ladder_price5 = #{ladderPrice5}
            </if>
            <if test="ladderPrice6 != null and ladderPrice6 != ''">
                and t1.ladder_price6 = #{ladderPrice6}
            </if>
            <if test="percentage != null and percentage != ''">
                and t1.percentage = #{percentage}
            </if>
            <if test="tornumLadder1 != null and tornumLadder1 != ''">
                and t1.tornum_ladder1 = #{tornumLadder1}
            </if>
            <if test="tornumLadder2 != null and tornumLadder2 != ''">
                and t1.tornum_ladder2 = #{tornumLadder2}
            </if>
            <if test="tornumLadder3 != null and tornumLadder3 != ''">
                and t1.tornum_ladder3 = #{tornumLadder3}
            </if>
            <if test="tornumLadder4 != null and tornumLadder4 != ''">
                and t1.tornum_ladder4 = #{tornumLadder4}
            </if>
            <if test="tornumLadder5 != null and tornumLadder5 != ''">
                and t1.tornum_ladder5 = #{tornumLadder5}
            </if>
            <if test="tornumLadder6 != null and tornumLadder6 != ''">
                and t1.tornum_ladder6 = #{tornumLadder6}
            </if>
            <if test="numberLadder1 != null and numberLadder1 != ''">
                and t1.number_ladder1 = #{numberLadder1}
            </if>
            <if test="numberLadder2 != null and numberLadder2 != ''">
                and t1.number_ladder2 = #{numberLadder2}
            </if>
            <if test="numberLadder3 != null and numberLadder3 != ''">
                and t1.number_ladder3 = #{numberLadder3}
            </if>
            <if test="numberLadder4 != null and numberLadder4 != ''">
                and t1.number_ladder4 = #{numberLadder4}
            </if>
            <if test="numberLadder5 != null and numberLadder5 != ''">
                and t1.number_ladder5 = #{numberLadder5}
            </if>
            <if test="numberLadder6 != null and numberLadder6 != ''">
                and t1.number_ladder6 = #{numberLadder6}
            </if>
            <if test="baseQuantity != null and baseQuantity != ''">
                and t1.base_quantity = #{baseQuantity}
            </if>
            <if test="category1 != null and category1 != ''">
                and t1.category1 = #{category1}
            </if>
            <if test="category2 != null and category2 != ''">
                and t1.category2 = #{category2}
            </if>
            <if test="category3 != null and category3 != ''">
                and t1.category3 = #{category3}
            </if>
            <if test="category4 != null and category4 != ''">
                and t1.category4 = #{category4}
            </if>
            <if test="category5 != null and category5 != ''">
                and t1.category5 = #{category5}
            </if>
            <if test="category6 != null and category6 != ''">
                and t1.category6 = #{category6}
            </if>
            <if test="itemId != null and itemId != ''">
                and t1.item_id = #{itemId}
            </if>
            <if test="itemId1 != null and itemId1 != ''">
                and t1.item_id1 = #{itemId1}
            </if>
            <if test="quoterDetailIds != null and quoterDetailIds.size() != 0">
                and t1.quoteruledetail_id in
                <foreach collection="quoterDetailIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selSubContractListByBase" resultMap="SubContractResultMap">
        select
        id,pk_id,quoterule_id,rulecode,fee_type,fee_type_str,item_id,is_calculated,quoterule_template_id,is_enable,remark,oper_by,oper_code,oper_time,oper_dept_id,del_flag,audit_state,audit_user,audit_user_name,audit_time,rule_type,bill_type,audit_remark,start_time,end_time,warning_time,client_id,create_by,create_code,create_time
        ,CASE
        WHEN now() BETWEEN start_time AND end_time THEN 0
        WHEN now() > end_time THEN 1
        ELSE  0
        END AS orderSort
        from pub_quoterule_detail
        <where>
            del_flag = 0
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="pkId != null and pkId != ''">
                and pk_id = #{pkId}
            </if>
            <if test="quoteruleId != null and quoteruleId != ''">
                and quoterule_id = #{quoteruleId}
            </if>
            <if test="rulecode != null and rulecode != ''">
                and rulecode = #{rulecode}
            </if>
            <if test="feeType != null and feeType != ''">
                and fee_type = #{feeType}
            </if>
            <if test="feeTypeStr != null and feeTypeStr != ''">
                and fee_type_str = #{feeTypeStr}
            </if>
            <if test="itemId != null and itemId != ''">
                and item_id = #{itemId}
            </if>
            <if test="isCalculated != null and isCalculated != ''">
                and is_calculated = #{isCalculated}
            </if>
            <if test="quoteruleTemplateId != null and quoteruleTemplateId != ''">
                and quoterule_template_id = #{quoteruleTemplateId}
            </if>
            <if test="isEnable != null and isEnable != ''">
                and is_enable = #{isEnable}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="operBy != null and operBy != ''">
                and oper_by = #{operBy}
            </if>
            <if test="operCode != null and operCode != ''">
                and oper_code = #{operCode}
            </if>
            <if test="operTime != null and operTime != ''">
                and oper_time = #{operTime}
            </if>
            <if test="operDeptId != null and operDeptId != ''">
                and oper_dept_id = #{operDeptId}
            </if>
            <if test="auditState != null and auditState != ''">
                and audit_state = #{auditState}
            </if>
            <if test="auditUser != null and auditUser != ''">
                and audit_user = #{auditUser}
            </if>
            <if test="auditUserName != null and auditUserName != ''">
                and audit_user_name = #{auditUserName}
            </if>
            <if test="auditTime != null and auditTime != ''">
                and audit_time = #{auditTime}
            </if>
            <if test="ruleType != null and ruleType != ''">
                and rule_type = #{ruleType}
            </if>
            <if test="billType != null and billType != ''">
                and bill_type = #{billType}
            </if>
            <if test="auditRemark != null and auditRemark != ''">
                and audit_remark = #{auditRemark}
            </if>
            <if test="startTime != null and startTime != ''">
                and start_time = #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and end_time = #{endTime}
            </if>
            <if test="warningTime != null and warningTime != ''">
                and warning_time = #{warningTime}
            </if>
            <if test="clientId != null and clientId != ''">
                and client_id = #{clientId}
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="createCode != null and createCode != ''">
                create_code = #{createCode},
            </if>
            <if test="createTime != null and createTime != ''">
                create_time = #{createTime},
            </if>
        </where>
    </select>

    <delete id="deleteBatchSubContract">
        update pub_quoterule_detail
            set del_flag = 1
        where id in
        <foreach item="entitie" collection="entities" open="(" separator="," close=")">
            #{entitie}
        </foreach>
    </delete>

    <update id="updateSubIsEnable">
        update pub_quoterule_detail
        <set>
            <if test="isEnable != null">
                is_enable = #{isEnable},
            </if>
        </set>
        where del_flag = 0 and quoterule_id = #{quoteruleId}
    </update>


    <resultMap id="YsCostInfoBaseResultMap" type="com.bbyb.joy.bms.domain.dto.BmsYscostInfo">
        <!--@mbg.generated-->
        <!--@Table bms_yscost_info-->
        <id column="pk_id" jdbcType="BIGINT" property="pkId" />
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="expenses_code" jdbcType="VARCHAR" property="expensesCode" />
        <result column="business_type" jdbcType="SMALLINT" property="businessType" />
        <result column="client_id" jdbcType="INTEGER" property="clientId" />
        <result column="institution_id" jdbcType="INTEGER" property="institutionId" />
        <result column="expenses_type" jdbcType="SMALLINT" property="expensesType" />
        <result column="cost_dimension" jdbcType="SMALLINT" property="costDimension" />
        <result column="charge_type" jdbcType="SMALLINT" property="chargeType" />
        <result column="fee_flag" jdbcType="SMALLINT" property="feeFlag" />
        <result column="quoterule_id" jdbcType="VARCHAR" property="quoteruleId" />
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="freight" jdbcType="DECIMAL" property="freight" />
        <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
        <result column="ultrafar_fee" jdbcType="DECIMAL" property="ultrafarFee" />
        <result column="superframes_fee" jdbcType="DECIMAL" property="superframesFee" />
        <result column="excess_fee" jdbcType="DECIMAL" property="excessFee" />
        <result column="reduce_fee" jdbcType="DECIMAL" property="reduceFee" />
        <result column="outboundsorting_fee" jdbcType="DECIMAL" property="outboundsortingFee" />
        <result column="shortbarge_fee" jdbcType="DECIMAL" property="shortbargeFee" />
        <result column="return_fee" jdbcType="DECIMAL" property="returnFee" />
        <result column="exception_fee" jdbcType="DECIMAL" property="exceptionFee" />
        <result column="adjust_fee" jdbcType="DECIMAL" property="adjustFee" />
        <result column="adjust_remark" jdbcType="VARCHAR" property="adjustRemark" />
        <result column="other_cost1" jdbcType="DECIMAL" property="otherCost1" />
        <result column="other_cost2" jdbcType="DECIMAL" property="otherCost2" />
        <result column="other_cost3" jdbcType="DECIMAL" property="otherCost3" />
        <result column="other_cost4" jdbcType="DECIMAL" property="otherCost4" />
        <result column="other_cost5" jdbcType="DECIMAL" property="otherCost5" />
        <result column="other_cost6" jdbcType="DECIMAL" property="otherCost6" />
        <result column="other_cost7" jdbcType="DECIMAL" property="otherCost7" />
        <result column="other_cost8" jdbcType="DECIMAL" property="otherCost8" />
        <result column="other_cost9" jdbcType="DECIMAL" property="otherCost9" />
        <result column="other_cost10" jdbcType="DECIMAL" property="otherCost10" />
        <result column="other_cost11" jdbcType="DECIMAL" property="otherCost11" />
        <result column="other_cost12" jdbcType="DECIMAL" property="otherCost12" />
        <result column="cost_attribute" jdbcType="SMALLINT" property="costAttribute" />
        <result column="oper_code" jdbcType="VARCHAR" property="operCode" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="bill_id" jdbcType="BIGINT" property="billId" />
        <result column="bill_date" jdbcType="VARCHAR" property="billDate" />
        <result column="business_time" jdbcType="TIMESTAMP" property="businessTime" />
        <result column="over_num" jdbcType="DECIMAL" property="overNum" />
        <result column="over_sendnum" jdbcType="INTEGER" property="overSendnum" />
        <result column="storage_fee_price" jdbcType="DECIMAL" property="storageFeePrice" />
        <result column="disposal_fee_price" jdbcType="DECIMAL" property="disposalFeePrice" />
        <result column="other_fee_remark" jdbcType="VARCHAR" property="otherFeeRemark" />
        <result column="fee_type_first" jdbcType="VARCHAR" property="feeTypeFirst" />
        <result column="fee_create_ate" jdbcType="VARCHAR" property="feeCreateAte" />
        <result column="is_increment" jdbcType="TINYINT" property="isIncrement" />
        <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
        <result column="signing_date" jdbcType="TIMESTAMP" property="signingDate" />
    </resultMap>


    <sql id="YsCostInfoBaseColumnList">
        <!--@mbg.generated-->
        pk_id, id, expenses_code, business_type, client_id, institution_id, expenses_type,
        cost_dimension, charge_type, fee_flag, quoterule_id, rule_name, remarks, freight,
        delivery_fee, ultrafar_fee, superframes_fee, excess_fee, reduce_fee, outboundsorting_fee,
        shortbarge_fee, return_fee, exception_fee, adjust_fee, adjust_remark, other_cost1,
        other_cost2, other_cost3, other_cost4, other_cost5, other_cost6, other_cost7, other_cost8,
        other_cost9, other_cost10, other_cost11, other_cost12, cost_attribute, oper_code,
        oper_by, oper_time, del_flag, bill_id, bill_date, business_time, over_num, over_sendnum,
        storage_fee_price, disposal_fee_price, other_fee_remark, fee_type_first, fee_create_ate,
        is_increment, order_date, signing_date
    </sql>

    <select id="selUsedYsCostInfoByClient" resultType="java.lang.String">
        select distinct
        quoter_rule_detail_id
        from bms_yscost_info
        where del_flag = 0 and
        <foreach collection="entities" item="entitie" separator=" OR " open="("  close=")">
            quoter_rule_detail_id LIKE CONCAT('%', #{entitie}, '%')
        </foreach>
    </select>

    <select id="selUsedYsCostInfoByClientGenerateBill" resultType="java.lang.String">
        select distinct
        quoter_rule_detail_id
        from bms_yscost_info
        where del_flag = 0
        AND bill_id is not null AND bill_id != ''
        AND
        <foreach collection="entities" item="entitie" separator=" OR " open="("  close=")">
            quoter_rule_detail_id LIKE CONCAT('%', #{entitie}, '%')
        </foreach>
    </select>
</mapper>