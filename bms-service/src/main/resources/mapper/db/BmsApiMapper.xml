<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsApiMapper">
    <resultMap id="BaseYsbillCodeInfoResultMap" type="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeinfo">
        <!--@mbg.generated-->
        <!--@Table bms_ysbillcodeinfo-->
        <id column="pk_id" jdbcType="BIGINT" property="pkId" />
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="code_type" jdbcType="SMALLINT" property="codeType" />
        <result column="relate_code" jdbcType="VARCHAR" property="relateCode" />
        <result column="scheduling_bill_code" jdbcType="VARCHAR" property="schedulingBillCode" />
        <result column="client_code" jdbcType="VARCHAR" property="clientCode" />
        <result column="client_name" jdbcType="VARCHAR" property="clientName" />
        <result column="company_id" jdbcType="INTEGER" property="companyId" />
        <result column="network_code" jdbcType="VARCHAR" property="networkCode" />
        <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
        <result column="total_boxes" jdbcType="DECIMAL" property="totalBoxes" />
        <result column="total_number" jdbcType="DECIMAL" property="totalNumber" />
        <result column="split_total_number" jdbcType="DECIMAL" property="splitTotalNumber" />
        <result column="sku_number" jdbcType="DECIMAL" property="skuNumber" />
        <result column="total_weight" jdbcType="DECIMAL" property="totalWeight" />
        <result column="total_volume" jdbcType="DECIMAL" property="totalVolume" />
        <result column="cargo_value" jdbcType="DECIMAL" property="cargoValue" />
        <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
        <result column="signing_date" jdbcType="TIMESTAMP" property="signingDate" />
        <result column="if_autarky" jdbcType="CHAR" property="ifAutarky" />
        <result column="storage_service_provider" jdbcType="VARCHAR" property="storageServiceProvider" />
        <result column="line_code" jdbcType="VARCHAR" property="lineCode" />
        <result column="line_name" jdbcType="VARCHAR" property="lineName" />
        <result column="if_base_stores" jdbcType="CHAR" property="ifBaseStores" />
        <result column="if_Super_base_kilometer" jdbcType="CHAR" property="ifSuperBaseKilometer" />
        <result column="store_distance_kilometer" jdbcType="DECIMAL" property="storeDistanceKilometer" />
        <result column="delivery_code" jdbcType="VARCHAR" property="deliveryCode" />
        <result column="delivery_name" jdbcType="VARCHAR" property="deliveryName" />
        <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
        <result column="receiving_store" jdbcType="VARCHAR" property="receivingStore" />
        <result column="province_origin" jdbcType="VARCHAR" property="provinceOrigin" />
        <result column="originating_city" jdbcType="VARCHAR" property="originatingCity" />
        <result column="originating_area" jdbcType="VARCHAR" property="originatingArea" />
        <result column="originating_address" jdbcType="VARCHAR" property="originatingAddress" />
        <result column="destination_Province" jdbcType="VARCHAR" property="destinationProvince" />
        <result column="destination_city" jdbcType="VARCHAR" property="destinationCity" />
        <result column="destination_area" jdbcType="VARCHAR" property="destinationArea" />
        <result column="destination_address" jdbcType="VARCHAR" property="destinationAddress" />
        <result column="cost_status" jdbcType="CHAR" property="costStatus" />
        <result column="billing_status" jdbcType="CHAR" property="billingStatus" />
        <result column="create_code" jdbcType="VARCHAR" property="createCode" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_dept_id" jdbcType="INTEGER" property="createDeptId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="oper_code" jdbcType="VARCHAR" property="operCode" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_dept_id" jdbcType="INTEGER" property="operDeptId" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="delivery_mode" jdbcType="SMALLINT" property="deliveryMode" />
        <result column="transport_type" jdbcType="VARCHAR" property="transportType" />
        <result column="is_rejected" jdbcType="CHAR" property="isRejected" />
        <result column="reject_parties" jdbcType="SMALLINT" property="rejectParties" />
        <result column="order_type" jdbcType="VARCHAR" property="orderType" />
        <result column="near_store_km" jdbcType="DECIMAL" property="nearStoreKm" />
        <result column="car_type" jdbcType="SMALLINT" property="carType" />
        <result column="car_model" jdbcType="VARCHAR" property="carModel" />
        <result column="cw_full_cases" jdbcType="DECIMAL" property="cwFullCases" />
        <result column="cw_split_cases" jdbcType="DECIMAL" property="cwSplitCases" />
        <result column="ld_full_cases" jdbcType="DECIMAL" property="ldFullCases" />
        <result column="ld_split_cases" jdbcType="DECIMAL" property="ldSplitCases" />
        <result column="lc_full_cases" jdbcType="DECIMAL" property="lcFullCases" />
        <result column="lc_split_cases" jdbcType="DECIMAL" property="lcSplitCases" />
        <result column="pallet_number" jdbcType="DECIMAL" property="palletNumber" />
        <result column="skuString" jdbcType="LONGVARCHAR" property="skuString" />
        <result column="temperatureTypeStr" jdbcType="LONGVARCHAR" property="temperatureTypeStr" />
        <result column="cw_pallet_number" jdbcType="DECIMAL" property="cwPalletNumber" />
        <result column="lc_pallet_number" jdbcType="DECIMAL" property="lcPalletNumber" />
        <result column="ld_pallet_number" jdbcType="DECIMAL" property="ldPalletNumber" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
        <result column="import_time" jdbcType="TIMESTAMP" property="importTime" />
        <result column="import_code" jdbcType="VARCHAR" property="importCode" />
        <result column="import_by" jdbcType="VARCHAR" property="importBy" />
        <result column="order_source" jdbcType="CHAR" property="orderSource" />
        <result column="returnr_over_num" jdbcType="DECIMAL" property="returnrOverNum" />
        <result column="fail_remark" jdbcType="VARCHAR" property="failRemark" />
        <result column="is_timeout" jdbcType="INTEGER" property="isTimeout" />
    </resultMap>
    <sql id="Base_YsbillcodeInfo_Column_List">
        <!--@mbg.generated-->
        pk_id, id, code_type, relate_code, scheduling_bill_code, client_code, client_name,
        company_id, network_code, warehouse_code, total_boxes, total_number, split_total_number,
        sku_number, total_weight, total_volume, cargo_value, order_date, signing_date, if_autarky,
        storage_service_provider, line_code, line_name, if_base_stores, if_Super_base_kilometer,
        store_distance_kilometer, delivery_code, delivery_name, store_code, receiving_store,
        province_origin, originating_city, originating_area, originating_address, destination_Province,
        destination_city, destination_area, destination_address, cost_status, billing_status,
        create_code, create_by, create_dept_id, create_time, oper_code, oper_by, oper_dept_id,
        oper_time, del_flag, delivery_mode, transport_type, is_rejected, reject_parties,
        order_type, near_store_km, car_type, car_model, cw_full_cases, cw_split_cases, ld_full_cases,
        ld_split_cases, lc_full_cases, lc_split_cases, pallet_number, skuString, temperatureTypeStr,
        cw_pallet_number, lc_pallet_number, ld_pallet_number, order_no, platform_code, import_time,
        import_code, import_by, order_source, returnr_over_num, fail_remark, is_timeout
    </sql>
    <select id="selectYsbillCodeInfoByCond"  resultMap="BaseYsbillCodeInfoResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_YsbillcodeInfo_Column_List" />
        from bms_ysbillcodeinfo
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="pkId != null">
                and pk_id = #{pkId}
            </if>
            <if test="codeType != null">
                and code_type = #{codeType}
            </if>
            <if test="relateCode != null">
                and relate_code = #{relateCode}
            </if>
            <if test="schedulingBillCode != null">
                and scheduling_bill_code = #{schedulingBillCode}
            </if>
            <if test="clientCode != null">
                and client_code = #{clientCode}
            </if>
            <if test="clientName != null">
                and client_name = #{clientName}
            </if>
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="networkCode != null">
                and network_code = #{networkCode}
            </if>
            <if test="warehouseCode != null">
                and warehouse_code = #{warehouseCode}
            </if>
            <if test="totalBoxes != null">
                and total_boxes = #{totalBoxes}
            </if>
            <if test="totalNumber != null">
                and total_number = #{totalNumber}
            </if>
            <if test="splitTotalNumber != null">
                and split_total_number = #{splitTotalNumber}
            </if>
            <if test="skuNumber != null">
                and sku_number = #{skuNumber}
            </if>
            <if test="totalWeight != null">
                and total_weight = #{totalWeight}
            </if>
            <if test="totalVolume != null">
                and total_volume = #{totalVolume}
            </if>
            <if test="cargoValue != null">
                and cargo_value = #{cargoValue}
            </if>
            <if test="orderDate != null">
                and order_date = #{orderDate}
            </if>
            <if test="signingDate != null">
                and signing_date = #{signingDate}
            </if>
            <if test="ifAutarky != null">
                and if_autarky = #{ifAutarky}
            </if>
            <if test="storageServiceProvider != null">
                and storage_service_provider = #{storageServiceProvider}
            </if>
            <if test="lineCode != null">
                and line_code = #{lineCode}
            </if>
            <if test="lineName != null">
                and line_name = #{lineName}
            </if>
            <if test="ifBaseStores != null">
                and if_base_stores = #{ifBaseStores}
            </if>
            <if test="ifSuperBaseKilometer != null">
                and if_Super_base_kilometer = #{ifSuperBaseKilometer}
            </if>
            <if test="storeDistanceKilometer != null">
                and store_distance_kilometer = #{storeDistanceKilometer}
            </if>
            <if test="deliveryCode != null">
                and delivery_code = #{deliveryCode}
            </if>
            <if test="deliveryName != null">
                and delivery_name = #{deliveryName}
            </if>
            <if test="storeCode != null">
                and store_code = #{storeCode}
            </if>
            <if test="receivingStore != null">
                and receiving_store = #{receivingStore}
            </if>
            <if test="provinceOrigin != null">
                and province_origin = #{provinceOrigin}
            </if>
            <if test="originatingCity != null">
                and originating_city = #{originatingCity}
            </if>
            <if test="originatingArea != null">
                and originating_area = #{originatingArea}
            </if>
            <if test="originatingAddress != null">
                and originating_address = #{originatingAddress}
            </if>
            <if test="destinationProvince != null">
                and destination_Province = #{destinationProvince}
            </if>
            <if test="destinationCity != null">
                and destination_city = #{destinationCity}
            </if>
            <if test="destinationArea != null">
                and destination_area = #{destinationArea}
            </if>
            <if test="destinationAddress != null">
                and destination_address = #{destinationAddress}
            </if>
            <if test="costStatus != null">
                and cost_status = #{costStatus}
            </if>
            <if test="billingStatus != null">
                and billing_status = #{billingStatus}
            </if>
            <if test="createCode != null">
                and create_code = #{createCode}
            </if>
            <if test="createBy != null">
                and create_by = #{createBy}
            </if>
            <if test="createDeptId != null">
                and create_dept_id = #{createDeptId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="operCode != null">
                and oper_code = #{operCode}
            </if>
            <if test="operBy != null">
                and oper_by = #{operBy}
            </if>
            <if test="operDeptId != null">
                and oper_dept_id = #{operDeptId}
            </if>
            <if test="operTime != null">
                and oper_time = #{operTime}
            </if>
            <if test="delFlag != null">
                and del_flag = #{delFlag}
            </if>
            <if test="deliveryMode != null">
                and delivery_mode = #{deliveryMode}
            </if>
            <if test="transportType != null">
                and transport_type = #{transportType}
            </if>
            <if test="isRejected != null">
                and is_rejected = #{isRejected}
            </if>
            <if test="rejectParties != null">
                and reject_parties = #{rejectParties}
            </if>
            <if test="orderType != null">
                and order_type = #{orderType}
            </if>
            <if test="nearStoreKm != null">
                and near_store_km = #{nearStoreKm}
            </if>
            <if test="carType != null">
                and car_type = #{carType}
            </if>
            <if test="carModel != null">
                and car_model = #{carModel}
            </if>
            <if test="cwFullCases != null">
                and cw_full_cases = #{cwFullCases}
            </if>
            <if test="cwSplitCases != null">
                and cw_split_cases = #{cwSplitCases}
            </if>
            <if test="ldFullCases != null">
                and ld_full_cases = #{ldFullCases}
            </if>
            <if test="ldSplitCases != null">
                and ld_split_cases = #{ldSplitCases}
            </if>
            <if test="lcFullCases != null">
                and lc_full_cases = #{lcFullCases}
            </if>
            <if test="lcSplitCases != null">
                and lc_split_cases = #{lcSplitCases}
            </if>
            <if test="palletNumber != null">
                and pallet_number = #{palletNumber}
            </if>
            <if test="skuString != null">
                and skuString = #{skuString}
            </if>
        </where>
    </select>

    <update id="deleteYsbillCodeByPrimaryKey" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeinfo">
        <!--@mbg.generated-->
        update bms_ysbillcodeinfo
            set del_flag=1
                ,oper_code='接口对接'
                ,oper_by='接口对接'
                ,oper_time=NOW()
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <insert id="insertYsbillCodes">
        <!--@mbg.generated-->
        insert into bms_ysbillcodeinfo (id, code_type, relate_code,
        scheduling_bill_code, client_code, client_name,
        company_id, network_code, warehouse_code,
        total_boxes, total_number, split_total_number,
        sku_number, total_weight, total_volume,
        cargo_value, order_date, signing_date,
        if_autarky, storage_service_provider, line_code,
        line_name, if_base_stores, if_Super_base_kilometer,
        store_distance_kilometer, delivery_code, delivery_name,
        store_code, receiving_store, province_origin,
        originating_city, originating_area, originating_address,
        destination_Province, destination_city, destination_area,
        destination_address, cost_status, billing_status,
        create_code, create_by, create_dept_id,
        create_time, oper_code, oper_by,
        oper_dept_id, oper_time, del_flag,
        delivery_mode, transport_type, is_rejected,
        reject_parties, order_type, near_store_km,
        car_type, car_model, cw_full_cases,
        cw_split_cases, ld_full_cases, ld_split_cases,
        lc_full_cases, lc_split_cases, pallet_number,
        skuString, temperatureTypeStr,
        cw_pallet_number, lc_pallet_number, ld_pallet_number,
        order_no, platform_code, import_time,
        import_code, import_by, order_source,
        returnr_over_num, fail_remark, is_timeout
        )
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.codeType}, #{entity.relateCode},
            #{entity.schedulingBillCode}, #{entity.clientCode}, #{entity.clientName},
            #{entity.companyId}, #{entity.networkCode}, #{entity.warehouseCode},
            #{entity.totalBoxes}, #{entity.totalNumber}, #{entity.splitTotalNumber},
            #{entity.skuNumber}, #{entity.totalWeight}, #{entity.totalVolume},
            #{entity.cargoValue}, #{entity.orderDate}, #{entity.signingDate},
            #{entity.ifAutarky }, #{entity.storageServiceProvider}, #{entity.lineCode },
            #{entity.lineName }, #{entity.ifBaseStores }, #{entity.ifSuperBaseKilometer },
            #{entity.storeDistanceKilometer }, #{entity.deliveryCode }, #{entity.deliveryName },
            #{entity.storeCode }, #{entity.receivingStore }, #{entity.provinceOrigin },
            #{entity.originatingCity }, #{entity.originatingArea }, #{entity.originatingAddress },
            #{entity.destinationProvince }, #{entity.destinationCity }, #{entity.destinationArea },
            #{entity.destinationAddress }, #{entity.costStatus }, #{entity.billingStatus },
            #{entity.createCode }, #{entity.createBy }, #{entity.createDeptId },
            #{entity.createTime }, #{entity.operCode }, #{entity.operBy },
            #{entity.operDeptId }, #{entity.operTime }, #{entity.delFlag },
            #{entity.deliveryMode }, #{entity.transportType }, #{entity.isRejected },
            #{entity.rejectParties,jdbcType=SMALLINT}, #{entity.orderType,jdbcType=VARCHAR}, #{entity.nearStoreKm },
            #{entity.carType }, #{entity.carModel }, #{entity.cwFullCases },
            #{entity.cwSplitCases }, #{entity.ldFullCases }, #{entity.ldSplitCases },
            #{entity.lcFullCases }, #{entity.lcSplitCases }, #{entity.palletNumber },
            #{entity.skuString }, #{entity.temperatureTypeStr },
            #{entity.cwPalletNumber }, #{entity.lcPalletNumber }, #{entity.ldPalletNumber },
            #{entity.orderNo }, #{entity.platformCode }, #{entity.importTime },
            #{entity.importCode }, #{entity.importBy }, #{entity.orderSource },
            #{entity.returnrOverNum }, #{entity.failRemark }, #{entity.isTimeout}
            )
        </foreach>
    </insert>

    <update id="updateYsbillCodeByCond" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeinfo">
        <!--@mbg.generated-->
        update bms_ysbillcodeinfo
        <set>
            <if test="codeType != null">
                code_type = #{codeType,jdbcType=SMALLINT},
            </if>
            <if test="relateCode != null">
                relate_code = #{relateCode,jdbcType=VARCHAR},
            </if>
            <if test="schedulingBillCode != null">
                scheduling_bill_code = #{schedulingBillCode,jdbcType=VARCHAR},
            </if>
            <if test="clientCode != null">
                client_code = #{clientCode,jdbcType=VARCHAR},
            </if>
            <if test="clientName != null">
                client_name = #{clientName,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="networkCode != null">
                network_code = #{networkCode,jdbcType=VARCHAR},
            </if>
            <if test="warehouseCode != null">
                warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="totalBoxes != null">
                total_boxes = #{totalBoxes,jdbcType=DECIMAL},
            </if>
            <if test="totalNumber != null">
                total_number = #{totalNumber,jdbcType=DECIMAL},
            </if>
            <if test="splitTotalNumber != null">
                split_total_number = #{splitTotalNumber,jdbcType=DECIMAL},
            </if>
            <if test="skuNumber != null">
                sku_number = #{skuNumber,jdbcType=DECIMAL},
            </if>
            <if test="totalWeight != null">
                total_weight = #{totalWeight,jdbcType=DECIMAL},
            </if>
            <if test="totalVolume != null">
                total_volume = #{totalVolume,jdbcType=DECIMAL},
            </if>
            <if test="cargoValue != null">
                cargo_value = #{cargoValue,jdbcType=DECIMAL},
            </if>
            <if test="orderDate != null">
                order_date = #{orderDate,jdbcType=TIMESTAMP},
            </if>
            <if test="signingDate != null">
                signing_date = #{signingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="ifAutarky != null">
                if_autarky = #{ifAutarky,jdbcType=CHAR},
            </if>
            <if test="storageServiceProvider != null">
                storage_service_provider = #{storageServiceProvider,jdbcType=VARCHAR},
            </if>
            <if test="lineCode != null">
                line_code = #{lineCode,jdbcType=VARCHAR},
            </if>
            <if test="lineName != null">
                line_name = #{lineName,jdbcType=VARCHAR},
            </if>
            <if test="ifBaseStores != null">
                if_base_stores = #{ifBaseStores,jdbcType=CHAR},
            </if>
            <if test="ifSuperBaseKilometer != null">
                if_Super_base_kilometer = #{ifSuperBaseKilometer,jdbcType=CHAR},
            </if>
            <if test="storeDistanceKilometer != null">
                store_distance_kilometer = #{storeDistanceKilometer,jdbcType=DECIMAL},
            </if>
            <if test="deliveryCode != null">
                delivery_code = #{deliveryCode,jdbcType=VARCHAR},
            </if>
            <if test="deliveryName != null">
                delivery_name = #{deliveryName,jdbcType=VARCHAR},
            </if>
            <if test="storeCode != null">
                store_code = #{storeCode,jdbcType=VARCHAR},
            </if>
            <if test="receivingStore != null">
                receiving_store = #{receivingStore,jdbcType=VARCHAR},
            </if>
            <if test="provinceOrigin != null">
                province_origin = #{provinceOrigin,jdbcType=VARCHAR},
            </if>
            <if test="originatingCity != null">
                originating_city = #{originatingCity,jdbcType=VARCHAR},
            </if>
            <if test="originatingArea != null">
                originating_area = #{originatingArea,jdbcType=VARCHAR},
            </if>
            <if test="originatingAddress != null">
                originating_address = #{originatingAddress,jdbcType=VARCHAR},
            </if>
            <if test="destinationProvince != null">
                destination_Province = #{destinationProvince,jdbcType=VARCHAR},
            </if>
            <if test="destinationCity != null">
                destination_city = #{destinationCity,jdbcType=VARCHAR},
            </if>
            <if test="destinationArea != null">
                destination_area = #{destinationArea,jdbcType=VARCHAR},
            </if>
            <if test="destinationAddress != null">
                destination_address = #{destinationAddress,jdbcType=VARCHAR},
            </if>
            <if test="costStatus != null">
                cost_status = #{costStatus,jdbcType=CHAR},
            </if>
            <if test="billingStatus != null">
                billing_status = #{billingStatus,jdbcType=CHAR},
            </if>
            <if test="createCode != null">
                create_code = #{createCode,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createDeptId != null">
                create_dept_id = #{createDeptId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operCode != null">
                oper_code = #{operCode,jdbcType=VARCHAR},
            </if>
            <if test="operBy != null">
                oper_by = #{operBy,jdbcType=VARCHAR},
            </if>
            <if test="operDeptId != null">
                oper_dept_id = #{operDeptId,jdbcType=INTEGER},
            </if>
            <if test="operTime != null">
                oper_time = #{operTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="deliveryMode != null">
                delivery_mode = #{deliveryMode,jdbcType=SMALLINT},
            </if>
            <if test="transportType != null">
                transport_type = #{transportType,jdbcType=VARCHAR},
            </if>
            <if test="isRejected != null">
                is_rejected = #{isRejected,jdbcType=CHAR},
            </if>
            <if test="rejectParties != null">
                reject_parties = #{rejectParties,jdbcType=SMALLINT},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="nearStoreKm != null">
                near_store_km = #{nearStoreKm,jdbcType=DECIMAL},
            </if>
            <if test="carType != null">
                car_type = #{carType,jdbcType=SMALLINT},
            </if>
            <if test="carModel != null">
                car_model = #{carModel,jdbcType=VARCHAR},
            </if>
            <if test="cwFullCases != null">
                cw_full_cases = #{cwFullCases,jdbcType=DECIMAL},
            </if>
            <if test="cwSplitCases != null">
                cw_split_cases = #{cwSplitCases,jdbcType=DECIMAL},
            </if>
            <if test="ldFullCases != null">
                ld_full_cases = #{ldFullCases,jdbcType=DECIMAL},
            </if>
            <if test="ldSplitCases != null">
                ld_split_cases = #{ldSplitCases,jdbcType=DECIMAL},
            </if>
            <if test="lcFullCases != null">
                lc_full_cases = #{lcFullCases,jdbcType=DECIMAL},
            </if>
            <if test="lcSplitCases != null">
                lc_split_cases = #{lcSplitCases,jdbcType=DECIMAL},
            </if>
            <if test="palletNumber != null">
                pallet_number = #{palletNumber,jdbcType=DECIMAL},
            </if>
            <if test="skustring != null">
                skuString = #{skustring,jdbcType=LONGVARCHAR},
            </if>
            <if test="temperatureTypeStr != null">
                temperatureTypeStr = #{temperatureTypeStr,jdbcType=LONGVARCHAR},
            </if>
            <if test="cwPalletNumber != null">
                cw_pallet_number = #{cwPalletNumber,jdbcType=DECIMAL},
            </if>
            <if test="lcPalletNumber != null">
                lc_pallet_number = #{lcPalletNumber,jdbcType=DECIMAL},
            </if>
            <if test="ldPalletNumber != null">
                ld_pallet_number = #{ldPalletNumber,jdbcType=DECIMAL},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="platformCode != null">
                platform_code = #{platformCode,jdbcType=VARCHAR},
            </if>
            <if test="importTime != null">
                import_time = #{importTime,jdbcType=TIMESTAMP},
            </if>
            <if test="importCode != null">
                import_code = #{importCode,jdbcType=VARCHAR},
            </if>
            <if test="importBy != null">
                import_by = #{importBy,jdbcType=VARCHAR},
            </if>
            <if test="orderSource != null">
                order_source = #{orderSource,jdbcType=CHAR},
            </if>
            <if test="returnrOverNum != null">
                returnr_over_num = #{returnrOverNum,jdbcType=DECIMAL},
            </if>
            <if test="failRemark != null">
                fail_remark = #{failRemark,jdbcType=VARCHAR},
            </if>
            <if test="isTimeout != null">
                is_timeout = #{isTimeout,jdbcType=INTEGER},
            </if>
        </set>
        where id = {id}
    </update>




    <resultMap id="BaseYsbillCodeDetailResultMap" type="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeDetailinfo">
        <!--@mbg.generated-->
        <!--@Table bms_ysbillcode_detailinfo-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="ysbill_id" jdbcType="VARCHAR" property="ysbillId" />
        <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
        <result column="sku_class" jdbcType="VARCHAR" property="skuClass" />
        <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
        <result column="total_boxes" jdbcType="DECIMAL" property="totalBoxes" />
        <result column="odd_boxes" jdbcType="DECIMAL" property="oddBoxes" />
        <result column="box_type" jdbcType="VARCHAR" property="boxType" />
        <result column="temperature_type" jdbcType="CHAR" property="temperatureType" />
        <result column="contents_number" jdbcType="DECIMAL" property="contentsNumber" />
        <result column="weight" jdbcType="DECIMAL" property="weight" />
        <result column="volume" jdbcType="DECIMAL" property="volume" />
        <result column="total_weight" jdbcType="DECIMAL" property="totalWeight" />
        <result column="total_volume" jdbcType="DECIMAL" property="totalVolume" />
        <result column="price" jdbcType="DECIMAL" property="price" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    </resultMap>
    <sql id="Base_YsbillcodeDetail_Column_List">
        <!--@mbg.generated-->
        id, ysbill_id, sku_code, sku_class, sku_name, total_boxes, odd_boxes, box_type, temperature_type,
        contents_number, weight, volume, total_weight, total_volume, price, del_flag, total_amount
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseYsbillCodeDetailResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_YsbillcodeDetail_Column_List" />
        from bms_ysbillcode_detailinfo
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="ysbillId != null and ysbillId != ''">
                and ysbill_id = #{ysbillId}
            </if>
            <if test="skuCode != null and skuCode != ''">
                and sku_code = #{skuCode}
            </if>
            <if test="skuClass != null and skuClass != ''">
                and sku_class = #{skuClass}
            </if>
            <if test="skuName != null and skuName != ''">
                and sku_name = #{skuName}
            </if>
            <if test="totalBoxes != null and totalBoxes != ''">
                and total_boxes = #{totalBoxes}
            </if>
            <if test="oddBoxes != null and oddBoxes != ''">
                and odd_boxes = #{oddBoxes}
            </if>
            <if test="boxType != null and boxType != ''">
                and box_type = #{boxType}
            </if>
            <if test="temperatureType != null and temperatureType != ''">
                and temperature_type = #{temperatureType}
            </if>
            <if test="contentsNumber != null and contentsNumber != ''">
                and contents_number = #{contentsNumber}
            </if>
            <if test="delFlag != null and delFlag != ''">
                and del_flag = #{delFlag}
            </if>
        </where>
    </select>
    <update id="deleteYsbillCodeDetailByPrimaryKey" parameterType="java.lang.String">
        <!--@mbg.generated-->
        update bms_ysbillcode_detailinfo
            set del_flag=1
            ,oper_code='接口对接'
            ,oper_by='接口对接'
            ,oper_time=NOW()
        where id = #{id,jdbcType=BIGINT}
    </update>
    <insert id="insertYsbillCodeDetail" >
        <!--@mbg.generated-->
        insert into bms_ysbillcode_detailinfo (ysbill_id, sku_code, sku_class,
        sku_name, total_boxes, odd_boxes,
        box_type, temperature_type, contents_number,
        weight, volume, total_weight,
        total_volume, price, del_flag,
        total_amount)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.ysbillId,jdbcType=VARCHAR}, #{entity.skuCode,jdbcType=VARCHAR}, #{entity.skuClass,jdbcType=VARCHAR},
            #{entity.skuName,jdbcType=VARCHAR}, #{entity.totalBoxes,jdbcType=DECIMAL}, #{entity.oddBoxes,jdbcType=DECIMAL},
            #{entity.boxType,jdbcType=VARCHAR}, #{entity.temperatureType,jdbcType=CHAR}, #{entity.contentsNumber,jdbcType=DECIMAL},
            #{entity.weight,jdbcType=DECIMAL}, #{entity.volume,jdbcType=DECIMAL}, #{entity.totalWeight,jdbcType=DECIMAL},
            #{entity.totalVolume,jdbcType=DECIMAL}, #{entity.price,jdbcType=DECIMAL}, #{entity.delFlag,jdbcType=CHAR},
            #{entity.totalAmount,jdbcType=DECIMAL})
        </foreach>
    </insert>

    <update id="updateYsbillCodeDetailByCond" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeDetailinfo">
        <!--@mbg.generated-->
        update bms_ysbillcode_detailinfo
        <set>
            <if test="ysbillId != null">
                ysbill_id = #{ysbillId,jdbcType=VARCHAR},
            </if>
            <if test="skuCode != null">
                sku_code = #{skuCode,jdbcType=VARCHAR},
            </if>
            <if test="skuClass != null">
                sku_class = #{skuClass,jdbcType=VARCHAR},
            </if>
            <if test="skuName != null">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="totalBoxes != null">
                total_boxes = #{totalBoxes,jdbcType=DECIMAL},
            </if>
            <if test="oddBoxes != null">
                odd_boxes = #{oddBoxes,jdbcType=DECIMAL},
            </if>
            <if test="boxType != null">
                box_type = #{boxType,jdbcType=VARCHAR},
            </if>
            <if test="temperatureType != null">
                temperature_type = #{temperatureType,jdbcType=CHAR},
            </if>
            <if test="contentsNumber != null">
                contents_number = #{contentsNumber,jdbcType=DECIMAL},
            </if>
            <if test="weight != null">
                weight = #{weight,jdbcType=DECIMAL},
            </if>
            <if test="volume != null">
                volume = #{volume,jdbcType=DECIMAL},
            </if>
            <if test="totalWeight != null">
                total_weight = #{totalWeight,jdbcType=DECIMAL},
            </if>
            <if test="totalVolume != null">
                total_volume = #{totalVolume,jdbcType=DECIMAL},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <resultMap id="BaseYsCostInfoResultMap" type="com.bbyb.joy.bms.domain.dto.BmsYscostInfo">
        <!--@mbg.generated-->
        <!--@Table bms_yscost_info-->
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="expenses_code" jdbcType="VARCHAR" property="expensesCode" />
        <result column="business_type" jdbcType="SMALLINT" property="businessType" />
        <result column="client_id" jdbcType="INTEGER" property="clientId" />
        <result column="institution_id" jdbcType="INTEGER" property="institutionId" />
        <result column="expenses_type" jdbcType="SMALLINT" property="expensesType" />
        <result column="cost_dimension" jdbcType="SMALLINT" property="costDimension" />
        <result column="charge_type" jdbcType="SMALLINT" property="chargeType" />
        <result column="fee_flag" jdbcType="SMALLINT" property="feeFlag" />
        <result column="quoterule_id" jdbcType="VARCHAR" property="quoteruleId" />
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="freight" jdbcType="DECIMAL" property="freight" />
        <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
        <result column="ultrafar_fee" jdbcType="DECIMAL" property="ultrafarFee" />
        <result column="superframes_fee" jdbcType="DECIMAL" property="superframesFee" />
        <result column="excess_fee" jdbcType="DECIMAL" property="excessFee" />
        <result column="reduce_fee" jdbcType="DECIMAL" property="reduceFee" />
        <result column="outboundsorting_fee" jdbcType="DECIMAL" property="outboundsortingFee" />
        <result column="shortbarge_fee" jdbcType="DECIMAL" property="shortbargeFee" />
        <result column="return_fee" jdbcType="DECIMAL" property="returnFee" />
        <result column="exception_fee" jdbcType="DECIMAL" property="exceptionFee" />
        <result column="adjust_fee" jdbcType="DECIMAL" property="adjustFee" />
        <result column="adjust_remark" jdbcType="VARCHAR" property="adjustRemark" />
        <result column="other_cost1" jdbcType="DECIMAL" property="otherCost1" />
        <result column="other_cost2" jdbcType="DECIMAL" property="otherCost2" />
        <result column="other_cost3" jdbcType="DECIMAL" property="otherCost3" />
        <result column="other_cost4" jdbcType="DECIMAL" property="otherCost4" />
        <result column="other_cost5" jdbcType="DECIMAL" property="otherCost5" />
        <result column="other_cost6" jdbcType="DECIMAL" property="otherCost6" />
        <result column="other_cost7" jdbcType="DECIMAL" property="otherCost7" />
        <result column="other_cost8" jdbcType="DECIMAL" property="otherCost8" />
        <result column="other_cost9" jdbcType="DECIMAL" property="otherCost9" />
        <result column="other_cost10" jdbcType="DECIMAL" property="otherCost10" />
        <result column="other_cost11" jdbcType="DECIMAL" property="otherCost11" />
        <result column="other_cost12" jdbcType="DECIMAL" property="otherCost12" />
        <result column="cost_attribute" jdbcType="SMALLINT" property="costAttribute" />
        <result column="oper_code" jdbcType="VARCHAR" property="operCode" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="bill_id" jdbcType="BIGINT" property="billId" />
        <result column="bill_date" jdbcType="VARCHAR" property="billDate" />
        <result column="business_time" jdbcType="TIMESTAMP" property="businessTime" />
        <result column="over_num" jdbcType="DECIMAL" property="overNum" />
        <result column="over_sendnum" jdbcType="INTEGER" property="overSendnum" />
        <result column="storage_fee_price" jdbcType="DECIMAL" property="storageFeePrice" />
        <result column="disposal_fee_price" jdbcType="DECIMAL" property="disposalFeePrice" />
        <result column="other_fee_remark" jdbcType="VARCHAR" property="otherFeeRemark" />
        <result column="fee_type_first" jdbcType="VARCHAR" property="feeTypeFirst" />
        <result column="fee_create_ate" jdbcType="VARCHAR" property="feeCreateAte" />
        <result column="is_increment" jdbcType="TINYINT" property="isIncrement" />
        <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
        <result column="signing_date" jdbcType="TIMESTAMP" property="signingDate" />
        <result column="quoteruledetail_id" jdbcType="VARCHAR" property="quoteruledetailId" />
        <result column="show_bill_code" jdbcType="VARCHAR" property="showBillCode" />
        <result column="show_bill_id" jdbcType="INTEGER" property="showBillId" />
    </resultMap>
    <sql id="Base_Cost_Info_Column_List">
        <!--@mbg.generated-->
        pk_id, id, expenses_code, business_type, client_id, institution_id, expenses_type,
        cost_dimension, charge_type, fee_flag, quoterule_id, rule_name, remarks, freight,
        delivery_fee, ultrafar_fee, superframes_fee, excess_fee, reduce_fee, outboundsorting_fee,
        shortbarge_fee, return_fee, exception_fee, adjust_fee, adjust_remark, other_cost1,
        other_cost2, other_cost3, other_cost4, other_cost5, other_cost6, other_cost7, other_cost8,
        other_cost9, other_cost10, other_cost11, other_cost12, cost_attribute, oper_code,
        oper_by, oper_time, del_flag, bill_id, bill_date, business_time, over_num, over_sendnum,
        storage_fee_price, disposal_fee_price, other_fee_remark, fee_type_first, fee_create_ate,
        is_increment, order_date, signing_date, quoteruledetail_id, show_bill_code, show_bill_id
    </sql>



    <insert id="insertYsCostInfo">
        <!--@mbg.generated-->
        insert into bms_yscost_info (id, expenses_code, business_type,
        client_id, institution_id, expenses_type,
        cost_dimension, charge_type, fee_flag,
        quoterule_id, rule_name, remarks,
        freight, delivery_fee, ultrafar_fee,
        superframes_fee, excess_fee, reduce_fee,
        outboundsorting_fee, shortbarge_fee, return_fee,
        exception_fee, adjust_fee, adjust_remark,
        other_cost1, other_cost2, other_cost3,
        other_cost4, other_cost5, other_cost6,
        other_cost7, other_cost8, other_cost9,
        other_cost10, other_cost11, other_cost12,
        cost_attribute, oper_code, oper_by,
        oper_time, del_flag, bill_id,
        bill_date, business_time, over_num,
        over_sendnum, storage_fee_price, disposal_fee_price,
        other_fee_remark, fee_type_first, fee_create_ate,
        is_increment, order_date, signing_date,
        quoteruledetail_id, show_bill_code, show_bill_id,settle_type,settle_amount
        )
        values
        <foreach collection="entities" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR}, #{entity.expensesCode,jdbcType=VARCHAR}, #{entity.businessType,jdbcType=SMALLINT},
            #{entity.clientId,jdbcType=INTEGER}, #{entity.institutionId,jdbcType=INTEGER}, #{entity.expensesType,jdbcType=SMALLINT},
            #{entity.costDimension,jdbcType=SMALLINT}, #{entity.chargeType,jdbcType=SMALLINT}, #{entity.feeFlag,jdbcType=SMALLINT},
            #{entity.quoteruleId,jdbcType=VARCHAR}, #{entity.ruleName,jdbcType=VARCHAR}, #{entity.remarks,jdbcType=VARCHAR},
            #{entity.freight,jdbcType=DECIMAL}, #{entity.deliveryFee,jdbcType=DECIMAL}, #{entity.ultrafarFee,jdbcType=DECIMAL},
            #{entity.superframesFee,jdbcType=DECIMAL}, #{entity.excessFee,jdbcType=DECIMAL}, #{entity.reduceFee,jdbcType=DECIMAL},
            #{entity.outboundsortingFee,jdbcType=DECIMAL}, #{entity.shortbargeFee,jdbcType=DECIMAL}, #{entity.returnFee,jdbcType=DECIMAL},
            #{entity.exceptionFee,jdbcType=DECIMAL}, #{entity.adjustFee,jdbcType=DECIMAL}, #{entity.adjustRemark,jdbcType=VARCHAR},
            #{entity.otherCost1,jdbcType=DECIMAL}, #{entity.otherCost2,jdbcType=DECIMAL}, #{entity.otherCost3,jdbcType=DECIMAL},
            #{entity.otherCost4,jdbcType=DECIMAL}, #{entity.otherCost5,jdbcType=DECIMAL}, #{entity.otherCost6,jdbcType=DECIMAL},
            #{entity.otherCost7,jdbcType=DECIMAL}, #{entity.otherCost8,jdbcType=DECIMAL}, #{entity.otherCost9,jdbcType=DECIMAL},
            #{entity.otherCost10,jdbcType=DECIMAL}, #{entity.otherCost11,jdbcType=DECIMAL}, #{entity.otherCost12,jdbcType=DECIMAL},
            #{entity.costAttribute,jdbcType=SMALLINT}, #{entity.operCode,jdbcType=VARCHAR}, #{entity.operBy,jdbcType=VARCHAR},
            #{entity.operTime,jdbcType=TIMESTAMP}, #{entity.delFlag,jdbcType=CHAR}, #{entity.billId,jdbcType=BIGINT},
            #{entity.billDate,jdbcType=VARCHAR}, #{entity.businessTime,jdbcType=TIMESTAMP}, #{entity.overNum,jdbcType=DECIMAL},
            #{entity.overSendnum,jdbcType=INTEGER}, #{entity.storageFeePrice,jdbcType=DECIMAL}, #{entity.disposalFeePrice,jdbcType=DECIMAL},
            #{entity.otherFeeRemark,jdbcType=VARCHAR}, #{entity.feeTypeFirst,jdbcType=VARCHAR}, #{entity.feeCreateAte,jdbcType=VARCHAR},
            #{entity.isIncrement,jdbcType=TINYINT}, #{entity.orderDate,jdbcType=TIMESTAMP}, #{entity.signingDate,jdbcType=TIMESTAMP},
            #{entity.quoteruledetailId,jdbcType=VARCHAR}, #{entity.showBillCode,jdbcType=VARCHAR}, #{entity.showBillId,jdbcType=INTEGER},
            #{entity.shareType},#{entity.shareAmount})
        </foreach>
    </insert>



    <resultMap id="BaseYsExtendResultMap" type="com.bbyb.joy.bms.domain.dto.BmsYscostExtend">
        <!--@mbg.generated-->
        <!--@Table bms_yscost_extend-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="expenses_id" jdbcType="VARCHAR" property="expensesId" />
        <result column="business_code" jdbcType="LONGVARCHAR" property="businessCode" />
        <result column="store_name" jdbcType="VARCHAR" property="storeName" />
        <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
        <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
        <result column="total_boxes" jdbcType="DECIMAL" property="totalBoxes" />
        <result column="total_number" jdbcType="DECIMAL" property="totalNumber" />
        <result column="total_weight" jdbcType="DECIMAL" property="totalWeight" />
        <result column="total_volume" jdbcType="DECIMAL" property="totalVolume" />
        <result column="cargo_value" jdbcType="DECIMAL" property="cargoValue" />
        <result column="total_quantity" jdbcType="DECIMAL" property="totalQuantity" />
        <result column="price" jdbcType="DECIMAL" property="price" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="automatic_billing_remark" jdbcType="LONGVARCHAR" property="automaticBillingRemark" />
        <result column="code_count" jdbcType="INTEGER" property="codeCount" />
        <result column="sku_number" jdbcType="DECIMAL" property="skuNumber" />
        <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
        <result column="signing_date" jdbcType="TIMESTAMP" property="signingDate" />
        <result column="quotation_information" jdbcType="LONGVARCHAR" property="quotationInformation" />
    </resultMap>
    <sql id="Base_Ys_Extend_Column_List">
        <!--@mbg.generated-->
        id, expenses_id, business_code, store_name, warehouse_code, warehouse_name, total_boxes,
        total_number, total_weight, total_volume, cargo_value, total_quantity, price, del_flag,
        automatic_billing_remark, code_count, sku_number, order_date, signing_date, quotation_information
    </sql>

    <insert id="insertYsExtend"  parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostExtend">
        <!--@mbg.generated-->
        insert into bms_yscost_extend (expenses_id, business_code, store_name,
        warehouse_code, warehouse_name, total_boxes,
        total_number, total_weight, total_volume,
        cargo_value, total_quantity, price,
        del_flag, automatic_billing_remark, code_count,
        sku_number, order_date, signing_date,
        quotation_information)
        values
        <foreach collection="entities" item="entity" separator=",">
            (
            #{entity.expensesId,jdbcType=VARCHAR}, #{entity.businessCode,jdbcType=LONGVARCHAR}, #{entity.storeName,jdbcType=VARCHAR},
            #{entity.warehouseCode,jdbcType=VARCHAR}, #{entity.warehouseName,jdbcType=VARCHAR}, #{entity.totalBoxes,jdbcType=DECIMAL},
            #{entity.totalNumber,jdbcType=DECIMAL}, #{entity.totalWeight,jdbcType=DECIMAL}, #{entity.totalVolume,jdbcType=DECIMAL},
            #{entity.cargoValue,jdbcType=DECIMAL}, #{entity.totalQuantity,jdbcType=DECIMAL}, #{entity.price,jdbcType=DECIMAL},
            #{entity.delFlag,jdbcType=CHAR}, #{entity.automaticBillingRemark,jdbcType=LONGVARCHAR}, #{entity.codeCount,jdbcType=INTEGER},
            #{entity.skuNumber,jdbcType=DECIMAL}, #{entity.orderDate,jdbcType=TIMESTAMP}, #{entity.signingDate,jdbcType=TIMESTAMP},
            #{entity.quotationInformation,jdbcType=LONGVARCHAR}
            )
        </foreach>
    </insert>



    <resultMap id="BaseYsMiddleResultMap" type="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle">
        <!--@mbg.generated-->
        <!--@Table bms_ysexpenses_middle-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="ysbill_id" jdbcType="VARCHAR" property="ysbillId" />
        <result column="ysbil_type" jdbcType="INTEGER" property="ysbilType" />
        <result column="expenses_id" jdbcType="VARCHAR" property="expensesId" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    </resultMap>
    <sql id="Base_Ys_Middle_Column_List">
        <!--@mbg.generated-->
        id, ysbill_id, ysbil_type, expenses_id, oper_by, oper_time, del_flag
    </sql>


    <insert id="insertYsMiddle"  parameterType="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle" >
        <!--@mbg.generated-->
        insert into bms_ysexpenses_middle (ysbill_id, ysbil_type, expenses_id,
        oper_by, oper_time, del_flag
        )
        values
        <foreach collection="entities" item="entity" separator=",">
            (
            #{entity.ysbillId,jdbcType=VARCHAR}, #{entity.ysbilType,jdbcType=INTEGER}, #{entity.expensesId,jdbcType=VARCHAR},
            #{entity.operBy,jdbcType=VARCHAR}, #{entity.operTime,jdbcType=TIMESTAMP}, #{entity.delFlag,jdbcType=CHAR}
            )
        </foreach>
    </insert>



    <select id="judgeYsIsCost" resultType="com.bbyb.joy.bms.domain.dto.BmsYscostExtend">
        SELECT
            t1.expenses_id AS expensesId,
            t1.ysbill_id AS ysbillId,
            t2.bill_id AS billId,
            t2.bill_date AS billDate
        FROM bms_ysexpenses_middle t1
        LEFT JOIN bms_yscost_info t2 ON t2.id = t1.expenses_id AND t2.del_flag=0
        WHERE t1.del_flag=0
          AND t1.ysbill_id = #{ysbillId}
        LIMIT 1
    </select>

    <update id="cancelYsCodeInfo">
        UPDATE bms_ysbillcode_detailinfo
            SET del_flag = '1'
        WHERE del_flag='0'
            AND ysbill_id = #{ysbillId};
        UPDATE bms_ysbillcodeinfo
            SET del_flag = '1'
                ,oper_by=#{operBy}
                ,oper_code=#{operCode}
                ,oper_time=NOW()
        WHERE del_flag='0'
        AND id = #{ysbillId};
    </update>




    <select id="yfJudgeYsIsCost" resultMap="BmsYfexpensesMiddleMap">
        SELECT
        t1.expenses_id,
        t1.yfbill_id,
        t2.bill_id AS billId,
        t2.bill_date AS billDate
        FROM bms_yfexpenses_middle t1
        LEFT JOIN bms_yfcost_info t2 ON t2.id = t1.expenses_id AND t2.del_flag=0
        WHERE t1.del_flag=0
        AND t1.yfbill_id IN
        <foreach collection="yfbillIds" item="yfbillId" open="(" separator="," close=")">
            #{yfbillId}
        </foreach>
    </select>


    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYfexpensesMiddle" id="BmsYfexpensesMiddleMap">
        <result property="id" column="id"/>
        <result property="yfbillId" column="yfbill_id"/>
        <result property="yfbillType" column="yfbill_type"/>
        <result property="expensesId" column="expenses_id" />
        <result property="operBy" column="oper_by"/>
        <result property="operTime" column="oper_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="billId" column="billId"/>
        <result property="billDate" column="billDate"/>

    </resultMap>

    <update id="cancelYfCostInfo">
        UPDATE bms_yfexpenses_middle
            SET del_flag = '1'
        WHERE del_flag='0'
            AND expenses_id IN
            <foreach collection="expensesIds" item="expensesId" open="(" separator="," close=")">
                #{expensesId}
            </foreach>;
        UPDATE bms_yfcost_extend
            SET del_flag = '1'
            ,oper_by=#{operBy}
            ,oper_code=#{operCode}
            ,oper_time=NOW()
        WHERE del_flag='0'
            AND expenses_id IN
            <foreach collection="expensesIds" item="expensesId" open="(" separator="," close=")">
                #{expensesId}
            </foreach>;
        UPDATE bms_yfcost_info
            SET del_flag = '1'
            ,oper_by=#{operBy}
            ,oper_code=#{operCode}
            ,oper_time=NOW()
        WHERE del_flag='0'
            AND id IN
            <foreach collection="expensesIds" item="expensesId" open="(" separator="," close=")">
                #{expensesId}
            </foreach>;
    </update>






    <update id="cancelYfCodeInfos">
        UPDATE bms_yfjobbill_goodsinfo
            SET del_flag = '1'
        WHERE del_flag = '0'
        AND jobbill_id IN (
            SELECT
                t2.id
            FROM bms_yfbillcodeinfo t1
            LEFT JOIN bms_yfjobbillinfo t2 ON t2.scheduling_bill_code = t1.virtual_order_no AND t2.del_flag = '0'
            WHERE t1.id IN
                <foreach collection="scheduleIds" item="scheduleId" open="(" separator="," close=")">
                    #{scheduleId}
                </foreach>
        );
        UPDATE bms_yfjobbillinfo
            SET del_flag = '1'
        WHERE del_flag = '0'
        AND scheduling_bill_code IN (
            SELECT
                virtual_order_no
            FROM bms_yfbillcodeinfo
            WHERE id IN
            <foreach collection="scheduleIds" item="scheduleId" open="(" separator="," close=")">
                #{scheduleId}
            </foreach>
        );
        UPDATE bms_yfbillcodeinfo
            SET del_flag = '1'
        WHERE id IN
        <foreach collection="scheduleIds" item="scheduleId" open="(" separator="," close=")">
            #{scheduleId}
        </foreach>;
    </update>




    <!-- 批量新增应付调度单数据 -->
    <insert id="insertYfbillCodeInfos" >
        insert into bms_yfbillcodeinfo(id,scheduling_bill_code,virtual_order_no,hy_order_no,project_quote,is_to_client,carrier_code,carrier_name,company_id,network_code,total_boxes,total_number,total_weight,total_volume,cargo_value,dispatch_date,start_date,finish_date,transport_type,delivery_mode,line_code,line_name,base_stores,base_kilometer,total_kilometer,number_loading_points,number_unloading_points,total_votenumber,driver,car_code,car_type,car_model,head_office_times,province_origin,originating_city,originating_area,originating_address,destination_province,destination_city,destination_area,destination_address,cost_status,billing_status,create_code,create_by,create_dept_id,create_time,oper_code,oper_by,oper_dept_id,oper_time,del_flag,client_id,body_office_times,tline_code,tline_name,vehicle_temperature_type,order_no,fail_remark,order_source,store_number,express_no,original_order_type,transport_mode)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.schedulingBillCode},#{entity.virtualOrderNo},#{entity.hyOrderNo},#{entity.projectQuote},#{entity.isToClient},#{entity.carrierCode},#{entity.carrierName},#{entity.companyId},#{entity.networkCode},#{entity.totalBoxes},#{entity.totalNumber},#{entity.totalWeight},#{entity.totalVolume},#{entity.cargoValue},#{entity.dispatchDate},#{entity.startTime},#{entity.finishDate},#{entity.transportType},#{entity.deliveryMode},#{entity.lineCode},#{entity.lineName},#{entity.baseStores},#{entity.baseKilometer},#{entity.totalKilometer},#{entity.numberLoadingPoints},#{entity.numberUnloadingPoints},#{entity.totalVotenumber},#{entity.driver},#{entity.carCode},#{entity.carType},#{entity.carModel},#{entity.headOfficeTimes},#{entity.provinceOrigin},#{entity.originatingCity},#{entity.originatingArea},#{entity.originatingAddress},#{entity.destinationProvince},#{entity.destinationCity},#{entity.destinationArea},#{entity.destinationAddress},#{entity.costStatus},#{entity.billingStatus},#{entity.createCode},#{entity.createBy},#{entity.createDeptId},#{entity.createTime},#{entity.operCode},#{entity.operBy},#{entity.operDeptId},#{entity.operTime},#{entity.delFlag},#{entity.clientId},#{entity.bodyOfficeTimes},#{entity.tlineCode},#{entity.tlineName},#{entity.vehicleTemperatureType},#{entity.orderNo},#{entity.failRemark},#{entity.orderSource},#{entity.storeNumber},#{entity.expressNo},#{entity.originalOrderType},#{entity.transportMode})
        </foreach>
    </insert>
    <!-- 批量新增应付作业单数据 -->
    <insert id="insertJobCodes">
        insert into bms_yfjobbillinfo(id,relate_code,workcode,scheduling_bill_code,client_code,client_name,company_id,network_code,total_boxes,total_number,total_weight,total_volume,cargo_value,if_base_stores,if_super_base_kilometer,store_distance_kilometer,store_code,receiving_store,create_code,create_by,create_dept_id,create_time,oper_code,oper_by,oper_dept_id,oper_time,del_flag,near_store_km,deliver_sitecode,deliver_sitename,deliver_address,deliver_province,deliver_city,deliver_district,order_province,order_city,order_district,arrive_sitecode,arrive_sitename,arrive_address,start_unit,arrive_unit,pay_unit)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.relateCode},#{entity.workCode},#{entity.schedulingBillCode},#{entity.clientCode},#{entity.clientName},#{entity.companyId},#{entity.networkCode},#{entity.totalBoxes},#{entity.totalNumber},#{entity.totalWeight},#{entity.totalVolume},#{entity.cargoValue},#{entity.ifBaseStores},#{entity.ifSuperBaseKilometer},#{entity.storeDistanceKilometer},#{entity.storeCode},#{entity.receivingStore},#{entity.createCode},#{entity.createBy},#{entity.createDeptId},#{entity.createTime},#{entity.operCode},#{entity.operBy},#{entity.operDeptId},#{entity.operTime},#{entity.delFlag},#{entity.nearStoreKm},#{entity.networkCode},#{entity.originatingCompany},#{entity.originatingAddress},#{entity.provinceOrigin},#{entity.originatingCity},#{entity.originatingArea},#{entity.destinationProvince},#{entity.destinationCity},#{entity.destinationArea},#{entity.storeCode},#{entity.destinationCompany},#{entity.destinationAddress},#{entity.companyId},#{entity.receiveCompanyId},#{entity.payCompanyId})
        </foreach>
    </insert>

    <!-- 批量新增作业单明细数据 -->
    <insert id="insertJobGoods">
        insert into bms_yfjobbill_goodsinfo(jobbill_id,sku_code,sku_name,total_boxes,odd_boxes,numbers,box_type,temperature_type,weight,volume,total_weight,total_volume,price,oper_code,oper_by,oper_dept_id,oper_time,del_flag,sku_class)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.jobbillId},#{entity.skuCode},#{entity.skuName},#{entity.totalBoxes},#{entity.oddBoxes},#{entity.numbers},#{entity.boxType},#{entity.temperatureType},#{entity.weight},#{entity.volume},#{entity.totalWeight},#{entity.totalVolume},#{entity.price},#{entity.operCode},#{entity.operBy},#{entity.operDeptId},#{entity.operTime},#{entity.delFlag},#{entity.skuClass})
        </foreach>
    </insert>


    <!-- 批量新增应付费用主表数据 -->
    <insert id="insertYfCosts">
        insert into bms_yfcost_info(id,expenses_code,business_type,carrier_code,carrier_name,company_id,expenses_type,cost_dimension,charge_type,fee_flag,quoterule_id,rule_name,remarks,freight,delivery_fee,superframes_fee,excess_fee,reduce_fee,shortbarge_fee,return_fee,ultrafar_fee,outboundsorting_fee,exception_fee,share_amount,share_type,adjust_fee,adjust_remark,other_cost1,other_cost2,other_cost3,other_cost4,other_cost5,other_cost6,other_cost7,other_cost8,other_cost9,other_cost10,other_cost11,other_cost12,cost_attribute,oper_code,oper_by,oper_time,del_flag,bill_id,bill_date,business_time,over_num,over_sendnum,storage_fee_price,disposal_fee_price,other_fee_remark,client_id,fee_type_first,fee_create_ate,is_increment,dispatch_date,finish_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.expensesCode},#{entity.businessType},#{entity.carrierCode},#{entity.carrierName},#{entity.companyId},#{entity.expensesType},#{entity.costDimension},#{entity.chargeType},#{entity.feeFlag},#{entity.quoteruleId},#{entity.ruleName},#{entity.remarks},#{entity.freight},#{entity.deliveryFee},#{entity.superframesFee},#{entity.excessFee},#{entity.reduceFee},#{entity.shortbargeFee},#{entity.returnFee},#{entity.ultrafarFee},#{entity.outboundsortingFee},#{entity.exceptionFee},#{entity.shareAmount},#{entity.shareType},#{entity.adjustFee},#{entity.adjustRemark},#{entity.otherCost1},#{entity.otherCost2},#{entity.otherCost3},#{entity.otherCost4},#{entity.otherCost5},#{entity.otherCost6},#{entity.otherCost7},#{entity.otherCost8},#{entity.otherCost9},#{entity.otherCost10},#{entity.otherCost11},#{entity.otherCost12},#{entity.costAttribute},#{entity.operCode},#{entity.operBy},#{entity.operTime},#{entity.delFlag},#{entity.billId},#{entity.billDate},#{entity.businessTime},#{entity.overNum},#{entity.overSendnum},#{entity.storageFeePrice},#{entity.disposalFeePrice},#{entity.otherFeeRemark},#{entity.clientId},#{entity.feeTypeFirst},#{entity.feeCreateAte},#{entity.isIncrement},#{entity.dispatchDate},#{entity.finishDate})
        </foreach>
    </insert>



    <!-- 批量新增应付费用中间表数据 -->
    <insert id="insertYfMiddles">
        insert into bms_yfexpenses_middle(yfbill_id,yfbill_type,expenses_id,oper_by,oper_time,del_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.yfbillId},#{entity.yfbillType},#{entity.expensesId},#{entity.operBy},#{entity.operTime},#{entity.delFlag})
        </foreach>
    </insert>


    <!-- 批量新增应付拓展表数据 -->
    <insert id="insertYfExtends">
        insert into bms_yfcost_extend(expenses_id,business_code,warehouse_code,warehouse_name,total_boxes,total_number,total_weight,total_volume,cargo_value,total_quantity,price,del_flag,automatic_billing_remark,code_count,tline_code,tline_name,line_code,dispatch_date,finish_date,quotation_information)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.expensesId},#{entity.businessCode},#{entity.warehouseCode},#{entity.warehouseName},#{entity.totalBoxes},#{entity.totalNumber},#{entity.totalWeight},#{entity.totalVolume},#{entity.cargoValue},#{entity.totalQuantity},#{entity.price},#{entity.delFlag},#{entity.automaticBillingRemark},#{entity.codeCount},#{entity.tlineCode},#{entity.tlineName},#{entity.lineCode},#{entity.dispatchDate},#{entity.finishDate},#{entity.quotationInformation})
        </foreach>
    </insert>


    <!-- 批量新增应付费用主表数据 -->
    <insert id="insertYfCostMains">
        insert into bms_yfcost_main_info(id,pk_id,expenses_code,business_type,carrier_code,carrier_name,company_id,expenses_type,cost_dimension,charge_type,fee_flag,quoterule_id,rule_name,remarks,freight,delivery_fee,superframes_fee,excess_fee,reduce_fee,shortbarge_fee,return_fee,ultrafar_fee,outboundsorting_fee,exception_fee,adjust_fee,adjust_remark,other_cost1,other_cost2,other_cost3,other_cost4,other_cost5,other_cost6,other_cost7,other_cost8,other_cost9,other_cost10,other_cost11,other_cost12,sum_fee,cost_attribute,total_weight,total_volume,total_boxes,total_number,oper_code,oper_by,oper_time,del_flag,bill_id,bill_date,business_time,over_num,over_sendnum,storage_fee_price,disposal_fee_price,other_fee_remark,client_id,fee_type_first,fee_create_ate,is_increment,dispatch_date,finish_date,show_bill_code,extra_field1,warehouse_code_arr,settle_setting,create_code,create_by,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.pkId},#{entity.expensesCode},#{entity.businessType},#{entity.carrierCode},#{entity.carrierName},#{entity.companyId},#{entity.expensesType},#{entity.costDimension},#{entity.chargeType},#{entity.feeFlag},#{entity.quoteruleId},#{entity.ruleName},#{entity.remarks},#{entity.freight},#{entity.deliveryFee},#{entity.superframesFee},#{entity.excessFee},#{entity.reduceFee},#{entity.shortbargeFee},#{entity.returnFee},#{entity.ultrafarFee},#{entity.outboundsortingFee},#{entity.exceptionFee},#{entity.adjustFee},#{entity.adjustRemark},#{entity.otherCost1},#{entity.otherCost2},#{entity.otherCost3},#{entity.otherCost4},#{entity.otherCost5},#{entity.otherCost6},#{entity.otherCost7},#{entity.otherCost8},#{entity.otherCost9},#{entity.otherCost10},#{entity.otherCost11},#{entity.otherCost12},#{entity.sumFee},#{entity.costAttribute},#{entity.totalWeight},#{entity.totalVolume},#{entity.totalBoxes},#{entity.totalNumber},#{entity.operCode},#{entity.operBy},#{entity.operTime},#{entity.delFlag},#{entity.billId},#{entity.billDate},#{entity.businessTime},#{entity.overNum},#{entity.overSendnum},#{entity.storageFeePrice},#{entity.disposalFeePrice},#{entity.otherFeeRemark},#{entity.clientId},#{entity.feeTypeFirst},#{entity.feeCreateAte},#{entity.isIncrement},#{entity.dispatchDate},#{entity.finishDate},#{entity.showBillCode},#{entity.extraField1},#{entity.warehouseCodeArr},#{entity.settleSetting},#{entity.createCode},#{entity.createBy},#{entity.createTime})
        </foreach>
    </insert>



    <!-- 批量新增数据 -->
    <insert id="insertYfMiddleShares">
        insert into bms_yfexpenses_middle_share(yfbill_id,yfbill_type,expenses_id,main_expense_id,oper_by,oper_time,del_flag,share_type,share_amount,freight,delivery_fee,superframes_fee,excess_fee,reduce_fee,shortbarge_fee,return_fee,ultrafar_fee,outboundsorting_fee,exception_fee,other_cost1,other_cost2,other_cost3,other_cost4,other_cost5,other_cost6,other_cost7,other_cost8,other_cost9,other_cost10,other_cost11,other_cost12)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.yfbillId},#{entity.yfbillType},#{entity.expensesId},#{entity.mainExpenseId},#{entity.operBy},#{entity.operTime},#{entity.delFlag},#{entity.shareType},#{entity.shareAmount},#{entity.freight},#{entity.deliveryFee},#{entity.superframesFee},#{entity.excessFee},#{entity.reduceFee},#{entity.shortbargeFee},#{entity.returnFee},#{entity.ultrafarFee},#{entity.outboundsortingFee},#{entity.exceptionFee},#{entity.otherCost1},#{entity.otherCost2},#{entity.otherCost3},#{entity.otherCost4},#{entity.otherCost5},#{entity.otherCost6},#{entity.otherCost7},#{entity.otherCost8},#{entity.otherCost9},#{entity.otherCost10},#{entity.otherCost11},#{entity.otherCost12})
        </foreach>
    </insert>







</mapper>