<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsEstimateCostInfoMapper">
  <resultMap id="BaseResultMap" type="com.bbyb.joy.bms.domain.dto.BmsEstimateCostInfo">
    <!--@mbg.generated-->
    <!--@Table bms_estimate_cost_info-->
    <id column="id" property="id" />
    <result column="billing_moudle" property="billingMoudle" />
    <result column="billing_fun" property="billingFun" />
    <result column="estimate_billing_state" property="estimateBillingState" />
    <result column="oper_by" property="operBy" />
    <result column="billing_start_time" property="billingStartTime" />
    <result column="billing_end_time" property="billingEndTime" />
    <result column="oper_code" property="operCode" />
    <result column="oper_time" property="operTime" />
    <result column="del_flag" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, billing_moudle, billing_fun, billing_start_time, billing_end_time, estimate_billing_state,
    oper_by, oper_code, oper_time, del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from bms_estimate_cost_info
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from bms_estimate_cost_info
    where id = #{id}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.bbyb.joy.bms.domain.dto.BmsEstimateCostInfo">
    <!--@mbg.generated-->
    insert into bms_estimate_cost_info (billing_moudle, billing_fun, billing_start_time, billing_end_time,
    estimate_billing_state, oper_by, oper_code, oper_time, del_flag)
    values (#{billingMoudle}, #{billingFun}, #{billingStartTime}, #{billingEndTime},
    #{estimateBillingState}, #{operBy}, #{operCode}, #{operTime}, #{delFlag})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.bbyb.joy.bms.domain.dto.BmsEstimateCostInfo">
    <!--@mbg.generated-->
    insert into bms_estimate_cost_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="billingMoudle != null">
        billing_moudle,
      </if>
      <if test="billingFun != null">
        billing_fun,
      </if>
      <if test="billingStartTime != null">
        billing_start_time,
      </if>
      <if test="billingEndTime != null">
        billing_end_time,
      </if>
      <if test="estimateBillingState != null">
        estimate_billing_state,
      </if>
      <if test="operBy != null">
        oper_by,
      </if>
      <if test="operCode != null">
        oper_code,
      </if>
      <if test="operTime != null">
        oper_time,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="billingMoudle != null">
        #{billingMoudle},
      </if>
      <if test="billingFun != null">
        #{billingFun},
      </if>
      <if test="billingStartTime != null">
        #{billingStartTime},
      </if>
      <if test="billingEndTime != null">
        #{billingEndTime},
      </if>
      <if test="estimateBillingState != null">
        #{estimateBillingState},
      </if>
      <if test="operBy != null">
        #{operBy},
      </if>
      <if test="operCode != null">
        #{operCode},
      </if>
      <if test="operTime != null">
        #{operTime},
      </if>
      <if test="delFlag != null">
        #{delFlag},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.bbyb.joy.bms.domain.dto.BmsEstimateCostInfo">
    <!--@mbg.generated-->
    update bms_estimate_cost_info
    <set>
      <if test="billingMoudle != null">
        billing_moudle = #{billingMoudle},
      </if>
      <if test="billingFun != null">
        billing_fun = #{billingFun},
      </if>
      <if test="billingStartTime != null">
        billing_start_time = #{billingStartTime},
      </if>
      <if test="billingEndTime != null">
        billing_end_time = #{billingEndTime},
      </if>
      <if test="estimateBillingState != null">
        estimate_billing_state = #{estimateBillingState},
      </if>
      <if test="operBy != null">
        oper_by = #{operBy},
      </if>
      <if test="operCode != null">
        oper_code = #{operCode},
      </if>
      <if test="operTime != null">
        oper_time = #{operTime},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bbyb.joy.bms.domain.dto.BmsEstimateCostInfo">
    <!--@mbg.generated-->
    update bms_estimate_cost_info
    set billing_moudle = #{billingMoudle},
    billing_fun = #{billingFun},
    billing_start_time = #{billingStartTime},
    billing_end_time = #{billingEndTime},
    estimate_billing_state = #{estimateBillingState},
    oper_by = #{operBy},
    oper_code = #{operCode},
    oper_time = #{operTime},
    del_flag = #{delFlag}
    where id = #{id}
  </update>

  <select id="selListByYs" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from bms_estimate_cost_info
    where del_flag = 0 and billing_moudle in
    <foreach item="entitie" collection="entities" open="(" separator="," close=")">
      #{entitie}
    </foreach>
  </select>

  <select id="selectByBillingMoudle" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bms_estimate_cost_info
    where del_flag = 0 and billing_moudle = #{billingMoudle}
  </select>

  <update id="updateByBillingMoudle">
    update bms_estimate_cost_info
    set estimate_billing_state = #{estimateBillingState},
    billing_start_time = #{billingStartTime},
    billing_end_time = #{billingEndTime},
    oper_by = #{operBy},
    oper_code = #{operCode},
    oper_time = #{operTime}
    where billing_moudle = #{billingMoudle}
  </update>
</mapper>