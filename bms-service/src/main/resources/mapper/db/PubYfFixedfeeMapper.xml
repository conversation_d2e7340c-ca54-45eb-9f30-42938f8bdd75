<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubYfFixedfeeMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.PubYfFixedfee" id="PubYfFixedfeeResult">
        <result property="id"    column="id"    />
        <result property="expensesCode"    column="expenses_code"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="itemId"    column="item_id"/>
        <result property="clientId"    column="client_id"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="companyId"    column="company_id"    />
        <result property="relationId"    column="relation_id"    />
        <result property="startDate"    column="start_date"    />
        <result property="frequency"    column="frequency"    />
        <result property="amount"    column="amount"    />
        <result property="billId"    column="bill_id"    />
        <result property="billCode"    column="bill_code"    />
        <result property="remark"    column="remark"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="startDateDesc"    column="startDateDesc"    />
        <result property="endDateDesc"    column="endDateDesc"    />
    </resultMap>

    <sql id="selectPubYfFixedfeeVo">
        select id, expenses_code, rule_id,item_id, rule_type, client_id, carrier_id, carrier_code, warehouse_code, company_id, relation_id, start_date, frequency, amount, bill_id, bill_code, remark, create_code, create_by, create_time, oper_code, oper_by, oper_time, del_flag from pub_yf_fixedfee
    </sql>

    <select id="selectPubYfFixedfeeList" parameterType="com.bbyb.joy.bms.domain.dto.PubYfFixedfee" resultMap="PubYfFixedfeeResult">
        <include refid="selectPubYfFixedfeeVo"/>
        <where>
            <if test="expensesCode != null  and expensesCode != ''"> and expenses_code = #{expensesCode}</if>
            <if test="ruleId != null "> and rule_id = #{ruleId}</if>
            <if test="ruleType != null "> and rule_type = #{ruleType}</if>
            <if test="clientId != null "> and client_id = #{clientId}</if>
            <if test="carrierId != null "> and carrier_id = #{carrierId}</if>
            <if test="carrierCode != null  and carrierCode != ''"> and carrier_code = #{carrierCode}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="relationId != null  and relationId != ''"> and relation_id = #{relationId}</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="frequency != null "> and frequency = #{frequency}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="billId != null "> and bill_id = #{billId}</if>
            <if test="billCode != null  and billCode != ''"> and bill_code = #{billCode}</if>
            <if test="createCode != null  and createCode != ''"> and create_code = #{createCode}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
        </where>
    </select>

    <select id="selectPubYfFixedfeeById" parameterType="java.lang.String" resultMap="PubYfFixedfeeResult">
        <include refid="selectPubYfFixedfeeVo"/>
        where id = #{id}
    </select>



    <insert id="insertPubYfFixedfee" parameterType="com.bbyb.joy.bms.domain.dto.PubYfFixedfee">
        insert into pub_yf_fixedfee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="expensesCode != null">expenses_code,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="clientId != null">client_id,</if>
            <if test="carrierId != null">carrier_id,</if>
            <if test="carrierCode != null">carrier_code,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="companyId != null">company_id,</if>
            <if test="relationId != null">relation_id,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="frequency != null">frequency,</if>
            <if test="amount != null">amount,</if>
            <if test="itemId != null">item_id,</if>
            <if test="billId != null">bill_id,</if>
            <if test="billCode != null">bill_code,</if>
            <if test="remark != null">remark,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="expensesCode != null">#{expensesCode},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="carrierId != null">#{carrierId},</if>
            <if test="carrierCode != null">#{carrierCode},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="relationId != null">#{relationId},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="amount != null">#{amount},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="billId != null">#{billId},</if>
            <if test="billCode != null">#{billCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updatePubYfFixedfee" parameterType="com.bbyb.joy.bms.domain.dto.PubYfFixedfee">
        update pub_yf_fixedfee
        <trim prefix="SET" suffixOverrides=",">
            <if test="expensesCode != null">expenses_code = #{expensesCode},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="ruleType != null">rule_type = #{ruleType},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="carrierId != null">carrier_id = #{carrierId},</if>
            <if test="carrierCode != null">carrier_code = #{carrierCode},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="relationId != null">relation_id = #{relationId},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate}</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="billId != null">bill_id = #{billId},</if>
            <if test="billCode != null">bill_code = #{billCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePubYfFixedfeeById" parameterType="java.lang.String">
        delete from pub_yf_fixedfee where id = #{id}
    </delete>

    <delete id="deletePubYfFixedfeeByIds">
        delete from pub_yf_fixedfee where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="del">
        update pub_yf_fixedfee set del_flag = 1 where id = #{id}
    </delete>

    <update id="updatePubYfFixedfeeStatusByIds">
        update pub_yf_fixedfee set status = #{status} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="batchUpdatePubYfFixedfee">
        <foreach collection="list" item="item" separator=";">
            update pub_yf_fixedfee
            <set>
                <if test="item.billId != null">bill_id = #{item.billId},</if>
                <if test="item.billCode != null and item.billCode != ''">billCode = #{item.billCode},</if>
                <if test="item.operCode != null">oper_code = #{item.operCode},</if>
                <if test="item.operBy != null">oper_by = #{item.operBy},</if>
                <if test="item.operTime != null">oper_time = #{item.operTime},</if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>
    <update id="updatePubYfFixedfeeAmount" parameterType="java.util.Map">
        <foreach collection="list" item="item" separator=";">
            update pub_yf_fixedfee
            <set>
                <if test="item.adjustBeforeRemark != null and item.adjustBeforeRemark != ''">
                    adjust_before_remark = #{item.adjustBeforeRemark},
                </if>
                <if test="item.amount!= null">
                    amount = #{item.amount}
                </if>

            </set>
            where id = #{item.id}
        </foreach>
    </update>
    <update id="updatePubYfFixedfeeByIds">
        <foreach collection="ids" item="id" separator=";">
            update pub_yf_fixedfee set bill_id = #{billId}, bill_code = #{billCode} where id = #{id}
        </foreach>
    </update>

    <update id="updatePubYfFixedfeeByIds2">
        <foreach collection="pubYfFixedfees" item="item" separator=";">
            update pub_yf_fixedfee set bill_id = #{item.billId}, bill_code = #{item.billCode} where id = #{item.id}
        </foreach>
    </update>

    <update id="fixedfeeModifyToNull">
        update pub_yf_fixedfee set bill_id = null, bill_code = null where id
        <foreach collection="billIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectMaxCodeOfDay" resultType="java.lang.String">
        select MAX(RIGHT(expenses_code,5)) as num from pub_yf_fixedfee where expenses_code like concat('FF_',#{date},'%')
    </select>
    <select id="selectForExistCheck" resultMap="PubYfFixedfeeResult">
        select id,
               expenses_code,
               rule_id,
               rule_type,
               item_id,
               client_id,
               carrier_id,
               carrier_code,
               warehouse_code,
               company_id,
               relation_id,
               start_date,
               end_date,
               frequency,
               amount,
               bill_id,
               bill_code,
               remark,
               create_code,
               create_by,
               create_time,
               oper_code,
               oper_by,
               oper_time,
               del_flag
        from pub_yf_fixedfee
        where del_flag = 0
        and client_id = #{clientId}
        and item_id = #{itemId}
        and rule_type = #{ruleType}
        and not (end_date &lt;= #{startDate} or start_date &gt;= #{endDate})
    </select>

    <resultMap type="com.bbyb.joy.bms.domain.dto.dto.PubYfFixedfeeDto" id="PubYfFixedfeeDto">
        <result property="id"    column="id"    />
        <result property="expensesCode"    column="expenses_code"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="itemId"    column="item_id"/>
        <result property="clientId"    column="client_id"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="companyId"    column="company_id"    />
        <result property="relationId"    column="relation_id"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="frequency"    column="frequency"    />
        <result property="amount"    column="amount"    />
        <result property="billId"    column="bill_id"    />
        <result property="billCode"    column="bill_code"    />
        <result property="remark"    column="remark"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="adjustBeforeAmount"    column="adjust_before_amount"    />
        <result property="adjustBeforeRemark"    column="adjust_before_remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>
    <select id="selectByBillId" resultMap="PubYfFixedfeeDto">
        select p.*,
               s.item_name AS itemName,
               mc.client_name AS clientName,
               p.frequency,
               case when p.frequency = 1 then '按月'
                    when p.frequency = 2 then '按季'
                   end as frequencyName
        from pub_yf_fixedfee p
                 left join pub_fee_subject s on p.item_id = s.id
                 left join bms_clientinfo mc on p.client_id = mc.id
        left join bms_yfbillmain yf on bill_id=yf.id
        where p.del_flag = 0
          and (p.bill_id = #{billId} or yf.fatherid=#{billId});
    </select>
    <select id="selectByBillIds" resultMap="PubYfFixedfeeDto">
        select p.*,
        s.item_name AS itemName,
        mc.client_name AS clientName,
        p.frequency,
        case when p.frequency = 1 then '按月'
        when p.frequency = 2 then '按季'
        end as frequencyName
        from pub_yf_fixedfee p
        left join pub_fee_subject s on p.item_id = s.id
        left join bms_clientinfo mc on p.client_id = mc.id
        left join bms_yfbillmain yf on bill_id=yf.id
        where p.del_flag = 0
        and p.bill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectPubYfFixedfee" resultMap="PubYfFixedfeeResult">
        select id,
               expenses_code,
               rule_id,
               rule_type,
               item_id,
               client_id,
               carrier_id,
               carrier_code,
               warehouse_code,
               company_id,
               relation_id,
               start_date,
               end_date,
               frequency,
               amount,
               bill_id,
               bill_code,
               remark,
               create_code,
               create_by,
               create_time,
               oper_code,
               oper_by,
               oper_time,
               del_flag
        from pub_yf_fixedfee <where>
        del_flag = 0
        and client_id = #{clientId}
        and carrier_id = #{carrierId}
        and rule_type = #{ruleType}
        and IFNULL(bill_id,'')=''
        <choose>
            <when test="ruleType == 1">
                AND company_id = #{companyId}
            </when>
            <when test="ruleType == 2">
                AND warehouse_code = #{warehouseCode}
            </when>
        </choose>
        <if test="frequency != null and frequency == 1">
            and frequency = 1 and DATE_FORMAT(start_date,'%Y-%m') = #{billDate}</if>
        <if test="frequency != null and frequency == 2">
            and frequency = 2
            and DATE_FORMAT(start_date,'%Y-%m') &gt;= #{billDateStart}
            and DATE_FORMAT(end_date,'%Y-%m') &lt;= #{billDate}
        </if>
    </where>
    </select>
    <select id="selectPubYfFixedfee2" resultMap="PubYfFixedfeeResult">
        select id,
               expenses_code,
               rule_id,
               rule_type,
               item_id,
               client_id,
               carrier_id,
               carrier_code,
               warehouse_code,
               company_id,
               relation_id,
               start_date,
               end_date,
               frequency,
               amount,
               bill_id,
               bill_code,
               remark,
               create_code,
               create_by,
               create_time,
               oper_code,
               oper_by,
               oper_time,
               del_flag,
        DATE_FORMAT(start_date,'%Y-%m') as startDateDesc,
        DATE_FORMAT(end_date,'%Y-%m')as endDateDesc
        from pub_yf_fixedfee <where>
        del_flag = 0
        and carrier_id in
        <foreach collection="carrierIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and client_id in
        <foreach collection="clientIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and rule_type = #{ruleType}
        and IFNULL(bill_id,'')=''
        <choose>
            <when test="ruleType == 1">
                AND company_id  in
                <foreach collection="companyIds" item="item" open="(" separator="," close=")">
                 #{item}
               </foreach>
            </when>
            <when test="ruleType == 2">
                AND warehouse_code in
                <foreach collection="warehouseCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
    </where>
    </select>
    <select id="selectPubYfFixedfeeByIds" resultMap="PubYfFixedfeeResult">
        select id,
               expenses_code,
               rule_id,
               rule_type,
               item_id,
               client_id,
               carrier_id,
               carrier_code,
               warehouse_code,
               company_id,
               relation_id,
               start_date,
               end_date,
               frequency,
               amount,
               bill_id,
               bill_code,
               remark,
               create_code,
               create_by,
               create_time,
               oper_code,
               oper_by,
               oper_time,
               del_flag from pub_yf_fixedfee
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getPubYfFixedFeeByBillIds" resultMap="PubYfFixedfeeResult">
        select id,
               expenses_code,
               rule_id,
               rule_type,
               item_id,
               client_id,
               carrier_id,
               carrier_code,
               warehouse_code,
               company_id,
               relation_id,
               start_date,
               end_date,
               frequency,
               amount,
               bill_id,
               bill_code,
               remark,
               create_code,
               create_by,
               create_time,
               oper_code,
               oper_by,
               oper_time,
               del_flag from pub_yf_fixedfee
        where bill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="pubYfFixedfeeList" resultMap="PubYfFixedfeeDto">
        select
        r.id,
        r.rule_type,
        case when r.rule_type = 1 then '运输'
        when r.rule_type = 2 then '仓储'
        end as ruleTypeName,
        r.warehouse_code,
        r.expenses_code,
        r.bill_id,
        r.bill_code,
        mw.warehouse_name as warehouseName,
        r.company_id,
        r.client_id,
        mc.client_name as clientName,
        r.carrier_id,
        r.carrier_code,
        m.carrier_name as carrierName,
        r.item_id,
        r.start_date,
        r.end_date,
        r.frequency,
        case when r.frequency = 1 then '按月'
        when r.frequency = 2 then '按季'
        end as frequencyName,
        r.amount,
        r.remark,
        r.create_by,
        r.create_code,
        r.create_time,
        r.oper_by,
        r.oper_code,
        r.oper_time
        from
        pub_yf_fixedfee r
        left join
        mdm_warehouseinfo mw
        on r.warehouse_code = mw.warehouse_code
        left join
        bms_clientinfo mc
        on r.client_id = mc.id
        left join
        bms_carrierinfo m
        on r.carrier_id = m.id
        <where>
            r.del_flag =0
            <if test="ruleType != null and ruleType != ''">
                and r.rule_type = #{ruleType}
            </if>
            <if test="clientId != null">
                and r.client_id = #{clientId}
            </if>
            <if test="carrierId != null and carrierId != ''">
                and r.carrier_id = #{carrierId}
            </if>
            <if test="clientName != null and clientName != ''">
                and mc.client_name like concat('%',#{clientName},'%')
            </if>
            <if test="expensesCode != null and expensesCode != ''">
                and r.expenses_code = #{expensesCode}
            </if>
            <if test="itemId != null and itemId != ''">
                and r.item_id = #{itemId}
            </if>
            <if test="beginCreateTime != null and beginCreateTime != '' and endCreateTime != null and endCreateTime != ''">
                and r.create_time BETWEEN #{beginCreateTime} and #{endCreateTime}
            </if>
            <if test="billStatus != null and billStatus == 1">
                AND r.bill_code IS NOT NULL
            </if>
            <if test="billStatus != null and billStatus == 2">
                AND r.bill_code IS NULL
            </if>
            <if test="frequency != null and frequency != ''">
                and r.frequency = #{frequency}
            </if>
        </where>
        order by r.expenses_code desc
    </select>
    <select id="selectYfFixedfeeByids" resultType="com.bbyb.joy.bms.domain.dto.PubYfFixedfee">
        select * from pub_yf_fixedfee where del_flag = 0 and id in <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </select>

    <insert id="insertBatchYfFixedFee">
        insert into pub_yf_fixedfee(id,expenses_code,rule_id,rule_type,item_id,client_id,carrier_id,carrier_code,warehouse_code,company_id,relation_id,start_date,end_date,frequency,amount,bill_id,bill_code,remark,create_code,create_by,create_time,oper_code,oper_by,oper_time,del_flag,adjust_before_amount,adjust_before_remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.expensesCode},#{entity.ruleId},#{entity.ruleType},#{entity.itemId},#{entity.clientId},#{entity.carrierId},#{entity.carrierCode},#{entity.warehouseCode},#{entity.companyId},#{entity.relationId},#{entity.startDate},#{entity.endDate},#{entity.frequency},#{entity.amount},#{entity.billId},#{entity.billCode},#{entity.remark},#{entity.createCode},#{entity.createBy},#{entity.createTime},#{entity.operCode},#{entity.operBy},#{entity.operTime},#{entity.delFlag},#{entity.adjustBeforeAmount},#{entity.adjustBeforeRemark})
        </foreach>
    </insert>
</mapper>
