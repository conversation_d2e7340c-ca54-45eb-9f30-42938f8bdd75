<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubSyslogicsetMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.PubSyslogicset" id="PubSyslogicsetResult">
        <result property="id"    column="id"    />
        <result property="logicName"    column="logic_name"    />
        <result property="logicValue"    column="logic_value"    />
        <result property="remark"    column="remark"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectPubSyslogicsetVo">
        select id, logic_name, logic_value, remark, oper_code, oper_by, oper_dept_id, oper_time, del_flag from pub_syslogicset
    </sql>

    <select id="selectPubSyslogicsetList" parameterType="com.bbyb.joy.bms.domain.dto.PubSyslogicset" resultMap="PubSyslogicsetResult">
        <include refid="selectPubSyslogicsetVo"/>
        <where>
            <if test="logicName != null  and logicName != ''"> and logic_name = #{logicName}</if>
            <if test="logicValue != null  and logicValue != ''"> and logic_value = #{logicValue}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operDeptId != null "> and oper_dept_id = #{operDeptId}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
        </where>
    </select>

    <select id="selectPubSyslogicsetListBylogicName"  resultMap="PubSyslogicsetResult">
        <include refid="selectPubSyslogicsetVo"/>
        <where>
            del_flag=0 and logic_name in
            <foreach item="name" collection="array" open="(" separator="," close=")">
                #{name}
            </foreach>
        </where>
    </select>

    <select id="selectPubSyslogicsetById" parameterType="java.lang.String" resultMap="PubSyslogicsetResult">
        <include refid="selectPubSyslogicsetVo"/>
        where id = #{id}
    </select>

    <insert id="insertPubSyslogicset" parameterType="com.bbyb.joy.bms.domain.dto.PubSyslogicset" useGeneratedKeys="true" keyProperty="id">
        insert into pub_syslogicset
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="logicName != null and logicName != ''">logic_name,</if>
            <if test="logicValue != null">logic_value,</if>
            <if test="remark != null">remark,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="logicName != null and logicName != ''">#{logicName},</if>
            <if test="logicValue != null">#{logicValue},</if>
            <if test="remark != null">#{remark},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updatePubSyslogicset" parameterType="com.bbyb.joy.bms.domain.dto.PubSyslogicset">
        update pub_syslogicset
        <trim prefix="SET" suffixOverrides=",">
            <if test="logicName != null and logicName != ''">logic_name = #{logicName},</if>
            <if test="logicValue != null">logic_value = #{logicValue},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePubSyslogicsetById" parameterType="java.lang.String">
        delete from pub_syslogicset where id = #{id}
    </delete>

    <delete id="deletePubSyslogicsetByIds">
        delete from pub_syslogicset where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updatePubSyslogicsetStatusByIds">
        update pub_syslogicset set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


<!--    应收应付结算日期设置 -->
    <resultMap type="com.bbyb.joy.bms.domain.dto.PubSettledateSetting" id="PubSettledateSettingResult">
        <result property="id"    column="id"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="billType"    column="bill_type"    />
        <result property="dateType"    column="date_type"    />
    </resultMap>

    <select id="selectSettledateSetting" resultMap="PubSettledateSettingResult">
        select  id,client_id,client_code,bill_type,date_type from pub_settledate_setting where bill_type = #{billType}
    </select>
</mapper>