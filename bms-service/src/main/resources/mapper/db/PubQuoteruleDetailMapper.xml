<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubQuoteruleDetailMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.PubQuoteruleDetail" id="PubQuoteruleDetailResult">
        <result property="id"    column="id"    />
        <result property="quoteruleId"    column="quoterule_id"    />
        <result property="rulecode"    column="rulecode"    />
        <result property="feeType"    column="fee_type"    />
        <result property="quoteruleTemplateId"    column="quoterule_template_id"    />
        <result property="isEnable"    column="is_enable"    />
        <result property="remark"    column="remark"    />
        <result property="updateBy"    column="oper_by"    />
        <result property="operCode"    column="oper_code"    />
        <result property="updateTime"    column="oper_time"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="auditState"    column="audit_state"    />
        <result property="auditUser"    column="audit_user"    />
        <result property="auditUserName"    column="audit_user_name"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="offerCode"    column="offerCode"    />
        <result property="ruleName"    column="ruleName"    />
        <result property="ruleTemplate"    column="ruleTemplate"    />
        <result property="clientName"    column="clientName"    />
        <result property="carrierName"    column="carrierName"    />
        <result property="companyId"    column="companyId"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="billType"    column="bill_type"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="warningTime"    column="warning_time"    />
        <result property="feeTypeStr"    column="fee_type_str"    />
        <result property="itemId" column="item_id" jdbcType="INTEGER"/>
        <result property="isCalculated" column="is_calculated" />
        <result property="clientId" column="client_id" />
        <result property="businessType" column="businessType" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="selectPubQuoteruleDetailVo">
        SELECT
            qd.id,
            qd.quoterule_id,
            qd.quoterule_template_id,
            qd.rulecode,
            q.rule_code AS offerCode,
            q.rule_name AS ruleName,
            qt.rule_name AS ruleTemplate,
            qd.is_enable,
            qd.audit_state,
            qd.fee_type,
            ( CASE q.rule_type WHEN 1 THEN c.client_name END ) AS clientName,
            ( CASE q.rule_type WHEN 2 THEN cr.carrier_name END ) AS carrierName,
            qd.oper_dept_id AS companyId,
            qd.remark,
            qd.oper_by,
            qd.oper_time,
            qd.remark bjRemark,
            qd.oper_by operBy,
            qd.oper_time operTime,
            qd.bill_type,
            -- case when qd.rule_type = 1 then qd.start_time  else  q.start_time  end start_time,
            -- case when qd.rule_type = 1 then qd.end_time  else  q.end_time  end   end_time,
            qd.start_time as start_time,
            qd.end_time as end_time,
<!--            case when qd.rule_type = 1 then qd.warning_time  else  q.warning_time  end  warning_time,-->
            qd.warning_time as warning_time,
            case when  q.rule_type = 1 then c.client_name else  cr.carrier_name end clientOrCarrieName,
            qd.audit_remark,
            qd.audit_user_name,
            qd.audit_time,
            qd.fee_type_str,
            qd.item_id,
            qd.is_calculated,
            qd.client_id,
            q.business_type AS businessType,
            qd.extra_field
        FROM
            pub_quoterule_detail qd
            LEFT JOIN pub_quoterule q ON q.id = qd.quoterule_id
            AND q.del_flag = 0
            LEFT JOIN pub_quoterule_template qt ON qt.id = qd.quoterule_template_id
            AND qt.del_flag = 0
            LEFT JOIN bms_clientinfo c ON q.relation_id = c.id
            AND c.del_flag = 0
            LEFT JOIN bms_carrierinfo cr ON q.relation_id = cr.id
            AND cr.del_flag = 0
	</sql>

    <select id="selectPubQuoteruleDetailList" parameterType="java.util.Map" resultMap="PubQuoteruleDetailResult">
        <include refid="selectPubQuoteruleDetailVo"/>
            WHERE qd.del_flag=0
         <if test="ruleType != null and ruleType != ''"> and qd.rule_type = #{ruleType}</if>
            <if test="beginOperTime != null and beginOperTime != ''"><!-- 开始时间检索 -->
                and qd.oper_time &gt;= #{beginOperTime,jdbcType=VARCHAR}
            </if>
            <if test="endOperTime != null and endOperTime != ''"><!-- 结束时间检索 -->
                and qd.oper_time &lt;=  #{endOperTime,jdbcType=VARCHAR}
            </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="carrierList != null">
            and  cr.carrier_code in
            <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                #{carrier}
            </foreach>
        </if>
        <if test="idList != null">
            and  qd.id in
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
            <if test="offerCode != null and offerCode != '' "> and q.rule_code like concat('%',#{offerCode},'%')</if>
            <if test="ruleTemplate != null  and ruleTemplate != ''"> and qt.rule_name = #{ruleTemplate}</if>
            <if test="clientName !=null and clientName !=''"> and (CASE q.rule_type WHEN 1 THEN c.client_name END) like concat('%',#{clientName},'%')</if>
            <if test="companyIds != null and companyIds.size > 0"> and qd.oper_dept_id in
                <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                    #{companyIds}
                </foreach>
            </if>
            <if test="rulecode != null and rulecode !='' "> and qd.rulecode like concat('%',#{rulecode},'%')</if>
            <if test="carrierName !=null and carrierName !=''"> and (CASE q.rule_type WHEN 2 THEN cr.carrier_name END) like concat('%',#{carrierName},'%')</if>
            <if test="feeType != null "> and fee_type = #{feeType}</if>
        <if test="auditState != null "> and  qd.audit_state = #{auditState}</if>
        <if test="billType != null "> and  qd.bill_type = #{billType}</if>
        <if test="auditUserName != null and auditUserName !='' "> and qd.audit_user_name like concat('%',#{auditUserName},'%')</if>
        <if test="operBy != null and operBy !='' "> and qd.oper_by like concat('%',#{operBy},'%')</if>
        <if test="feeTypeStr != null and feeTypeStr !='' "> and qd.fee_type_str like concat('%',#{feeTypeStr},'%')</if>
        <if test="htState !=null and htState !=''">
            <if test="htState == 1">
                and now() between qd.start_time and qd.warning_time
            </if>
            <if test="htState == 3">
                and now() > qd.end_time
            </if>
            <if test="htState == 2">
                and now() between qd.warning_time and qd.end_time
            </if>
        </if>

            GROUP BY qd.id
            ORDER BY qd.oper_time desc
    </select>

    <select id="selectPubQuoteruleDetailById" parameterType="String" resultMap="PubQuoteruleDetailResult">
        <include refid="selectPubQuoteruleDetailVo"/>
        where qd.id = #{id}
    </select>

    <update id="auditPubQuoteruleDetail" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoteruleDetail">
        update pub_quoterule_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="auditState != null "> audit_state=#{auditState},</if>
            <if test="auditUser != null "> audit_user=#{auditUser},</if>
            <if test="auditUserName != null "> audit_user_name=#{auditUserName},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            audit_time = sysdate(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePubQuoteruleDetailByIds" >
        update pub_quoterule_detail
        set del_flag= 1
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="updateAuditState" >
        update pub_quoterule_detail
        set audit_state= 1,audit_user = '',audit_user_name = '',audit_remark = '',audit_time = null
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="insertPubQuoteruleDetail" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoteruleDetail" >
        insert into pub_quoterule_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="quoteruleId != null">quoterule_id,</if>
            <if test="rulecode != null">rulecode,</if>
            <if test="feeType != null">fee_type,</if>
            <if test="quoteruleTemplateId != null">quoterule_template_id,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="remark != null">remark,</if>
            <if test="updateBy != null">oper_by,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="updateTime != null">oper_time,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="auditUser != null">audit_user,</if>
            <if test="auditUserName != null">audit_user_name,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="billType != null">bill_type,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="warningTime != null">warning_time,</if>
            <if test="feeTypeStr != null">fee_type_str,</if>
            <if test="itemId != null">item_id,</if>
            <if test="isCalculated != null">is_calculated,</if>
            id,
            del_flag,
            <if test="clientId != null">client_id,</if>
            <if test="extraField != null">extra_field,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="quoteruleId != null">#{quoteruleId},</if>
            <if test="rulecode != null">#{rulecode},</if>
            <if test="feeType != null">#{feeType},</if>
            <if test="quoteruleTemplateId != null">#{quoteruleTemplateId},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="remark != null">#{remark},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="auditUser != null">#{auditUser},</if>
            <if test="auditUserName != null">#{auditUserName},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="billType != null">#{billType},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="warningTime != null">#{warningTime},</if>
            <if test="feeTypeStr != null">#{feeTypeStr},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="isCalculated != null">#{isCalculated},</if>
            #{id},
            '0',
            <if test="clientId != null">#{clientId},</if>
            <if test="extraField != null">#{extraField},</if>
        </trim>
    </insert>

    <update id="updatePubQuoteruleDetail" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoteruleDetail">
        update pub_quoterule_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="quoteruleId != null">quoterule_id = #{quoteruleId},</if>
            <if test="rulecode != null">rulecode = #{rulecode},</if>
            <if test="feeType != null">fee_type = #{feeType},</if>
            <if test="quoteruleTemplateId != null">quoterule_template_id = #{quoteruleTemplateId},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">oper_by = #{updateBy},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="auditState != null">audit_state = #{auditState},</if>
            <if test="auditUser != null">audit_user = #{auditUser},</if>
            <if test="auditUserName != null">audit_user_name = #{auditUserName},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="ruleType != null">rule_type = #{ruleType},</if>
            <if test="billType != null">bill_type = #{billType},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="warningTime != null">warning_time = #{warningTime},</if>
            <if test="feeTypeStr != null">fee_type_str = #{feeTypeStr},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="isCalculated != null">is_calculated = #{isCalculated},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="extraField != null">extra_field=#{extraField},</if>
            oper_time = sysdate()

        </trim>
        where id = #{id}
    </update>


    <select id="checkRuleDetailCodeUnique" parameterType="String" resultMap="PubQuoteruleDetailResult">
        select id from pub_quoterule_detail
        where del_flag = '0' and  rulecode=#{ruleCode} limit 1
    </select>

    <update id="updatePubQuoteruleByIds" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoteruleDetail">
        update pub_quoterule_detail
        set is_enable= #{isEnable}
        where id  =   #{id}
    </update>

    <select id="selectByid" resultMap="PubQuoteruleDetailResult">
        select  id,rulecode ,audit_state
        from pub_quoterule_detail where del_flag = '0' and is_enable = '0'
        <if test="ids != null and ids.size>0 ">
            and  id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
    </select>

    <select id="getQuoteruleTemplate" resultType="java.lang.Integer">
        select extra_charging from pub_quoterule_template where del_flag = '0' and is_enable = '0' and id = #{id}
    </select>

</mapper>