<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYsbillOperinfoMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillOperinfo" id="BmsYsbillOperinfoResult">
        <result property="id"    column="id"    />
        <result property="billid"    column="billid"    />
        <result property="userCode"    column="user_code"    />
        <result property="userName"    column="user_name"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="operNode"    column="oper_node"    />
        <result property="operContent"    column="oper_content"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectBmsYsbillOperinfoVo">
        select id, billid, user_code, user_name, oper_dept_id, oper_time, oper_node, oper_content, del_flag from bms_ysbill_operinfo
    </sql>

    <select id="selectBmsYsbillOperinfoList" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillOperinfo" resultMap="BmsYsbillOperinfoResult">
        <include refid="selectBmsYsbillOperinfoVo"/>
        <where>  
            <if test="billid != null "> and billid = #{billid}</if>
            <if test="userCode != null  and userCode != ''"> and user_code = #{userCode}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="operDeptId != null "> and oper_dept_id = #{operDeptId}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
            <if test="operNode != null  and operNode != ''"> and oper_node = #{operNode}</if>
            <if test="operContent != null  and operContent != ''"> and oper_content = #{operContent}</if>
            <if test="delFlag != null  and delFlag != ''"> and del_flag = #{delFlag}</if>
        </where>
        order by oper_time
    </select>
    
    <select id="selectBmsYsbillOperinfoById" parameterType="java.lang.String" resultMap="BmsYsbillOperinfoResult">
        <include refid="selectBmsYsbillOperinfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBmsYsbillOperinfo" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillOperinfo" useGeneratedKeys="true" keyProperty="id">
        insert into bms_ysbill_operinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billid != null">billid,</if>
            <if test="userCode != null">user_code,</if>
            <if test="userName != null">user_name,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="operNode != null">oper_node,</if>
            <if test="operContent != null">oper_content,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billid != null">#{billid},</if>
            <if test="userCode != null">#{userCode},</if>
            <if test="userName != null">#{userName},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="operNode != null">#{operNode},</if>
            <if test="operContent != null">#{operContent},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateBmsYsbillOperinfo" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillOperinfo">
        update bms_ysbill_operinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="billid != null">billid = #{billid},</if>
            <if test="userCode != null">user_code = #{userCode},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="operNode != null">oper_node = #{operNode},</if>
            <if test="operContent != null">oper_content = #{operContent},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYsbillOperinfoById" parameterType="java.lang.String">
        delete from bms_ysbill_operinfo where id = #{id}
    </delete>

    <delete id="deleteBmsYsbillOperinfoByIds">
        delete from bms_ysbill_operinfo where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYsbillOperinfoStatusByIds">
        update bms_ysbill_operinfo set status = #{status} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <update id="updateBmsYsbillOperinfoByBillNode" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillOperinfo">
        update bms_ysbill_operinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="userCode != null">user_code = #{userCode},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            del_flag=1
        </trim>
        where del_flag=0
        <if test="billid!=null">
            and billid = #{billid}
        </if>
        <if test="billIds!=null and billIds.size>0">
            and billid in
            <foreach item="item" collection="billIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="operNode!=null and operNode!=''">
            and oper_node=#{operNode}
        </if>
        <if test="operNodes!=null and operNodes.size>0">
            and oper_node in
            <foreach item="item" collection="operNodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>