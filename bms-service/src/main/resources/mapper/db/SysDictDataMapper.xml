<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="mapper.db.SysDictDataMapper">
    <resultMap type="com.bbyb.joy.bms.domain.dto.entity.SysDictData" id="SysDictDataResult">
        <id     property="dictCode"   column="dict_code"   />
        <result property="dictSort"   column="dict_sort"   />
        <result property="dictLabel"  column="dict_label"  />
        <result property="dictValue"  column="dict_value"  />
        <result property="dictType"   column="dict_type"   />
        <result property="cssClass"   column="css_class"   />
        <result property="listClass"  column="list_class"  />
        <result property="isDefault"  column="is_default"  />
        <result property="status"     column="status"      />
        <result property="createBy"   column="create_by"   />
        <result property="createTime" column="create_time" />
        <result property="updateBy"   column="update_by"   />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--开启二级缓存-->
<!--    <cache-->
<!--            type="com.bbyb.bms.common.core.cache.MybatisRedisCache"-->
<!--            eviction="LRU"-->
<!--            flushInterval="3600"-->
<!--            size="1024"-->
<!--            readOnly="false"-->
<!--    />-->


    <sql id="selectDictDataVo">
        select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark
        from sys_dict_data
    </sql>

    <select id="selectDictDataList" parameterType="com.bbyb.joy.bms.domain.dto.entity.SysDictData" resultMap="SysDictDataResult">
        <include refid="selectDictDataVo"/>
        <where>
            <if test="dictType != null and dictType != ''">
                AND dict_type = #{dictType}
            </if>
            <if test="dictLabel != null and dictLabel != ''">
                AND dict_label like concat('%', #{dictLabel}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="dictValue != null and dictValue != ''">
                AND dict_value = #{dictValue}
            </if>
        </where>
        order by dict_sort asc
    </select>

    <select id="selectDictDataByType" parameterType="com.bbyb.joy.bms.domain.dto.entity.SysDictData" resultMap="SysDictDataResult">
        <include refid="selectDictDataVo"/>
        where status = '0' and dict_type = #{dictType} order by dict_sort asc
    </select>

    <select id="selectDictLabel" resultType="java.lang.String">
        select dict_label from sys_dict_data
        where dict_type = #{dictType} and dict_value = #{dictValue}
    </select>

    <select id="selectDictDataById" parameterType="java.lang.Long" resultMap="SysDictDataResult">
        <include refid="selectDictDataVo"/>
        where dict_code = #{dictCode}
    </select>

    <select id="countDictDataByType" resultType="java.lang.Integer">
        select count(1) from sys_dict_data where dict_type=#{dictType}
    </select>

    <delete id="deleteDictDataById" parameterType="java.lang.Long">
        delete from sys_dict_data where dict_code = #{dictCode}
    </delete>

    <delete id="deleteDictDataByIds" parameterType="java.lang.Long">
        delete from sys_dict_data where dict_code in
        <foreach collection="array" item="dictCode" open="(" separator="," close=")">
            #{dictCode}
        </foreach>
    </delete>

    <update id="updateDictData" parameterType="com.bbyb.joy.bms.domain.dto.entity.SysDictData">
        update sys_dict_data
        <set>
            <if test="dictSort != null">dict_sort = #{dictSort},</if>
            <if test="dictLabel != null and dictLabel != ''">dict_label = #{dictLabel},</if>
            <if test="dictValue != null and dictValue != ''">dict_value = #{dictValue},</if>
            <if test="dictType != null and dictType != ''">dict_type = #{dictType},</if>
            <if test="cssClass != null">css_class = #{cssClass},</if>
            <if test="listClass != null">list_class = #{listClass},</if>
            <if test="isDefault != null and isDefault != ''">is_default = #{isDefault},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where dict_code = #{dictCode}
    </update>

    <update id="updateDictDataType" parameterType="java.lang.String">
        update sys_dict_data set dict_type = #{newDictType} where dict_type = #{oldDictType}
    </update>

    <insert id="insertDictData" parameterType="com.bbyb.joy.bms.domain.dto.entity.SysDictData">
        insert into sys_dict_data(
        <if test="dictSort != null">dict_sort,</if>
        <if test="dictLabel != null and dictLabel != ''">dict_label,</if>
        <if test="dictValue != null and dictValue != ''">dict_value,</if>
        <if test="dictType != null and dictType != ''">dict_type,</if>
        <if test="cssClass != null and cssClass != ''">css_class,</if>
        <if test="listClass != null and listClass != ''">list_class,</if>
        <if test="isDefault != null and isDefault != ''">is_default,</if>
        <if test="status != null">status,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="dictSort != null">#{dictSort},</if>
        <if test="dictLabel != null and dictLabel != ''">#{dictLabel},</if>
        <if test="dictValue != null and dictValue != ''">#{dictValue},</if>
        <if test="dictType != null and dictType != ''">#{dictType},</if>
        <if test="cssClass != null and cssClass != ''">#{cssClass},</if>
        <if test="listClass != null and listClass != ''">#{listClass},</if>
        <if test="isDefault != null and isDefault != ''">#{isDefault},</if>
        <if test="status != null">#{status},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <insert id="insertSelective" parameterType="java.util.Map" useGeneratedKeys="true" keyProperty="id" keyColumn="ID" >
        insert into pub_idreplace
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                ID,
            </if>
            <if test="typename != null" >
                TYPENAME,
            </if>
            <if test="itemname != null" >
                ITEMNAME,
            </if>
            <if test="value != null" >
                VALUE,
            </if>
            <if test="orderid != null" >
                ORDERID,
            </if>
            <if test="systype != null" >
                SYSTYPE,
            </if>
            <if test="allowedit != null" >
                ALLOWEDIT,
            </if>
            <if test="remark != null" >
                REMARK,
            </if>
            <if test="invalid != null" >
                Invalid,
            </if>
            <if test="guid != null" >
                guid,
            </if>
            <if test="parameter1 != null" >
                Parameter1,
            </if>
            <if test="parameter2 != null" >
                Parameter2,
            </if>
            <if test="parameter3 != null" >
                Parameter3,
            </if>
            <if test="parameter4 != null" >
                Parameter4,
            </if>
            <if test="parameter5 != null" >
                Parameter5,
            </if>
            <if test="parameter6 != null" >
                Parameter6,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="typename != null" >
                #{typename,jdbcType=VARCHAR},
            </if>
            <if test="itemname != null" >
                #{itemname,jdbcType=VARCHAR},
            </if>
            <if test="value != null" >
                #{value,jdbcType=SMALLINT},
            </if>
            <if test="orderid != null" >
                #{orderid,jdbcType=SMALLINT},
            </if>
            <if test="systype != null" >
                #{systype,jdbcType=VARCHAR},
            </if>
            <if test="allowedit != null" >
                #{allowedit,jdbcType=SMALLINT},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="invalid != null" >
                #{invalid,jdbcType=SMALLINT},
            </if>
            <if test="guid != null" >
                #{guid,jdbcType=VARCHAR},
            </if>
            <if test="parameter1 != null" >
                #{parameter1,jdbcType=VARCHAR},
            </if>
            <if test="parameter2 != null" >
                #{parameter2,jdbcType=VARCHAR},
            </if>
            <if test="parameter3 != null" >
                #{parameter3,jdbcType=VARCHAR},
            </if>
            <if test="parameter4 != null" >
                #{parameter4,jdbcType=VARCHAR},
            </if>
            <if test="parameter5 != null" >
                #{parameter5,jdbcType=VARCHAR},
            </if>
            <if test="parameter6 != null" >
                #{parameter6,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>