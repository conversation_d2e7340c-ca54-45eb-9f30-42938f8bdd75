<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubYsElectronicContractMapper">

    <select id="queryYsElectronicContractById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pub_ys_electronic_contract
        where deleted=0
        <if test="id !=null and id !=''">
            and id=#{id}
        </if>
        <if test="orderId !=null and orderId !=''">
            and order_id=#{orderId}
        </if>
        <if test="fileId !=null and fileId !=''">
            and file_id=#{fileId}
        </if>
        <if test="signFlowId !=null and signFlowId !=''">
            and sign_flow_id=#{signFlowId}
        </if>
    </select>

    <insert id="insertYsElectronicContract" parameterType="com.bbyb.joy.bms.domain.dto.PubYsElectronicContract" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO pub_ys_electronic_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="fileId != null">file_id,</if>
            <if test="signingStatus != null">signing_status,</if>
            <if test="signingFailReason != null">signing_fail_reason,</if>
            <if test="signingDate != null">signing_date,</if>
            <if test="contractGenerationDate != null">contract_generation_date,</if>
            <if test="pdfAddress != null">pdf_address,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileTotal != null">file_total,</if>
            <if test="signFlowId != null">sign_flow_id,</if>
            <if test="signContent != null">sign_content,</if>
            <if test="signPosition != null">sign_position,</if>
            <if test="sealType != null">seal_type,</if>
            <if test="sealId != null">seal_id,</if>
            created_by, created_time
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="signingStatus != null">#{signingStatus},</if>
            <if test="signingFailReason != null">#{signingFailReason},</if>
            <if test="signingDate != null">#{signingDate},</if>
            <if test="contractGenerationDate != null">#{contractGenerationDate},</if>
            <if test="pdfAddress != null">#{pdfAddress},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileTotal != null">#{fileTotal},</if>
            <if test="signFlowId != null">#{signFlowId},</if>
            <if test="signContent != null">#{signContent},</if>
            <if test="signPosition != null">#{signPosition},</if>
            <if test="sealType != null">#{sealType},</if>
            <if test="sealId != null">#{sealId},</if>
            #{createdBy}, #{createdTime}
        </trim>
    </insert>

    <update id="modifyYsElectronicContract" parameterType="com.bbyb.joy.bms.domain.dto.PubYsElectronicContract">
        UPDATE pub_ys_electronic_contract
        <set>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="signingStatus != null">signing_status = #{signingStatus},</if>
            <if test="signingFailReason != null">signing_fail_reason = #{signingFailReason},</if>
            <if test="signingDate != null">signing_date = #{signingDate},</if>
            <if test="contractGenerationDate != null">contract_generation_date = #{contractGenerationDate},</if>
            <if test="pdfAddress != null">pdf_address = #{pdfAddress},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileTotal != null">file_total = #{fileTotal},</if>
            <if test="signFlowId != null">sign_flow_id = #{signFlowId},</if>
            <if test="signContent != null">sign_content = #{signContent},</if>
            <if test="signPosition != null">sign_position = #{signPosition},</if>
            <if test="sealType != null">seal_type = #{sealType},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="sealId != null">seal_id = #{sealId},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="delYsElectronicContractByOrderId">
        UPDATE pub_ys_electronic_contract
        set deleted=1
        where order_id in
        <foreach collection="orderId" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
          and deleted = 0
    </update>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bbyb.joy.bms.domain.dto.PubYsElectronicContract">
        <id column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="order_id" property="orderId"/>
        <result column="file_id" property="fileId"/>
        <result column="signing_status" property="signingStatus"/>
        <result column="signing_fail_reason" property="signingFailReason"/>
        <result column="signing_date" property="signingDate"/>
        <result column="contract_generation_date" property="contractGenerationDate"/>
        <result column="pdf_address" property="pdfAddress"/>
        <result column="file_name" property="fileName"/>
        <result column="file_size" property="fileSize"/>
        <result column="file_total" property="fileTotal"/>
        <result column="sign_flow_id" property="signFlowId"/>
        <result column="sign_content" property="signContent"/>
        <result column="sign_position" property="signPosition"/>
        <result column="seal_type" property="sealType"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="seal_id" property="sealId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, file_id, signing_status, signing_fail_reason, signing_date, contract_generation_date, pdf_address, file_name, file_size,
        file_total, sign_flow_id, sign_content, sign_position, seal_type, created_by, created_time, updated_by, updated_time, deleted,seal_id
    </sql>

    <select id="queryListFilterStatus" resultMap="BaseResultMap" >
        select <include refid="Base_Column_List"/> from  pub_ys_electronic_contract
         where signing_status=#{status} and deleted=0  limit 30
    </select>

</mapper>