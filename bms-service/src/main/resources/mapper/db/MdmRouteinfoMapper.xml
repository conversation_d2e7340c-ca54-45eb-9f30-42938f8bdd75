<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.MdmRouteinfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.MdmRouteinfo" id="MdmRouteinfoResult">
        <result property="id"    column="id"    />
        <result property="routeCode"    column="route_code"    />
        <result property="routeName"    column="route_name"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseProvince"    column="warehouse_province"    />
        <result property="warehouseCity"    column="warehouse_city"    />
        <result property="warehouseArea"    column="warehouse_area"    />
        <result property="routeType"    column="route_type"    />
        <result property="transportModel"    column="transport_model"    />
        <result property="totalMileage"    column="total_mileage"    />
        <result property="storeNum"    column="store_num"    />
        <result property="companyId"    column="company_id"    />
        <result property="isenable"    column="is_enable"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateuserCode"    column="updateuser_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="deptCode"    column="dept_code"    />
        <result property="relationshipType"    column="relationship_type"    />
        <result property="relationshipCode"    column="relationship_code"    />
    </resultMap>

    <sql id="selectMdmRouteinfoVo">
        SELECT
            r.id,
            r.route_code,
            r.route_name,
            r.company_id,
            r.relationship_type,
            case when r.relationship_type = 0 then c.client_name else cr.carrier_name end as ClientName,
            r.route_type,
            r.warehouse_code,
            r.is_enable,
	        CONCAT(r.warehouse_province,r.warehouse_city,r.warehouse_area) AS Area,
	        r.remark,
            r.store_num,
            r.total_mileage,
            r.oper_by,
            r.oper_time,
            r.del_flag
            FROM
            bms_routeinfo r
            LEFT JOIN bms_carrierinfo	cr ON r.relationship_code = cr.carrier_code
            AND cr.del_flag=0
            LEFT JOIN bms_clientinfo c ON r.relationship_code = c.client_code
            AND c.del_flag=0
    </sql>

    <select id="selectMdmRouteinfoList" parameterType="com.bbyb.joy.bms.domain.dto.MdmRouteinfo" resultMap="MdmRouteinfoResult">
        <include refid="selectMdmRouteinfoVo"/>
        where r.del_flag='0'
        <if test="companyIds != null and companyIds.length>0 ">
            and r.company_id in
            <foreach collection="companyIds" item="item" open="(" separator="," close=")" >
                #{item}
            </foreach>
        </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="carrierList != null">
            and  cr.carrier_code in
            <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                #{carrier}
            </foreach>
        </if>
        <if test="routeName != null  and routeName != ''">
            and r.route_name like concat('%', #{routeName}, '%')
        </if>
        <if test="routeCode != null and routeCode != ''">
            and r.route_code like concat('%',#{routeCode},'%')
        </if>
        <if test="routeType != null ">
            and r.route_type = #{routeType}
        </if>
        <if test="relationshipType!=null">
            and r.relationship_type = #{relationshipType}
        </if>
        GROUP BY r.id
    </select>

    <select id="selectMdmRouteinfoById" parameterType="java.lang.String" resultMap="MdmRouteinfoResult">
        <include refid="selectMdmRouteinfoVo"/>
        where r.id = #{id}
    </select>

    <update id="updateMdmRouteinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmRouteinfo">
        update bms_routeinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeNum != null">store_num = #{storeNum},</if>
            <if test="totalMileage != null">total_mileage = #{totalMileage},</if>
            <if test="isenable != null">is_enable = #{isenable},</if>
            <if test="updateBy != null and updateBy != ''">oper_by = #{updateBy},</if>
            oper_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <select id="selectRouteinfoList" resultMap="MdmRouteinfoResult">
        select
        r.id,
        r.route_code,
        r.route_name,
        r.company_id,
        r.route_type,
        r.warehouse_code,
        r.is_enable,
        r.remark,
        r.store_num,
        r.total_mileage,
        r.oper_by,
        r.oper_time,
        r.del_flag
        FROM
        bms_routeinfo r
        where r.del_flag=0
        <if test="ids != null and ids.size>0 ">
            AND r.id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
        <if test="codes != null and codes.size>0 ">
            AND r.route_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <select id="checkLineCodeUnique" parameterType="java.lang.String" resultMap="MdmRouteinfoResult">
        select id, route_code from bms_routeinfo where del_flag = 0 and route_code=#{routeCode} limit 1
    </select>

    <insert id="insertMdmRouteinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmRouteinfo" useGeneratedKeys="true" keyProperty="id">
        insert into bms_routeinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="routeCode != null">route_code,</if>
            <if test="routeName != null">route_name,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseProvince != null">warehouse_province,</if>
            <if test="warehouseCity != null">warehouse_city,</if>
            <if test="warehouseArea != null">warehouse_area,</if>
            <if test="routeType != null">route_type,</if>
            <if test="transportModel != null">transport_model,</if>
            <if test="totalMileage != null">total_mileage,</if>
            <if test="storeNum != null">store_num,</if>
            <if test="companyId != null">company_id,</if>
            <if test="isenable != null">is_enable,</if>
            <if test="remark != null">remark,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="deptCode != null">dept_code,</if>
            <if test="relationshipType != null">relationship_type,</if>
            <if test="relationshipCode != null">relationship_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="routeCode != null">#{routeCode},</if>
            <if test="routeName != null">#{routeName},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseProvince != null">#{warehouseProvince},</if>
            <if test="warehouseCity != null">#{warehouseCity},</if>
            <if test="warehouseArea != null">#{warehouseArea},</if>
            <if test="routeType != null">#{routeType},</if>
            <if test="transportModel != null">#{transportModel},</if>
            <if test="totalMileage != null">#{totalMileage},</if>
            <if test="storeNum != null">#{storeNum},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="isenable != null">#{isenable},</if>
            <if test="remark != null">#{remark},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="deptCode != null">#{deptCode},</if>
            <if test="relationshipType != null">#{relationshipType},</if>
            <if test="relationshipCode != null">#{relationshipCode},</if>
        </trim>
    </insert>

    <update id="updateMdmRouteinfoApi" parameterType="com.bbyb.joy.bms.domain.dto.MdmRouteinfo">
        update bms_routeinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="routeCode != null">route_code = #{routeCode},</if>
            <if test="routeName != null">route_name = #{routeName},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseProvince != null">warehouse_province = #{warehouseProvince},</if>
            <if test="warehouseCity != null">warehouse_city = #{warehouseCity},</if>
            <if test="warehouseArea != null">warehouse_area = #{warehouseArea},</if>
            <if test="routeType != null">route_type = #{routeType},</if>
            <if test="transportModel != null">transport_model = #{transportModel},</if>
            <if test="totalMileage != null">total_mileage = #{totalMileage},</if>
            <if test="storeNum != null">store_num = #{storeNum},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="isenable != null">is_enable = #{isenable},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">oper_by = #{updateBy},</if>
            <if test="updateTime != null">oper_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="deptCode != null">dept_code = #{deptCode},</if>
            <if test="relationshipType != null">relationship_type = #{relationshipType},</if>
            <if test="relationshipCode != null">relationship_code = #{relationshipCode},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>