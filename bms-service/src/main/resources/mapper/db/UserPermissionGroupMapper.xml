<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.UserPermissionGroupMapper">


    <insert id="batchUserPermissionGroup">
        insert into sys_permissions_group_user(data_group_id, user_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.permissionGroupId},#{item.userId})
        </foreach>
    </insert>

    <delete id="deleteUserPermissionsByUserId" parameterType="java.lang.Long">
        delete from sys_permissions_group_user where user_id=#{userId}
    </delete>

    <select id="selectUserPermissionsGroupId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT
            spg.id
        FROM
            sys_permissions_group_user supg
                left JOIN sys_permissions_group spg on spg.id = supg.data_group_id
        where supg.del_flag = '0'  and spg.del_flag = '0' and spg.status = '0' and supg.user_id = #{userId}
    </select>

</mapper>