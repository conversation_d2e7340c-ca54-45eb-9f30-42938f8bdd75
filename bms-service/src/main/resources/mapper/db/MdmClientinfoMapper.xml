<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.MdmClientinfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.MdmClientinfo" id="MdmClientinfoResult">
        <result property="id"    column="id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="costDateDimension"    column="cost_date_dimension"    />
        <result property="brandName"    column="brand_name"    />
        <result property="carrierpayType"    column="carrierpay_type"    />
        <result property="isGroup"    column="is_group"    />
        <result property="companyId"    column="company_id"    />
        <result property="userCompanyId"    column="user_company_id"    />
        <result property="remark"    column="remark"    />
        <result property="updateUserCode"    column="update_user_code"    />
        <result property="updateUserName"    column="update_user_name"    />
        <result property="updateUserCompanyId"    column="update_user_company_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="businessType"    column="business_type"    />
        <result property="cooperateStart"    column="cooperate_start"    />
        <result property="cooperateEnd"    column="cooperate_end"    />
        <result property="isEnable"    column="is_enable"    />
        <result property="clientLevel"    column="client_level"    />
        <result property="paymentDays"    column="payment_days"    />
        <result property="collectionDays"    column="collection_days"    />
        <result property="cooperateType"    column="cooperate_type"    />
        <result property="lateFee"    column="late_fee"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="openingBank"    column="opening_bank"    />
        <result property="cardNumber"    column="card_number"    />
        <result property="openingName"    column="opening_name"    />
        <result property="linkPhone"    column="link_phone"    />
        <result property="taxpayerNum"    column="taxpayer_num"    />
        <result property="tacAddress"    column="tac_address"    />
        <result property="taxRate"    column="tax_rate"    />
        <result property="checkName"    column="check_name"    />
        <result property="network"    column="network"    />
        <result property="invalid"    column="del_flag"    />
        <result property="clientType"    column="client_type"    />
        <result property="cooperationState"    column="cooperation_state"    />
        <result property="areaId"    column="area_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="clientProperty"    column="client_property"    />
        <result property="clientGroup"    column="client_group"    />
        <result property="clientGrade"    column="client_grade"    />
        <result property="region"    column="region"    />
        <result property="freightRatio"    column="freight_ratio"    />
        <result property="storageRatio"    column="storage_ratio"    />
        <result property="messageRatio"    column="message_ratio"    />
        <result property="platformRatio"    column="platform_ratio"    />
        <result property="brandRatio"    column="brand_ratio"    />
        <result property="billLogic"    column="bill_logic"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="area"    column="area"    />
        <result property="deptCode"    column="dept_code"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="groupCustomerCode"    column="group_customer_code"    />
        <result property="settlementAccount"    column="settlement_account"    />
        <result property="invoiceMode" column="invoice_mode" />
        <result property="deliveryRatio" column="delivery_ratio" />
        <result property="storageTaxRate" column="storage_tax_rate" />
        <result property="endDate" column="end_date" />
        <result property="othersTaxRate" column="others_tax_rate" />
        <result property="claimsType" column="claims_type" />
        <result property="airtransportRadio" column="airtransport_radio"/>
        <result property="consumablesRadio" column="consumables_radio"/>
        <result property="unloadRadio" column="unload_radio"/>
        <result property="gpsRadio" column="gps_radio"/>
        <result property="scmRadio" column="scm_radio"/>
        <result column="airtransport_rate" property="airtransportRate" />
        <result column="consumables_rate" property="consumablesRate" />
        <result column="unload_rate" property="unloadRate" />
        <result column="gps_rate" property="gpsRate" />
        <result column="scm_rate" property="scmRate" />
        <result column="settle_setting" property="settleSetting" />
        <result column="share_type" property="shareType" />
    </resultMap>

    <sql id="selectMdmClientinfoVo">
        SELECT id,
               client_code,
               client_name,
               brand_name,
               cost_date_dimension,
               carrierpay_type,
               is_group,
               company_id,
               user_company_id,
               remark,
               update_user_code,
               update_user_name,
               update_user_company_id,
               update_time,
               business_type,
               cooperate_start,
               cooperate_end,
               is_enable,
               client_level,
               payment_days,
               collection_days,
               cooperate_type,
               late_fee,
               invoice_type,
               opening_bank,
               card_number,
               opening_name,
               link_phone,
               taxpayer_num,
               tac_address,
               tax_rate,
               storage_tax_rate,
               check_name,
               network,
               del_flag,
               client_type,
               cooperation_state,
               area_id,
               oper_time,
               client_property,
               client_group,
               client_grade,
               region,
               freight_ratio,
               storage_ratio,
               message_ratio,
               platform_ratio,
               brand_ratio,
               delivery_ratio,
               bill_logic,
               others_tax_rate,
               group_customer_code,
               settlement_account,
               invoice_mode,
               end_date,
               claims_type,
               airtransport_radio,
               consumables_radio,
               unload_radio,
               gps_radio,
               scm_radio,
               airtransport_rate,
               consumables_rate,
               unload_rate,
               gps_rate,
               scm_rate,
               settle_setting,
               share_type
        FROM bms_clientinfo
  </sql>

    <select id="selectSameUnderTheGroup" parameterType="com.bbyb.joy.bms.domain.dto.MdmClientinfo" resultMap="MdmClientinfoResult">
        <include refid="selectMdmClientinfoVo"/>
        where
        bill_logic = #{billLogic}
        and (id = #{id} or id=#{clientGroup} or client_group=#{clientGroup} )
        and IFNULL(payment_days,'')!=''
        and IFNULL(del_flag,1)=0
        and IFNULL(is_enable,0)=0

    </select>

    <select id="selectClientinfoByCode" parameterType="com.bbyb.joy.bms.domain.dto.MdmClientinfo" resultMap="MdmClientinfoResult">
        <include refid="selectMdmClientinfoVo"/>
        where
        1=1
        and (IFNULL(client_code,'')=#{code} or IFNULL(client_name,'') = #{code})
        and IFNULL(del_flag,1)=0
        and IFNULL(is_enable,0)=0
    </select>

    <select id="selectNetworkByBillName" parameterType="com.bbyb.joy.bms.domain.dto.MdmClientinfo" resultMap="MdmClientinfoResult">
        select
        mechanism_id as company_id
        from company_network
        where bill_name = #{billName}
        and mechanism_id !=100
        limit 1
    </select>

    <select id="getclientInvoiceinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmClientInvoiceinfo" resultType="com.bbyb.joy.bms.domain.dto.MdmClientInvoiceinfo">
        select
        id,
        clientid,
        invoice_type as invoiceType,
        opening_bank as openingBank,
        card_number as cardNumber,
        opening_name as openingName,
        link_phone as linkPhone,
        tac_address as tacAddress,
        taxpayer_num as taxpayerNum,
        update_time as updateTime
        from bms_client_invoiceinfo
        where del_flag = '0'
        and clientid = #{clientid}
        <if test="invoiceType!=null">
            and invoice_type = #{invoiceType}
        </if>
        ORDER BY update_time DESC
    </select>

    <select id="selectSameUnderThe" parameterType="com.bbyb.joy.bms.domain.dto.MdmClientinfo" resultMap="MdmClientinfoResult">
        select
        a.client_code as client_code,
        IFNULL(b.client_name,a.client_name) as client_name
        from bms_clientinfo a
        left join bms_clientinfo b
        on a.client_group = b.id
        where
         a.id = #{id}

    </select>

    <select id="selectBillDateAndRuleList" resultMap="MdmClientinfoResult">
        <include refid="selectMdmClientinfoVo"/>
       where
       IFNULL(payment_days,'')!=''
       and IFNULL(del_flag,1)=0
       and IFNULL(is_enable,0)=0
       and IFNULL(bill_logic,-1)!=-1
    </select>
    <select id="selectMdmClientinfoAll" parameterType="com.bbyb.joy.bms.domain.dto.MdmClientinfo" resultMap="MdmClientinfoResult">
        <include refid="selectMdmClientinfoVo"/>
    </select>
    <select id="selectMdmClientinfoList" parameterType="com.bbyb.joy.bms.domain.dto.MdmClientinfo" resultMap="MdmClientinfoResult">
        <include refid="selectMdmClientinfoVo"/>
        <where>
            <if test="clientList != null and clientList.size>0">
                and   client_code in
                <foreach collection="clientList" item="client" open="(" separator="," close=")">
                    #{client}
                </foreach>
            </if>
            <if test="clientCode != null  and clientCode != ''"> and client_code like concat('%', #{clientCode}, '%')</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="brandName != null  and brandName != ''"> and brand_name like concat('%', #{brandName}, '%')</if>
            <if test="carrierpayType != null "> and carrierpay_type = #{carrierpayType}</if>
            <if test="isGroup != null "> and is_group = #{isGroup}</if>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="userCompanyId != null  and userCompanyId != ''"> and user_company_id = #{userCompanyId}</if>
            <if test="updateUserCode != null "> and update_user_code like concat('%', #{updateUserCode}, '%')</if>
            <if test="updateUserName != null  and updateUserName != ''"> and update_user_name like concat('%', #{updateUserName}, '%')</if>
            <if test="updateUserCompanyId != null "> and update_user_company_id = #{updateUserCompanyId}</if>
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="cooperateStart != null "> and cooperate_start = #{cooperateStart}</if>
            <if test="cooperateEnd != null "> and cooperate_end = #{cooperateEnd}</if>
            <if test="isEnable != null "> and is_enable = #{isEnable}</if>
            <if test="clientLevel != null "> and client_level = #{clientLevel}</if>
            <if test="paymentDays != null  and paymentDays != ''"> and payment_days = #{paymentDays}</if>
            <if test="collectionDays != null "> and collection_days = #{collectionDays}</if>
            <if test="cooperateType != null "> and cooperate_type = #{cooperateType}</if>
            <if test="lateFee != null  and lateFee != ''"> and late_fee = #{lateFee}</if>
            <if test="invoiceType != null "> and invoice_type = #{invoiceType}</if>
            <if test="openingBank != null  and openingBank != ''"> and opening_bank = #{openingBank}</if>
            <if test="cardNumber != null  and cardNumber != ''"> and card_number = #{cardNumber}</if>
            <if test="openingName != null  and openingName != ''"> and opening_name like concat('%', #{openingName}, '%')</if>
            <if test="linkPhone != null  and linkPhone != ''"> and link_phone = #{linkPhone}</if>
            <if test="taxpayerNum != null  and taxpayerNum != ''"> and taxpayer_num = #{taxpayerNum}</if>
            <if test="tacAddress != null  and tacAddress != ''"> and tac_address = #{tacAddress}</if>
            <if test="taxRate != null "> and tax_rate = #{taxRate}</if>
            <if test="checkName != null  and checkName != ''"> and check_name like concat('%', #{checkName}, '%')</if>
            <if test="network != null  and network != ''"> and network = #{network}</if>
            <if test="invalid != null "> and del_flag = #{invalid}</if>
            <if test="clientType != null "> and client_type = #{clientType}</if>
            <if test="cooperationState != null "> and cooperation_state = #{cooperationState}</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="params.beginOperTime != null and params.beginOperTime != '' and params.endOperTime != null and params.endOperTime != ''"> and update_time between #{params.beginOperTime} and #{params.endOperTime}</if>
            <if test="companyIds!=null and companyIds.length!=0">
                 and  company_id in
                 <foreach collection="companyIds" item="item" open="(" separator="," close=")">
                     #{item}
                 </foreach>
            </if>
        </where>
        order by oper_time desc
    </select>

    <select id="selectMdmClientinfoById" parameterType="java.lang.String" resultMap="MdmClientinfoResult">
        <include refid="selectMdmClientinfoVo"/>
        where id = #{id}
    </select>


    <insert id="insertMdmClientinfo" parameterType="java.util.Map"  useGeneratedKeys="true"  keyProperty="id">
        insert into bms_clientinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="clientCode != null">client_code,</if>
            <if test="clientName != null">client_name,</if>
            <if test="brandName != null">brand_name,</if>
            <if test="carrierpayType != null">carrierpay_type,</if>
            <if test="isGroup != null">is_group,</if>
            <if test="companyId != null">company_id,</if>
            <if test="userCompanyId != null">user_company_id,</if>
            <if test="remark != null">remark,</if>
            <if test="updateUserCode != null">update_user_code,</if>
            <if test="updateUserName != null">update_user_name,</if>
            <if test="updateUserCompanyId != null">update_user_company_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="businessType != null">business_type,</if>
            <if test="cooperateStart != null">cooperate_start,</if>
            <if test="cooperateEnd != null">cooperate_end,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="clientLevel != null">client_level,</if>
            <if test="paymentDays != null">payment_days,</if>
            <if test="collectionDays != null">collection_days,</if>
            <if test="cooperateType != null">cooperate_type,</if>
            <if test="lateFee != null">late_fee,</if>
            <if test="invoiceType != null">invoice_type,</if>
            <if test="openingBank != null">opening_bank,</if>
            <if test="cardNumber != null">card_number,</if>
            <if test="openingName != null">opening_name,</if>
            <if test="linkPhone != null">link_phone,</if>
            <if test="taxpayerNum != null">taxpayer_num,</if>
            <if test="tacAddress != null">tac_address,</if>
            <if test="taxRate != null">tax_rate,</if>
            <if test="storageTaxRate != null">storage_tax_rate,</if>
            <if test="checkName != null">check_name,</if>
            <if test="network != null">network,</if>
            <if test="invalid != null">del_flag,</if>
            <if test="clientType != null">client_type,</if>
            <if test="cooperationState != null">cooperation_state,</if>
            <if test="areaId != null">area_id,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="clientProperty != null">client_property,</if>
            <if test="clientGroup != null">client_group,</if>
            <if test="clientGrade != null">client_grade,</if>
            <if test="region != null">region,</if>
            <if test="freightRatio != null">freight_ratio,</if>
            <if test="storageRatio != null">storage_ratio,</if>
            <if test="messageRatio != null">message_ratio,</if>
            <if test="platformRatio != null">platform_ratio,</if>
            <if test="brandRatio != null">brand_ratio,</if>
            <if test="billLogic != null">bill_logic,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="area != null">area,</if>
            <if test="deptCode != null">dept_code,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="areaName != null">area_name,</if>
            <if test="groupCustomerCode != null">group_customer_code,</if>
            <if test="settlementAccount != null">settlement_account,</if>
            <if test="endDate != null">end_date,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="settleSetting != null">settle_setting,</if>
            <if test="shareType != null">share_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="brandName != null">#{brandName},</if>
            <if test="carrierpayType != null">#{carrierpayType},</if>
            <if test="isGroup != null">#{isGroup},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="userCompanyId != null">#{userCompanyId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="updateUserCode != null">#{updateUserCode},</if>
            <if test="updateUserName != null">#{updateUserName},</if>
            <if test="updateUserCompanyId != null">#{updateUserCompanyId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="cooperateStart != null">#{cooperateStart},</if>
            <if test="cooperateEnd != null">#{cooperateEnd},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="clientLevel != null">#{clientLevel},</if>
            <if test="paymentDays != null">#{paymentDays},</if>
            <if test="collectionDays != null">#{collectionDays},</if>
            <if test="cooperateType != null">#{cooperateType},</if>
            <if test="lateFee != null">#{lateFee},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="openingBank != null">#{openingBank},</if>
            <if test="cardNumber != null">#{cardNumber},</if>
            <if test="openingName != null">#{openingName},</if>
            <if test="linkPhone != null">#{linkPhone},</if>
            <if test="taxpayerNum != null">#{taxpayerNum},</if>
            <if test="tacAddress != null">#{tacAddress},</if>
            <if test="taxRate != null">#{taxRate},</if>
            <if test="storageTaxRate != null">#{storageTaxRate},</if>
            <if test="checkName != null">#{checkName},</if>
            <if test="network != null">#{network},</if>
            <if test="invalid != null">#{invalid},</if>
            <if test="clientType != null">#{clientType},</if>
            <if test="cooperationState != null">#{cooperationState},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="clientProperty != null">#{clientProperty},</if>
            <if test="clientGroup != null">#{clientGroup},</if>
            <if test="clientGrade != null">#{clientGrade},</if>
            <if test="region != null">#{region},</if>
            <if test="freightRatio != null">#{freightRatio},</if>
            <if test="storageRatio != null">#{storageRatio},</if>
            <if test="messageRatio != null">#{messageRatio},</if>
            <if test="platformRatio != null">#{platformRatio},</if>
            <if test="brandRatio != null">#{brandRatio},</if>
            <if test="billLogic != null">#{billLogic},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="area != null">#{area},</if>
            <if test="deptCode != null">#{deptCode},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="groupCustomerCode != null">#{groupCustomerCode},</if>
            <if test="settlementAccount != null">#{settlementAccount},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="settleSetting != null">1,</if>
            <if test="shareType != null">1,</if>
        </trim>
    </insert>

    <update id="updateMdmClientinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmClientinfo">
        update bms_clientinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="costDateDimension!=null ">cost_date_dimension = #{costDateDimension}, </if>
            <if test="brandName != null">brand_name = #{brandName},</if>
            <if test="carrierpayType != null">carrierpay_type = #{carrierpayType},</if>
            <if test="isGroup != null">is_group = #{isGroup},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="userCompanyId != null">user_company_id = #{userCompanyId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateUserCode != null">update_user_code = #{updateUserCode},</if>
            <if test="updateUserName != null">update_user_name = #{updateUserName},</if>
            <if test="updateUserCompanyId != null">update_user_company_id = #{updateUserCompanyId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="cooperateStart != null">cooperate_start = #{cooperateStart},</if>
            <if test="cooperateEnd != null">cooperate_end = #{cooperateEnd},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="clientLevel != null">client_level = #{clientLevel},</if>
            <if test="paymentDays != null">payment_days = #{paymentDays},</if>
            <if test="collectionDays != null">collection_days = #{collectionDays},</if>
            <if test="cooperateType != null">cooperate_type = #{cooperateType},</if>
            <if test="lateFee != null">late_fee = #{lateFee},</if>
            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>
            <if test="openingBank != null">opening_bank = #{openingBank},</if>
            <if test="cardNumber != null">card_number = #{cardNumber},</if>
            <if test="openingName != null">opening_name = #{openingName},</if>
            <if test="linkPhone != null">link_phone = #{linkPhone},</if>
            <if test="taxpayerNum != null">taxpayer_num = #{taxpayerNum},</if>
            <if test="tacAddress != null">tac_address = #{tacAddress},</if>
            <if test="taxRate != null">tax_rate = #{taxRate},</if>
            <if test="storageTaxRate != null">storage_tax_rate = #{storageTaxRate},</if>
            <if test="checkName != null">check_name = #{checkName},</if>
            <if test="network != null">network = #{network},</if>
            <if test="invalid != null">del_flag = #{invalid},</if>
            <if test="clientType != null">client_type = #{clientType},</if>
            <if test="cooperationState != null">cooperation_state = #{cooperationState},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="clientProperty != null">client_property = #{clientProperty},</if>
            <if test="clientGroup != null">client_group = #{clientGroup},</if>
            <if test="clientGrade != null">client_grade = #{clientGrade},</if>
            <if test="region != null">region = #{region},</if>
            <if test="freightRatio != null">freight_ratio = #{freightRatio},</if>
            <if test="storageRatio != null">storage_ratio = #{storageRatio},</if>
            <if test="messageRatio != null">message_ratio = #{messageRatio},</if>
            <if test="platformRatio != null">platform_ratio = #{platformRatio},</if>
            <if test="brandRatio != null">brand_ratio = #{brandRatio},</if>
            <if test="billLogic != null">bill_logic = #{billLogic},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="deptCode != null">dept_code = #{deptCode},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="groupCustomerCode != null">group_customer_code = #{groupCustomerCode},</if>
            <if test="settlementAccount != null">settlement_account = #{settlementAccount},</if>
            <if test="deliveryRatio!=null">delivery_ratio=#{deliveryRatio},</if>
            <if test="invoiceMode!=null" >invoice_mode=#{invoiceMode},</if>
            <if test="othersTaxRate!=null" >others_tax_rate=#{othersTaxRate},</if>
            <if test="endDate!=null" >end_date=#{endDate},</if>
            <if test="claimsType!=null" >claims_type=#{claimsType},</if>
            <if test="airtransportRadio != null ">airtransport_radio = #{airtransportRadio},</if>
            <if test="consumablesRadio != null">consumables_radio = #{consumablesRadio},</if>
            <if test="unloadRadio != null">unload_radio = #{unloadRadio},</if>
            <if test="gpsRadio != null">gps_radio = #{gpsRadio},</if>
            <if test="scmRadio != null">scm_radio = #{scmRadio},</if>
            <if test="airtransportRate != null">airtransport_rate = #{airtransportRate},</if>
            <if test="consumablesRate != null">consumables_rate = #{consumablesRate},</if>
            <if test="unloadRate != null">unload_rate = #{unloadRate},</if>
            <if test="gpsRate != null">gps_rate = #{gpsRate},</if>
            <if test="scmRate != null">scm_rate = #{scmRate},</if>
            <if test="delFlag!=null" >del_flag=#{delFlag},</if>
            <if test="settleSetting!=null">settle_setting=#{settleSetting},</if>
            <if test="shareType!=null">share_type=#{shareType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMdmClientinfoById" parameterType="java.lang.String">
        delete from bms_clientinfo where id = #{id}
    </delete>

    <delete id="deleteMdmClientinfoByIds">
        delete from bms_clientinfo where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateMdmClientinfoStatusByIds">
        update bms_clientinfo set is_enable = #{status} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectClientinfoList" resultMap="MdmClientinfoResult">
        <include refid="selectMdmClientinfoVo"/>
        where del_flag=0
        <if test="ids != null and ids.size>0 ">
            AND id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
        <if test="codes != null and codes.size>0 ">
            AND client_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
        <if test="names != null and names.size>0 ">
            AND client_name in
            <foreach collection="names" item="names" open="(" separator="," close=")">
                #{names}
            </foreach>
        </if>
        <if test="NameLike !=null and NameLike !=''" >
            AND client_name like concat('%', #{NameLike}, '%')
        </if>
    </select>

    <select id="selectClientinfoList2" resultMap="MdmClientinfoResult">
        <include refid="selectMdmClientinfoVo"/>
        where del_flag=0
        <if test="ids != null and ids.size>0 ">
            AND id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
        <if test="codes != null and codes.size>0 ">
            AND client_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
        <if test="names != null and names.size>0 ">
            AND client_name in
            <foreach collection="names" item="names" open="(" separator="," close=")">
                #{names}
            </foreach>
        </if>
        <if test="brandName != null and brandName.size>0 ">
            AND brand_name in
            <foreach collection="brandName" item="brandName" open="(" separator="," close=")">
                #{brandName}
            </foreach>
        </if>
    </select>

    <select id="selectClientinfoGroupList" resultMap="MdmClientinfoResult">
        <include refid="selectMdmClientinfoVo"/>
        where del_flag=0
        <if test="codes != null and codes.size>0 ">
            AND group_customer_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <select id="checkClientCodeUnique" parameterType="java.lang.String" resultMap="MdmClientinfoResult">
        select id, client_code,client_name,carrierpay_type from bms_clientinfo where client_code=#{clientCode} limit 1
    </select>


    <select id="getClientSettingInfo"  resultType="com.bbyb.joy.bms.domain.dto.MdmPubSettledateSettingInfo">
        select
        id,
        client_id as clientId,
        client_code as clientCode,
        bill_type as billType,
        date_type as dateType
        from pub_settledate_setting
        where client_id = #{id}
        limit 1
    </select>
    <select id="selectMdmClientSettledateById" resultType="com.bbyb.joy.bms.domain.dto.PubSettledateSetting"
            parameterType="java.lang.String">
        select date_type as dateType from pub_settledate_setting where client_id =#{id} order by id desc limit 1
    </select>

    <update id="updateClientSettingInfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmPubSettledateSettingInfo">
        update pub_settledate_setting set date_type = #{dateType}
        where id = #{id}
    </update>

    <insert id="insertClientSettingInfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmPubSettledateSettingInfo" useGeneratedKeys="true" keyProperty="id">
        insert into pub_settledate_setting
        (
            client_id,
            client_code,
            bill_type,
            date_type
        )
        value (
            #{clientId},
            #{clientCode},
            #{billType},
            #{dateType}
        )
    </insert>


    <select id="getCompanyNetWork" parameterType="String" resultType="com.bbyb.joy.bms.domain.dto.CompanyNetwork">
        select
            id,
            warehouse_code as warehouseCode,
            warehouse_name as warehouseName,
            mechanism_id as mechanismId,
            mechanism_name as mechanismName,
            network_name as networkName,
            bill_name as billName,
            create_code as createCode,
            create_by as createBy,
            create_time as createTime,
            create_dept_id as createDeptId,
            oper_dept_id as operDeptId,
            oper_code as operCode,
            oper_by as operBy,
            oper_time as operTime,
            del_flag as delFlag
        from company_network
        where
            del_flag=0
          and warehouse_code = #{warehouseCode}
            limit 1
    </select>

    <insert id="insertCompanyNetWork" parameterType="com.bbyb.joy.bms.domain.dto.CompanyNetwork">
        insert into company_network(
            warehouse_code,
            warehouse_name,
            mechanism_id,
            mechanism_name,
            network_name,
            bill_name,
            create_code,
            create_by,
            create_time,
            create_dept_id,
            oper_dept_id,
            oper_code,
            oper_by,
            oper_time,
            del_flag
        )value
            (
            #{warehouseCode},
            #{warehouseName},
            #{mechanismId},
            #{mechanismName},
            #{networkName},
            #{billName},
            #{createCode},
            #{createBy},
            #{createTime},
            #{createDeptId},
            #{operDeptId},
            #{operCode},
            #{operBy},
            #{operTime},
            0
            )
    </insert>
    <!--    修改省区对应关系-->
    <update id="updateCompanyNetWork" parameterType="com.bbyb.joy.bms.domain.dto.CompanyNetwork">
        update company_network
        set
        <if test="billName!=null">
            bill_name = #{billName},
        </if>
        <if test="warehouseName!=null">
            warehouse_name = #{warehouseName},
        </if>
        <if test="mechanismName!=null">
            mechanism_name = #{mechanismName},
        </if>
        <if test="networkName!=null">
            network_name = #{networkName},
        </if>
        oper_dept_id = #{operDeptId},
        oper_code = #{operCode},
        oper_by = #{operBy},
        oper_time = #{operTime}
        where
        warehouse_code = #{warehouseCode}
    </update>


    <select id="getNetWorkList"  resultType="com.bbyb.joy.bms.domain.dto.CompanyNetwork">
        select
            id,
            warehouse_code as warehouseCode,
            warehouse_name as warehouseName,
            mechanism_id as mechanismId,
            mechanism_name as mechanismName,
            network_name as networkName,
            bill_name as billName,
            create_code as createCode,
            create_by as createBy,
            create_time as createTime,
            create_dept_id as createDeptId,
            oper_dept_id as operDeptId,
            oper_code as operCode,
            oper_by as operBy,
            oper_time as operTime,
            del_flag as delFlag
        from company_network
            where 1=1
            and IFNULL(bill_name,'')!=''
        group by bill_name
    </select>




    <resultMap type="com.bbyb.joy.bms.domain.dto.model.MdmClientinfoModel" id="MdmClientinfoModelResult">
        <result property="id"    column="id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="brandName"    column="brand_name"    />
        <result property="carrierpayType"    column="carrierpay_type"    />
        <result property="isGroup"    column="is_group"    />
        <result property="companyId"    column="company_id"    />
        <result property="userCompanyId"    column="user_company_id"    />
        <result property="remark"    column="remark"    />
        <result property="updateUserCode"    column="update_user_code"    />
        <result property="updateUserName"    column="update_user_name"    />
        <result property="updateUserCompanyId"    column="update_user_company_id"    />
        <result property="businessType"    column="business_type"    />
        <result property="cooperateStart"    column="cooperate_start"    />
        <result property="cooperateEnd"    column="cooperate_end"    />
        <result property="isEnable"    column="is_enable"    />
        <result property="clientLevel"    column="client_level"    />
        <result property="paymentDays"    column="payment_days"    />
        <result property="collectionDays"    column="collection_days"    />
        <result property="cooperateType"    column="cooperate_type"    />
        <result property="lateFee"    column="late_fee"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="openingBank"    column="opening_bank"    />
        <result property="cardNumber"    column="card_number"    />
        <result property="openingName"    column="opening_name"    />
        <result property="linkPhone"    column="link_phone"    />
        <result property="taxpayerNum"    column="taxpayer_num"    />
        <result property="tacAddress"    column="tac_address"    />
        <result property="taxRate"    column="tax_rate"    />
        <result property="checkName"    column="check_name"    />
        <result property="network"    column="network"    />
        <result property="invalid"    column="del_flag"    />
        <result property="clientType"    column="client_type"    />
        <result property="cooperationState"    column="cooperation_state"    />
        <result property="areaId"    column="area_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="clientProperty"    column="client_property"    />
        <result property="clientGroup"    column="client_group"    />
        <result property="clientGrade"    column="client_grade"    />
        <result property="region"    column="region"    />
        <result property="freightRatio"    column="freight_ratio"    />
        <result property="storageRatio"    column="storage_ratio"    />
        <result property="messageRatio"    column="message_ratio"    />
        <result property="platformRatio"    column="platform_ratio"    />
        <result property="brandRatio"    column="brand_ratio"    />
        <result property="billLogic"    column="bill_logic"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="area"    column="area"    />
        <result property="deptCode"    column="dept_code"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="groupCustomerCode"    column="group_customer_code"    />
        <result property="settlementAccount"    column="settlement_account"    />
        <result property="invoiceMode" column="invoice_mode" />
        <result property="deliveryRatio" column="delivery_ratio" />
        <result property="storageTaxRate" column="storage_tax_rate" />
        <result property="endDate" column="end_date" />
        <result property="othersTaxRate" column="others_tax_rate" />
        <result property="claimsType" column="claims_type" />
        <result property="airtransportRadio" column="airtransport_radio"/>
        <result property="consumablesRadio" column="consumables_radio"/>
        <result property="unloadRadio" column="unload_radio"/>
        <result property="gpsRadio" column="gps_radio"/>
        <result property="scmRadio" column="scm_radio"/>
        <result column="airtransport_rate" property="airtransportRate" />
        <result column="consumables_rate" property="consumablesRate" />
        <result column="unload_rate" property="unloadRate" />
        <result column="gps_rate" property="gpsRate" />
        <result column="scm_rate" property="scmRate" />
        <result column="settle_setting" property="settleSetting" />
        <result column="share_type" property="shareType" />
        <result column="workCode" property="workCode" />
        <result column="workId" property="workId" />
        <result column="codeType" property="codeType" />
        <result column="isToClient" property="isToClient" />
    </resultMap>


    <select id="selectMdmClientByCodeForYs" resultMap="MdmClientinfoModelResult">
        <if test="codeType!=null and codeType in {1,2,3}">
            SELECT
                t1.id,
                t1.client_code,
                t1.client_name,
                t1.brand_name,
                t1.carrierpay_type,
                t1.is_group,
                t1.company_id,
                t1.user_company_id,
                t1.remark,
                t1.update_user_code,
                t1.update_user_name,
                t1.update_user_company_id,
                t1.update_time,
                t1.business_type,
                t1.cooperate_start,
                t1.cooperate_end,
                t1.is_enable,
                t1.client_level,
                t1.payment_days,
                t1.collection_days,
                t1.cooperate_type,
                t1.late_fee,
                t1.invoice_type,
                t1.opening_bank,
                t1.card_number,
                t1.opening_name,
                t1.link_phone,
                t1.taxpayer_num,
                t1.tac_address,
                t1.tax_rate,
                t1.storage_tax_rate,
                t1.check_name,
                t1.network,
                t1.del_flag,
                t1.client_type,
                t1.cooperation_state,
                t1.area_id,
                t1.oper_time,
                t1.client_property,
                t1.client_group,
                t1.client_grade,
                t1.region,
                t1.freight_ratio,
                t1.storage_ratio,
                t1.message_ratio,
                t1.platform_ratio,
                t1.brand_ratio,
                t1.delivery_ratio,
                t1.bill_logic,
                t1.others_tax_rate,
                t1.group_customer_code,
                t1.settlement_account,
                t1.invoice_mode,
                t1.end_date,
                t1.claims_type,
                t1.airtransport_radio,
                t1.consumables_radio,
                t1.unload_radio,
                t1.gps_radio,
                t1.scm_radio,
                t1.airtransport_rate,
                t1.consumables_rate,
                t1.unload_rate,
                t1.gps_rate,
                t1.scm_rate,
                t1.settle_setting,
                t1.share_type,
                t2.relate_code AS workCode,
                t2.id AS workId,
                t2.code_type AS codeType
            FROM bms_clientinfo t1
            JOIN bms_ysbillcodeinfo t2 ON t2.client_code = t1.client_code AND t2.del_flag = 0
            WHERE t1.del_flag=0
            AND t2.code_type = #{codeType}
            <if test="codes!=null and codes.size>0">
                AND t2.relate_code IN
                <foreach collection="codes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="ids!=null and ids.size>0">
                AND t2.id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </if>
        <if test="codeType!=null and codeType == 4">
            SELECT
                t1.id,
                t1.client_code,
                t1.client_name,
                t1.brand_name,
                t1.carrierpay_type,
                t1.is_group,
                t1.company_id,
                t1.user_company_id,
                t1.remark,
                t1.update_user_code,
                t1.update_user_name,
                t1.update_user_company_id,
                t1.update_time,
                t1.business_type,
                t1.cooperate_start,
                t1.cooperate_end,
                t1.is_enable,
                t1.client_level,
                t1.payment_days,
                t1.collection_days,
                t1.cooperate_type,
                t1.late_fee,
                t1.invoice_type,
                t1.opening_bank,
                t1.card_number,
                t1.opening_name,
                t1.link_phone,
                t1.taxpayer_num,
                t1.tac_address,
                t1.tax_rate,
                t1.storage_tax_rate,
                t1.check_name,
                t1.network,
                t1.del_flag,
                t1.client_type,
                t1.cooperation_state,
                t1.area_id,
                t1.oper_time,
                t1.client_property,
                t1.client_group,
                t1.client_grade,
                t1.region,
                t1.freight_ratio,
                t1.storage_ratio,
                t1.message_ratio,
                t1.platform_ratio,
                t1.brand_ratio,
                t1.delivery_ratio,
                t1.bill_logic,
                t1.others_tax_rate,
                t1.group_customer_code,
                t1.settlement_account,
                t1.invoice_mode,
                t1.end_date,
                t1.claims_type,
                t1.airtransport_radio,
                t1.consumables_radio,
                t1.unload_radio,
                t1.gps_radio,
                t1.scm_radio,
                t1.airtransport_rate,
                t1.consumables_rate,
                t1.unload_rate,
                t1.gps_rate,
                t1.scm_rate,
                t1.settle_setting,
                t1.share_type,
                t2.stock_code AS workCode,
                t2.id AS workId,
                4 AS codeType
            FROM bms_clientinfo t1
            JOIN bms_ysstockinfo t2 ON t2.client_code = t1.client_code AND t2.del_flag = 0
            WHERE t1.del_flag = 0
            <if test="codes!=null and codes.size>0">
                AND t2.stock_code IN
                <foreach collection="codes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="ids!=null and ids.size>0">
                AND t2.id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            )
        </if>
    </select>


    <select id="selectMdmClientByCodeForYf" resultMap="MdmClientinfoModelResult">
        <if test="codeType!=null and codeType == 1">
            SELECT
            t1.id,
            t1.client_code,
            t1.client_name,
            t1.brand_name,
            t1.carrierpay_type,
            t1.is_group,
            t1.company_id,
            t1.user_company_id,
            t1.remark,
            t1.update_user_code,
            t1.update_user_name,
            t1.update_user_company_id,
            t1.update_time,
            t1.business_type,
            t1.cooperate_start,
            t1.cooperate_end,
            t1.is_enable,
            t1.client_level,
            t1.payment_days,
            t1.collection_days,
            t1.cooperate_type,
            t1.late_fee,
            t1.invoice_type,
            t1.opening_bank,
            t1.card_number,
            t1.opening_name,
            t1.link_phone,
            t1.taxpayer_num,
            t1.tac_address,
            t1.tax_rate,
            t1.storage_tax_rate,
            t1.check_name,
            t1.network,
            t1.del_flag,
            t1.client_type,
            t1.cooperation_state,
            t1.area_id,
            t1.oper_time,
            t1.client_property,
            t1.client_group,
            t1.client_grade,
            t1.region,
            t1.freight_ratio,
            t1.storage_ratio,
            t1.message_ratio,
            t1.platform_ratio,
            t1.brand_ratio,
            t1.delivery_ratio,
            t1.bill_logic,
            t1.others_tax_rate,
            t1.group_customer_code,
            t1.settlement_account,
            t1.invoice_mode,
            t1.end_date,
            t1.claims_type,
            t1.airtransport_radio,
            t1.consumables_radio,
            t1.unload_radio,
            t1.gps_radio,
            t1.scm_radio,
            t1.airtransport_rate,
            t1.consumables_rate,
            t1.unload_rate,
            t1.gps_rate,
            t1.scm_rate,
            t1.settle_setting,
            t1.share_type,
            t2.virtual_order_no AS workCode,
            t2.id AS workId,
            1 AS codeType,
            t2.is_to_client AS isToClient
            FROM bms_clientinfo t1
            LEFT JOIN bms_yfbillcodeinfo t2 ON t2.client_id = t1.id
            WHERE t1.del_flag=0
            <if test="codes!=null and codes.size>0">
                AND t2.virtual_order_no IN
                <foreach collection="codes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="ids!=null and ids.size>0">
                AND t2.id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </if>
        <if test="codeType!=null and codeType in {2,3}">
            SELECT
            t1.id,
            t1.client_code,
            t1.client_name,
            t1.brand_name,
            t1.carrierpay_type,
            t1.is_group,
            t1.company_id,
            t1.user_company_id,
            t1.remark,
            t1.update_user_code,
            t1.update_user_name,
            t1.update_user_company_id,
            t1.update_time,
            t1.business_type,
            t1.cooperate_start,
            t1.cooperate_end,
            t1.is_enable,
            t1.client_level,
            t1.payment_days,
            t1.collection_days,
            t1.cooperate_type,
            t1.late_fee,
            t1.invoice_type,
            t1.opening_bank,
            t1.card_number,
            t1.opening_name,
            t1.link_phone,
            t1.taxpayer_num,
            t1.tac_address,
            t1.tax_rate,
            t1.storage_tax_rate,
            t1.check_name,
            t1.network,
            t1.del_flag,
            t1.client_type,
            t1.cooperation_state,
            t1.area_id,
            t1.oper_time,
            t1.client_property,
            t1.client_group,
            t1.client_grade,
            t1.region,
            t1.freight_ratio,
            t1.storage_ratio,
            t1.message_ratio,
            t1.platform_ratio,
            t1.brand_ratio,
            t1.delivery_ratio,
            t1.bill_logic,
            t1.others_tax_rate,
            t1.group_customer_code,
            t1.settlement_account,
            t1.invoice_mode,
            t1.end_date,
            t1.claims_type,
            t1.airtransport_radio,
            t1.consumables_radio,
            t1.unload_radio,
            t1.gps_radio,
            t1.scm_radio,
            t1.airtransport_rate,
            t1.consumables_rate,
            t1.unload_rate,
            t1.gps_rate,
            t1.scm_rate,
            t1.settle_setting,
            t1.share_type,
            t2.relate_code AS workCode,
            t2.id AS workId,
            case
                when t2.code_type = 1 then 2
                when t2.code_type = 1 then 3
                else t2.code_type
            end AS codeType
            FROM bms_clientinfo t1
            JOIN bms_yfstock_codeinfo t2 ON t2.client_id = t1.id
            WHERE t1.del_flag=0
            <if test="codes!=null and codes.size>0">
                AND t2.relate_code IN
                <foreach collection="codes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="ids!=null and ids.size>0">
                AND t2.id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </if>
        <if test="codeType!=null and codeType == 4">
            SELECT
            t1.id,
            t1.client_code,
            t1.client_name,
            t1.brand_name,
            t1.carrierpay_type,
            t1.is_group,
            t1.company_id,
            t1.user_company_id,
            t1.remark,
            t1.update_user_code,
            t1.update_user_name,
            t1.update_user_company_id,
            t1.update_time,
            t1.business_type,
            t1.cooperate_start,
            t1.cooperate_end,
            t1.is_enable,
            t1.client_level,
            t1.payment_days,
            t1.collection_days,
            t1.cooperate_type,
            t1.late_fee,
            t1.invoice_type,
            t1.opening_bank,
            t1.card_number,
            t1.opening_name,
            t1.link_phone,
            t1.taxpayer_num,
            t1.tac_address,
            t1.tax_rate,
            t1.storage_tax_rate,
            t1.check_name,
            t1.network,
            t1.del_flag,
            t1.client_type,
            t1.cooperation_state,
            t1.area_id,
            t1.oper_time,
            t1.client_property,
            t1.client_group,
            t1.client_grade,
            t1.region,
            t1.freight_ratio,
            t1.storage_ratio,
            t1.message_ratio,
            t1.platform_ratio,
            t1.brand_ratio,
            t1.delivery_ratio,
            t1.bill_logic,
            t1.others_tax_rate,
            t1.group_customer_code,
            t1.settlement_account,
            t1.invoice_mode,
            t1.end_date,
            t1.claims_type,
            t1.airtransport_radio,
            t1.consumables_radio,
            t1.unload_radio,
            t1.gps_radio,
            t1.scm_radio,
            t1.airtransport_rate,
            t1.consumables_rate,
            t1.unload_rate,
            t1.gps_rate,
            t1.scm_rate,
            t1.settle_setting,
            t1.share_type,
            t2.stock_code AS workCode,
            t2.id AS workId,
            4 AS codeType
            FROM bms_clientinfo t1
            JOIN bms_yfstockinfo t2 ON t2.client_id = t1.id
            WHERE t1.del_flag=0
            <if test="codes!=null and codes.size>0">
                AND t2.stock_code IN
                <foreach collection="codes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="ids!=null and ids.size>0">
                AND t2.id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            )
        </if>
    </select>


</mapper>