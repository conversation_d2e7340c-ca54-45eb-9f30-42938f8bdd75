<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="mapper.db.BmsPubFeeSubjectMapper">
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsPubFeeSubject" id="BmsPubFeeSubjectMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="levelCode" column="level_code" jdbcType="VARCHAR"/>
        <result property="levelName" column="level_name" jdbcType="VARCHAR"/>
        <result property="fatherId" column="father_id" jdbcType="INTEGER"/>
        <result property="leve2Code" column="leve2_code" jdbcType="VARCHAR"/>
        <result property="leve2Name" column="leve2_name" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="operBy" column="oper_by" jdbcType="VARCHAR"/>
        <result property="operCode" column="oper_code" jdbcType="VARCHAR"/>
        <result property="operTime" column="oper_time" jdbcType="DATE"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdName" column="created_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="DATE"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedName" column="updated_name" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="DATE"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="selectPubFeeSubjectVo">
        select id, item_code, item_name, father_id, level, del_flag, oper_by, oper_code, oper_time from pub_fee_subject
    </sql>

    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultMap="BmsPubFeeSubjectMap">
        select
            id,level_code,level_name,father_id,leve2_code,leve2_name,del_flag,oper_by,oper_code,oper_time,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark
        from bms_pub_fee_subject
        where id = #{id}
    </select>

    <!--分页查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BmsPubFeeSubjectMap">
        select
        id,level_code,level_name,father_id,leve2_code,leve2_name,del_flag,oper_by,oper_code,oper_time,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark
        from bms_pub_fee_subject
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="levelCode != null and levelCode != ''">
                and level_code = #{levelCode}
            </if>
            <if test="levelName != null and levelName != ''">
                and level_name = #{levelName}
            </if>
            <if test="fatherId != null and fatherId != ''">
                and father_id = #{fatherId}
            </if>
            <if test="leve2Code != null and leve2Code != ''">
                and leve2_code = #{leve2Code}
            </if>
            <if test="leve2Name != null and leve2Name != ''">
                and leve2_name = #{leve2Name}
            </if>
            <if test="delFlag != null and delFlag != ''">
                and del_flag = #{delFlag}
            </if>
            <if test="operBy != null and operBy != ''">
                and oper_by = #{operBy}
            </if>
            <if test="operCode != null and operCode != ''">
                and oper_code = #{operCode}
            </if>
            <if test="operTime != null and operTime != ''">
                and oper_time = #{operTime}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdName != null and createdName != ''">
                and created_name = #{createdName}
            </if>
            <if test="createdTime != null and createdTime != ''">
                and created_time = #{createdTime}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and updated_by = #{updatedBy}
            </if>
            <if test="updatedName != null and updatedName != ''">
                and updated_name = #{updatedName}
            </if>
            <if test="updatedTime != null and updatedTime != ''">
                and updated_time = #{updatedTime}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>


    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into bms_pub_fee_subject(id,level_code,level_name,father_id,leve2_code,leve2_name,del_flag,oper_by,oper_code,oper_time,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark)
        values (#{id},#{levelCode},#{levelName},#{fatherId},#{leve2Code},#{leve2Name},#{delFlag},#{operBy},#{operCode},#{operTime},#{createdBy},#{createdName},#{createdTime},#{updatedBy},#{updatedName},#{updatedTime},#{remark})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bms_pub_fee_subject(id,level_code,level_name,father_id,leve2_code,leve2_name,del_flag,oper_by,oper_code,oper_time,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.levelCode},#{entity.levelName},#{entity.fatherId},#{entity.leve2Code},#{entity.leve2Name},#{entity.delFlag},#{entity.operBy},#{entity.operCode},#{entity.operTime},#{entity.createdBy},#{entity.createdName},#{entity.createdTime},#{entity.updatedBy},#{entity.updatedName},#{entity.updatedTime},#{entity.remark})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bms_pub_fee_subject(id,level_code,level_name,father_id,leve2_code,leve2_name,del_flag,oper_by,oper_code,oper_time,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.levelCode},#{entity.levelName},#{entity.fatherId},#{entity.leve2Code},#{entity.leve2Name},#{entity.delFlag},#{entity.operBy},#{entity.operCode},#{entity.operTime},#{entity.createdBy},#{entity.createdName},#{entity.createdTime},#{entity.updatedBy},#{entity.updatedName},#{entity.updatedTime},#{entity.remark})
        </foreach>
        on duplicate key update
        id=values(id),
        level_code=values(level_code),
        level_name=values(level_name),
        father_id=values(father_id),
        leve2_code=values(leve2_code),
        leve2_name=values(leve2_name),
        del_flag=values(del_flag),
        oper_by=values(oper_by),
        oper_code=values(oper_code),
        oper_time=values(oper_time),
        created_by=values(created_by),
        created_name=values(created_name),
        created_time=values(created_time),
        updated_by=values(updated_by),
        updated_name=values(updated_name),
        updated_time=values(updated_time),
        remark=values(remark)
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update bms_pub_fee_subject
        <set>
            <if test="id != null and id != ''">
                id = #{id},
            </if>
            <if test="levelCode != null and levelCode != ''">
                level_code = #{levelCode},
            </if>
            <if test="levelName != null and levelName != ''">
                level_name = #{levelName},
            </if>
            <if test="fatherId != null and fatherId != ''">
                father_id = #{fatherId},
            </if>
            <if test="leve2Code != null and leve2Code != ''">
                leve2_code = #{leve2Code},
            </if>
            <if test="leve2Name != null and leve2Name != ''">
                leve2_name = #{leve2Name},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>
            <if test="operBy != null and operBy != ''">
                oper_by = #{operBy},
            </if>
            <if test="operCode != null and operCode != ''">
                oper_code = #{operCode},
            </if>
            <if test="operTime != null and operTime != ''">
                oper_time = #{operTime},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdName != null and createdName != ''">
                created_name = #{createdName},
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time = #{createdTime},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedName != null and updatedName != ''">
                updated_name = #{updatedName},
            </if>
            <if test="updatedTime != null and updatedTime != ''">
                updated_time = #{updatedTime},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键作废-->
    <delete id="deleteById">
        delete from bms_pub_fee_subject where id = #{id}
    </delete>


    <!--单条根据费用单编码获取增值费信息-->
    <select id="getBmsPubFeeSubjectInfoByCode"  resultMap="BmsPubFeeSubjectMap">
        select s.id, s.item_code, s.item_name, s.father_id, s.level, s.del_flag, s.oper_by, s.oper_code, s.oper_time
        from pub_fee_subject  s
        left join pub_fee_subject st on st.id = s.father_id and st.`level` = 1  and st.del_flag = '0'
        where s.del_flag = '0' and  s.`level` = 2
        and st.item_name  = #{levelName}
        and s.item_name = #{level2Name}
    </select>

    <select id="selectPubFeeinfoList" resultType="com.bbyb.joy.bms.domain.dto.BmsExpenseItemInfo">
        select id, item_code as itemCode,item_name as itemName,father_id as fatherId, level, del_flag as delFlag,oper_by as operBy,oper_code as operCode,oper_time as operTime,show_type as showType from pub_fee_subject
        <where>
            <if test="ids != null and ids.size>0 ">
                AND id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="codes != null and codes.size>0 ">
                AND item_code in
                <foreach collection="codes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="names != null and names.size>0 ">
                AND item_name in
                <foreach collection="names" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
            </if>
            <if test="level != null and level != '' ">
                AND level = #{level}
            </if>
            <if test="showType != null and showType != '' ">
                AND show_type = #{showType}
            </if>
        </where>
    </select>


</mapper>