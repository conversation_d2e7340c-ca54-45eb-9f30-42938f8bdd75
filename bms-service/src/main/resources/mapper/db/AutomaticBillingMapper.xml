<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.AutomaticBillingMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.charginglogic.Quoterule" id="QuoteruleResult">
        <result property="id"    column="id"    />
        <result property="detailRulecode"    column="detailRulecode"    />
        <result property="ruleCode"    column="rule_code"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="relationId"    column="relation_id"    />
        <result property="userCompanyId"    column="user_company_id"    />
        <result property="projectQuotation"    column="project_quotation"    />
        <result property="clientIdstr"    column="client_idstr"    />
        <result property="chargebyWeight"    column="chargeby_weight"    />
        <result property="chargebyVolume"    column="chargeby_volume"    />
        <result property="decimalPoint"    column="decimal_point"    />
        <result property="detailId"    column="detailId"    />
        <result property="feeType"    column="fee_type"    />
        <result property="quoteruleTemplateId"    column="quoterule_template_id"    />
        <result property="templateRuleCode"    column="rule_code"    />
        <result property="templateRuleName"    column="rule_name"    />
        <result property="templateRuleType"    column="rule_type"    />
        <result property="templateConsolidationRule"    column="consolidation_rule"    />
        <result property="templateGroupRule"    column="group_rule"    />
        <result property="templateCalculationFormulaCode"    column="calculation_formula_code"    />
        <result property="templateMatchingConditionsCode"    column="matching_conditions_code"    />
        <result property="templateCalculationProcessCode"    column="calculation_process_code"    />
        <result property="clientId"    column="client_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="mainEndTime"    column="mainEndTime"    />
        <result property="billType" column="bill_type" />
        <result property="feeTypeStr" column="fee_type_str" />
        <result property="auditState" column="auditState" />
        <result property="delFlag" column="del_flag" />
        <result property="settleType" column="settle_type" />
        <result property="settleSetting" column="settle_setting"/>
        <result property="weightValue" column="weight_value" />
        <result property="shareType" column="shareType" />
    </resultMap>

    <!--    根据客户id/承运商id 查询报价规则明细-->
    <select id="selectQuoteruleList"  resultMap="QuoteruleResult">
        SELECT
            q.id,
            q.rule_code,
            q.rule_name,
            q.relation_id,
            q.user_company_id,
            q.project_quotation,
            q.client_idstr,
            q.chargeby_weight,
            q.chargeby_volume,
            q.decimal_point,
            d.id AS detailId,
            d.fee_type,
            d.quoterule_template_id,
            qt.rule_code,
            qt.rule_name,
            qt.consolidation_rule,
            qt.group_rule,
            qt.calculation_formula_code,
            qt.matching_conditions_code,
            qt.calculation_process_code,
            d.rulecode detailRulecode,
            d.client_id,
            d.start_time,
            d.end_time,
            d.bill_type,
            d.fee_type_str,
            q.rule_type,
            CASE
                WHEN q.rule_type = 1
                    THEN t4.carrierpay_type
                WHEN q.rule_type = 2
                    THEN t5.carrier_paytype
            END AS settle_type,
            CASE
                WHEN q.rule_type = 1
                    THEN t4.settle_setting
                WHEN q.rule_type = 2
                    THEN t5.settle_setting
            END AS settle_setting,
            CASE
                WHEN q.rule_type = 1
                    THEN t4.share_type
                WHEN q.rule_type = 2
                    THEN t5.share_type
            END AS shareType,
            d.audit_state AS auditState
        FROM pub_quoterule q
        LEFT JOIN pub_quoterule_detail d ON q.id=d.quoterule_id
        LEFT JOIN pub_quoterule_template qt ON qt.id=d.quoterule_template_id
        LEFT JOIN bms_clientinfo t4 ON t4.id = q.relation_id
        LEFT JOIN bms_carrierinfo t5 ON t5.id = q.relation_id
        <where>
            q.del_flag=0
            AND q.is_enable=0
            AND q.business_type=#{businessType}
            AND d.del_flag=0
            AND d.is_enable=0
            AND d.bill_type=#{billType}
            AND qt.del_flag=0
            AND qt.is_enable=0
            AND qt.calculation_formula_code &lt;&gt;''
            AND qt.matching_conditions_code &lt;&gt;''
            AND q.rule_type=#{ruleType}
            <if test="billType != null and billType==4">
                AND d.is_calculated = 0
            </if>
            <if test="relationIDS != null and relationIDS.size>0">
                AND q.relation_id IN
                <foreach item="id" collection="relationIDS" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="ruleType==1" >
                AND DATE_FORMAT(d.end_time,'%Y-%m-%d') &gt;= #{startDate}
                AND d.audit_state = 2
            </if>
            <if test="ruleType==2" >
                AND DATE_FORMAT(q.end_time,'%Y-%m-%d') &gt;= #{startDate}
            </if>
            <if test="rulecode!=null" >
                AND d.rulecode = #{rulecode}
            </if>
            ORDER BY q.project_quotation DESC
        </where>
    </select>


    <!--    根据客户id/承运商id 查询报价规则明细-->
    <select id="selectQuoteruleValueAddedList"  resultMap="QuoteruleResult">
        select  q.id,q.rule_code,q.rule_name,q.relation_id,q.user_company_id,q.project_quotation,
        q.client_idstr,q.chargeby_weight,q.chargeby_volume,q.decimal_point,
        d.id as detailId,d.fee_type,d.quoterule_template_id,
        qt.rule_code,qt.rule_name,qt.consolidation_rule,
        qt.group_rule,qt.calculation_formula_code,
        qt.matching_conditions_code,qt.calculation_process_code,
        d.rulecode detailRulecode,d.client_id
        from pub_quoterule q
        left join pub_quoterule_detail d on q.id=d.quoterule_id
        left join pub_quoterule_template qt on qt.id=d.quoterule_template_id
        <where>
            q.del_flag=0 and q.is_enable=0 and q.business_type=#{businessType}
            and d.del_flag=0 and d.is_enable=0  and d.bill_type=#{billType}
            and qt.del_flag=0 and qt.is_enable=0 and qt.calculation_formula_code &lt;&gt;'' and qt.matching_conditions_code &lt;&gt;''
            and IFNULL(d.is_calculated,0)=1 /** 查询不过滤已计费的报价*/
            and q.rule_type=#{ruleType}
            <!--and q.relation_id in
            <foreach item="id" collection="relationIDS" open="(" separator="," close=")">
                #{id}
            </foreach>-->
            <if test="ruleType==1" >
                and d.start_time &lt;=timestamp(date_add(curdate(), interval - 1 day)) and d.end_time &gt;= timestamp(date_add(curdate(), interval - 1 day))
            </if>
            <if test="ruleType==2" >
                and q.start_time &lt;=timestamp(date_add(curdate(), interval - 1 day)) and q.end_time &gt;= timestamp(date_add(curdate(), interval - 1 day))
            </if>

            <if test="rulecode!=null" >
                and d.rulecode = #{rulecode}
            </if>

            order by q.project_quotation desc

        </where>
    </select>

    <!--    根据客户id/承运商id 查询报价规则明细-->
    <select id="selectQuoteruleValueAddedServiceList"  resultMap="QuoteruleResult">
        select  q.id,q.rule_code,q.rule_name,q.relation_id,q.user_company_id,q.project_quotation,
        q.client_idstr,q.chargeby_weight,q.chargeby_volume,q.decimal_point,
        d.id as detailId,d.fee_type,d.quoterule_template_id,
        qt.rule_code,qt.rule_name,qt.consolidation_rule,
        qt.group_rule,qt.calculation_formula_code,
        qt.matching_conditions_code,qt.calculation_process_code,
        d.rulecode detailRulecode,d.client_id,d.item_id as itemId,
        d.bill_type
        from pub_quoterule q
        left join pub_quoterule_detail d on q.id=d.quoterule_id
        left join pub_quoterule_template qt on qt.id=d.quoterule_template_id
        <where>
            q.del_flag=0 and q.is_enable=0
            and d.del_flag=0 and d.is_enable=0
            and qt.del_flag=0 and qt.is_enable=0 and qt.calculation_formula_code &lt;&gt;'' and qt.matching_conditions_code &lt;&gt;''
            and q.rule_type=#{ruleType}
            <if test="ruleType==1" >
                <!--                and d.start_time &lt;=timestamp(date_add(curdate(), interval - 1 day)) and d.end_time &gt;= timestamp(date_add(curdate(), interval - 1 day))-->
                and DATE_FORMAT(d.end_time,'%Y-%m-%d') &gt;= #{startDate}
            </if>
            <if test="ruleType==2" >
                <!--                and q.start_time &lt;=timestamp(date_add(curdate(), interval - 1 day)) and q.end_time &gt;= timestamp(date_add(curdate(), interval - 1 day))-->
                and DATE_FORMAT(q.end_time,'%Y-%m-%d') &gt;= #{startDate}
            </if>

            <if test="rulecode!=null" >
                and d.rulecode = #{rulecode}
            </if>
            <!--运输报价-->
            <if test="businessType!=null and businessType==1" >
                and d.bill_type = 6
            </if>
            <!--仓储报价-->
            <if test="businessType!=null and businessType==2" >
                and d.bill_type = 5
            </if>
            <if test="businessType!=null and businessType==3" >
                and d.bill_type in (5,6)
                and q.business_type=#{businessType}
            </if>

            order by q.project_quotation desc

        </where>
    </select>

    <resultMap type="com.bbyb.joy.bms.domain.dto.PubQuoteruleTemplate" id="PubQuoteruleTemplateResult">
        <result property="id"    column="id"    />
        <result property="ruleCode"    column="rule_code"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="consolidationRule"    column="consolidation_rule"    />
        <result property="groupRule"    column="group_rule"    />
        <result property="calculationFormulaCode"    column="calculation_formula_code"    />
        <result property="matchingConditionsCode"    column="matching_conditions_code"    />
    </resultMap>
    <!--    根据id 查询规则模板-->
    <select id="selectQuoteruleTemplateList"  resultMap="PubQuoteruleTemplateResult">
        select  q.id,q.rule_code,q.rule_name,q.rule_type,q.consolidation_rule,q.group_rule,q.calculation_formula_code,q.matching_conditions_code
        from pub_quoterule_template q
        <where>
            q.del_flag=0
            and q.is_enable=0
            and q.id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>

        </where>
    </select>



    <!-- 批量新增自动计费错误日志表  pub_automatic_log  -->
    <insert id="insertPubAutomaticLogBatch" parameterType="com.bbyb.joy.bms.domain.dto.charginglogic.PubAutomaticLog" >
        INSERT INTO pub_automatic_log
        (
        time, module,content, `order`,remark
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.time}, #{item.module},#{item.content}, #{item.order}, #{item.remark}
            )
        </foreach>
    </insert>

    <select id="selectQuoteruleIncludeExpireList" resultMap="QuoteruleResult">
        select  q.id,q.rule_code,q.rule_name,q.relation_id,q.user_company_id,q.project_quotation,
        q.client_idstr,q.chargeby_weight,q.chargeby_volume,q.decimal_point,
        d.id as detailId,d.fee_type,d.quoterule_template_id,
        qt.rule_code,qt.rule_name,qt.consolidation_rule,
        qt.group_rule,qt.calculation_formula_code,
        qt.matching_conditions_code,qt.calculation_process_code,
        d.rulecode detailRulecode,d.client_id,d.start_time,d.end_time,q.end_time as mainEndTime
        ,d.audit_state as auditState,d.del_flag
        from pub_quoterule q
        left join pub_quoterule_detail d on q.id=d.quoterule_id
        left join pub_quoterule_template qt on qt.id=d.quoterule_template_id
        <where>
            q.del_flag=0 and q.business_type=#{businessType}
            and q.rule_type=#{ruleType}
            and d.bill_type=#{billType}
            and NOT ((d.start_time &gt; #{endDate} OR d.end_time &lt; #{startDate}) and d.audit_state !=2)
            and q.relation_id in
            <foreach item="id" collection="relationIDS" open="(" separator="," close=")">
                #{id}
            </foreach>
            order by q.project_quotation desc
        </where>
    </select>
</mapper>