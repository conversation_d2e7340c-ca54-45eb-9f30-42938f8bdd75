<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfcostInfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYfcostInfo" id="BmsYfcostInfoResult">
        <result property="id"    column="id"    />
        <result property="pkId"    column="pk_id"    />
        <result property="expensesCode"    column="expenses_code"    />
        <result property="businessType"    column="business_type"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="companyId"    column="company_id"    />
        <result property="expensesType"    column="expenses_type"    />
        <result property="costDimension"    column="cost_dimension"    />
        <result property="chargeType"    column="charge_type"    />
        <result property="feeFlag"    column="fee_flag"    />
        <result property="quoteruleId"    column="quoterule_id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="remarks"    column="remarks"    />
        <result property="freight"    column="freight"    />
        <result property="deliveryFee"    column="delivery_fee"    />
        <result property="superframesFee"    column="superframes_fee"    />
        <result property="excessFee"    column="excess_fee"    />
        <result property="shortbargeFee"    column="shortbarge_fee"    />
        <result property="returnFee"    column="return_fee"    />
        <result property="ultrafarFee"    column="ultrafar_fee"    />
        <result property="outboundsortingFee"    column="Outboundsorting_fee"    />
        <result property="exceptionFee"    column="exception_fee"    />
        <result property="otherCost1"    column="other_cost1"    />
        <result property="otherCost2"    column="other_cost2"    />
        <result property="otherCost3"    column="other_cost3"    />
        <result property="otherCost4"    column="other_cost4"    />
        <result property="otherCost5"    column="other_cost5"    />
        <result property="otherCost6"    column="other_cost6"    />
        <result property="otherCost7"    column="other_cost7"    />
        <result property="otherCost8"    column="other_cost8"    />
        <result property="otherCost9"    column="other_cost9"    />
        <result property="otherCost10"    column="other_cost10"    />
        <result property="otherCost11"    column="other_cost11"    />
        <result property="otherCost12"    column="other_cost12"    />
        <result property="costAttribute"    column="cost_attribute"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="billId"    column="bill_id"    />
        <result property="billDate"    column="bill_date"    />
        <result property="businessTime"    column="business_time"    />
        <result property="settleType"    column="settle_type"    />
        <result property="settleAmount"    column="settle_amount"    />
        <result property="mainExpenseId"    column="main_expense_id"    />
        <result property="settleMainId"    column="settle_main_id"    />
        <result property="settleMainName"    column="settle_main_name"    />
        <result property="feeTypeFirst"    column="fee_type_first"    />
            <result property="automaticBillingRemark"    column="automatic_billing_remark"    />
    </resultMap>

    <sql id="selectBmsYfcostInfoVo">
        SELECT
            t1.pk_id,
            t1.id,
            t1.expenses_code,
            t1.carrier_id,
            t2.carrier_code,
            t2.carrier_name,
            t1.company_id,
            t1.expenses_type,
            t1.expenses_dimension cost_dimension,
            t1.charge_type,
            t1.expenses_mark fee_flag,
            t1.quoter_rule_id quoterule_id,
            t1.quoter_rule_name rule_name,
            t1.remarks,
            t1.freight,
            t1.delivery_fee,
            t1.superframes_fee,
            t1.excess_fee,
            t1.reduce_fee,
            t1.shortbarge_fee,
            t1.return_fee,
            t1.ultrafar_fee,
            t1.outboundsorting_fee,
            t1.exception_fee,
            t1.adjust_fee,
            t1.adjust_remark,
            t1.other_cost1,
            t1.other_cost2,
            t1.other_cost3,
            t1.other_cost4,
            t1.other_cost5,
            t1.other_cost6,
            t1.other_cost7,
            t1.other_cost8,
            t1.other_cost9,
            t1.other_cost10,
            t1.other_cost11,
            t1.other_cost12,
            t1.oper_code,
            t1.oper_by,
            t1.oper_time,
            t1.del_flag,
            t1.bill_id,
            t1.bill_date,
            t1.business_time,
            t1.client_id,
            t1.fee_type_first,
            t1.is_increment,
            t1.dispatch_date,
            t1.finish_date,
            t1.show_bill_code,
            t1.extra_field1,
            t1.warehouse_code warehouse_code_arr,
            t1.settle_type,
            t1.settle_amount,
            t1.main_code_id main_expense_id,
            t1.settle_main_id
        FROM bms_yfcost_info t1
        LEFT JOIN bms_carrierinfo t2 ON t2.id = t1.carrier_id
    </sql>

    <select id="selectBmsYfcostInfoList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfcostInfo" resultMap="BmsYfcostInfoResult">
        <include refid="selectBmsYfcostInfoVo"/>
        <where>
            <if test="expensesCode != null  and expensesCode != ''"> and t1.expenses_code = #{expensesCode}</if>
            <if test="businessType != null "> and t1.business_type = #{businessType}</if>
            <if test="carrierCode != null  and carrierCode != ''"> and t2.carrier_code = #{carrierCode}</if>
            <if test="carrierName != null  and carrierName != ''"> and t2.carrier_name like concat('%', #{carrierName}, '%')</if>
            <if test="companyId != null "> and t1.company_id = #{companyId}</if>
            <if test="expensesType != null "> and t1.expenses_type = #{expensesType}</if>
            <if test="costDimension != null "> and t1.expenses_dimension = #{costDimension}</if>
            <if test="chargeType != null "> and t1.charge_type = #{chargeType}</if>
            <if test="feeFlag != null "> and t1.expenses_mark = #{feeFlag}</if>
            <if test="quoteruleId != null "> and t1.quoter_rule_id = #{quoteruleId}</if>
            <if test="ruleName != null  and ruleName != ''"> and t1.quoter_rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="remarks != null  and remarks != ''"> and t1.remarks = #{remarks}</if>
            <if test="freight != null "> and t1.freight = #{freight}</if>
            <if test="deliveryFee != null "> and t1.delivery_fee = #{deliveryFee}</if>
            <if test="superframesFee != null "> and t1.superframes_fee = #{superframesFee}</if>
            <if test="excessFee != null "> and t1.excess_fee = #{excessFee}</if>
            <if test="shortbargeFee != null "> and t1.shortbarge_fee = #{shortbargeFee}</if>
            <if test="returnFee != null "> and t1.return_fee = #{returnFee}</if>
            <if test="ultrafarFee != null "> and t1.ultrafar_fee = #{ultrafarFee}</if>
            <if test="outboundsortingFee != null "> and t1.outboundsorting_fee = #{outboundsortingFee}</if>
            <if test="exceptionFee != null "> and t1.exception_fee = #{exceptionFee}</if>
            <if test="otherCost1 != null "> and t1.other_cost1 = #{otherCost1}</if>
            <if test="otherCost2 != null "> and t1.other_cost2 = #{otherCost2}</if>
            <if test="otherCost3 != null "> and t1.other_cost3 = #{otherCost3}</if>
            <if test="otherCost4 != null "> and t1.other_cost4 = #{otherCost4}</if>
            <if test="otherCost5 != null "> and t1.other_cost5 = #{otherCost5}</if>
            <if test="otherCost6 != null "> and t1.other_cost6 = #{otherCost6}</if>
            <if test="otherCost7 != null "> and t1.other_cost7 = #{otherCost7}</if>
            <if test="otherCost8 != null "> and t1.other_cost8 = #{otherCost8}</if>
            <if test="otherCost9 != null "> and t1.other_cost9 = #{otherCost9}</if>
            <if test="otherCost10 != null "> and t1.other_cost10 = #{otherCost10}</if>
            <if test="otherCost11 != null "> and t1.other_cost11 = #{otherCost11}</if>
            <if test="otherCost12 != null "> and t1.other_cost12 = #{otherCost12}</if>
            <if test="costAttribute != null "> and t1.cost_attribute = #{costAttribute}</if>
            <if test="operCode != null  and operCode != ''"> and t1.and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and t1.oper_by = #{operBy}</if>
            <if test="operTime != null "> and t1.oper_time = #{operTime}</if>
            <if test="billId != null "> and t1.bill_id = #{billId}</if>
            <if test="billDate != null  and billDate != ''"> and t1.bill_date = #{billDate}</if>
            <if test="businessTime != null "> and business_time = #{businessTime}</if>
        </where>
    </select>

    <select id="selectBmsYfCostInfoListById"  resultMap="BmsYfcostInfoResult">
        <include refid="selectBmsYfcostInfoVo"/>
        WHERE t1.del_flag = 0 AND t1.id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectBmsYfCostInfoListByExpenseMainId"  resultMap="BmsYfcostInfoResult">
        <include refid="selectBmsYfcostInfoVo"/>
        WHERE t1.del_flag = 0 AND t1.main_code_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="selectBmsYfCostMainInfoListByID"  resultMap="BmsYfcostInfoResult">
        <include refid="selectBmsYfcostInfoVo"/>
        WHERE t1.del_flag = 0 AND t1.main_code_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="getCostInfoByCode"  resultMap="BmsYfcostInfoResult">
        <include refid="selectBmsYfcostInfoVo"/>
        WHERE t1.del_flag = 0 AND t1.expenses_code = #{code}
    </select>

    <select id="selectBmsYfcostInfoById" parameterType="java.lang.String" resultMap="BmsYfcostInfoResult">
        <include refid="selectBmsYfcostInfoVo"/>
        WHERE t1.pk_id = #{pkId}
    </select>

    <insert id="insertBmsYfcostInfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfcostInfo" useGeneratedKeys="true" keyProperty="pkId">
        INSER INTO bms_yfcost_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="expensesCode != null">
                expenses_code,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="carrierCode != null">
                carrier_code,
            </if>
            <if test="carrierName != null">
                carrier_name,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="expensesType != null">
                expenses_type,
            </if>
            <if test="costDimension != null">
                cost_dimension,
            </if>
            <if test="chargeType != null">
                charge_type,
            </if>
            <if test="feeFlag != null">
                fee_flag,
            </if>
            <if test="quoteruleId != null">
                quoterule_id,
            </if>
            <if test="ruleName != null">
                rule_name,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="freight != null">
                freight,
            </if>
            <if test="deliveryFee != null">
                delivery_fee,
            </if>
            <if test="superframesFee != null">
                superframes_fee,
            </if>
            <if test="excessFee != null">
                excess_fee,
            </if>
            <if test="reduceFee != null">
                reduce_fee,
            </if>
            <if test="shortbargeFee != null">
                shortbarge_fee,
            </if>
            <if test="returnFee != null">
                return_fee,
            </if>
            <if test="ultrafarFee != null">
                ultrafar_fee,
            </if>
            <if test="outboundsortingFee != null">
                outboundsorting_fee,
            </if>
            <if test="exceptionFee != null">
                exception_fee,
            </if>
            <if test="adjustFee != null">
                adjust_fee,
            </if>
            <if test="adjustRemark != null">
                adjust_remark,
            </if>
            <if test="otherCost1 != null">
                other_cost1,
            </if>
            <if test="otherCost2 != null">
                other_cost2,
            </if>
            <if test="otherCost3 != null">
                other_cost3,
            </if>
            <if test="otherCost4 != null">
                other_cost4,
            </if>
            <if test="otherCost5 != null">
                other_cost5,
            </if>
            <if test="otherCost6 != null">
                other_cost6,
            </if>
            <if test="otherCost7 != null">
                other_cost7,
            </if>
            <if test="otherCost8 != null">
                other_cost8,
            </if>
            <if test="otherCost9 != null">
                other_cost9,
            </if>
            <if test="otherCost10 != null">
                other_cost10,
            </if>
            <if test="otherCost11 != null">
                other_cost11,
            </if>
            <if test="otherCost12 != null">
                other_cost12,
            </if>
            <if test="costAttribute != null">
                cost_attribute,
            </if>
            <if test="operCode != null">
                oper_code,
            </if>
            <if test="operBy != null">
                oper_by,
            </if>
            <if test="operTime != null">
                oper_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="billId != null">
                bill_id,
            </if>
            <if test="billDate != null">
                bill_date,
            </if>
            <if test="businessTime != null">
                business_time,
            </if>
            <if test="overNum != null">
                over_num,
            </if>
            <if test="overSendnum != null">
                over_sendnum,
            </if>
            <if test="storageFeePrice != null">
                storage_fee_price,
            </if>
            <if test="disposalFeePrice != null">
                disposal_fee_price,
            </if>
            <if test="otherFeeRemark != null">
                other_fee_remark,
            </if>
            <if test="clientId != null">
                client_id,
            </if>
            <if test="feeTypeFirst != null">
                fee_type_first,
            </if>
            <if test="feeCreateAte != null">
                fee_create_ate,
            </if>
            <if test="isIncrement != null">
                is_increment,
            </if>
            <if test="dispatchDate != null">
                dispatch_date,
            </if>
            <if test="finishDate != null">
                finish_date,
            </if>
            <if test="showBillCode != null">
                show_bill_code,
            </if>
            <if test="extraField1 != null">
                extra_field1,
            </if>
            <if test="warehouseCodeArr != null">
                warehouse_code_arr,
            </if>
            <if test="settleType != null">
                settle_type,
            </if>
            <if test="settleAmount != null">
                settle_amount,
            </if>
            <if test="mainExpenseId != null">
                main_expense_id,
            </if>
            <if test="settleMainId != null">
                settle_main_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="expensesCode != null">
                #{expensesCode},
            </if>
            <if test="businessType != null">
                #{businessType},
            </if>
            <if test="carrierCode != null">
                #{carrierCode},
            </if>
            <if test="carrierName != null">
                #{carrierName},
            </if>
            <if test="companyId != null">
                #{companyId},
            </if>
            <if test="expensesType != null">
                #{expensesType},
            </if>
            <if test="costDimension != null">
                #{costDimension},
            </if>
            <if test="chargeType != null">
                #{chargeType},
            </if>
            <if test="feeFlag != null">
                #{feeFlag},
            </if>
            <if test="quoteruleId != null">
                #{quoteruleId},
            </if>
            <if test="ruleName != null">
                #{ruleName},
            </if>
            <if test="remarks != null">
                #{remarks},
            </if>
            <if test="freight != null">
                #{freight},
            </if>
            <if test="deliveryFee != null">
                #{deliveryFee},
            </if>
            <if test="superframesFee != null">
                #{superframesFee},
            </if>
            <if test="excessFee != null">
                #{excessFee},
            </if>
            <if test="reduceFee != null">
                #{reduceFee},
            </if>
            <if test="shortbargeFee != null">
                #{shortbargeFee},
            </if>
            <if test="returnFee != null">
                #{returnFee},
            </if>
            <if test="ultrafarFee != null">
                #{ultrafarFee},
            </if>
            <if test="outboundsortingFee != null">
                #{outboundsortingFee},
            </if>
            <if test="exceptionFee != null">
                #{exceptionFee},
            </if>
            <if test="adjustFee != null">
                #{adjustFee},
            </if>
            <if test="adjustRemark != null">
                #{adjustRemark},
            </if>
            <if test="otherCost1 != null">
                #{otherCost1},
            </if>
            <if test="otherCost2 != null">
                #{otherCost2},
            </if>
            <if test="otherCost3 != null">
                #{otherCost3},
            </if>
            <if test="otherCost4 != null">
                #{otherCost4},
            </if>
            <if test="otherCost5 != null">
                #{otherCost5},
            </if>
            <if test="otherCost6 != null">
                #{otherCost6},
            </if>
            <if test="otherCost7 != null">
                #{otherCost7},
            </if>
            <if test="otherCost8 != null">
                #{otherCost8},
            </if>
            <if test="otherCost9 != null">
                #{otherCost9},
            </if>
            <if test="otherCost10 != null">
                #{otherCost10},
            </if>
            <if test="otherCost11 != null">
                #{otherCost11},
            </if>
            <if test="otherCost12 != null">
                #{otherCost12},
            </if>
            <if test="costAttribute != null">
                #{costAttribute},
            </if>
            <if test="operCode != null">
                #{operCode},
            </if>
            <if test="operBy != null">
                #{operBy},
            </if>
            <if test="operTime != null">
                #{operTime},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="billId != null">
                #{billId},
            </if>
            <if test="billDate != null">
                #{billDate},
            </if>
            <if test="businessTime != null">
                #{businessTime},
            </if>
            <if test="overNum != null">
                #{overNum},
            </if>
            <if test="overSendnum != null">
                #{overSendnum},
            </if>
            <if test="storageFeePrice != null">
                #{storageFeePrice},
            </if>
            <if test="disposalFeePrice != null">
                #{disposalFeePrice},
            </if>
            <if test="otherFeeRemark != null">
                #{otherFeeRemark},
            </if>
            <if test="clientId != null">
                #{clientId},
            </if>
            <if test="feeTypeFirst != null">
                #{feeTypeFirst},
            </if>
            <if test="feeCreateAte != null">
                #{feeCreateAte},
            </if>
            <if test="isIncrement != null">
                #{isIncrement},
            </if>
            <if test="dispatchDate != null">
                #{dispatchDate},
            </if>
            <if test="finishDate != null">
                #{finishDate},
            </if>
            <if test="showBillCode != null">
                #{showBillCode},
            </if>
            <if test="extraField1 != null">
                #{extraField1},
            </if>
            <if test="warehouseCodeArr != null">
                #{warehouseCodeArr},
            </if>
            <if test="settleType != null">
                #{settleType},
            </if>
            <if test="settleAmount != null">
                #{settleAmount},
            </if>
            <if test="mainExpenseId != null">
                #{mainExpenseId},
            </if>
            <if test="settleMainId != null">
                #{settleMainId},
            </if>
        </trim>
    </insert>

    <update id="updateBmsYfcostInfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfcostInfo">
        UPDATE bms_yfcost_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="expensesCode != null">expenses_code = #{expensesCode},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="carrierCode != null">carrier_code = #{carrierCode},</if>
            <if test="carrierName != null">carrier_name = #{carrierName},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="expensesType != null">expenses_type = #{expensesType},</if>
            <if test="costDimension != null">cost_dimension = #{costDimension},</if>
            <if test="chargeType != null">charge_type = #{chargeType},</if>
            <if test="feeFlag != null">fee_flag = #{feeFlag},</if>
            <if test="quoteruleId != null">quoterule_id = #{quoteruleId},</if>
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="adjustRemark != null">adjust_remark = #{adjustRemark},</if>
            <if test="freight != null">freight = #{freight},</if>
            <if test="deliveryFee != null">delivery_fee = #{deliveryFee},</if>
            <if test="superframesFee != null">superframes_fee = #{superframesFee},</if>
            <if test="excessFee != null">excess_fee = #{excessFee},</if>
            <if test="shortbargeFee != null">shortbarge_fee = #{shortbargeFee},</if>
            <if test="returnFee != null">return_fee = #{returnFee},</if>
            <if test="ultrafarFee != null">ultrafar_fee = #{ultrafarFee},</if>
            <if test="outboundsortingFee != null">Outboundsorting_fee = #{outboundsortingFee},</if>
            <if test="exceptionFee != null">exception_fee = #{exceptionFee},</if>
            <if test="otherCost1 != null">other_cost1 = #{otherCost1},</if>
            <if test="otherCost2 != null">other_cost2 = #{otherCost2},</if>
            <if test="otherCost3 != null">other_cost3 = #{otherCost3},</if>
            <if test="otherCost4 != null">other_cost4 = #{otherCost4},</if>
            <if test="otherCost5 != null">other_cost5 = #{otherCost5},</if>
            <if test="otherCost6 != null">other_cost6 = #{otherCost6},</if>
            <if test="otherCost7 != null">other_cost7 = #{otherCost7},</if>
            <if test="otherCost8 != null">other_cost8 = #{otherCost8},</if>
            <if test="otherCost9 != null">other_cost9 = #{otherCost9},</if>
            <if test="otherCost10 != null">other_cost10 = #{otherCost10},</if>
            <if test="otherCost11 != null">other_cost11 = #{otherCost11},</if>
            <if test="otherCost12 != null">other_cost12 = #{otherCost12},</if>
            <if test="costAttribute != null">cost_attribute = #{costAttribute},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="billId != null">bill_id = #{billId},</if>
            <if test="billDate != null">bill_date = #{billDate},</if>
            <if test="businessTime != null">business_time = #{businessTime},</if>
            <if test="reduceFee != null">reduce_fee = #{reduceFee},</if>
            <if test="warehouseCodeArr != null">warehouse_code_arr = #{warehouseCodeArr},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteBmsYfcostInfoById" parameterType="java.lang.String">
        DELETE FROM bms_yfcost_info WHERE pk_id = #{pkId}
    </delete>

    <delete id="deleteBmsYfcostInfoByIds">
        DELETE FROM bms_yfcost_info WHERE pk_id IN
        <foreach item="pkId" collection="array" open="(" separator="," close=")">
            #{pkId}
        </foreach>
    </delete>

    <update id="updateBmsYfcostInfoStatusByIds">
        UPDATE bms_yfcost_info SET status = #{status} WHERE pk_id IN
        <foreach item="pkId" collection="array" open="(" separator="," close=")">
            #{pkId}
        </foreach>
    </update>

    <update id="updateBillIdById" >
        UPDATE bms_yfcost_info t1
        LEFT JOIN bms_carrierinfo t2 ON t2.id = t1.carrier_id
        SET t1.bill_id = #{billId},
        t1.show_bill_id = #{billId},
        t1.show_bill_code = #{billCode}
        WHERE t1.del_flag = 0
        AND t2.carrier_code =#{carrierCode}
        <if test="companyId!=null">
            AND t1.company_id = #{companyId}
        </if>
        <if test="clientId!=null">
            AND t1.client_id = #{clientId}
        </if>
        AND IFNULL(t1.bill_id,0)=0
        AND IFNULL(t1.expenses_mark,1) = 1
        AND IFNULL(t1.bill_date,'') = #{billDate}
        AND IFNULL(t1.del_flag,'0') = '0'
    </update>

    <update id="updateBillIdYsById" >
        UPDATE bms_yfcost_info t1
        LEFT JOIN bms_carrierinfo t2 ON t2.id = t1.carrier_id
        SET t1.bill_id = #{billId}
        WHERE t1.del_flag = 0
        AND t2.carrier_code =#{carrierCode}
        <if test="companyId!=null">
            AND t1.company_id = #{companyId}
        </if>
        <if test="clientId!=null">
            AND t1.client_id = #{clientId}
        </if>
        AND IFNULL(t1.bill_id,0)=0
        AND IFNULL(t1.expenses_mark,1) = 1
        AND IFNULL(t1.bill_date,'') = #{billDate}
        AND IFNULL(t1.del_flag,0) = 0
        AND IFNULL(t1.expenses_type,0) IN (1)
    </update>

    <update id="updateBillIdCcById" >
        UPDATE bms_yfcost_info a
        LEFT JOIN bms_carrierinfo b ON b.id = a.carrier_id
        SET a.bill_id = #{billId}
        WHERE a.del_flag = 0
        AND b.carrier_code =#{carrierCode}
        <if test="companyId!=null">
            AND a.company_id = #{companyId}
        </if>
        <if test="warehouseCode!=null and warehouseCode!= ''">
            AND a.warehouse_code = #{warehouseCode}
        </if>
        <if test="clientId!=null">
            AND a.client_id = #{clientId}
        </if>
        <if test="clientId == null">
            AND a.client_id IS NULL
        </if>
        AND IFNULL(a.bill_id,0)=0
        AND IFNULL(a.expenses_mark,1) = 1
        AND IFNULL(a.bill_date,'') = #{billDate}
        AND IFNULL(a.del_flag,'0') = '0'
        AND IFNULL(a.expenses_type,0) IN (2,3,4)
    </update>

    <update id="updateBillIdFeeFlagById" >
        UPDATE bms_yfcost_info a
        LEFT JOIN bms_carrierinfo b ON b.id = a.carrier_id
            SET a.bill_id = #{billId}
        WHERE a.del_flag = 0
        AND b.carrier_code =#{carrierCode}
        AND a.company_id = #{companyId}
        AND IFNULL(a.bill_id,0)=0
        AND IFNULL(a.expenses_mark,1) = 2
        AND IFNULL(a.bill_date,'') =#{billDate}
        AND IFNULL(a.del_flag,'0') = '0'
    </update>


    <select id="getcodeListGroupcompanyByClientAndCompany" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        SELECT
        SUM(
        IFNULL(a.freight,0) +
        IFNULL(a.ultrafar_fee,0) +
        IFNULL(a.superframes_fee,0) +
        IFNULL(a.excess_fee,0) +
        IFNULL(a.delivery_fee,0) +
        IFNULL(a.outboundsorting_fee,0) +
        IFNULL(a.shortbarge_fee,0) +
        IFNULL(a.return_fee,0) +
        IFNULL(a.exception_fee,0) +
        IFNULL(a.adjust_fee,0) +
        IFNULL(a.other_cost1,0) +
        IFNULL(a.other_cost2,0) +
        IFNULL(a.other_cost3,0) +
        IFNULL(a.other_cost4,0) +
        IFNULL(a.other_cost5,0) +
        IFNULL(a.other_cost6,0) +
        IFNULL(a.other_cost7,0) +
        IFNULL(a.other_cost8,0) +
        IFNULL(a.other_cost9,0) +
        IFNULL(a.other_cost10,0) +
        IFNULL(a.other_cost11,0) +
        IFNULL(a.other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        a.bill_date as billDate,
        a.company_id as companyId,
        a.client_id as clientId,
        b.client_name as clientName
        FROM bms_yfcost_info a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_carrierinfo d ON d.id = a.carrier_id
        WHERE a.del_flag = 0
        AND d.carrier_code =#{carrierCode}
        AND IFNULL(a.expenses_mark,1) = 1
        <if test="billDate!=null">
            AND IFNULL(a.bill_date,'') =#{billDate}
        </if>
        AND IFNULL(a.del_flag,'0') = '0'
        AND IFNULL(a.bill_id,0)=0
        GROUP BY a.company_id,a.bill_date
    </select>

    <select id="getcodeListGroupcompanyByClient" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        SELECT
        SUM(
        IFNULL(a.freight,0) +
        IFNULL(a.ultrafar_fee,0) +
        IFNULL(a.superframes_fee,0) +
        IFNULL(a.excess_fee,0) +
        IFNULL(a.delivery_fee,0) +
        IFNULL(a.outboundsorting_fee,0) +
        IFNULL(a.shortbarge_fee,0) +
        IFNULL(a.return_fee,0) +
        IFNULL(a.exception_fee,0) +
        IFNULL(a.adjust_fee,0) +
        IFNULL(a.other_cost1,0) +
        IFNULL(a.other_cost2,0) +
        IFNULL(a.other_cost3,0) +
        IFNULL(a.other_cost4,0) +
        IFNULL(a.other_cost5,0) +
        IFNULL(a.other_cost6,0) +
        IFNULL(a.other_cost7,0) +
        IFNULL(a.other_cost8,0) +
        IFNULL(a.other_cost9,0) +
        IFNULL(a.other_cost10,0) +
        IFNULL(a.other_cost11,0) +
        IFNULL(a.other_cost12,0)
        ) AS sumAmt,
        count(1) AS votes,
        a.bill_date AS billDate,
        /*company_id as companyId*/
        '100' AS companyId,
        a.client_id AS clientId,
        b.client_name AS clientName
        FROM bms_yfcost_info a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_carrierinfo d ON d.id = a.carrier_id
        WHERE a.del_flag = 0 AND d.carrier_code = #{carrierCode}
        AND IFNULL(a.expenses_mark,1) = 1
        <if test="billDate!=null">
            AND IFNULL(a.bill_date,'') = #{billDate}
        </if>
        AND IFNULL(a.del_flag,'0') = '0'
        AND IFNULL(a.bill_id,0)=0
        GROUP BY a.bill_date
    </select>


    <select id="getcodeListGroupcompanyByClientAndCompanyAndStock" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        SELECT
        SUM(
        IFNULL(a.freight,0) +
        IFNULL(a.ultrafar_fee,0) +
        IFNULL(a.superframes_fee,0) +
        IFNULL(a.excess_fee,0) +
        IFNULL(a.delivery_fee,0) +
        IFNULL(a.outboundsorting_fee,0) +
        IFNULL(a.shortbarge_fee,0) +
        IFNULL(a.return_fee,0) +
        IFNULL(a.exception_fee,0) +
        IFNULL(a.adjust_fee,0) +
        IFNULL(a.other_cost1,0) +
        IFNULL(a.other_cost2,0) +
        IFNULL(a.other_cost3,0) +
        IFNULL(a.other_cost4,0) +
        IFNULL(a.other_cost5,0) +
        IFNULL(a.other_cost6,0) +
        IFNULL(a.other_cost7,0) +
        IFNULL(a.other_cost8,0) +
        IFNULL(a.other_cost9,0) +
        IFNULL(a.other_cost10,0) +
        IFNULL(a.other_cost11,0) +
        IFNULL(a.other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        a.bill_date as billDate,
        '1' as billType,
        /*company_id as companyId*/
        '100' as companyId,
        a.client_id as clientId,
        b.client_name as clientName
        FROM bms_yfcost_info a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_carrierinfo d ON d.id = a.carrier_id
        WHERE a.del_flag = 0
        AND d.carrier_code =#{carrierCode}
        AND IFNULL(a.expenses_mark,1) = 1
        <if test="billDate!=null">
            AND IFNULL(a.bill_date,'') =#{billDate}
        </if>
        AND IFNULL(a.del_flag,'0') = '0'
        AND IFNULL(a.bill_id,0)=0
        AND a.expenses_type = 1
        GROUP BY a.company_id,a.bill_date

        UNION ALL

        SELECT
        SUM(
        IFNULL(a.freight,0) +
        IFNULL(a.ultrafar_fee,0) +
        IFNULL(a.superframes_fee,0) +
        IFNULL(a.excess_fee,0) +
        IFNULL(a.delivery_fee,0) +
        IFNULL(a.outboundsorting_fee,0) +
        IFNULL(a.shortbarge_fee,0) +
        IFNULL(a.return_fee,0) +
        IFNULL(a.exception_fee,0) +
        IFNULL(a.adjust_fee,0) +
        IFNULL(a.other_cost1,0) +
        IFNULL(a.other_cost2,0) +
        IFNULL(a.other_cost3,0) +
        IFNULL(a.other_cost4,0) +
        IFNULL(a.other_cost5,0) +
        IFNULL(a.other_cost6,0) +
        IFNULL(a.other_cost7,0) +
        IFNULL(a.other_cost8,0) +
        IFNULL(a.other_cost9,0) +
        IFNULL(a.other_cost10,0) +
        IFNULL(a.other_cost11,0) +
        IFNULL(a.other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        a.bill_date as billDate,
        '2' as billType,
        /*company_id as companyId*/
        '100' as companyId,
        a.client_id as clientId,
        b.client_name as clientName
        FROM bms_yfcost_info a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_carrierinfo d ON d.id = a.carrier_id
        WHERE a.del_flag = 0
        AND d.carrier_code = #{carrierCode}
        AND IFNULL(a.expenses_mark,1) = 1
        <if test="billDate!=null">
            AND IFNULL(a.bill_date,'') = #{billDate}
        </if>
        AND IFNULL(a.del_flag,'0') = '0'
        AND IFNULL(a.bill_id,0)=0
        AND a.expenses_type IN (2,3,4)
        GROUP BY a.company_id,a.bill_date
    </select>

    <select id="getcodeListGroupcompanyAndWareByClientAndCompanyAndStock" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        SELECT
        SUM(
        IFNULL(a.freight,0) +
        IFNULL(a.ultrafar_fee,0) +
        IFNULL(a.superframes_fee,0) +
        IFNULL(a.excess_fee,0) +
        IFNULL(a.delivery_fee,0) +
        IFNULL(a.outboundsorting_fee,0) +
        IFNULL(a.shortbarge_fee,0) +
        IFNULL(a.return_fee,0) +
        IFNULL(a.exception_fee,0) +
        IFNULL(a.adjust_fee,0) +
        IFNULL(a.other_cost1,0) +
        IFNULL(a.other_cost2,0) +
        IFNULL(a.other_cost3,0) +
        IFNULL(a.other_cost4,0) +
        IFNULL(a.other_cost5,0) +
        IFNULL(a.other_cost6,0) +
        IFNULL(a.other_cost7,0) +
        IFNULL(a.other_cost8,0) +
        IFNULL(a.other_cost9,0) +
        IFNULL(a.other_cost10,0) +
        IFNULL(a.other_cost11,0) +
        IFNULL(a.other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        a.bill_date as billDate,
        '1' as billType,
        /*company_id as companyId*/
        '100' as companyId,
        a.client_id as clientId,
        b.client_name as clientName,
        '' as warehouseCode,
        '' as warehouseName
        FROM bms_yfcost_info a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_carrierinfo d ON d.id = a.carrier_id
        WHERE a.del_flag = 0
        AND d.carrier_code =#{carrierCode}
        AND IFNULL(a.expenses_mark,1) = 1
        <if test="billDate!=null">
            AND IFNULL(a.bill_date,'') = #{billDate}
        </if>
        AND IFNULL(a.del_flag,'0') = '0'
        AND IFNULL(a.bill_id,0)=0
        AND a.expenses_type = 1
        AND IFNULL(a.client_id,0)!=0
        GROUP BY a.company_id,a.bill_date

        UNION ALL

        SELECT SUM(
        IFNULL(a.freight, 0) +
        IFNULL(a.ultrafar_fee, 0) +
        IFNULL(a.superframes_fee, 0) +
        IFNULL(a.excess_fee, 0) +
        IFNULL(a.delivery_fee, 0) +
        IFNULL(a.outboundsorting_fee, 0) +
        IFNULL(a.shortbarge_fee, 0) +
        IFNULL(a.return_fee, 0) +
        IFNULL(a.exception_fee, 0) +
        IFNULL(a.adjust_fee, 0) +
        IFNULL(a.other_cost1, 0) +
        IFNULL(a.other_cost2, 0) +
        IFNULL(a.other_cost3, 0) +
        IFNULL(a.other_cost4, 0) +
        IFNULL(a.other_cost5, 0) +
        IFNULL(a.other_cost6, 0) +
        IFNULL(a.other_cost7, 0) +
        IFNULL(a.other_cost8, 0) +
        IFNULL(a.other_cost9, 0) +
        IFNULL(a.other_cost10, 0) +
        IFNULL(a.other_cost11, 0) +
        IFNULL(a.other_cost12, 0)
        )                as sumAmt,
        count(1)         as votes,
        a.bill_date      as billDate,
        '2'              as billType,
        /*company_id as companyId*/
        '100'            as companyId,
        a.client_id      as clientId,
        c.client_name    as clientName,
        a.warehouse_code as warehouseCode,
        a.warehouse_name as warehouseName
        FROM bms_yfcost_info a
        LEFT JOIN bms_clientinfo c ON a.client_id = c.id
        LEFT JOIN bms_carrierinfo d ON d.id = a.carrier_id
        WHERE a.del_flag = 0
        AND d.carrier_code =#{carrierCode}
        AND IFNULL(a.expenses_mark,1) = 1
        AND IFNULL(a.client_id,0)!=0
        <if test="billDate!=null">
            AND IFNULL(a.bill_date,'') = #{billDate}
        </if>
        AND IFNULL(a.del_flag,'0') = '0'
        AND IFNULL(a.bill_id,0)=0
        AND a.expenses_type IN (2,3,4)
        GROUP BY a.company_id,a.bill_date,a.warehouse_code
    </select>


    <select id="getcodeListGroupcompanyAndStockByClient" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        select
        SUM(
        IFNULL(a.freight,0) +
        IFNULL(a.ultrafar_fee,0) +
        IFNULL(a.superframes_fee,0) +
        IFNULL(a.excess_fee,0) +
        IFNULL(a.delivery_fee,0) +
        IFNULL(a.outboundsorting_fee,0) +
        IFNULL(a.shortbarge_fee,0) +
        IFNULL(a.return_fee,0) +
        IFNULL(a.exception_fee,0) +
        IFNULL(a.adjust_fee,0) +
        IFNULL(a.other_cost1,0) +
        IFNULL(a.other_cost2,0) +
        IFNULL(a.other_cost3,0) +
        IFNULL(a.other_cost4,0) +
        IFNULL(a.other_cost5,0) +
        IFNULL(a.other_cost6,0) +
        IFNULL(a.other_cost7,0) +
        IFNULL(a.other_cost8,0) +
        IFNULL(a.other_cost9,0) +
        IFNULL(a.other_cost10,0) +
        IFNULL(a.other_cost11,0) +
        IFNULL(a.other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        a.bill_date as billDate,
        '1' as billType,
        "100" as companyId,
        a.client_id as clientId,
        b.client_name as clientName
        FROM bms_yfcost_info a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_carrierinfo d ON d.id = a.carrier_id
        WHERE a.del_flag = 0
        AND d.carrier_code =#{carrierCode}
        AND IFNULL(a.expenses_mark,0) = 0
        <if test="billDate!=null">
            AND IFNULL(a.bill_date,'') =#{billDate}
        </if>
        AND IFNULL(a.bill_id,0)=0
        AND a.expenses_type = 1
        GROUP BY a.bill_date

        UNION ALL

        SELECT
        SUM(
        IFNULL(a.freight,0) +
        IFNULL(a.ultrafar_fee,0) +
        IFNULL(a.superframes_fee,0) +
        IFNULL(a.excess_fee,0) +
        IFNULL(a.delivery_fee,0) +
        IFNULL(a.outboundsorting_fee,0) +
        IFNULL(a.shortbarge_fee,0) +
        IFNULL(a.return_fee,0) +
        IFNULL(a.exception_fee,0) +
        IFNULL(a.adjust_fee,0) +
        IFNULL(a.other_cost1,0) +
        IFNULL(a.other_cost2,0) +
        IFNULL(a.other_cost3,0) +
        IFNULL(a.other_cost4,0) +
        IFNULL(a.other_cost5,0) +
        IFNULL(a.other_cost6,0) +
        IFNULL(a.other_cost7,0) +
        IFNULL(a.other_cost8,0) +
        IFNULL(a.other_cost9,0) +
        IFNULL(a.other_cost10,0) +
        IFNULL(a.other_cost11,0) +
        IFNULL(a.other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        a.bill_date as billDate,
        /*company_id as companyId*/
        '2' as billType,
        '100' as companyId,
        a.client_id as clientId,
        b.client_name as clientName
        FROM bms_yfcost_info a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_carrierinfo d ON d.id = a.carrier_id
        WHERE a.del_flag = 0
        AND d.carrier_code =#{carrierCode}
        AND IFNULL(a.expenses_mark,1) = 1
        <if test="billDate!=null">
            AND IFNULL(a.bill_date,'') =#{billDate}
        </if>
        AND IFNULL(a.del_flag,'0') = '0'
        AND IFNULL(a.bill_id,0)=0
        AND a.expenses_type in (2,3,4)
        GROUP BY a.bill_date
    </select>

    <select id="getcodeListGroupcompanyAndWareByClient" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        select SUM(
        IFNULL(a.freight, 0) +
        IFNULL(a.ultrafar_fee, 0) +
        IFNULL(a.superframes_fee, 0) +
        IFNULL(a.excess_fee, 0) +
        IFNULL(a.delivery_fee, 0) +
        IFNULL(a.outboundsorting_fee, 0) +
        IFNULL(a.shortbarge_fee, 0) +
        IFNULL(a.return_fee, 0) +
        IFNULL(a.exception_fee, 0) +
        IFNULL(a.adjust_fee, 0) +
        IFNULL(a.other_cost1, 0) +
        IFNULL(a.other_cost2, 0) +
        IFNULL(a.other_cost3, 0) +
        IFNULL(a.other_cost4, 0) +
        IFNULL(a.other_cost5, 0) +
        IFNULL(a.other_cost6, 0) +
        IFNULL(a.other_cost7, 0) +
        IFNULL(a.other_cost8, 0) +
        IFNULL(a.other_cost9, 0) +
        IFNULL(a.other_cost10, 0) +
        IFNULL(a.other_cost11, 0) +
        IFNULL(a.other_cost12, 0)
        )             as sumAmt,
        count(1)      as votes,
        a.bill_date   as billDate,
        '1'           as billType,
        a.company_id  as companyId,
        a.client_id   as clientId,
        b.client_name as clientName,
        ''            as warehouseCode,
        ''            as warehouseName
        FROM bms_yfcost_info a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_carrierinfo d ON d.id = a.carrier_id
        WHERE a.del_flag = 0
        AND d.carrier_code =#{carrierCode}
        AND IFNULL(a.expenses_mark,0) = 0
        <if test="billDate!=null">
            AND IFNULL(a.bill_date,'') =#{billDate}
        </if>
        AND IFNULL(a.del_flag,'0') = '0'
        AND IFNULL(a.bill_id,0)=0
        AND a.expenses_type = 1
        GROUP BY a.bill_date

        union all

        select
        SUM(
        IFNULL(a.freight,0) +
        IFNULL(a.ultrafar_fee,0) +
        IFNULL(a.superframes_fee,0) +
        IFNULL(a.excess_fee,0) +
        IFNULL(a.delivery_fee,0) +
        IFNULL(a.outboundsorting_fee,0) +
        IFNULL(a.shortbarge_fee,0) +
        IFNULL(a.return_fee,0) +
        IFNULL(a.exception_fee,0) +
        IFNULL(a.adjust_fee,0) +
        IFNULL(a.other_cost1,0) +
        IFNULL(a.other_cost2,0) +
        IFNULL(a.other_cost3,0) +
        IFNULL(a.other_cost4,0) +
        IFNULL(a.other_cost5,0) +
        IFNULL(a.other_cost6,0) +
        IFNULL(a.other_cost7,0) +
        IFNULL(a.other_cost8,0) +
        IFNULL(a.other_cost9,0) +
        IFNULL(a.other_cost10,0) +
        IFNULL(a.other_cost11,0) +
        IFNULL(a.other_cost12,0)
        ) as sumAmt,
        count(1) as votes,
        a.bill_date as billDate,
        '2' as billType,
        a.company_id as companyId,
        a.client_id as clientId,
        c.client_name as clientName,
        a.warehouse_code as warehouseCode,
        a.warehouse_name as warehouseName
        FROM bms_yfcost_info a
        LEFT JOIN bms_clientinfo c ON a.client_id = c.id
        LEFT JOIN bms_carrierinfo d ON d.id = a.carrier_id
        WHERE a.del_flag = 0
        AND d.carrier_code =#{carrierCode}
        AND IFNULL(a.expenses_mark,1) = 1
        <if test="billDate!=null">
            AND IFNULL(a.bill_date,'') =#{billDate}
        </if>
        AND IFNULL(a.del_flag,'0') = '0'
        AND IFNULL(a.bill_id,0)=0
        AND a.expenses_type in (2,3,4)
        group by a.bill_date,a.warehouse_code
    </select>


    <select id="getcodeFeeFlagList" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto">
        select
            SUM(
            IFNULL(a.freight,0) +
            IFNULL(a.ultrafar_fee,0) +
            IFNULL(a.superframes_fee,0) +
            IFNULL(a.excess_fee,0) +
            IFNULL(a.delivery_fee,0) +
            IFNULL(a.outboundsorting_fee,0) +
            IFNULL(a.shortbarge_fee,0) +
            IFNULL(a.return_fee,0) +
            IFNULL(a.exception_fee,0) +
            IFNULL(a.adjust_fee,0) +
            IFNULL(a.other_cost1,0) +
            IFNULL(a.other_cost2,0) +
            IFNULL(a.other_cost3,0) +
            IFNULL(a.other_cost4,0) +
            IFNULL(a.other_cost5,0) +
            IFNULL(a.other_cost6,0) +
            IFNULL(a.other_cost7,0) +
            IFNULL(a.other_cost8,0) +
            IFNULL(a.other_cost9,0) +
            IFNULL(a.other_cost10,0) +
            IFNULL(a.other_cost11,0) +
            IFNULL(a.other_cost12,0)
            ) as sumAmt,
            count(1) as votes,
            a.bill_date as billDate,
            a.client_id as clientId,
            b.client_name as clientName,
            a.company_id as companyId
        FROM bms_yfcost_info a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_carrierinfo c ON c.id = a.carrier_id
        WHERE a.del_flag = 0
        AND c.carrier_code =#{carrierCode}
        AND IFNULL(a.expenses_mark,1) = 2
        <if test="billDate!=null">
            AND IFNULL(a.bill_date,'') =#{billDate}
        </if>
        AND IFNULL(a.del_flag,'0') = '0'
        AND IFNULL(a.bill_id,0)=0

    </select>

    <update id="refreshYfCostBillDate">
        <if test="billDate!=null and billDate!=''">
            UPDATE bms_yfcost_info a
            SET a.bill_date =#{billDate}
            WHERE a.del_flag=0
            AND IFNULL(a.bill_id,0)=0
            AND a.id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>




    <select id="queryCostByMainCostId" resultMap="BmsYfcostInfoResult" >
        SELECT
            t1.pk_id,
            t1.id,
            t1.expenses_code,
            t1.carrier_id,
            t2.carrier_code,
            t2.carrier_name,
            t1.company_id,
            t1.expenses_type,
            t1.expenses_dimension cost_dimension,
            t1.charge_type,
            t1.expenses_mark fee_flag,
            t1.quoter_rule_id quoterule_id,
            t1.quoter_rule_name rule_name,
            t1.remarks,
            t1.freight,
            t1.delivery_fee,
            t1.superframes_fee,
            t1.excess_fee,
            t1.reduce_fee,
            t1.shortbarge_fee,
            t1.return_fee,
            t1.ultrafar_fee,
            t1.outboundsorting_fee,
            t1.exception_fee,
            t1.adjust_fee,
            t1.adjust_remark,
            t1.other_cost1,
            t1.other_cost2,
            t1.other_cost3,
            t1.other_cost4,
            t1.other_cost5,
            t1.other_cost6,
            t1.other_cost7,
            t1.other_cost8,
            t1.other_cost9,
            t1.other_cost10,
            t1.other_cost11,
            t1.other_cost12,
            t1.oper_code,
            t1.oper_by,
            t1.oper_time,
            t1.del_flag,
            t1.bill_id,
            t1.bill_date,
            t1.business_time,
            t1.client_id,
            t1.fee_type_first,
            t1.is_increment,
            t1.dispatch_date,
            t1.finish_date,
            t1.show_bill_code,
            t1.extra_field1,
            t1.warehouse_code warehouse_code_arr,
            t1.settle_type,
            t1.settle_amount,
            t1.main_code_id main_expense_id,
            t1.settle_main_id,
            t2.carrier_name AS settle_main_name,
            '' AS automatic_billing_remark
        FROM bms_yfcost_info t1
        LEFT JOIN bms_carrierinfo t2 ON t2.id = t1.settle_main_id
        WHERE t1.del_flag = 0
        AND t1.main_code_id IN
        <foreach item="id" collection="mainCostIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectYfMiddleShareByMainExpenseCond" resultType="com.bbyb.joy.bms.domain.dto.BmsYfexpensesMiddleShare" >
        SELECT id,
        code_id AS yfbillId,
        1 AS yfbillType,
        id AS expensesId,
        main_code_id AS mainExpenseId,
        del_flag AS delFlag,
        share_type AS shareType,
        settle_amount AS shareAmount,
        freight,
        delivery_fee AS deliveryFee,
        superframes_fee AS superframesFee,
        excess_fee AS excessFee,
        reduce_fee AS reduceFee,
        shortbarge_fee AS shortbargeFee,
        return_fee AS returnFee,
        ultrafar_fee AS ultrafarFee,
        outboundsorting_fee AS outboundsortingFee,
        exception_fee AS exceptionFee,
        other_cost1 AS otherCost1,
        other_cost2 AS otherCost2,
        other_cost3 AS otherCost3,
        other_cost4 AS otherCost4,
        other_cost5 AS otherCost5,
        other_cost6 AS otherCost6,
        other_cost7 AS otherCost7,
        other_cost8 AS otherCost8,
        other_cost9 AS otherCost9,
        other_cost10 AS otherCost10,
        other_cost11 AS otherCost11,
        other_cost12 AS otherCost12
        FROM bms_yfexpenses_middle_share
        WHERE del_flag = 0
        AND main_code_id  IN
        <foreach item="id" collection="mainCostIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>







</mapper>