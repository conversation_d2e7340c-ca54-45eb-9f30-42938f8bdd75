<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsPaymentRecordMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.pay.BmsPaymentRecord" id="BmsPaymentRecordResult">
        <result property="id" column="pr_id"/>
        <!--        付款单号-->
        <result property="paymentCode" column="pr_payment_code"/>
        <result property="payType" column="pr_pay_type"/>
        <result property="paymentTime" column="pr_payment_time"/>
        <result property="paymentAmount" column="pr_payment_amount"/>
        <result property="availableAmount" column="pr_available_amount"/>
        <result property="collectioner" column="pr_collectioner"/>
        <result property="handler" column="pr_handler"/>
        <result property="remark" column="pr_remark"/>
        <result property="operDeptId" column="pr_oper_dept_id"/>
        <result property="operCode" column="pr_oper_code"/>
        <result property="operBy" column="pr_oper_by"/>
        <result property="operTime" column="pr_oper_time"/>
        <result property="HXAmount" column="hxAmount"/>
        <result property="codeSum" column="codeNum"/>
        <result property="createCode" column="pr_create_code"/>
        <result property="createBy" column="pr_create_by" />
        <result property="createDeptId" column="pr_create_dept_id"/>
        <result property="createTime" column="pr_create_time" />
        <result property="billCodes" column="billCodes" />

    </resultMap>
    <resultMap type="com.bbyb.joy.bms.domain.dto.pay.BmsPaymentRecordDetail" id="BmsPaymentRecordResultDetail">
        <result property="id" column="pr_id"/>
        <!--        付款单号-->
        <result property="paymentCode" column="pr_payment_code"/>
        <result property="collectioner" column="pr_collectioner"/>
        <result property="payType" column="pr_pay_type"/>
        <result property="paymentTime" column="pr_payment_time"/>
        <result property="paymentAmount" column="pr_payment_amount"/>
        <result property="availableAmount" column="pr_available_amount"/>
        <result property="handler" column="pr_handler"/>
        <result property="remark" column="pr_remark"/>
        <result property="replyCode" column="replyCode"/>
        <result property="createTime" column="pr_create_time" />
        <collection property="hxInfoList" javaType="java.util.List" ofType="com.bbyb.joy.bms.domain.dto.settlement.HxInfo">
            <result property="id" column="hx_id"/>
            <result property="billCodes" column="hx_billcodes"/>
            <result property="payment" column="hx_payment"/>
            <result property="paymentTime" column="hx_time"/>
            <result property="remark" column="hx_remark"/>
<!--            <result property="createTime" column="hx_create_code" />-->
            <result property="createBy" column="hx_create_by" />
        </collection>
        <collection property="attInfo" javaType="java.util.List" ofType="com.bbyb.joy.bms.domain.dto.settlement.Attachmentiinfo">
            <result property="id" column="pa_id"/>
            <result property="relationIde" column="pa_relation_ide"/>
            <result property="attachmentName" column="attachment_name"/>
            <result property="attachmentPath" column="attachment_path"/>
            <result property="remark" column="pa_remark"/>
        </collection>
    </resultMap>

    <sql id="selectBmsPaymentRecordVo">
        select id, payment_code, pay_type,payment_time, payment_amount, available_amount, handler, remark, create_code, create_by, create_time, create_dept_id, oper_dept_id, oper_code, oper_by, oper_time, del_flag from bms_payment_record
    </sql>
    <sql id="selectList">
SELECT
	pr.id AS pr_id,
-- 	pr.company_id AS pr_company_id,-- 所属机构
	pr.payment_code AS pr_payment_code,-- 付款单号
	pr.pay_type AS pr_pay_type,
	pr.carrier_name AS pr_collectioner,-- 收款放
	pr.payment_time pr_payment_time,
    CAST(pr.payment_amount as DECIMAL(18,2)) AS pr_payment_amount,
    CAST(pr.available_amount as DECIMAL(18,2)) AS pr_available_amount,-- 可用金额
	pr.HANDLER AS pr_handler,-- 对接人
	pr.remark AS pr_remark,
	pr.create_code AS pr_create_code,
	pr.create_by AS pr_create_by,
	pr.create_time AS pr_create_time,
	pr.create_dept_id AS pr_create_dept_id,
	pr.oper_dept_id AS pr_oper_dept_id,
	pr.oper_code AS pr_oper_code,
	pr.oper_by AS pr_oper_by,
	pr.oper_time AS pr_oper_time,
    --  CAST(sum( ifnull(yfhx.payment,0)) as DECIMAL(18,2)) AS hxAmount,
    (pr.payment_amount-pr.available_amount) as  hxAmount,
    group_concat(distinct yfhx.bill_codes) as billCodes,
	pr.reply_code as replyCode,
	pr.carrier_name as carrierName
FROM
	bms_payment_record pr
	LEFT JOIN bms_yfhx_record_middle rm ON rm.record_id = pr.id  and rm.del_flag=0
	LEFT JOIN bms_yfbill_hxinfo yfhx ON rm.hx_id = yfhx.id
	AND yfhx.del_flag =0
    </sql>
    <sql id="selectDetailSql">
        	SELECT
	pr.id AS pr_id,
-- 	pr.company_id AS pr_company_id,-- 所属机构
	pr.payment_code AS pr_payment_code,-- 付款单号
	pr.pay_type AS pr_pay_type,
	pr.carrier_name AS pr_collectioner,-- 收款放
	pr.payment_time pr_payment_time,
	pr.payment_amount AS pr_payment_amount,
	pr.available_amount AS pr_available_amount,-- 可用金额
	pr.HANDLER AS pr_handler,-- 对接人
	pr.remark AS pr_remark,
	pr.create_code AS pr_create_code,
	pr.create_by AS pr_create_by,
	pr.create_time AS pr_create_time,
	pr.create_dept_id AS pr_create_dept_id,
	pr.oper_dept_id AS pr_oper_dept_id,
	pr.oper_code AS pr_oper_code,
	pr.oper_by AS pr_oper_by,
	pr.oper_time AS pr_oper_time,
	pr.reply_code AS replyCode,
	yfhx.id as hx_id,
	yfhx.payment_time as hx_time,
	yfhx.remark as hx_remark,
	yfhx.bill_codes as hx_billcodes,
	yfhx.payment as hx_payment,
	yfhx.create_code as hx_create_code,
	yfhx.create_by as hx_create_by,
	att.id as  pa_id,
	att.attachment_path,
	att.relation_ide as pa_relation_ide,
	att.attachment_name,
	att.remark as pa_remark
FROM
	bms_payment_record pr
	LEFT JOIN pub_attachmentiinfo att ON pr.payment_code = att.relation_ide
	AND att.del_flag = 0
	LEFT JOIN bms_yfhx_record_middle rm ON rm.record_id = pr.id  and rm.del_flag=0
	LEFT JOIN bms_yfbill_hxinfo yfhx ON rm.hx_id = yfhx.id
	AND yfhx.del_flag =0
    </sql>
    <select id="selectBmsPaymentRecordList" parameterType="com.bbyb.joy.bms.domain.dto.pay.BmsPaymentRecord" resultMap="BmsPaymentRecordResult">
        <include refid="selectList"/>
        <where>
            <if test="payType != null ">and pr.pay_type = #{payType}</if>
            <if test="params.beginPaymentTime != null and params.beginPaymentTime != '' and params.endPaymentTime != null and params.endPaymentTime != ''">
                and pr.payment_time between #{params.beginPaymentTime} and #{params.endPaymentTime}
            </if>
            <if test="collectioner != null and collectioner != ''">and pr.carrier_name like concat('%', #{collectioner}, '%')</if>
            <if test="remark != null  and remark != ''">and pr.remark like concat('%', #{remark}, '%')</if>
            <if test="createDeptIds != null and createDeptIds.length>0 ">
                AND pr.create_dept_id in
                <foreach collection="createDeptIds"  item="companyIds" open="(" separator="," close=")">
                    #{companyIds}
                </foreach>
            </if>
            <if test="collectioner !=null and collectioner !=''">
                and pr.carrier_name like concat('%', #{collectioner}, '%')
            </if>
            and pr.del_flag=0
        </where>
        GROUP BY pr.id
        ORDER BY pr.create_time desc
    </select>

    <select id="selectBmsPaymentRecordById" parameterType="java.lang.String" resultMap="BmsPaymentRecordResultDetail">
<!--        <include refid="selectBmsPaymentRecordVo"/>-->
            <include refid="selectDetailSql"></include>
        where  pr.del_flag=0 and pr.id = #{id}
    </select>
    <select id="selectDetail" >
        <include refid="selectDetailSql" />
        <where>
            and pr.del_flag=0
            <if test="id!=null" >and pr.id=#{id} </if>
            <if test="paymentCode!=null and paymentCode!=''">and pr.paymentCode=#{paymentCode}</if>
        </where>
    </select>
    <insert id="insertBmsPaymentRecord" parameterType="com.bbyb.joy.bms.domain.dto.pay.BmsPaymentRecord" useGeneratedKeys="true" keyProperty="id">
        insert into bms_payment_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="paymentCode != null">payment_code,</if>
            <if test="payType != null">pay_type,</if>
            <if test="carrierId!=null">carrier_id,</if>
            <if test="carrierName!=null">carrier_name,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="availableAmount != null">available_amount,</if>
            <if test="handler != null">handler,</if>
            <if test="remark != null">remark,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createDeptId != null">create_dept_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="paymentCode != null">#{paymentCode},</if>
            <if test="payType != null">#{payType},</if>
            <if test="carrierId!=null">#{carrierId},</if>
            <if test="carrierName!=null">#{carrierName},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="availableAmount != null">#{availableAmount},</if>
            <if test="handler != null">#{handler},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
        </trim>
    </insert>

    <update id="updateBmsPaymentRecord" parameterType="com.bbyb.joy.bms.domain.dto.pay.BmsPaymentRecord">
        update bms_payment_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="paymentCode != null">payment_code = #{paymentCode},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="availableAmount != null">available_amount = #{availableAmount},</if>
            <if test="handler != null">handler = #{handler},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="replyCode!=null">reply_code=#{replyCode},</if>
            <if test="collectioner!=null and collectioner!=''">carrier_name=#{collectioner},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteBmsPaymentRecordById" parameterType="java.lang.String">
        update  bms_payment_record set del_flag=1 where id = #{id}
    </update>

    <delete id="deleteBmsPaymentRecordByIds">
        delete from bms_payment_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsPaymentRecordStatusByIds">
        update bms_payment_record set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectBmsYfbillByRecordId" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">
        select
        distinct
        yfhx.bill_codes as billCode
        from
        bms_payment_record pr
        LEFT JOIN bms_yfhx_record_middle rm ON rm.record_id = pr.id  and rm.del_flag=0
        LEFT JOIN bms_yfbill_hxinfo yfhx ON rm.hx_id = yfhx.id
        where
        pr.del_flag=0
        and yfhx.del_flag=0
        and pr.id = #{recordId}
    </select>

    <select id="selectBmsYfbillmainList" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain">
        select
        a.id,
        a.bill_code billCode,
        a.bill_name as billName,
         a.hx_time hxTime,
         a.oper_by operBy,
         a.oper_time operTime,
        d.client_name as clientName,
        mc.carrier_name as carrierName,
        a.hx_amount hxAmount
        from bms_yfbillmain a
        left join bms_clientinfo d
        on a.client_id = d.id
        left join bms_carrierinfo mc on mc.id=a.carrier_id
        <where>
        a.del_flag=0
            and a.hx_state!=1
            <if test="ids != null  and ids.size>0">
             and a.bill_code in
                <foreach item="item" collection="ids" open="(" separator="," close=")">
                    #{item}
                </foreach>
             </if>
        </where>
        GROUP by a.id
        order by a.create_time desc
    </select>
</mapper>