<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubQuoteruleMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.PubQuoterule" id="PubQuoteruleResult">
        <result property="id"    column="id"    />
        <result property="ruleCode"    column="rule_code"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleEdit"    column="rule_edit"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="warningTime"    column="warning_time"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="relationId"    column="relation_id"    />
        <result property="businessType"    column="business_type"    />
        <result property="userCompanyId"    column="user_company_id"    />
        <result property="remark"    column="remark"    />
        <result property="projectQuotation"    column="project_quotation"    />
        <result property="clientIdstr"    column="client_idstr"    />
        <result property="chargebyWeight"    column="chargeby_weight"    />
        <result property="chargebyVolume"    column="chargeby_volume"    />
        <result property="decimalPoint"    column="decimal_point"    />
        <result property="isEnable"    column="is_enable"    />
        <result property="createBy"    column="create_by"    />
        <result property="createCode"    column="create_code"    />
        <result property="createTime"    column="create_time"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="updateBy"    column="oper_by"    />
        <result property="operCode"    column="oper_code"    />
        <result property="updateTime"    column="oper_time"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="customerName"    column="customername"    />
        <result property="carrierName"    column="carriername"    />
        <result property="companyId"    column="companyid"    />
        <result property="shareType" column="share_type" />
        <result property="weightValue" column="weight_value" />
    </resultMap>

    <sql id="selectPubQuoteruleVo">
SELECT
    q.id,
	q.rule_code,
	q.rule_name,
	q.rule_edit,
	q.is_enable,
	q.rule_type,
	q.relation_id,
	q.user_company_id,
	q.client_idstr,
(CASE
		q.rule_type
		WHEN 1 THEN
		c.client_name
	END) AS customername,
(CASE
		q.rule_type
		WHEN 2 THEN
		cr.carrier_name
	END) AS carriername,
(CASE
		q.rule_type
		WHEN 1 THEN
		c.company_id ELSE cr.companyid
	END) AS companyid,
	q.business_type,
	q.start_time,
	q.end_time,
	q.warning_time,
	q.remark,
    q.remark ruleRemark,
	q.project_quotation,
	q.chargeby_weight,
	q.chargeby_volume,
	q.decimal_point,
	q.create_by,
	q.create_time,
    q.create_by insertBy,
    q.create_time insertTime,
	q.oper_by,
	q.oper_time,
    q.oper_by updateByName,
    q.oper_time updateTimes,
    case when  q.rule_type = 1 then c.client_name else  cr.carrier_name end clientOrCarrieName,
    q.share_type,
    q.weight_value
FROM
	pub_quoterule q
	LEFT JOIN bms_clientinfo c ON q.relation_id = c.id
	AND c.del_flag = 0
	LEFT JOIN bms_carrierinfo cr ON q.relation_id = cr.id
	AND cr.del_flag = 0
	    </sql>

    <select id="selectPubQuoteruleList" parameterType="java.util.Map" resultMap="PubQuoteruleResult">
        <include refid="selectPubQuoteruleVo"/>
        WHERE  q.del_flag= 0
        <if test="beginOperTime != null and beginOperTime != ''"><!-- 开始时间检索 -->
            and q.oper_time &gt;= #{beginOperTime,jdbcType=VARCHAR}
        </if>
        <if test="endOperTime != null and endOperTime != ''"><!-- 结束时间检索 -->
            and q.oper_time &lt;= #{endOperTime,jdbcType=VARCHAR}
        </if>
        <if test="companyIds != null and companyIds.size > 0"> and (CASE q.rule_type WHEN 1 THEN c.company_id ELSE cr.companyid END) in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="clientList != null">
            and  c.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        <if test="carrierList != null">
            and  cr.carrier_code in
            <foreach collection="carrierList" item="carrier" open="(" separator="," close=")">
                #{carrier}
            </foreach>
        </if>
        <if test="ruleCode != null  and ruleCode != ''"> and q.rule_code like concat('%', #{ruleCode}, '%')</if>
        <if test="ruleName != null  and ruleName != ''"> and q.rule_name like concat('%', #{ruleName}, '%')</if>
        <if test="ruleType != null and ruleType != ''"> and q.rule_type = #{ruleType}</if>
        <if test="customerName !=null and customerName !=''"> and (CASE q.rule_type WHEN 1 THEN c.client_name END) like concat('%',#{customerName},'%')</if>
        <if test="carrierName !=null and carrierName !=''"> and (CASE q.rule_type WHEN 2 THEN cr.carrier_name END) like concat('%',#{carrierName},'%')</if>
        <if test="businessType !=null and businessType !=''"> and q.business_type =#{businessType}</if>
        <if test="projectQuotation !=null and projectQuotation !=''"> and q.project_quotation =#{projectQuotation}</if>
        <if test="endTimeOrNow !=null and endTimeOrNow !=''"> and q.end_time &gt;= #{endTimeOrNow,jdbcType=VARCHAR}</if>
        <if test="cdServices != null and cdServices != ''"> and q.client_idstr is not null and q.client_idstr!='' </if>
        <if test="htState !=null and htState !=''">
            <if test="htState == 1">
                and now() between q.start_time and q.warning_time
            </if>
            <if test="htState == 3">
                and now() > q.end_time
            </if>
            <if test="htState == 2">
                and now() between q.warning_time and q.end_time
            </if>
        </if>

        ORDER BY q.create_time desc
    </select>



    <select id="selectPubQuoteruleById" parameterType="String" resultMap="PubQuoteruleResult">
        <include refid="selectPubQuoteruleVo"/>
        where q.id = #{id}
    </select>

    <select id="checkRuleCodeUnique" parameterType="String" resultMap="PubQuoteruleResult">
        <include refid="selectPubQuoteruleVo"/>
        where  q.del_flag='0' and q.rule_code=#{ruleCode} limit 1
    </select>

     <select id="selectPubQuoteruleCode" resultMap="PubQuoteruleResult">
        select  id,rule_code , rule_name
        from pub_quoterule where del_flag = '0' and is_enable = '0' and rule_type=#{ruleType}
    </select>

    <select id="selectByid" resultMap="PubQuoteruleResult">
        select  id,rule_code
        from pub_quoterule where del_flag = '0' and is_enable = '0'
        <if test="ids != null and ids.size>0 ">
           and  id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
    </select>

    <select id="selectPubClient" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoterule" resultMap="PubQuoteruleResult">
        select  id,rule_code,client_idstr
        from pub_quoterule where del_flag = '0' and  rule_type = 1
        and ( #{startTime,jdbcType=VARCHAR}  BETWEEN start_time and end_time or  #{endTime,jdbcType=VARCHAR}  BETWEEN start_time and end_time)
        <if test="relationId != null and relationId != ''">
            and  relation_id = #{relationId}
        </if>
        <if test="businessType != null">
            and  business_type = #{businessType}
        </if>
        <if test="id != null and id != ''">
            and  id != #{id}
        </if>

    </select>

    <select id="selectPubCarrier" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoterule" resultMap="PubQuoteruleResult">
        select  id,rule_code,client_idstr
        from pub_quoterule where del_flag = '0' and  rule_type = 2
        and ( #{startTime,jdbcType=VARCHAR}  BETWEEN start_time and end_time or  #{endTime,jdbcType=VARCHAR}  BETWEEN start_time and end_time)
        <if test="relationId != null and relationId != ''">
            and  relation_id = #{relationId}
        </if>
        <if test="businessType != null">
            and  business_type = #{businessType}
        </if>
        <if test="projectQuotation != null and projectQuotation != ''">
            and  project_quotation = #{projectQuotation}
        </if>
        <if test="id != null and id != ''">
            and  id != #{id}
        </if>

    </select>


    <insert id="insertPubQuoterule" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoterule" >
        insert into pub_quoterule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="ruleCode != null">rule_code,</if>
            <if test="ruleName != null">rule_name,</if>
            <if test="ruleEdit != null">rule_edit,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="warningTime != null">warning_time,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="relationId != null">relation_id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="userCompanyId != null">user_company_id,</if>
            <if test="remark != null">remark,</if>
            <if test="projectQuotation != null">project_quotation,</if>
            <if test="clientIdstr != null">client_idstr,</if>
            <if test="chargebyWeight != null">chargeby_weight,</if>
            <if test="chargebyVolume != null">chargeby_volume,</if>
            <if test="decimalPoint != null">decimal_point,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="updateBy != null">oper_by,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="updateTime != null">oper_time,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="shareType!=null">share_type,</if>
            <if test="weightValue!=null">weight_value,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="ruleCode != null">#{ruleCode},</if>
            <if test="ruleName != null">#{ruleName},</if>
            <if test="ruleEdit != null">#{ruleEdit},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="warningTime != null">#{warningTime},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="relationId != null">#{relationId},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="userCompanyId != null">#{userCompanyId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="projectQuotation != null">#{projectQuotation},</if>
            <if test="clientIdstr != null">#{clientIdstr},</if>
            <if test="chargebyWeight != null">#{chargebyWeight},</if>
            <if test="chargebyVolume != null">#{chargebyVolume},</if>
            <if test="decimalPoint != null">#{decimalPoint},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="shareType!=null">#{shareType},</if>
            <if test="weightValue!=null">#{weightValue},</if>
        </trim>
    </insert>

    <update id="updatePubQuoterule" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoterule">
        update pub_quoterule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleCode != null">rule_code = #{ruleCode},</if>
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            <if test="ruleEdit != null">rule_edit = #{ruleEdit},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="warningTime != null">warning_time = #{warningTime},</if>
            <if test="ruleType != null">rule_type = #{ruleType},</if>
            <if test="relationId != null">relation_id = #{relationId},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="userCompanyId != null">user_company_id = #{userCompanyId},</if>
            <if test="remark != null">remark = #{remark},</if>
            project_quotation = #{projectQuotation},
            client_idstr = #{clientIdstr},
            <if test="chargebyWeight != null">chargeby_weight = #{chargebyWeight},</if>
            <if test="chargebyVolume != null">chargeby_volume = #{chargebyVolume},</if>
            <if test="decimalPoint != null">decimal_point = #{decimalPoint},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="updateBy != null">oper_by = #{updateBy},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="updateTime != null">oper_time = #{updateTime},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="shareType!=null">share_type = #{shareType},</if>
            <if test="weightValue!=null">weight_value=#{weightValue},</if>
            oper_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePubQuoteruleByIds">
        update pub_quoterule
        set del_flag= 1
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updatePubQuoteruleByIds" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoterule">
        update pub_quoterule
        set is_enable= #{isEnable}
        where id  =   #{id}
    </update>


    <insert id="batchPubQuoteruleTemplatedetail">
        insert into pub_quoterule_templatedetail( id, quoterule_template_id, filedseting_id, field_chinese, field_english, oper_by, oper_time, del_flag) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.templateId}, #{item.filedsetingId}, #{item.fieldChinese}, #{item.fieldEnglish}, #{item.operBy}, #{item.operTime}, #{item.delFlag})
        </foreach>
    </insert>

    <delete id="delQuotationCost" parameterType="com.bbyb.bms.model.po.PubYsQuotationCostPO">
        delete from pub_ys_quotation_cost pyqc
        where pyqc.quotation_detail_id = #{qutationDetailId}
        and pyqc.ys_cost_id = #{ysCostId}
        <if test="quotationId !=null and quotationId !=''">
            and quotation_id=#{quotationId}
        </if>
    </delete>

    <insert id="insertQuotationCost" parameterType="com.bbyb.bms.model.po.PubYsQuotationCostPO">
        insert into pub_ys_quotation_cost(quotation_detail_id,ys_cost_id) values
        <foreach collection="pubYsQuotationCostPOs" item="item" open="(" separator="," close=")">
            #{item.quotationDetailId},#{item.ysCostId}
        </foreach>
    </insert>

    <select id="queryQuotationCost" resultType="com.bbyb.bms.model.po.PubYsQuotationCostPO">
        select quotation_id as quotationId , quotation_detail_id as quotationDetailId,ys_cost_id,id as ysCostId
        from pub_ys_quotation_cost
        where quotation_detail_id in
        <foreach collection="quotationDetailIds"  item="detail" separator="," open="(" close=")">
            #{detail}
        </foreach>
    </select>
</mapper>