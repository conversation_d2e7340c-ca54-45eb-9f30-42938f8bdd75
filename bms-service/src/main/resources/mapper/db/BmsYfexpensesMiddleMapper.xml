<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfexpensesMiddleMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYfexpensesMiddle" id="BmsYfexpensesMiddleResult">
        <result property="id"    column="id"    />
        <result property="yfbillId"    column="yfbill_id"    />
        <result property="yfbillType"    column="yfbill_type"    />
        <result property="expensesId"    column="expenses_id"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result column="share_type" property="shareType" jdbcType="INTEGER" />
        <result column="share_amount" property="shareAmount" jdbcType="DECIMAL" />
        <result column="freight" property="freight" jdbcType="DECIMAL" />
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL" />
        <result column="ultrafar_fee" property="ultrafarFee" jdbcType="DECIMAL" />
        <result column="superframes_fee" property="superframesFee" jdbcType="DECIMAL" />
        <result column="excess_fee" property="excessFee" jdbcType="DECIMAL" />
        <result column="reduce_fee" property="reduceFee" jdbcType="DECIMAL" />
        <result column="outboundsorting_fee" property="outboundsortingFee" jdbcType="DECIMAL" />
        <result column="shortbarge_fee" property="shortbargeFee" jdbcType="DECIMAL" />
        <result column="return_fee" property="returnFee" jdbcType="DECIMAL" />
        <result column="exception_fee" property="exceptionFee" jdbcType="DECIMAL" />
        <result column="other_cost1" property="otherCost1" jdbcType="DECIMAL" />
        <result column="other_cost2" property="otherCost2" jdbcType="DECIMAL" />
        <result column="other_cost3" property="otherCost3" jdbcType="DECIMAL" />
        <result column="other_cost4" property="otherCost4" jdbcType="DECIMAL" />
        <result column="other_cost5" property="otherCost5" jdbcType="DECIMAL" />
        <result column="other_cost6" property="otherCost6" jdbcType="DECIMAL" />
        <result column="other_cost7" property="otherCost7" jdbcType="DECIMAL" />
        <result column="other_cost8" property="otherCost8" jdbcType="DECIMAL" />
        <result column="other_cost9" property="otherCost9" jdbcType="DECIMAL" />
        <result column="other_cost10" property="otherCost10" jdbcType="DECIMAL" />
        <result column="other_cost11" property="otherCost11" jdbcType="DECIMAL" />
        <result column="other_cost12" property="otherCost12" jdbcType="DECIMAL" />
        <result column="main_expense_id" property="mainExpenseId" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="selectBmsYfexpensesMiddleVo">
        select id, yfbill_id, yfbill_type, expenses_id, oper_by, oper_time, del_flag, share_type,
               share_amount, freight, delivery_fee, ultrafar_fee, superframes_fee, excess_fee, reduce_fee,
               outboundsorting_fee, shortbarge_fee, return_fee, exception_fee, other_cost1, other_cost2,
               other_cost3, other_cost4, other_cost5, other_cost6, other_cost7, other_cost8, other_cost9,
               other_cost10, other_cost11, other_cost12, main_expense_id from bms_yfexpenses_middle
    </sql>

    <select id="selectBmsYfexpensesMiddleList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfexpensesMiddle" resultMap="BmsYfexpensesMiddleResult">
        <include refid="selectBmsYfexpensesMiddleVo"/>
        <where>
            <if test="yfbillId != null  and yfbillId != ''"> and yfbill_id = #{yfbillId}</if>
            <if test="yfbillType != null "> and yfbill_type = #{yfbillType}</if>
            <if test="expensesId != null  and expensesId != ''"> and expenses_id = #{expensesId}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
        </where>
    </select>

    <select id="selectBmsYfcostMiddleListByExpensesIds"  resultMap="BmsYfexpensesMiddleResult">
        <include refid="selectBmsYfexpensesMiddleVo"/>
        where del_flag = 0 and expenses_id in
        <foreach item="id" collection="expensesIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectBmsYfcostMiddleListByIds"  resultMap="BmsYfexpensesMiddleResult">
        <include refid="selectBmsYfexpensesMiddleVo"/>
        where del_flag = 0 and yfbill_id in (
        <if test="ids!=null and ids.size()>0">
            select id from bms_yfbillcodeinfo where id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="codes!=null and codes.size()>0">
            select id from bms_yfstock_codeinfo where relate_code in
            <foreach item="id" collection="codes" open="(" separator="," close=")">
                #{id}
            </foreach>
            UNION
            select id from bms_yfstockinfo where stock_code in
            <foreach item="id" collection="codes" open="(" separator="," close=")">
                #{id}
            </foreach>

        </if>
        )

    </select>


    <select id="selectBmsYfcostMiddleListByMainCostIds"  resultMap="BmsYfexpensesMiddleResult">
        <include refid="selectBmsYfexpensesMiddleVo"/>
        where del_flag = 0 and yfbill_id in (
        <if test="ids!=null and ids.size()>0">
            select id from bms_yfbillcodeinfo where id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="codes!=null and codes.size()>0">
            select id from bms_yfstock_codeinfo where relate_code in
            <foreach item="id" collection="codes" open="(" separator="," close=")">
                #{id}
            </foreach>
            UNION
            select id from bms_yfstockinfo where stock_code in
            <foreach item="id" collection="codes" open="(" separator="," close=")">
                #{id}
            </foreach>

        </if>
        )

    </select>


    <select id="selectBmsYfexpensesMiddleById" parameterType="java.lang.String" resultMap="BmsYfexpensesMiddleResult">
        <include refid="selectBmsYfexpensesMiddleVo"/>
        where id = #{id}
    </select>

    <insert id="insertBmsYfexpensesMiddle" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfexpensesMiddle">
        insert into bms_yfexpenses_middle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="yfbillId != null">yfbill_id,</if>
            <if test="yfbillType != null">yfbill_type,</if>
            <if test="expensesId != null">expenses_id,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="mainExpenseId != null">main_expense_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="yfbillId != null">#{yfbillId},</if>
            <if test="yfbillType != null">#{yfbillType},</if>
            <if test="expensesId != null">#{expensesId},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="mainExpenseId != null">#{mainExpenseId},</if>
        </trim>
    </insert>

    <update id="updateBmsYfexpensesMiddle" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfexpensesMiddle">
        update bms_yfexpenses_middle
        <trim prefix="SET" suffixOverrides=",">
            <if test="yfbillId != null">yfbill_id = #{yfbillId},</if>
            <if test="yfbillType != null">yfbill_type = #{yfbillType},</if>
            <if test="expensesId != null">expenses_id = #{expensesId},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYfexpensesMiddleById" parameterType="java.lang.String">
        delete from bms_yfexpenses_middle where id = #{id}
    </delete>

    <delete id="deleteBmsYfexpensesMiddleByIds">
        delete from bms_yfexpenses_middle where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYfexpensesMiddleStatusByIds">
        update bms_yfexpenses_middle set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="selectByOrderId" resultMap="BmsYfexpensesMiddleResult">
        <include refid="selectBmsYfexpensesMiddleVo"/>
        WHERE del_flag = '0'
        AND yfbill_type = #{yfbillType}
        AND yfbill_id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </select>





</mapper>