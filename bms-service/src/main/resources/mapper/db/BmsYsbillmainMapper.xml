<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYsbillmainMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" id="BmsYsbillmainResult">
        <result property="id"    column="id"    />
        <result property="billType"    column="bill_type"    />
        <result property="fatherid"    column="fatherid"    />
        <result property="billCode"    column="bill_code"    />
        <result property="collectionGuid"    column="collection_guid"    />
        <result property="billName"    column="bill_name"    />
        <result property="billDate"    column="bill_date"    />
        <result property="companyId"    column="company_id"    />
        <result property="clientId"    column="client_id"    />
        <result property="billMarking"    column="bill_marking"    />
        <result property="votes"    column="votes"    />
        <result property="billAmount"    column="bill_amount"    />
        <result property="adjustedAmount"    column="adjusted_amount"    />
        <result property="ysAmount"    column="ys_amount"    />
        <result property="billState"    column="bill_state"    />
        <result property="auditState"    column="audit_state"    />
        <result property="auditUser"    column="audit_user"    />
        <result property="auditUserName"    column="audit_user_name"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="ticketState"    column="ticket_state"    />
        <result property="ticketAmount"    column="ticket_amount"    />
        <result property="ticketNum"    column="ticket_num"    />
        <result property="ticketUserName"    column="ticket_user_name"    />
        <result property="ticketTime"    column="ticket_time"    />
        <result property="hxAmount"    column="hx_amount"    />
        <result property="hxState"    column="hx_state"    />
        <result property="hxUserName"    column="hx_user_name"    />
        <result property="hxTime"    column="hx_time"    />
        <result property="hxRemark"    column="hx_remark"    />
        <result property="billRemark"    column="bill_remark"    />
        <result property="arClerk"    column="ar_clerk"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="ticketApplyState"    column="ticket_apply_state"    />
        <result property="applyAuditState"    column="applyAuditState"    />
        <result property="ticketApplyTime"    column="ticket_apply_time"    />
        <result property="ticketApplyRemark"    column="ticket_apply_remark"    />
        <result property="submitStatus"    column="submit_status"    />
        <result property="submitDate"    column="submit_date"    />
        <result property="ticketApplyAllowUpdate"    column="ticket_apply_allow_update"    />
        <result property="exceptionFee"    column="exception_fee"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="orderLevel"    column="order_level"    />
        <result property="unClaimesAmount"    column="un_claimes_amount"    />
    </resultMap>

    <sql id="selectBmsYsbillmainVo">
        SELECT id,
               bill_type,
               fatherid,
               bill_code,
               collection_guid,
               bill_name,
               bill_date,
               company_id,
               client_id,
               bill_marking,
               votes,
               bill_amount,
               adjusted_amount,
               ys_amount,
               bill_state,
               audit_state,
               audit_user,
               audit_user_name,
               audit_time,
               audit_remark,
               ticket_state,
               ticket_amount,
               ticket_num,
               ticket_user_name,
               ticket_time,
               hx_amount,
               hx_state,
               hx_user_name,
               hx_time,
               hx_remark,
               bill_remark,
               ar_clerk,
               create_code,
               create_by,
               create_time,
               create_dept_id,
               oper_dept_id,
               oper_code,
               oper_by,
               oper_time,
               del_flag,
               ticket_apply_state,
               ticket_apply_time,
               ticket_apply_remark,
               IFNULL(merge_status, 0) AS mergeStatus,
               submit_status,
               submit_date,
               IFNULL(adjusted_amount, 0) - IFNULL(exception_fee, 0) AS receivableAmount,
               exception_fee
        FROM bms_ysbillmain
    </sql>

    <select id="getCostList" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        SELECT
        a.id                        as id,
        a.expenses_code             as expensesCode,
        a.total_boxes               as totalBoxes,
        a.total_number              as totalNumber,
        b.total_weight              as totalWeight,
        b.total_volume              as totalVolume,
        a.warehouse_code            as warehouseCode,
        a.warehouse_name            as warehouseName,
        c.client_code               as clientCode,
        c.client_name               as clientName,
        a.business_time             as businessTime,
        a.expenses_type             as expensesType,
        a.expenses_dimension        as costDimension,
        a.charge_type               as chargeType,
        a.expenses_mark             as feeFlag,
        a.remarks                   as remarks,
        a.freight                   as freight,
        a.ultrafar_fee              as ultrafarFee,
        a.superframes_fee           as superframesFee,
        a.excess_fee                as excessFee,
        a.reduce_fee                as reduceFee,
        a.delivery_fee              as deliveryFee,
        a.outboundsorting_fee       as outboundsortingFee,
        a.shortbarge_fee            as shortbargeFee,
        a.return_fee                as returnFee,
        a.exception_fee             as exceptionFee,
        a.other_cost1               as otherCost1,
        a.other_cost2               as otherCost2,
        a.other_cost3               as otherCost3,
        a.other_cost4               as otherCost4,
        a.other_cost5               as otherCost5,
        a.other_cost6               as otherCost6,
        a.other_cost7               as otherCost7,
        a.other_cost8               as otherCost8,
        a.other_cost9               as otherCost9,
        a.other_cost10              as otherCost10,
        a.other_cost11              as otherCost11,
        a.other_cost12              as otherCost12,
        a.adjust_fee                as adjustFee,
        SUM(
        IFNULL(a.freight, 0) + IFNULL(a.ultrafar_fee, 0) + IFNULL(a.superframes_fee, 0) + IFNULL(a.excess_fee, 0)
        + IFNULL(a.delivery_fee, 0) + IFNULL(a.outboundsorting_fee, 0) + IFNULL(a.shortbarge_fee, 0) +
        IFNULL(a.return_fee, 0) + IFNULL(a.exception_fee, 0) + IFNULL(a.other_cost1, 0) +
        IFNULL(a.other_cost2, 0) +
        IFNULL(a.other_cost3, 0) + IFNULL(a.other_cost4, 0) + IFNULL(a.other_cost5, 0) +
        IFNULL(a.other_cost6, 0) +
        IFNULL(a.other_cost7, 0) + IFNULL(a.other_cost8, 0) + IFNULL(a.other_cost9, 0) +
        IFNULL(a.other_cost10, 0) +
        IFNULL(a.other_cost11, 0) + IFNULL(a.other_cost12, 0) + IFNULL(a.reduce_fee, 0) +
        IFNULL(a.adjust_fee, 0)
        )                           as cargoValue,
        null                        as totalAmount,
        a.bill_date                 as billDate,
        a.bill_id                   as billMainid,
        a.settle_type                           as settleType,
        a.settle_amount                         as settleAmount,
        a.main_code_id                          as mainExpenseId,
        a.company_id                            as companyId,
        a.create_time                           as feeCreateAte,
        DATE_FORMAT(a.create_time,'%Y-%m-%d %H:%i:%s') as feeCreateDate,
        a.create_time                           as createTime,
        DATE_FORMAT(a.create_time,'%Y-%m-%d %H:%i:%s') as createTimeyw
        FROM bms_yscost_info a
        LEFT JOIN bms_ysexpenses_middle b ON b.main_pk_id = a.main_pk_id AND b.del_flag = 0
        LEFT JOIN bms_clientinfo c ON a.client_id = c.id
        <where>
            a.del_flag =0
            AND IFNULL(a.bill_id,0) = 0
            <if test="clientList!=null and clientList.size>0">
                AND c.client_code IN
                <foreach collection="clientList" item="client" open="(" separator="," close=")">
                    #{client}
                </foreach>
            </if>
            <if test="companyIdList!=null and companyIdList.size>0">
                AND a.company_id in
                <foreach collection="companyIdList" item="compayId" open="(" separator="," close=")">
                    #{compayId}
                </foreach>
            </if>
            <if test="expensesTypeList!=null">
                and a.expenses_type in
                <foreach collection="expensesTypeList" item="value" open="(" separator="," close=")">
                    #{value}
                </foreach>
            </if>
            <if test="clientCode!=null and clientCode != ''">
                and c.client_code = #{clientCode}
            </if>
            <if test="businessCode!=null and businessCode !='' ">
                and b.business_code like  concat('%',#{businessCode},'%')
            </if>
            <if test="expensesCode!=null and expensesCode !='' ">
                and a.expenses_code like  concat('%',#{expensesCode},'%')
            </if>
            <if test="businessType!=null and businessType !='' ">
                and a.business_type = #{businessType}
            </if>
            <if test="warehouseCode!=null and warehouseCode !='' ">
                and b.warehouse_code  like  concat('%',#{warehouseCode},'%')
            </if>
            <if test="warehouseName!=null and warehouseName !='' ">
                and b.warehouse_name like  concat('%',#{warehouseName},'%')
            </if>
            <if test="clientCode!=null and clientCode !='' ">
                and c.client_code like  concat('%',#{clientCode},'%')
            </if>
            <if test="clientName!=null and clientName !='' ">
                and c.client_name like  concat('%',#{clientName},'%')
            </if>
            <if test="beginBusinessTime!=null">
                and a.business_time &gt;= #{beginBusinessTime}
            </if>
            <if test="endBusinessTime!=null">
                and a.business_time &lt;= #{endBusinessTime}
            </if>
        </where>
        GROUP BY a.id
        ORDER BY a.business_time
    </select>

    <select id="getCostWareList" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        SELECT
            CASE
                WHEN a.expenses_dimension = 1 THEN b.relate_code
                ELSE '-'
            END                                             AS businessCode,
            CASE
                WHEN a.expenses_dimension = 1 THEN b.relate_code
                ELSE '-'
            END                                             AS relateCode,
            a.id                                            AS id,
            a.expenses_code                                 AS expensesCode,
            a.total_boxes                                   AS totalBoxes,
            a.total_number                                  AS totalNumber,
            b.total_weight                                  AS totalWeight,
            b.total_volume                                  AS totalVolume,
            a.warehouse_code                                AS warehouseCode,
            a.warehouse_name                                AS warehouseName,
            c.client_code                                   AS clientCode,
            c.client_name                                   AS clientName,
            a.business_time                                 AS businessTime,
            a.expenses_type                                 AS expensesType,
            a.expenses_dimension                            AS costDimension,
            a.charge_type                                   AS chargeType,
            a.expenses_mark                                 AS feeFlag,
            a.remarks                                       AS remarks,
            a.freight                                       AS freight,
            a.ultrafar_fee                                  AS ultrafarFee,
            a.superframes_fee                               AS superframesFee,
            a.excess_fee                                    AS excessFee,
            a.reduce_fee                                    AS reduceFee,
            a.delivery_fee                                  AS deliveryFee,
            a.outboundsorting_fee                           AS outboundsortingFee,
            a.shortbarge_fee                                AS shortbargeFee,
            a.return_fee                                    AS returnFee,
            a.exception_fee                                 AS exceptionFee,
            a.other_cost1                                   AS otherCost1,
            a.other_cost2                                   AS otherCost2,
            a.other_cost3                                   AS otherCost3,
            a.other_cost4                                   AS otherCost4,
            a.other_cost5                                   AS otherCost5,
            a.other_cost6                                   AS otherCost6,
            a.other_cost7                                   AS otherCost7,
            a.other_cost8                                   AS otherCost8,
            a.other_cost9                                   AS otherCost9,
            a.other_cost10                                  AS otherCost10,
            a.other_cost11                                  AS otherCost11,
            a.other_cost12                                  AS otherCost12,
            a.adjust_fee                                    AS adjustFee,
            SUM(
            IFNULL(a.freight, 0) + IFNULL(a.ultrafar_fee, 0) + IFNULL(a.superframes_fee, 0) + IFNULL(a.excess_fee, 0)
            + IFNULL(a.delivery_fee, 0) + IFNULL(a.outboundsorting_fee, 0) + IFNULL(a.shortbarge_fee, 0) +
            IFNULL(a.return_fee, 0) + IFNULL(a.exception_fee, 0) + IFNULL(a.other_cost1, 0) +
            IFNULL(a.other_cost2, 0) +
            IFNULL(a.other_cost3, 0) + IFNULL(a.other_cost4, 0) + IFNULL(a.other_cost5, 0) +
            IFNULL(a.other_cost6, 0) +
            IFNULL(a.other_cost7, 0) + IFNULL(a.other_cost8, 0) + IFNULL(a.other_cost9, 0) +
            IFNULL(a.other_cost10, 0) +
            IFNULL(a.other_cost11, 0) + IFNULL(a.other_cost12, 0) + IFNULL(a.reduce_fee, 0) +
            IFNULL(a.adjust_fee, 0)
            )                                               AS cargoValue,
            null                                            AS totalAmount,
            a.bill_date                                     AS billDate,
            a.bill_id                                       AS billMainid,
            a.settle_type                                   AS settleType,
            a.settle_amount                                 AS settleAmount,
            a.main_code_id                                  AS mainExpenseId,
            a.main_pk_id                                    AS mainExpensePkId,
            a.company_id                                    AS companyId,
            a.create_time                                   AS feeCreateAte,
            DATE_FORMAT(a.create_time, '%Y-%m-%d %H:%i:%s') AS feeCreateDate,
            a.create_time                                   AS createTime,
            DATE_FORMAT(a.create_time, '%Y-%m-%d %H:%i:%s') AS createTimeyw
        FROM bms_yscost_info a
        LEFT JOIN bms_ysexpenses_middle b ON b.main_pk_id = a.main_pk_id AND b.del_flag = 0
        LEFT JOIN bms_clientinfo c ON a.client_id = c.id
        <where>
            a.del_flag = 0
            AND IFNULL(a.bill_id,0) = 0
            <if test="clientList!=null and clientList.size>0">
                AND  c.client_code IN
                <foreach collection="clientList" item="client" open="(" separator="," close=")">
                    #{client}
                </foreach>
            </if>
            <if test="mainExpensePkId!=null ">
                a.main_pk_id = #{mainExpensePkId}
            </if>
            <if test="warehouseList!=null and warehouseList.size>0">
                AND  a.warehouse_code IN
                <foreach collection="warehouseList" item="warehouse" open="(" separator="," close=")">
                    #{warehouse}
                </foreach>
            </if>
            <if test="companyIdList!=null and companyIdList.size>0">
                AND a.company_id IN
                <foreach collection="companyIdList" item="compayId" open="(" separator="," close=")">
                    #{compayId}
                </foreach>
            </if>
            <if test="expensesTypeList!=null">
                AND a.expenses_type IN
                <foreach collection="expensesTypeList" item="value" open="(" separator="," close=")">
                    #{value}
                </foreach>
            </if>
            <if test="clientCode!=null and clientCode != ''">
                AND c.client_code = #{clientCode}
            </if>
            <if test="businessCode!=null and businessCode !='' ">
                AND bym.relate_code LIKE concat('%',#{businessCode},'%')
            </if>
            <if test="expensesCode!=null and expensesCode !='' ">
                AND a.expenses_code LIKE concat('%',#{expensesCode},'%')
            </if>
            <if test="warehouseCode!=null and warehouseCode !='' ">
                AND a.warehouse_code LIKE concat('%',#{warehouseCode},'%')
            </if>
            <if test="warehouseName!=null and warehouseName !='' ">
                AND a.warehouse_name LIKE concat('%',#{warehouseName},'%')
            </if>
            <if test="clientCode!=null and clientCode !='' ">
                AND c.client_code LIKE concat('%',#{clientCode},'%')
            </if>
            <if test="clientName!=null and clientName !='' ">
                AND c.client_name LIKE concat('%',#{clientName},'%')
            </if>
            <if test="beginBusinessTime!=null">
                AND a.business_time &gt;= #{beginBusinessTime}
            </if>
            <if test="endBusinessTime!=null">
                AND a.business_time &lt;= #{endBusinessTime}
            </if>
        </where>
        GROUP BY a.id
        ORDER BY a.business_time
    </select>

    <select id="getCostWareListKc" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        SELECT
            CASE
                WHEN a.expenses_dimension = 1 THEN bym.relate_code
                ELSE '-'
            END                                         AS businessCode,
            CASE
                WHEN a.expenses_dimension = 1 THEN bym.relate_code
                ELSE '-'
            END                                         AS relateCode,
            a.id                                        AS id,
            a.pk_id                                     AS pkId,
            a.company_id                                AS companyId,
            a.expenses_code                             AS expensesCode,
            a.total_boxes                               AS totalBoxes,
            a.warehouse_code                            AS warehouseCode,
            a.warehouse_name                            AS warehouseName,
            c.client_code                               AS clientCode,
            c.client_name                               AS clientName,
            a.business_time                             AS businessTime,
            a.expenses_type                             AS expensesType,
            a.expenses_dimension                        AS costDimension,
            a.charge_type                               AS chargeType,
            a.expenses_mark                             AS feeFlag,
            a.remarks                                   AS remarks,
            GROUP_CONCAT(a.adjust_remark SEPARATOR ',') AS adjustRemark,
            a.freight                                   AS freight,
            a.ultrafar_fee                              AS ultrafarFee,
            a.superframes_fee                           AS superframesFee,
            a.excess_fee                                AS excessFee,
            a.reduce_fee                                AS reduceFee,
            a.delivery_fee                              AS deliveryFee,
            a.outboundsorting_fee                       AS outboundsortingFee,
            a.shortbarge_fee                            AS shortbargeFee,
            a.return_fee                                AS returnFee,
            a.exception_fee                             AS exceptionFee,
            a.other_cost1                               AS otherCost1,
            a.other_cost2                               AS otherCost2,
            a.other_cost3                               AS otherCost3,
            a.other_cost4                               AS otherCost4,
            a.other_cost5                               AS otherCost5,
            a.other_cost6                               AS otherCost6,
            a.other_cost7                               AS otherCost7,
            a.other_cost8                               AS otherCost8,
            a.other_cost9                               AS otherCost9,
            a.other_cost10                              AS otherCost10,
            a.other_cost11                              AS otherCost11,
            a.other_cost12                              AS otherCost12,
            a.adjust_fee                                AS adjustFee,
            SUM(
            IFNULL(a.freight, 0) + IFNULL(a.ultrafar_fee, 0) + IFNULL(a.superframes_fee, 0) + IFNULL(a.excess_fee, 0)
            + IFNULL(a.delivery_fee, 0) + IFNULL(a.outboundsorting_fee, 0) + IFNULL(a.shortbarge_fee, 0) +
            IFNULL(a.return_fee, 0) + IFNULL(a.exception_fee, 0) + IFNULL(a.other_cost1, 0) +
            IFNULL(a.other_cost2, 0) +
            IFNULL(a.other_cost3, 0) + IFNULL(a.other_cost4, 0) + IFNULL(a.other_cost5, 0) +
            IFNULL(a.other_cost6, 0) +
            IFNULL(a.other_cost7, 0) + IFNULL(a.other_cost8, 0) + IFNULL(a.other_cost9, 0) +
            IFNULL(a.other_cost10, 0) +
            IFNULL(a.other_cost11, 0) + IFNULL(a.other_cost12, 0) + IFNULL(a.adjust_fee, 0) + IFNULL(a.reduce_fee, 0)
            )                                           AS cargoValue,
            null                                        AS totalAmount,
            a.bill_date                                 AS billDate,
            a.bill_id                                   AS billMainid,
            CASE
                WHEN a.expenses_dimension = 1 THEN '单'
                WHEN a.expenses_dimension = 2 THEN '趟'
                WHEN a.expenses_dimension = 3 THEN '日'
                WHEN a.expenses_dimension = 4 THEN '月'
                WHEN a.expenses_dimension = 5 THEN '品规'
                ELSE ''
            END AS costDimensionName,
            a.signing_date                              AS instorageTime,
            GROUP_CONCAT((case
            when a.charge_type = 2
            then '手动计费'
            when a.charge_type = 1
            then '自动计费'
            else ''
            end) SEPARATOR ',')                     AS chargeTypeName,
            IFNULL(a.total_number, 0)                   AS totalNumber,
            IFNULL(a.total_weight, 0)                   AS totalWeight,
            IFNULL(a.total_volume, 0)                   AS totalVolume,
            GROUP_CONCAT(a.oper_by SEPARATOR ',')       AS operBy,
            c.remark                                    AS automaticBillingRemark,
            SUM(IFNULL(a.other_cost1, 0) + IFNULL(a.other_cost2, 0) + IFNULL(a.other_cost3, 0) + IFNULL(a.other_cost4, 0)
            + IFNULL(a.other_cost5, 0) + IFNULL(a.other_cost6, 0) + IFNULL(a.other_cost7, 0) + IFNULL(a.other_cost8, 0)
            + IFNULL(a.other_cost9, 0) + IFNULL(a.other_cost10, 0) + IFNULL(a.other_cost11, 0) +
            IFNULL(a.other_cost12, 0)
            + IFNULL(a.freight, 0) + IFNULL(a.delivery_fee, 0) + IFNULL(a.outboundsorting_fee, 0) +
            IFNULL(a.shortbarge_fee, 0)
            + IFNULL(a.ultrafar_fee, 0) + IFNULL(a.superframes_fee, 0) + IFNULL(a.excess_fee, 0) +
            IFNULL(a.return_fee, 0)
            + IFNULL(a.exception_fee, 0))           AS sumAmt,
            a.settle_type                           AS settleType,
            a.settle_amount                         AS settleAmount,
            a.main_code_id                          AS mainExpenseId,
            a.main_pk_id                            AS mainExpensePkId,
            a.create_time                           AS feeCreateAte,
            DATE_FORMAT(a.create_time,'%Y-%m-%d %H:%i:%s') AS feeCreateDate,
            a.signing_date                          AS signingDate,
            a.order_date                            AS orderDate,
            a.create_time                           AS createTime,
            DATE_FORMAT(a.create_time,'%Y-%m-%d %H:%i:%s') AS createTimeyw
        FROM bms_yscost_info a
        LEFT JOIN bms_clientinfo c ON a.client_id = c.id
        LEFT JOIN bms_ysexpenses_middle bym ON bym.main_pk_id = a.main_pk_id AND bym.del_flag = 0
        <where>
            a.del_flag =0 AND IFNULL(a.bill_id,0)=0
            <if test="mainExpensePkId!=null ">
                a.main_pk_id = #{mainExpensePkId}
            </if>
            <if test="clientList!=null and clientList.size>0">
                AND c.client_code IN
                <foreach collection="clientList" item="client" open="(" separator="," close=")">
                    #{client}
                </foreach>
            </if>
            <if test="warehouseList!=null and warehouseList.size>0">
                AND a.warehouse_code IN
                <foreach collection="warehouseList" item="warehouse" open="(" separator="," close=")">
                    #{warehouse}
                </foreach>
            </if>
            <if test="companyIdList!=null and companyIdList.size>0">
                AND a.company_id IN
                <foreach collection="companyIdList" item="compayId" open="(" separator="," close=")">
                    #{compayId}
                </foreach>
            </if>
            <if test="expensesTypeList!=null">
                AND a.expenses_type IN
                <foreach collection="expensesTypeList" item="value" open="(" separator="," close=")">
                    #{value}
                </foreach>
            </if>
            <if test="clientCode!=null and clientCode != ''">
                AND c.client_code = #{clientCode}
            </if>
            <if test="businessCode!=null and businessCode !='' ">
                AND bym.relate_code LIKE concat('%',#{businessCode},'%')
            </if>
            <if test="expensesCode!=null and expensesCode !='' ">
                AND a.expenses_code LIKE concat('%',#{expensesCode},'%')
            </if>
            <if test="businessType!=null and businessType !='' ">
                AND a.business_type = #{businessType}
            </if>
            <if test="warehouseCode!=null and warehouseCode !='' ">
                AND a.warehouse_code LIKE concat('%',#{warehouseCode},'%')
            </if>
            <if test="warehouseName!=null and warehouseName !='' ">
                AND a.warehouse_name LIKE concat('%',#{warehouseName},'%')
            </if>
            <if test="clientCode!=null and clientCode !='' ">
                AND c.client_code LIKE concat('%',#{clientCode},'%')
            </if>
            <if test="clientName!=null and clientName !='' ">
                AND c.client_name LIKE concat('%',#{clientName},'%')
            </if>
            <if test="beginBusinessTime!=null">
                AND a.business_time &gt;= #{beginBusinessTime}
            </if>
            <if test="endBusinessTime!=null">
                AND a.business_time &lt;= #{endBusinessTime}
            </if>
        </where>
        GROUP BY a.id
        ORDER BY a.business_time
    </select>


    <select id="getCostListBymainIdBypursea" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
            null as businessCode,
            a.id as id,
            a.ys_feecode as expensesCode,
            null as businessType,
            null as warehouseCode,
            null as warehouseName,
            null as clientId,
            a.relation_code as clientCode,
            b.client_name as clientName,
            a.fee_flag as feeFlag,
            a.register_time as registerTime,
            a.registerr_company_id as registerrCompanyId,
            a.registerr_companyname as registerrCompanyname,
            a.sku_name as skuName,
            a.num as num,
            a.price as price,
            a.unit as unit,
            a.remark as remark,
            null as businessTime,
            a.total_amount as cargoValue,
            null as expensesType,
            null as costDimension,
            null as chargeType,
            a.fee_flag as feeFlag,
            a.remark as remarks,
            a.total_amount as freight,
            null as ultrafarFee,
            null as superframesFee,
            null as excessFee,
            null as deliveryFee,
            null as outboundsortingFee,
            null as shortbargeFee,
            null as returnFee,
            null as exceptionFee,
            null as otherCost1,
            null as otherCost2,
            null as otherCost3,
            null as otherCost4,
            null as otherCost5,
            null as otherCost6,
            null as otherCost7,
            null as otherCost8,
            null as otherCost9,
            null as otherCost10,
            null as otherCost11,
            null as otherCost12,
            null as adjustFee,
            a.total_amount as totalAmount,
            null as billMainid,
            null as ysbilType,
            a.bill_date as billDate,
            a.id as codeInfoId
        from bms_yspurchase_feeinfo a
        left join bms_clientinfo b on a.relation_code = b.client_code
        <where>
            <if test="ids!=null and ids.size>0">
                and IFNULL(a.ysbill_id,0) in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="expensesCode!=null and expensesCode !='' ">
                and a.ys_feecode = #{expensesCode}
            </if>
        </where>
        order by a.create_time
    </select>


    <select id="getCostListBypursea" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        select
            null as businessCode,
            a.id as id,
            a.ys_feecode as expensesCode,
            a.fee_flag as feeFlag,
            null as businessType,
            null as warehouseCode,
            null as warehouseName,
            a.relation_code as clientCode,
            b.client_name as clientName,
            null as businessTime,
            a.total_amount as cargoValue,
            a.register_time as registerTime,
            null as expensesType,
            null as costDimension,
            null as chargeType,
            a.fee_flag as feeFlag,
            a.remark as remarks,
            0 as freight,
            0 as ultrafarFee,
            0 as superframesFee,
            0 as excessFee,
            0 as reduceFee,
            0 as deliveryFee,
            0 as outboundsortingFee,
            0 as shortbargeFee,
            0 as returnFee,
            0 as exceptionFee,
            0 as otherCost1,
            0 as otherCost2,
            0 as otherCost3,
            0 as otherCost4,
            0 as otherCost5,
            0 as otherCost6,
            0 as otherCost7,
            0 as otherCost8,
            0 as otherCost9,
            0 as otherCost10,
            0 as otherCost11,
            0 as otherCost12,
            0 as adjustFee,
            0 as totalBoxes,
            0 as totalNumber,
            0 as totalWeight,
            0 as totalVolume,
            a.sku_name as skuName,
            a.total_amount as totalAmount,
            a.num as num,
            a.price as price,
            a.unit as unit,
            a.spec as spec,
            a.register_time as registerTime,
            a.registerr_companyname as registerrCompanyname,
            a.remark as remark,
            null as billMainid,
            null as ysbilType,
            null as ysbillId
        from bms_yspurchase_feeinfo a
        left join bms_clientinfo b on a.relation_code = b.client_code
        <where>
            1=1
            and IFNULL(a.ysbill_id,0)=0
            <if test="companyIdList!=null">
                and a.company_id in
                <foreach collection="companyIdList" item="compayId" open="(" separator="," close=")" >
                    #{compayId}
                </foreach>
            </if>
            <if test="clientCode!=null and clientCode !=null">
                and a.relation_code =#{clientCode}
            </if>
            <if test="expensesCode!=null and expensesCode !='' ">
                and a.ys_feecode = #{expensesCode}
            </if>
            <if test="expensesCode!=null and expensesCode !='' ">
                and a.ys_feecode = #{expensesCode}
            </if>
            <if test="clientName!=null and clientName !='' ">
                and a.relation_name like  concat('%', #{clientName}, '%')
            </if>
        </where>
        order by a.create_time
    </select>


    <select id="getCostListBymainId" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto">
        SELECT
            CASE
                WHEN a.expenses_dimension = 1 THEN d.relate_code
                ELSE '-'
            END                                                            AS businessCode,
            CASE
                WHEN a.expenses_dimension = 1 THEN d.relate_code
                ELSE '-'
            END                                                            AS relateCode,
            a.id                                                           AS id,
            a.business_time                                                AS businessTime,
            a.expenses_code                                                AS expensesCode,
            a.warehouse_code                                               AS warehouseCode,
            a.warehouse_name                                               AS warehouseName,
            a.id                                                           AS clientId,
            c.client_code                                                  AS clientCode,
            c.client_name                                                  AS clientName,
            a.expenses_type                                                AS expensesType,
            a.expenses_dimension                                           AS costDimension,
            a.charge_type                                                  AS chargeType,
            a.expenses_mark                                                AS feeFlag,
            a.remarks                                                      AS remarks,
            IFNULL(a.freight, 0)                                           AS freight,
            IFNULL(a.ultrafar_fee, 0)                                      AS ultrafarFee,
            IFNULL(a.superframes_fee, 0)                                   AS superframesFee,
            IFNULL(a.excess_fee, 0)                                        AS excessFee,
            IFNULL(a.delivery_fee, 0)                                      AS deliveryFee,
            IFNULL(a.outboundsorting_fee, 0)                               AS outboundsortingFee,
            IFNULL(a.shortbarge_fee, 0)                                    AS shortbargeFee,
            IFNULL(a.return_fee, 0)                                        AS returnFee,
            IFNULL(a.exception_fee, 0)                                     AS exceptionFee,
            IFNULL(a.other_cost1, 0)                                       AS otherCost1,
            IFNULL(a.other_cost2, 0)                                       AS other_cost2,
            IFNULL(a.other_cost3, 0)                                       AS otherCost3,
            IFNULL(a.other_cost4, 0)                                       AS otherCost4,
            IFNULL(a.other_cost5, 0)                                       AS otherCost5,
            IFNULL(a.other_cost6, 0)                                       AS otherCost6,
            IFNULL(a.other_cost7, 0)                                       AS otherCost7,
            IFNULL(a.other_cost8, 0)                                       AS otherCost8,
            IFNULL(a.other_cost9, 0)                                       AS otherCost9,
            IFNULL(a.other_cost10, 0)                                      AS otherCost10,
            IFNULL(a.other_cost11, 0)                                      AS otherCost11,
            IFNULL(a.other_cost12, 0)                                      AS otherCost12,
            IFNULL(a.adjust_fee, 0)                                        AS adjustFee,
            IFNULL(a.total_boxes, 0)                                       AS totalBoxes,
            IFNULL(a.total_number, 0)                                      AS totalNumber,
            IFNULL(a.total_weight, 0)                                      AS totalWeight,
            IFNULL(a.total_volume, 0)                                      AS totalVolume,
            SUM(
            IFNULL(a.freight, 0) + IFNULL(a.ultrafar_fee, 0) + IFNULL(a.superframes_fee, 0) + IFNULL(a.excess_fee, 0)
            + IFNULL(a.delivery_fee, 0) + IFNULL(a.outboundsorting_fee, 0) + IFNULL(a.shortbarge_fee, 0) +
            IFNULL(a.return_fee, 0) + IFNULL(a.exception_fee, 0) + IFNULL(a.other_cost1, 0) +
            IFNULL(a.other_cost2, 0) +
            IFNULL(a.other_cost3, 0) + IFNULL(a.other_cost4, 0) + IFNULL(a.other_cost5, 0) +
            IFNULL(a.other_cost6, 0) +
            IFNULL(a.other_cost7, 0) + IFNULL(a.other_cost8, 0) + IFNULL(a.other_cost9, 0) +
            IFNULL(a.other_cost10, 0) +
            IFNULL(a.other_cost11, 0) + IFNULL(a.other_cost12, 0)) AS cargoValue,
            SUM(
            IFNULL(a.freight, 0) + IFNULL(a.ultrafar_fee, 0) + IFNULL(a.superframes_fee, 0) + IFNULL(a.excess_fee, 0)
            + IFNULL(a.delivery_fee, 0) + IFNULL(a.outboundsorting_fee, 0) + IFNULL(a.shortbarge_fee, 0) +
            IFNULL(a.return_fee, 0) + IFNULL(a.exception_fee, 0) + IFNULL(a.other_cost1, 0) +
            IFNULL(a.other_cost2, 0) +
            IFNULL(a.other_cost3, 0) + IFNULL(a.other_cost4, 0) + IFNULL(a.other_cost5, 0) +
            IFNULL(a.other_cost6, 0) +
            IFNULL(a.other_cost7, 0) + IFNULL(a.other_cost8, 0) + IFNULL(a.other_cost9, 0) +
            IFNULL(a.other_cost10, 0) +
            IFNULL(a.other_cost11, 0) + IFNULL(a.other_cost12, 0)) AS totalAmount,
            a.bill_id                                                      AS billMainid,
            a.bill_date                                                    AS billDate,
            d.code_type                                                    AS ysbilType,
            a.company_id                                                   AS institutionId,
            a.order_date                                                   AS orderDate,
            a.signing_date                                                 AS signingDate,
            a.main_code_id                                                 AS mainExpenseId,
            a.main_pk_id                                                   AS mainExpensePkId
        FROM bms_yscost_info a
        INNER JOIN bms_clientinfo c ON a.client_id = c.id
        INNER JOIN bms_ysexpenses_middle d ON a.main_pk_id = d.main_pk_id AND d.del_flag = 0
        <where>
            a.del_flag =0
            <if test="ids!=null and ids.size>0">
                and IFNULL(a.bill_id,0) in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="mainExpensePkId!=null">
                a.main_pk_id = #{mainExpensePkId}
            </if>
            <if test="businessCode!=null and businessCode !='' ">
                and d.relate_code like  concat('%',#{businessCode},'%')
            </if>
            <if test="expensesCode!=null and expensesCode !='' ">
                and a.expenses_code like  concat('%',#{expensesCode},'%')
            </if>
            <if test="businessType!=null and businessType !='' ">
                and a.business_type = #{businessType}
            </if>
            <if test="warehouseCode!=null and warehouseCode !='' ">
                and a.warehouse_code  like  concat('%',#{warehouseCode},'%')
            </if>
            <if test="warehouseName!=null and warehouseName !='' ">
                and a.warehouse_name like  concat('%',#{warehouseName},'%')
            </if>
            <if test="clientCode!=null and clientCode !='' ">
                and c.client_code like  concat('%',#{clientCode},'%')
            </if>
            <if test="clientName!=null and clientName !='' ">
                and c.client_name like  concat('%',#{clientName},'%')
            </if>
            <if test="beginBusinessTime!=null">
                and a.business_time &gt;= #{beginBusinessTime}
            </if>
            <if test="endBusinessTime!=null">
                and a.business_time &lt;= #{endBusinessTime}
            </if>
        </where>
        GROUP BY a.id ,d.code_pk_id
        ORDER BY a.business_time
    </select>

    <select id="selectBmsYsbillmainExportList" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            a.id,
            bill_type                 as billType,
            bill_code                 as billCode,
            bill_name                 as billName,
            bill_date                 as billDate,
            a.company_id              as companyId,
            client_id                 as clientId,
            bill_marking              as billMarking,
            votes,
            bill_amount               as billAmount,
            adjusted_amount           as adjustedAmount,
            ys_amount                 as ysAmount,
            bill_state                as billState,
            audit_state               as auditState,
            audit_user                as auditUser,
            audit_user_name           as auditUserName,
            audit_time                as auditTime,
            audit_remark              as auditRemark,
            ticket_state              as ticketState,
            ticket_time               as ticketTime,
            hx_amount                 as hxAmount,
            hx_state                  as hxState,
            bill_remark               as billRemark,
            create_by                 as createBy,
            create_time               as createTime,
            oper_by                   as operBy,
            a.oper_time               as operTime,
            submit_status             as submitStatus,
            submit_date               as submitDate,
            b.client_code             as clientCode,
            b.client_name             as clientName,
            a.submit_status           as submitStatus,
            a.submit_date             as submitDate,
            b.client_code             as clientCode,
            b.client_name             as clientName,
            IFNULL(a.merge_status, 0) as mergeStatus
        FROM bms_ysbillmain a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        <where>
            AND IFNULL(fatherid,0) = 0
            <if test="billType != null "> and bill_type = #{billType}</if>
            <if test="fatherid != null "> and fatherid = #{fatherid}</if>
            <if test="billCode != null  and billCode != ''"> and bill_code = #{billCode}</if>
            <if test="collectionGuid != null  and collectionGuid != ''"> and collection_guid = #{collectionGuid}</if>
            <if test="billName != null  and billName != ''"> and bill_name like concat('%', #{billName}, '%')</if>
            <if test="billDate != null  and billDate != ''"> and bill_date = #{billDate}</if>
            <if test="clientName != null  and clientName != ''"> and b.client_name like concat('%', #{clientName}, '%')</if>
            <if test="companyIdList != null ">
                and a.company_id in
                <foreach item="compId" collection="companyIdList" open="(" separator="," close=")">
                    #{compId}
                </foreach>
            </if>
            <if test="clientId != null "> and client_id = #{clientId}</if>
            <if test="billMarking != null "> and bill_marking = #{billMarking}</if>
            <if test="votes != null "> and votes = #{votes}</if>
            <if test="billAmount != null "> and bill_amount = #{billAmount}</if>
            <if test="adjustedAmount != null "> and adjusted_amount = #{adjustedAmount}</if>
            <if test="ysAmount != null "> and ys_amount = #{ysAmount}</if>
            <if test="billState != null "> and bill_state = #{billState}</if>
            <if test="auditState != null "> and audit_state = #{auditState}</if>
            <if test="auditUser != null "> and audit_user = #{auditUser}</if>
            <if test="auditUserName != null  and auditUserName != ''"> and audit_user_name like concat('%', #{auditUserName}, '%')</if>
            <if test="auditTime != null "> and audit_time = #{auditTime}</if>
            <if test="auditRemark != null  and auditRemark != ''"> and audit_remark like concat('%', #{auditRemark}, '%')</if>
            <if test="ticketState != null "> and ticket_state = #{ticketState}</if>
            <if test="ticketAmount != null "> and ticket_amount = #{ticketAmount}</if>
            <if test="ticketNum != null "> and ticket_num = #{ticketNum}</if>
            <if test="ticketUserName != null  and ticketUserName != ''"> and ticket_user_name like concat('%', #{ticketUserName}, '%')</if>
            <if test="ticketTime != null "> and ticket_time = #{ticketTime}</if>
            <if test="hxAmount != null "> and hx_amount = #{hxAmount}</if>
            <if test="hxState != null "> and hx_state = #{hxState}</if>
            <if test="hxUserName != null  and hxUserName != ''"> and hx_user_name like concat('%', #{hxUserName}, '%')</if>
            <if test="hxTime != null "> and hx_time = #{hxTime}</if>
            <if test="hxRemark != null  and hxRemark != ''"> and hx_remark like concat('%', #{hxRemark}, '%')</if>
            <if test="billRemark != null  and billRemark != ''"> and bill_remark like concat('%', #{billRemark}, '%')</if>
            <if test="arClerk != null  and arClerk != ''"> and ar_clerk like concat('%', #{arClerk}, '%')</if>
            <if test="createCode != null  and createCode != ''"> and create_code = #{createCode}</if>
            <if test="createBy != null  and createBy != ''"> and create_by like concat('%', #{createBy}, '%')</if>
            <if test="beginCreateTime != null "> and a.create_time &gt;= #{beginCreateTime}</if>
            <if test="endCreateTime != null "> and a.create_time &lt;= #{endCreateTime}</if>
            <if test="createDeptId != null "> and create_dept_id = #{createDeptId}</if>
            <if test="operDeptId != null "> and oper_dept_id = #{operDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by like concat('%', #{operBy}, '%')</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
            <if test="ticketApplyState != null "> and ticket_apply_state = #{ticketApplyState}</if>
            <if test="ticketApplyTime != null "> and ticket_apply_time = #{ticketApplyTime}</if>
            <if test="ticketApplyRemark != null  and ticketApplyRemark != ''"> and ticket_apply_remark = #{ticketApplyRemark}</if>
            <if test="submitStatus != null  and submitStatus != ''"> and submit_status = #{submitStatus}</if>
            <if test="submitDate != null "> and submit_date = #{submitDate}</if>
        </where>
        order by create_time desc
    </select>


    <select id="selectBmsYsbillmainList" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" resultMap="BmsYsbillmainResult">
        SELECT
            a.id,
            a.bill_type,
            a.fatherid,
            a.bill_code,
            a.collection_guid,
            a.bill_name,
            a.bill_date,
            a.company_id,
            a.client_id,
            a.bill_marking,
            a.votes,
            a.bill_amount,
            a.adjusted_amount,
            a.ys_amount,
            a.bill_state,
            a.audit_state,
            a.audit_user,
            a.audit_user_name,
            a.audit_time,
            a.audit_remark,
            a.ticket_state,
            IFNULL(a.ticket_amount, 0) AS ticketAmount,
            a.ticket_num,
            a.ticket_user_name,
            a.ticket_time,
            a.hx_amount,
            a.hx_state,
            a.hx_user_name,
            a.hx_time,
            a.hx_remark,
            a.bill_remark,
            a.ar_clerk,
            a.create_code,
            a.create_by,
            a.create_time,
            a.create_dept_id,
            a.oper_dept_id,
            a.oper_code,
            a.oper_by,
            a.oper_time,
            a.del_flag,
            a.ticket_apply_state,
            a.ticket_apply_time,
            a.ticket_apply_remark,
            a.submit_status,
            a.submit_date,
            b.client_code                                        AS clientCode,
            b.client_name                                        AS clientName,
            b.invoice_type                                          invoiceType,
            b.opening_name                                          openingName,
            b.taxpayer_num                                          taxpayerNum,
            b.opening_bank                                          openingBank,
            b.card_number                                           cardNumber,
            b.link_phone                                            linkPhone,
            b.invoice_mode                                       as invoiceMode,
            b.tac_address                                        as tacAddress,
            IFNULL(a.ticket_apply_amount, 0)                        ticketApplyAmount,
            IFNULL(a.bill_source, 1)                             as billSource,
            IFNULL(a.merge_status, 0)                            AS mergeStatus,
            a.end_date                                           as endDate,
            case when IFNULL(fix.id, '') != '' then 1 else 0 end as isFixedfee,
            case when IFNULL(ad.id, '') != '' then 1 else 0 end  as isAddedfee
        FROM bms_ysbillmain a
        LEFT JOIN bms_clientinfo b on a.client_id = b.id
        LEFT JOIN bms_fixedfee fix on fix.bill_id = a.id and fix.del_flag = 0
        LEFT JOIN bms_addedfee ad on ad.bill_id = a.id and ad.del_flag = 0 AND ad.settle_type = 1
        <where>
            a.del_flag = 0
            and (IFNULL(a.fatherid,0)=0 or IFNULL(a.fatherid,0) = a.id)
            <if test="clientList!=null and clientList.size>0">
                and  b.client_code in
                <foreach item="client" collection="clientList" open="(" separator="," close=")">
                    #{client}
                </foreach>
            </if>
            <if test="billSource != null "> and IFNULL(a.bill_source,1) = #{billSource}</if>
            <if test="billType != null "> and a.bill_type = #{billType}</if>
            <if test="fatherid != null "> and a.fatherid = #{fatherid}</if>
            <if test="billCode != null  and billCode != ''"> and a.bill_code = #{billCode}</if>
            <if test="collectionGuid != null  and collectionGuid != ''"> and a.collection_guid = #{collectionGuid}</if>
            <if test="billName != null  and billName != ''"> and a.bill_name like concat('%', #{billName}, '%')</if>
            <if test="billDate != null  and billDate != ''"> and a.bill_date = #{billDate}</if>
            <if test="clientName != null  and clientName != ''"> and b.client_name like concat('%', #{clientName}, '%')</if>
            <if test="companyIdList != null ">
                and a.company_id in
                <foreach item="compId" collection="companyIdList" open="(" separator="," close=")">
                    #{compId}
                </foreach>
            </if>
            <if test="clientId != null "> and a.client_id = #{clientId}</if>
            <if test="billMarking != null "> and a.bill_marking = #{billMarking}</if>
            <if test="votes != null "> and a.votes = #{votes}</if>
            <if test="billAmount != null "> and a.bill_amount = #{billAmount}</if>
            <if test="adjustedAmount != null "> and a.adjusted_amount = #{adjustedAmount}</if>
            <if test="ysAmount != null "> and a.ys_amount = #{ysAmount}</if>
            <if test="billState != null "> and a.bill_state = #{billState}</if>
            <if test="auditState != null "> and a.audit_state = #{auditState}</if>
            <if test="auditUser != null "> and a.audit_user = #{auditUser}</if>
            <if test="auditUserName != null  and auditUserName != ''"> and a.audit_user_name like concat('%', #{auditUserName}, '%')</if>
            <if test="auditTime != null "> and a.audit_time = #{auditTime}</if>
            <if test="auditRemark != null  and auditRemark != ''"> and a.audit_remark like concat('%', #{auditRemark}, '%')</if>
            <if test="ticketState != null "> and a.ticket_state = #{ticketState}</if>
            <if test="ticketAmount != null "> and a.ticket_amount = #{ticketAmount}</if>
            <if test="ticketNum != null "> and a.ticket_num = #{ticketNum}</if>
            <if test="ticketUserName != null  and ticketUserName != ''"> and a.ticket_user_name like concat('%', #{ticketUserName}, '%')</if>
            <if test="ticketTime != null "> and a.ticket_time = #{ticketTime}</if>
            <if test="hxAmount != null "> and a.hx_amount = #{hxAmount}</if>
            <if test="hxState != null "> and a.hx_state = #{hxState}</if>
            <if test="hxUserName != null  and hxUserName != ''"> and a.hx_user_name like concat('%', #{hxUserName}, '%')</if>
            <if test="hxTime != null "> and a.hx_time = #{hxTime}</if>
            <if test="hxRemark != null  and hxRemark != ''"> and a.hx_remark like concat('%', #{hxRemark}, '%')</if>
            <if test="billRemark != null  and billRemark != ''"> and a.bill_remark like concat('%', #{billRemark}, '%')</if>
            <if test="arClerk != null  and arClerk != ''"> and a.ar_clerk like concat('%', #{arClerk}, '%')</if>
            <if test="createCode != null  and createCode != ''"> and a.create_code = #{createCode}</if>
            <if test="createBy != null  and createBy != ''"> and a.create_by like concat('%', #{createBy}, '%')</if>
            <if test="beginCreateTime != null "> and a.create_time &gt;= #{beginCreateTime}</if>
            <if test="endCreateTime != null "> and a.create_time &lt;= #{endCreateTime}</if>
            <if test="beginEndDate != null "> and a.end_date &gt;= #{beginEndDate}</if>
            <if test="endEndDate != null "> and a.end_date &lt;= #{endEndDate}</if>
            <if test="createDeptId != null "> and a.create_dept_id = #{createDeptId}</if>
            <if test="operDeptId != null "> and a.oper_dept_id = #{operDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and a.oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and a.oper_by like concat('%', #{operBy}, '%')</if>
            <if test="operTime != null "> and a.oper_time = #{operTime}</if>
            <if test="ticketApplyState != null "> and a.ticket_apply_state = #{ticketApplyState}</if>
            <if test="ticketApplyTime != null "> and a.ticket_apply_time = #{ticketApplyTime}</if>
            <if test="ticketApplyRemark != null  and ticketApplyRemark != ''"> and a.ticket_apply_remark = #{ticketApplyRemark}</if>
            <if test="submitStatus != null  and submitStatus != ''"> and a.submit_status = #{submitStatus}</if>
            <if test="submitDate != null "> and a.submit_date = #{submitDate}</if>
        </where>
        GROUP BY a.bill_code
        ORDER BY a.create_time DESC
    </select>

    <select id="selectBmsYsbillmainListBillDateByIds" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" resultMap="BmsYsbillmainResult">
        select bill_date from bms_ysbillmain
        <where>
            fatherid in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        group by bill_date
    </select>
    <select id="selectBmsYsbillmainListSonById" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" resultMap="BmsYsbillmainResult">
        select id from bms_ysbillmain
        <where>
            fatherid = #{id}
        </where>
        group by  id
    </select>
    <select id="selectBmsYsbillmainListSonByIds" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" resultMap="BmsYsbillmainResult">
        select fatherid from bms_ysbillmain
        <where>
            fatherid in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        group by fatherid
    </select>


    <select id="selectBmsYsbillmainListByIds" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" resultMap="BmsYsbillmainResult">
        SELECT
            a.id,
            bill_type,
            fatherid,
            bill_code,
            collection_guid,
            bill_name,
            bill_date,
            a.company_id,
            client_id,
            bill_marking,
            votes,
            bill_amount,
            adjusted_amount,
            ys_amount,
            bill_state,
            audit_state,
            audit_user,
            audit_user_name,
            audit_time,
            audit_remark,
            ticket_state,
            ticket_amount,
            ticket_num,
            ticket_user_name,
            ticket_time,
            hx_amount,
            hx_state,
            hx_user_name,
            hx_time,
            hx_remark,
            bill_remark,
            ar_clerk,
            create_code,
            create_by,
            create_time,
            create_dept_id,
            oper_dept_id,
            oper_code,
            oper_by,
            a.oper_time,
            a.del_flag,
            ticket_apply_state,
            ticket_apply_time,
            ticket_apply_remark,
            submit_status,
            submit_date,
            b.client_code as clientCode,
            b.client_name as clientName,
            exception_fee,
            a.order_level,
            a.un_claimes_amount
        FROM bms_ysbillmain a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        <where>
            a.id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectBmsYsbillmainListByIdsGroup" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" resultMap="BmsYsbillmainResult">
        SELECT client_id FROM bms_ysbillmain
        <where>
            id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        GROUP BY client_id
    </select>


    <select id="selectBmsYsbillmainById" parameterType="java.lang.String" resultMap="BmsYsbillmainResult">
        select
            a.id,
            a.bill_type,
            a.fatherid,
            a.bill_code,
            a.collection_guid,
            IFNULL(a.bill_source,1) as billSource,
            a.bill_name,
            a.bill_date,
            a.company_id,
            a.client_id,
            a.bill_marking,
            a.votes, bill_amount,
            a.adjusted_amount,
            a.ys_amount,
            a.bill_state,
            a.audit_state,
            a.audit_user,
            a.audit_user_name,
            a.audit_time,
            a.audit_remark,
            a.ticket_state,
            a.ticket_amount,
            a.ticket_num,
            a.ticket_user_name,
            a.ticket_time,
            a.hx_amount,
            a.hx_state,
            a.hx_user_name,
            a.hx_time,
            a.hx_remark,
            a.bill_remark,
            a.ar_clerk,
            a.create_code,
            a.create_by,
            a.create_time,
            a.create_dept_id,
            a.oper_dept_id,
            a.oper_code,
            a.oper_by,
            a.oper_time,
            a.del_flag,
            a.ticket_apply_state,
            a.ticket_apply_time,
            a.ticket_apply_remark,
            IFNULL(a.merge_status,0) as mergeStatus,
            a.submit_status,
            a.submit_date,
            b.client_code as clientCode,
            b.client_name as  clientName,
            b.invoice_type invoiceType,
            b.opening_name openingName,
            b.taxpayer_num taxpayerNum,
            b.opening_bank openingBank,
            b.card_number cardNumber,
            b.link_phone linkPhone,
            b.tac_address tacAddress,
            a.ticket_apply_remark,
            a.ticket_apply_allow_update,
            b.invoice_mode as invoiceMode ,
            case
                when IFNULL(fix.id,'')!='' then 1
                else 0 end
            AS isFixedfee ,
            case
                when IFNULL(ad.id,'')!='' then 1
                else 0 end
            AS isAddedfee ,
            IFNULL(a.exception_fee,0) as exception_fee ,
            a.warehouse_code as warehouse_code ,
            a.apply_audit_state as applyAuditState
        from bms_ysbillmain a
        left join bms_clientinfo b on a.client_id = b.id
        left join bms_fixedfee fix on fix.bill_id = a.id and fix.del_flag =0
        left join bms_addedfee ad on ad.bill_id = a.id and ad.del_flag =0
        WHERE a.del_flag = '0' AND a.id = #{id}
        GROUP BY a.bill_code
        order by a.create_time desc
    </select>


    <select id="selectBmsYsbillmainByBatchIds"  resultMap="BmsYsbillmainResult">
        SELECT
            a.id,
            a.bill_type,
            a.fatherid,
            a.bill_code,
            a.collection_guid,
            IFNULL(a.bill_source,1) AS billSource,
            a.bill_name,
            a.bill_date,
            a.company_id,
            a.client_id,
            a.bill_marking,
            a.votes, bill_amount,
            a.adjusted_amount,
            a.ys_amount,
            a.bill_state,
            a.audit_state,
            a.audit_user,
            a.audit_user_name,
            a.audit_time,
            a.audit_remark,
            a.ticket_state,
            a.ticket_amount,
            a.ticket_num,
            a.ticket_user_name,
            a.ticket_time,
            a.hx_amount,
            a.hx_state,
            a.hx_user_name,
            a.hx_time,
            a.hx_remark,
            a.bill_remark,
            a.ar_clerk,
            a.create_code,
            a.create_by,
            a.create_time,
            a.create_dept_id,
            a.oper_dept_id,
            a.oper_code,
            a.oper_by,
            a.oper_time,
            a.del_flag,
            a.ticket_apply_state,
            a.ticket_apply_time,
            a.ticket_apply_remark,
            IFNULL(a.merge_status,0) AS mergeStatus,
            a.submit_status,
            a.submit_date,
            b.client_code AS clientCode,
            b.client_name AS clientName,
            b.invoice_type invoiceType,
            b.opening_name openingName,
            b.taxpayer_num taxpayerNum,
            b.opening_bank openingBank,
            b.card_number cardNumber,
            b.link_phone linkPhone,
            b.tac_address tacAddress,
            a.ticket_apply_remark,
            a.ticket_apply_allow_update ,
            b.invoice_mode AS invoiceMode ,
            CASE WHEN IFNULL(fix.id,'')!='' THEN 1 ELSE 0 END AS isFixedfee ,
            CASE WHEN IFNULL(ad.id,'')!='' THEN 1 ELSE 0 END AS isAddedfee ,
            IFNULL(a.exception_fee,0) as exception_fee ,
            a.warehouse_code as warehouse_code
        FROM bms_ysbillmain a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_fixedfee fix ON fix.bill_id = a.id AND fix.del_flag =0
        LEFT JOIN bms_addedfee ad ON ad.bill_id = a.id AND ad.del_flag =0
        WHERE a.del_flag = 0
        AND a.id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY a.bill_code
        ORDER BY a.create_time DESC
    </select>

    <select id="selectBmsYsbillmainByIdBatch" resultMap="BmsYsbillmainResult">
        SELECT
            a.id,
            a.bill_type,
            a.fatherid,
            a.bill_code,
            a.collection_guid,
            IFNULL(a.bill_source,1) AS billSource,
            a.bill_name,
            a.bill_date,
            a.company_id,
            a.client_id,
            a.bill_marking,
            a.votes, bill_amount,
            a.adjusted_amount,
            a.ys_amount,
            a.bill_state,
            a.audit_state,
            a.audit_user,
            a.audit_user_name,
            a.audit_time,
            a.audit_remark,
            a.ticket_state,
            a.ticket_amount,
            a.ticket_num,
            a.ticket_user_name,
            a.ticket_time,
            a.hx_amount,
            a.hx_state,
            a.hx_user_name,
            a.hx_time,
            a.hx_remark,
            a.bill_remark,
            a.ar_clerk,
            a.create_code,
            a.create_by,
            a.create_time,
            a.create_dept_id,
            a.oper_dept_id,
            a.oper_code,
            a.oper_by,
            a.oper_time,
            a.del_flag,
            a.ticket_apply_state,
            a.ticket_apply_time,
            a.ticket_apply_remark,
            IFNULL(a.merge_status,0) as mergeStatus,
            a.submit_status,
            a.submit_date,
            b.client_code as clientCode,
            b.client_name as  clientName,
            b.invoice_type invoiceType,
            b.opening_name openingName,
            b.taxpayer_num taxpayerNum,
            b.opening_bank openingBank,
            b.card_number cardNumber,
            b.link_phone linkPhone,
            b.tac_address tacAddress,
            a.ticket_apply_remark,
            a.ticket_apply_allow_update,
            b.invoice_mode as invoiceMode,
            CASE WHEN IFNULL(fix.id,'')!='' THEN 1 ELSE 0 END AS isFixedfee,
            CASE WHEN IFNULL(ad.id,'')!='' THEN 1 ELSE 0 END AS isAddedfee,
            IFNULL(a.exception_fee,0) AS exception_fee,
            a.warehouse_code AS warehouse_code
        FROM bms_ysbillmain a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_fixedfee fix ON fix.bill_id = a.id AND fix.del_flag =0
        LEFT JOIN bms_addedfee ad ON ad.bill_id = a.id AND ad.del_flag =0
        WHERE a.del_flag = 0
        AND a.id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY a.bill_code
        ORDER BY a.create_time DESC
    </select>

    <select id="selectBmsYsbillmainHxAmtByBillCode" parameterType="java.lang.String" resultMap="BmsYsbillmainResult">
        select
            a.id,
            a.bill_type,
            a.fatherid,
            a.bill_code,
            a.collection_guid,
            IFNULL(a.bill_source,1) as billSource,
            a.bill_name,
            a.bill_date,
            a.company_id,
            a.client_id,
            a.bill_marking,
            a.votes, bill_amount,
            a.adjusted_amount,
            a.ys_amount,
            a.bill_state,
            a.audit_state,
            a.audit_user,
            a.audit_user_name,
            a.audit_time,
            a.audit_remark,
            a.ticket_state,
            a.ticket_amount,
            a.ticket_num,
            a.ticket_user_name,
            a.ticket_time,
            a.hx_amount,
            a.hx_state,
            a.hx_user_name,
            a.hx_time,
            a.hx_remark,
            a.bill_remark,
            a.ar_clerk,
            a.create_code,
            a.create_by,
            a.create_time,
            a.create_dept_id,
            a.oper_dept_id,
            a.oper_code,
            a.oper_by,
            a.oper_time,
            a.del_flag,
            a.ticket_apply_state,
            a.ticket_apply_time,
            a.ticket_apply_remark,
            IFNULL(a.merge_status,0) as mergeStatus,
            a.submit_status,
            a.submit_date
        from bms_ysbillmain a
        where 1=1 and a.del_flag = '0'
          and bill_code = #{code}
            limit 1
    </select>


    <select id="selectBmsYsbillmainByBillCode" parameterType="java.lang.String" resultMap="BmsYsbillmainResult">
        <include refid="selectBmsYsbillmainVo"/>
        where bill_code = #{billCode}   and del_flag = '0'
    </select>

    <insert id="insertBmsYsbillmainRetunId" useGeneratedKeys="true" keyProperty="id" keyColumn="id" parameterType="java.util.Map">
        insert into bms_ysbillmain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="billType != null">bill_type,</if>
            <if test="fatherid != null">fatherid,</if>
            <if test="billCode != null">bill_code,</if>
            <if test="collectionGuid != null">collection_guid,</if>
            <if test="billName != null">bill_name,</if>
            <if test="billDate != null">bill_date,</if>
            <if test="companyId != null">company_id,</if>
            <if test="clientId != null">client_id,</if>
            <if test="billMarking != null">bill_marking,</if>
            <if test="votes != null">votes,</if>
            <if test="billAmount != null">bill_amount,</if>
            <if test="adjustedAmount != null">adjusted_amount,</if>
            <if test="ysAmount != null">ys_amount,</if>
            <if test="exceptionFee != null">exception_fee,</if>
            <if test="billState != null">bill_state,</if>
            <if test="auditState != null">audit_state,</if>
            <if test="auditUser != null">audit_user,</if>
            <if test="auditUserName != null">audit_user_name,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="ticketState != null">ticket_state,</if>
            <if test="ticketAmount != null">ticket_amount,</if>
            <if test="ticketNum != null">ticket_num,</if>
            <if test="ticketUserName != null">ticket_user_name,</if>
            <if test="ticketTime != null">ticket_time,</if>
            <if test="hxAmount != null">hx_amount,</if>
            <if test="hxState != null">hx_state,</if>
            <if test="hxUserName != null">hx_user_name,</if>
            <if test="hxTime != null">hx_time,</if>
            <if test="hxRemark != null">hx_remark,</if>
            <if test="billRemark != null">bill_remark,</if>
            <if test="arClerk != null">ar_clerk,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="ticketApplyState != null">ticket_apply_state,</if>
            <if test="ticketApplyTime != null">ticket_apply_time,</if>
            <if test="ticketApplyRemark != null">ticket_apply_remark,</if>
            <if test="submitStatus != null">submit_status,</if>
            <if test="submitDate != null">submit_date,</if>
            <if test="mergeStatus != null">merge_status,</if>
            <if test="endDate != null">end_date,</if>
            <if test="warehouseCode!=null">warehouse_code,</if>
            <if test="orderLevel!=null">order_level,</if>
            <if test="unClaimesAmount!=null">un_claimes_amount,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="billType != null">#{billType},</if>
            <if test="fatherid != null">#{fatherid},</if>
            <if test="billCode != null">#{billCode},</if>
            <if test="collectionGuid != null">#{collectionGuid},</if>
            <if test="billName != null">#{billName},</if>
            <if test="billDate != null">#{billDate},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="billMarking != null">#{billMarking},</if>
            <if test="votes != null">#{votes},</if>
            <if test="billAmount != null">#{billAmount},</if>
            <if test="adjustedAmount != null">#{adjustedAmount},</if>
            <if test="ysAmount != null">#{ysAmount},</if>
            <if test="exceptionFee != null">#{exceptionFee},</if>
            <if test="billState != null">#{billState},</if>
            <if test="auditState != null">#{auditState},</if>
            <if test="auditUser != null">#{auditUser},</if>
            <if test="auditUserName != null">#{auditUserName},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="ticketState != null">#{ticketState},</if>
            <if test="ticketAmount != null">#{ticketAmount},</if>
            <if test="ticketNum != null">#{ticketNum},</if>
            <if test="ticketUserName != null">#{ticketUserName},</if>
            <if test="ticketTime != null">#{ticketTime},</if>
            <if test="hxAmount != null">#{hxAmount},</if>
            <if test="hxState != null">#{hxState},</if>
            <if test="hxUserName != null">#{hxUserName},</if>
            <if test="hxTime != null">#{hxTime},</if>
            <if test="hxRemark != null">#{hxRemark},</if>
            <if test="billRemark != null">#{billRemark},</if>
            <if test="arClerk != null">#{arClerk},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="ticketApplyState != null">#{ticketApplyState},</if>
            <if test="ticketApplyTime != null">#{ticketApplyTime},</if>
            <if test="ticketApplyRemark != null">#{ticketApplyRemark},</if>
            <if test="submitStatus != null">#{submitStatus},</if>
            <if test="submitDate != null">#{submitDate},</if>
            <if test="mergeStatus != null">#{mergeStatus},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="orderLevel != null">#{orderLevel},</if>
            <if test="unClaimesAmount != null">#{unClaimesAmount},</if>
        </trim>
    </insert>

    <select id="selectBillmainAndCostInfoListByIds"  resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean">
        select * from (
            SELECT
                CASE
                    WHEN c.expenses_dimension = 1 THEN bym.relate_code
                    ELSE '-'
                END                          AS businessCode,
                CASE
                    WHEN c.expenses_dimension = 1 THEN bym.relate_code
                    ELSE '-'
                END                          AS relateCode,
                c.warehouse_code                 AS warehouseCode,
                c.warehouse_name                 AS warehouseName,
                c.total_boxes                    AS totalBoxes,
                c.total_number                   AS totalNumber,
                c.total_weight                   AS totalWeight,
                c.total_volume                   AS totalVolume,
                c.total_cargo_value              AS cargoValue,
                c.id                             AS id,
                c.pk_id                          AS pkId,
                c.business_time                  AS businessTime,
                c.expenses_code                  AS expensesCode,
                c.client_id                      AS clientId,
                c.company_id                     AS companyId,
                c.expenses_type                  AS expensesType,
                c.expenses_dimension             AS costDimension,
                c.charge_type                    AS chargeType,
                c.expenses_mark                  AS feeFlag,
                c.quoter_rule_detail_id          AS quoteruleId,
                c.quoter_rule_detail_name        AS ruleName,
                c.adjust_remark                  AS remarks,
                IFNULL(c.freight, 0)             AS freight,
                IFNULL(c.delivery_fee, 0)        AS deliveryFee,
                IFNULL(c.Outboundsorting_fee, 0) AS OutboundsortingFee,
                IFNULL(c.shortbarge_fee, 0)      AS shortbargeFee,
                IFNULL(c.superframes_fee, 0)     AS superframesFee,
                IFNULL(c.excess_fee, 0)          AS excessFee,
                IFNULL(c.reduce_fee, 0)          AS reduceFee,
                IFNULL(c.Ultrafar_fee, 0)        AS UltrafarFee,
                IFNULL(c.exception_fee, 0)       AS exceptionFee,
                IFNULL(c.return_fee, 0)          AS returnFee,
                IFNULL(c.other_cost1, 0)         AS otherCost1,
                IFNULL(c.other_cost2, 0)         AS otherCost2,
                IFNULL(c.other_cost3, 0)         AS otherCost3,
                IFNULL(c.other_cost4, 0)         AS otherCost4,
                IFNULL(c.other_cost5, 0)         AS otherCost5,
                IFNULL(c.other_cost6, 0)         AS otherCost6,
                IFNULL(c.other_cost7, 0)         AS otherCost7,
                IFNULL(c.other_cost8, 0)         AS otherCost8,
                IFNULL(c.other_cost9, 0)         AS otherCost9,
                IFNULL(c.other_cost10, 0)        AS otherCost10,
                IFNULL(c.other_cost11, 0)        AS otherCost11,
                IFNULL(c.other_cost12, 0)        AS otherCost12,
                IFNULL(c.adjust_fee, 0)          AS adjustFee,
                null                             AS totalAmount,
                c.oper_code                      AS operCode,
                c.oper_by                        AS operBy,
                c.oper_time                      AS operTime,
                c.del_flag                       AS delFlag,
                c.bill_id                        AS billId,
                c.bill_date                      AS billDate,
                d.client_name                    AS clientName,
                d.client_code                    AS clientCode,
                c.main_code_id                   AS mainExpenseId,
                c.main_pk_id                     AS mainExpensePkId,
            FROM bms_yscost_info c
            LEFT JOIN bms_ysexpenses_middle bym ON bym.main_pk_id = c.main_pk_id AND bym.del_flag = 0
            LEFT JOIN bms_clientinfo d ON c.client_id = d.id
            WHERE c.del_flag = 0
            <if test="mainExpensePkId!=null ">
                c.main_pk_id = #{mainExpensePkId}
            </if>
            AND c.bill_id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND c.expenses_type IN
            <foreach item="type" collection="codeTypes" open="(" separator="," close=")">
                #{type}
            </foreach>
            GROUP BY c.id
        ) a
    </select>

    <select id="selectBillmainAndCostInfoListByIdsNew"  resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean">
        SELECT * FROM (
            SELECT
                DATE_FORMAT(c.oper_time, '%Y-%m-%d %H:%i:%s')    AS operTime,
                CASE
                    WHEN c.expenses_dimension = 1 THEN bym.relate_code
                    ELSE '-'
                END                                              AS businessCode,
                CASE
                    WHEN c.expenses_dimension = 1 THEN bym.relate_code
                    ELSE '-'
                END                                              AS relateCode,
                c.expenses_mark                                  AS feeFlag,
                case
                    when c.expenses_mark = 1 then '正常费用'
                    when c.expenses_mark = 2 then '冲销费'
                else '' END                                  AS feeFlagName,
                IFNULL(c.adjust_fee, 0)                          AS adjustFee,
                c.adjust_remark                                  AS adjustRemark,
                c.company_id                                     AS companyId,
                DATE_FORMAT(c.create_time, '%Y-%m-%d %H:%i:%s')  AS createTimeyw,
                DATE_FORMAT(c.signing_date, '%Y-%m-%d %H:%i:%s') AS signingDate,
                DATE_FORMAT(c.order_date, '%Y-%m-%d %H:%i:%s')   AS orderDate,
                DATE_FORMAT(c.signing_date, '%Y-%m-%d %H:%i:%s') AS instorageTime,
                d.client_name                                    AS clientName,
                d.client_code                                    AS clientCode,
                c.total_code_number                              AS skuNumber,
                IFNULL(c.freight, 0)                             AS freight,
                IFNULL(c.delivery_fee, 0)                        AS deliveryFee,
                IFNULL(c.Outboundsorting_fee, 0)                 AS OutboundsortingFee,
                IFNULL(c.shortbarge_fee, 0)                      AS shortbargeFee,
                IFNULL(c.superframes_fee, 0)                     AS superframesFee,
                IFNULL(c.excess_fee, 0)                          AS excessFee,
                IFNULL(c.reduce_fee, 0)                          AS reduceFee,
                IFNULL(c.Ultrafar_fee, 0)                        AS UltrafarFee,
                IFNULL(c.exception_fee, 0)                       AS exceptionFee,
                IFNULL(c.return_fee, 0)                          AS returnFee,
                IFNULL(c.other_cost1, 0)                         AS otherCost1,
                IFNULL(c.other_cost2, 0)                         AS otherCost2,
                IFNULL(c.other_cost3, 0)                         AS otherCost3,
                IFNULL(c.other_cost4, 0)                         AS otherCost4,
                IFNULL(c.other_cost5, 0)                         AS otherCost5,
                IFNULL(c.other_cost6, 0)                         AS otherCost6,
                IFNULL(c.other_cost7, 0)                         AS otherCost7,
                IFNULL(c.other_cost8, 0)                         AS otherCost8,
                IFNULL(c.other_cost9, 0)                         AS otherCost9,
                IFNULL(c.other_cost10, 0)                        AS otherCost10,
                IFNULL(c.other_cost11, 0)                        AS otherCost11,
                IFNULL(c.other_cost12, 0)                        AS otherCost12,
                IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
                IFNULL(c.shortbarge_fee, 0) + IFNULL(c.superframes_fee, 0)
                + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
                IFNULL(c.exception_fee, 0) +
                IFNULL(c.return_fee, 0)                          AS basicsFee, -- 基础费合计
                IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) + IFNULL(c.other_cost3, 0) + IFNULL(c.other_cost4, 0) +
                IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) + IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) +
                IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) + IFNULL(c.other_cost11, 0) +
                IFNULL(c.other_cost12, 0)                        AS otherFee,  -- 其他费合计
                IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
                IFNULL(c.shortbarge_fee, 0) + IFNULL(c.superframes_fee, 0)
                + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
                IFNULL(c.exception_fee, 0) + IFNULL(c.return_fee, 0) + IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) +
                IFNULL(c.other_cost3, 0) + IFNULL(c.other_cost4, 0) + IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) +
                IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) + IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) +
                IFNULL(c.other_cost11, 0) +
                IFNULL(c.other_cost12, 0)                        AS sumFee,    -- 总费用
                IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
                IFNULL(c.shortbarge_fee, 0) + IFNULL(c.superframes_fee, 0)
                + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
                IFNULL(c.exception_fee, 0) + IFNULL(c.return_fee, 0) + IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) +
                IFNULL(c.other_cost3, 0) + IFNULL(c.other_cost4, 0) + IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) +
                IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) + IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) +
                IFNULL(c.other_cost11, 0) +
                IFNULL(c.other_cost12, 0)                        AS sumAmt,    -- 总费用
                c.oper_by                                        AS operBy,
                c.remarks                                        AS remarks,
                c.total_number                                   AS totalNumber,
                c.total_weight                                   AS totalWeight,
                c.total_volume                                   AS totalVolume,
                c.total_cargo_value                              AS cargoValue,
                c.total_boxes                                    AS totalBoxes,
                c.warehouse_name                                 AS warehouseNameFh,
                c.warehouse_code                                 AS warehouseCode,
                c.warehouse_name                                 AS warehouseName,
                c.id                                             AS id,
                c.pk_id                                          AS pkId,
                c.signing_date                                   AS businessTime,
                c.expenses_code                                  AS expensesCode,
                null                                             AS businessType,
                c.client_id                                      AS clientId,
                c.expenses_type                                  AS expensesType,
                case
                    when c.expenses_type = 1 then '订单'
                    when c.expenses_type = 2 then '出库单'
                    when c.expenses_type = 3 then '入库单'
                    when c.expenses_type = 4 then '库存单'
                else '' end
                AS expensesTypeName,
                c.expenses_dimension                             AS costDimension,
                case
                    when c.expenses_dimension = 1 then '单'
                    when c.expenses_dimension = 2 then '趟'
                    when c.expenses_dimension = 3 then '日'
                    when c.expenses_dimension = 4 then '月'
                    when c.expenses_dimension = 5 then '品规'
                else '' end                                  AS costDimensionName,
                c.charge_type                                    AS chargeType,
                case
                    when c.charge_type = 1 then '自动计费'
                    when c.charge_type = 2 then '手动计费'
                else '' end
                AS chargeTypeName,
                c.quoter_rule_detail_id                          AS quoteruleId,
                c.quoter_rule_detail_name                        AS ruleName,
                null                                             AS totalAmount,
                c.oper_code                                      AS operCode,
                c.del_flag                                       AS delFlag,
                c.bill_id                                        AS billId,
                c.bill_date                                      AS billDate,
                c.is_increment                                   AS isIncrement,
                c.fee_type_first                                 AS feeTypeFirst,
                c.total_extra_fee_number                         AS totalQuantity,
                c.total_extra_fee_price                          AS price,
                c.expenses_type                                  AS orderType,
                c.remarks                                        AS automaticBillingRemark,
                c.main_code_id                                   AS mainExpenseId,
                c.main_pk_id                                     AS mainExpensePkId,
                c.settle_type                                    AS settleType,
                c.settle_amount                                  AS settleAmount
            FROM bms_yscost_info c
            LEFT JOIN bms_clientinfo d on c.client_id = d.id
            LEFT JOIN bms_ysexpenses_middle bym on bym.main_pk_id = c.main_pk_id and bym.del_flag = 0
            WHERE c.del_flag = 0
            <if test="mainExpensePkId!=null ">
                c.main_pk_id = #{mainExpensePkId}
            </if>
            AND c.bill_id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND c.expenses_type IN
            <foreach item="type" collection="codeTypes" open="(" separator="," close=")">
                #{type}
            </foreach>
            GROUP BY c.id
        ) a
    </select>

    <select id="selectBillmainAndPurseList"  resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean">
        select
        a.id as codeInfoId,
        null as codeType,
        null as relateCode,
        null as schedulingBillCode,
        a.relation_code as clientCode,
        b.client_name as clientName,
        null as companyId,
        null as networkCode,
        null as warehouseCode,
        null as totalBoxes,
        null as totalNumber,
        null as totalWeight,
        null as totalVolume,
        null as cargoValue,
        null as orderDate,
        null as signingDate,
        null as ifAutarky,
        null as storageServiceProvider,
        null as lineCode,
        null as lineName,
        null as ifBaseStores,
        null as ifSuperBaseKilometer,
        null as storeDistanceKilometer,
        null as deliveryCode,
        null as deliveryName,
        null as storeCode,
        null as receivingStore,
        null as provinceOrigin,
        null as originatingCity,
        null as originatingArea,
        null as originatingAddress,
        null as destinationProvince,
        null as destinationCity,
        null as destinationArea,
        null as destinationAddress,
        null as costStatus,
        null as billingStatus,
        null as warehouseArea,
        null as volume,
        null as weight,
        null as trust,
        null as temperatureType,
        null as boxType,
        null as oddBoxes,
        null as instorageTime,
        a.sku_name as skuName,
        null as skuCode,
        a.ys_feecode as ysFeecode,
        a.register_time as registerTime,
        a.registerr_companyname as registerrCompanyname,
        a.fee_flag as feeFlag,
        a.ys_feecode as expensesCode,
        null as ysbilType,
        a.id as id,
        null as pkId,
        null as expensesCode,
        null as businessType,
        null as clientId,
        null as institutionId,
        null as expensesType,
        null as costDimension,
        null as chargeType,
        null as feeFlag,
        null as quoteruleId,
        null as ruleName,
        null as remarks,
        null as adjustRemark,
        null as freight,
        null as deliveryFee,
        null as OutboundsortingFee,
        null as shortbargeFee,
        null as superframesFee,
        null as excessFee,
        null as UltrafarFee,
        null as exceptionFee,
        null as otherCost1,
        null as otherCost2,
        null as otherCost3,
        null as otherCost4,
        null as otherCost5,
        null as otherCost6,
        null as otherCost7,
        null as otherCost8,
        null as otherCost9,
        null as otherCost10,
        null as otherCost11,
        null as otherCost12,
        null as adjustFee,
        a.num as num,
        a.price as price,
        a.unit as unit,
        /*a.remark as remark,*/
        a.adjust_remark as remark,
        a.spec as spec,
        a.total_amount as totalAmount,
        a.oper_code as operCode,
        a.oper_by as operBy,
        a.oper_time as operTime,
        a.del_flag as delFlag,
        a.ysbill_id as billId,
        a.bill_date as billDate
        from bms_yspurchase_feeinfo a
        left join bms_clientinfo b
        on a.relation_code = b.client_code
        where
        a.ysbill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="selectBillmainAndCostInfoList" parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean">
        SELECT * FROM (
            SELECT
                CASE
                    WHEN c.expenses_dimension = 1 THEN d.relate_code
                    ELSE '-'
                END                              AS businessCode,
                CASE
                    WHEN c.expenses_dimension = 1 THEN d.relate_code
                    ELSE '-'
                END                              AS relateCode,
                c.warehouse_code                 AS warehouseCode,
                c.warehouse_name                 AS warehouseName,
                c.total_boxes                    AS totalBoxes,
                c.total_number                   AS totalNumber,
                c.total_weight                   AS totalWeight,
                c.total_volume                   AS totalVolume,
                c.total_cargo_value              AS cargoValue,
                c.id                             AS id,
                c.pk_id                          AS pkId,
                c.business_time                  AS businessTime,
                c.expenses_code                  AS expensesCode,
                c.client_id                      AS clientId,
                c.company_id                     AS companyId,
                c.expenses_type                  AS expensesType,
                c.expenses_dimension             AS costDimension,
                c.charge_type                    AS chargeType,
                c.expenses_mark                  AS feeFlag,
                c.quoter_rule_detail_id          AS quoteruleId,
                c.quoter_rule_detail_name        AS ruleName,
                c.adjust_remark                  AS remarks,
                IFNULL(c.freight, 0)             AS freight,
                IFNULL(c.delivery_fee, 0)        AS deliveryFee,
                IFNULL(c.Outboundsorting_fee, 0) AS OutboundsortingFee,
                IFNULL(c.shortbarge_fee, 0)      AS shortbargeFee,
                IFNULL(c.superframes_fee, 0)     AS superframesFee,
                IFNULL(c.excess_fee, 0)          AS excessFee,
                IFNULL(c.reduce_fee, 0)          AS reduceFee,
                IFNULL(c.Ultrafar_fee, 0)        AS UltrafarFee,
                IFNULL(c.exception_fee, 0)       AS exceptionFee,
                IFNULL(c.return_fee, 0)          AS returnFee,
                IFNULL(c.other_cost1, 0)         AS otherCost1,
                IFNULL(c.other_cost2, 0)         AS otherCost2,
                IFNULL(c.other_cost3, 0)         AS otherCost3,
                IFNULL(c.other_cost4, 0)         AS otherCost4,
                IFNULL(c.other_cost5, 0)         AS otherCost5,
                IFNULL(c.other_cost6, 0)         AS otherCost6,
                IFNULL(c.other_cost7, 0)         AS otherCost7,
                IFNULL(c.other_cost8, 0)         AS otherCost8,
                IFNULL(c.other_cost9, 0)         AS otherCost9,
                IFNULL(c.other_cost10, 0)        AS otherCost10,
                IFNULL(c.other_cost11, 0)        AS otherCost11,
                IFNULL(c.other_cost12, 0)        AS otherCost12,
                IFNULL(c.adjust_fee, 0)          AS adjustFee,
                NULL                             AS totalAmount,
                c.oper_code                      AS operCode,
                c.oper_by                        AS operBy,
                c.oper_time                      AS operTime,
                c.del_flag                       AS delFlag,
                c.bill_id                        AS billId,
                c.bill_date                      AS billDate,
                d.client_name                    AS clientName,
                d.client_code                    AS clientCode,
                c.main_code_id                   AS mainExpenseId,
                c.main_pk_id                     AS mainExpensePkId
            FROM bms_yscost_info c
            LEFT JOIN bms_ysexpenses_middle d ON d.main_pk_id = c.main_pk_id AND d.del_flag = 0
            LEFT JOIN bms_clientinfo d ON c.client_id = d.id
            WHERE c.del_flag = 0
            AND c.bill_id = #{id}
            <if test="mainExpensePkId!=null ">
                c.main_pk_id = #{mainExpensePkId}
            </if>
            AND c.expenses_type IN
            <foreach item="type" collection="codeTypes" open="(" separator="," close=")">
                #{type}
            </foreach>
            GROUP BY c.id
        ) a
    </select>

    <select id="selectBillmainAndCostInfoListNew" parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean">
        SELECT * FROM (
            SELECT
                DATE_FORMAT(c.oper_time, '%Y-%m-%d %H:%i:%s')    AS operTime,      -- 计费时间
                CASE
                    WHEN c.expenses_dimension = 1 THEN bym.relate_code
                    ELSE '-'
                END                                             AS businessCode,
                CASE
                    WHEN c.expenses_dimension = 1 THEN bym.relate_code
                    ELSE '-'
                END                                             AS relateCode,
                c.expenses_mark                                 AS feeFlag,       -- 费用标识
                CASE
                    WHEN c.expenses_mark = 1 THEN '正常费用'
                    WHEN c.expenses_mark = 2 THEN '冲销费'
                    ELSE '-'
                END                                              AS feeFlagName,   -- 费用标识名称
                IFNULL(c.adjust_fee, 0)                          AS adjustFee,     -- 调账费
                c.adjust_remark                                  AS adjustRemark,  -- 调账备注
                c.company_id                                     AS companyId,     -- 单据所属
                DATE_FORMAT(c.create_time, '%Y-%m-%d %H:%i:%s')  AS createTimeyw,  -- 新增时间
                DATE_FORMAT(c.signing_date, '%Y-%m-%d %H:%i:%s') AS signingDate,   -- 签收时间
                DATE_FORMAT(c.order_date, '%Y-%m-%d %H:%i:%s')   AS orderDate,     -- 订单日期
                DATE_FORMAT(c.signing_date, '%Y-%m-%d %H:%i:%s') AS instorageTime, -- 库存日期
                d.client_name                                    AS clientName,    -- 客户名称
                d.client_code                                    AS clientCode,    -- 客户编码
                IFNULL(c.freight, 0)                             AS freight,
                IFNULL(c.delivery_fee, 0)                        AS deliveryFee,
                IFNULL(c.Outboundsorting_fee, 0)                 AS OutboundsortingFee,
                IFNULL(c.shortbarge_fee, 0)                      AS shortbargeFee,
                IFNULL(c.superframes_fee, 0)                     AS superframesFee,
                IFNULL(c.excess_fee, 0)                          AS excessFee,
                IFNULL(c.reduce_fee, 0)                          AS reduceFee,
                IFNULL(c.Ultrafar_fee, 0)                        AS UltrafarFee,
                IFNULL(c.exception_fee, 0)                       AS exceptionFee,
                IFNULL(c.return_fee, 0)                          AS returnFee,
                IFNULL(c.other_cost1, 0)                         AS otherCost1,
                IFNULL(c.other_cost2, 0)                         AS otherCost2,
                IFNULL(c.other_cost3, 0)                         AS otherCost3,
                IFNULL(c.other_cost4, 0)                         AS otherCost4,
                IFNULL(c.other_cost5, 0)                         AS otherCost5,
                IFNULL(c.other_cost6, 0)                         AS otherCost6,
                IFNULL(c.other_cost7, 0)                         AS otherCost7,
                IFNULL(c.other_cost8, 0)                         AS otherCost8,
                IFNULL(c.other_cost9, 0)                         AS otherCost9,
                IFNULL(c.other_cost10, 0)                        AS otherCost10,
                IFNULL(c.other_cost11, 0)                        AS otherCost11,
                IFNULL(c.other_cost12, 0)                        AS otherCost12,
                IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
                IFNULL(c.shortbarge_fee, 0) + IFNULL(c.superframes_fee, 0)
                + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
                IFNULL(c.exception_fee, 0) +
                IFNULL(c.return_fee, 0)                          AS basicsFee,     -- 基础费合计
                IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) + IFNULL(c.other_cost3, 0) + IFNULL(c.other_cost4, 0) +
                IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) + IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) +
                IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) + IFNULL(c.other_cost11, 0) +
                IFNULL(c.other_cost12, 0)                        AS otherFee,      -- 其他费合计
                IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
                IFNULL(c.shortbarge_fee, 0) + IFNULL(c.superframes_fee, 0)
                + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
                IFNULL(c.exception_fee, 0) + IFNULL(c.return_fee, 0) + IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) +
                IFNULL(c.other_cost3, 0) + IFNULL(c.other_cost4, 0) + IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) +
                IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) + IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) +
                IFNULL(c.other_cost11, 0) +
                IFNULL(c.other_cost12, 0)                        AS sumFee,        -- 总费用
                IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
                IFNULL(c.shortbarge_fee, 0) + IFNULL(c.superframes_fee, 0)
                + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
                IFNULL(c.exception_fee, 0) + IFNULL(c.return_fee, 0) + IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) +
                IFNULL(c.other_cost3, 0) + IFNULL(c.other_cost4, 0) + IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) +
                IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) + IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) +
                IFNULL(c.other_cost11, 0) +
                IFNULL(c.other_cost12, 0)                        AS sumAmt,        -- 总费用
                c.oper_by                                        AS operBy,        -- 计费人名称
                c.remarks                                        AS remarks,       -- 计费备注
                c.total_number                                   AS totalNumber,   -- 总件数
                c.total_weight                                   AS totalWeight,   -- 总重量
                c.total_volume                                   AS totalVolume,   -- 总体积
                c.total_cargo_value                              AS cargoValue,    -- 总货值
                c.total_boxes                                    AS totalBoxes,    -- 总箱数
                null                                             AS warehouseCode,
                null                                             AS warehouseName,
                c.id                                             AS id,
                c.pk_id                                          AS pkId,
                c.signing_date                                   AS businessTime,
                c.expenses_code                                  AS expensesCode,
                null                                             AS businessType,
                c.client_id                                      AS clientId,
                c.expenses_type                                  AS expensesType,
                case
                when c.expenses_type = 1 then '订单'
                when c.expenses_type = 2 then '出库单'
                when c.expenses_type = 3 then '入库单'
                when c.expenses_type = 4 then '库存单'
                else '' end
                AS expensesTypeName,
                c.expenses_dimension                             AS costDimension,
                case
                when c.expenses_dimension = 1 then '单'
                when c.expenses_dimension = 2 then '趟'
                when c.expenses_dimension = 3 then '日'
                when c.expenses_dimension = 4 then '月'
                when c.expenses_dimension = 5 then '品规'
                else '' end                                  AS costDimensionName,
                c.charge_type                                    AS chargeType,
                case
                when c.charge_type = 1 then '自动计费'
                when c.charge_type = 2 then '手动计费'
                else '' end
                AS chargeTypeName,
                c.quoter_rule_detail_id                          AS quoteruleId,
                c.quoter_rule_detail_name                        AS ruleName,
                null                                             AS totalAmount,
                c.oper_code                                      AS operCode,
                c.del_flag                                       AS delFlag,
                c.bill_id                                        AS billId,
                c.bill_date                                      AS billDate,
                c.is_increment                                   AS isIncrement,
                c.fee_type_first                                 AS feeTypeFirst,
                c.total_extra_fee_number                         AS totalQuantity,
                c.total_extra_fee_price                          AS price,
                c.expenses_type                                  AS orderType,
                c.remarks                                        AS automaticBillingRemark,
                c.expenses_type                                  AS codeType,
                c.main_code_id                                   AS mainExpenseId,
                c.main_pk_id                                     AS mainExpensePkId,
                c.settle_type                                    AS settleType,
                c.settle_amount                                  AS settleAmount
            FROM bms_yscost_info c
            LEFT JOIN bms_clientinfo d on c.client_id = d.id
            LEFT JOIN bms_ysexpenses_middle bym on bym.main_pk_id = c.main_pk_id and bym.del_flag = 0
            WHERE c.del_flag = 0
            <if test="mainExpensePkId!=null ">
                c.main_pk_id = #{mainExpensePkId}
            </if>
            AND c.bill_id = #{id}
            AND c.expenses_type IN
            <foreach item="type" collection="codeTypes" open="(" separator="," close=")">
                #{type}
            </foreach>
            GROUP BY c.id
        ) a
    </select>

    <select id="selectBillmainAndCostInfoListNew2"  resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean">
        SELECT * FROM (
            SELECT
                DATE_FORMAT(c.oper_time, '%Y-%m-%d %H:%i:%s')              AS operTime,
                CASE
                    WHEN c.expenses_dimension = 1 THEN bym.relate_code
                    ELSE '-'
                END                                                        AS businessCode,
                CASE
                    WHEN c.expenses_dimension = 1 THEN bym.relate_code
                    ELSE '-'
                END                                                        AS relateCode,
                c.expenses_mark                                            AS feeFlag,
                case
                    when c.expenses_mark = 1 then '正常费用'
                    when c.expenses_mark = 2 then '冲销费'
                else '' end                                                AS feeFlagName,
                IFNULL(c.adjust_fee, 0)                                    AS adjustFee,
                c.adjust_remark                                            AS adjustRemark,
                c.company_id                                               AS companyId,
                DATE_FORMAT(c.create_time, '%Y-%m-%d %H:%i:%s')            AS createTimeyw,
                DATE_FORMAT(c.signing_date, '%Y-%m-%d %H:%i:%s')           AS signingDate,
                DATE_FORMAT(c.order_date, '%Y-%m-%d %H:%i:%s')             AS orderDate,
                d.client_name                                              AS clientName,
                d.client_code                                              AS clientCode,
                IFNULL(c.freight, 0)                                       AS freight,
                IFNULL(c.delivery_fee, 0)                                  AS deliveryFee,
                IFNULL(c.Outboundsorting_fee, 0)                           AS OutboundsortingFee,
                IFNULL(c.shortbarge_fee, 0)                                AS shortbargeFee,
                IFNULL(c.superframes_fee, 0)                               AS superframesFee,
                IFNULL(c.excess_fee, 0)                                    AS excessFee,
                IFNULL(c.reduce_fee, 0)                                    AS reduceFee,
                IFNULL(c.Ultrafar_fee, 0)                                  AS UltrafarFee,
                IFNULL(c.exception_fee, 0)                                 AS exceptionFee,
                IFNULL(c.return_fee, 0)                                    AS returnFee,
                IFNULL(c.other_cost1, 0)                                   AS otherCost1,
                IFNULL(c.other_cost2, 0)                                   AS otherCost2,
                IFNULL(c.other_cost3, 0)                                   AS otherCost3,
                IFNULL(c.other_cost4, 0)                                   AS otherCost4,
                IFNULL(c.other_cost5, 0)                                   AS otherCost5,
                IFNULL(c.other_cost6, 0)                                   AS otherCost6,
                IFNULL(c.other_cost7, 0)                                   AS otherCost7,
                IFNULL(c.other_cost8, 0)                                   AS otherCost8,
                IFNULL(c.other_cost9, 0)                                   AS otherCost9,
                IFNULL(c.other_cost10, 0)                                  AS otherCost10,
                IFNULL(c.other_cost11, 0)                                  AS otherCost11,
                IFNULL(c.other_cost12, 0)                                  AS otherCost12,
                SUM(IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
                IFNULL(c.shortbarge_fee, 0) + IFNULL(c.superframes_fee, 0)
                + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
                IFNULL(c.exception_fee, 0) + IFNULL(c.return_fee, 0))  AS basicsFee,
                SUM(IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) + IFNULL(c.other_cost3, 0) + IFNULL(c.other_cost4, 0) +
                IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) + IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) +
                IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) + IFNULL(c.other_cost11, 0) +
                IFNULL(c.other_cost12, 0))                             AS otherFee,
                SUM(IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
                IFNULL(c.shortbarge_fee, 0) + IFNULL(c.superframes_fee, 0)
                + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
                IFNULL(c.exception_fee, 0) + IFNULL(c.return_fee, 0) + IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) +
                IFNULL(c.other_cost3, 0) + IFNULL(c.other_cost4, 0) + IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) +
                IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) + IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) +
                IFNULL(c.other_cost11, 0) + IFNULL(c.other_cost12, 0)) AS sumFee,
                SUM(IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
                IFNULL(c.shortbarge_fee, 0) + IFNULL(c.superframes_fee, 0)
                + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
                IFNULL(c.exception_fee, 0) + IFNULL(c.return_fee, 0) + IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) +
                IFNULL(c.other_cost3, 0) + IFNULL(c.other_cost4, 0) + IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) +
                IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) + IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) +
                IFNULL(c.other_cost11, 0) + IFNULL(c.other_cost12, 0)) AS sumAmt,
                c.oper_by                                                  AS operBy,
                c.remarks                                                  AS remarks,
                c.total_code_number                                        AS totalNumber,
                c.total_volume                                             AS totalVolume,
                c.total_cargo_value                                        AS cargoValue,
                c.total_boxes                                              AS totalBoxes,
                GROUP_CONCAT(distinct ware.warehouse_name)                 AS warehouseNameFh,
                c.warehouse_code                                           AS warehouseCode,
                c.warehouse_name                                           AS warehouseName,
                c.id                                                       AS id,
                c.pk_id                                                    AS pkId,
                c.signing_date                                             AS businessTime,
                c.expenses_code                                            AS expensesCode,
                c.client_id                                                AS clientId,
                c.expenses_type                                            AS expensesType,
                CASE
                    WHEN c.expenses_type = 1 THEN '订单'
                    WHEN c.expenses_type = 2 THEN '出库单'
                    WHEN c.expenses_type = 3 THEN '入库单'
                    WHEN c.expenses_type = 4 THEN '库存单'
                    else ''
                end                                            AS expensesTypeName,
                c.expenses_dimension                                       AS costDimension,
                CASE
                    WHEN c.expenses_dimension = 1 THEN '单'
                    WHEN c.expenses_dimension = 2 THEN '趟'
                    WHEN c.expenses_dimension = 3 THEN '日'
                    WHEN c.expenses_dimension = 4 THEN '月'
                    WHEN c.expenses_dimension = 5 THEN '品规'
                else ''
                end                                            AS costDimensionName,
                c.charge_type                                              AS chargeType,
                case
                    when c.charge_type = 1 then '自动计费'
                    when c.charge_type = 2 then '手动计费'
                else ''
                end                                            AS chargeTypeName,
                c.quoter_rule_detail_id                                    AS quoteruleId,
                c.quoter_rule_detail_name                                  AS ruleName,
                null                                                       AS totalAmount,
                c.oper_code                                                AS operCode,
                c.del_flag                                                 AS delFlag,
                c.bill_id                                                  AS billId,
                c.bill_date                                                AS billDate,
                c.is_increment                                             AS isIncrement,
                c.fee_type_first                                           AS feeTypeFirst,
                1                                                          AS orderType,
                c.remarks                                                  AS automaticBillingRemark,
                IFNULL(c.total_weight, 0)                                  AS weight,
                IFNULL(c.total_weight, 0) / 1000                           AS totalWeight,
                c.main_code_id                                             AS mainExpenseId,
                c.main_pk_id                                               AS mainExpensePkId,
                c.settle_type                                              AS settleType,
                c.settle_amount                                            AS settleAmount
            FROM bms_yscost_info c
            LEFT JOIN bms_clientinfo d ON c.client_id = d.id
            LEFT JOIN bms_ysexpenses_middle bym ON bym.main_pk_id = c.main_pk_id AND bym.del_flag = 0
            LEFT JOIN mdm_warehouseinfo ware ON ware.warehouse_code = c.warehouse_code AND ware.del_flag = 0
            WHERE c.del_flag = 0
            <if test="mainExpensePkId!=null ">
                c.main_pk_id = #{mainExpensePkId}
            </if>
            AND c.bill_id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND c.expenses_type IN
            <foreach item="type" collection="codeTypes" open="(" separator="," close=")">
                #{type}
            </foreach>
            GROUP BY c.id
    ) a
    </select>

    <select id="selectBillmainAndCostInfoListNewKc"  resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean">
        select * from (
        SELECT DATE_FORMAT(c.oper_time, '%Y-%m-%d %H:%i:%s')    AS operTime,
            CASE
                WHEN c.expenses_dimension = 1 THEN bym.relate_code
                ELSE '-'
            END                                              AS businessCode,
            CASE
                WHEN c.expenses_dimension = 1 THEN bym.relate_code
                ELSE '-'
            END                                              AS relateCode,
            c.expenses_mark                                  AS feeFlag,
            CASE
                WHEN c.expenses_mark = 1 THEN '正常费用'
                WHEN c.expenses_mark = 2 THEN '冲销费'
            ELSE '-' END                                     AS feeFlagName,
            IFNULL(c.adjust_fee, 0)                          AS adjustFee,
            c.adjust_remark                                  AS adjustRemark,
            c.company_id                                     AS companyId,
            DATE_FORMAT(c.create_time, '%Y-%m-%d %H:%i:%s')  AS createTimeyw,
            DATE_FORMAT(c.signing_date, '%Y-%m-%d %H:%i:%s') AS instorageTime,
            d.client_name                                    AS clientName,
            d.client_code                                    AS clientCode,
            c.total_code_number                              AS skuNumber,
            IFNULL(c.freight, 0)                             AS freight,
            IFNULL(c.delivery_fee, 0)                        AS deliveryFee,
            IFNULL(c.Outboundsorting_fee, 0)                 AS OutboundsortingFee,
            IFNULL(c.shortbarge_fee, 0)                      AS shortbargeFee,
            IFNULL(c.superframes_fee, 0)                     AS superframesFee,
            IFNULL(c.excess_fee, 0)                          AS excessFee,
            IFNULL(c.reduce_fee, 0)                          AS reduceFee,
            IFNULL(c.Ultrafar_fee, 0)                        AS UltrafarFee,
            IFNULL(c.exception_fee, 0)                       AS exceptionFee,
            IFNULL(c.return_fee, 0)                          AS returnFee,
            IFNULL(c.other_cost1, 0)                         AS otherCost1,
            IFNULL(c.other_cost2, 0)                         AS otherCost2,
            IFNULL(c.other_cost3, 0)                         AS otherCost3,
            IFNULL(c.other_cost4, 0)                         AS otherCost4,
            IFNULL(c.other_cost5, 0)                         AS otherCost5,
            IFNULL(c.other_cost6, 0)                         AS otherCost6,
            IFNULL(c.other_cost7, 0)                         AS otherCost7,
            IFNULL(c.other_cost8, 0)                         AS otherCost8,
            IFNULL(c.other_cost9, 0)                         AS otherCost9,
            IFNULL(c.other_cost10, 0)                        AS otherCost10,
            IFNULL(c.other_cost11, 0)                        AS otherCost11,
            IFNULL(c.other_cost12, 0)                        AS otherCost12,
            SUM(IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
            IFNULL(c.shortbarge_fee, 0) + IFNULL(c.superframes_fee, 0)
            + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
            IFNULL(c.exception_fee, 0) +
            IFNULL(c.return_fee, 0))                     AS basicsFee,    -- 基础费合计
            SUM(IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) + IFNULL(c.other_cost3, 0) + IFNULL(c.other_cost4, 0) +
            IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) + IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) +
            IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) + IFNULL(c.other_cost11, 0) +
            IFNULL(c.other_cost12, 0))                   AS otherFee,     -- 其他费合计
            SUM(IFNULL(c.freight, 0) + IFNULL(c.delivery_fee, 0) + IFNULL(c.Outboundsorting_fee, 0) +
            IFNULL(c.shortbarge_fee, 0) + IFNULL(c.superframes_fee, 0)
            + IFNULL(c.excess_fee, 0) + IFNULL(c.reduce_fee, 0) + IFNULL(c.Ultrafar_fee, 0) +
            IFNULL(c.exception_fee, 0) + IFNULL(c.return_fee, 0) + IFNULL(c.other_cost1, 0) + IFNULL(c.other_cost2, 0) +
            IFNULL(c.other_cost3, 0) + IFNULL(c.other_cost4, 0) + IFNULL(c.other_cost5, 0) + IFNULL(c.other_cost6, 0) +
            IFNULL(c.other_cost7, 0) + IFNULL(c.other_cost8, 0) + IFNULL(c.other_cost9, 0) + IFNULL(c.other_cost10, 0) +
            IFNULL(c.other_cost11, 0) +
            IFNULL(c.other_cost12, 0))                   AS sumFee,       -- 总费用
            c.oper_by                                        AS operBy,
            c.remarks                                        AS remarks,
            c.warehouse_name                                 AS warehouseNameFh,
            c.warehouse_code                                 AS warehouseCode,
            c.warehouse_name                                 AS warehouseName,
            c.id                                             AS id,
            c.pk_id                                          AS pkId,
            c.signing_date                                   AS businessTime,
            c.expenses_code                                  AS expensesCode,
            c.client_id                                      AS clientId,
            c.expenses_type                                  AS expensesType,
            CASE
                WHEN c.expenses_type = 1 THEN '订单'
                WHEN c.expenses_type = 2 THEN '出库单'
                WHEN c.expenses_type = 3 THEN '入库单'
                WHEN c.expenses_type = 4 THEN '库存单'
            ELSE '' END
            AS expensesTypeName,
            c.expenses_dimension                             AS costDimension,
            CASE
                WHEN c.expenses_dimension = 1 THEN '单'
                WHEN c.expenses_dimension = 2 THEN '趟'
                WHEN c.expenses_dimension = 3 THEN '日'
                WHEN c.expenses_dimension = 4 THEN '月'
                WHEN c.expenses_dimension = 5 THEN '品规'
            ELSE '' END                                  AS costDimensionName,
            c.charge_type                                    AS chargeType,
            CASE
                WHEN c.charge_type = 1 THEN '自动计费'
                WHEN c.charge_type = 2 THEN '手动计费'
            ELSE '' END
            AS chargeTypeName,
            c.quoter_rule_detail_id                          AS quoteruleId,
            c.quoter_rule_detail_name                        AS ruleName,
            null                                             AS totalAmount,
            c.oper_code                                      AS operCode,
            c.del_flag                                       AS delFlag,
            c.bill_id                                        AS billId,
            c.bill_date                                      AS billDate,
            c.is_increment                                   AS isIncrement,
            c.fee_type_first                                 AS feeTypeFirst, -- 计费大类
            c.remarks                                        AS automaticBillingRemark,
            IFNULL(c.total_weight, 0)                        AS weight,       -- 总重量(KG)
            IFNULL(c.total_weight, 0) / 1000                 AS totalWeight,
            IFNULL(c.total_boxes, 0)                         AS totalBoxes,
            IFNULL(c.total_number, 0)                        AS totalNumber,
            IFNULL(c.total_volume, 0)                        AS totalVolume,
            c.main_code_id                                   AS mainExpenseId,
            c.main_pk_id                                     AS mainExpensePkId,
            c.settle_type                                    AS settleType,
            c.settle_amount                                  AS settleAmount
        FROM bms_yscost_info c
        LEFT JOIN bms_clientinfo d ON c.client_id = d.id
        LEFT JOIN bms_ysexpenses_middle bym ON bym.main_pk_id = c.main_pk_id AND bym.del_flag = 0
        WHERE c.del_flag = 0
        <if test="mainExpensePkId!=null ">
            c.main_pk_id = #{mainExpensePkId}
        </if>
        AND c.bill_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND c.expenses_type IN
        <foreach item="type" collection="codeTypes" open="(" separator="," close=")">
            #{type}
        </foreach>
        GROUP BY c.id
        ) a
    </select>

    <select id="selectBmsYsbillmainByFatherid" parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" >
        SELECT
            a.id,
            a.bill_type as billType,
            a.fatherid,
            a.bill_code as billCode,
            a.collection_guid as collectionGuid,
            a.bill_name as billName,
            a.bill_date as billDate,
            a.company_id as companyId,
            a.client_id as clientId,
            a.bill_marking as billMarking,
            a.votes,
            a.bill_amount as billAmount,
            a.adjusted_amount as adjustedAmount,
            a.ys_amount as ysAmount,
            a.bill_state as billState,
            a.audit_state as auditState,
            a.audit_user as auditUser,
            a.audit_user_name as auditUserName,
            a.audit_time as auditTime,
            a.audit_remark as auditRemark,
            a.ticket_state as ticketState,
            a.ticket_amount as ticketAmount,
            a.ticket_num as ticketNum,
            a.ticket_user_name as ticketUserName,
            a.ticket_time as ticketTime,
            a.hx_amount as hxAmount,
            a.hx_state as hxState,
            a.hx_user_name as hxUserName,
            a.hx_time as hxTime,
            a.hx_remark as hxRemark,
            a.bill_remark as billRemark,
            a.ar_clerk as arClerk,
            a.create_code as createCode,
            a.create_by as createBy,
            a.create_time as createTime,
            a.create_dept_id as createDeptId,
            a.oper_dept_id as operDeptId,
            a.oper_code as operCode,
            a.oper_by as operBy,
            a.oper_time as operTime,
            a.del_flag as delFlag,
            a.ticket_apply_state as ticketApplyState,
            a.ticket_apply_time as ticketApplyTime,
            a.ticket_apply_remark as ticketApplyRemark,
            IFNULL( a.merge_status, 0 ) AS mergeStatus,
            a.submit_status as submitStatus,
            a.submit_date as submitDate,
            IFNULL( a.adjusted_amount, 0 )- IFNULL( a.exception_fee, 0 ) AS receivableAmount,
            a.exception_fee as exceptionFee,
            a.ticket_apply_allow_update as ticketApplyAllowUpdate
            ,case when IFNULL(fix.bill_id,'')!='' then 1 else 0 end as isFixedfee
            ,case when IFNULL(ad.bill_id,'')!='' then 1 else 0 end as isAddedfee
        FROM bms_ysbillmain a
        LEFT JOIN (
            SELECT
                bill_id,
                COUNT(*) as num
            FROM bms_fixedfee
            WHERE del_flag = 0
            GROUP BY bill_id
        ) fix ON fix.bill_id = a.id
        LEFT JOIN (
            SELECT
                bill_id,
                COUNT(*) as num
            FROM bms_addedfee
            WHERE del_flag = 0
            GROUP BY bill_id
        ) ad ON ad.bill_id = a.id
        WHERE a.fatherid = #{id} AND a.del_flag = '0'
    </select>

    <insert id="insertBmsYsbillmain" useGeneratedKeys="true" keyProperty="id" keyColumn="id" parameterType="java.util.Map">
        insert into bms_ysbillmain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="billType != null">bill_type,</if>
            <if test="fatherid != null">fatherid,</if>
            <if test="billCode != null">bill_code,</if>
            <if test="collectionGuid != null">collection_guid,</if>
            <if test="billName != null">bill_name,</if>
            <if test="billDate != null">bill_date,</if>
            <if test="companyId != null">company_id,</if>
            <if test="clientId != null">client_id,</if>
            <if test="billMarking != null">bill_marking,</if>
            <if test="votes != null">votes,</if>
            <if test="billAmount != null">bill_amount,</if>
            <if test="adjustedAmount != null">adjusted_amount,</if>
            <if test="ysAmount != null">ys_amount,</if>
            <if test="billState != null">bill_state,</if>
            <if test="auditState != null">audit_state,</if>
            <if test="auditUser != null">audit_user,</if>
            <if test="auditUserName != null">audit_user_name,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="ticketState != null">ticket_state,</if>
            <if test="ticketAmount != null">ticket_amount,</if>
            <if test="ticketNum != null">ticket_num,</if>
            <if test="ticketUserName != null">ticket_user_name,</if>
            <if test="ticketTime != null">ticket_time,</if>
            <if test="hxAmount != null">hx_amount,</if>
            <if test="hxState != null">hx_state,</if>
            <if test="hxUserName != null">hx_user_name,</if>
            <if test="hxTime != null">hx_time,</if>
            <if test="hxRemark != null">hx_remark,</if>
            <if test="billRemark != null">bill_remark,</if>
            <if test="arClerk != null">ar_clerk,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="ticketApplyState != null">ticket_apply_state,</if>
            <if test="ticketApplyTime != null">ticket_apply_time,</if>
            <if test="ticketApplyRemark != null">ticket_apply_remark,</if>
            <if test="submitStatus != null">submit_status,</if>
            <if test="submitDate != null">submit_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="billType != null">#{billType},</if>
            <if test="fatherid != null">#{fatherid},</if>
            <if test="billCode != null">#{billCode},</if>
            <if test="collectionGuid != null">#{collectionGuid},</if>
            <if test="billName != null">#{billName},</if>
            <if test="billDate != null">#{billDate},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="billMarking != null">#{billMarking},</if>
            <if test="votes != null">#{votes},</if>
            <if test="billAmount != null">#{billAmount},</if>
            <if test="adjustedAmount != null">#{adjustedAmount},</if>
            <if test="ysAmount != null">#{ysAmount},</if>
            <if test="billState != null">#{billState},</if>
            <if test="auditState != null">#{auditState},</if>
            <if test="auditUser != null">#{auditUser},</if>
            <if test="auditUserName != null">#{auditUserName},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="ticketState != null">#{ticketState},</if>
            <if test="ticketAmount != null">#{ticketAmount},</if>
            <if test="ticketNum != null">#{ticketNum},</if>
            <if test="ticketUserName != null">#{ticketUserName},</if>
            <if test="ticketTime != null">#{ticketTime},</if>
            <if test="hxAmount != null">#{hxAmount},</if>
            <if test="hxState != null">#{hxState},</if>
            <if test="hxUserName != null">#{hxUserName},</if>
            <if test="hxTime != null">#{hxTime},</if>
            <if test="hxRemark != null">#{hxRemark},</if>
            <if test="billRemark != null">#{billRemark},</if>
            <if test="arClerk != null">#{arClerk},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="ticketApplyState != null">#{ticketApplyState},</if>
            <if test="ticketApplyTime != null">#{ticketApplyTime},</if>
            <if test="ticketApplyRemark != null">#{ticketApplyRemark},</if>
            <if test="submitStatus != null">#{submitStatus},</if>
            <if test="submitDate != null">#{submitDate},</if>
        </trim>
    </insert>

    <insert id="insertBmsYsbillmainList" useGeneratedKeys="true" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        insert into bms_ysbillmain
        (
        bill_type,
        bill_code,
        bill_name,
        bill_date,
        company_id,
        client_id,
        bill_marking,
        votes,
        bill_amount,
        adjusted_amount,
        ys_amount,
        bill_state,
        audit_state,
        submit_status,
        ticket_state,
        hx_state,
        bill_remark,
        ar_clerk,
        create_time,
        oper_time,
        del_flag,
        bill_source,
        merge_status,
        create_code,
        create_by,
        create_dept_id,
        oper_dept_id,
        oper_code,
        oper_by
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.billType},
            #{item.billCode},
            #{item.billName},
            #{item.billDate},
            #{item.companyId},
            #{item.clientId},
            #{item.billMarking},
            #{item.votes},
            #{item.billAmount},
            #{item.adjustedAmount},
            #{item.ysAmount},
            #{item.billState},
            #{item.auditState},
            #{item.submitStatus},
            #{item.ticketState},
            #{item.hxState},
            #{item.billRemark},
            #{item.arClerk},
            #{item.createTime},
            #{item.operTime},
            #{item.delFlag},
            #{item.billSource},
            #{item.mergeStatus},
            #{item.createCode},
            #{item.createBy},
            #{item.createDeptId},
            #{item.operDeptId},
            #{item.operCode},
            #{item.operBy}
            )
        </foreach>

    </insert>

    <update id="updateBmsYsbillmain" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        update bms_ysbillmain
        <trim prefix="SET" suffixOverrides=",">
            <if test="billType != null">bill_type = #{billType},</if>
            <if test="fatherid != null">fatherid = #{fatherid},</if>
            <if test="billCode != null">bill_code = #{billCode},</if>
            <if test="collectionGuid != null">collection_guid = #{collectionGuid},</if>
            <if test="billName != null">bill_name = #{billName},</if>
            <if test="billDate != null">bill_date = #{billDate},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="billMarking != null">bill_marking = #{billMarking},</if>
            <if test="votes != null">votes = #{votes},</if>
            <if test="billAmount != null">bill_amount = #{billAmount},</if>
            <if test="adjustedAmount != null">adjusted_amount = #{adjustedAmount},</if>
            <if test="ysAmount != null">ys_amount = #{ysAmount},</if>
            <if test="billState != null">bill_state = #{billState},</if>
            <if test="auditState != null">audit_state = #{auditState},</if>
            <if test="auditUser != null">audit_user = #{auditUser},</if>
            <if test="auditUserName != null">audit_user_name = #{auditUserName},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="ticketState != null">ticket_state = #{ticketState},</if>
            <if test="ticketAmount != null">ticket_amount = #{ticketAmount},</if>
            <if test="ticketNum != null">ticket_num = #{ticketNum},</if>
            <if test="ticketUserName != null">ticket_user_name = #{ticketUserName},</if>
            <if test="delFlag == 1 ">ticket_user_name = null,</if>
            <if test="ticketTime != null">ticket_time = #{ticketTime},</if>
            <if test="delFlag == 1 ">ticket_time = null,</if>
            <if test="hxAmount != null">hx_amount = #{hxAmount},</if>
            <if test="hxNum != null">hx_num = #{hxNum},</if>
            <if test="hxState != null">hx_state = #{hxState},</if>
            <if test="hxUserName != null">hx_user_name = #{hxUserName},</if>
            <if test="hxTime != null">hx_time = #{hxTime},</if>
            <if test="hxRemark != null">hx_remark = #{hxRemark},</if>
            <if test="billRemark != null">bill_remark = #{billRemark},</if>
            <if test="arClerk != null">ar_clerk = #{arClerk},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="ticketApplyState != null">ticket_apply_state = #{ticketApplyState},</if>
            <if test="ticketApplyTime != null">ticket_apply_time = #{ticketApplyTime},</if>
            <if test="ticketApplyRemark != null">ticket_apply_remark = #{ticketApplyRemark},</if>
            <if test="ticketApplyAmount != null">ticket_apply_amount = #{ticketApplyAmount},</if>
            <if test="submitStatus != null">submit_status = #{submitStatus},</if>
            <if test="submitDate != null">submit_date = #{submitDate},</if>
            <if test="canlenCommitDate != null">submit_date = null,</if>
            <if test="applyAuditState != null">apply_audit_state = #{applyAuditState},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateBmsYsbillmainStatus" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        update bms_ysbillmain
        set bill_state = #{billState}
        where id = #{id}
    </update>


    <update id="updateBmsYsbillmainStatusByIds">
        update bms_ysbillmain set del_flag = #{delflag} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateBmsTicketYsApply" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillMainApplyDto" >
        update bms_ysbillmain
        <trim prefix="SET" suffixOverrides=",">
            <if test="ticketApplyState != null">ticket_apply_state = #{ticketApplyState},</if>
            <if test="ticketApplyRemark != null">ticket_apply_remark = #{ticketApplyRemark},</if>
            <if test="ticketState != null">ticket_state = #{ticketState},</if>
            <if test="ticketApplyAmount != null">ticket_apply_amount = #{ticketApplyAmount},</if>
            <if test="ppstorageFee != null">ppstorage_fee = #{ppstorageFee},</if>
            <if test="zpstorageFee != null">zpstorage_fee = #{zpstorageFee},</if>
            <if test="ppfreightFee != null">ppfreight_fee = #{ppfreightFee},</if>
            <if test="zpfreightFee != null">zpfreight_fee = #{zpfreightFee},</if>
            <if test="ppfreightFee != null">pp_message_fee = #{ppMessageFee},</if>
            <if test="zpfreightFee != null">zp_message_fee = #{zpMessageFee},</if>
            <if test="ppfreightFee != null">pp_platform_fee = #{ppPlatformFee},</if>
            <if test="zpfreightFee != null">zp_platform_fee = #{zpPlatformFee},</if>
            <if test="ppfreightFee != null">pp_brand_fee = #{ppBrandFee},</if>
            <if test="zpfreightFee != null">zp_brand_fee = #{zpBrandFee},</if>
            <if test="ppfreightFee != null">pp_delivery_fee = #{ppDeliveryFee},</if>
            <if test="zpfreightFee != null">zp_delivery_fee = #{zpDeliveryFee},</if>
            <if test="zpAirtransportFee != null">zp_airtransport_fee = #{zpAirtransportFee},</if>
            <if test="ppAirtransportFee != null">pp_airtransport_fee = #{ppAirtransportFee},</if>
            <if test="ppConsumablesFee != null">pp_consumables_fee = #{ppConsumablesFee},</if>
            <if test="zpConsumablesFee != null">zp_consumables_fee = #{zpConsumablesFee},</if>
            <if test="ppUnloadFee != null">pp_unload_fee = #{ppUnloadFee},</if>
            <if test="zpUnloadFee != null">zp_unload_fee = #{zpUnloadFee},</if>
            <if test="ppGpsFee != null">pp_gps_fee = #{ppGpsFee},</if>
            <if test="zpGpsFee != null">zp_gps_fee = #{zpGpsFee},</if>
            <if test="ppScmFee != null">pp_scm_fee = #{ppScmFee},</if>
            <if test="zpScmFee != null">zp_scm_fee = #{zpScmFee},</if>
            <if test="openingName != null">opening_name = #{openingName},</if>
            <if test="openingBank != null">opening_bank = #{openingBank},</if>
            <if test="cardNumber != null">card_number = #{cardNumber},</if>
            <if test="linkPhone != null">link_phone = #{linkPhone},</if>
            <if test="tacAddress != null">tac_address = #{tacAddress},</if>
            <if test="taxpayerNum != null">taxpayer_num = #{taxpayerNum},</if>
            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>
            <if test="billState != null">bill_state = #{billState},</if>
            <if test="iscontainsExceFee != null">iscontains_exce_fee = #{iscontainsExceFee},</if>
            <if test="ticketApplyAmount != null">ticket_apply_amount = #{ticketApplyAmount},</if>
            <if test="transExceptionFee !=null">trans_exception_fee = #{transExceptionFee},</if>
            <if test="wareExceptionFee !=null">ware_exception_fee = #{wareExceptionFee},</if>
            ticket_apply_time = sysdate()
            where id = #{id}
        </trim>
    </update>

    <update id="updateBmsYsbillmainTicketInfo" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE bms_ysbillmain
            <set>
                <if test="item.ticketState != null">
                    ticket_state = #{item.ticketState},
                </if>
                <if test="item.ticketAmount != null">
                    ticket_amount = #{item.ticketAmount},
                </if>
                <if test="item.ticketNum != null">
                    ticket_num = #{item.ticketNum},
                </if>
                <if test="item.ticketUserName != null">
                    ticket_user_name = #{item.ticketUserName},
                </if>
                <if test="item.billState != null">
                    bill_state = #{item.billState},
                </if>
                ticket_time = sysdate()
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>


    <select id="selectBmsYsbillmainAndClientInfo" parameterType="java.util.List" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainAndClientInfo" >
        select
        ys.id id,
        ys.bill_type billType,
        ys.bill_code billCode,
        ys.company_id companyId,
        ys.client_id clientId,
        ys.bill_amount billAmount,
        ys.ticket_state ticketState,
        ys.ticket_amount ticketAmount,
        ys.ticket_num ticketNum,
        ys.ticket_user_name ticketUserName,
        ys.ticket_time ticketTime,
        ys.oper_dept_id operDeptId,
        ys.oper_code operCode,
        ys.oper_by operBy,
        ys.oper_time operTime,
        ys.del_flag delFlag,
        ys.invoice_type invoiceType,
        ys.opening_bank openingBank,
        ys.card_number cardNumber,
        ys.opening_name openingName,
        ys.link_phone linkPhone,
        ys.taxpayer_num taxpayerNum,
        ys.tac_address tacAddress,
        cli.tax_rate taxRate,
        cli.storage_tax_rate as storageTaxRate,
        cli.others_tax_rate as othersTaxRate,
        cli.airtransport_rate as airtransportRate,
        cli.consumables_rate as consumablesRate,
        cli.unload_rate as unloadRate,
        cli.gps_rate as gpsRate,
        cli.scm_rate as scmRate,
        cli.freight_ratio freightRatio,
        cli.storage_ratio storageRatio,
        cli.message_ratio messageRatio,
        cli.platform_ratio platformRatio,
        cli.brand_ratio brandRatio,
        cli.airtransport_radio airtransportRadio,
        cli.consumables_radio consumablesRadio,
        cli.unload_radio unloadRadio,
        cli.gps_radio gpsRadio,
        cli.scm_radio scmRadio,
        cli.invoice_mode invoiceMode,
        cli.cooperate_type cooperateType,
        ys.ticket_apply_state ticketApplyState,
        ys.ticket_apply_amount ticketApplyAmount,
        IFNULL(ys.ppstorage_fee,0) as ppstorageFee,
        IFNULL(ys.zpstorage_fee,0) as zpstorageFee,
        IFNULL(ys.ppfreight_fee,0) as ppfreightFee,
        IFNULL(ys.zpfreight_fee,0) as zpfreightFee,
        IFNULL(ys.zp_message_fee,0) as zpMessageFee,
        IFNULL(ys.pp_message_fee,0) as ppMessageFee,
        IFNULL(ys.pp_platform_fee,0) as ppPlatformFee,
        IFNULL(ys.zp_platform_fee,0) as zpPlatformFee,
        IFNULL(ys.pp_brand_fee,0) as ppBrandFee,
        IFNULL(ys.zp_brand_fee,0) as zpBrandFee,
        IFNULL(ys.pp_delivery_fee,0) as ppDeliveryFee,
        IFNULL(ys.zp_delivery_fee,0) as zpDeliveryFee,
        IFNULL(ys.adjusted_amount,0)-IFNULL(ys.exception_fee,0) as receivableAmount
        from bms_ysbillmain ys
        left join bms_clientinfo cli on ys.client_id = cli.id and cli.del_flag ='0'
        where ys.del_flag = '0' and  bill_code in
        <foreach item="billCode" collection="list" open="(" separator="," close=")">
            #{billCode}
        </foreach>
    </select>

    <select id="approveOrNot" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean" resultMap="BmsYsbillmainResult">
        <include refid="selectBmsYsbillmainVo"/>
        <where>
            id = #{billMainid}
            and IFNULL(bill_state,0) > 1
        </where>
    </select>

    <select id="getBillmainStatus" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" resultMap="BmsYsbillmainResult">
        <include refid="selectBmsYsbillmainVo"/>
        <where>
            id = #{id}
            <if test="submitStatus!=null and submitStatus!='' ">
                and submit_status = #{submitStatus}
            </if>
            <if test="ticketApplyState!=null and ticketApplyState!='' ">
                and ticket_apply_state = #{ticketApplyState}
            </if>
            <if test="ticketState!=null and ticketState!='' ">
                and ticket_state = #{ticketState}
            </if>
            <if test="auditState!=null and auditState!='' ">
                and audit_state = #{auditState}
            </if>
            <if test="hxState!=null and hxState!='' ">
                and hx_state = #{hxState}
            </if>
        </where>
    </select>


    <update id="updateTicketApplyState" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE bms_ysbillmain
            <set>
                <if test="item.ticketState != null">
                    ticket_state = #{item.ticketState},
                </if>
                <if test="item.applyAuditState != null">
                    apply_audit_state = #{item.applyAuditState},
                </if>
                <if test="item.billState != null">
                    bill_state = #{item.billState},
                </if>
                ticket_apply_state = '1',
                ticket_apply_remark = '',
                ticket_apply_amount = NULL,
                ticket_apply_time = NULL,
                ppstorage_fee=0,
                zpstorage_fee=0,
                ppfreight_fee=0,
                zpfreight_fee=0,
                ware_exception_fee = NULL,
                trans_exception_fee = NULL
            </set>
            WHERE id = #{item.id}
            ;
            update bms_ysinvoice_apply set del_flag =1 where billid = #{item.id}
        </foreach>
    </update>

    <update id="updateBillMainAmtBySon" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean">
        update bms_ysbillmain a
        inner join (
        select
        c.bill_id as id,
        sum(
        IFNULL(c.freight,0) +
        IFNULL(c.Ultrafar_fee,0) +
        IFNULL(c.superframes_fee,0) +
        IFNULL(c.excess_fee,0) +
        IFNULL(c.reduce_fee,0) +
        IFNULL(c.delivery_fee,0) +
        IFNULL(c.outboundsorting_fee,0) +
        IFNULL(c.shortbarge_fee,0) +
        IFNULL(c.return_fee,0) +
        IFNULL(c.exception_fee,0) +
        IFNULL(c.adjust_fee,0) +
        IFNULL(c.other_cost1,0) +
        IFNULL(c.other_cost2,0) +
        IFNULL(c.other_cost3,0) +
        IFNULL(c.other_cost4,0) +
        IFNULL(c.other_cost5,0) +
        IFNULL(c.other_cost6,0) +
        IFNULL(c.other_cost7,0) +
        IFNULL(c.other_cost8,0) +
        IFNULL(c.other_cost9,0) +
        IFNULL(c.other_cost10,0) +
        IFNULL(c.other_cost11,0) +
        IFNULL(c.other_cost12,0)
        ) as sumAmt,
        SUM(IFNULL(c.exception_fee,0)) as sumExceptionFee,
        count(1) as count
        from bms_yscost_info c
        where
        c.bill_id  in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by c.bill_id
        ) b on a.id = b.id
        set
        a.adjusted_amount = b.sumAmt
        ,a.ys_amount = b.sumAmt
        ,a.bill_amount = b.sumAmt
        ,a.votes = b.count
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_time = #{operTime}
        ,a.exception_fee = b.sumExceptionFee
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_ysbillmain a inner join (
        select
        ysbill_id as id,
        sum(IFNULL(total_amount,0)) as sumAmt,
        count(1) as count
        from bms_yspurchase_feeinfo
        where ysbill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by ysbill_id
        ) b on a.id = b.id
        set
        a.adjusted_amount = a.adjusted_amount + b.sumAmt
        ,a.bill_amount = a.adjusted_amount + b.sumAmt
        ,a.ys_amount = a.ys_amount+b.sumAmt
        ,a.votes = a.votes + b.count
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_time = #{operTime}
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_ysbillmain a
        inner join (
        select
        bill_id as id,
        sum(IFNULL(amount,0)) as sumAmt,
        count(1) as count
        from bms_fixedfee
        where del_flag = '0'
        and bill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY bill_id
        ) b on a.id = b.id
        set
        a.adjusted_amount = a.adjusted_amount + b.sumAmt
        ,a.bill_amount = a.adjusted_amount + b.sumAmt
        ,a.ys_amount = a.ys_amount+b.sumAmt
        ,a.votes = a.votes + b.count
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_time = #{operTime}
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_ysbillmain a
        inner join (
        select
        bill_id as id,
        sum(IFNULL(amount,0)) as sumAmt,
        count(1) as count
        from bms_addedfee
        where del_flag = '0'
        and settle_type=1
        and bill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY bill_id
        ) b on a.id = b.id
        set
        a.adjusted_amount = a.adjusted_amount + b.sumAmt
        ,a.bill_amount = a.adjusted_amount + b.sumAmt
        ,a.ys_amount = a.ys_amount+b.sumAmt
        ,a.votes = a.votes + b.count
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_time = #{operTime}
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        <!-- 理赔费 -->
        update bms_ysbillmain a
        inner join (
        select
        bill_id as id,
        sum(IFNULL(responsible_money,0)) as sumAmt,
        count(1) as count
        from bms_claims_info
        where del_flag = '0'
        and bill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY bill_id
        ) b on a.id = b.id
        set
        a.adjusted_amount = a.adjusted_amount + b.sumAmt
        ,a.bill_amount = a.adjusted_amount + b.sumAmt
        ,a.ys_amount = a.ys_amount+b.sumAmt
        ,a.exception_fee = a.exception_fee+b.sumAmt
        ,a.votes = a.votes + b.count
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_time = #{operTime}
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_ysbillmain a inner join (
        select
        fatherid as id ,
        sum(ys_amount) as ysAmount,
        sum( adjusted_amount ) AS sumAmt,
        sum( votes ) AS count,
        sum( exception_fee ) AS sumExceptionFee
        from bms_ysbillmain
        where fatherid = #{billId}
        group by fatherid
        ) b on a.id = b.id
        set
        a.adjusted_amount = b.sumAmt,
        a.bill_amount = b.sumAmt,
        a.ys_amount = b.ysAmount,
        a.votes = b.count
        ,a.bill_state = #{commitType}
        ,a.submit_status = #{commitType}
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_time = #{operTime}
        ,a.exception_fee = b.sumExceptionFee
        where a.id = #{billId}
    </update>


    <update id="updateBillMainAmt" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean">
        <if test="type==1">
            update bms_ysbillmain a
            inner join (
            select
            c.bill_id as id,
            sum(
            IFNULL(c.freight,0) +
            IFNULL(c.Ultrafar_fee,0) +
            IFNULL(c.superframes_fee,0) +
            IFNULL(c.excess_fee,0) +
            IFNULL(c.reduce_fee,0) +
            IFNULL(c.delivery_fee,0) +
            IFNULL(c.outboundsorting_fee,0) +
            IFNULL(c.shortbarge_fee,0) +
            IFNULL(c.return_fee,0) +
            IFNULL(c.exception_fee,0) +
            IFNULL(c.other_cost1,0) +
            IFNULL(c.other_cost2,0) +
            IFNULL(c.other_cost3,0) +
            IFNULL(c.other_cost4,0) +
            IFNULL(c.other_cost5,0) +
            IFNULL(c.other_cost6,0) +
            IFNULL(c.other_cost7,0) +
            IFNULL(c.other_cost8,0) +
            IFNULL(c.other_cost9,0) +
            IFNULL(c.other_cost10,0) +
            IFNULL(c.other_cost11,0) +
            IFNULL(c.other_cost12,0)
            ) as sumAmt,
            count(1) as count,
            SUM(IFNULL(c.exception_fee,0)) as sumExceptionFee
            from bms_yscost_info c
            where
            c.bill_id = #{billId,jdbcType=BIGINT}
            group by c.bill_id
            ) b on a.id = b.id
            set
            a.adjusted_amount = b.sumAmt
#           ,a.bill_amount = b.sumAmt
            ,a.ys_amount = b.sumAmt
            ,a.bill_state = #{commitType}
            ,a.submit_status = #{commitType}
            ,a.votes = b.count
            ,a.oper_dept_id = #{operDeptId}
            ,a.oper_code = #{operCode}
            ,a.oper_by = #{operBy}
            ,a.oper_time = #{operTime}
            ,a.exception_fee = b.sumExceptionFee
            <if test="commitType!=null and commitType != '' ">
                ,a.bill_state = #{commitType}
                ,a.submit_status = #{commitType}
            </if>
            <if test="commitType == 2">
                ,a.submit_date =now()
            </if>
            where
            a.id = #{billId,jdbcType=BIGINT}

            ;

            update bms_ysbillmain a
            inner join (
            SELECT
            ad.bill_id AS id,
            sum( IFNULL(amount,0) ) AS sumAmt,
            count( 1 ) AS count,
            DATE_FORMAT(creat_date,'%Y-%m') as billDate
            FROM bms_addedfee ad
            WHERE ad.del_flag = 0
            and ad.settle_type=1
            AND ad.bill_id = #{billId,jdbcType=BIGINT}
            GROUP BY ad.bill_id,DATE_FORMAT(creat_date,'%Y-%m')
            ) b on a.id = b.id and b.billDate = a.bill_date
            set
            a.adjusted_amount = a.adjusted_amount+b.sumAmt
#             ,a.bill_amount = a.adjusted_amount+b.sumAmt
            ,a.ys_amount = a.ys_amount+b.sumAmt
            ,a.votes = a.votes+b.count
            where
            a.id = #{billId,jdbcType=BIGINT};

            update bms_ysbillmain a
            inner join (
            select
            ad.bill_id as id,
            sum(ad.amount) as sumAmt,
            count(1) as count,
            DATE_FORMAT(ad.start_date,'%Y-%m') as billDate
            from bms_fixedfee ad
            where ad.del_flag =0
            and ad.bill_id = #{billId,jdbcType=BIGINT}
            group by ad.bill_id,DATE_FORMAT(ad.start_date,'%Y-%m')
            ) b on a.id = b.id and b.billDate = a.bill_date
            set
            a.adjusted_amount = a.adjusted_amount+b.sumAmt
#             ,a.bill_amount = a.adjusted_amount+b.sumAmt
            ,a.ys_amount = a.ys_amount+b.sumAmt
            ,a.votes = a.votes+b.count
            where a.id = #{billId,jdbcType=BIGINT}
            ;


            <!-- 理赔费 -->
            update bms_ysbillmain a
            inner join (
            select
            cla.bill_id as id,
            sum(cla.responsible_money) as sumAmt,
            count(1) as count
            from bms_claims_info cla
            where cla.del_flag =0
            and cla.bill_id =#{billId}
            group by cla.bill_id
            ) b on a.id = b.id
            set
            a.adjusted_amount = a.adjusted_amount+b.sumAmt
#             ,a.bill_amount = a.adjusted_amount+b.sumAmt
            ,a.ys_amount = a.ys_amount+b.sumAmt
            ,a.votes = a.votes+b.count
            ,a.exception_fee = a.exception_fee+b.sumAmt
            where a.id =#{billId}
            ;


        </if>
        <if test="type==3">
            update bms_ysbillmain a inner join (
            select
            ysbill_id as id,
            sum(IFNULL(total_amount,0)) as sumAmt,
            count(1) as count
            from bms_yspurchase_feeinfo
            where ysbill_id = #{id}

            group by ysbill_id
            ) b on a.id = b.id
            set
            a.adjusted_amount = b.sumAmt
#             ,a.bill_amount = b.sumAmt
            ,a.votes = b.count
            ,a.bill_state = #{commitType}
            ,a.submit_status = #{commitType}
            ,a.oper_dept_id = #{operDeptId}
            ,a.oper_code = #{operCode}
            ,a.oper_by = #{operBy}
            ,a.oper_time = #{operTime}
            <if test="commitType!=null and commitType != '' ">
                ,a.bill_state = #{commitType}
                ,a.submit_status = #{commitType}
            </if>
            <if test="commitType == 2">
                ,a.submit_date =now()
            </if>

            where a.id = #{id}
        </if>

    </update>

    <update id="cancalFatherbillmain" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        update  bms_ysbillmain ys set del_flag=1 where ys.id = #{id}
        ;
        update bms_ysbillmain set fatherid = null
        where
            fatherid = #{id}

    </update>

    <update id="cancalFatherbillmainBatch" >
        update  bms_ysbillmain ys set del_flag=1 where ys.id  in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach> ;
        update bms_ysbillmain set fatherid = null
        where fatherid in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>

    <select id="selectHxInfoBybillCode" parameterType="java.util.Arrays" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        SELECT
        ys.id,
        ys.bill_code as billCode,
        ys.hx_state as hxState,
        ys.adjusted_amount as adjustedAmount,
        ys.hx_amount as hxAmount,
        ys.hx_num as hxNum,
        ysh.payment as nowAmount
        FROM
        bms_ysbillmain ys
        left join
        (
        SELECT
        bill_codes,
        sum(payment) as payment
        from
        bms_ysbill_hxinfo ysh
        WHERE
        ysh.del_flag = 0
        and ysh.bill_codes in
        <foreach collection="codes" item="code" separator="," open="(" close=")">
            #{code}
        </foreach>
        GROUP BY bill_codes
        ) ysh
        on ys.bill_code = ysh.bill_codes
        where
        1=1
        and IFNULL(ys.del_flag,0)=0
        and IFNULL(ysh.payment,0) != 0
        and ys.bill_code in
        <foreach collection="codes" item="code" separator="," open="(" close=")">
            #{code}
        </foreach>
    </select>

    <select id="selectBmsPubFixedfeeRuleByClinet" resultType="com.bbyb.joy.bms.domain.dto.BmsPubFixedfeeRule">
        SELECT
            id
             ,expenses_code as expensesCode
             ,IFNULL(amount,0) as amount
             ,remark as remarks

        from  bms_fixedfee
        where
            del_flag = 0
          and settle_type = 1
          and client_id = #{clientId}
          and IFNULL(bill_id,'')=''
          and DATE_FORMAT(start_date,'%Y-%m') = #{billDate}
    </select>
    <select id="selectBmsPubAddedfeefeeRuleByClinet" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto">
        SELECT
        id
        ,expenses_code as expensesCode
        ,IFNULL(amount,0) as amount
        ,remark as remarks

        from  bms_addedfee
        where
        del_flag = 0
        and cost_status = 1
        and settle_type = 1
        and client_code = #{clinetCode}
        and IFNULL(bill_id,'')=''
        and dept_id = #{companyId}
        and DATE_FORMAT(creat_date,'%Y-%m') = #{billDate}
        <!--运输增值 -->
        <if test="type !=null and type ==2">
            and fee_belong=1
        </if>
        <!--仓储增值 -->
        <if test="type !=null and type ==3">
            and fee_belong=2
        </if>
    </select>



    <select id="selectBmsYsbillmainList2" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillmainDto">
        select
        a.id as id,
        a.order_level as orderLevel,
        a.bill_type as billType,
        a.fatherid as fatherid,
        a.bill_code as billCode,
        a.collection_guid as collectionGuid,
        a.bill_name as billName,
        a.bill_date as billDate,
        a.company_id as companyId,
        a.client_id as clientId,
        a.bill_marking as billMarking,
        a.votes as votes,
        a.bill_amount as billAmount,
        a.adjusted_amount as adjustedAmount,
        a.ys_amount as ysAmount,
        a.bill_state as billState,
        a.audit_state as auditState,
        a.audit_user as auditUser,
        a.audit_user_name as auditUserName,
        a.audit_time as auditTime,
        a.audit_remark as auditRemark,
        a.ticket_state as ticketState,
        IFNULL(a.ticket_amount,0) AS ticketAmount,
        a.ticket_num as ticketNum,
        a.ticket_user_name as ticketUserName,
        a.ticket_time as ticketTime,
        a.hx_amount as hxAmount,
        a.hx_state as hxState,
        a.hx_user_name as hxUserName,
        a.hx_time as hxTime,
        a.hx_remark as hxRemark,
        a.bill_remark as billRemark,
        a.ar_clerk as arClerk,
        a.create_code as createCode,
        a.create_by as createBy,
        a.create_time as createTime,
        a.create_dept_id as createDeptId,
        a.oper_dept_id as operDeptId,
        a.oper_code as operCode,
        a.oper_by as operBy,
        a.oper_time as operTime,
        a.del_flag as delFlag,
        a.ticket_apply_state as ticketApplyState,
        a.ticket_apply_time as ticketApplyTime,
        a.ticket_apply_remark as ticketApplyRemark,
        a.submit_status as submitStatus,
        a.submit_date as submitDate,
        b.client_code AS clientCode,
        b.client_name AS clientName,
        b.invoice_type invoiceType,
        b.opening_name openingName,
        b.taxpayer_num taxpayerNum,
        b.opening_bank openingBank,
        b.card_number cardNumber,
        b.link_phone linkPhone,
        b.invoice_mode as invoiceMode,
        b.tac_address as tacAddress,
        IFNULL(a.ticket_apply_amount,0 ) ticketApplyAmount,
        IFNULL(a.bill_source,1) as billSource,
        IFNULL( a.merge_status, 0 ) AS mergeStatus,
        a.end_date as endDate
        ,case when IFNULL(fix.id,'')!='' then 1 else 0 end as isFixedfee
        ,case when IFNULL(ad.id,'')!='' then 1 else 0 end as isAddedfee
        ,a.exception_fee as exceptionFee
        ,IFNULL(a.adjusted_amount,0)-IFNULL(a.exception_fee,0) as receivableAmount
        ,a.apply_audit_state applyAuditState
        ,IFNULL(sdd.dict_label,'待生成签署文件')as signingStatusDesc
        ,IFNULL(byec.signing_status,0 ) as signingStatus
        ,byec.signing_fail_reason as signingFailReason
        ,byec. contract_generation_date as contractGenerationDate
        ,byec.signing_date as signingDate
        from bms_ysbillmain a
        left join bms_clientinfo b on a.client_id = b.id
        left join bms_fixedfee fix on fix.bill_id = a.id and fix.del_flag =0
        left join bms_addedfee ad on ad.bill_id = a.id and ad.del_flag =0
        left join pub_ys_electronic_contract byec on byec.order_id=a.id and byec.deleted=0
        left join sys_dict_data sdd on sdd.dict_value=byec.signing_status and dict_type='bms_contract_sign'
        <where>
            1=1
            and (IFNULL(a.fatherid,0)=0 or IFNULL(a.fatherid,0) = a.id)
            and IFNULL(a.del_flag,0)=0
            <if test="clientList!=null and clientList.size>0">
                and  b.client_code in
                <foreach item="client" collection="clientList" open="(" separator="," close=")">
                    #{client}
                </foreach>
            </if>
            <if test="billSource != null "> and IFNULL(a.bill_source,1) = #{billSource}</if>
            <if test="billType != null "> and a.bill_type = #{billType}</if>
            <if test="fatherid != null "> and a.fatherid = #{fatherid}</if>
            <if test="billCode != null  and billCode != ''"> and a.bill_code = #{billCode}</if>
            <if test="collectionGuid != null  and collectionGuid != ''"> and a.collection_guid = #{collectionGuid}</if>
            <if test="billName != null  and billName != ''"> and a.bill_name like concat('%', #{billName}, '%')</if>
            <if test="billDate != null  and billDate != ''"> and a.bill_date = #{billDate}</if>
            <if test="clientName != null  and clientName != ''"> and b.client_name like concat('%', #{clientName}, '%')</if>
            <if test="companyIdList != null and companyIdList.size>0 ">
                and a.company_id in
                <foreach item="compId" collection="companyIdList" open="(" separator="," close=")">
                    #{compId}
                </foreach>
            </if>
            <if test="clientId != null "> and a.client_id = #{clientId}</if>
            <if test="billMarking != null "> and a.bill_marking = #{billMarking}</if>
            <if test="votes != null "> and a.votes = #{votes}</if>
            <if test="billAmount != null "> and a.bill_amount = #{billAmount}</if>
            <if test="adjustedAmount != null "> and a.adjusted_amount = #{adjustedAmount}</if>
            <if test="ysAmount != null "> and a.ys_amount = #{ysAmount}</if>
            <if test="billState != null "> and a.bill_state = #{billState}</if>
            <if test="auditState != null "> and a.audit_state = #{auditState}</if>
            <if test="auditUser != null "> and a.audit_user = #{auditUser}</if>
            <if test="auditUserName != null  and auditUserName != ''"> and a.audit_user_name like concat('%', #{auditUserName}, '%')</if>
            <if test="auditTime != null "> and a.audit_time = #{auditTime}</if>
            <if test="auditRemark != null  and auditRemark != ''"> and a.audit_remark like concat('%', #{auditRemark}, '%')</if>
            <if test="ticketState != null "> and a.ticket_state = #{ticketState}</if>
            <if test="ticketAmount != null "> and a.ticket_amount = #{ticketAmount}</if>
            <if test="ticketNum != null "> and a.ticket_num = #{ticketNum}</if>
            <if test="ticketUserName != null  and ticketUserName != ''"> and a.ticket_user_name like concat('%', #{ticketUserName}, '%')</if>
            <if test="ticketTime != null "> and a.ticket_time = #{ticketTime}</if>
            <if test="hxAmount != null "> and a.hx_amount = #{hxAmount}</if>
            <if test="hxState != null "> and a.hx_state = #{hxState}</if>
            <if test="hxUserName != null  and hxUserName != ''"> and a.hx_user_name like concat('%', #{hxUserName}, '%')</if>
            <if test="hxTime != null "> and a.hx_time = #{hxTime}</if>
            <if test="hxRemark != null  and hxRemark != ''"> and a.hx_remark like concat('%', #{hxRemark}, '%')</if>
            <if test="billRemark != null  and billRemark != ''"> and a.bill_remark like concat('%', #{billRemark}, '%')</if>
            <if test="arClerk != null  and arClerk != ''"> and a.ar_clerk like concat('%', #{arClerk}, '%')</if>
            <if test="createCode != null  and createCode != ''"> and a.create_code = #{createCode}</if>
            <if test="createBy != null  and createBy != ''"> and a.create_by like concat('%', #{createBy}, '%')</if>
            <if test="beginCreateTime != null "> and a.create_time &gt;= #{beginCreateTime}</if>
            <if test="endCreateTime != null "> and a.create_time &lt;= #{endCreateTime}</if>
            <if test="beginEndDate != null "> and a.end_date &gt;= #{beginEndDate}</if>
            <if test="endEndDate != null "> and a.end_date &lt;= #{endEndDate}</if>
            <if test="createDeptId != null "> and a.create_dept_id = #{createDeptId}</if>
            <if test="operDeptId != null "> and a.oper_dept_id = #{operDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and a.oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and a.oper_by like concat('%', #{operBy}, '%')</if>
            <if test="operTime != null "> and a.oper_time = #{operTime}</if>
            <if test="ticketApplyState != null "> and a.ticket_apply_state = #{ticketApplyState}</if>
            <if test="ticketApplyTime != null "> and a.ticket_apply_time = #{ticketApplyTime}</if>
            <if test="ticketApplyRemark != null  and ticketApplyRemark != ''"> and a.ticket_apply_remark = #{ticketApplyRemark}</if>
            <if test="submitStatus != null  and submitStatus != ''"> and a.submit_status = #{submitStatus}</if>
            <if test="submitDate != null "> and a.submit_date = #{submitDate}</if>
            <if test="applyAuditState!=null">and a.apply_audit_state=#{applyAuditState}</if>
            <if test="signingStatus !=null and signingStatus!=0 ">
                and byec.signing_status=#{signingStatus}
            </if>
            <if test="signingStatus !=null and signingStatus ==0 ">
                and(byec.signing_status=#{signingStatus} or byec.signing_status IS NULL )
            </if>
        </where>
        GROUP BY a.bill_code
        order by a.create_time desc
    </select>
    <select id="selectBmsYsbillmainById2" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillmainDto">
        SELECT
            a.id,
            a.bill_type as billType,
            a.fatherid,
            a.bill_code as billCode,
            a.collection_guid as collectionGuid,
            IFNULL(a.bill_source,1) as billSource,
            a.bill_name as billName,
            a.bill_date as billDate,
            a.company_id as companyId,
            a.client_id as clientId,
            a.bill_marking as billMarking,
            a.votes,
            a.bill_amount as billAmount,
            a.adjusted_amount as adjustedAmount,
            a.ys_amount as ysAmount,
            a.bill_state as billState,
            a.audit_state as auditState,
            a.audit_user as auditUser,
            a.audit_user_name as auditUserName,
            a.audit_time as auditTime,
            a.audit_remark as auditRemark,
            a.ticket_state as ticketState,
            a.ticket_amount as ticketAmount,
            a.ticket_num as ticketNum,
            a.ticket_user_name as ticketUserName,
            a.ticket_time as ticketTime,
            a.hx_amount as hxAmount,
            a.hx_state as hxState,
            a.hx_user_name as hxUserName,
            a.hx_time as hxTime,
            a.hx_remark as hxRemark,
            a.bill_remark as billRemark,
            a.ar_clerk as arClerk,
            a.create_code as createCode,
            a.create_by as createBy,
            a.create_time as createTime,
            a.create_dept_id as createDeptId,
            a.oper_dept_id as operDeptId,
            a.oper_code as operCode,
            a.oper_by as operBy,
            a.oper_time as operTime,
            a.del_flag as delFlag,
            a.ticket_apply_state as ticketApplyState,
            a.ticket_apply_time as ticketApplyTime,
            a.ticket_apply_remark as ticketApplyRemark,
            a.ticket_apply_amount as ticketApplyAmount,
            IFNULL(a.merge_status,0) as mergeStatus,
            a.submit_status as submitStatus,
            a.submit_date as submitDate,
            b.client_code as clientCode,
            b.client_name as  clientName,
            b.invoice_type invoiceType,
            b.opening_name openingName,
            b.taxpayer_num taxpayerNum,
            b.opening_bank openingBank,
            b.card_number cardNumber,
            b.link_phone linkPhone,
            b.tac_address tacAddress,
            a.ticket_apply_remark as ticketApplyRemark,
            a.ticket_apply_allow_update,
            case
                when IFNULL(fix.id,'')!='' then 1 else 0 end AS isFixedfee ,
            case
                when IFNULL(ad.id,'')!='' then 1 else 0 end AS isAddedfee ,
            IFNULL(a.exception_fee,0) AS exceptionFee ,
            IFNULL(a.adjusted_amount,0)-IFNULL(a.exception_fee,0) AS receivableAmount ,
            a.iscontains_exce_fee AS iscontainsExceFee ,
            case a.iscontains_exce_fee
                when '0' then '包含'
                when '1' then '不包含'
                else ''
            end AS iscontainsExceFeeName ,
            DATE_FORMAT(c.invoice_time,'%Y-%m-%d %H:%i:%s') AS invoiceTime ,
            a.ticket_amount AS invoicefee ,
            a.un_claimes_amount unClaimesAmount
        FROM bms_ysbillmain a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_fixedfee fix ON fix.bill_id = a.id AND fix.del_flag =0
        LEFT JOIN bms_addedfee ad ON ad.bill_id = a.id AND ad.del_flag =0
        LEFT JOIN (
            SELECT
                billid,
                bill_codes,
                invoice_time,
                invoicefee
            FROM bms_ysbillinvoice
            WHERE del_flag = '0'
                AND billid = #{id}
                AND invoice_state !='1'
            ORDER BY invoice_time ASC
            LIMIT 1
        ) c ON c.billid = a.id
        WHERE a.id = #{id} and a.del_flag = '0'
        GROUP BY a.bill_code
        ORDER BY a.create_time DESC
    </select>

    <select id="selectBmsYsbillmainByFathersid" resultMap="BmsYsbillmainResult">
        <include refid="selectBmsYsbillmainVo"/>
        WHERE del_flag = '0'
        AND fatherid IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectBmsYsbillmainByFatherids" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        SELECT
        a.id,
        a.bill_type as billType,
        a.fatherid,
        a.bill_code as billCode,
        a.collection_guid as collectionGuid,
        a.bill_name as billName,
        a.bill_date as billDate,
        a.company_id as companyId,
        a.client_id as clientId,
        a.bill_marking as billMarking,
        a.votes,
        a.bill_amount as billAmount,
        a.adjusted_amount as adjustedAmount,
        a.ys_amount as ysAmount,
        a.bill_state as billState,
        a.audit_state as auditState,
        a.audit_user as auditUser,
        a.audit_user_name as auditUserName,
        a.audit_time as auditTime,
        a.audit_remark as auditRemark,
        a.ticket_state as ticketState,
        a.ticket_amount as ticketAmount,
        a.ticket_num as ticketNum,
        a.ticket_user_name as ticketUserName,
        a.ticket_time as ticketTime,
        a.hx_amount as hxAmount,
        a.hx_state as hxState,
        a.hx_user_name as hxUserName,
        a.hx_time as hxTime,
        a.hx_remark as hxRemark,
        a.bill_remark as billRemark,
        a.ar_clerk as arClerk,
        a.create_code as createCode,
        a.create_by as createBy,
        a.create_time as createTime,
        a.create_dept_id as createDeptId,
        a.oper_dept_id as operDeptId,
        a.oper_code as operCode,
        a.oper_by as operBy,
        a.oper_time as operTime,
        a.del_flag as delFlag,
        a.ticket_apply_state as ticketApplyState,
        a.ticket_apply_time as ticketApplyTime,
        a.ticket_apply_remark as ticketApplyRemark,
        IFNULL( a.merge_status, 0 ) AS mergeStatus,
        a.submit_status as submitStatus,
        a.submit_date as submitDate,
        IFNULL( a.adjusted_amount, 0 )- IFNULL( a.exception_fee, 0 ) AS receivableAmount,
        a.exception_fee as exceptionFee,
        a.ticket_apply_allow_update as ticketApplyAllowUpdate
        ,case when IFNULL(fix.bill_id,'')!='' then 1 else 0 end as isFixedfee
        ,case when IFNULL(ad.bill_id,'')!='' then 1 else 0 end as isAddedfee
        FROM bms_ysbillmain a
        left join (
        SELECT
        bill_id,
        COUNT(*) as num
        FROM bms_fixedfee
        WHERE del_flag = 0
        GROUP BY bill_id
        ) fix on fix.bill_id = a.id
        left join (
        SELECT
        bill_id,
        COUNT(*) as num
        FROM bms_addedfee
        WHERE del_flag = 0
        GROUP BY bill_id
        ) ad on ad.bill_id = a.id
        where a.del_flag = '0'
        and a.fatherid in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <update id="updateFixedfeeRule">
        update bms_fixedfee set
        oper_time = now(),
        <if test="type == 0">
            bill_id = #{billId},
            bill_code = #{billCode},
            show_bill_id = #{billId},
            show_bill_code = #{billCode}
            where id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="type == 1">
            bill_id = null,
            bill_code = null,
            show_bill_id = null,
            show_bill_code = null
            where bill_id = #{billId}
        </if>

    </update>

    <update id="updateAddedfeefeeRule">
        update bms_addedfee set
        oper_time = now(),
        <if test="type == 0">
            bill_id = #{billId},
            bill_code = #{billCode},
            show_bill_id = #{billId},
            show_bill_code = #{billCode}
            where id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="type == 1">
            bill_id = null,
            bill_code = null,
            show_bill_id = null,
            show_bill_code = null
            where bill_id = #{billId}
        </if>

    </update>

    <update id="updatebillMainAmont">
        update bms_ysbillmain set
                                  oper_time = now(),
                                  bill_amount = #{amont},
                                  ys_amount = #{amont},
                                  adjusted_amount = #{amont}
        where id = #{id}
    </update>



    <select id="selectBmsYsbillmainExceptionByFatherid" parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        SELECT
            sum(IFNULL(t1.exception_fee,0)) as exceptionFee,
            sum(IFNULL(t1.adjusted_amount,0)) as adjustedAmount,
            t1.bill_type as billType
        from bms_ysbillmain t1
        where
            t1.del_flag =0
          and t1.fatherid = #{id}
        GROUP BY t1.bill_type
    </select>

    <select id="selectBmsPubFixedfeeRuleByClinetByCond" resultType="com.bbyb.joy.bms.domain.dto.BmsPubFixedfeeRule">
        SELECT id
        ,expenses_code as expensesCode
        ,IFNULL(amount,0) as amount
        ,remark as remarks
        from bms_fixedfee
        where del_flag = 0
        and settle_type = 1
        and client_id = #{clientId}
        and IFNULL(bill_id,'')=''
        <if test="frequency!=null and frequency == 1">
            and frequency = 1
            and DATE_FORMAT(start_date,'%Y-%m') = #{billDate}
        </if>
        <if test="frequency!=null and frequency == 2">
            and frequency = 2
            and DATE_FORMAT(start_date,'%Y-%m') &gt;= #{billDateStart}
            and DATE_FORMAT(end_date,'%Y-%m') &lt;= #{billDate}
        </if>
    </select>

    <select id="getBillCodeById" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        select
            a.bill_code billCode
             ,a.bill_type billType
        from bms_ysbillmain a
        where a.del_flag=0
          and (a.id = #{id} or a.fatherid=#{id})
    </select>

    <select id="getBillCodeByIds"  resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        select
        a.bill_code billCode
        ,a.bill_type billType
        from bms_ysbillmain a
        where a.del_flag=0
        and id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <update id="batchUpdateBmsYsbillClaims">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";" >
            update bms_ysbillmain
            <set>
                <if test="item.submitStatus != null and item.submitStatus!=''">submit_status = #{item.submitStatus},</if>
                <if test="item.operCode != null">oper_code = #{item.operCode},</if>
                <if test="item.operBy != null">oper_by = #{item.operBy},</if>
                <if test="item.operDeptId != null">oper_dept_id = #{item.operDeptId},</if>
                <if test="item.votes!=null">votes=votes+#{item.votes},</if>
                oper_time=now(),bill_amount=bill_amount+#{item.responsibleMoney},ys_amount=ys_amount+#{item.responsibleMoney},adjusted_amount=adjusted_amount+#{item.responsibleMoney},exception_fee=exception_fee+#{item.responsibleMoney}
            </set>
            where bill_code = #{item.billCode}
        </foreach>
    </update>

    <select id="checkIsExistMerge"  parameterType="java.lang.String" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillmainDto">
        select
        id,
        bill_code as billCode,
        bill_name as billName
        from bms_ysbillmain
        where del_flag = 0
        and merge_status = 1
        and id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <update id="updateAddBillMainAmt" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean">
        update bms_ysbillmain a
            inner join (
            SELECT
            ad.bill_id AS id,
            sum( IFNULL(amount,0) ) AS sumAmt,
            count( 1 ) AS count
            FROM bms_addedfee ad
            WHERE ad.del_flag = 0
            and ad.settle_type=1
            AND ad.bill_id = #{billId,jdbcType=BIGINT}
            GROUP BY ad.bill_id
            ) b on a.id = b.id
            set
                a.adjusted_amount = b.sumAmt
                    ,a.bill_amount = b.sumAmt
                    ,a.ys_amount = b.sumAmt
                    ,a.votes = b.count
        where a.id = #{billId,jdbcType=BIGINT}
    </update>

    <select id="selAddSumInfo" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsSumDto" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsSumDto">
        SELECT
            ad.bill_id AS id,
            sum( IFNULL(amount,0) ) AS sumAmt,
            count( 1 ) AS sumCount
        FROM bms_addedfee ad
        WHERE ad.del_flag = 0
          and ad.settle_type=1
          AND ad.bill_id = #{billId,jdbcType=BIGINT}
        GROUP BY ad.bill_id
    </select>

    <update id="updateAddBillMainAmt2">
        update bms_ysbillmain a
        SET
            a.adjusted_amount = #{sumAmt}
          ,a.ys_amount = #{sumAmt}
          ,a.votes = #{sumCount}
        WHERE a.id = #{id}
    </update>

    <update id="updateBillMainAmtBySon2" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean">
        update bms_ysbillmain a
        inner join (
        select
        c.bill_id as id,
        sum(
        IFNULL(c.freight,0) +
        IFNULL(c.Ultrafar_fee,0) +
        IFNULL(c.superframes_fee,0) +
        IFNULL(c.excess_fee,0) +
        IFNULL(c.reduce_fee,0) +
        IFNULL(c.delivery_fee,0) +
        IFNULL(c.outboundsorting_fee,0) +
        IFNULL(c.shortbarge_fee,0) +
        IFNULL(c.return_fee,0) +
        IFNULL(c.exception_fee,0) +
        IFNULL(c.other_cost1,0) +
        IFNULL(c.other_cost2,0) +
        IFNULL(c.other_cost3,0) +
        IFNULL(c.other_cost4,0) +
        IFNULL(c.other_cost5,0) +
        IFNULL(c.other_cost6,0) +
        IFNULL(c.other_cost7,0) +
        IFNULL(c.other_cost8,0) +
        IFNULL(c.other_cost9,0) +
        IFNULL(c.other_cost10,0) +
        IFNULL(c.other_cost11,0) +
        IFNULL(c.other_cost12,0)
        ) as sumAmt,
        SUM(IFNULL(c.exception_fee,0)) as sumExceptionFee,
        count(1) as count
        from bms_yscost_info c
        where
        c.bill_id  in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by c.bill_id
        ) b on a.id = b.id
        set
        a.adjusted_amount = b.sumAmt
        ,a.ys_amount = b.sumAmt
        ,a.votes = b.count
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_time = #{operTime}
        ,a.exception_fee = b.sumExceptionFee
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_ysbillmain a
        inner join (
        select
        bill_id as id,
        sum(IFNULL(amount,0)) as sumAmt,
        count(1) as count
        from bms_fixedfee
        where del_flag = '0'
        and bill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY bill_id
        ) b on a.id = b.id
        set
        a.adjusted_amount = a.adjusted_amount + b.sumAmt
#         ,a.bill_amount = a.adjusted_amount + b.sumAmt
        ,a.ys_amount = a.ys_amount+b.sumAmt
        ,a.votes = a.votes + b.count
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_time = #{operTime}
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_ysbillmain a
        inner join (
        select
        bill_id as id,
        sum(IFNULL(amount,0)) as sumAmt,
        count(1) as count
        from bms_addedfee
        where del_flag = '0'
        and settle_type=1
        and bill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY bill_id
        ) b on a.id = b.id
        set
        a.adjusted_amount = a.adjusted_amount + b.sumAmt
        ,a.ys_amount = a.ys_amount+b.sumAmt
        ,a.votes = a.votes + b.count
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_time = #{operTime}
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        <!-- 理赔费 -->
        update bms_ysbillmain a
        inner join (
        select
        bill_id as id,
        sum(IFNULL(responsible_money,0)) as sumAmt,
        count(1) as count
        from bms_claims_info
        where del_flag = '0'
        and bill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY bill_id
        ) b on a.id = b.id
        set
         a.adjusted_amount = a.adjusted_amount + b.sumAmt
        ,a.ys_amount = a.ys_amount+b.sumAmt
        ,a.exception_fee = a.exception_fee+b.sumAmt
        ,a.votes = a.votes + b.count
        ,a.oper_dept_id = #{operDeptId}
        ,a.oper_code = #{operCode}
        ,a.oper_by = #{operBy}
        ,a.oper_time = #{operTime}
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
    </update>

    <select id="selSumAllChildBillInfo" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsSumDto">
        select
        sum(ys_amount) as ysAmount,
        sum( adjusted_amount ) AS sumAmt,
        sum( votes ) AS sumCount,
        sum( exception_fee ) AS sumExceptionFee
        from bms_ysbillmain
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="flushFatherAmount">
        update bms_ysbillmain a
        <set>
            <if test="bmsSumDto.sumAmt!=null">
                a.adjusted_amount = #{bmsSumDto.sumAmt},
            </if>
<!--            <if test="bmsSumDto.sumAmt!=null">-->
<!--                a.bill_amount = #{bmsSumDto.sumAmt},-->
<!--            </if>-->
            <if test="bmsSumDto.sumAmt!=null">
                a.ys_amount = #{bmsSumDto.sumAmt},
            </if>
            <if test="bmsSumDto.sumCount!=null">
                a.votes = #{bmsSumDto.sumCount},
            </if>
            <if test="bmsSumDto.commitType!=null">
                a.bill_state = #{bmsSumDto.commitType},
            </if>
            <if test="bmsSumDto.commitType!=null">
                a.submit_status = #{bmsSumDto.commitType},
            </if>
            <if test="bmsSumDto.sumExceptionFee!=null">
                a.exception_fee = #{bmsSumDto.sumExceptionFee},
            </if>
        </set>
        where a.id = #{billId}
    </update>



    <update id="updateBillMainForAddAmtBySon2" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmainBean">
        update bms_ysbillmain a
        inner join (
        select
        bill_id as id,
        sum(IFNULL(amount,0)) as sumAmt,
        count(1) as count
        from bms_addedfee
        where del_flag = '0'
        and settle_type=1
        and bill_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY bill_id
        ) b on b.id = a.id
        SET a.adjusted_amount = b.sumAmt
#        ,a.bill_amount = b.sumAmt
        ,a.ys_amount = b.sumAmt
        ,a.votes = b.count
    </update>

    <select id="selSumAllChildForAddBillInfo" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsSumDto">
        select
        sum(ys_amount) as ysAmount,
        sum( adjusted_amount ) AS sumAmt,
        sum( votes ) AS sumCount,
        sum( exception_fee ) AS sumExceptionFee
        from bms_ysbillmain
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="flushFatherForAddAmount" parameterType="java.util.Map">
        update bms_ysbillmain
        set
        votes = #{bmsSumDto.sumCount}
        ,bill_amount=#{bmsSumDto.sumAmt}
        ,adjusted_amount = #{bmsSumDto.sumAmt}
        ,ys_amount = #{bmsSumDto.sumAmt}
        <if test="commitType!=null">
            ,a.submit_status = #{commitType}
            ,a.bill_state = #{commitType}
        </if>
        <if test="commitType== 2">
            ,a.submit_date = now()
        </if>
        where a.id = #{billId}
    </update>

    <select id="selectBmsYsbillmainByFathersidByStr" resultMap="BmsYsbillmainResult">
        <include refid="selectBmsYsbillmainVo"/>
        where del_flag = '0'
        and fatherid in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <update id="updateBatchCostShowBillByChild">
        UPDATE bms_yscost_info
        SET show_bill_code = #{flushBillCode}
        ,show_bill_id = #{flushBillId}
        WHERE bill_id IN
        <foreach collection="childBills" item="billInfo" separator="," open="(" close=")">
            #{billInfo.id}
        </foreach>
        ;
        UPDATE bms_addedfee
        SET show_bill_code = #{flushBillCode}
        WHERE bill_id IN
        <foreach collection="childBills" item="billInfo" separator="," open="(" close=")">
            #{billInfo.id}
        </foreach>
        ;
        UPDATE bms_fixedfee
        SET show_bill_code = #{flushBillCode}
        WHERE bill_id IN
        <foreach collection="childBills" item="billInfo" separator="," open="(" close=")">
            #{billInfo.id}
        </foreach>
        ;
        UPDATE bms_claims_info
        SET show_bill_code = #{flushBillCode}
        WHERE bill_id IN
        <foreach collection="childBills" item="billInfo" separator="," open="(" close=")">
            #{billInfo.id}
        </foreach>
    </update>

    <update id="updateBatchCostShowBillByCur">
        UPDATE bms_yscost_info
        SET show_bill_code = #{flushBillCode}
          ,show_bill_id = #{flushBillId}
        WHERE bill_id = #{curBillId}
        ;
        UPDATE bms_addedfee
        SET show_bill_code = #{flushBillCode}
          ,show_bill_id = #{flushBillId}
        WHERE bill_id = #{curBillId}
        ;
        UPDATE bms_fixedfee
        SET show_bill_code = #{flushBillCode}
          ,show_bill_id = #{flushBillId}
        WHERE bill_id = #{curBillId}
        ;
        UPDATE bms_claims_info
        SET show_bill_code = #{flushBillCode}
          ,show_bill_id = #{flushBillId}
        WHERE bill_id = #{curBillId}
    </update>

    <update id="childBindFatherId" >
        UPDATE bms_ysbillmain
        SET fatherid = #{fatherId}
        WHERE del_flag = 0
        AND id IN
        <foreach collection="childIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="selByGroupFatherIdSumAllChildBillInfo" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsSumDto">
        select
        fatherid,
        sum(ys_amount) as ysAmount,
        sum( adjusted_amount ) AS sumAmt,
        sum( votes ) AS sumCount,
        sum( exception_fee ) AS sumExceptionFee
        from bms_ysbillmain
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY fatherid
    </select>


    <update id="updateBmsYsbillmainByClaims" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        update bms_ysbillmain
        <trim prefix="SET" suffixOverrides=",">
            <if test="votes != null">votes = #{votes},</if>
            <if test="responsibleMoney != null">bill_amount = bill_amount+#{responsibleMoney},
                adjusted_amount = adjusted_amount+#{responsibleMoney},
                ys_amount = ys_amount+#{responsibleMoney},exception_fee=ifnull(exception_fee,0)+#{responsibleMoney},
            </if>
            <if test="unClaimesAmount != null">un_claimes_amount = #{unClaimesAmount},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>