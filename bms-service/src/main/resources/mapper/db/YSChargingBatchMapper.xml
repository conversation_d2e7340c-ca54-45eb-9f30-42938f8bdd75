<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.YSChargingBatchMapper">

    <!-- 批量新增应收主费用表  bms_yscost_main_info  -->
    <insert id="insertBmsyscostMainInfoBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostMainInfo" >
        insert into bms_yscost_main_info(id,expenses_code,business_type,client_id,institution_id,expenses_type,cost_dimension,charge_type,fee_flag,quoterule_id,rule_name,remarks,freight,delivery_fee,ultrafar_fee,superframes_fee,excess_fee,reduce_fee,outboundsorting_fee,shortbarge_fee,return_fee,exception_fee,adjust_fee,adjust_remark,other_cost1,other_cost2,other_cost3,other_cost4,other_cost5,other_cost6,other_cost7,other_cost8,other_cost9,other_cost10,other_cost11,other_cost12,sum_fee,cost_attribute,total_weight,total_volume,total_boxes,total_number,oper_code,oper_by,oper_time,create_code,create_by,create_time,del_flag,bill_id,bill_date,business_time,over_num,over_sendnum,storage_fee_price,disposal_fee_price,other_fee_remark,fee_type_first,fee_create_ate,is_increment,order_date,signing_date,quoteruledetail_id,show_bill_id,show_bill_code,extra_field1,warehouse_code_arr,settle_setting)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id},#{entity.expensesCode},#{entity.businessType},#{entity.clientId},#{entity.institutionId},#{entity.expensesType},#{entity.costDimension},#{entity.chargeType},#{entity.feeFlag},#{entity.quoteruleId},#{entity.ruleName},#{entity.remarks},#{entity.freight},#{entity.deliveryFee},#{entity.ultrafarFee},#{entity.superframesFee},#{entity.excessFee},#{entity.reduceFee},#{entity.outboundsortingFee},#{entity.shortbargeFee},#{entity.returnFee},#{entity.exceptionFee},#{entity.adjustFee},#{entity.adjustRemark},#{entity.otherCost1},#{entity.otherCost2},#{entity.otherCost3},#{entity.otherCost4},#{entity.otherCost5},#{entity.otherCost6},#{entity.otherCost7},#{entity.otherCost8},#{entity.otherCost9},#{entity.otherCost10},#{entity.otherCost11},#{entity.otherCost12},#{entity.sumFee},#{entity.costAttribute},#{entity.totalWeight},#{entity.totalVolume},#{entity.totalBoxes},#{entity.totalNumber},#{entity.operCode},#{entity.operBy},#{entity.operTime},#{entity.createCode},#{entity.createBy},#{entity.createTime},#{entity.delFlag},#{entity.billId},#{entity.billDate},#{entity.businessTime},#{entity.overNum},#{entity.overSendnum},#{entity.storageFeePrice},#{entity.disposalFeePrice},#{entity.otherFeeRemark},#{entity.feeTypeFirst},#{entity.feeCreateAte},#{entity.isIncrement},#{entity.orderDate},#{entity.signingDate},#{entity.quoteruledetailId},#{entity.showBillId},#{entity.showBillCode},#{entity.extraField1},#{entity.warehouseCodeArr},#{entity.settleSetting})
        </foreach>
    </insert>



    <!-- 批量新增应收费用表  bms_yscost_info  -->
    <insert id="insertBmsyscostInfoBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostInfo" >
        insert into bms_yscost_info(id,expenses_code,business_type,client_id,institution_id,expenses_type,cost_dimension,charge_type,fee_flag,quoterule_id,rule_name,remarks,freight,delivery_fee,ultrafar_fee,superframes_fee,excess_fee,reduce_fee,outboundsorting_fee,shortbarge_fee,return_fee,exception_fee,adjust_fee,adjust_remark,other_cost1,other_cost2,other_cost3,other_cost4,other_cost5,other_cost6,other_cost7,other_cost8,other_cost9,other_cost10,other_cost11,other_cost12,cost_attribute,oper_code,oper_by,oper_time,del_flag,bill_id,bill_date,business_time,over_num,over_sendnum,storage_fee_price,disposal_fee_price,other_fee_remark,fee_type_first,fee_create_ate,is_increment,order_date,signing_date,quoteruledetail_id,show_bill_id,show_bill_code,extra_field1,warehouse_code_arr,settle_type,settle_amount,main_expense_id,settle_main_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.expensesCode},#{item.businessType},#{item.clientId},#{item.institutionId},#{item.expensesType},#{item.costDimension},#{item.chargeType},#{item.feeFlag},#{item.quoteruleId},#{item.ruleName},#{item.remarks},#{item.freight},#{item.deliveryFee},#{item.ultrafarFee},#{item.superframesFee},#{item.excessFee},#{item.reduceFee},#{item.outboundsortingFee},#{item.shortbargeFee},#{item.returnFee},#{item.exceptionFee},#{item.adjustFee},#{item.adjustRemark},#{item.otherCost1},#{item.otherCost2},#{item.otherCost3},#{item.otherCost4},#{item.otherCost5},#{item.otherCost6},#{item.otherCost7},#{item.otherCost8},#{item.otherCost9},#{item.otherCost10},#{item.otherCost11},#{item.otherCost12},#{item.costAttribute},#{item.operCode},#{item.operBy},#{item.operTime},0,#{item.billId},#{item.billDate},#{item.businessTime},#{item.overNum},#{item.overSendnum},#{item.storageFeePrice},#{item.disposalFeePrice},#{item.otherFeeRemark},#{item.feeTypeFirst},#{item.feeCreateAte},#{item.isIncrement},#{item.orderDate},#{item.signingDate},#{item.quoteruledetailId},#{item.showBillId},#{item.showBillCode},#{item.extraField1},#{item.warehouseCodeArr},#{item.settleType},#{item.settleAmount},#{item.mainExpenseId},#{item.settleMainId})
        </foreach>
    </insert>
    <!-- 批量作废  bms_yscost_info  -->
    <update id="delBmsyscostInfoByIds">
        update bms_yscost_info set del_flag = 1,oper_time=now(),oper_by=#{operBy} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量新增应收费用拓展表  bms_yscost_extend  -->
    <insert id="insertBmsYscostExtendBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsYscostExtend" >
        INSERT INTO bms_yscost_extend
        (
        expenses_id, business_code,warehouse_code, warehouse_name,total_boxes,
         total_number,total_weight,total_volume,cargo_value,del_flag,automatic_billing_remark,code_count,sku_number,
         store_name,order_date,signing_date,quotation_information,main_expense_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.expensesId}, #{item.businessCode},#{item.warehouseCode}, #{item.warehouseName}, #{item.totalBoxes},
             #{item.totalNumber}, #{item.totalWeight}, #{item.totalVolume}, #{item.cargoValue}, 0, #{item.automaticBillingRemark},
             #{item.codeCount}, #{item.skuNumber},
            #{item.storeName}, #{item.orderDate},#{item.signingDate},#{item.quotationInformation},#{item.mainExpenseId}
            )
        </foreach>
    </insert>
    <!-- 批量作废  bms_yscost_extend  -->
    <update id="delBmsYscostExtendByExpensesIds">
        update bms_yscost_extend set del_flag = 1 where expenses_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量新增应收费用中间表  bms_ysexpenses_middle  -->
    <insert id="insertBmsYsexpensesMiddleBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsexpensesMiddle" >
        insert into bms_ysexpenses_middle(id,ysbill_id,ysbil_type,expenses_id,main_expense_id,oper_by,oper_time,del_flag,share_type,share_amount,freight,delivery_fee,ultrafar_fee,superframes_fee,excess_fee,reduce_fee,outboundsorting_fee,shortbarge_fee,return_fee,exception_fee,other_cost1,other_cost2,other_cost3,other_cost4,other_cost5,other_cost6,other_cost7,other_cost8,other_cost9,other_cost10,other_cost11,other_cost12)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.ysbillId},#{item.ysbilType},#{item.expensesId},#{item.mainExpenseId},#{item.operBy},#{item.operTime},0,#{item.shareType},#{item.shareAmount},#{item.freight},#{item.deliveryFee},#{item.ultrafarFee},#{item.superframesFee},#{item.excessFee},#{item.reduceFee},#{item.outboundsortingFee},#{item.shortbargeFee},#{item.returnFee},#{item.exceptionFee},#{item.otherCost1},#{item.otherCost2},#{item.otherCost3},#{item.otherCost4},#{item.otherCost5},#{item.otherCost6},#{item.otherCost7},#{item.otherCost8},#{item.otherCost9},#{item.otherCost10},#{item.otherCost11},#{item.otherCost12})
        </foreach>
    </insert>
    <!-- 批量作废  bms_ysexpenses_middle  -->
    <update id="delBmsYsexpensesMiddeByExpensesIds">
        update bms_ysexpenses_middle set del_flag = 1,oper_time=now(),oper_by=#{operBy} where expenses_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量修改 单据信息  -->
    <update id="updateBmsYsbillcodeinfoReturnrOverByIds">
        <foreach item="code" collection="codes" separator=";" >
            update bms_ysbillcodeinfo set returnr_over_num = #{code.returnrOverNum} where id = #{code.id}
        </foreach>
    </update>


    <!-- 批量修改 单据计费状态  -->
    <!--  `cost_status` '计费状态0未计费1已计费',-->
    <update id="updateBmsYsbillcodeinfoStatusByIds">
        update bms_ysbillcodeinfo set cost_status = #{status},fail_remark=null where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <!-- 批量修改 单据计费状态 库存表 -->
    <!--  `cost_status` '计费状态0未计费1已计费',-->
    <update id="updateBmsYsstockinfoStatusByIds">
        update bms_ysstockinfo set cost_status = #{status},fail_remark=null where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <!--  `cost_vlaue_status` '再次计费状态0未计费1已计费',-->
    <update id="updateBmsYsbillcodeinfoVlueStatusByIds">
        update bms_ysstockinfo set cost_vlaue_status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


<!--    查询单据信息-->
    <resultMap type="com.bbyb.joy.bms.domain.dto.charginglogic.YsBillCodeInfo" id="YsbillcodeInfoResult">
        <result property="id"    column="id"    />
        <result property="relateCode"    column="relate_code"    />
        <result property="codeType"    column="code_type"    />
        <result property="paymentDays"    column="payment_days"    />
        <result property="clientId"    column="client_id"    />
        <result property="businessTime"    column="business_time"    />
        <result property="companyId"    column="company_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="totalNumber"    column="total_number"    />
        <result property="skuNumber"    column="sku_number"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="cargoValue"    column="cargo_value"    />
        <result property="orderDate"    column="order_date"    />
        <result property="signingDate"    column="signing_date"    />
        <result property="receivingStore"    column="receiving_store"    />
        <result property="settleSetting"    column="settle_setting"    />
    </resultMap>


    <resultMap type="com.bbyb.joy.bms.domain.dto.charginglogic.YsBillCodeInfo" id="YsbillcodeInfoResult2">
        <result property="id"    column="id"    />
        <result property="relateCode"    column="relate_code"    />
        <result property="codeType"    column="code_type"    />
        <result property="paymentDays"    column="payment_days"    />
        <result property="clientId"    column="client_id"    />
        <result property="businessTime"    column="business_time"    />
        <result property="companyId"    column="company_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="totalNumber"    column="total_number"    />
        <result property="trust"    column="trust"    />
        <result property="skuNumber"    column="sku_number"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="cargoValue"    column="cargo_value"    />
        <result property="orderDate"    column="order_date"    />
        <result property="signingDate"    column="signing_date"    />
        <result property="trust"    column="trust"    />
        <result property="receivingStore"    column="receiving_store"    />
        <result property="settleSetting"    column="settle_setting"    />
    </resultMap>

<!--    单据表-->
    <select id="selectYsbillcodeInfoList"  resultMap="YsbillcodeInfoResult">
        select t1.id,t1.relate_code,t1.code_type,t2.payment_days,t2.id client_id,t1.order_date,t1.signing_date,t1.receiving_store,
        case when setting.date_type=1 then t1.order_date else  t1.signing_date end  business_time,
        t1.company_id,t1.warehouse_code,t1.total_boxes,t1.total_number,t1.total_weight,t1.total_volume,t1.cargo_value,t3.warehouse_name,t1.sku_number,t2.settle_setting
        from bms_ysbillcodeinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        left join pub_settledate_setting setting on setting.client_id=t2.id and bill_type=1
        <where>
            t1.del_flag=0 and t1.id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>
<!--    库存表-->
    <select id="selectYsStorageList"  resultMap="YsbillcodeInfoResult2">
        select t1.id,t1.stock_code as relate_code,t2.payment_days,t2.id client_id,t1.instorage_time as business_time,t1.company_id,
        t1.instorage_time as order_date,t1.instorage_time as signing_date,
               t1.warehouse_code,t1.total_boxes,t1.weight as total_weight,t1.volume as total_volume,t3.warehouse_name,t1.trust,t2.settle_setting
        from bms_ysstockinfo t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        <where>
            t1.del_flag=0 and t1.id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>
    <!--    查询费用单客户对账日(费用单)-->
    <select id="selectYsCostPaymentDaysList"  resultMap="YsbillcodeInfoResult">
        select t1.id,t1.client_id,t1.business_time,t2.payment_days
        from bms_yscost_info t1
        left join bms_clientinfo t2 on t1.client_id=t2.id
        <where>
            t1.del_flag=0 and t1.id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <!--    取消计费-查询单据是否有其他计费-->
    <select id="selectCancelOrderBillId" resultType="java.lang.String">
        select ysbill_id from bms_ysexpenses_middle
        <where>
            del_flag=0 and ysbil_type=#{ysbilType}

            <if test="null != yscostIds and yscostIds.size > 0">
                and expenses_id not in
                <foreach item="yscostid" collection="yscostIds" open="(" separator="," close=")">
                    #{yscostid}
                </foreach>
            </if>


            <if test="null != ysbillIds and ysbillIds.size > 0">
                and ysbill_id in
                <foreach item="ysbillId" collection="ysbillIds" open="(" separator="," close=")">
                    #{ysbillId}
                </foreach>
            </if>


        </where>
    </select>


    <update id="updateYfbillCostStatus">
        <if test="ids!=null and ids.size()>0">
            update bms_ysbillcodeinfo set cost_status = 0,returnr_over_num=0,oper_time = null,fail_remark = null
            <where>
                del_flag=0 and code_type = 1 and id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </where>
            ;
        </if>

        <if test="codes!=null and codes.size()>0">
            update bms_ysbillcodeinfo set cost_status = 0,returnr_over_num=0,fail_remark = null
            <where>
                del_flag=0 and code_type in (2,3) and relate_code  in
                <foreach item="id" collection="codes" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </where>
            ;
            update bms_ysstockinfo set cost_status = 0,cost_vlaue_status=0,fail_remark = null
            <where>
                del_flag=0 and stock_code in
                <foreach item="id" collection="codes" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </where>
        </if>

    </update>

    <update id="updateYfbillCostDelete">
        update bms_ysexpenses_middle set del_flag = 1 where main_expense_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yscost_info set del_flag = 1 where main_expense_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yscost_main_info set del_flag = 1 where id in (
            select distinct
                main_expense_id
            from bms_yscost_info
            where main_expense_id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        )
        ;
        update bms_yscost_extend set del_flag = 1 where main_expense_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        delete from pub_ys_quotation_cost where ys_cost_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>

    </update>


    <update id="updateYfbillCostDeleteForShare">
        update bms_ysexpenses_middle set del_flag = 1 where main_expense_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yscost_info set del_flag = 1 where main_expense_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yscost_main_info set del_flag = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        update bms_yscost_extend set del_flag = 1 where main_expense_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
        delete from pub_ys_quotation_cost where ys_cost_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>

    </update>


    <update id="updateBmsAddedfeeCost">
        <if test="list!=null and list.size()>0">
            <foreach collection="list" item="item">
                update bms_addedfee set
                charging_date=now(),
                <if test="item.itemId !=null">
                    item_id = #{item.itemId},
                </if>
                <if test="item.amount !=null">
                    amount = #{item.amount},
                </if>
                <if test="item.remark !=null">
                    remark = #{item.remark},
                </if>
                cost_status = 1,
                fee_source=1
                where id = #{item.id}
                ;
            </foreach>
        </if>

    </update>

    <select id="selectOrderByBillDate" resultMap="YsbillcodeInfoResult" parameterType="com.bbyb.joy.bms.domain.dto.charginglogic.OrderBillingBean">

        SELECT t1.* from bms_ysbillcodeinfo t1
        left join bms_ysexpenses_middle t2 on t1.id = t2.ysbill_id and t2.del_flag =0
        left join bms_yscost_info t3 on t3.id = t2.expenses_id and t3.del_flag =0
        where
        t1.id = #{ysbillId}
        and t3.bill_date = #{billDate}
        limit 1

    </select>




    <update id="modifyCommnonBatchCodeFailRemark">
        update bms_ysbillcodeinfo
        set
        fail_remark=#{autoYsCodeInfo.failRemark},
        oper_time=now()
        where del_flag = '0'
        and id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="modifyBatchCodeFailRemark">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update bms_ysbillcodeinfo
            set
            fail_remark=#{item.failRemark},
            oper_time=now()
            where del_flag = '0'
            and id = #{item.id}
        </foreach>
    </update>

    <update id="modifyCommonBatchStockCodeFailRemark">
        update bms_ysstockinfo
        set
        fail_remark=#{autoYsCodeInfo.failRemark},
        oper_time=now()
        where del_flag = '0'
        and id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="modifyBatchStockCodeFailRemark">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update bms_ysstockinfo
            set
            fail_remark=#{item.failRemark},
            oper_time=now()
            where del_flag = '0'
            and id = #{item.id}
        </foreach>
    </update>


</mapper>