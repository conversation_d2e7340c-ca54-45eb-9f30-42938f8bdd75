<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYshxRecordMiddleMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYshxRecordMiddle" id="BmsYshxRecordMiddleResult">
        <result property="id"    column="id"    />
        <result property="recordId"    column="record_id"    />
        <result property="hxId"    column="hx_id"    />
        <result property="payment"    column="payment"    />
        <result property="collectionTime"    column="collection_time"    />
        <result property="clientId"    column="client_id"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="payType"    column="pay_type"    />
    </resultMap>

    <sql id="selectBmsYshxRecordMiddleVo">
        select id, record_id, hx_id, payment,collection_time,client_id, oper_dept_id, oper_code, oper_by, oper_time, del_flag,pay_type from bms_yshx_record_middle
    </sql>

    <select id="selectBmsYshxRecordMiddleList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYshxRecordMiddle" resultMap="BmsYshxRecordMiddleResult">
        <include refid="selectBmsYshxRecordMiddleVo"/>
        <where>
            <if test="recordId != null "> and record_id = #{recordId}</if>
            <if test="hxId != null "> and hx_id = #{hxId}</if>
            <if test="payment != null "> and payment = #{payment}</if>
            <if test="collectionTime != null "> and collection_time = #{collectionTime}</if>
            <if test="clientId != null "> and client_id = #{clientId}</if>
            <if test="operDeptId != null "> and oper_dept_id = #{operDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
            <if test="payType != null">and pay_type = #{payType}</if>
        </where>
    </select>

    <select id="selectBmsYshxRecordMiddleById" parameterType="java.lang.String" resultMap="BmsYshxRecordMiddleResult">
        <include refid="selectBmsYshxRecordMiddleVo"/>
        where id = #{id}
    </select>

    <insert id="insertBmsYshxRecordMiddle" parameterType="com.bbyb.joy.bms.domain.dto.BmsYshxRecordMiddle" useGeneratedKeys="true" keyProperty="id">
        insert into bms_yshx_record_middle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordId != null">record_id,</if>
            <if test="hxId != null">hx_id,</if>
            <if test="payment != null">payment,</if>
            <if test="collectionTime != null">collection_time,</if>
            <if test="clientId != null">client_id,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="payType != null">pay_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recordId != null">#{recordId},</if>
            <if test="hxId != null">#{hxId},</if>
            <if test="payment != null">#{payment},</if>
            <if test="collectionTime != null">#{collectionTime},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="payType != null">#{payType},</if>
         </trim>
    </insert>

    <insert id="insertBmsYshxRecordMiddleList" parameterType="java.util.Arrays">
        insert into bms_yshx_record_middle
        (
            record_id,
            hx_id,
            payment,
            collection_time,
            client_id,
            oper_dept_id,
            oper_code,
            oper_by,
            oper_time,
            del_flag,
            pay_type
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.recordId},
            #{item.hxId},
            #{item.payment},
            #{item.collectionTime},
            #{item.clientId},
            #{item.operDeptId},
            #{item.operCode},
            #{item.operBy},
            #{item.operTime},
            #{item.delFlag},
            #{item.payType}
            )
        </foreach>

    </insert>


    <update id="updateBmsYshxRecordMiddle" parameterType="com.bbyb.joy.bms.domain.dto.BmsYshxRecordMiddle">
        update bms_yshx_record_middle
        <trim prefix="SET" suffixOverrides=",">
            <if test="recordId != null">record_id = #{recordId},</if>
            <if test="hxId != null">hx_id = #{hxId},</if>
            <if test="payment != null">payment = #{payment},</if>
            <if test="collectionTime != null">collection_time = #{collectionTime},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="payType != null">pay_type = #{payType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYshxRecordMiddleById" parameterType="java.lang.String">
        delete from bms_yshx_record_middle where id = #{id}
    </delete>

    <delete id="deleteBmsYshxRecordMiddleByIds">
        delete from bms_yshx_record_middle where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYshxRecordMiddleStatusByIds">
        update bms_yshx_record_middle set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchSave" parameterType="java.util.List">
        insert into bms_yshx_record_middle
        (record_id, hx_id, payment, collection_time, client_id, oper_dept_id, oper_code, oper_by, oper_time, del_flag,pay_type)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.recordId},#{item.hxId},#{item.payment},#{item.collectionTime},#{item.clientId},#{item.operDeptId},#{item.operCode},#{item.operBy},#{item.operTime},#{item.delFlag},#{item.payType})
        </foreach>
    </insert>

    <select id="selectByhxId" resultMap="BmsYshxRecordMiddleResult">
        <include refid="selectBmsYshxRecordMiddleVo"/>
        where
        <if test="hxId != null and hxId.size>0 ">
            hx_id in
            <foreach collection="hxId" item="hxId" open="(" separator="," close=")">
                #{hxId}
            </foreach>
        </if>
    </select>
</mapper>