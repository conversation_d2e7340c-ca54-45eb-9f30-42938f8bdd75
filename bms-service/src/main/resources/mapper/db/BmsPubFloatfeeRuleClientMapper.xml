<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="mapper.db.BmsPubFloatfeeRuleClientMapper">
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsPubFloatfeeRuleClient" id="BmsPubFloatfeeRuleClientMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="INTEGER"/>
        <result property="clientCode" column="client_code" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdName" column="created_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="DATE"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedName" column="updated_name" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="DATE"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.bbyb.joy.bms.domain.dto.dto.BmsPubFloatfeeRuleClientDto" id="BmsPubFloatfeeRuleClientDtoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="INTEGER"/>
        <result property="clientCode" column="client_code" jdbcType="VARCHAR"/>
        <result property="clientId" column="clientId" jdbcType="VARCHAR"/>
        <result property="clientName" column="client_name" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdName" column="created_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="DATE"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedName" column="updated_name" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="DATE"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultMap="BmsPubFloatfeeRuleClientMap">
        select
            id,rule_id,client_code,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark
        from pub_floatfee_rule_client
        where id = #{id}
    </select>
    <select id="queryByRuleId" resultMap="BmsPubFloatfeeRuleClientMap">
        select
            id,rule_id,client_code,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark
        from pub_floatfee_rule_client
        where rule_id = #{ruleId}
    </select>
    <select id="queryDtoByRuleId" resultMap="BmsPubFloatfeeRuleClientDtoMap">
        SELECT
            t1.id,
            t1.rule_id,
            t2.id as clientId,
            t1.client_code,
            t2.client_name,
            t1.created_by,
            t1.created_name,
            t1.created_time,
            t1.updated_by,
            t1.updated_name,
            t1.updated_time,
            t1.remark
        FROM pub_floatfee_rule_client t1
        LEFT JOIN bms_clientinfo t2 ON t2.client_code = t1.client_code
		AND IFNULL(t2.del_flag,1)=0
        WHERE t1.rule_id = #{ruleId}
          and IFNULL(t1.del_flag,1)=0
        ORDER BY t1.updated_time DESC
    </select>


    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into pub_floatfee_rule_client(id,rule_id,client_code,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark)
        values (#{id},#{ruleId},#{clientCode},#{createdBy},#{createdName},#{createdTime},#{updatedBy},#{updatedName},#{updatedTime},#{remark})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into pub_floatfee_rule_client(id,rule_id,client_code,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.ruleId},#{entity.clientCode},#{entity.createdBy},#{entity.createdName},#{entity.createdTime},#{entity.updatedBy},#{entity.updatedName},#{entity.updatedTime},#{entity.remark})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into pub_floatfee_rule_client(id,rule_id,client_code,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.ruleId},#{entity.clientCode},#{entity.createdBy},#{entity.createdName},#{entity.createdTime},#{entity.updatedBy},#{entity.updatedName},#{entity.updatedTime},#{entity.remark})
        </foreach>
        on duplicate key update
        id=values(id),
        rule_id=values(rule_id),
        client_code=values(client_code),
        created_by=values(created_by),
        created_name=values(created_name),
        created_time=values(created_time),
        updated_by=values(updated_by),
        updated_name=values(updated_name),
        updated_time=values(updated_time),
        remark=values(remark)
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update pub_floatfee_rule_client
        <set>
            <if test="id != null and id != ''">
                id = #{id},
            </if>
            <if test="ruleId != null and ruleId != ''">
                rule_id = #{ruleId},
            </if>
            <if test="clientCode != null and clientCode != ''">
                client_code = #{clientCode},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdName != null and createdName != ''">
                created_name = #{createdName},
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time = #{createdTime},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedName != null and updatedName != ''">
                updated_name = #{updatedName},
            </if>
            <if test="updatedTime != null and updatedTime != ''">
                updated_time = #{updatedTime},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键作废-->
    <delete id="deleteById">
        delete from pub_floatfee_rule_client where id = #{id}
    </delete>



    <!--通过规则id作废-->
    <delete id="deleteByRuleId">
        delete from pub_floatfee_rule_client
        where rule_id = #{ruleId}
    </delete>

</mapper>