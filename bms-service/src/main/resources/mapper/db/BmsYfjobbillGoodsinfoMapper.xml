<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfjobbillGoodsinfoMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYfjobbillGoodsinfo" id="BmsYfjobbillGoodsinfoResult">
        <result property="id"    column="id"    />
        <result property="jobbillId"    column="jobbill_id"    />
        <result property="skuCode"    column="sku_code"    />
        <result property="skuName"    column="sku_name"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="oddBoxes"    column="odd_boxes"    />
        <result property="numbers"    column="numbers"    />
        <result property="boxType"    column="box_type"    />
        <result property="temperatureType"    column="temperature_type"    />
        <result property="weight"    column="weight"    />
        <result property="volume"    column="volume"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="price"    column="price"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="schedulingBillCode"    column="scheduling_bill_code"    />
        <result property="skuClass"    column="sku_Class"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="unit"    column="unit"    />
        <result property="specification"    column="specification"    />

    </resultMap>

    <sql id="selectBmsYfjobbillGoodsinfoVo">
        select id, jobbill_id, sku_code, sku_name, total_boxes, odd_boxes, numbers, box_type, temperature_type, weight, volume,total_weight,total_volume, price, oper_code, oper_by, oper_dept_id, oper_time, del_flag,sku_class from bms_yfjobbill_goodsinfo
    </sql>

    <select id="selectBmsYfjobbillGoodsinfoList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfjobbillGoodsinfo" resultMap="BmsYfjobbillGoodsinfoResult">
        <include refid="selectBmsYfjobbillGoodsinfoVo"/>
        <where>
            <if test="jobbillIds != null and jobbillIds.size>0 ">
                AND jobbill_id in
                <foreach collection="jobbillIds" item="jobbillIds" open="(" separator="," close=")">
                    #{jobbillIds}
                </foreach>
            </if>
            <if test="jobbillId != null  and jobbillId != ''"> and jobbill_id = #{jobbillId}</if>
            <if test="skuCode != null  and skuCode != ''"> and sku_code = #{skuCode}</if>
            <if test="skuName != null  and skuName != ''"> and sku_name like concat('%', #{skuName}, '%')</if>
            <if test="totalBoxes != null "> and total_boxes = #{totalBoxes}</if>
            <if test="oddBoxes != null "> and odd_boxes = #{oddBoxes}</if>
            <if test="numbers != null "> and numbers = #{numbers}</if>
            <if test="boxType != null  and boxType != ''"> and box_type = #{boxType}</if>
            <if test="temperatureType != null  and temperatureType != ''"> and temperature_type = #{temperatureType}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="volume != null "> and volume = #{volume}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operDeptId != null "> and oper_dept_id = #{operDeptId}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
        </where>
    </select>
    
    <select id="selectBmsYfjobbillGoodsinfoById" parameterType="java.lang.String" resultMap="BmsYfjobbillGoodsinfoResult">
        <include refid="selectBmsYfjobbillGoodsinfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBmsYfjobbillGoodsinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfjobbillGoodsinfo" useGeneratedKeys="true" keyProperty="id">
        insert into bms_yfjobbill_goodsinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jobbillId != null">jobbill_id,</if>
            <if test="skuCode != null">sku_code,</if>
            <if test="skuName != null">sku_name,</if>
            <if test="totalBoxes != null">total_boxes,</if>
            <if test="oddBoxes != null">odd_boxes,</if>
            <if test="numbers != null">numbers,</if>
            <if test="boxType != null">box_type,</if>
            <if test="temperatureType != null">temperature_type,</if>
            <if test="weight != null">weight,</if>
            <if test="volume != null">volume,</if>
            <if test="totalWeight != null">total_weight,</if>
            <if test="totalVolume != null">total_volume,</if>
            <if test="price != null">price,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="skuClass != null">sku_class,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobbillId != null">#{jobbillId},</if>
            <if test="skuCode != null">#{skuCode},</if>
            <if test="skuName != null">#{skuName},</if>
            <if test="schedulingBillCode != null">#{schedulingBillCode},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="networkCode != null">#{networkCode},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="totalBoxes != null">#{totalBoxes},</if>
            <if test="oddBoxes != null">#{oddBoxes},</if>
            <if test="numbers != null">#{numbers},</if>
            <if test="boxType != null">#{boxType},</if>
            <if test="temperatureType != null">#{temperatureType},</if>
            <if test="weight != null">#{weight},</if>
            <if test="volume != null">#{volume},</if>
            <if test="totalWeight != null">#{totalWeight},</if>
            <if test="totalVolume != null">#{totalVolume},</if>
            <if test="price != null">#{price},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="skuClass != null">#{skuClass},</if>
         </trim>
    </insert>

    <update id="updateBmsYfjobbillGoodsinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfjobbillGoodsinfo">
        update bms_yfjobbill_goodsinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobbillId != null">jobbill_id = #{jobbillId},</if>
            <if test="skuCode != null">sku_code = #{skuCode},</if>
            <if test="skuName != null">sku_name = #{skuName},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="oddBoxes != null">odd_boxes = #{oddBoxes},</if>
            <if test="numbers != null">numbers = #{numbers},</if>
            <if test="boxType != null">box_type = #{boxType},</if>
            <if test="temperatureType != null">temperature_type = #{temperatureType},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="volume != null">volume = #{volume},</if>
            <if test="totalWeight != null">total_weight = #{totalWeight},</if>
            <if test="totalVolume != null">total_volume = #{totalVolume},</if>
            <if test="price != null">price = #{price},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="skuClass != null">sku_class = #{skuClass},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYfjobbillGoodsinfoById" parameterType="java.lang.String">
        delete from bms_yfjobbill_goodsinfo where id = #{id}
    </delete>

    <delete id="deleteBmsYfjobbillGoodsinfoByIds">
        delete from bms_yfjobbill_goodsinfo where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYfjobbillGoodsinfoStatusByIds">
        update bms_yfjobbill_goodsinfo set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchSave" parameterType="java.util.List">
        insert into bms_yfjobbill_goodsinfo
        (jobbill_id, sku_code, sku_name,total_boxes,odd_boxes, numbers, box_type, temperature_type, weight, volume,total_weight,total_volume, price, oper_code, oper_by,
         oper_dept_id, oper_time, del_flag,sku_class)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.jobbillId},#{item.skuCode},#{item.skuName},#{item.totalBoxes},#{item.oddBoxes},#{item.numbers},#{item.boxType},#{item.temperatureType},
            #{item.weight},#{item.volume},#{item.totalWeight},#{item.totalVolume},#{item.price},#{item.operCode},#{item.operBy},#{item.operDeptId},#{item.operTime},#{item.delFlag},#{item.skuClass})
        </foreach>
    </insert>

    <select id="selectBySchedulingBillCode" resultMap="BmsYfjobbillGoodsinfoResult">

        select yg.id, yg.jobbill_id, yg.sku_code, yg.sku_name, yg.total_boxes, yg.odd_boxes, yg.numbers,
        yg.box_type, yg.temperature_type, ms.outerbox_weight as weight, ms.outerbox_volume as volume,yg.total_weight,yg.total_volume,
        yg.price, yg.oper_code, yg.oper_by, yg.oper_dept_id, yg.oper_time, yg.del_flag
        ,y.relate_code relateCode,yg.sku_class,ms.unit,ms.specification
        from bms_yfjobbill_goodsinfo yg
        left join bms_yfjobbillinfo y on yg.jobbill_id = y.id and y.del_flag = '0'
        left join mdm_skuinfo ms on ms.sku_code=yg.sku_code and ms.status=0
         where  yg.del_flag= '0'
        <if test="codes != null and codes.size>0 ">
          and  yg.jobbill_id in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>


</mapper>