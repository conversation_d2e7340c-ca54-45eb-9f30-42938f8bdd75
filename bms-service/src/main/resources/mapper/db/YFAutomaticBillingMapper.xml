<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.YFAutomaticBillingMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.charginglogic.AutoYfCodeInfo" id="AutoYFCodeInfoResult">
        <result property="id"    column="id"    />
        <result property="pkId"    column="pk_id"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="schedulingBillCode"    column="scheduling_bill_code"    />
        <result property="virtualOrderNo"    column="virtual_order_no"    />
        <result property="projectQuote"    column="project_quote"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="carrierName"    column="carrier_name"    />
        <result property="companyId"    column="company_id"    />
        <result property="networkCode"    column="network_code"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="totalNumber"    column="total_number"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="cargoValue"    column="cargo_value"    />
        <result property="dispatchDate"    column="dispatch_date"    />
        <result property="finishDate"    column="finish_date"    />
        <result property="storageServiceProvider"    column="storage_service_provider"    />
        <result property="lineCode"    column="line_code"    />
        <result property="lineName"    column="line_name"    />
        <result property="baseStores"    column="base_stores"    />
        <result property="baseKilometer"    column="base_kilometer"    />
        <result property="totalKilometer"    column="total_kilometer"    />
        <result property="numberLoadingPoints"    column="number_loading_points"    />
        <result property="numberUnloadingPoints"    column="number_unloading_points"    />
        <result property="totalVotenumber"    column="total_votenumber"    />
        <result property="driver"    column="driver"    />
        <result property="carCode"    column="car_code"    />
        <result property="carType"    column="car_type"    />
        <result property="carModel"    column="car_model"    />
        <result property="headOfficeTimes"    column="head_office_times"    />
        <result property="bodyOfficeTimes"    column="body_office_times"    />
        <result property="provinceOrigin"    column="province_origin"    />
        <result property="originatingCity"    column="originating_City"    />
        <result property="originatingArea"    column="originating_area"    />
        <result property="originatingAddress"    column="originating_address"    />
        <result property="destinationProvince"    column="destination_Province"    />
        <result property="destinationCity"    column="destination_city"    />
        <result property="destinationArea"    column="destination_area"    />
        <result property="destinationAddress"    column="destination_address"    />
        <result property="costStatus"    column="cost_status"    />
        <result property="billingStatus"    column="billing_status"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="transportType"    column="transport_type"    />
        <result property="deliveryMode"    column="delivery_mode"    />
        <result property="deliveryMode"    column="delivery_mode"    />
        <result property="tlineCode"    column="tline_code"    />
        <result property="tlineName"    column="tline_name"    />
        <result property="vehicleTemperatureType"    column="vehicle_temperature_type"    />
        <result property="hyOrderNo"    column="hy_order_no"    />
        <result property="expressNo"    column="express_no"    />
        <result property="paymentDays"    column="payment_days"    />
        <result property="carrierId"    column="carrier_id"    />
        <result property="relateCode"    column="relate_code"    />
        <result property="oddBoxes"    column="odd_boxes"    />
        <result property="boxType"    column="box_type"    />
        <result property="temperatureType"    column="temperature_type"    />
        <result property="trust"    column="trust"    />
        <result property="warehouseArea"    column="warehouse_area"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="aqty"    column="aqty"    />
        <result property="palletNumber"    column="pallet_number"    />
        <result property="carModelStr"    column="car_model"    />
        <result property="splitTotalNumber"    column="split_total_number"    />
        <result property="skuNumber"    column="sku_number"    />
        <result property="ifAutarky"    column="if_autarky"    />
        <result property="lineStoreNum"    column="store_num"    />
        <result property="lineTotalMileage"    column="total_mileage"    />
        <result property="tlineCode"    column="tline_code"    />
        <result property="tlineName"    column="tline_name"    />
        <result property="orderType"    column="order_type"    />
        <result property="transportType"    column="transport_type"    />
        <result property="signingDate"    column="signing_date"    />
        <result property="itemId"    column="item_id"    />
        <result property="vehicleTemperatureType"    column="vehicle_temperature_type"    />
        <result property="cwPalletNumber"    column="cw_pallet_number"    />
        <result property="ldPalletNumber"    column="ld_pallet_number"    />
        <result property="lcPalletNumber"    column="lc_pallet_number"    />
        <result property="feeBelong"    column="fee_belong"    />
        <result property="dispatchDateWeek"    column="dispatchDateWeek"    />
        <result property="finishDateWeek"    column="finishDateWeek"    />
        <result property="isTimeout"    column="isTimeout"    />
        <result property="unit"    column="unit"    />
    </resultMap>

<!--    查询应付单据表需要计费的所有承运商id以及机构id-->
    <select id="selectYfBillCodeCarrierIds"  resultMap="AutoYFCodeInfoResult" >
        select t1.id ,t2.id as carrier_id, t1.company_id
        from bms_yfbillcodeinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join bms_clientinfo t3 on t1.client_id=t3.id
        WHERE t1.del_flag=0
        and t2.del_flag=0
        and t1.cost_status=0
        and t1.transport_mode = 2
        and t2.id&lt;&gt;''
        and t1.company_id&lt;&gt;''
        and t1.project_quote>0
        <if test="subjectID != null ">and t2.id = #{subjectID}</if>
        <if test="code != null ">and t1.virtual_order_no =#{code}</if>
        <if test="param.dateType == 1 ">
            and DATE_FORMAT(t1.finish_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.finish_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.dateType == 2 ">
            and DATE_FORMAT(t1.dispatch_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.dispatch_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.companyId!=null">
            and t1.company_id in
            <foreach collection="param.companyId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.schedulingBillCodeList!=null">
            and t1.scheduling_bill_code in
            <foreach collection="param.schedulingBillCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.virtualOrderNoList!=null">
            and t1.virtual_order_no in
            <foreach collection="param.virtualOrderNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.lineCode != null and param.lineCode != ''">and t1.tline_code =#{param.lineCode}</if>
        <if test="param.lineName != null and param.lineName != ''">and t1.tline_name like concat('%', #{param.lineName}, '%')</if>
        <if test="param.deliveryMode != null">and t1.delivery_mode =#{param.deliveryMode}</if>
        <if test="param.transportType != null">and t1.transport_type =#{param.transportType}</if>
        <if test="param.clientName != null and param.clientName != ''">and t3.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.carrierName != null and param.carrierName != ''">and t2.carrier_name like concat('%', #{param.carrierName}, '%')</if>
        GROUP BY t2.id,t1.company_id
    </select>
    <!--    根据承运商id以及机构id 查询应付单据表信息-->
    <select id="selectYfBillCodeInfo"  resultMap="AutoYFCodeInfoResult">
        select t1.id ,t2.id as carrier_id , t1.company_id,t2.accountperi as payment_days,t1.virtual_order_no relate_code,
               t1.scheduling_bill_code,t1.virtual_order_no,t1.project_quote,t1.carrier_code,
               t1.carrier_name,t1.network_code, t1.total_boxes,t1.total_number,t1.total_weight,t1.total_volume,
               t1.cargo_value,t1.dispatch_date,t1.finish_date, t1.transport_type,t1.delivery_mode,t1.line_code,t1.line_name,
               t1.base_stores,t1.base_kilometer,t1.total_kilometer,t1.number_loading_points,t1.number_unloading_points,
               t1.total_votenumber,t1.driver,t1.car_code,t1.car_type,t1.car_model,t1.head_office_times,t1.body_office_times,
               t1.province_origin,t1.originating_City,t1.originating_area,t1.originating_address,
               t1.destination_Province,t1.destination_city,t1.destination_area,t1.destination_address,t1.client_id,
               t1.car_model,line.total_mileage,line.store_num,client.client_code,t1.tline_code,t1.tline_name,
                t1.vehicle_temperature_type,weekday(t1.dispatch_date) dispatchDateWeek,weekday(t1.finish_date) finishDateWeek
                ,IFNULL(t1.finish_date,t1.dispatch_date) as signing_date
        from bms_yfbillcodeinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join bms_routeinfo line on t1.line_code=line.route_code and line.del_flag=0
        left join bms_clientinfo client on t1.client_id=client.id
        WHERE t1.del_flag=0
        and t2.del_flag=0
        and t1.cost_status=0
        and t1.project_quote>0
        and t1.transport_mode = 2
        and t2.id = #{carrierId} and t1.company_id = #{companyId}
        <if test="code != null ">and t1.virtual_order_no =#{code}</if>
        <if test="param.schedulingBillCodeList!=null">
            and t1.scheduling_bill_code in
            <foreach collection="param.schedulingBillCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.virtualOrderNoList!=null">
            and t1.virtual_order_no in
            <foreach collection="param.virtualOrderNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.lineCode != null and param.lineCode != ''">and t1.tline_code =#{param.lineCode}</if>
        <if test="param.lineName != null and param.lineName != ''">and t1.tline_name like concat('%', #{param.lineName}, '%')</if>
        <if test="param.deliveryMode != null ">and t1.delivery_mode =#{param.deliveryMode}</if>
        <if test="param.transportType != null ">and t1.transport_type =#{param.transportType}</if>
        <if test="param.clientName != null and param.clientName != ''">and client.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.carrierName != null and param.carrierName != ''">and t2.carrier_name like concat('%', #{param.carrierName}, '%')</if>
        <if test="param.dateType == 1 ">
            and DATE_FORMAT(t1.finish_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.finish_date,'%Y-%m-%d') &lt;= #{param.endDate}
            -- 查询今天之前的数据
            and timestamp(date_add(curdate(), interval - 0 day))>t1.finish_date
        </if>
        <if test="param.dateType == 2 ">
            and DATE_FORMAT(t1.dispatch_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.dispatch_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
    </select>
    <!--    查询应付库存表 需要计费的所有承运商id以及机构id-->
    <select id="selectYfStockInfoCarrierIds"  resultMap="AutoYFCodeInfoResult" >
        select t1.id ,t2.id as carrier_id , t1.company_id
        from bms_yfstockinfo t1
                 left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join bms_clientinfo t3 on t1.client_id=t3.id
        left join mdm_warehouseinfo t4 on t1.warehouse_code=t4.warehouse_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0 and t2.id&lt;&gt;'' and t1.company_id&lt;&gt;''
        <if test="subjectID != null ">and t2.id = #{subjectID}</if>
        <if test="code != null ">and t1.stock_code =#{code}</if>
        <if test="param.companyId!=null">
            and t1.company_id in
            <foreach collection="param.companyId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.relateCodeList!=null">
            and t1.stock_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &gt;= #{param.startDate}
        and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &lt;= #{param.endDate}
        <if test="param.clientName != null and param.clientName != ''">and t3.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.carrierName != null and param.carrierName != ''">and t1.carrier_name like concat('%', #{param.carrierName}, '%')</if>
        <if test="param.warehouseName != null and param.warehouseName != ''">and t4.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
        GROUP BY t2.id, t1.company_id
    </select>
    <!--    根据承运商id以及机构id 查询应付库存表-->
    <select id="selectYfStockInfo"  resultMap="AutoYFCodeInfoResult">
        select t1.id ,t2.id as carrier_id , t1.company_id,t2.accountperi as payment_days,
               t1.stock_code as relate_code,t1.carrier_code,t1.carrier_name,t1.warehouse_code,
        t1.instorage_time as finish_date,t1.instorage_time as dispatch_date,t1.instorage_time as signing_date,t1.total_boxes,t1.odd_boxes,t1.box_type,t1.temperature_type,
               t1.trust,t1.warehouse_area,t1.weight as total_weight,t1.volume as total_volume,
               t1.warehouse_code,t3.warehouse_name,t1.client_id,t1.aqty,t1.pallet_number,t1.client_id,client.client_code,weekday(t1.instorage_time) dispatchDateWeek,weekday(t1.instorage_time) finishDateWeek
        from bms_yfstockinfo t1
                 left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
                 left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        left join bms_clientinfo client on t1.client_id=client.id
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0
          and t2.id = #{carrierId} and t1.company_id = #{companyId}
        <if test="code != null ">and t1.stock_code =#{code}</if>
        and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &gt;= #{param.startDate}
        and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &lt;= #{param.endDate}
        <if test="param.clientName != null and param.clientName != ''">and client.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.carrierName != null and param.carrierName != ''">and t2.carrier_name like concat('%', #{param.carrierName}, '%')</if>
        <if test="param.warehouseName != null and param.warehouseName != ''">and t3.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
        -- 查询今天之前的数据
        and timestamp(date_add(curdate(), interval - 0 day))>t1.instorage_time
    </select>

    <!--    查询应付出入库单表 需要计费的所有承运商id以及机构id-->
    <select id="selectYfStockCodeInfoCarrierIds"  resultMap="AutoYFCodeInfoResult" >
        select t1.id ,t2.id as carrier_id , t1.company_id
        from bms_yfstock_codeinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join bms_clientinfo t3 on t1.client_id=t3.id
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0 and t2.id&lt;&gt;'' and t1.company_id&lt;&gt;'' and t1.code_type = #{codeType}
        <if test="subjectID != null ">and t2.id = #{subjectID}</if>
        <if test="code != null ">and t1.relate_code =#{code}</if>
        <if test="param.dateType == 1 ">
            and DATE_FORMAT(t1.stockoper_time,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.stockoper_time,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.dateType == 2 ">
            and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.companyId!=null">
            and t1.company_id in
            <foreach collection="param.companyId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.relateCodeList!=null">
            and t1.relate_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.clientName != null and param.clientName != ''">and t3.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.carrierName != null and param.carrierName != ''">and t2.carrier_name like concat('%', #{param.carrierName}, '%')</if>
        <if test="param.warehouseName != null and param.warehouseName != ''">and t1.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
        GROUP BY t2.id,t1.company_id
    </select>
    <!--    根据承运商id以及机构id 查询应付出入库单表-->
    <select id="selectYfStockCodeInfo"  resultMap="AutoYFCodeInfoResult">
        select t1.id ,t2.id as carrier_id , t1.company_id,t2.accountperi as payment_days,
               t1.code_type,t1.relate_code ,t1.carrier_code,t1.carrier_name,t1.warehouse_code,t1.warehouse_code,t1.warehouse_name,
               t1.stockoper_time as finish_date,t1.stockoper_time as dispatch_date,t1.stockoper_time as signing_date,t1.total_boxes,
               t1.pallet_number as trust,t1.warehouse_area,t1.weight as total_weight,t1.volume as total_volume,t1.client_id,
               t1.transport_type as transportTypeStr,
               t1.split_total_number,t1.sku_number,t1.if_autarky,t1.order_type,t1.client_id,client.client_code,t1.pallet_number
        ,t1.cw_pallet_number,t1.lc_pallet_number,t1.ld_pallet_number,weekday(t1.stockoper_time) dispatchDateWeek,weekday(t1.stockoper_time)
        finishDateWeek,t1.is_timeout as isTimeout
        from bms_yfstock_codeinfo t1
                 left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join bms_clientinfo client on t1.client_id=client.id
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0
          and t2.id = #{carrierId} and t1.company_id = #{companyId} and t1.code_type = #{codeType}
        <if test="code != null ">and t1.relate_code =#{code}</if>
        <if test="param.dateType == 1 ">
            and DATE_FORMAT(t1.stockoper_time,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.stockoper_time,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.dateType == 2 ">
            and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.relateCodeList!=null">
            and t1.relate_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.clientName != null and param.clientName != ''">and client.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.carrierName != null and param.carrierName != ''">and t2.carrier_name like concat('%', #{param.carrierName}, '%')</if>
        <if test="param.warehouseName != null and param.warehouseName != ''">and t1.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
        -- 查询今天之前的数据
        and timestamp(date_add(curdate(), interval - 0 day))>t1.stockoper_time
    </select>





<!--    预估表-->
    <!-- 批量新增应付费用表  bms_yfcost_info_estimate  -->
    <insert id="insertBmsyfcostInfoEstimateBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfcostInfo" >
        INSERT INTO bms_yfcost_info_estimate
        (
        id, expenses_code, business_type,carrier_code, carrier_name,company_id,
        expenses_type, cost_dimension, charge_type,fee_flag,quoterule_id,
        rule_name,remarks,
        freight,Ultrafar_fee,superframes_fee,excess_fee,reduce_fee,delivery_fee,
        outboundsorting_fee,shortbarge_fee,return_fee,exception_fee,
        other_cost1,other_cost2,other_cost3,other_cost4,other_cost5,other_cost6,
        other_cost7,other_cost8,other_cost9,other_cost10,other_cost11,other_cost12,
        oper_code,oper_by,oper_time,del_flag,bill_date,business_time,cost_attribute,
        over_num,over_sendnum,client_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.expensesCode}, #{item.businessType}, #{item.carrierCode},#{item.carrierName}, #{item.companyId},
            #{item.expensesType}, #{item.costDimension}, #{item.chargeType}, #{item.feeFlag}, #{item.quoteruleId},
            #{item.ruleName}, #{item.remarks},
            #{item.freight},#{item.ultrafarFee},#{item.superframesFee},#{item.excessFee},#{item.reduceFee}, #{item.deliveryFee},
            #{item.outboundsortingFee}, #{item.shortbargeFee},#{item.returnFee},#{item.exceptionFee},
            #{item.otherCost1},#{item.otherCost2},#{item.otherCost3},#{item.otherCost4},#{item.otherCost5},#{item.otherCost6},
            #{item.otherCost7},#{item.otherCost8},#{item.otherCost9},#{item.otherCost10},#{item.otherCost11},#{item.otherCost12},
            #{item.operCode}, #{item.operBy}, #{item.operTime}, 0,#{item.billDate},#{item.businessTime},#{item.costAttribute},
            #{item.overNum}, #{item.overSendnum}, #{item.clientId}
            )
        </foreach>
    </insert>
    <!-- 批量作废  bms_yfcost_info_estimate  -->
    <update id="delBmsyfcostInfoEstimate">
        DELETE FROM  bms_yfcost_info_estimate where expenses_type = #{expensesType}
    </update>


    <!-- 批量新增应付费用中间表  bms_yfexpenses_middle_estimate  -->
    <insert id="insertBmsYfexpensesMiddleEstimateBatch" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfexpensesMiddle" >
        INSERT INTO bms_yfexpenses_middle_estimate
        (
        yfbill_id, yfbill_type,expenses_id, oper_by,oper_time, del_flag,expenses_type
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.yfbillId}, #{item.yfbillType},#{item.expensesId}, #{item.operBy}, #{item.operTime}, 0,#{item.expensesType}
            )
        </foreach>
    </insert>
    <!-- 批量作废  bms_yfexpenses_middle_estimate  -->
    <update id="delBmsYfexpensesMiddeEstimate">
        DELETE FROM bms_yfexpenses_middle_estimate where expenses_type = #{expensesType}
    </update>

    <!--查询单据商品-->
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYfjobbillGoodsinfo" id="BmsYfGoodsinfoResult"
               extends="mapper.db.BmsYfjobbillGoodsinfoMapper.BmsYfjobbillGoodsinfoResult">
        <result property="chargingID"    column="chargingID"    />
        <result property="jobbillId"    column="jobbillId"    />
        <result property="contentsNumber"    column="contents_number"    />
        <result property="totalAllBoxes"    column="total_all_boxes"    />
        <result property="jobId"    column="jobId"    />
    </resultMap>
    <!--    查询调度单商品-->
    <select id="selectYfJobGoodsInfo"  resultMap="BmsYfGoodsinfoResult" >
        select  t3.id as chargingID,t2.id as jobbillId,t2.sku_code,t2.sku_name,
                sum(case when t2.odd_boxes>0 then t2.total_boxes+1 else t2.total_boxes end ) total_all_boxes,
                sum(t2.total_boxes) total_boxes,sum(t2.odd_boxes) odd_boxes,
                sum(t2.numbers) numbers,t2.box_type,t2.temperature_type,
                sum(t2.numbers) as contents_number,
                max(t2.weight) weight,max(t2.volume) volume,min(t2.price) price,t2.sku_class,
                sum(t2.total_weight) total_weight,sum(t2.total_volume) total_volume ,t1.id as jobId
        from bms_yfjobbillinfo t1
        left join bms_yfjobbill_goodsinfo t2 on t1.id=t2.jobbill_id
        left join bms_yfbillcodeinfo t3 on t3.virtual_order_no=t1.scheduling_bill_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t3.del_flag=0 and t3.id in
        <foreach item="id" collection="relateID" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY t3.id,t1.id
        <if test="groupType == 1 ">,t2.sku_class</if>
        <if test="groupType == 2 ">,t2.temperature_type</if>
        <if test="groupType == 3 ">,t2.sku_class,t2.temperature_type</if>
        <if test="groupType == 4 ">,t2.id</if>
    </select>
    <!--    查询出入库单商品-->
    <select id="selectYfStockCodeGoodsInfo"  resultMap="BmsYfGoodsinfoResult">
        select  t2.id,t2.yfstock_id as chargingID,t2.sku_code,t2.sku_name,
        sum(case when t2.odd_boxes>0 then t2.total_boxes+1 else t2.total_boxes end ) total_all_boxes,
        sum(t2.total_boxes) total_boxes,sum(t2.odd_boxes) odd_boxes,t2.box_type,t2.temperature_type,
        max(t2.weight) weight,max(t2.volume) volume, min(t2.price) price,t2.sku_class,sum(total_amount) total_amount,
        sum(t2.total_weight) total_weight,sum(t2.total_volume) total_volume,sum(contents_number) contents_number
        from bms_yfstockcode_detailinfo t2
        WHERE t2.del_flag=0 and t2.yfstock_id in
        <foreach item="id" collection="stockIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY t2.yfstock_id
        <if test="groupType == 1 ">,t2.sku_class</if>
        <if test="groupType == 2 ">,t2.temperature_type</if>
        <if test="groupType == 3 ">,t2.sku_class,t2.temperature_type</if>
        <if test="groupType == 3 ">,t2.id</if>
    </select>

    <select id="selectYfAddedServiceOrderChargingInfoClientIds"  resultMap="AutoYFCodeInfoResult" >
        select t2.id as client_id, t1.dept_id as company_id,t3.id as carrier_id
        from bms_addedfee t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join bms_carrierinfo t3 on t3.carrier_code = t1.carrier_code
        WHERE t1.del_flag=0 and t1.settle_type = 2 and t2.del_flag=0 and t1.cost_status=0 and t3.id&lt;&gt;'' and t1.dept_id&lt;&gt;''
        <if test="clientIds != null ">
            and t2.id in
            <foreach item="id" collection="clientIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        and DATE_FORMAT(t1.creat_date ,'%Y-%m-%d') &gt;= #{startDate}
        and DATE_FORMAT(t1.creat_date ,'%Y-%m-%d') &lt;= #{endDate}
        <if test="code != null ">and t1.relate_code =#{code}</if>
        GROUP BY t3.id,t1.dept_id
    </select>

    <!--    根据客户id以及机构id 查询增值表-->
    <select id="selectYFstockInfoValueService"  resultMap="AutoYFCodeInfoResult">
        select t1.id ,t2.id as client_id , t1.dept_id as company_id,t2.payment_days,
        t1.relate_code as relate_code,t1.client_code,t2.client_name,t1.warehouse_code,
        t3.warehouse_name,t3.warehouse_city
        ,t1.item_id
        ,t4.carrier_code
        ,t4.carrier_name
        ,t1.number as total_number
        ,t1.number as pallet_number
        ,t1.number as trust
        ,t1.number as sku_number
        ,t1.fee_belong
        ,t1.unit
        from bms_addedfee t1
        left join bms_clientinfo t2 on t1.client_code=t2.client_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        left join bms_carrierinfo t4 on t4.carrier_code = t1.carrier_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0
        and t4.id = #{carrierId} and t1.dept_id = #{companyId}
        and t1.settle_type = 2
        and DATE_FORMAT(t1.creat_date ,'%Y-%m-%d') &gt;= #{startDate}
        and DATE_FORMAT(t1.creat_date ,'%Y-%m-%d') &lt;= #{endDate}
        <if test="code != null ">and t1.relate_code =#{code}</if>
    </select>

    <!--    库存单查询出库单的总箱数、总件数-->
    <select id="getOutStockInfo"  resultType="com.bbyb.joy.bms.domain.dto.charginglogic.AutoYfCodeInfo" >
        select
        sum(IFNULL(total_boxes,0)) as outTotalBoxes,
        sum(IFNULL(sku_number,0)) as outSkuNumber
        from bms_yfstock_codeinfo bi
        left join bms_carrierinfo carr
        on carr.carrier_code = bi.carrier_code
        and carr.del_flag = 0
        where
        bi.code_type = 1
        and bi.del_flag=0
        <if test="clientId!=null">
            and bi.client_id = #{clientId}
        </if>
        and carr.id = #{carrierId}
        and bi.company_id = #{companyId}
        and DATE_FORMAT(bi.stockoper_time,'%Y-%m') = #{signingDate}
    </select>

<!--    查询原调度单信息-->
    <select id="selectYfBillCodeYddInfo"  resultMap="AutoYFCodeInfoResult">
        select t1.id ,t2.id as carrier_id , t1.company_id,t2.accountperi as payment_days,t1.virtual_order_no relate_code,
        t1.scheduling_bill_code,t1.virtual_order_no,t1.project_quote,t1.carrier_code,
        t1.carrier_name,t1.network_code, t1.total_boxes,t1.total_number,t1.total_weight,t1.total_volume,
        t1.cargo_value,t1.dispatch_date,t1.finish_date, t1.transport_type,t1.delivery_mode,t1.line_code,t1.line_name,
        t1.base_stores,t1.base_kilometer,t1.total_kilometer,t1.number_loading_points,t1.number_unloading_points,
        t1.total_votenumber,t1.driver,t1.car_code,t1.car_type,t1.car_model,t1.head_office_times,t1.body_office_times,
        t1.province_origin,t1.originating_City,t1.originating_area,t1.originating_address,
        t1.destination_Province,t1.destination_city,t1.destination_area,t1.destination_address,t1.client_id,
        t1.car_model,line.total_mileage,line.store_num,client.client_code,t1.tline_code,t1.tline_name,
        t1.vehicle_temperature_type
        from bms_yfbillcodeinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join bms_routeinfo line on t1.line_code=line.route_code and line.del_flag=0
        left join bms_clientinfo client on t1.client_id=client.id
        WHERE
        t1.virtual_order_no =#{code}
        ORDER BY t1.create_time DESC
        limit 1

    </select>

    <select id="selectYfBillCodes" resultMap="AutoYFCodeInfoResult">
        select t1.id ,t2.id as carrier_id, t1.company_id,t2.carrier_code,t2.carrier_name
        from bms_yfbillcodeinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join bms_clientinfo t3 on t1.client_id=t3.id
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0 and t2.id&lt;&gt;'' and t1.company_id&lt;&gt;''
        and t1.project_quote>0
        <if test="subjectID != null ">and t2.id = #{subjectID}</if>
        <if test="code != null ">and t1.virtual_order_no =#{code}</if>
        <if test="param.dateType == 1 ">
            and DATE_FORMAT(t1.finish_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.finish_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.dateType == 2 ">
            and DATE_FORMAT(t1.dispatch_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.dispatch_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.companyId!=null">
            and t1.company_id in
            <foreach collection="param.companyId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.schedulingBillCodeList!=null">
            and t1.scheduling_bill_code in
            <foreach collection="param.schedulingBillCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.virtualOrderNoList!=null">
            and t1.virtual_order_no in
            <foreach collection="param.virtualOrderNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.lineCode != null and param.lineCode != ''">and t1.tline_code =#{param.lineCode}</if>
        <if test="param.lineName != null and param.lineName != ''">and t1.tline_name like concat('%', #{param.lineName}, '%')</if>
        <if test="param.deliveryMode != null">and t1.delivery_mode =#{param.deliveryMode}</if>
        <if test="param.transportType != null">and t1.transport_type =#{param.transportType}</if>
        <if test="param.clientName != null and param.clientName != ''">and t3.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.carrierName != null and param.carrierName != ''">and t2.carrier_name like concat('%', #{param.carrierName}, '%')</if>
    </select>

    <select id="selectYfStockCodeInfos" resultMap="AutoYFCodeInfoResult">
        select t1.id ,t2.id as carrier_id , t1.company_id,t2.carrier_code,t2.carrier_name
        from bms_yfstock_codeinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join bms_clientinfo t3 on t1.client_id=t3.id
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0 and t2.id&lt;&gt;'' and t1.company_id&lt;&gt;'' and t1.code_type = #{codeType}
        <if test="subjectID != null ">and t2.id = #{subjectID}</if>
        <if test="code != null ">and t1.relate_code =#{code}</if>
        <if test="param.dateType == 1 ">
            and DATE_FORMAT(t1.stockoper_time,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.stockoper_time,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.dateType == 2 ">
            and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.companyId!=null">
            and t1.company_id in
            <foreach collection="param.companyId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.relateCodeList!=null">
            and t1.relate_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.clientName != null and param.clientName != ''">and t3.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.carrierName != null and param.carrierName != ''">and t2.carrier_name like concat('%', #{param.carrierName}, '%')</if>
        <if test="param.warehouseName != null and param.warehouseName != ''">and t1.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
    </select>

    <select id="selectYFstockinfos" resultMap="AutoYFCodeInfoResult" >
        select t1.id ,t2.id as carrier_id , t1.company_id,t2.carrier_code,t2.carrier_name
        from bms_yfstockinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join bms_clientinfo t3 on t1.client_id=t3.id
        left join mdm_warehouseinfo t4 on t1.warehouse_code=t4.warehouse_code
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0 and t2.id&lt;&gt;'' and t1.company_id&lt;&gt;''
        <if test="subjectID != null ">and t2.id = #{subjectID}</if>
        <if test="code != null ">and t1.stock_code =#{code}</if>
        <if test="param.companyId!=null">
            and t1.company_id in
            <foreach collection="param.companyId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.relateCodeList!=null">
            and t1.stock_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &gt;= #{param.startDate}
        and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &lt;= #{param.endDate}
        <if test="param.clientName != null and param.clientName != ''">and t3.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.carrierName != null and param.carrierName != ''">and t1.carrier_name like concat('%', #{param.carrierName}, '%')</if>
        <if test="param.warehouseName != null and param.warehouseName != ''">and t4.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
    </select>

    <select id="selectYfBillCodeInfoForAfterCurDay" resultMap="AutoYFCodeInfoResult">
        select t1.id ,t2.id as carrier_id , t1.company_id,t2.accountperi as payment_days,t1.virtual_order_no relate_code,
        t1.scheduling_bill_code,t1.virtual_order_no,t1.project_quote,t1.carrier_code,
        t1.carrier_name,t1.network_code, t1.total_boxes,t1.total_number,t1.total_weight,t1.total_volume,
        t1.cargo_value,t1.dispatch_date,t1.finish_date, t1.transport_type,t1.delivery_mode,t1.line_code,t1.line_name,
        t1.base_stores,t1.base_kilometer,t1.total_kilometer,t1.number_loading_points,t1.number_unloading_points,
        t1.total_votenumber,t1.driver,t1.car_code,t1.car_type,t1.car_model,t1.head_office_times,t1.body_office_times,
        t1.province_origin,t1.originating_City,t1.originating_area,t1.originating_address,
        t1.destination_Province,t1.destination_city,t1.destination_area,t1.destination_address,t1.client_id,
        t1.car_model,line.total_mileage,line.store_num,client.client_code,t1.tline_code,t1.tline_name,
        t1.vehicle_temperature_type
        from bms_yfbillcodeinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join bms_routeinfo line on t1.line_code=line.route_code and line.del_flag=0
        left join bms_clientinfo client on t1.client_id=client.id
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0 and t1.project_quote>0
        and t2.id = #{carrierId} and t1.company_id = #{companyId}
        <if test="code != null ">and t1.virtual_order_no =#{code}</if>
        <if test="param.schedulingBillCodeList!=null">
            and t1.scheduling_bill_code in
            <foreach collection="param.schedulingBillCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.virtualOrderNoList!=null">
            and t1.virtual_order_no in
            <foreach collection="param.virtualOrderNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.lineCode != null and param.lineCode != ''">and t1.tline_code =#{param.lineCode}</if>
        <if test="param.lineName != null and param.lineName != ''">and t1.tline_name like concat('%', #{param.lineName}, '%')</if>
        <if test="param.deliveryMode != null ">and t1.delivery_mode =#{param.deliveryMode}</if>
        <if test="param.transportType != null ">and t1.transport_type =#{param.transportType}</if>
        <if test="param.clientName != null and param.clientName != ''">and client.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.carrierName != null and param.carrierName != ''">and t2.carrier_name like concat('%', #{param.carrierName}, '%')</if>
        <if test="param.dateType == 1 ">
            and DATE_FORMAT(t1.finish_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.finish_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.dateType == 2 ">
            and DATE_FORMAT(t1.dispatch_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.dispatch_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        -- 查询今天之前的数据
        and t1.finish_date &gt;= timestamp(date_add(curdate(), interval - 0 day))
    </select>

    <select id="selectYFstockCodeInfoForAfterCurDay" resultMap="AutoYFCodeInfoResult">
        select t1.id ,t2.id as carrier_id , t1.company_id,t2.accountperi as payment_days,
        t1.code_type,t1.relate_code ,t1.carrier_code,t1.carrier_name,t1.warehouse_code,t1.warehouse_code,t1.warehouse_name,
        t1.stockoper_time as finish_date,t1.stockoper_time as dispatch_date,t1.stockoper_time as signing_date,t1.total_boxes,
        t1.pallet_number as trust,t1.warehouse_area,t1.weight as total_weight,t1.volume as total_volume,t1.client_id,
        t1.transport_type as transportTypeStr,
        t1.split_total_number,t1.sku_number,t1.if_autarky,t1.order_type,t1.client_id,client.client_code,t1.pallet_number
        ,t1.cw_pallet_number,t1.lc_pallet_number,t1.ld_pallet_number
        from bms_yfstock_codeinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join bms_clientinfo client on t1.client_id=client.id
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0
        and t2.id = #{carrierId} and t1.company_id = #{companyId} and t1.code_type = #{codeType}
        <if test="code != null ">and t1.relate_code =#{code}</if>
        <if test="param.dateType == 1 ">
            and DATE_FORMAT(t1.stockoper_time,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.stockoper_time,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.dateType == 2 ">
            and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &gt;= #{param.startDate}
            and DATE_FORMAT(t1.order_date,'%Y-%m-%d') &lt;= #{param.endDate}
        </if>
        <if test="param.relateCodeList!=null">
            and t1.relate_code in
            <foreach collection="param.relateCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.clientName != null and param.clientName != ''">and client.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.carrierName != null and param.carrierName != ''">and t2.carrier_name like concat('%', #{param.carrierName}, '%')</if>
        <if test="param.warehouseName != null and param.warehouseName != ''">and t1.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
        -- 查询今天之前的数据
        and t1.stockoper_time &gt;= timestamp(date_add(curdate(), interval - 0 day))
    </select>

    <select id="selectYFstockInfoForAfterCurDay" resultMap="AutoYFCodeInfoResult">
        select t1.id ,t2.id as carrier_id , t1.company_id,t2.accountperi as payment_days,
        t1.stock_code as relate_code,t1.carrier_code,t1.carrier_name,t1.warehouse_code,
        t1.instorage_time as finish_date,t1.instorage_time as dispatch_date,t1.instorage_time as signing_date,t1.total_boxes,t1.odd_boxes,t1.box_type,t1.temperature_type,
        t1.trust,t1.warehouse_area,t1.weight as total_weight,t1.volume as total_volume,
        t1.warehouse_code,t3.warehouse_name,t1.client_id,t1.aqty,t1.pallet_number,t1.client_id,client.client_code
        from bms_yfstockinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join mdm_warehouseinfo t3 on t1.warehouse_code=t3.warehouse_code
        left join bms_clientinfo client on t1.client_id=client.id
        WHERE t1.del_flag=0 and t2.del_flag=0 and t1.cost_status=0
        and t2.id = #{carrierId} and t1.company_id = #{companyId}
        <if test="code != null ">and t1.stock_code =#{code}</if>
        and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &gt;= #{param.startDate}
        and DATE_FORMAT(t1.instorage_time,'%Y-%m-%d') &lt;= #{param.endDate}
        <if test="param.clientName != null and param.clientName != ''">and client.client_name like concat('%', #{param.clientName}, '%')</if>
        <if test="param.carrierName != null and param.carrierName != ''">and t2.carrier_name like concat('%', #{param.carrierName}, '%')</if>
        <if test="param.warehouseName != null and param.warehouseName != ''">and t3.warehouse_name like concat('%', #{param.warehouseName}, '%')</if>
        -- 查询今天之前的数据
        and t1.instorage_time &gt;= timestamp(date_add(curdate(), interval - 0 day))
    </select>

    <select id="selectYFBillCodeYdddInfoSpecial" resultMap="AutoYFCodeInfoResult">
        select
        t1.id,
        t2.id AS carrier_id,
        t1.company_id,
        t2.accountperi AS payment_days,
        t1.virtual_order_no relate_code,
        t1.scheduling_bill_code,
        t1.virtual_order_no,
        t1.project_quote,
        t1.carrier_code,
        t1.carrier_name,
        t1.network_code,
        SUM(t1.total_boxes) AS total_boxes,
        SUM(t1.total_number) AS total_number,
        SUM(t1.total_weight) AS total_weight,
        SUM(t1.total_volume) AS total_volume,
        SUM(t1.cargo_value) AS cargo_value,
        t1.dispatch_date,
        t1.finish_date,
        t1.transport_type,
        t1.delivery_mode,
        t1.line_code,
        t1.line_name,
        t1.base_stores,
        t1.base_kilometer,
        t1.total_kilometer,
        t1.number_loading_points,
        t1.number_unloading_points,
        t1.total_votenumber,
        t1.driver,
        t1.car_code,
        t1.car_type,
        t1.car_model,
        t1.head_office_times,
        t1.body_office_times,
        t1.province_origin,
        t1.originating_City,
        t1.originating_area,
        t1.originating_address,
        t1.destination_Province,
        t1.destination_city,
        t1.destination_area,
        t1.destination_address,
        t1.client_id,
        t1.car_model,
        line.total_mileage,
        line.store_num,
        client.client_code,
        t1.tline_code,
        t1.tline_name,
        t1.vehicle_temperature_type
        from bms_yfbillcodeinfo t1
        left join bms_carrierinfo t2 on t1.carrier_code=t2.carrier_code
        left join bms_routeinfo line on t1.line_code=line.route_code and line.del_flag=0
        left join bms_clientinfo client on t1.client_id=client.id
        WHERE t1.del_flag=0
        AND t1.scheduling_bill_code =#{code}
        GROUP BY t1.scheduling_bill_code
    </select>


    <!--    查询浮动规则信息-->
    <select id="getDoubleRatio"  resultType="com.bbyb.joy.bms.domain.dto.PubFloatfeeRuleInfo" >
        SELECT
        main.rule_type as ruleType,
        a.start_hour as startHour,
        a.end_hour as endHour,
        main.rule_name as ruleName,
        DATE_FORMAT(a.start_date,
        '%Y-%m-%d %H:%i:%s') as startDate,
        DATE_FORMAT(a.end_date,
        '%Y-%m-%d %H:%i:%s') as endDate,
        MAX(main.ratio/100) as ratio,
        mw.warehouse_code as warehouseCode,
        mw.id as warehouseId,
        mc.carrier_code as carrierCode,
        mc.id as carrierId,
        <!--        c.subject_id as subjectId,-->
        d.item_code as subjectId,
        d.item_name as itemName
        ,df.item_code
        from pub_yf_floatfee_rule_main main
        left join pub_yf_floatfee_rule a
        on main.id=a.rule_main_id
        and a.del_flag=0
        left join pub_yf_floatfee_rule_carrier b
        on main.id = b.rule_id and b.del_flag=0
        left join  mdm_warehouseinfo mw
        on b.warehouse_code=mw.warehouse_code
        left join pub_yf_floatfee_rule_feetype c
        on c.rule_id = main.id and c.del_flag=0
        left join pub_fee_subject d
        on d.id = c.subject_id
        left join bms_carrierinfo mc
        on mc.id = b.carrier_id
        and mc.del_flag =0
        left join pub_fee_subject df
        on df.id = d.father_id
        and df.del_flag = 0
        where main.enabled = 0
        and main.del_flag=0
        and IFNULL(c.subject_id,'')!=''
        and IFNULL(mc.id,'')!=''
        <if test="instorageTime!=null">
            and a.start_date &lt;= #{instorageTime}
            and a.end_date &gt;= #{instorageTime}
        </if>
        <if test="carrierId!=null">
            and IFNULL(mc.id,'')=#{carrierId}
        </if>
        <if test="feeType!=null">
            and c.subject_id=#{feeType}
        </if>
        <if test="operType!=null">
            and df.item_code= concat('00', #{operType},'')
        </if>
        <if test="orderType!=null">
            and df.order_type= #{orderType}
        </if>
        GROUP BY
        a.id,
        mc.id,
        df.item_code,
        d.item_code,
        c.subject_id;

    </select>


    <resultMap id="BaseResultMap2" type="com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto">
        <!--@mbg.generated-->
        <!--@Table bms_yfbillcodeinfo-->
        <id column="pk_id" property="pkId" />
        <result column="id" property="id" />
        <result column="scheduling_bill_code" property="schedulingBillCode" />
        <result column="virtual_order_no" property="virtualOrderNo" />
        <result column="hy_order_no" property="hyOrderNo" />
        <result column="project_quote" property="projectQuote" />
        <result column="carrier_code" property="carrierCode" />
        <result column="carrier_name" property="carrierName" />
        <result column="company_id" property="companyId" />
        <result column="network_code" property="networkCode" />
        <result column="total_boxes" property="totalBoxes" />
        <result column="total_number" property="totalNumber" />
        <result column="total_weight" property="totalWeight" />
        <result column="total_volume" property="totalVolume" />
        <result column="cargo_value" property="cargoValue" />
        <result column="dispatch_date" property="dispatchDate" />
        <result column="finish_date" property="finishDate" />
        <result column="transport_type" property="transportType" />
        <result column="delivery_mode" property="deliveryMode" />
        <result column="line_code" property="lineCode" />
        <result column="line_name" property="lineName" />
        <result column="base_stores" property="baseStores" />
        <result column="base_kilometer" property="baseKilometer" />
        <result column="total_kilometer" property="totalKilometer" />
        <result column="number_loading_points" property="numberLoadingPoints" />
        <result column="number_unloading_points" property="numberUnloadingPoints" />
        <result column="total_votenumber" property="totalVotenumber" />
        <result column="driver" property="driver" />
        <result column="car_code" property="carCode" />
        <result column="car_type" property="carType" />
        <result column="car_model" property="carModel" />
        <result column="head_office_times" property="headOfficeTimes" />
        <result column="province_origin" property="provinceOrigin" />
        <result column="originating_city" property="originatingCity" />
        <result column="originating_area" property="originatingArea" />
        <result column="originating_address" property="originatingAddress" />
        <result column="destination_province" property="destinationProvince" />
        <result column="destination_city" property="destinationCity" />
        <result column="destination_area" property="destinationArea" />
        <result column="destination_address" property="destinationAddress" />
        <result column="cost_status" property="costStatus" />
        <result column="billing_status" property="billingStatus" />
        <result column="create_code" property="createCode" />
        <result column="create_by" property="createBy" />
        <result column="create_dept_id" property="createDeptId" />
        <result column="create_time" property="createTime" />
        <result column="oper_code" property="operCode" />
        <result column="oper_by" property="operBy" />
        <result column="oper_dept_id" property="operDeptId" />
        <result column="oper_time" property="operTime" />
        <result column="del_flag" property="delFlag" />
        <result column="client_id" property="clientId" />
        <result column="body_office_times" property="bodyOfficeTimes" />
        <result column="tline_code" property="tlineCode" />
        <result column="tline_name" property="tlineName" />
        <result column="vehicle_temperature_type" property="vehicleTemperatureType" />
        <result column="order_no" property="orderNo" />
        <result column="fail_remark" property="failRemark" />
        <result column="order_source" property="orderSource" />
        <result column="store_number" property="storeNumber" />
    </resultMap>
    <sql id="Base_Column_List_FOR_YDDD">
        <!--@mbg.generated-->
        pk_id, id, scheduling_bill_code, virtual_order_no, hy_order_no, project_quote, carrier_code,
        carrier_name, company_id, network_code, total_boxes, total_number, total_weight,
        total_volume, cargo_value, dispatch_date, finish_date, transport_type, delivery_mode,
        line_code, line_name, base_stores, base_kilometer, total_kilometer, number_loading_points,
        number_unloading_points, total_votenumber, driver, car_code, car_type, car_model,
        head_office_times, province_origin, originating_city, originating_area, originating_address,
        destination_province, destination_city, destination_area, destination_address, cost_status,
        billing_status, create_code, create_by, create_dept_id, create_time, oper_code, oper_by,
        oper_dept_id, oper_time, del_flag, client_id, body_office_times, tline_code, tline_name,
        vehicle_temperature_type, order_no, fail_remark, order_source, store_number
    </sql>



    <select id="selectYfbillCodeInfoByYDdd" resultMap="BaseResultMap2">
        SELECT
        <include refid="Base_Column_List_FOR_YDDD" />
        FROM bms_yfbillcodeinfo
        WHERE del_flag=0
        AND scheduling_bill_code = #{schedulingBillCode}
    </select>



    <select id="selYfTransCodeInfoByAuto" resultMap="AutoYFCodeInfoResult">
        SELECT
            id AS id,
            id AS yfbillId,
            scheduling_bill_code,
            virtual_order_no,
            total_weight,
            total_volume,
            total_number,
            total_boxes
        FROM bms_yfbillcodeinfo
        WHERE del_flag = '0'
        <if test="ids != null and ids.size()>0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="relateCodes!=null and relateCodes.size()>0">
            AND virtual_order_no IN
            <foreach collection="relateCodes" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
    </select>


    <select id="selYfJobsCodeInfoByAuto" resultMap="AutoYFCodeInfoResult">
        SELECT
            id AS id,
            relate_code,
            scheduling_bill_code,
            total_weight,
            total_volume,
            total_number,
            total_boxes
        FROM bms_yfjobbillinfo
        WHERE del_flag = '0'
        <if test="ids!=null and ids.size()>0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="relateCodes!=null and relateCodes.size()>0">
            AND scheduling_bill_code IN
            <foreach collection="relateCodes" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
    </select>



    <select id="selYfInOutCodeInfoByAuto">
        SELECT
            id AS id,
            relate_code AS relateCode,
            weight AS totalWeight,
            volume AS totalVolume,
            sku_number AS totalNumber,
            total_boxes AS totalBoxes
        FROM bms_yfstock_codeinfo
        WHERE del_flag = '0'
        <if test="ids!=null and ids.size()>0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="relateCodes!=null and relateCodes.size()>0">
            AND relate_code IN
            <foreach collection="relateCodes" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
    </select>


    <select id="selYfStockCodeInfoByAuto">
        SELECT
            id AS id,
            stock_code AS relateCode,
            weight AS totalWeight,
            volume AS totalVolume,
            trust AS totalNumber,
            total_boxes AS totalBoxes
        FROM bms_yfstockinfo
        WHERE del_flag = '0'
        <if test="ids!=null and ids.size()>0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="relateCodes!=null and relateCodes.size()>0">
            AND relate_code IN
            <foreach collection="relateCodes" item="relateCode" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
    </select>







</mapper>
