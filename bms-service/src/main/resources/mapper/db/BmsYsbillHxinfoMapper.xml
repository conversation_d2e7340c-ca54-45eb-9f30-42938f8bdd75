<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYsbillHxinfoMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYsbillHxinfo" id="BmsYsbillHxinfoResult">
        <result property="id"    column="id"    />
        <result property="billCodes"    column="bill_codes"    />
        <result property="collectionId"    column="collection_id"    />
        <result property="payment"    column="payment"    />
        <result property="paymentTime"    column="payment_time"    />
        <result property="remark"    column="remark"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="hxMode"    column="hx_mode"    />
        <result property="writeoffSubject"    column="writeoff_subject"    />
    </resultMap>

    <sql id="selectBmsYsbillHxinfoVo">
        select id, bill_codes, collection_id, payment, payment_time, remark, create_code, create_by, create_time, create_dept_id, oper_dept_id, oper_code, oper_by, oper_time, del_flag, hx_mode, writeoff_subject from bms_ysbill_hxinfo
    </sql>

    <select id="selectBmsYsbillHxinfoList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsbillHxinfo" resultMap="BmsYsbillHxinfoResult">
        <include refid="selectBmsYsbillHxinfoVo"/>
        <where>  
            <if test="billCodes != null  and billCodes != ''"> and bill_codes = #{billCodes}</if>
            <if test="collectionId != null "> and collection_id = #{collectionId}</if>
            <if test="payment != null "> and payment = #{payment}</if>
            <if test="paymentTime != null "> and payment_time = #{paymentTime}</if>
            <if test="createCode != null  and createCode != ''"> and create_code = #{createCode}</if>
            <if test="createDeptId != null "> and create_dept_id = #{createDeptId}</if>
            <if test="operDeptId != null "> and oper_dept_id = #{operDeptId}</if>
            <if test="operCode != null  and operCode != ''"> and oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''"> and oper_by = #{operBy}</if>
            <if test="operTime != null "> and oper_time = #{operTime}</if>
            <if test="hxMode != null  and hxMode != ''"> and hx_mode = #{hxMode}</if>
            <if test="writeoffSubject != null  and writeoffSubject != ''"> and writeoff_subject = #{writeoffSubject}</if>
        </where>
    </select>
    
    <select id="selectBmsYsbillHxinfoById" parameterType="java.lang.String" resultMap="BmsYsbillHxinfoResult">
        <include refid="selectBmsYsbillHxinfoVo"/>
        where id = #{id}
    </select>

    <select id="selectYsbillHx" parameterType="java.util.Map" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillHxDto">
        select
        y.id
        , y.bill_type billType
        , y.bill_code billCode
        , y.bill_name billName
        , y.bill_date billDate
        , y.company_id companyId
        , y.client_id clientId
        , ifnull(y.ys_amount,0) ysAmount
        , y.ticket_state ticketState
        , ifnull(y.ticket_amount,0) ticketAmount
        , ifnull(y.hx_amount,0) hxAmount
        , ifnull(ifnull(y.ys_amount,0)-ifnull(y.hx_amount,0),0)  nohxAmount
        , ifnull(y.hx_num,0) hxNum
        , y.hx_state hxState
        , y.bill_remark billRemark
         from bms_ysbillmain y
        left join bms_clientinfo c on y.client_id=c.id
         where y.del_flag=0 and y.fatherid is null and y.audit_state=2
        <if test="clientIds != null and clientIds.size>0 ">
            AND y.client_id in
            <foreach collection="clientIds" item="clientIds" open="(" separator="," close=")">
                #{clientIds}
            </foreach>
        </if>
        <if test="createTimeStart != null and createTimeStart != '' and createTimeEnd != null and createTimeEnd!=''">
            AND y.create_time &gt;= #{createTimeStart,jdbcType=VARCHAR} AND y.create_time &lt;= #{createTimeEnd,jdbcType=VARCHAR}
        </if>
        <if test="clientId != null and clientId != ''">
            AND y.client_id = #{clientId}
        </if>
        <if test="ticketState != null and ticketState != ''">
            AND y.ticket_state = #{ticketState}
        </if>
        <if test="billType != null">
            AND y.bill_type = #{billType}
        </if>
        <if test="companyIds != null and companyIds.size>0 ">
            AND y.company_id in
            <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">
                #{companyIds}
            </foreach>
        </if>
        <if test="billCode != null and billCode != ''">
            AND y.bill_code like concat('%',trim(#{billCode}),'%')
        </if>
        <if test="clientName != null and clientName != ''">
            AND c.client_name like concat('%',trim(#{clientName}),'%')
        </if>
        <if test="hxState != null and hxState != ''">
            AND y.hx_state = #{hxState}
        </if>
        <if test="billIds != null and billIds.size>0 ">
            AND y.id in
            <foreach collection="billIds" item="billIds" open="(" separator="," close=")">
                #{billIds}
            </foreach>
        </if>

        order by y.hx_time desc
    </select>

    <select id="selectCollectionRecordByIds"  parameterType="java.util.Map"  resultType="com.bbyb.joy.bms.domain.dto.dto.BmsCollectionRecordDto">
        select
        id
        ,collection_code collectionCode
        ,client_id clientId
        ,client_name clientName
        ,ifnull(collection_amount,0) collectionAmount
        ,ifnull(available_amount,0)  availableAmount
        ,ifnull(collection_amount-available_amount,0) userAmount
        from bms_collection_record
        where del_flag=0 and ifnull(available_amount,0)>0
          and id in
        <foreach collection="recordIds" item="recordIds" open="(" separator="," close=")">
            #{recordIds}
        </foreach>

        order by create_time desc
    </select>

    <select id="selectCollectionRecord" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsCollectionRecordDto">
    select
    id
    ,collection_code collectionCode
    ,client_id clientId
    ,client_name clientName
    ,ifnull(collection_amount,0) collectionAmount
    ,ifnull(available_amount,0)  availableAmount
    ,ifnull(collection_amount-available_amount,0) userAmount
    from bms_collection_record
    where del_flag=0 and ifnull(available_amount,0)>0
    <if test="clientName != null and clientName.size>0 ">
        AND (client_name in
        <foreach collection="clientName" item="clientName" open="(" separator="," close=")">
            #{clientName}
        </foreach>)
    </if>
<!--    <if test="companyIds != null and companyIds.size>0 ">-->
<!--        AND create_dept_id in-->
<!--        <foreach collection="companyIds" item="companyIds" open="(" separator="," close=")">-->
<!--            #{companyIds}-->
<!--        </foreach>-->
<!--    </if>-->
    order by create_time desc
    </select>

    <select id="hxDetailList" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillHxDetailDto">
        select
        y.bill_code billCode
        ,y.id billId
        ,yh.id hxId
        ,yh.bill_codes hxBillCodes
        ,yh.payment hxPayment
        ,case when yh.bill_codes like '%,%' then y.ys_amount else yh.payment end BillPayment
        ,yh.payment_time paymentTime
        ,yh.remark
        ,yh.create_by createBy
        ,yh.create_dept_id createDeptId
        ,yh.create_time createTimes
        ,case when y.client_id=yrm.client_id then 1 else 2 end clientState
        ,c.client_code clientCode
        ,c.client_name clientName
        ,GROUP_CONCAT(a.attachment_name)  attachmentName
        ,GROUP_CONCAT(a.attachment_path)  attachmentPath
        from bms_ysbillmain y left join  bms_ysbill_hxinfo yh on yh.bill_codes like CONCAT('%',y.bill_code,'%') and yh.del_flag=0
        left join bms_yshx_record_middle yrm on yrm.hx_id=yh.id and yrm.del_flag =0 and yrm.pay_type =1
        left join bms_clientinfo c on y.client_id=c.id
        left join pub_attachmentiinfo a on yh.id = a.relation_ide and a.del_flag =0 and a.attachment_type=4
        where y.del_flag=0 and yh.id is not null
        <if test="billCode != null and billCode.size>0 ">
            AND y.bill_code in
            <foreach collection="billCode" item="billCode" open="(" separator="," close=")">
                #{billCode}
            </foreach>
        </if>
        group by yh.id
        order by yh.create_time desc
    </select>

    <insert id="insertBmsYsbillHxinfo" parameterType="java.util.Map" useGeneratedKeys="true" keyProperty="id">
        insert into bms_ysbill_hxinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billCodes != null">bill_codes,</if>
            <if test="payment != null">payment,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="hxMode != null">hx_mode,</if>
            <if test="writeoffSubject != null">writeoff_subject,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billCodes != null">#{billCodes},</if>
            <if test="payment != null">#{payment},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="hxMode != null">#{hxMode},</if>
            <if test="writeoffSubject != null">#{writeoffSubject},</if>
        </trim>
    </insert>
    <update id="updateBmsYsbillHxinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsbillHxinfo">
        update bms_ysbill_hxinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="billCodes != null">bill_codes = #{billCodes},</if>
            <if test="payment != null">payment = #{payment},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="hxMode != null">hx_mode = #{hxMode},</if>
            <if test="writeoffSubject != null">writeoff_subject = #{writeoffSubject},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectBmsCollectionRecordList" resultType="com.bbyb.joy.bms.domain.dto.settlement.BmsCollectionRecord">
        select id,
        create_dept_id companyId,
        client_id clientId,
        client_name clientName,
        collection_code collectionCode,
        pay_type payType,
        bank_name bankName,
        collection_time collectionTime,
        collection_amount collectionAmount,
        available_amount availableAmount,
        remark
        from bms_collection_record where 1=1
        <if test="ids != null and ids.size>0 ">
            AND id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
    </select>
</mapper>