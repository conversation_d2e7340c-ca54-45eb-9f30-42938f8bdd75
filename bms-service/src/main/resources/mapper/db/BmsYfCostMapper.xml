<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfCostMapper">


    <update id="flushCostPkId">
        UPDATE bms_yfcost_info t1
        INNER JOIN bms_yfcost_main_info t2 ON t2.id = t1.main_code_id AND t2.del_flag = 0
            SET t1.main_pk_id = t2.pk_id
        WHERE t1.del_flag = 0
        AND t1.main_code_id IN
        <foreach item="id" collection="mainIds" open="(" separator="," close=")">
            #{id}
        </foreach>;
        UPDATE bms_yfexpenses_middle t1
        INNER JOIN bms_yfcost_main_info t2 ON t2.id = t1.main_code_id AND t2.del_flag = 0
            SET t1.main_pk_id = t2.pk_id
        WHERE t1.del_flag = 0
        AND t1.main_code_id IN
        <foreach item="id" collection="mainIds" open="(" separator="," close=")">
            #{id}
        </foreach>;
        UPDATE bms_yfexpenses_middle_share t1
        INNER JOIN bms_yfcost_main_info t2 ON t2.id = t1.main_code_id AND t2.del_flag = 0
            SET t1.main_pk_id = t2.pk_id
        WHERE t1.del_flag = 0
        AND t1.main_code_id IN
        <foreach item="id" collection="mainIds" open="(" separator="," close=")">
            #{id}
        </foreach>;
        <if test="codeType != null and codeType == 1">
            UPDATE bms_yfexpenses_middle t1
            INNER JOIN bms_dispatch_code_info t2 ON t2.id = t1.code_id AND t2.del_flag = 0
            SET t1.code_pk_id = t2.pk_id,
                t1.total_weight = t2.total_weight,
                t1.total_volume = t2.total_volume,
            WHERE t1.del_flag = 0 AND t1.code_type = 1
            AND t1.main_code_id IN
            <foreach item="id" collection="mainIds" open="(" separator="," close=")">
                #{id}
            </foreach>;
            UPDATE bms_yfexpenses_middle_share t1
            INNER JOIN bms_job_code_info t2 ON t2.id = t1.code_id AND t2.del_flag = 0
            SET t1.code_pk_id = t2.pk_id,
                t1.code_main_id = t2.main_code_id,
                t1.code_main_pk_id = t2.main_pk_id,
                t1.total_weight = t2.total_weight,
                t1.total_volume = t2.total_volume
            WHERE t1.del_flag = 0
            AND t1.main_code_id IN
            <foreach item="id" collection="mainIds" open="(" separator="," close=")">
                #{id}
            </foreach>;
        </if>
        <if test="codeType != null and codeType == 2">
            UPDATE bms_yfexpenses_middle t1
            INNER JOIN bms_storage_code_info t2 ON t2.id = t1.code_id AND t2.del_flag = 0
            SET t1.code_pk_id = t2.pk_id,
                t1.total_weight = t2.total_weight,
                t1.total_volume = t2.total_volume
            WHERE t1.del_flag = 0 AND t1.code_type = 2
            AND t1.main_code_id IN
            <foreach item="id" collection="mainIds" open="(" separator="," close=")">
                #{id}
            </foreach>;
        </if>
    </update>


    <select id="queryCost" resultType="com.bbyb.joy.bms.cost.domain.dto.BmsYfCostInfoDto">
        SELECT
            t1.id as id,
            t1.pk_id as pkId,
            t1.expenses_code as expensesCode,
            t2.expenses_code as mainExpensesCode,
            t1.main_code_id as mainCodeId,
            t1.main_pk_id as mainPkId,
            t1.expenses_type as expensesType,
            t1.expenses_dimension as expensesDimension,
            t1.expenses_mark as expensesMark,
            t1.expenses_attribute as expensesAttribute,
            t1.fee_type_first as feeTypeFirst,
            t1.is_increment as isIncrement,
            t1.charge_type as chargeType,
            t1.business_time as businessTime,
            t1.dispatch_date as dispatchDate,
            t1.finish_date as finishDate,
            t1.carrier_id as carrierId,
            t1.client_id as clientId,
            t1.company_id as companyId,
            t1.bill_id as billId,
            t1.bill_date as billDate,
            t1.show_bill_id as showBillId,
            t1.show_bill_code as showBillCode,
            t1.quoter_rule_id as quoterRuleId,
            t1.quoter_rule_name as quoterRuleName,
            t1.quoter_rule_detail_id as quoterRuleDetailId,
            t1.quoter_rule_detail_name as quoterRuleDetailName,
            t1.total_code_number as totalCodeNumber,
            t1.total_sku_number as totalSkuNumber,
            t1.total_boxes as totalBoxes,
            t1.total_number as totalNumber,
            t1.total_weight as totalWeight,
            t1.total_volume as totalVolume,
            t1.total_cargo_value as totalCargoValue,
            t1.total_extra_fee_amount as totalExtraFeeAmount,
            t1.total_extra_fee_number as totalExtraFeeNumber,
            t1.total_extra_fee_price as totalExtraFeePrice,
            t1.extra_fee_remark as extraFeeRemark,
            t1.total_over_num as totalOverNum,
            t1.total_over_store_num as totalOverStoreNum,
            t1.extra_field1 as extraField1,
            t1.settle_type as settleType,
            t1.settle_entity_type as settleEntityType,
            t1.settle_setting as settleSetting,
            t1.settle_main_id as settleMainId,
            t1.settle_amount as settleAmount,
            t1.freight as freight,
            t1.delivery_fee as deliveryFee,
            t1.ultrafar_fee as ultrafarFee,
            t1.superframes_fee as superframesFee,
            t1.excess_fee as excessFee,
            t1.reduce_fee as reduceFee,
            t1.outboundsorting_fee as outboundsortingFee,
            t1.shortbarge_fee as shortbargeFee,
            t1.return_fee as returnFee,
            t1.exception_fee as exceptionFee,
            t1.adjust_fee as adjustFee,
            t1.adjust_remark as adjustRemark,
            t1.other_cost1 as otherCost1,
            t1.other_cost2 as otherCost2,
            t1.other_cost3 as otherCost3,
            t1.other_cost4 as otherCost4,
            t1.other_cost5 as otherCost5,
            t1.other_cost6 as otherCost6,
            t1.other_cost7 as otherCost7,
            t1.other_cost8 as otherCost8,
            t1.other_cost9 as otherCost9,
            t1.other_cost10 as otherCost10,
            t1.other_cost11 as otherCost11,
            t1.other_cost12 as otherCost12,
            t1.remarks as remarks,
            t1.create_code as createCode,
            t1.create_by as createBy,
            t1.create_time as createTime,
            t1.oper_code as operCode,
            t1.oper_by as operBy,
            t1.oper_time as operTime,
            t1.del_flag as delFlag,
            t1.opt_month as optMonth,
            t1.opt_day as optDay
        FROM bms_yfcost_info t1
        LEFT JOIN bms_yfcost_main_info t2 ON t2.pk_id = t1.main_pk_id AND t2.del_flag = 0
        LEFT JOIN bms_yfexpenses_middle t3 ON t3.main_pk_id = t1.main_pk_id AND t3.del_flag = 0
        WHERE t1.del_flag = 0
        <if test="startDate!=null and startDate!='' and endDate!=null and endDate!='' ">
            <choose>
                <when test="dateType != null and dateType == 2">
                    AND t1.dispatch_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 1">
                    AND t1.finish_date BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType != null and dateType == 3">
                    AND t1.oper_time BETWEEN #{startDate} AND #{endDate}
                </when>
                <otherwise>
                    AND t1.oper_time BETWEEN #{startDate} AND #{endDate}
                </otherwise>
            </choose>
        </if>
        <if test="companyIds != null and companyIds.size>0">
            AND t1.company_id IN
            <foreach item="companyId" collection="companyIds" open="(" separator="," close=")">
                #{companyId}
            </foreach>
        </if>
        <if test="carrierId != null">
            AND t1.carrier_id = #{carrierId}
        </if>
        <if test="carrierIds != null  and carrierIds.size > 0 ">
            AND t1.carrier_id IN
            <foreach collection="carrierIds" item="carrierId" open="(" separator="," close=")">
                #{carrierId}
            </foreach>
        </if>
        <if test="clientId != null">
            AND t1.client_id = #{clientId}
        </if>
        <if test="clientIds != null and clientIds.size>0">
            AND t1.client_id IN
            <foreach item="clientId" collection="clientIds" open="(" separator="," close=")">
                #{clientId}
            </foreach>
        </if>
        <if test="settleTypes != null and settleTypes.size>0">
            AND t1.settle_type IN
            <foreach item="settleType" collection="settleTypes" open="(" separator="," close=")">
                #{settleType}
            </foreach>
        </if>
        <if test="expensesCode != null and expensesCode!=''">
            AND t1.expenses_code = #{expensesCode}
        </if>
        <if test="mainExpensesCode != null and mainExpensesCode!=''">
            AND t2.expenses_code = #{mainExpensesCode}
        </if>
        <if test="billCode != null and billCode != ''">
            AND t1.show_bill_code = #{billCode}
        </if>
        <if test="relateCode !=null and relateCode != ''">
            AND t3.relate_code = #{relateCode}
        </if>
        <if test="codeId != null and codeId!=''">
            AND t3.code_id = #{codeId}
        </if>
        <if test="codeIds !=null and codeIds.size>0">
            AND t3.code_id IN
            <foreach item="codeId" collection="codeIds" open="(" separator="," close=")">
                #{codeId}
            </foreach>
        </if>
        <if test="codePkId != null">
            AND t3.code_pk_id = #{codePkId}
        </if>
        <if test="codePkIds !=null and codePkIds.size>0">
            AND t3.code_pk_id IN
            <foreach item="codePkId" collection="codePkIds" open="(" separator="," close=")">
                #{codePkId}
            </foreach>
        </if>
        <if test="relateCodes !=null and relateCodes.size>0">
            AND t3.relate_code IN
            <foreach item="relateCode" collection="relateCodes" open="(" separator="," close=")">
                #{relateCode}
            </foreach>
        </if>
        <if test="expensesDimensions != null and expensesDimensions.size>0">
            AND t1.expenses_dimension IN
            <foreach item="expensesDimension" collection="expensesDimensions" open="(" separator="," close=")">
                #{expensesDimension}
            </foreach>
        </if>
        <if test="expensesTypes != null and expensesTypes.size>0">
            AND t1.expenses_type IN
            <foreach item="expensesType" collection="expensesTypes" open="(" separator="," close=")">
                #{expensesType}
            </foreach>
        </if>
        <if test="expensesMark != null">
            AND t1.expenses_mark = #{expensesMark}
        </if>
        <if test="pkIds != null and pkIds.size>0">
            AND t1.pk_id IN
            <foreach item="pkId" collection="pkIds" open="(" separator="," close=")">
                #{pkId}
            </foreach>
        </if>
        <if test="ids != null and ids.size>0">
            AND t1.id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="chargeType != null">
            AND t1.charge_type = #{chargeType}
        </if>
        <if test="createBy != null and createBy != '' ">
            AND t1.create_by = #{createBy}
        </if>
        <if test="billingStatus != null and billingStatus == 0 ">
            AND t1.bill_id IS NULL
        </if>
        <if test="billingStatus != null and billingStatus == 1 ">
            AND t1.bill_id IS NOT NULL
        </if>
        GROUP BY t1.pk_id
        ORDER BY
        <choose>
            <when test="dateType != null and dateType == 2">
                t1.dispatch_date DESC
            </when>
            <when test="dateType != null and dateType == 1">
                t1.finish_date DESC
            </when>
            <when test="dateType != null and dateType == 3">
                t1.oper_time DESC
            </when>
            <otherwise>
                t1.oper_time DESC
            </otherwise>
        </choose>
    </select>

    
    
    <select id="queryMiddleCost" resultType="com.bbyb.joy.bms.cost.domain.dto.BmsYfExpensesMiddleDto">
        SELECT t1.id,
            t1.code_id AS codeId,
            t1.code_pk_id AS codePkId,
            t1.code_type AS codeType,
            t1.relate_code AS relateCode,
            t1.total_weight AS totalWeight,
            t1.total_volume AS totalVolume,
            t1.total_number AS totalNumber,
            t1.main_code_id AS mainCodeId,
            t1.main_pk_id AS mainPkId,
            t1.share_type AS shareType,
            t1.freight,
            t1.delivery_fee AS deliveryFee,
            t1.ultrafar_fee AS ultrafarFee,
            t1.superframes_fee AS superframesFee,
            t1.excess_fee AS excessFee,
            t1.reduce_fee AS reduceFee,
            t1.outboundsorting_fee AS outboundsortingFee,
            t1.shortbarge_fee AS shortbargeFee,
            t1.return_fee AS returnFee,
            t1.exception_fee AS exceptionFee,
            t1.other_cost1 AS otherCost1,
            t1.other_cost2 AS otherCost2,
            t1.other_cost3 AS otherCost3,
            t1.other_cost4 AS otherCost4,
            t1.other_cost5 AS otherCost5,
            t1.other_cost6 AS otherCost6,
            t1.other_cost7 AS otherCost7,
            t1.other_cost8 AS otherCost8,
            t1.other_cost9 AS otherCost9,
            t1.other_cost10 AS otherCost10,
            t1.other_cost11 AS otherCost11,
            t1.other_cost12 AS otherCost12,
            t1.settle_amount AS settleAmount,
            t1.del_flag AS delFlag,
            t1.opt_month AS optMonth,
            t1.opt_day AS optDay
        FROM bms_yfexpenses_middle t1
        LEFT JOIN bms_yfcost_main_info t2 ON t2.pk_id = t1.main_pk_id AND t2.del_flag = 0
        WHERE t1.del_flag = 0
        <if test="pkId != null">
            AND t1.main_pk_id = #{pkId}
        </if>
        <if test="relateCode!=null and relateCode!=''">
            AND t1.relate_code LIKE concat(#{relateCode}, '%')
        </if>
    </select>
    
    <select id="queryMiddleSharePage" resultType="com.bbyb.joy.bms.cost.domain.dto.BmsYfExpensesMiddleShareDto">
        SELECT t1.id,
            t1.code_id AS codeId,
            t1.code_pk_id AS codePkId,
            t1.relate_code AS relateCode,
            t1.total_weight AS totalWeight,
            t1.total_volume AS totalVolume,
            t1.total_number AS totalNumber,
            t1.main_code_id AS mainCodeId,
            t1.main_pk_id AS mainPkId,
            t1.share_type AS shareType,
            t1.freight,
            t1.delivery_fee AS deliveryFee,
            t1.ultrafar_fee AS ultrafarFee,
            t1.superframes_fee AS superframesFee,
            t1.excess_fee AS excessFee,
            t1.reduce_fee AS reduceFee,
            t1.outboundsorting_fee AS outboundsortingFee,
            t1.shortbarge_fee AS shortbargeFee,
            t1.return_fee AS returnFee,
            t1.exception_fee AS exceptionFee,
            t1.other_cost1 AS otherCost1,
            t1.other_cost2 AS otherCost2,
            t1.other_cost3 AS otherCost3,
            t1.other_cost4 AS otherCost4,
            t1.other_cost5 AS otherCost5,
            t1.other_cost6 AS otherCost6,
            t1.other_cost7 AS otherCost7,
            t1.other_cost8 AS otherCost8,
            t1.other_cost9 AS otherCost9,
            t1.other_cost10 AS otherCost10,
            t1.other_cost11 AS otherCost11,
            t1.other_cost12 AS otherCost12,
            t1.settle_amount AS settleAmount,
            t1.del_flag AS delFlag,
            t1.opt_month AS optMonth,
            t1.opt_day AS optDay
        FROM bms_yfexpenses_middle_share t1
        LEFT JOIN bms_yfcost_main_info t2 ON t2.pk_id = t1.main_pk_id AND t2.del_flag = 0
        WHERE t1.del_flag = 0
        <if test="pkId != null">
            AND t1.main_pk_id = #{pkId}
        </if>
        <if test="relateCode!=null and relateCode!=''">
            AND t1.relate_code LIKE concat(#{relateCode}, '%')
        </if>
    </select>

    <select id="queryDispatchCodePageByCost" resultType="com.bbyb.joy.bms.code.domain.dto.BmsDispatchCodeInfoDto">
        SELECT t2.id,
            t2.pk_id AS pkId,
            t2.scheduling_code AS schedulingCode,
            t2.virtual_scheduling_code AS virtualSchedulingCode,
            t2.original_scheduling_code AS originalSchedulingCode,
            t2.original_order_type AS originalOrderType,
            t2.express_no AS expressNo,
            t2.order_source AS orderSource,
            t2.split_status AS splitStatus,
            t2.clients_dimension AS clientsDimension,
            t2.transport_type AS transportType,
            t2.transport_mode AS transportMode,
            t2.delivery_mode AS deliveryMode,
            t2.carrier_id AS carrierId,
            t2.carrier_code AS carrierCode,
            t2.carrier_name AS carrierName,
            t2.client_id AS clientId,
            t2.client_code AS clientCode,
            t2.client_name AS clientName,
            t2.company_id AS companyId,
            t2.cost_status AS costStatus,
            t2.cost_fail_remark AS costFailRemark,
            t2.billing_status AS billingStatus,
            t2.network_code AS networkCode,
            t2.base_stores AS baseStores,
            t2.total_store_times AS totalStoreTimes,
            t2.total_in_office_times AS totalInOfficeTimes,
            t2.total_out_office_times AS totalOutOfficeTimes,
            t2.base_kilometer AS baseKilometer,
            t2.total_kilometer AS totalKilometer,
            t2.line_code AS lineCode,
            t2.line_name AS lineName,
            t2.tms_line_code AS tmsLineCode,
            t2.tms_line_name AS tmsLineName,
            t2.dispatch_date AS dispatchDate,
            t2.start_date AS startDate,
            t2.finish_date AS finishDate,
            t2.total_boxes AS totalBoxes,
            t2.total_number AS totalNumber,
            t2.total_weight AS totalWeight,
            t2.total_volume AS totalVolume,
            t2.total_cargo_value AS totalCargoValue,
            t2.total_votes AS totalVotes,
            t2.originating_province AS originatingProvince,
            t2.originating_city AS originatingCity,
            t2.originating_area AS originatingArea,
            t2.originating_address AS originatingAddress,
            t2.destination_province AS destinationProvince,
            t2.destination_city AS destinationCity,
            t2.destination_area AS destinationArea,
            t2.destination_address AS destinationAddress,
            t2.loading_points_number AS loadingPointsNumber,
            t2.unloading_points_number AS unloadingPointsNumber,
            t2.driver AS driver,
            t2.car_number AS carNumber,
            t2.car_type AS carType,
            t2.car_model AS carModel,
            t2.remark AS remark,
            t2.create_code AS createCode,
            t2.create_by AS createBy,
            t2.create_dept_id AS createDeptId,
            t2.create_time AS createTime,
            t2.oper_code AS operCode,
            t2.oper_by AS operBy,
            t2.oper_dept_id AS operDeptId,
            t2.oper_time AS operTime,
            t2.del_flag AS delFlag,
            t2.opt_month AS optMonth,
            t2.opt_day AS optDay
        FROM bms_yfexpenses_middle t1
        LEFT JOIN bms_dispatch_code_info t2 ON t2.pk_id = t1.code_pk_id
        WHERE t1.del_flag = 0 AND t1.code_type = 1
        <if test="pkId != null">
            AND t1.main_pk_id = #{pkId}
        </if>
        <if test="relateCode!=null and relateCode!=''">
            AND t1.relate_code LIKE concat(#{relateCode}, '%')
        </if>
    </select>

    
    <select id="queryJobCodePageByCost" resultType="com.bbyb.joy.bms.code.domain.dto.BmsJobCodeInfoDto">
        SELECT t2.id,
               t2.pk_id AS pkId,
               t2.relate_code AS relateCode,
               t2.order_code AS orderCode,
               t2.virtual_scheduling_code AS virtualSchedulingCode,
               t2.main_code_id AS mainCodeId,
               t2.main_pk_id AS mainPkId,
               t2.client_id AS clientId,
               t2.client_code AS clientCode,
               t2.client_name AS clientName,
               t2.company_id AS companyId,
               t2.originating_company_id AS originatingCompanyId,
               t2.destination_company_id AS destinationCompanyId,
               t2.pay_company_id AS payCompanyId,
               t2.network_code AS networkCode,
               t2.receiving_store_code AS receivingStoreCode,
               t2.receiving_store_name AS receivingStoreName,
               t2.base_store_status AS baseStoreStatus,
               t2.base_mileage_status AS baseMileageStatus,
               t2.over_mileage AS overMileage,
               t2.total_boxes AS totalBoxes,
               t2.total_number AS totalNumber,
               t2.total_weight AS totalWeight,
               t2.total_volume AS totalVolume,
               t2.total_cargo_value AS totalCargoValue,
               t2.originating_province AS originatingProvince,
               t2.originating_city AS originatingCity,
               t2.originating_area AS originatingArea,
               t2.originating_address AS originatingAddress,
               t2.originating_address_code AS originatingAddressCode,
               t2.originating_address_name AS originatingAddressName,
               t2.destination_province AS destinationProvince,
               t2.destination_city AS destinationCity,
               t2.destination_area AS destinationArea,
               t2.destination_address AS destinationAddress,
               t2.destination_address_code AS destinationAddressCode,
               t2.destination_address_name AS destinationAddressName,
               t2.remark AS remark,
               t2.create_code AS createCode,
               t2.create_by AS createBy,
               t2.create_dept_id AS createDeptId,
               t2.create_time AS createTime,
               t2.oper_code AS operCode,
               t2.oper_by AS operBy,
               t2.oper_dept_id AS operDeptId,
               t2.oper_time AS operTime,
               t2.del_flag AS delFlag,
               t2.opt_month AS optMonth,
               t2.opt_day AS optDay
        FROM bms_yfexpenses_middle_share t1
        LEFT JOIN bms_job_code_info t2 ON t2.pk_id = t1.code_pk_id AND t2.del_flag = 0
        WHERE t1.del_flag = 0
        <if test="pkId != null">
            AND t1.main_pk_id = #{pkId}
        </if>
        <if test="relateCode!=null and relateCode!=''">
            AND t1.relate_code LIKE concat(#{relateCode}, '%')
        </if>
    </select>

    <select id="queryJobCodeDetailPageByCost" resultType="com.bbyb.joy.bms.code.domain.dto.BmsJobCodeDetailInfoDto">
        SELECT t2.id,
               t2.pk_id AS pkId,
               t2.main_code_id AS mainCodeId,
               t2.main_pk_id AS mainPkId,
               t2.scheduling_pk_id AS schedulingPkId,
               t2.sku_code AS skuCode,
               t2.sku_name AS skuName,
               t2.temperature_type_id AS temperatureTypeId,
               t2.temperature_type AS temperatureType,
               t2.total_boxes AS totalBoxes,
               t2.total_odd_boxes AS totalOddBoxes,
               t2.total_number AS totalNumber,
               t2.total_weight AS totalWeight,
               t2.total_volume AS totalVolume,
               t2.box_rule_code AS boxRuleCode,
               t2.box_rule AS boxRule,
               t2.remark AS remark,
               t2.del_flag AS delFlag,
               t2.opt_month AS optMonth,
               t2.opt_day AS optDay
        FROM bms_yfexpenses_middle_share t1
        LEFT JOIN bms_job_code_detail_info t2 ON t2.main_pk_id = t1.code_pk_id AND t2.del_flag = 0
        WHERE t1.del_flag = 0
        <if test="pkId != null">
            AND t1.main_pk_id = #{pkId}
        </if>
        <if test="relateCode!=null and relateCode!=''">
            AND t1.relate_code LIKE concat(#{relateCode}, '%')
        </if>
    </select>


    <select id="queryStorageCodePageByCost" resultType="com.bbyb.joy.bms.code.domain.dto.BmsStorageCodeInfoDto">
        SELECT t2.id,
            t2.pk_id AS pkId,
            t2.relate_code AS relateCode,
            t2.order_code AS orderCode,
            t2.transaction_code AS transactionCode,
            t2.code_type AS codeType,
            t2.business_type AS businessType,
            t2.order_source AS orderSource,
            t2.client_id AS clientId,
            t2.client_code AS clientCode,
            t2.client_name AS clientName,
            t2.carrier_id AS carrierId,
            t2.carrier_code AS carrierCode,
            t2.carrier_name AS carrierName,
            t2.company_id AS companyId,
            t2.cost_mode AS costMode,
            t2.ys_cost_status AS ysCostStatus,
            t2.ys_cost_fail_remark AS ysCostFailRemark,
            t2.ys_billing_status AS ysBillingStatus,
            t2.yf_cost_status AS yfCostStatus,
            t2.yf_cost_fail_remark AS yfCostFailRemark,
            t2.yf_billing_status AS yfBillingStatus,
            t2.is_timeout AS isTimeout,
            t2.is_autarky AS isAutarky,
            t2.is_rejected AS isRejected,
            t2.rejected_responsible_party AS rejectedResponsibleParty,
            t2.delivery_mode AS deliveryMode,
            t2.transport_type AS transportType,
            t2.storage_service_provider AS storageServiceProvider,
            t2.warehouse_code AS warehouseCode,
            t2.network_code AS networkCode,
            t2.total_boxes AS totalBoxes,
            t2.total_split_boxes AS totalSplitBoxes,
            t2.total_normal_full_boxes AS totalNormalFullBoxes,
            t2.total_normal_split_boxes AS totalNormalSplitBoxes,
            t2.total_unnormal_full_boxes AS totalUnnormalFullBoxes,
            t2.total_unnormal_split_boxes AS totalUnnormalSplitBoxes,
            t2.total_number AS totalNumber,
            t2.total_split_number AS totalSplitNumber,
            t2.total_over_number AS totalOverNumber,
            t2.total_day_number AS totalDayNumber,
            t2.total_weight AS totalWeight,
            t2.total_volume AS totalVolume,
            t2.total_sku_number AS totalSkuNumber,
            t2.total_pallet_number AS totalPalletNumber,
            t2.total_payable_pallet_number AS totalPayablePalletNumber,
            t2.total_day_pallet_number AS totalDayPalletNumber,
            t2.total_cw_pallet_number AS totalCwPalletNumber,
            t2.total_ld_pallet_number AS totalLdPalletNumber,
            t2.total_lc_pallet_number AS totalLcPalletNumber,
            t2.total_cargo_value AS totalCargoValue,
            t2.pallet_rule AS palletRule,
            t2.box_rule_code AS boxRuleCode,
            t2.box_rule AS boxRule,
            t2.lpn_code AS lpnCode,
            t2.temperature_type_id AS temperatureTypeId,
            t2.temperature_type AS temperatureType,
            t2.sku_code AS skuCode,
            t2.sku_name AS skuName,
            t2.unit AS unit,
            t2.order_date AS orderDate,
            t2.signing_date AS signingDate,
            t2.delivery_code AS deliveryCode,
            t2.delivery_name AS deliveryName,
            t2.receiving_store_code AS receivingStoreCode,
            t2.receiving_store_name AS receivingStoreName,
            t2.originating_province AS originatingProvince,
            t2.originating_city AS originatingCity,
            t2.originating_area AS originatingArea,
            t2.originating_address AS originatingAddress,
            t2.destination_province AS destinationProvince,
            t2.destination_city AS destinationCity,
            t2.destination_area AS destinationArea,
            t2.destination_address AS destinationAddress,
            t2.over_mileage AS overMileage,
            t2.base_store_status AS baseStoreStatus,
            t2.base_mileage_status AS baseMileageStatus,
            t2.distance_warehouse_mileage AS distanceWarehouseMileage,
            t2.car_type AS carType,
            t2.car_model AS carModel,
            t2.remark AS remark,
            t2.create_code AS createCode,
            t2.create_by AS createBy,
            t2.create_dept_id AS createDeptId,
            t2.create_time AS createTime,
            t2.oper_code AS operCode,
            t2.oper_by AS operBy,
            t2.oper_dept_id AS operDeptId,
            t2.oper_time AS operTime,
            t2.del_flag AS delFlag,
            t2.opt_month AS optMonth,
            t2.opt_day AS optDay
        FROM bms_yfexpenses_middle t1
        LEFT JOIN bms_storage_code_info t2 ON t2.pk_id = t1.code_pk_id AND t2.del_flag = 0 AND t2.cost_mode = 2
        WHERE t1.del_flag = 0 AND t1.code_type = 2
        <if test="pkId != null">
            AND t1.main_pk_id = #{pkId}
        </if>
        <if test="relateCode!=null and relateCode!=''">
            AND t1.relate_code LIKE concat(#{relateCode}, '%')
        </if>
    </select>

    <select id="queryStorageCodeDetailPageByCost" resultType="com.bbyb.joy.bms.code.domain.dto.BmsStorageCodeDetailInfoDto">
        SELECT t2.id,
            t2.main_code_id AS mainCodeId,
            t2.main_pk_id AS mainPkId,
            t2.sku_code AS skuCode,
            t2.sku_name AS skuName,
            t2.temperature_type_id AS temperatureTypeId,
            t2.temperature_type AS temperatureType,
            t2.good_type AS goodType,
            t2.total_boxes AS totalBoxes,
            t2.total_odd_boxes AS totalOddBoxes,
            t2.total_number AS totalNumber,
            t2.total_weight AS totalWeight,
            t2.total_volume AS totalVolume,
            t2.total_cargo_value AS totalCargoValue,
            t2.product_date AS productDate,
            t2.expire_date AS expireDate,
            t2.inbound_date AS inboundDate,
            t2.batch_code AS batchCode,
            t2.box_rule_code AS boxRuleCode,
            t2.lpn_code AS lpnCode,
            t2.box_rule AS boxRule,
            t2.del_flag AS delFlag,
            t2.opt_month AS optMonth,
            t2.opt_day AS optDay
        FROM bms_yfexpenses_middle t1
        LEFT JOIN bms_storage_code_detail_info t2 ON t2.main_pk_id = t1.code_pk_id AND t2.del_flag = 0
        WHERE t1.del_flag = 0 AND t1.code_type = 2
        <if test="pkId != null">
            AND t1.main_pk_id = #{pkId}
        </if>
        <if test="relateCode!=null and relateCode!=''">
            AND t1.relate_code LIKE concat(#{relateCode}, '%')
        </if>
    </select>

</mapper>