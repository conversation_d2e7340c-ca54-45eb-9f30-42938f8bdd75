<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYsbillcodeDetailinfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeDetailinfo" id="BmsYsbillcodeDetailinfoResult">
        <result property="id"    column="id"    />
        <result property="ysbillId"    column="ysbill_id"    />
        <result property="skuCode"    column="sku_code"    />
        <result property="skuClass"    column="sku_class"    />
        <result property="skuName"    column="sku_name"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="oddBoxes"    column="odd_boxes"    />
        <result property="boxType"    column="box_type"    />
        <result property="temperatureType"    column="temperature_type"    />
        <result property="contentsNumber"    column="contents_number"    />
        <result property="weight"    column="weight"    />
        <result property="volume"    column="volume"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="price"    column="price"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="boxes"    column="boxes"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="specification"    column="specification"    />
        <result property="unit"    column="unit"    />

    </resultMap>

    <sql id="selectBmsYsbillcodeDetailinfoVo">
        select id, ysbill_id, sku_code, sku_class, sku_name, total_boxes, odd_boxes, box_type, temperature_type, contents_number, weight, volume,total_weight,total_volume, price, del_flag from bms_ysbillcode_detailinfo
    </sql>

    <select id="selectBmsYsbillcodeDetailinfoList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeDetailinfo" resultMap="BmsYsbillcodeDetailinfoResult">
        <include refid="selectBmsYsbillcodeDetailinfoVo"/>
        <where>
            <if test="ysbillId != null "> and ysbill_id = #{ysbillId}</if>
            <if test="skuCode != null  and skuCode != ''"> and sku_code = #{skuCode}</if>
            <if test="skuClass != null  and skuClass != ''"> and sku_class = #{skuClass}</if>
            <if test="skuName != null  and skuName != ''"> and sku_name like concat('%', #{skuName}, '%')</if>
            <if test="totalBoxes != null "> and total_boxes = #{totalBoxes}</if>
            <if test="oddBoxes != null "> and odd_boxes = #{oddBoxes}</if>
            <if test="boxType != null  and boxType != ''"> and box_type = #{boxType}</if>
            <if test="temperatureType != null "> and temperature_type = #{temperatureType}</if>
            <if test="contentsNumber != null "> and contents_number = #{contentsNumber}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="volume != null "> and volume = #{volume}</if>
            <if test="totalWeight != null "> and total_weight = #{totalWeight}</if>
            <if test="totalVolume != null "> and total_volume = #{totalVolume}</if>
            <if test="price != null "> and price = #{price}</if>
        </where>
    </select>

    <select id="selectBmsYsbillcodeDetailinfoById" parameterType="java.lang.String" resultMap="BmsYsbillcodeDetailinfoResult">
        <include refid="selectBmsYsbillcodeDetailinfoVo"/>
        where id = #{id}
    </select>

    <select id="selectBmsYsbillcodeDetailinfoByYsbillId" resultMap="BmsYsbillcodeDetailinfoResult">
        select yd.id, yd.ysbill_id, yd.sku_code, yd.sku_class, yd.sku_name, yd.total_boxes,
        yd.odd_boxes, yd.box_type, yd.temperature_type, yd.contents_number, yd.weight,
        yd.volume,yd.total_weight,yd.total_volume, yd.price, yd.del_flag,y.relate_code relateCode,
        case when yd.odd_boxes>0 then yd.total_boxes+1 else yd.total_boxes end boxes,yd.total_amount,
        ms.specification,ms.unit
        from bms_ysbillcode_detailinfo yd
        left join  bms_ysbillcodeinfo y on yd.ysbill_id = y.id and y.del_flag = 0
        left join mdm_skuinfo ms on yd.sku_code=ms.sku_code and ms.status=0
        where yd.del_flag = 0
        <if test="ids!=null and ids.size()>0">
            and yd.ysbill_id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <insert id="insertBmsYsbillcodeDetailinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeDetailinfo">
        insert into bms_ysbillcode_detailinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="ysbillId != null">ysbill_id,</if>
            <if test="skuCode != null">sku_code,</if>
            <if test="skuClass != null">sku_class,</if>
            <if test="skuName != null">sku_name,</if>
            <if test="totalBoxes != null">total_boxes,</if>
            <if test="oddBoxes != null">odd_boxes,</if>
            <if test="boxType != null">box_type,</if>
            <if test="temperatureType != null">temperature_type,</if>
            <if test="contentsNumber != null">contents_number,</if>
            <if test="weight != null">weight,</if>
            <if test="volume != null">volume,</if>
            <if test="totalWeight != null">total_weight,</if>
            <if test="totalVolume != null">total_volume,</if>
            <if test="price != null">price,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="ysbillId != null">#{ysbillId},</if>
            <if test="skuCode != null">#{skuCode},</if>
            <if test="skuClass != null">#{skuClass},</if>
            <if test="skuName != null">#{skuName},</if>
            <if test="totalBoxes != null">#{totalBoxes},</if>
            <if test="oddBoxes != null">#{oddBoxes},</if>
            <if test="boxType != null">#{boxType},</if>
            <if test="temperatureType != null">#{temperatureType},</if>
            <if test="contentsNumber != null">#{contentsNumber},</if>
            <if test="weight != null">#{weight},</if>
            <if test="volume != null">#{volume},</if>
            <if test="totalWeight != null">#{totalWeight},</if>
            <if test="totalVolume != null">#{totalVolume},</if>
            <if test="price != null">#{price},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateBmsYsbillcodeDetailinfo" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeDetailinfo">
        update bms_ysbillcode_detailinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="ysbillId != null">ysbill_id = #{ysbillId},</if>
            <if test="skuCode != null">sku_code = #{skuCode},</if>
            <if test="skuClass != null">sku_class = #{skuClass},</if>
            <if test="skuName != null">sku_name = #{skuName},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="oddBoxes != null">odd_boxes = #{oddBoxes},</if>
            <if test="boxType != null">box_type = #{boxType},</if>
            <if test="temperatureType != null">temperature_type = #{temperatureType},</if>
            <if test="contentsNumber != null">contents_number = #{contentsNumber},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="volume != null">volume = #{volume},</if>
            <if test="totalWeight != null">total_weight = #{totalWeight},</if>
            <if test="totalVolume != null">total_volume = #{totalVolume},</if>
            <if test="price != null">price = #{price},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBmsYsbillcodeDetailinfoById" parameterType="java.lang.String">
        delete from bms_ysbillcode_detailinfo where id = #{id}
    </delete>

    <delete id="deleteBmsYsbillcodeDetailinfoByIds">
        delete from bms_ysbillcode_detailinfo where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYsbillcodeDetailinfoStatusByIds">
        update bms_ysbillcode_detailinfo set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchSave" parameterType="java.util.List">
        insert into bms_ysbillcode_detailinfo
        (ysbill_id, sku_code, sku_class, sku_name, total_boxes, odd_boxes, box_type, temperature_type, contents_number, weight, volume,total_weight,total_volume, price, del_flag,total_amount)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.ysbillId},
            #{item.skuCode},
            #{item.skuClass},
            #{item.skuName},
            #{item.totalBoxes},
            #{item.oddBoxes},
            #{item.boxType},
            #{item.temperatureType},
            #{item.contentsNumber},
            #{item.weight},
            #{item.volume},
            #{item.totalWeight},
            #{item.totalVolume},
            #{item.price},
            #{item.delFlag},
            #{item.totalAmount})
        </foreach>
    </insert>

    <select id="selectBmsYsbillcodeDetailinfoList2" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDetailinfoDto" parameterType="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeDetailinfo">
        select
            t1.id as id,
            t2.relate_code as relateCode,
            t1.ysbill_id as ysbillId,
            t1.sku_code as skuCode,
            t1.sku_class as skuClass,
            t1.sku_name as skuName,
            t1.total_boxes as totalBoxes,
            t1.odd_boxes as oddBoxes,
            t1.box_type as boxType,
            t1.temperature_type as temperatureType,
            t1.contents_number as contentsNumber,
            t1.weight as weight,
            t1.volume as volume,
            t1.total_weight as totalWeight,
            t1.total_volume as totalVolume,
            t1.price as price,
            t1.del_flag as delFlag,
            t2.order_type as orderType,
            t2.cw_pallet_number as cwPalletNumber,
            t2.lc_pallet_number as lcPalletNumber,
            t2.ld_pallet_number as ldPalletNumber,
            t2.order_no as orderNo,
            t1.total_amount as totalAmount,
            t2.platform_code as platformCode,
            ms.unit,
            ms.specification
        from bms_ysbillcode_detailinfo t1
        left join bms_ysbillcodeinfo t2 on t2.id = t1.ysbill_id and t2.del_flag = '0'
        left join mdm_skuinfo ms on ms.sku_code=t1.sku_code and ms.status=0
        <where>
            t1.del_flag = '0'
            <if test="ysbillId != null "> and t1.ysbill_id = #{ysbillId}</if>
            <if test="ysbillIds!=null and ysbillIds.size()>0">
                and t1.ysbill_id in
                <foreach collection="ysbillIds"  item="item" open="(" separator="," close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="skuCode != null  and skuCode != ''"> and t1.sku_code = #{skuCode}</if>
            <if test="skuClass != null  and skuClass != ''"> and t1.sku_class = #{skuClass}</if>
            <if test="skuName != null  and skuName != ''"> and t1.sku_name like concat('%', #{skuName}, '%')</if>
            <if test="totalBoxes != null "> and t1.total_boxes = #{totalBoxes}</if>
            <if test="oddBoxes != null "> and t1.odd_boxes = #{oddBoxes}</if>
            <if test="boxType != null  and boxType != ''"> and t1.box_type = #{boxType}</if>
            <if test="temperatureType != null "> and t1.temperature_type = #{temperatureType}</if>
            <if test="contentsNumber != null "> and t1.contents_number = #{contentsNumber}</if>
            <if test="weight != null "> and t1.weight = #{weight}</if>
            <if test="volume != null "> and t1.volume = #{volume}</if>
            <if test="totalWeight != null "> and t1.total_weight = #{totalWeight}</if>
            <if test="totalVolume != null "> and t1.total_volume = #{totalVolume}</if>
            <if test="price != null "> and t1.price = #{price}</if>
        </where>
        ORDER BY t2.relate_code
    </select>
</mapper>