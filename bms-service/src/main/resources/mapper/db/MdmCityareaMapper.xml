<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.MdmCityareaMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.MdmCityarea" id="MdmCityareaResult">
        <result property="id"    column="id"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="area"    column="area"    />
        <result property="lastRegion"    column="last_region"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="externalId"    column="external_id"    />
        <result property="addressLevel"    column="address_level"    />
        <result property="status"    column="status"    />
    </resultMap>

    <insert id="insertMdmCityarea" parameterType="com.bbyb.joy.bms.domain.dto.MdmCityarea" useGeneratedKeys="true" keyProperty="id">
        insert into mdm_cityarea
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="area != null">area,</if>
            <if test="lastRegion != null">last_region,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="externalId != null">external_id,</if>
            <if test="addressLevel != null">address_level,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="area != null">#{area},</if>
            <if test="lastRegion != null">#{lastRegion},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="externalId != null">#{externalId},</if>
            <if test="addressLevel != null">#{addressLevel},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateMdmCityarea" parameterType="com.bbyb.joy.bms.domain.dto.MdmCityarea">
        update mdm_cityarea
        <trim prefix="SET" suffixOverrides=",">
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="lastRegion != null">last_region = #{lastRegion},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="externalId != null">external_id = #{externalId},</if>
            <if test="addressLevel != null">address_level = #{addressLevel},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="checkExternalIdUnique" parameterType="java.lang.Long" resultMap="MdmCityareaResult">
        select id, external_id from mdm_cityarea where del_flag = 0 and external_id=#{externalId} limit 1
    </select>


    <select id="selectCityInfoList" resultMap="MdmCityareaResult">
        SELECT
        Id,
        province,
        city,
        area
        FROM
        mdm_cityarea
        where del_flag=0

        <if test="provinces != null and provinces.size>0 ">
            AND province in
            <foreach collection="provinces" item="provinces" open="(" separator="," close=")">
                #{provinces}
            </foreach>
            GROUP BY province
        </if>
        <if test="citys != null and citys.size>0 ">
            AND city in
            <foreach collection="citys" item="citys" open="(" separator="," close=")">
                #{citys}
            </foreach>
            GROUP BY city
        </if>
        <if test="areas != null and areas.size>0 ">
            AND area in
            <foreach collection="areas" item="areas" open="(" separator="," close=")">
                #{areas}
            </foreach>
            GROUP BY area
        </if>

    </select>

    <select id="selectAllProvinceInfoList" resultMap="MdmCityareaResult">
        SELECT DISTINCT
            Id,
            province
        FROM mdm_cityarea
        WHERE del_flag=0
          AND (ISNULL(city) OR city = '')
          AND (ISNULL(area) OR area = '')
    </select>
</mapper>