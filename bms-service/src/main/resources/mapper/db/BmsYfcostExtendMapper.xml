<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfcostExtendMapper">

	<resultMap type="com.bbyb.joy.bms.domain.dto.BmsYfcostExtend" id="BmsYfcostExtendResult">
		<result property="id"    column="id"    />
		<result property="expensesId"    column="expenses_id"    />
		<result property="businessCode"    column="business_code"    />
		<result property="warehouseCode"    column="warehouse_code"    />
		<result property="warehouseName"    column="warehouse_name"    />
		<result property="totalBoxes"    column="total_boxes"    />
		<result property="totalNumber"    column="total_number"    />
		<result property="totalWeight"    column="total_weight"    />
		<result property="totalVolume"    column="total_volume"    />
		<result property="cargoValue"    column="cargo_value"    />
		<result property="delFlag"    column="del_flag"    />
		<result property="codeCount"    column="code_count"    />
		<result property="tlineCode"    column="tline_code"    />
		<result property="tlineName"    column="tline_name"    />
		<result property="lineCode"    column="line_code"    />
		<result property="dispatchDate"    column="dispatch_date"    />
		<result property="finishDate"    column="finish_date"    />
	</resultMap>

	<sql id="selectBmsYfcostExtendVo">
		select id, expenses_id, business_code, warehouse_code, warehouse_name, total_boxes, total_number, total_weight, total_volume, cargo_value, del_flag,code_count,tline_code,tline_name,line_code,dispatch_date,finish_date from bms_yfcost_extend
	</sql>

	<select id="selectBmsYfcostExtendList" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfcostExtend" resultMap="BmsYfcostExtendResult">
		<include refid="selectBmsYfcostExtendVo"/>
		<where>
			<if test="expensesId != null "> and expenses_id = #{expensesId}</if>
			<if test="businessCode != null  and businessCode != ''"> and business_code = #{businessCode}</if>
			<if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
			<if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
			<if test="totalBoxes != null "> and total_boxes = #{totalBoxes}</if>
			<if test="totalNumber != null "> and total_number = #{totalNumber}</if>
			<if test="totalWeight != null "> and total_weight = #{totalWeight}</if>
			<if test="totalVolume != null "> and total_volume = #{totalVolume}</if>
			<if test="cargoValue != null "> and cargo_value = #{cargoValue}</if>
		</where>
	</select>

	<select id="selectBmsYfcostExtendById" parameterType="java.lang.String" resultMap="BmsYfcostExtendResult">
		<include refid="selectBmsYfcostExtendVo"/>
		where id = #{id}
	</select>

	<select id="selectBmsYfcostExtendListByExpensesIds"  resultMap="BmsYfcostExtendResult">
		<include refid="selectBmsYfcostExtendVo"/>
		where del_flag = 0 and expenses_id in
		<foreach item="id" collection="expensesIds" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

	<insert id="insertBmsYfcostExtend" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfcostExtend" useGeneratedKeys="true" keyProperty="id">
		insert into bms_yfcost_extend
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="expensesId != null">expenses_id,</if>
			<if test="businessCode != null and businessCode != ''">business_code,</if>
			<if test="warehouseCode != null">warehouse_code,</if>
			<if test="warehouseName != null">warehouse_name,</if>
			<if test="totalBoxes != null">total_boxes,</if>
			<if test="totalNumber != null">total_number,</if>
			<if test="totalWeight != null">total_weight,</if>
			<if test="totalVolume != null">total_volume,</if>
			<if test="cargoValue != null">cargo_value,</if>
			<if test="delFlag != null">del_flag,</if>
			<if test="mainExpenseId != null">main_expense_id,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="expensesId != null">#{expensesId},</if>
			<if test="businessCode != null and businessCode != ''">#{businessCode},</if>
			<if test="warehouseCode != null">#{warehouseCode},</if>
			<if test="warehouseName != null">#{warehouseName},</if>
			<if test="totalBoxes != null">#{totalBoxes},</if>
			<if test="totalNumber != null">#{totalNumber},</if>
			<if test="totalWeight != null">#{totalWeight},</if>
			<if test="totalVolume != null">#{totalVolume},</if>
			<if test="cargoValue != null">#{cargoValue},</if>
			<if test="delFlag != null">#{delFlag},</if>
			<if test="mainExpenseId != null">#{mainExpenseId},</if>
		</trim>
	</insert>

	<update id="updateBmsYfcostExtend" parameterType="com.bbyb.joy.bms.domain.dto.BmsYfcostExtend">
		update bms_yfcost_extend
		<trim prefix="SET" suffixOverrides=",">
			<if test="expensesId != null">expenses_id = #{expensesId},</if>
			<if test="businessCode != null and businessCode != ''">business_code = #{businessCode},</if>
			<if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
			<if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
			<if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
			<if test="totalNumber != null">total_number = #{totalNumber},</if>
			<if test="totalWeight != null">total_weight = #{totalWeight},</if>
			<if test="totalVolume != null">total_volume = #{totalVolume},</if>
			<if test="cargoValue != null">cargo_value = #{cargoValue},</if>
			<if test="delFlag != null">del_flag = #{delFlag},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteBmsYfcostExtendById" parameterType="java.lang.String">
		delete from bms_yfcost_extend where id = #{id}
	</delete>

	<delete id="deleteBmsYfcostExtendByIds">
		delete from bms_yfcost_extend where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

	<update id="updateBmsYfcostExtendStatusByIds">
		update bms_yfcost_extend set status = #{status} where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>
</mapper>