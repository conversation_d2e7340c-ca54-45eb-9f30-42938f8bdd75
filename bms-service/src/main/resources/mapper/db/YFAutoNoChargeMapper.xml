<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.YFAutoNoChargeMapper">

    <!--    查询线路编码为9999的单据 -->
    <resultMap type="com.bbyb.joy.bms.domain.dto.charginglogic.AutoYfCodeInfo" id="AutoYFCodeInfoResult"
               extends="mapper.db.BmsYfbillcodeinfoMapper.BmsYfbillcodeinfoResult">
        <result property="clientCode"    column="client_code"    />
    </resultMap>
    <select id="SelectYfCodeByLine"  resultMap="AutoYFCodeInfoResult">
        select  t1.id,t1.scheduling_bill_code,t1.virtual_order_no,t1.tline_code,t1.carrier_code,t2.client_code
        froM bms_yfbillcodeinfo t1
                 left join bms_clientinfo t2 on t1.client_id=t2.id
        where t1.del_flag=0 and t1.cost_status=0 and t1.client_id>0 and t1.line_code='9999'
    </select>

    <!--    查询配置表 -->
    <resultMap type="com.bbyb.joy.bms.domain.dto.charginglogic.AutoYfSettingInfo" id="AutoYFSettingResult">
        <result property="clientCode"    column="client_code"    />
        <result property="lineCode"    column="line_code"    />
    </resultMap>
    <select id="SelectYfSettingInfo"  resultMap="AutoYFSettingResult" >
        select client_code,line_code from bms_yfcost_setting WHERE del_flag=0 order by client_code ,line_code
    </select>
</mapper>