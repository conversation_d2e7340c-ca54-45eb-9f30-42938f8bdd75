<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsCollectionRecordMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.settlement.BmsCollectionRecord" id="BmsCollectionRecordResult">
        <result property="id"    column="id"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="collectionCode"    column="collection_code"    />
        <result property="payType"    column="pay_type"    />
        <result property="bankName"    column="bank_name"    />
        <result property="collectionTime"    column="collection_time"    />
        <result property="collectionAmount"    column="collection_amount"    />
        <result property="availableAmount"    column="available_amount"    />
        <result property="remark"    column="remark"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <!--        经办人-->
        <result property="handler" column="handler" />
        <!--        核销金额-->
        <result property="HXAmount" column="payment" />
        <!--        使用账单总条数-->
        <result property="billSum" column="bill_count" />
        <result property="billCodes" column="billCodes" />

        <result property="replyCode" column="reply_code"/>
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="createDeptId" column="create_dept_id" />
        <result property="collectionAmount" column="collection_amount" />
        <result property="availableAmount" column="available_amount" />
        <result property="transactionCode" column="transaction_code" />
        <result property="isAvailable" column="is_available" />
        <result property="incomeType" column="income_type" />
        <result property="incomeTypeDesc" column="income_type_desc" />
        <result property="middleId" column="middleId" />
    </resultMap>

    
    <resultMap id="CollectionDetail" type="com.bbyb.joy.bms.domain.dto.settlement.BmsCollectionRecordDetails">
        <result property="id" column="id" />
        <result property="clientName" column="client_name" />
        <result property="clientId" column="client_id" />
        <result property="payType" column="pay_type" />
        <result property="bankName" column="bank_name" />
        <result property="collectionTime" column="collection_time" />
        <result property="replyCode" column="reply_code" />
        <result property="remark" column="remark" />
        <result property="collectionAmount"  column="collection_amount" />
        <result property="collectionCode" column="collection_code" />
        <result property="handler" column="handler" />
        <collection property="hxBillList" javaType="java.util.ArrayList"  ofType="com.bbyb.joy.bms.domain.dto.settlement.HxInfo">
            <result property="id"    column="hx_id"     />
            <result property="billCodes"    column="hx_billcodes"    />
            <result property="collectionId"    column="hx_collection_id"    />
            <result property="payment"    column="hx_payment"    />
            <result property="paymentTime"    column="hx_payment_time"    />
            <result property="remark"    column="hx_remark"    />
        </collection>
        <collection property="fileList" javaType="java.util.ArrayList" ofType="com.bbyb.joy.bms.domain.dto.settlement.Attachmentiinfo" >
            <result property="id"    column="pa_id"    />
            <result property="relationIde"    column="pa_relation_ide"    />
            <result property="attachmentName"    column="attachment_name"    />
            <result property="attachmentPath"    column="attachment_path"    />
            <result property="remark"    column="pa_remark"    />
        </collection>
    </resultMap>
    <sql id="selectBmsCollectionRecordVo">
        select available_amount,collection_amount,id, client_id,income_type, client_name, collection_code, pay_type, bank_name,
               collection_time,  collection_amount, remark, create_code, create_by, create_time, create_dept_id, oper_dept_id, oper_code, oper_by, oper_time, del_flag ,handler from bms_collection_record
    </sql>

    <sql id="selectCollectionList">
        SELECT
	cr.id,
	cr.client_id,
	cr.client_name,
	cr.collection_code,
	cr.pay_type,
	cr.bank_name,
	cr.collection_time,
	cr.collection_amount,
	cr.available_amount,
	cr.remark,
	cr.oper_dept_id,
	cr.oper_code,
	cr.oper_by,
	cr.oper_time,
	cr.del_flag,
	cr.handler,
	cr.reply_code, -- 回执单号
	cr.create_code,
	cr.create_by,
	cr.create_time,
	cr.create_dept_id,
        cr.transaction_code,
        cr.is_available,
	sum(yhx.payment) payment, -- 核销总金额
	count(yhx.id) as bill_count
FROM
	bms_collection_record cr
	LEFT JOIN bms_yshx_record_middle rm on rm.record_id=cr.id and rm.del_flag=0
        LEFT JOIN (SELECT rm.id,sum(yhx.payment) payment from bms_yshx_record_middle rm
        LEFT JOIN bms_ysbill_hxinfo  yhx ON yhx.id = rm.hx_id
        where rm.record_id = 30 GROUP BY yhx.bill_codes)  yhx ON yhx.id = rm.id
    </sql>

    <sql id="detailCollection">
        SELECT
	cr.id,
	cr.client_id,
	cr.client_name,
	cr.collection_code,
	cr.pay_type,
	cr.bank_name,
	cr.collection_time,
	cr.collection_amount,
	cr.remark,
	cr.oper_dept_id,
	cr.oper_code,
	cr.oper_by,
	cr.oper_time,
	cr.del_flag,
	cr.handler,
	cr.reply_code,-- 回执单号
	pa.id as  pa_id,
	pa.attachment_path,
	pa.relation_ide as pa_relation_ide,
	pa.attachment_name,
	pa.remark as pa_remark,
	yhx.id as hx_id,
	yhx.payment_time as hx_time,
	yhx.remark as hx_remark,
	yhx.bill_codes as hx_billcodes,
	yhx.payment as hx_payment,
	yhx.create_code as hx_create_code,
	yhx.create_by as hx_create_by
FROM
	bms_collection_record cr
	LEFT JOIN bms_yshx_record_middle rm on rm.record_id=cr.id and rm.del_flag=0
	LEFT JOIN bms_ysbill_hxinfo yhx ON yhx.id = rm.hx_id  and  yhx.del_flag = 0
	LEFT JOIN pub_attachmentiinfo pa ON cr.collection_code = pa.relation_ide and pa.del_flag =0 -- 附件表
    </sql>
    <!--获取详情 核销明细-->
    <select id="selectDetail" parameterType="com.bbyb.joy.bms.domain.dto.settlement.BmsCollectionRecord" resultMap="CollectionDetail">
        <include refid="detailCollection" />
        <where>
            <if test="id!=null">and cr.id=#{id} </if>
            <if test="collectionCode!=null and collectionCode!=''">and cr.collection_code=#{collectionCode}</if>
            and cr.del_flag=0
        </where>
    </select>
    <select id="selectBmsCollectionRecordList" parameterType="com.bbyb.joy.bms.domain.dto.settlement.BmsCollectionRecord" resultMap="BmsCollectionRecordResult">
        SELECT
        cr.id,
        cr.client_id,
        mc.client_code,
        cr.client_name,
        cr.collection_code,
        cr.pay_type,
        cr.bank_name,
        cr.collection_time,
        cr.collection_amount,
        cr.available_amount,
        cr.remark,
        cr.oper_dept_id,
        cr.oper_code,
        cr.oper_by,
        cr.oper_time,
        DATE_FORMAT(cr.oper_time,'%Y-%m-%d %H:%i:%s') AS operTimeStr,
        cr.del_flag,
        cr.handler,
        cr.reply_code, -- 回执单号
        cr.create_code,
        cr.create_by,
        cr.create_time,
        DATE_FORMAT(cr.create_time,'%Y-%m-%d %H:%i:%s') AS createTimeStr,
        cr.create_dept_id,
        cr.transaction_code,
        cr.is_available,
        cr.income_type,
        rm.id as middleId,
        --         sum(yhx.payment) payment, -- 核销总金额
        (cr.collection_amount-cr.available_amount) payment,   -- 核销总金额
        group_concat(distinct yhx.bill_codes) as billCodes
        FROM
        bms_collection_record cr
        LEFT JOIN bms_yshx_record_middle rm on rm.record_id=cr.id and rm.del_flag=0
        left join bms_clientinfo mc on cr.client_id=mc.id and mc.del_flag=0
        LEFT JOIN (SELECT rm.id,sum(yhx.payment) payment,
        group_concat(distinct yhx.bill_codes) bill_codes
        from bms_yshx_record_middle rm
        LEFT JOIN bms_ysbill_hxinfo  yhx ON yhx.id = rm.hx_id and yhx.del_flag =0
        where 1=1
        and IFNULL(yhx.payment,0)!=0
        <if test="ids != null and ids.size>0 ">
            and rm.record_id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
        GROUP BY rm.record_id,yhx.bill_codes
        )  yhx ON yhx.id = rm.id
        <where>
            cr.del_flag = 0
            <if test="isAvailable != null "> and cr.is_available = #{isAvailable}</if>
            <if test="clientId != null "> and cr.client_id = #{clientId}</if>
            <if test="clientCode != null  and clientCode != ''"> and mc.client_code like concat('%', #{clientCode}, '%')</if>
            <if test="clientName != null  and clientName != ''"> and cr.client_name like concat('%', #{clientName}, '%')</if>
            <if test="payType != null "> and cr.pay_type = #{payType}</if>
            <if test="params.beginCollectionTime != null and params.beginCollectionTime != '' and params.endCollectionTime != null and params.endCollectionTime != ''"> and cr.collection_time between #{params.beginCollectionTime} and #{params.endCollectionTime}</if>
            <if test="createDeptId != null "> and cr.create_dept_id = #{createDeptId}</if>
            <if test="remark!=null and remark!=''" >and cr.remark like CONCAT('%',#{remark},'%')</if>
            <if test="id!=null">and cr.id=#{id}</if>
            <if test="transactionCode!=null and transactionCode!=''" >and cr.transaction_code like CONCAT('%',#{transactionCode},'%')</if>
            <if test="bankName != null and bankName != ''"> and cr.bank_name like CONCAT('%',#{bankName},'%')</if>
            <if test="createDeptIds!=null and createDeptIds.length>0">
                and cr.create_dept_id in
                <foreach collection="createDeptIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size>0 ">
                and cr.id in
                <foreach collection="ids" item="ids" open="(" separator="," close=")">
                    #{ids}
                </foreach>
            </if>
            <if test="incomeType != null">
                and cr.income_type =#{incomeType}
            </if>
        </where>

        GROUP BY cr.id
        ORDER BY cr.create_time desc
    </select>

    <select id="queryDetail" resultMap="BmsCollectionRecordResult">
        SELECT
        cr.id,
        cr.client_id,
        mc.client_code,
        cr.client_name,
        cr.pay_type,
        cr.bank_name,
        cr.collection_time,
        cr.collection_amount,
        cr.available_amount,
        cr.remark,
        cr.oper_dept_id,
        cr.oper_code,
        cr.oper_by,
        cr.oper_time,
        cr.del_flag,
        cr.handler,
        cr.reply_code, -- 回执单号
        cr.create_code,
        cr.create_by,
        cr.create_time,
        cr.create_dept_id,
        cr.transaction_code,
        cr.is_available,
        cr.income_type,
        sdd.dict_label as income_type_desc
        FROM
        bms_collection_record cr
        left join bms_clientinfo mc on cr.client_id=mc.id and mc.del_flag=0
        left join sys_dict_data sdd on cr.income_type=sdd.dict_value and dict_type='bms_collection_type'
        where 1=1
        <if test="ids != null and ids.size>0 ">
            and cr.id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
    </select>

    <select id="selectBmsYsbillmainList" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        select
        ys.bill_code as billCode,
        ys.bill_name as billName,
        ys.bill_type as billType,
        cli.id as clientId,
        cli.client_name as clientName,
        ys.hx_amount as hxAmount,
        IFNULL(ys.adjusted_amount,0) - IFNULL(ys.hx_amount,0) as whxAmount,
        max(bshx.payment_time) as hxTime
        ,bshx.create_code as createCode
        ,bshx.create_by as createBy
        ,bshx.create_time as createTime

        from bms_ysbillmain ys
        left join bms_clientinfo cli
        on cli.id = ys.client_id
        left join bms_ysbill_hxinfo bshx
        on bshx.bill_codes = ys.bill_code
        and bshx.del_flag = 0
        left join bms_yshx_record_middle rm
        on rm.hx_id =bshx.id
        and rm.del_flag = 0
        left join bms_collection_record bcr
        on bcr.id = rm.record_id
        and bcr.del_flag = 0 and IFNULL(bcr.is_available,0) = 0

        where 1=1
        and ys.del_flag=0

        and ys.audit_state = 2
        <if test="hxType != null and hxType==1">
            and IFNULL(ys.hx_state,1) in (1,3)
        </if>
        <if test="hxType != null and hxType==2">
            #and IFNULL(ys.hx_state,1) in (2)
            and bcr.id = #{recordId}
        </if>
        <if test="billCode != null">and ys.bill_code = #{billCode}</if>
        <if test="clientName != null">and (cli.client_name like CONCAT('%',#{clientName},'%')
            or cli.brand_name like CONCAT('%',#{clientName},'%')
            )</if>
        group by ys.bill_code
        order by bshx.payment_time desc,ys.bill_code
    </select>

    <select id="selectBmsCollectionRecordById" parameterType="java.lang.String" resultMap="BmsCollectionRecordResult">
        <include refid="selectBmsCollectionRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectBmsCollectionRecordByCode" parameterType="java.lang.String" resultMap="BmsCollectionRecordResult">
        <include refid="selectBmsCollectionRecordVo"/>
        where collection_code = #{code}
    </select>

    <select id="selectBmsCollectionRecordAmtById" parameterType="java.lang.String" resultMap="BmsCollectionRecordResult">
        <include refid="selectBmsCollectionRecordVo"/>
        where id = #{id}
        and del_flag = 0
        limit 1

    </select>
        
    <insert id="insertBmsCollectionRecord" parameterType="com.bbyb.joy.bms.domain.dto.settlement.BmsCollectionRecord" useGeneratedKeys="true" keyProperty="id">
        insert into bms_collection_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clientId != null">client_id,</if>
            <if test="clientName != null">client_name,</if>
            <if test="collectionCode != null">collection_code,</if>
            <if test="payType != null">pay_type,</if>
            <if test="bankName != null">bank_name,</if>
            <if test="collectionTime != null">collection_time,</if>
            <if test="collectionAmount != null">collection_amount,</if>
            <if test="availableAmount!=null">available_amount,</if>
            <if test="remark != null">remark,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="handler!=null" >handler,</if>
            <if test="replyCode!=null">reply_code,</if>
            <if test="transactionCode!=null" >transaction_code,</if>
            <if test="isAvailable!=null">is_available,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clientId != null">#{clientId},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="collectionCode != null">#{collectionCode},</if>
            <if test="payType != null">#{payType},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="collectionTime != null">#{collectionTime},</if>
            <if test="collectionAmount != null">#{collectionAmount},</if>
            <if test="availableAmount!=null">#{availableAmount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="handler!=null">#{handler},</if>
            <if test="replyCode!=null">#{replyCode},</if>
            <if test="transactionCode!=null">#{transactionCode},</if>
            <if test="isAvailable!=null">#{isAvailable},</if>
         </trim>
    </insert>

    <update id="updateBmsCollectionRecord" parameterType="com.bbyb.joy.bms.domain.dto.settlement.BmsCollectionRecord">
        update bms_collection_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="collectionCode != null">collection_code = #{collectionCode},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="collectionTime != null">collection_time = #{collectionTime},</if>
            <if test="collectionAmount != null">collection_amount = #{collectionAmount},</if>
            <if test="availableAmount != null">available_amount = #{availableAmount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="handler!=null">handler=#{handler},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="replyCode!=null">reply_code = #{replyCode},</if>
            <if test="transactionCode != null">transaction_code = #{transactionCode},</if>
            <if test="isAvailable!=null">is_available = #{isAvailable},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateBmsCollectionRecordList" parameterType="java.util.Arrays">
        <foreach collection="list" item="item" separator=";">
            update bms_collection_record
            set
            is_available = (case when (available_amount - #{item.availableAmount}) = 0 then 1 else 0 end),
            available_amount = available_amount - #{item.availableAmount}

            where id = #{item.id}
        </foreach>
    </update>

    <update id="batchModifyIncomeType" parameterType="java.util.Arrays">
        <foreach collection="list" item="item" separator=";">
            update bms_collection_record
            set income_type=#{item.incomeType}
            where id = #{item.id}
        </foreach>
    </update>

    <update id="deleteBmsCollectionRecordById" parameterType="java.lang.String">
        update  bms_collection_record set del_flag=1 where id = #{id}
    </update>

<!--    批量逻辑作废-->
    <update id="deleteBmsCollectionRecordByIds">
        update  bms_collection_record set del_flag=1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateBmsCollectionRecordStatusByIds">
        update bms_collection_record set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectBmsYsbillByRecordId" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        select
        distinct
        yshx.bill_codes as billCode
        from
        bms_collection_record pr
        LEFT JOIN bms_yshx_record_middle rm ON rm.record_id = pr.id  and rm.del_flag=0
        LEFT JOIN bms_ysbill_hxinfo yshx ON rm.hx_id = yshx.id
        where
        pr.del_flag=0
        and yshx.del_flag=0
        and pr.id = #{recordId}
    </select>

    <select id="selectBmsYsbillByBillCode" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain" resultType="com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain">
        select
        ys.bill_code as billCode,
        ys.bill_name as billName,
        ys.bill_type as billType,
        cli.id as clientId,
        cli.client_name as clientName,
        ys.hx_amount as hxAmount,
        IFNULL(ys.adjusted_amount,0) - IFNULL(ys.hx_amount,0) as whxAmount,
        max(ys.hx_time) as hxTime
        ,ys.hx_user_name as createBy
        ,ys.hx_time as createTime
        from bms_ysbillmain ys
        left join bms_clientinfo cli
        on cli.id = ys.client_id
        <where>
            ys.del_flag=0
            and ys.hx_state!=1
            <if test="billCodeList != null  and billCodeList.size>0">
                and ys.bill_code in
                <foreach item="item" collection="billCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by ys.bill_code
        order by ys.hx_time desc,ys.bill_code
    </select>

    <update id="deleteBmsCollectionRecordByIds2">
        update  bms_collection_record set del_flag=1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateBmsCollectionRecord2" parameterType="com.bbyb.joy.bms.domain.dto.settlement.BmsCollectionRecord">
        update bms_collection_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="clientId == null">client_id = null,</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="collectionCode != null">collection_code = #{collectionCode},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="collectionTime != null">collection_time = #{collectionTime},</if>
            <if test="collectionAmount != null">collection_amount = #{collectionAmount},</if>
            <if test="availableAmount != null">available_amount = #{availableAmount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="handler!=null">handler=#{handler},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="replyCode!=null">reply_code = #{replyCode},</if>
            <if test="transactionCode != null">transaction_code = #{transactionCode},</if>
            <if test="isAvailable!=null">is_available = #{isAvailable},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="hasCollectionRecordsWriteOff" resultType="java.lang.Long">
        select count(record_id) from  bms_yshx_record_middle where del_flag=0 and  record_id in
        <foreach item="recordId" collection="recordIds" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </select>
</mapper>