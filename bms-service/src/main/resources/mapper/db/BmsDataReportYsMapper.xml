<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsDataReportYsMapper">




    <resultMap type="com.bbyb.joy.bms.domain.dto.datareport.StateYsGroup" id="bmsCarrierExistenceVos">
        <result property="billCode" column="bill_code"/>
        <result property="billDate" column="bill_date" />
        <result property="createTimeStr" column="createTimeStr"/>
        <result property="ticketState" column="bill_state"/>
        <result property="companyId"    column="company_id" />
        <result property="clientPrimary" column="clientPrimary"/>
        <result property="clientSynergy" column="clientSynergy"/>
        <result property="clientAlliance" column="clientAlliance"/>
        <result property="clientBy" column="clientBy"/>
        <result property="total" column="total" />
        <result property="brandName" column="brand_name" />
        <result property="clientName" column="client_name" />
        <result property="clientCode" column="client_code"/>
        <result property="areaName" column="area_name" />
        <result property="openingName" column="opening_name" />
        <result property="createTime" column="create_time" />
    </resultMap>


    <select id="groupList" parameterType="com.bbyb.joy.bms.domain.dto.datareport.StateYsGroup" resultMap="bmsCarrierExistenceVos">
        SELECT
        ysm.bill_state,
        ysm.company_id,
        ysm.client_id,
        ysm.create_time,
        ci.client_name,
        ci.client_type,
        SUM(
        CASE
        ci.client_type
        WHEN 0 THEN
        ifnull( ysi.freight, 0 ) + ifnull( ysi.ultrafar_fee, 0 ) + ifnull( ysi.superframes_fee, 0 ) + ifnull( ysi.excess_fee, 0 ) + ifnull( ysi.delivery_fee, 0 ) + ifnull( ysi.Outboundsorting_fee, 0 ) + ifnull( ysi.shortbarge_fee, 0 ) + ifnull( ysi.return_fee, 0 ) + ifnull( ysi.exception_fee, 0 ) + ifnull( ysi.other_cost1, 0 ) + ifnull( ysi.other_cost2, 0 ) + ifnull( ysi.other_cost3, 0 ) + ifnull( ysi.other_cost4, 0 ) + ifnull( ysi.other_cost5, 0 ) + ifnull( ysi.other_cost6, 0 ) + ifnull( ysi.other_cost7, 0 ) + ifnull( ysi.other_cost8, 0 ) + ifnull( ysi.other_cost9, 0 ) + ifnull( ysi.other_cost10, 0 ) + ifnull( ysi.other_cost11, 0 ) + ifnull( ysi.other_cost12, 0 ) ELSE 0
        END
        ) AS clientSynergy,
        SUM(
        CASE
        ci.client_type
        WHEN 1 THEN
        ifnull( ysi.freight, 0 ) + ifnull( ysi.ultrafar_fee, 0 ) + ifnull( ysi.superframes_fee, 0 ) + ifnull( ysi.excess_fee, 0 ) + ifnull( ysi.delivery_fee, 0 ) + ifnull( ysi.Outboundsorting_fee, 0 ) + ifnull( ysi.shortbarge_fee, 0 ) + ifnull( ysi.return_fee, 0 ) + ifnull( ysi.exception_fee, 0 ) + ifnull( ysi.other_cost1, 0 ) + ifnull( ysi.other_cost2, 0 ) + ifnull( ysi.other_cost3, 0 ) + ifnull( ysi.other_cost4, 0 ) + ifnull( ysi.other_cost5, 0 ) + ifnull( ysi.other_cost6, 0 ) + ifnull( ysi.other_cost7, 0 ) + ifnull( ysi.other_cost8, 0 ) + ifnull( ysi.other_cost9, 0 ) + ifnull( ysi.other_cost10, 0 ) + ifnull( ysi.other_cost11, 0 ) + ifnull( ysi.other_cost12, 0 ) ELSE 0
        END
        ) AS clientPrimary,
        SUM(
        CASE
        ci.client_type
        WHEN 2 THEN
        ifnull( ysi.freight, 0 ) + ifnull( ysi.ultrafar_fee, 0 ) + ifnull( ysi.superframes_fee, 0 ) + ifnull( ysi.excess_fee, 0 ) + ifnull( ysi.delivery_fee, 0 ) + ifnull( ysi.Outboundsorting_fee, 0 ) + ifnull( ysi.shortbarge_fee, 0 ) + ifnull( ysi.return_fee, 0 ) + ifnull( ysi.exception_fee, 0 ) + ifnull( ysi.other_cost1, 0 ) + ifnull( ysi.other_cost2, 0 ) + ifnull( ysi.other_cost3, 0 ) + ifnull( ysi.other_cost4, 0 ) + ifnull( ysi.other_cost5, 0 ) + ifnull( ysi.other_cost6, 0 ) + ifnull( ysi.other_cost7, 0 ) + ifnull( ysi.other_cost8, 0 ) + ifnull( ysi.other_cost9, 0 ) + ifnull( ysi.other_cost10, 0 ) + ifnull( ysi.other_cost11, 0 ) + ifnull( ysi.other_cost12, 0 ) ELSE 0
        END
        ) AS clientAlliance,
        SUM(
        CASE
        ci.client_type
        WHEN 3 THEN
        ifnull( ysi.freight, 0 ) + ifnull( ysi.ultrafar_fee, 0 ) + ifnull( ysi.superframes_fee, 0 ) + ifnull( ysi.excess_fee, 0 ) + ifnull( ysi.delivery_fee, 0 ) + ifnull( ysi.Outboundsorting_fee, 0 ) + ifnull( ysi.shortbarge_fee, 0 ) + ifnull( ysi.return_fee, 0 ) + ifnull( ysi.exception_fee, 0 ) + ifnull( ysi.other_cost1, 0 ) + ifnull( ysi.other_cost2, 0 ) + ifnull( ysi.other_cost3, 0 ) + ifnull( ysi.other_cost4, 0 ) + ifnull( ysi.other_cost5, 0 ) + ifnull( ysi.other_cost6, 0 ) + ifnull( ysi.other_cost7, 0 ) + ifnull( ysi.other_cost8, 0 ) + ifnull( ysi.other_cost9, 0 ) + ifnull( ysi.other_cost10, 0 ) + ifnull( ysi.other_cost11, 0 ) + ifnull( ysi.other_cost12, 0 ) ELSE 0
        END
        ) AS clientBy,
        GROUP_CONCAT(DISTINCT ysm.id SEPARATOR ',') as billIds
        FROM bms_ysbillmain ysm
        LEFT JOIN bms_yscost_info ysi ON ysi.bill_id = ysm.id AND ysi.del_flag = 0
        LEFT JOIN bms_clientinfo ci ON ysi.client_id = ci.id AND ci.del_flag = 0
        WHERE ysm.del_flag = 0
        AND (
        IFNULL(ysm.fatherid, 0) = 0
        OR IFNULL(ysm.fatherid, 0) = ysm.id
        )
        <if test="companyIds!=null and companyIds.size>0">
            and ysm.company_id in
            <foreach collection="companyIds"  item="item" open="(" separator="," close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.beginBillData != null and params.beginBillData != '' and params.endBillData != null and params.endBillData != ''">
            and ysm.create_time between #{params.beginBillData} and #{params.endBillData}
        </if>
        <if test="clientList != null">
            and  ci.client_code in
            <foreach collection="clientList" item="client" open="(" separator="," close=")">
                #{client}
            </foreach>
        </if>
        GROUP BY
        ysm.bill_state
        <if test="groupType==2">
            , ysm.company_id
        </if>
    </select>


    <select id="detailsList" parameterType="com.bbyb.joy.bms.domain.dto.datareport.StateYsGroup" resultMap="bmsCarrierExistenceVos">
        SELECT
        ysm.id,
        ysm.bill_code,
        ysm.bill_state,
        ysm.bill_date,
        DATE_FORMAT(ysm.create_time, '%Y-%m-%d %H:%i:%s') AS createTimeStr,
        ysm.company_id ,
        ysi.expenses_code, -- 费用单号
        cli.client_name,
        cli.client_code,
        cli.brand_name,
        cli.opening_name,
        cli.area_name,
        CAST(ifnull( ysi.freight, 0 ) + ifnull( ysi.ultrafar_fee, 0 ) + ifnull( ysi.superframes_fee, 0 ) + ifnull( ysi.excess_fee, 0 ) + ifnull( ysi.delivery_fee, 0 ) + ifnull( ysi.Outboundsorting_fee, 0 ) + ifnull( ysi.shortbarge_fee, 0 ) + ifnull( ysi.return_fee, 0 ) + ifnull( ysi.exception_fee, 0 ) + ifnull( ysi.other_cost1, 0 ) + ifnull( ysi.other_cost2, 0 ) + ifnull( ysi.other_cost3, 0 ) + ifnull( ysi.other_cost4, 0 ) + ifnull( ysi.other_cost5, 0 ) + ifnull( ysi.other_cost6, 0 ) + ifnull( ysi.other_cost7, 0 ) + ifnull( ysi.other_cost8, 0 ) + ifnull( ysi.other_cost9, 0 ) + ifnull( ysi.other_cost10, 0 ) + ifnull( ysi.other_cost11, 0 ) + ifnull( ysi.other_cost12, 0 )
        as DECIMAL(18,2)) AS total
        FROM
        bms_ysbillmain ysm
        LEFT JOIN bms_yscost_info ysi ON ysi.bill_id = ysm.id AND ysi.del_flag = 0
        LEFT JOIN bms_clientinfo cli ON ysi.client_id=cli.id AND cli.del_flag=0
        <where>
            ysm.del_flag = 0
            AND (
            IFNULL(ysm.fatherid, 0) = 0
            OR IFNULL(ysm.fatherid, 0) = ysm.id
            )
            <if test="billIdArr!=null and billIdArr.size>0">
                and ysm.id in
                <foreach collection="billIdArr"  item="item" open="(" separator="," close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="companyIds!=null and companyIds.size>0">
                and ysm.company_id in
                <foreach collection="companyIds"  item="item" open="(" separator="," close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="ticketState!=null">
                and ysm.bill_state=#{ticketState}
            </if>
            <if test="companyId!=null">
                and ysm.company_id=#{companyId}
            </if>
            <if test="params.createTimeStart != null and params.createTimeStart != '' and params.createTimeEnd != null and params.createTimeEnd != ''">
                and ysm.bill_date between #{params.createTimeStart} and #{params.createTimeEnd}
            </if>
            <if test="areaName!=null and areaName!=''">
                and cli.area_name like concat('%',#{areaName},'%')
            </if>
            <if test="clientName!=null and clientName!=''">
                and cli.client_name like concat('%',#{clientName},'%')
            </if>
            <if test="clientList != null and clientList.size>0">
                and  cli.client_code in
                <foreach collection="clientList" item="client" open="(" separator="," close=")">
                    #{client}
                </foreach>
            </if>
        </where>

    </select>

    <select id="getBillingInformation" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT IFNULL(a.ticket_amount, 0) AS ticketAmount,
            a.bill_code                AS billCode,
            a.bill_name                AS billName,
            a.bill_date                AS billDate,
            b.client_name              AS clientName
        FROM bms_ysbillmain a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        WHERE a.id = #{id}
    </select>

    <select id="selectBmsYsBillFeeTotalInfo"  resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsbillmainExportAll">
        SELECT
        a.sheet1Name,
        GROUP_CONCAT(DISTINCT  a.billType) AS billTypeStr,
        a.companyId,
        a.billDate,
        a.endDate,
        IFNULL(a.billState,1) as billState,
        a.iscontainsExceFee,
        a.billName,
        a.adjustedAmount,
        a.ysAmount,
        a.billAmount,
        a.billType,
        a.clientId,
        a.clientName,
        a.expensesType,
        a.feeSettlementDate,
        a.addId,
        a.fixId,
        sum(IFNULL(a.glFeeCwAmount,0)) glFeeCwAmount,
        sum(IFNULL(a.glFeeLcAmount,0)) glFeeLcAmount,
        sum(IFNULL(a.glFeeLdAmount,0)) glFeeLdAmount,
        sum(IFNULL(a.ccFeeCwAmount,0)) ccFeeCwAmount,
        sum(IFNULL(a.ccFeeLcAmount,0)) ccFeeLcAmount,
        sum(IFNULL(a.ccFeeLdAmount,0)) ccFeeLdAmount,
        sum(IFNULL(a.fjFeeCwRkAmount,0)) fjFeeCwRkAmount,
        sum(IFNULL(a.fjFeeLcRkAmount,0)) fjFeeLcRkAmount,
        sum(IFNULL(a.fjFeeLdRkAmount,0)) fjFeeLdRkAmount,
        sum(IFNULL(a.fjFeeCwCkAmount,0)) fjFeeCwCkAmount,
        sum(IFNULL(a.fjFeeLcCkAmount,0)) fjFeeLcCkAmount,
        sum(IFNULL(a.fjFeeLdCkAmount,0)) fjFeeLdCkAmount,
        sum(IFNULL(a.fjFeeLcRkZhAmount,0)) fjFeeRkZhAmount,
        sum(IFNULL(a.fjFeeLdRkPhAmount,0)) fjFeeRkPhAmount,
        sum(IFNULL(a.fjFeeLcCkZhAmount,0)) fjFeeCkZhAmount,
        sum(IFNULL(a.fjFeeLdCkPhAmount,0)) fjFeeCkPhAmount,
        sum(IFNULL(a.ccShortbargeFeeCW,0)) ccShortbargeFeeCW,
        sum(IFNULL(a.ccShortbargeFeeLD,0)) ccShortbargeFeeLD,
        sum(IFNULL(a.ccShortbargeFeeLC,0)) ccShortbargeFeeLC,
        sum(IFNULL(a.rkShortbargeFeeCW,0)) rkShortbargeFeeCW,
        sum(IFNULL(a.rkShortbargeFeeLD,0)) rkShortbargeFeeLD,
        sum(IFNULL(a.rkShortbargeFeeLC,0)) rkShortbargeFeeLC,
        sum(IFNULL(a.ccReturnFee,0)) ccReturnFee ,
        sum(IFNULL(a.ccOtherCost1,0)) ccOtherCost1,
        sum(IFNULL(a.ccOtherCost2,0)) ccOtherCost2,
        sum(IFNULL(a.ccOtherCost3,0)) ccOtherCost3,
        sum(IFNULL(a.ccOtherCost4,0)) ccOtherCost4,
        sum(IFNULL(a.ccOtherCost5,0)) ccOtherCost5,
        sum(IFNULL(a.ccOtherCost6,0)) ccOtherCost6,
        sum(IFNULL(a.ccOtherCost7,0)) ccOtherCost7,
        sum(IFNULL(a.ccOtherCost8,0)) ccOtherCost8,
        sum(IFNULL(a.ccOtherCost9,0)) ccOtherCost9,
        sum(IFNULL(a.ccOtherCost10,0)) ccOtherCost10,
        sum(IFNULL(a.ccOtherCost11,0)) ccOtherCost11,
        sum(IFNULL(a.ccOtherCost12,0)) ccOtherCost12,
        sum(IFNULL(a.psReduceFee,0)) psReduceFee,
        sum(IFNULL(a.psFreight,0)) psFreight,
        sum(IFNULL(a.psDeliveryFee,0)) psDeliveryFee,
        sum(IFNULL(a.psOutboundsortingFee,0)) psOutboundsortingFee,
        sum(IFNULL(a.psSuperframesFee,0)) psSuperframesFee,
        sum(IFNULL(a.psExcessFee,0)) psExcessFee,
        sum(IFNULL(a.psShortbargeFee,0)) psShortbargeFee,
        sum(IFNULL(a.psReturnFee,0)) psReturnFee,
        sum(IFNULL(a.psUltrafarFee,0)) psUltrafarFee,
        sum(IFNULL(a.psOtherCost1,0)) psOtherCost1,
        sum(IFNULL(a.psOtherCost2,0)) psOtherCost2,
        sum(IFNULL(a.psOtherCost3,0)) psOtherCost3,
        sum(IFNULL(a.psOtherCost4,0)) psOtherCost4,
        sum(IFNULL(a.psOtherCost5,0)) psOtherCost5,
        sum(IFNULL(a.psOtherCost6,0)) psOtherCost6,
        sum(IFNULL(a.psOtherCost7,0)) psOtherCost7,
        sum(IFNULL(a.psOtherCost8,0)) psOtherCost8,
        sum(IFNULL(a.psOtherCost9,0)) psOtherCost9,
        sum(IFNULL(a.psOtherCost10,0)) psOtherCost10,
        sum(IFNULL(a.psOtherCost11,0)) psOtherCost11,
        sum(IFNULL(a.psOtherCost12,0)) psOtherCost12,
        (sum(IFNULL(a.glFeeCwAmount,0)) + sum(IFNULL(a.glFeeLcAmount,0)) + sum(IFNULL(a.glFeeLdAmount,0)) + sum(IFNULL(a.ccFeeCwAmount,0)) + sum(IFNULL(a.ccFeeLcAmount,0)) + sum(IFNULL(a.ccFeeLdAmount,0))
        + sum(IFNULL(a.ccShortbargeFeeCW,0)) + sum(IFNULL(a.ccShortbargeFeeLD,0)) +sum(IFNULL(a.ccShortbargeFeeLC,0)) +
        sum(IFNULL(a.rkShortbargeFeeCW,0)) +sum(IFNULL(a.rkShortbargeFeeLD,0)) +sum(IFNULL(a.rkShortbargeFeeLC,0)) +
        sum(IFNULL(a.ccReturnFee,0))  +
        sum(IFNULL(a.fjFeeCwRkAmount,0)) + sum(IFNULL(a.fjFeeLcRkAmount,0)) + sum(IFNULL(a.fjFeeLdRkAmount,0)) + sum(IFNULL(a.fjFeeCwCkAmount,0)) + sum(IFNULL(a.fjFeeLcCkAmount,0)) +
        sum(IFNULL(a.fjFeeLdCkAmount,0)) +sum(IFNULL(a.fjFeeLcRkZhAmount,0)) + sum(IFNULL(a.fjFeeLdRkPhAmount,0))  +
        sum(IFNULL(a.ccOtherCost1,0))+ sum(IFNULL(a.ccOtherCost2,0)) + sum(IFNULL(a.ccOtherCost3,0)) + sum(IFNULL(a.ccOtherCost4,0)) + sum(IFNULL(a.ccOtherCost5,0)) +
        sum(IFNULL(a.ccOtherCost6,0)) + sum(IFNULL(a.ccOtherCost7,0)) + sum(IFNULL(a.ccOtherCost8,0))+ sum(IFNULL(a.ccOtherCost9,0))+ sum(IFNULL(a.ccOtherCost10,0)) + sum(IFNULL(a.ccOtherCost11,0)) +
        sum(IFNULL(a.ccOtherCost12,0)) ) as ccFeeSum,
        (sum(IFNULL(a.psFreight,0)) +    sum(IFNULL(a.psDeliveryFee,0)) + sum(IFNULL(a.psOutboundsortingFee,0)) + sum(IFNULL(a.psSuperframesFee,0)) + sum(IFNULL(a.psShortbargeFee,0))+
        sum(IFNULL(a.psExcessFee,0)) +  sum(IFNULL(a.psReturnFee,0))   + sum(IFNULL(a.psUltrafarFee,0))        + sum(IFNULL(a.psOtherCost1,0))+
        sum(IFNULL(a.psOtherCost2,0)) + sum(IFNULL(a.psOtherCost3,0))  + sum(IFNULL(a.psOtherCost4,0))         + sum(IFNULL(a.psOtherCost5,0))     + sum(IFNULL(a.psOtherCost6,0)) +
        sum(IFNULL(a.psOtherCost7,0)) + sum(IFNULL(a.psOtherCost8,0))  + sum(IFNULL(a.psOtherCost9,0))         + sum(IFNULL(a.psOtherCost10,0))    + sum(IFNULL(a.psOtherCost11,0)) +
        sum(IFNULL(a.psOtherCost12,0)) + sum(IFNULL(a.psReduceFee,0)) )  as psFeeSum,

        (sum(IFNULL(a.glFeeCwAmount,0)) + sum(IFNULL(a.glFeeLcAmount,0)) + sum(IFNULL(a.glFeeLdAmount,0)) + sum(IFNULL(a.ccFeeCwAmount,0)) + sum(IFNULL(a.ccFeeLcAmount,0)) + sum(IFNULL(a.ccFeeLdAmount,0))
        + sum(IFNULL(a.ccShortbargeFeeCW,0)) + sum(IFNULL(a.ccShortbargeFeeLD,0)) +sum(IFNULL(a.ccShortbargeFeeLC,0)) +
        sum(IFNULL(a.rkShortbargeFeeCW,0)) +sum(IFNULL(a.rkShortbargeFeeLD,0)) +sum(IFNULL(a.rkShortbargeFeeLC,0)) +
        sum(IFNULL(a.ccReturnFee,0))  +
        sum(IFNULL(a.fjFeeCwRkAmount,0)) + sum(IFNULL(a.fjFeeLcRkAmount,0)) + sum(IFNULL(a.fjFeeLdRkAmount,0)) + sum(IFNULL(a.fjFeeCwCkAmount,0)) + sum(IFNULL(a.fjFeeLcCkAmount,0)) +
        sum(IFNULL(a.fjFeeLdCkAmount,0)) +sum(IFNULL(a.fjFeeLcRkZhAmount,0)) + sum(IFNULL(a.fjFeeLdRkPhAmount,0))  +
        sum(IFNULL(a.ccOtherCost1,0))+ sum(IFNULL(a.ccOtherCost2,0)) + sum(IFNULL(a.ccOtherCost3,0)) + sum(IFNULL(a.ccOtherCost4,0)) + sum(IFNULL(a.ccOtherCost5,0)) +
        sum(IFNULL(a.ccOtherCost6,0)) + sum(IFNULL(a.ccOtherCost7,0)) + sum(IFNULL(a.ccOtherCost8,0))+ sum(IFNULL(a.ccOtherCost9,0))+ sum(IFNULL(a.ccOtherCost10,0)) + sum(IFNULL(a.ccOtherCost11,0)) +
        sum(IFNULL(a.ccOtherCost12,0)) ) as ccFeeSumAll,
        (sum(IFNULL(a.psFreight,0)) +    sum(IFNULL(a.psDeliveryFee,0)) + sum(IFNULL(a.psOutboundsortingFee,0)) + sum(IFNULL(a.psSuperframesFee,0)) + sum(IFNULL(a.psShortbargeFee,0))+
        sum(IFNULL(a.psExcessFee,0)) +  sum(IFNULL(a.psReturnFee,0))   + sum(IFNULL(a.psUltrafarFee,0))        + sum(IFNULL(a.psOtherCost1,0))+
        sum(IFNULL(a.psOtherCost2,0)) + sum(IFNULL(a.psOtherCost3,0))  + sum(IFNULL(a.psOtherCost4,0))         + sum(IFNULL(a.psOtherCost5,0))     + sum(IFNULL(a.psOtherCost6,0)) +
        sum(IFNULL(a.psOtherCost7,0)) + sum(IFNULL(a.psOtherCost8,0))  + sum(IFNULL(a.psOtherCost9,0))         + sum(IFNULL(a.psOtherCost10,0))    + sum(IFNULL(a.psOtherCost11,0)) +
        sum(IFNULL(a.psOtherCost12,0)) + sum(IFNULL(a.psReduceFee,0)) )  as psFeeSumAll,a.storagefee
        ,a.freightFee
        ,a.messageFee
        ,a.platformFee
        ,a.brandFee
        ,a.deliveryFee
        ,a.exceptionFee
        ,a.airtransportFee
        ,a.consumablesFee
        ,a.unloadFee
        ,a.gpsFee
        ,a.scmFee
        from
        (
        SELECT
        ys.id,
        ys.company_id as companyId,
        ys.bill_date as billDate,
        ys.bill_state as billState,
        ys.iscontains_exce_fee as iscontainsExceFee,
        ys.end_date as endDate,
        ys.bill_name as billName,
        ys.adjusted_amount as adjustedAmount,
        ys.ys_amount as ysAmount,
        ys.bill_amount as billAmount,
        ys.bill_type as billType,
        ad.id addId,
        fix.id fixId,
        ys.exception_fee exceptionFee,
        case when ysi.expenses_type=1 then 1
        else 2 end as expensesType,
        concat(IFNULL(cli.area,''),"地区",IFNULL(cli.brand_name,''),"品牌",replace(ys.bill_date,"-","年"),"月份物流费用应收对账单")	 sheet1Name,
        cli.client_name clientName,
        cli.id as clientId,
        concat(DATE_FORMAT(DATE_SUB(STR_TO_DATE(concat("2022-02",'-',cli.payment_days),'%Y-%m-%d'),INTERVAL 1 MONTH),'%Y年%m月%d日'),"至",
        DATE_FORMAT(STR_TO_DATE(concat("2022-02",'-',cli.payment_days+1),'%Y-%m-%d'),'%Y年%m月%d日')) feeSettlementDate,
        case when IFNULL(ysi.expenses_attribute,3) = 3 and ysi.expenses_type in (2,3,4)  then IFNULL(ysi.ultrafar_fee,0) else 0 end glFeeCwAmount,
        case when IFNULL(ysi.expenses_attribute,3) = 4 and ysi.expenses_type in (2,3,4)  then IFNULL(ysi.ultrafar_fee,0) else 0 end glFeeLcAmount,
        case when IFNULL(ysi.expenses_attribute,3) = 5 and ysi.expenses_type in (2,3,4)  then IFNULL(ysi.ultrafar_fee,0) else 0 end glFeeLdAmount,
        case when IFNULL(ysi.expenses_attribute,3) = 3 and ysi.expenses_type in (2,3,4)  then IFNULL(ysi.freight,0) else 0 end ccFeeCwAmount,
        case when IFNULL(ysi.expenses_attribute,3) = 4 and ysi.expenses_type in (2,3,4)  then IFNULL(ysi.freight,0) else 0 end ccFeeLcAmount,
        case when IFNULL(ysi.expenses_attribute,3) = 5 and ysi.expenses_type in (2,3,4)  then IFNULL(ysi.freight,0) else 0 end ccFeeLdAmount,
        case when IFNULL(ysi.expenses_attribute,3) = 3 and ysi.expenses_type = 3  then IFNULL(ysi.superframes_fee,0)+IFNULL(excess_fee,0) else 0 end fjFeeCwRkAmount,
        case when IFNULL(ysi.expenses_attribute,3) = 4 and ysi.expenses_type = 3  then IFNULL(ysi.superframes_fee,0)+IFNULL(excess_fee,0) else 0 end fjFeeLcRkAmount,
        case when IFNULL(ysi.expenses_attribute,3) = 5 and ysi.expenses_type = 3  then IFNULL(ysi.superframes_fee,0)+IFNULL(excess_fee,0) else 0 end fjFeeLdRkAmount,
        case when IFNULL(ysi.expenses_attribute,3) = 3 and ysi.expenses_type = 2  then IFNULL(ysi.superframes_fee,0)+IFNULL(excess_fee,0) else 0 end fjFeeCwCkAmount,
        case when IFNULL(ysi.expenses_attribute,3) = 4 and ysi.expenses_type = 2  then IFNULL(ysi.superframes_fee,0)+IFNULL(excess_fee,0) else 0 end fjFeeLcCkAmount,
        case when IFNULL(ysi.expenses_attribute,3) = 5 and ysi.expenses_type = 2  then IFNULL(ysi.superframes_fee,0)+IFNULL(excess_fee,0) else 0 end fjFeeLdCkAmount,
        case when ysi.expenses_type = 3  then IFNULL(ysi.delivery_fee,0) else 0 end fjFeeLcRkZhAmount,
        case when ysi.expenses_type = 2  then IFNULL(ysi.delivery_fee,0) else 0 end fjFeeLdRkPhAmount,
        case when IFNULL(ysi.expenses_attribute,4) = 4 and ysi.expenses_type = 2  then IFNULL(ysi.delivery_fee,0) else 0 end fjFeeLcCkZhAmount,
        case when IFNULL(ysi.expenses_attribute,4) = 5 and ysi.expenses_type = 2  then IFNULL(ysi.delivery_fee,0) else 0 end fjFeeLdCkPhAmount,
        case when ysi.expenses_type in(2,4) and IFNULL(ysi.expenses_attribute,3) = 3  then IFNULL(ysi.shortbarge_fee,0) else 0 end  ccShortbargeFeeCW,
        case when ysi.expenses_type in(2,4) and IFNULL(ysi.expenses_attribute,3) = 4  then IFNULL(ysi.shortbarge_fee,0) else 0 end  ccShortbargeFeeLC,
        case when ysi.expenses_type in(2,4) and IFNULL(ysi.expenses_attribute,3) = 5  then IFNULL(ysi.shortbarge_fee,0) else 0 end  ccShortbargeFeeLD,
        case when ysi.expenses_type in(3) and IFNULL(ysi.expenses_attribute,3) = 3 then IFNULL(ysi.shortbarge_fee,0) else 0 end  rkShortbargeFeeCW,
        case when ysi.expenses_type in(3) and IFNULL(ysi.expenses_attribute,3) = 4 then IFNULL(ysi.shortbarge_fee,0) else 0 end   rkShortbargeFeeLC,
        case when ysi.expenses_type in(3) and IFNULL(ysi.expenses_attribute,3) = 5 then IFNULL(ysi.shortbarge_fee,0) else 0 end rkShortbargeFeeLD,
        case when ysi.expenses_type in(2,3,4)  then IFNULL(ysi.return_fee,0) else 0 end  ccReturnFee,
        case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost1,0) else 0 end ccOtherCost1,
        case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost2,0) else 0 end ccOtherCost2,
        case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost3,0) else 0 end ccOtherCost3,
        case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost4,0) else 0 end ccOtherCost4,
        case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost5,0) else 0 end ccOtherCost5,
        case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost6,0) else 0 end ccOtherCost6,
        case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost7,0) else 0 end ccOtherCost7,
        case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost8,0) else 0 end ccOtherCost8,
        case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost9,0) else 0 end ccOtherCost9,
        case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost10,0) else 0 end ccOtherCost10,
        case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost11,0) else 0 end ccOtherCost11,
        case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost12,0) else 0 end ccOtherCost12,
        case when ysi.expenses_type = 1 then IFNULL(ysi.freight,0) else 0 end  psFreight,
        case when ysi.expenses_type = 1 then IFNULL(ysi.delivery_fee,0) else 0 end  psDeliveryFee,
        case when ysi.expenses_type = 1 then IFNULL(ysi.outboundsorting_fee,0) else 0 end  psOutboundsortingFee,
        case when ysi.expenses_type = 1 then IFNULL(ysi.superframes_fee,0) else 0 end  psSuperframesFee,
        case when ysi.expenses_type = 1 then IFNULL(ysi.excess_fee,0) else 0 end  psExcessFee,
        case when ysi.expenses_type = 1 then IFNULL(ysi.reduce_fee,0) else 0 end  psReduceFee,
        case when ysi.expenses_type = 1 then IFNULL(ysi.shortbarge_fee,0) else 0 end  psShortbargeFee,
        case when ysi.expenses_type = 1 then IFNULL(ysi.return_fee,0) else 0 end  psReturnFee,
        case when ysi.expenses_type = 1 then IFNULL(ysi.ultrafar_fee,0) else 0 end  psUltrafarFee,
        case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost1,0) else 0 end psOtherCost1,
        case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost2,0) else 0 end psOtherCost2,
        case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost3,0) else 0 end psOtherCost3,
        case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost4,0) else 0 end psOtherCost4,
        case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost5,0) else 0 end psOtherCost5,
        case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost6,0) else 0 end psOtherCost6,
        case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost7,0) else 0 end psOtherCost7,
        case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost8,0) else 0 end psOtherCost8,
        case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost9,0) else 0 end psOtherCost9,
        case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost10,0) else 0 end psOtherCost10,
        case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost11,0) else 0 end psOtherCost11,
        case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost12,0) else 0 end psOtherCost12
        ,IFNULL(ys.ppstorage_fee,0)+IFNULL(ys.zpstorage_fee,0) as storageFee
        ,IFNULL(ys.ppfreight_fee,0)+IFNULL(ys.zpfreight_fee,0) as freightFee
        ,IFNULL(ys.pp_message_fee,0)+IFNULL(ys.zp_message_fee,0) as messageFee
        ,IFNULL(ys.pp_platform_fee,0)+IFNULL(ys.zp_platform_fee,0) as platformFee
        ,IFNULL(ys.pp_brand_fee,0)+IFNULL(ys.zp_brand_fee,0) as brandFee
        ,IFNULL(ys.pp_delivery_fee,0)+IFNULL(ys.zp_delivery_fee,0) as deliveryFee
        ,IFNULL(ys.pp_airtransport_fee,0)+IFNULL(ys.zp_airtransport_fee,0) as airtransportFee
        ,IFNULL(ys.pp_consumables_fee,0)+IFNULL(ys.zp_consumables_fee,0) as consumablesFee
        ,IFNULL(ys.pp_unload_fee,0)+IFNULL(ys.zp_unload_fee,0) as unloadFee
        ,IFNULL(ys.pp_gps_fee,0)+IFNULL(ys.zp_gps_fee,0) as gpsFee
        ,IFNULL(ys.pp_scm_fee,0)+IFNULL(ys.zp_scm_fee,0) as scmFee
        from bms_ysbillmain ys
        left join bms_yscost_info ysi on ysi.bill_id = ys.id and ysi.del_flag = 0
        left join bms_clientinfo cli on ys.client_id = cli.id and cli.del_flag = 0
        left join bms_addedfee ad on ad.bill_id = ys.id and ad.del_flag=0 and ad.settle_type = 1
        left join bms_fixedfee fix on fix.bill_id = ys.id and fix.del_flag=0 and fix.settle_type = 1
        where 1=1
        and ys.id in
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        GROUP BY ysi.id
        ) a where 1=1
    </select>

    <select id="getGoupRouteinfoListByBillId" parameterType="Long" resultType="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeinfo">
        SELECT
            1 AS id,
            info.line_code AS lineCode,
            info.line_name AS lineName,
            rou.store_num AS storeNum,
            dc.count AS dcStoreNum,
            sto.count AS distributionStoreNum,
            SUM(IFNULL(info.total_number,0)) AS totalNumber,
            SUM(IFNULL(info.total_boxes,0)) AS totalBoxes,
            SUM(IFNULL(info.total_weight,0)) AS totalWeight,
            SUM(IFNULL(info.total_volume,0)) AS totalVolume,
            SUM(IFNULL(info.total_cargo_value,0)) AS cargoValue
        FROM bms_ysbillmain a
        INNER JOIN bms_yscost_info cos ON a.id = cos.bill_id and cos.del_flag = 0
        INNER JOIN bms_ysexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id and mid.del_flag = 0
        INNER JOIN bms_trans_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN bms_routeinfo rou ON info.line_code = rou.route_code
        LEFT JOIN (
            SELECT
                sum(a.count) AS count,a.id
            FROM (
                SELECT
                    1  AS count,
                    a.id
                FROM bms_ysbillmain a
                INNER JOIN bms_yscost_info cos ON a.id = cos.bill_id
                INNER JOIN bms_ysexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id
                INNER JOIN bms_trans_code_info info ON mid.code_pk_id = info.pk_id
                WHERE a.del_flag = 0
                AND (a.id = #{id} or a.fatherid=#{id})
                AND cos.expenses_type = 1
                GROUP BY info.receiving_store_code
            ) a
        ) sto ON sto.id = a.id
        LEFT JOIN (
            SELECT
                sum(a.count) AS count,
                a.id
            FROM (
                SELECT
                    1  AS count,
                    a.id
                FROM bms_ysbillmain a
                INNER JOIN bms_yscost_info cos ON a.id = cos.bill_id
                INNER JOIN bms_ysexpenses_middle mid ON cos.id = mid.main_pk_id and mid.del_flag = 0
                INNER JOIN bms_trans_code_info info ON mid.code_pk_id = info.pk_id
                where a.del_flag = 0
                AND (a.id = #{id} OR a.fatherid = #{id})
                AND cos.expenses_type =1
                GROUP BY info.receiving_store_code,info.scheduling_code
            ) a
        ) dc on dc.id = a.id
        WHERE a.del_flag = 0 and (a.id = #{id} OR a.fatherid = #{id})
        AND cos.expenses_type =1
    </select>

        <select id="getStorageByBillId" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
            SELECT
                cos.signing_date AS instorageTime,
                info.relate_code AS stockCode,
                info.temperature_type AS temperatureType,
                '' AS temperatureName,
                info.warehouse_code AS warehouseCode,
                wah.warehouse_name AS warehouseName,
                IFNULL(info.total_cargo_value,0) AS aqty,
                IFNULL(info.total_pallet_number,0) AS palletNumber,
                0 AS palletRuler,
                IFNULL(info.total_boxes,0) AS totalBoxes,
                0 AS totalNumber,
                IFNULL(info.total_weight,0) AS totalWeight,
                IFNULL(info.total_volume,0) AS totalVolume,
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.freight,0) ELSE SUM(IFNULL(cos.freight,0)) END AS freight,  -- 存储费
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.outboundsorting_fee,0) ELSE SUM(IFNULL(cos.outboundsorting_fee,0)) END AS outboundsortingFee, -- 送货费
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.ultrafar_fee,0) ELSE SUM(IFNULL(cos.ultrafar_fee,0)) END AS ultrafarFee, -- 管理处置费
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.superframes_fee,0) ELSE SUM(IFNULL(cos.superframes_fee,0)) END AS superframesFee, -- 整箱分拣费
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.excess_fee,0) ELSE SUM(IFNULL(cos.excess_fee,0)) END AS excessFee, -- 拆零分拣费
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.reduce_fee,0) ELSE SUM(IFNULL(cos.reduce_fee,0)) END AS reduceFee, -- 减点费（订单）
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.adjust_fee,0) ELSE SUM(IFNULL(cos.adjust_fee,0)) END AS adjustFee, -- 调账费
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.shortbarge_fee,0) ELSE SUM(IFNULL(cos.shortbarge_fee,0)) END AS shortbargeFee, -- 操作费
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.return_fee,0) ELSE SUM(IFNULL(cos.return_fee,0)) END AS returnFee, -- 制单费
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.delivery_fee,0) ELSE SUM(IFNULL(cos.delivery_fee,0)) END AS deliveryFee, -- 装卸费
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.exception_fee,0) ELSE SUM(IFNULL(cos.exception_fee,0)) END AS exceptionFee, -- 异常赔付费
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost1,0) ELSE SUM(IFNULL(cos.other_cost1,0)) END AS otherCost1,
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost2,0) ELSE SUM(IFNULL(cos.other_cost2,0)) END AS otherCost2,
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost3,0) ELSE SUM(IFNULL(cos.other_cost3,0)) END AS otherCost3,
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost4,0) ELSE SUM(IFNULL(cos.other_cost4,0)) END AS otherCost4,
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost5,0) ELSE SUM(IFNULL(cos.other_cost5,0)) END AS otherCost5,
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost6,0) ELSE SUM(IFNULL(cos.other_cost6,0)) END AS otherCost6,
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost7,0) ELSE SUM(IFNULL(cos.other_cost7,0)) END AS otherCost7,
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost8,0) ELSE SUM(IFNULL(cos.other_cost8,0)) END AS otherCost8,
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost9,0) ELSE SUM(IFNULL(cos.other_cost9,0)) END AS otherCost9,
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost10,0) ELSE SUM(IFNULL(cos.other_cost10,0)) END AS otherCost10,
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost11,0) ELSE SUM(IFNULL(cos.other_cost11,0)) END AS otherCost11,
                CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost12,0) ELSE SUM(IFNULL(cos.other_cost12,0)) END AS otherCost12,
                case
                when cos.expenses_dimension IN (2,3,4)
                THEN IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
                +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
                +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
                ELSE SUM(IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
                +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
                +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0))
                end otherSum,
                case
                when cos.expenses_dimension IN (2,3,4)
                THEN IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
                +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
                +IFNULL(cos.exception_fee,0)
                ELSE SUM(IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
                +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
                +IFNULL(cos.exception_fee,0))
                end basicsSum,
                SUM(IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
                +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
                +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
                +IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
                +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
                +IFNULL(cos.exception_fee,0))
                AS sumAmt
                ,cos.fee_type_first AS feeTypeFirst -- 费用大类
                ,'' AS feeTypeFirstName
                ,cos.is_increment AS isIncrement -- 是否为增值费用
                ,SUM(cos.total_extra_fee_number) AS totalQuantity -- 附加费总数量
                ,cos.total_extra_fee_price AS price -- 附加费单价
                ,GROUP_CONCAT(cos.id SEPARATOR ',') AS id
                ,'' AS automaticBillingRemark -- 自动计费备注
                ,GROUP_CONCAT((CASE WHEN cos.charge_type = 2
                THEN '手动计费'
                when cos.charge_type = 1
                THEN '自动计费'
                ELSE ''
                END) SEPARATOR ',') AS chargeTypeName -- 计费类型
                ,GROUP_CONCAT(cos.expenses_code SEPARATOR ',') AS expensesCode  -- 费用单号
                ,'' AS costDimensionName -- 费用维度
                ,info.company_id AS companyId -- 单据所属
                ,GROUP_CONCAT(cos.oper_by SEPARATOR ',') AS operBy -- 计费人
                ,GROUP_CONCAT(cos.remarks SEPARATOR ',') AS remarksJf -- 计费备注
                ,GROUP_CONCAT(cos.adjust_remark SEPARATOR ',') AS adjustRemark -- 调账备注
            FROM bms_ysbillmain a
            INNER JOIN bms_yscost_info cos ON a.id = cos.bill_id AND cos.del_flag =0
            LEFT JOIN bms_ysexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0 AND mid.code_type = 2
            LEFT JOIN bms_storage_code_info info ON mid.code_pk_id = info.pk_id and info.cost_mode = 1
            AND (
                (IFNULL(cos.expenses_attribute,-1) IN (3,4,5,6) AND
                (CASE WHEN IFNULL(info.temperature_type,'')='CW' THEN '3'
                WHEN IFNULL(info.temperature_type,'')='LC' THEN '4'
                WHEN IFNULL(info.temperature_type,'')='LD' THEN '5'
                WHEN IFNULL(info.temperature_type,'')='HW' THEN '6'
                ELSE '' END
                ) = cos.expenses_attribute) OR (IFNULL(cos.expenses_attribute,-1) IN (-1,1,2))
            )
            LEFT JOIN mdm_warehouseinfo wah ON info.warehouse_code = wah.warehouse_code
            WHERE cos.del_flag =0
                AND info.del_flag =0
                AND a.id IN
                <foreach collection="ids" item="ids" open="(" separator="," close=")">
                    #{ids}
                </foreach>
                AND cos.expenses_type IN (4)
            GROUP BY CASE WHEN cos.expenses_dimension IN (2,3,4) THEN cos.expenses_code ELSE info.id end,CASE WHEN cos.expenses_dimension IN (2,3,4) THEN info.id END
            ORDER BY cos.id ASC,info.signing_date ASC
    </select>

    <select id="getStorageGoodsDetailByBillId" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            cos.signing_date AS instorageTime,
            null AS temperatureType,
            null AS temperatureName,
            cli.client_code AS clientCode,
            cli.client_name AS clientName,
            goodsinfo.sku_code AS skuCode,
            sku.sku_name AS skuName,
            goodsinfo.product_date AS productDate,
            0 AS stockQuantity,
            goodsinfo.box_rule_code AS boxType,
            0 AS boxLength,
            0 AS weightWidth,
            IFNULL(goodsinfo.total_weight,0) AS weightHeight,
            0 AS calculatePallet,
            IFNULL(goodsinfo.total_number,0) AS integerNumber,
            0 AS greaterThan5,
            0 AS lessThan5,
            0 AS totalPallet
        FROM bms_ysbillmain a
        LEFT JOIN bms_yscost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_ysexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.code_type = 2 AND mid.del_flag = 0
        LEFT JOIN bms_storage_code_detail_info goodsinfo ON goodsinfo.main_pk_id = mid.code_pk_id
        LEFT JOIN mdm_warehouseinfo wah ON a.warehouse_code = wah.warehouse_code
        LEFT JOIN bms_clientinfo cli ON cli.id = a.client_id
        LEFT JOIN mdm_skuinfo sku ON sku.sku_code = goodsinfo.sku_code
        WHERE a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        AND cos.expenses_type IN (4)
        GROUP BY goodsinfo.id
        ORDER BY cos.signing_date DESC;
    </select>

    <select id="getStorageinWarehouseGoodsDetailByBillId" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
        info.signing_date as signingDate,
        info.relate_code as relateCode,
        cos.expenses_code as expensesCode,
        IFNULL(goodsinfo.total_boxes,0) as totalBoxes,
        IFNULL(goodsinfo.total_odd_boxes,0) as oddBoxes,
        IFNULL(goodsinfo.total_volume,0) as totalVolume,
        IFNULL(info.total_number,0) as palletNumber,
        sku.sku_code as skuCode,
        sku.sku_name as skuName,
        dic.dict_label as temperatureName,
        goodsinfo.box_rule as boxType,
        sku.unit as unit,
        ext.automatic_billing_remark as automaticBillingRemark,
        IFNULL(goodsinfo.total_number,0) as contentsNumber,
        IFNULL(goodsinfo.total_weight,0) as weight,
        IFNULL(goodsinfo.total_weight,0)/1000 as totalWeight,
        IFNULL(cos.freight,0) as freight,
        IFNULL(cos.outboundsorting_fee,0) as outboundsortingFee,
        IFNULL(cos.ultrafar_fee,0) as ultrafarFee,
        IFNULL(cos.superframes_fee,0) as superframesFee,
        IFNULL(cos.excess_fee,0) as excessFee,
        IFNULL(cos.reduce_fee,0) as reduceFee,
        IFNULL(cos.adjust_fee,0) as adjustFee,
        IFNULL(cos.shortbarge_fee,0) as shortbargeFee,
        IFNULL(cos.return_fee,0) as returnFee,
        IFNULL(cos.delivery_fee,0) as deliveryFee,
        IFNULL(cos.exception_fee,0) as exceptionFee,
        IFNULL(cos.other_cost1,0) as otherCost1,
        IFNULL(cos.other_cost2,0) as otherCost2,
        IFNULL(cos.other_cost3,0) as otherCost3,
        IFNULL(cos.other_cost4,0) as otherCost4,
        IFNULL(cos.other_cost5,0) as otherCost5,
        IFNULL(cos.other_cost6,0) as otherCost6,
        IFNULL(cos.other_cost7,0) as otherCost7,
        IFNULL(cos.other_cost8,0) as otherCost8,
        IFNULL(cos.other_cost9,0) as otherCost9,
        IFNULL(cos.other_cost10,0) as otherCost10,
        IFNULL(cos.other_cost11,0) as otherCost11,
        IFNULL(cos.other_cost12,0) as otherCost12,
        IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
        +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
        +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0) as otherSum,

        IFNULL(cos.freight,0)+IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
        +IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
        +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0)
        as basicsSum,

        IFNULL(cos.freight,0)+IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
        +IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
        +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0)+IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
        +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
        +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)  as sumAmt,
        cos.id
        ,case when (IFNULL(cos.other_cost1,0)!=0 or IFNULL(cos.other_cost2,0)!=0 or IFNULL(cos.other_cost3,0)!=0 or IFNULL(cos.other_cost4,0)!=0 or
        IFNULL(cos.other_cost5,0)!=0 or IFNULL(cos.other_cost6,0)!=0 or IFNULL(cos.other_cost7,0)!=0 or IFNULL(cos.other_cost8,0)!=0 or
        IFNULL(cos.other_cost9,0)!=0 or IFNULL(cos.other_cost10,0)!=0 or IFNULL(cos.other_cost11,0)!=0 or IFNULL(cos.other_cost12,0)!=0 or
        IFNULL(cos.freight,0)!=0 or IFNULL(cos.delivery_fee,0)!=0 or IFNULL(cos.outboundsorting_fee,0)!=0 or IFNULL(cos.shortbarge_fee,0)!=0 or
        IFNULL(cos.ultrafar_fee,0)!=0 or IFNULL(cos.superframes_fee,0)!=0 or IFNULL(cos.excess_fee,0)!=0 or IFNULL(cos.return_fee,0)!=0 or
        IFNULL(cos.exception_fee,0)!=0) then 1 else 0 end as hideHigh,
        info.order_code as orderId,
        info.code_type as orderType,
        info.total_cw_pallet_number as cwPalletNumber,
        info.total_lc_pallet_number as lcPalletNumber,
        info.total_ld_pallet_number as ldPalletNumber
        FROM bms_ysbillmain a
        INNER JOIN bms_yscost_info cos ON a.id = cos.bill_id and cos.del_flag = 0
        INNER JOIN bms_ysexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id and mid.del_flag = 0 and mid.code_type = 2
        INNER JOIN bms_storage_code_info info ON mid.code_pk_id = info.id and info.del_flag = 0
        INNER JOIN bms_storage_code_detail_info goodsinfo ON goodsinfo.main_pk_id = info.pk_id and goodsinfo.del_flag = 0
        and (
        (IFNULL(cos.expenses_attribute,-1) in (3,4,5,6) and
        (
        case
        when IFNULL(goodsinfo.temperature_type,'')='CW' then '3'
        when IFNULL(goodsinfo.temperature_type,'')='LC' then '4'
        when IFNULL(goodsinfo.temperature_type,'')='LD' then '5'
        when IFNULL(goodsinfo.temperature_type,'')='HW' then '6'
        else '' end
        ) = cos.expenses_attribute) OR (IFNULL(cos.expenses_attribute,-1) IN (-1,1,2))
        )
        LEFT JOIN mdm_warehouseinfo wah ON info.warehouse_code = wah.warehouse_code
        LEFT JOIN bms_yscost_extend ext ON ext.expenses_id = cos.id
        LEFT JOIN sys_dict_data dic ON goodsinfo.temperature_type = dic.dict_value AND dict_type = 'warm_zone_type'
        LEFT JOIN bms_clientinfo cli ON cli.client_code = info.client_code
        LEFT JOIN mdm_skuinfo sku ON sku.sku_code = goodsinfo.sku_code
        left JOIN bms_ysbillcode_detailinfo infode on infode.ysbill_id = info.id
        LEFT JOIN sys_dict_data dic2 on dic2.dict_type = 'cost_dimension' and dic2.dict_value=cos.expenses_dimension
        WHERE a.del_flag = 0
        AND (a.id = #{id} OR a.fatherid=#{id})
        AND cos.expenses_type IN ( 3 )
        GROUP BY goodsinfo.id,cos.id
        ORDER BY cos.expenses_code DESC,info.relate_code DESC,info.signing_date ASC
    </select>

    <select id="getStorageOutWarehouseGoodsDetailByBillId" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            info.signing_date as signingDate,
            info.relate_code as relateCode,
            cos.expenses_code as expensesCode,
            IFNULL(info.total_cargo_value,0) as cargoValue,
            sku.sku_code as skuCode,
            sku.sku_name as skuName,
            dic.dict_label as temperatureName,
            goodsinfo.box_rule as boxType,
            sku.unit as unit,
            '' as automaticBillingRemark,
            IFNULL(goodsinfo.total_boxes,0) as totalBoxes,
            IFNULL(goodsinfo.total_odd_boxes,0) as oddBoxes,
            IFNULL(goodsinfo.total_number,0) as contentsNumber,
            IFNULL(goodsinfo.total_weight,0) as weight,
            IFNULL(goodsinfo.total_weight,0)/1000 as totalWeight,
            IFNULL(cos.freight,0) as freight,
            IFNULL(cos.outboundsorting_fee,0) as outboundsortingFee,
            IFNULL(cos.ultrafar_fee,0) as ultrafarFee,
            IFNULL(cos.superframes_fee,0) as superframesFee,
            IFNULL(cos.excess_fee,0) as excessFee,
            IFNULL(cos.reduce_fee,0) as reduceFee,
            IFNULL(cos.adjust_fee,0) as adjustFee,
            IFNULL(cos.shortbarge_fee,0) as shortbargeFee,
            IFNULL(cos.return_fee,0) as returnFee,
            IFNULL(cos.delivery_fee,0) as deliveryFee,
            IFNULL(cos.exception_fee,0) as exceptionFee,
            IFNULL(cos.other_cost1,0) as otherCost1,
            IFNULL(cos.other_cost2,0) as otherCost2,
            IFNULL(cos.other_cost3,0) as otherCost3,
            IFNULL(cos.other_cost4,0) as otherCost4,
            IFNULL(cos.other_cost5,0) as otherCost5,
            IFNULL(cos.other_cost6,0) as otherCost6,
            IFNULL(cos.other_cost7,0) as otherCost7,
            IFNULL(cos.other_cost8,0) as otherCost8,
            IFNULL(cos.other_cost9,0) as otherCost9,
            IFNULL(cos.other_cost10,0) as otherCost10,
            IFNULL(cos.other_cost11,0) as otherCost11,
            IFNULL(cos.other_cost12,0) as otherCost12,
            IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
            +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
            +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0) as otherSum,

            IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
            +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
            +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0)
            as basicsSum,

            IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
            +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
            +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
            +IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
            +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
            +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0)
            as sumAmt,
            cos.id,
            info.warehouse_code as warehouseCode,
            info.receiving_store_name as receivingStore
            ,case when (IFNULL(cos.other_cost1,0)!=0 or IFNULL(cos.other_cost2,0)!=0 or IFNULL(cos.other_cost3,0)!=0 or IFNULL(cos.other_cost4,0)!=0 or
            IFNULL(cos.other_cost5,0)!=0 or IFNULL(cos.other_cost6,0)!=0 or IFNULL(cos.other_cost7,0)!=0 or IFNULL(cos.other_cost8,0)!=0 or
            IFNULL(cos.other_cost9,0)!=0 or IFNULL(cos.other_cost10,0)!=0 or IFNULL(cos.other_cost11,0)!=0 or IFNULL(cos.other_cost12,0)!=0 or
            IFNULL(cos.freight,0)!=0 or IFNULL(cos.delivery_fee,0)!=0 or IFNULL(cos.outboundsorting_fee,0)!=0 or IFNULL(cos.shortbarge_fee,0)!=0 or
            IFNULL(cos.ultrafar_fee,0)!=0 or IFNULL(cos.superframes_fee,0)!=0 or IFNULL(cos.excess_fee,0)!=0 or IFNULL(cos.return_fee,0)!=0 or
            IFNULL(cos.exception_fee,0)!=0) then 1 else 0 end as hideHigh,
            info.order_code as orderId,
            info.code_type as orderType
            FROM bms_ysbillmain a
            INNER JOIN bms_yscost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
            INNER JOIN bms_ysexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0 and mid.code_type = 2
            INNER JOIN bms_storage_code_info info ON mid.code_pk_id = info.pk_id and info.del_flag = 0
            INNER JOIN bms_storage_code_detail_info goodsinfo ON goodsinfo.main_pk_id = info.pk_id and goodsinfo.del_flag = 0
            AND (
            (IFNULL(cos.expenses_attribute,-1) in (3,4,5,6) and
            (case when IFNULL(goodsinfo.temperature_type,'')='CW' then '3'
            when IFNULL(goodsinfo.temperature_type,'')='LC' then '4'
            when IFNULL(goodsinfo.temperature_type,'')='LD' then '5'
            when IFNULL(goodsinfo.temperature_type,'')='HW' then '6'
            else '' end
            ) = cos.expenses_attribute) or (IFNULL(cos.expenses_attribute,-1) in (-1,1,2))
            )
        LEFT JOIN mdm_warehouseinfo wah ON info.warehouse_code = wah.warehouse_code
        LEFT JOIN sys_dict_data dic ON goodsinfo.temperature_type = dic.dict_value AND dict_type = 'warm_zone_type'
        LEFT JOIN bms_clientinfo cli ON cli.client_code = info.client_code
        LEFT JOIN mdm_skuinfo sku ON sku.sku_code = goodsinfo.sku_code
        WHERE a.del_flag = 0
        AND (a.id = #{id} or a.fatherid=#{id})
        AND cos.expenses_type IN ( 2 )
        GROUP BY goodsinfo.id,cos.id
        order by cos.expenses_code,info.relate_code desc,info.signing_date asc
    </select>

    <select id="getDistribution1GoodsDetailByBillId" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
        info.destination_city as destinationCity,
        info.line_code as lineCode,
        info.line_name as lineName,
        info.receiving_store_code as storeCode,
        info.receiving_store_name as receivingStore,
        SUM(IFNULL(info.total_number,0)) as totalNumber,
        case when IFNULL(st.date_type,2) = 2 then DATE_FORMAT(info.signing_date,'%Y-%m-%d')
        when IFNULL(st.date_type,2) = 1 then DATE_FORMAT(info.order_date,'%Y-%m-%d')
        else DATE_FORMAT(info.signing_date,'%Y-%m-%d')  end
        as signingDate,
        cos.bill_date as billDate,
        0 as nearStoreKm,
        SUM(IFNULL(info.total_over_number,0))  as overNum,
        dc.count as dcNum
        FROM bms_ysbillmain a
        LEFT JOIN bms_yscost_info cos ON a.id = cos.bill_id and cos.del_flag = 0
        LEFT JOIN bms_ysexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id and mid.code_type = 1 and mid.del_flag = 0
        LEFT JOIN bms_trans_code_info info ON mid.main_pk_id = info.id
        LEFT JOIN mdm_warehouseinfo wah ON info.warehouse_code = wah.warehouse_code
        LEFT JOIN bms_clientinfo cli ON cli.client_code = info.client_code
        LEFT JOIN pub_settledate_setting st ON st.client_id = cos.client_id
        left join (
                SELECT sum(a.count) AS count,
                    a.destination_city,
                    a.line_code,
                    a.store_code,
                    a.bill_date
                    FROM (
                        SELECT
                            1 AS count,
                            info.destination_city,
                            info.line_code,
                            info.receiving_store_code as store_code,
                            cos.bill_date
                        FROM bms_ysbillmain a
                        LEFT JOIN bms_yscost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
                        LEFT JOIN bms_ysexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0
                        LEFT JOIN bms_trans_code_info info ON mid.code_pk_id = info.pk_id
                        WHERE 1=1
                        AND a.id in
                        <foreach collection="ids" item="ids" open="(" separator="," close=")">
                            #{ids}
                        </foreach>
                        AND cos.expenses_type =1
                        GROUP BY info.receiving_store_code,info.scheduling_code
                    ) a
            GROUP BY a.line_code, a.store_code, a.bill_date
        ) dc ON dc.destination_city = info.destination_city
        AND dc.store_code = info.receiving_store_code
        AND dc.bill_date = cos.bill_date
        AND IFNULL(dc.line_code,'') = IFNULL(info.line_code,'')
        WHERE a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        AND cos.expenses_type IN ( 1 )
        GROUP BY info.destination_city,info.line_code,info.receiving_store_code,cos.bill_date
        ORDER BY cos.bill_date ASC
    </select>

    <select id="getDetailDays" parameterType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            info.destination_city AS destinationCity,
            info.line_code AS lineCode,
            info.line_name AS lineName,
            info.receiving_store_name AS receivingStore,
            SUM(IFNULL(info.total_number,0)) AS totalNumber,
            CASE WHEN IFNULL(st.date_type,2) = 2 THEN info.signing_date
            WHEN IFNULL(st.date_type,2) = 1 THEN info.order_date
            ELSE info.signing_date END AS signingDate,
            0 AS nearStoreKm
        FROM bms_ysbillmain a
        LEFT JOIN bms_yscost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_ysexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.code_type = 1 AND mid.del_flag = 0
        LEFT JOIN bms_trans_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN mdm_warehouseinfo wah ON info.warehouse_code = wah.warehouse_code
        LEFT JOIN bms_clientinfo cli ON cli.id = a.client_id
        LEFT JOIN pub_settledate_setting st ON st.client_id = cos.client_id
        WHERE a.id = #{id} OR a.fatherid=#{id}
            AND cos.expenses_type IN ( 1 )
            AND cos.del_flag =0
            AND mid.del_flag=0
            AND info.del_flag=0
            <if test="destinationCity!=null">
                AND info.destination_city = #{destinationCity}
            </if>
            <if test="lineName!=null">
                AND info.line_name = #{lineName}
            </if>
            <if test="receivingStore!=null">
                AND info.receiving_store = #{receivingStore}
            </if>
            <if test="billDate!=null">
                AND cos.bill_date = #{billDate}
            </if>
            GROUP BY info.destination_city,info.line_name,info.receiving_store_code,
            (case when IFNULL(st.date_type,2) = 2 then DATE_FORMAT(info.signing_date,'%Y-%m-%d')
            when IFNULL(st.date_type,2) = 1 then DATE_FORMAT(info.order_date,'%Y-%m-%d')
            else DATE_FORMAT(info.signing_date,'%Y-%m-%d')  end)
            order by (case when IFNULL(st.date_type,2) = 2 then DATE_FORMAT(info.signing_date,'%Y-%m-%d')
            when IFNULL(st.date_type,2) = 1 then DATE_FORMAT(info.order_date,'%Y-%m-%d')
            else DATE_FORMAT(info.signing_date,'%Y-%m-%d')  end) asc,cos.id desc
    </select>

    <select id="getDetailDaysTwo"  resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            info.destination_city as destinationCity,
            info.line_code as lineCode,
            info.receiving_store_code as storeCode,
            SUM(IFNULL(info.total_number,0)) as totalNumber,
            ifnull((case when IFNULL(st.date_type,2) = 2 then info.signing_date
            when IFNULL(st.date_type,2) = 1 then info.order_date
            else info.signing_date  end),info.order_date) as signingDate
        FROM bms_ysbillmain a
        LEFT JOIN bms_yscost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_ysexpenses_middle mid ON cos.main_code_id = mid.main_code_id AND cos.del_flag = 0
        LEFT JOIN bms_trans_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN mdm_warehouseinfo wah ON info.warehouse_code = wah.warehouse_code
        LEFT JOIN bms_clientinfo cli ON cli.client_code = info.client_code
        LEFT join pub_settledate_setting st on st.client_id = cos.client_id
        WHERE cos.expenses_type IN ( 1 )
        AND a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        GROUP BY info.destination_city,info.line_code,info.receiving_store_code,
        (case when IFNULL(st.date_type,2) = 2 then DATE_FORMAT(info.signing_date,'%Y-%m-%d')
        when IFNULL(st.date_type,2) = 1 then DATE_FORMAT(info.order_date,'%Y-%m-%d')
        else DATE_FORMAT(info.signing_date,'%Y-%m-%d')  end)
        order by (case when IFNULL(st.date_type,2) = 2 then DATE_FORMAT(info.signing_date,'%Y-%m-%d')
        when IFNULL(st.date_type,2) = 1 then DATE_FORMAT(info.order_date,'%Y-%m-%d')
        else DATE_FORMAT(info.signing_date,'%Y-%m-%d')  end) asc,cos.id DESC
    </select>

    <select id="getDistribution2GoodsDetailByBillId" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
        info.client_name as clientName,
        info.receiving_store_code as storeCode,
        cli.brand_name as brandName,
        cli.region as region,
        '' as consignee,
        '' as telephone,
        info.destination_address as destinationAddress,
        IFNULL(info.total_number,0) as totalNumber,
        '已收货' as receivingStatus,
        info.total_cargo_value as cargoValue,
        info.order_date as orderDate,
        info.signing_date as signingDate,
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.freight,0) else SUM(IFNULL(cos.freight,0)) end as freight, /* 存储费 */
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.outboundsorting_fee,0) else SUM(IFNULL(cos.outboundsorting_fee,0)) end as outboundsortingFee, /* 送货费 */
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.ultrafar_fee,0) else SUM(IFNULL(cos.ultrafar_fee,0)) end as ultrafarFee, /* 管理处置费 */
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.superframes_fee,0) else SUM(IFNULL(cos.superframes_fee,0)) end as superframesFee, /* 整箱分拣费 */
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.excess_fee,0) else SUM(IFNULL(cos.excess_fee,0)) end as excessFee, /* 拆零分拣费 */
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.reduce_fee,0) else SUM(IFNULL(cos.reduce_fee,0)) end as reduceFee, /* 减点费（订单） */
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.adjust_fee,0) else SUM(IFNULL(cos.adjust_fee,0)) end as adjustFee, /* 调账费 */
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.shortbarge_fee,0) else SUM(IFNULL(cos.shortbarge_fee,0)) end as shortbargeFee, /* 操作费 */
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.return_fee,0) else SUM(IFNULL(cos.return_fee,0)) end as returnFee, /* 制单费 */
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.delivery_fee,0) else SUM(IFNULL(cos.delivery_fee,0)) end as deliveryFee, /* 装卸费 */
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.exception_fee,0) else SUM(IFNULL(cos.exception_fee,0)) end as exceptionFee, /* 异常赔付费 */
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.other_cost1,0) else SUM(IFNULL(cos.other_cost1,0)) end as otherCost1,
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.other_cost2,0) else SUM(IFNULL(cos.other_cost2,0)) end as otherCost2,
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.other_cost3,0) else SUM(IFNULL(cos.other_cost3,0)) end as otherCost3,
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.other_cost4,0) else SUM(IFNULL(cos.other_cost4,0)) end as otherCost4,
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.other_cost5,0) else SUM(IFNULL(cos.other_cost5,0)) end as otherCost5,
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.other_cost6,0) else SUM(IFNULL(cos.other_cost6,0)) end as otherCost6,
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.other_cost7,0) else SUM(IFNULL(cos.other_cost7,0)) end as otherCost7,
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.other_cost8,0) else SUM(IFNULL(cos.other_cost8,0)) end as otherCost8,
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.other_cost9,0) else SUM(IFNULL(cos.other_cost9,0)) end as otherCost9,
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.other_cost10,0) else SUM(IFNULL(cos.other_cost10,0)) end as otherCost10,
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.other_cost11,0) else SUM(IFNULL(cos.other_cost11,0)) end as otherCost11,
        case when cos.expenses_dimension in (2,3,4) then IFNULL(cos.other_cost12,0) else SUM(IFNULL(cos.other_cost12,0)) end as otherCost12,

        case
        when cos.expenses_dimension in (2,3,4)
        then IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
        +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
        +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
        else SUM(IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
        +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
        +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0))
        end otherSum,

        case
        when cos.expenses_dimension in (2,3,4)
        then
        IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
        +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
        +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0)
        else
        SUM(IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
        +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
        +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0))
        end basicsSum,

        case when
        cos.expenses_dimension in (2,3,4)
        then IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
        +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
        +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
        +IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
        +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
        +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0)
        else SUM(IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
        +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
        +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
        +IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
        +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
        +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0))
        end sumAmt ,/* 总费用 */

        GROUP_CONCAT(cos.id SEPARATOR ',') as id,
        GROUP_CONCAT(cos.expenses_code SEPARATOR ',') as expensesCode, /* 计费单号 */
        info.relate_code as relateCode, /* 订单号 */
        GROUP_CONCAT((case when cos.charge_type = 2 then '手动计费'
        when cos.charge_type = 1 then '自动计费'
        else '' end) SEPARATOR ',') as chargeTypeName, /* 计费类型 */
        cos.oper_time as operTime, /* 计费时间 */
        '' as costDimensionName, /* 费用维度 */
        '' as feeFlagName, /* 费用标识 */
        GROUP_CONCAT(cos.adjust_remark SEPARATOR ',') as adjustRemark, /* 调账备注 */
        info.company_id as companyId, /* 单据所属 */
        info.create_time as createTime, /* 新增时间 */
        info.signing_date as signingDate, /* 签收时间 */
        info.client_name as clientName, /* 客户名称 */
        info.client_code as clientCode, /* 客户编码 */
        GROUP_CONCAT(cos.oper_by SEPARATOR ',') as operBy, /* 计费人 */
        GROUP_CONCAT(cos.remarks SEPARATOR ',') as remarksJf, /* 计费备注 */
        IFNULL(info.total_weight,0) as totalWeight, /* 总重量 */
        IFNULL(info.total_volume,0) as totalVolume, /* 总体积 */
        IFNULL(info.total_cargo_value,0) as cargoValue, /* 总货值 */
        IFNULL(info.total_boxes,0) as totalBoxes, /* 总箱数 */
        info.receiving_store_name as receivingStore, /* 收货门店 */
        info.delivery_mode as deliveryMode, /* 配送类型 */
        case when info.delivery_mode=1 then '专配'
        when info.delivery_mode =2 then '共配'
        when info.delivery_mode =3 then '零担'
        when info.delivery_mode =4 then '快递'
        when info.delivery_mode =5 then '整车'
        else '' end as deliveryModeName, /* 配送类型名称 */
        info.delivery_code as deliveryCode, /* 发货地编码 */
        wah.warehouse_name as warehouseName, /* 发货仓库 */
        info.originating_province as provinceOrigin, /* 始发省 */
        info.originating_city as originatingCity, /* 始发市 */
        info.originating_area as originatingArea, /* 始发区 */
        info.destination_Province as destinationProvince, /* 目的省 */
        info.destination_city as destinationCity, /* 目的市 */
        info.destination_area as destinationArea, /* 目的区 */
        a.bill_code as billCode, /* 账单编号 */
        GROUP_CONCAT(cos.bill_date SEPARATOR ',') as billDate, /* 账期 */
        cos.fee_type_first as feeTypeFirst, /* 费用大类 */
        '' as feeTypeFirstName,
        cos.is_increment as isIncrement, /* 是否为增值费 */
        SUM(IFNULL(cos.total_extra_fee_number,0)) as totalQuantity, /* 附加费总数量 */
        IFNULL(cos.total_extra_fee_price,0) as price, /* 附加费单价 */
        '' as automaticBillingRemark,
        case when (IFNULL(cos.other_cost1,0)!=0 or IFNULL(cos.other_cost2,0)!=0 or IFNULL(cos.other_cost3,0)!=0 or
        IFNULL(cos.other_cost4,0)!=0 or
        IFNULL(cos.other_cost5,0)!=0 or IFNULL(cos.other_cost6,0)!=0 or IFNULL(cos.other_cost7,0)!=0 or
        IFNULL(cos.other_cost8,0)!=0 or
        IFNULL(cos.other_cost9,0)!=0 or IFNULL(cos.other_cost10,0)!=0 or IFNULL(cos.other_cost11,0)!=0 or
        IFNULL(cos.other_cost12,0)!=0 or
        IFNULL(cos.freight,0)!=0 or IFNULL(cos.delivery_fee,0)!=0 or IFNULL(cos.outboundsorting_fee,0)!=0 or
        IFNULL(cos.shortbarge_fee,0)!=0 or
        IFNULL(cos.ultrafar_fee,0)!=0 or IFNULL(cos.superframes_fee,0)!=0 or IFNULL(cos.excess_fee,0)!=0 or
        IFNULL(cos.return_fee,0)!=0 or
        IFNULL(cos.exception_fee,0)!=0) then 1 else 0 end as hideHigh,
        info.originating_address as originatingAddress,
        info.line_code as lineCode,
        info.line_name as lineName,
        GROUP_CONCAT(cos.remarks SEPARATOR ',') as remarks
        FROM bms_ysbillmain a
        LEFT JOIN bms_yscost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_ysexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0 and mid.code_type = 1
        LEFT JOIN bms_trans_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN mdm_warehouseinfo wah ON info.warehouse_code = wah.warehouse_code
        LEFT JOIN bms_clientinfo cli ON cli.client_code = info.client_code
        WHERE a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        AND cos.expenses_type IN ( 1 )
        GROUP BY cos.pk_id
        ORDER BY cos.pk_id asc,cos.signing_date ASC
    </select>

    <select id="getTransportGoodsDetailByBillId" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            info.id AS id,
            info.relate_code AS relateCode,
            info.order_date AS orderDate,
            info.receiving_store_code AS storeCode,
            info.receiving_store_name AS storeName,
            sku.sku_code AS skuCode,
            IFNULL(sku.sku_name,goodsinfo.sku_name) AS skuName,
            sku.unit,
            sku.specification,
            IFNULL(goodsinfo.total_number,0) AS contentsNumber,
            IFNULL(goodsinfo.total_boxes,0) AS totalBoxes,
            IFNULL(goodsinfo.total_boxes,0) AS sumBoxes,
            IFNULL(goodsinfo.total_weight,0) AS totalWeight,
            IFNULL(goodsinfo.total_volume,0) AS totalVolume,
            IFNULL(goodsinfo.total_amount,0) AS totalAmount,
            IFNULL(sku.weight,0) AS weight,
            IFNULL(sku.volume,0) AS volume
        FROM bms_ysbillmain a
        LEFT JOIN bms_yscost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_ysexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0 AND mid.code_type = 1
        LEFT JOIN bms_trans_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN bms_trans_code_detail_info goodsinfo ON goodsinfo.main_pk_id = mid.code_pk_id and goodsinfo.del_flag = 0
        LEFT JOIN mdm_warehouseinfo wah ON info.warehouse_code = wah.warehouse_code
        LEFT JOIN bms_clientinfo cli ON cli.id = a.client_id
        LEFT JOIN mdm_skuinfo sku ON sku.sku_code = goodsinfo.sku_code
        WHERE a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        AND cos.expenses_type IN ( 1 )
        GROUP BY info.relate_code,sku.sku_code
        ORDER BY cos.expenses_code DESC,info.relate_code DESC,info.signing_date ASC
    </select>


    <select id="getFixedExpensesByBillId" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            fix.id AS id,
            fix.expenses_code AS expensesCode,
            fix.item_id AS itemId,
            '' AS itemName,
            IFNULL( fix.amount, 0 ) AS amount,
            fix.frequency AS frequency,
            fix.start_date AS startDate,
            fix.end_date AS endDate,
            fix.oper_time AS operTime,
            cli.client_name AS clientName,
            fix.remark AS remarks
        FROM bms_fixedfee fix
        LEFT JOIN bms_ysbillmain bill ON bill.id = fix.bill_id
        LEFT JOIN bms_clientinfo cli ON cli.id = fix.client_id AND cli.del_flag =0
        WHERE fix.del_flag =0
        AND bill.id in
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        AND fix.del_flag =0
        ORDER BY fix.item_id DESC,fix.oper_time DESC
    </select>

    <select id="getValueAddedFeeByBillId" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            ad.id AS id,
            ad.expenses_code AS expensesCode,
            ad.relate_code AS relateCode,
            DATE_FORMAT( ad.charging_date, '%Y-%m-%d %H:%i:%s' ) AS chargingDate,
            ad.item_olevel_id AS itemOlevelId,
            '' AS itemOlevelName,
            ad.item_id AS itemId,
            '' AS itemName,
            ad.amount AS amount,
            IFNULL( ad.number, 0 ) AS skuNumber,
            '' AS unit,
            ad.fee_source AS feeSource,
            ad.order_source AS orderSource,
            DATE_FORMAT( ad.creat_date, '%Y-%m-%d' ) AS operTime,
            ad.warehouse_code AS warehouseCode,
            mw.warehouse_name AS warehouseName,
            cli.client_name AS clientName,
            IFNULL( ad.amount, 0 ) AS amount,
            ad.remark AS remarks,
            ad.fee_belong AS feeBelong,
            CASE
            WHEN ad.fee_belong=1 THEN '运输增值'
            WHEN ad.fee_belong=2 THEN '仓储增值'
            ELSE ''
            END AS feeBelongDesc
        FROM bms_addedfee ad
        LEFT JOIN bms_ysbillmain bill ON bill.id = ad.bill_id
        LEFT JOIN bms_clientinfo cli ON cli.client_code = ad.client_code AND cli.del_flag =0
        LEFT JOIN mdm_warehouseinfo mw ON mw.warehouse_code = ad.warehouse_code
        WHERE ad.del_flag =0
        AND bill.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        ORDER BY ad.item_id DESC,ad.creat_date DESC
    </select>


    <select id="getFixedExpensesByBillIdGroupBy" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT fix.oper_time             AS operTime
            , fix.expenses_code          AS expensesCode
            , cli.client_name            AS clientName
            , ''                         AS itemName
            , SUM(IFNULL(fix.amount, 0)) AS amount
            , fix.remark                 AS remarks
        FROM bms_fixedfee fix
        LEFT JOIN bms_ysbillmain bill ON bill.id = fix.bill_id
        LEFT JOIN bms_clientinfo cli ON cli.id = fix.client_id AND cli.del_flag = 0
        WHERE fix.del_flag = 0
        AND bill.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        GROUP BY fix.item_id
        ORDER BY fix.oper_time DESC
    </select>

    <select id="getValueAddedFeeByBillIdGroupBy" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT DATE_FORMAT(ad.creat_date, '%Y-%m-%d') AS operTime
        , ad.relate_code                         AS relateCode
        , ad.expenses_code                       AS expensesCode
        , cli.client_name                        AS clientName
        , SUM(IFNULL(ad.number, 0))              AS skuNumber
        , ''                                     AS unit
        , ''                                     AS itemName
        , SUM(IFNULL(ad.amount, 0))              AS amount
        , ad.remark                              AS remarks
        , ad.fee_belong                          AS feeBelong
        FROM bms_addedfee ad
        LEFT JOIN bms_ysbillmain bill ON bill.id = ad.bill_id
        LEFT JOIN bms_clientinfo cli ON cli.client_code = ad.client_code AND cli.del_flag = 0
        WHERE ad.del_flag = 0
        AND (bill.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>)
        AND ad.settle_type = #{settleType}
        GROUP BY ad.item_id,ad.fee_belong
        ORDER BY ad.creat_date DESC
    </select>


    <select id="inWarehouseSummaryMain" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            cos.order_date AS orderDate, <!-- 订单日期 -->
            cos.signing_date AS signingDate, <!-- 入库时间 -->
            '' AS relateCode, <!-- 入库单号 -->
            '' AS orderNo, <!-- 外部单号 -->
            cos.charge_type AS chargeType, <!-- 计费类型 -->
            GROUP_CONCAT((CASE
            WHEN cos.charge_type = 2
            THEN '手动计费'
            WHEN cos.charge_type = 1
            THEN '自动计费'
            ELSE ''
            END) SEPARATOR ',')  AS chargeTypeName, <!-- 计费类型 -->
            GROUP_CONCAT(cos.expenses_code SEPARATOR ',') AS expensesCode, <!-- 费用单号 -->
            '' AS costDimensionName, <!-- 费用维度 -->
            IFNULL(cos.total_boxes,0) AS totalBoxes, <!-- 总箱数 -->
            IFNULL(cos.total_volume,0) AS totalVolume, <!-- 总体积 -->
            0 AS palletNumber, <!-- 总托数 -->
            '' AS temperatureName, <!-- 配送方式 -->
            '' AS automaticBillingRemark,
            IFNULL(cos.total_weight,0) AS weight, <!-- 总重量(KG) -->
            IFNULL(cos.total_weight,0)/1000 AS totalWeight, <!-- 总重量(T) -->
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.freight,0) ELSE SUM(IFNULL(cos.freight,0)) END AS freight,  <!-- 存储费 -->
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.outboundsorting_fee,0) ELSE SUM(IFNULL(cos.outboundsorting_fee,0)) END AS outboundsortingFee, <!-- 送货费 -->
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.ultrafar_fee,0) ELSE SUM(IFNULL(cos.ultrafar_fee,0)) END AS ultrafarFee, <!-- 管理处置费 -->
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.superframes_fee,0) ELSE SUM(IFNULL(cos.superframes_fee,0)) END AS superframesFee, <!-- 整箱分拣费 -->
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.excess_fee,0) ELSE SUM(IFNULL(cos.excess_fee,0)) END AS excessFee, <!-- 拆零分拣费 -->
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.reduce_fee,0) ELSE SUM(IFNULL(cos.reduce_fee,0)) END AS reduceFee, <!-- 减点费（订单） -->
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.adjust_fee,0) ELSE SUM(IFNULL(cos.adjust_fee,0)) END AS adjustFee, <!-- 调账费 -->
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.shortbarge_fee,0) ELSE SUM(IFNULL(cos.shortbarge_fee,0)) END AS shortbargeFee, <!-- 操作费 -->
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.return_fee,0) ELSE SUM(IFNULL(cos.return_fee,0)) END AS returnFee, <!-- 制单费 -->
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.delivery_fee,0) ELSE SUM(IFNULL(cos.delivery_fee,0)) END AS deliveryFee, <!-- 装卸费 -->
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.exception_fee,0) ELSE SUM(IFNULL(cos.exception_fee,0)) END AS exceptionFee, <!-- 异常赔付费 -->
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost1,0) ELSE SUM(IFNULL(cos.other_cost1,0)) END AS otherCost1,
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost2,0) ELSE SUM(IFNULL(cos.other_cost2,0)) END AS otherCost2,
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost3,0) ELSE SUM(IFNULL(cos.other_cost3,0)) END AS otherCost3,
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost4,0) ELSE SUM(IFNULL(cos.other_cost4,0)) END AS otherCost4,
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost5,0) ELSE SUM(IFNULL(cos.other_cost5,0)) END AS otherCost5,
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost6,0) ELSE SUM(IFNULL(cos.other_cost6,0)) END AS otherCost6,
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost7,0) ELSE SUM(IFNULL(cos.other_cost7,0)) END AS otherCost7,
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost8,0) ELSE SUM(IFNULL(cos.other_cost8,0)) END AS otherCost8,
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost9,0) ELSE SUM(IFNULL(cos.other_cost9,0)) END AS otherCost9,
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost10,0) ELSE SUM(IFNULL(cos.other_cost10,0)) END AS otherCost10,
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost11,0) ELSE SUM(IFNULL(cos.other_cost11,0)) END AS otherCost11,
            CASE WHEN cos.expenses_dimension IN (2,3,4) THEN IFNULL(cos.other_cost12,0) ELSE SUM(IFNULL(cos.other_cost12,0)) END AS otherCost12,
            case
            when cos.expenses_dimension IN (2,3,4)
            THEN IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
            +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
            +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
            ELSE SUM(IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
            +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
            +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0))
            END otherSum,

            case
            when cos.expenses_dimension IN (2,3,4)
            THEN IFNULL(cos.freight,0)+IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
            +IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
            +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0)
            ELSE SUM(IFNULL(cos.freight,0)+IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
            +IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
            +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0))
            END basicsSum,

            CASE WHEN
            cos.expenses_dimension IN (2,3,4)
            THEN IFNULL(cos.freight,0)+IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
            +IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
            +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0)+IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
            +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
            +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
            ELSE SUM(IFNULL(cos.freight,0)+IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
            +IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
            +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0)+IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
            +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
            +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0))
            END sumAmt ,<!-- 总费用 -->

            GROUP_CONCAT(cos.id SEPARATOR ',') AS id
            ,CASE WHEN (IFNULL(cos.other_cost1,0)!=0 or IFNULL(cos.other_cost2,0)!=0 or IFNULL(cos.other_cost3,0)!=0 or IFNULL(cos.other_cost4,0)!=0 or
            IFNULL(cos.other_cost5,0)!=0 or IFNULL(cos.other_cost6,0)!=0 or IFNULL(cos.other_cost7,0)!=0 or IFNULL(cos.other_cost8,0)!=0 or
            IFNULL(cos.other_cost9,0)!=0 or IFNULL(cos.other_cost10,0)!=0 or IFNULL(cos.other_cost11,0)!=0 or IFNULL(cos.other_cost12,0)!=0 or
            IFNULL(cos.freight,0)!=0 or IFNULL(cos.delivery_fee,0)!=0 or IFNULL(cos.outboundsorting_fee,0)!=0 or IFNULL(cos.shortbarge_fee,0)!=0 or
            IFNULL(cos.ultrafar_fee,0)!=0 or IFNULL(cos.superframes_fee,0)!=0 or IFNULL(cos.excess_fee,0)!=0 or IFNULL(cos.return_fee,0)!=0 or
            IFNULL(cos.exception_fee,0)!=0) THEN 1 ELSE 0 END AS hideHigh
            ,cos.company_id AS companyId <!-- 单据所属 -->
            ,wah.warehouse_name AS warehouseName <!-- 发货仓库 -->
            ,'' AS storeCode <!-- 门店编码 -->
            ,'' AS receivingStore <!-- 收货门店 -->
            ,IFNULL(cos.total_number,0) AS  skuNumber <!-- 总件数 -->
            ,0 AS cwFullCases <!-- 常温整箱数 -->
            ,0 AS cwSplitCases <!-- 常温散箱数 -->
            ,0 AS ldFullCases <!-- 非常温整箱数 -->
            ,0 AS ldSplitCases <!-- 非常温散箱数 -->
            ,GROUP_CONCAT(cos.oper_by SEPARATOR ',') AS operBy <!-- 计费人名称 -->
            ,GROUP_CONCAT(cos.remarks SEPARATOR ',') AS remarks <!-- 备注 -->
            ,GROUP_CONCAT(cos.adjust_remark SEPARATOR ',') AS adjustRemark <!-- 调账备注 -->
            ,0 AS splitTotalNumber <!-- 拆零总件数 -->
        FROM bms_ysbillmain a
        LEFT JOIN bms_yscost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_ysexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id and mid.code_type = 2 and mid.del_flag = 0
        LEFT JOIN mdm_warehouseinfo wah ON a.warehouse_code = wah.warehouse_code
        LEFT JOIN bms_clientinfo cli ON cli.id = a.client_id
        WHERE cos.expenses_type IN ( 3 )
        AND a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        GROUP BY cos.pk_id
        ORDER BY cos.pk_id ASC ,cos.signing_date ASC
    </select>

    <select id="inWarehouseDetailsSummary" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            t1.id as id,
            t1.main_code_id as ysbillId,
            t1.sku_code as skuCode, -- 物料编码
            ms.sku_type_name as skuClass, -- 品类
            t1.sku_name as skuName, -- 商品名称
            ms.unit,
            ms.specification,
            t1.total_boxes as totalBoxes, -- 整箱数
            t1.total_odd_boxes as oddBoxes, -- 拆零件数
            t1.box_rule as boxType, -- 箱规
            t1.temperature_type as temperatureType, -- 温区
            t1.total_number as contentsNumber, -- 件数
            ms.weight as weight, -- 单件重量(kg)
            ms.volume as volume, -- 单件体积(cm³)
            t1.total_weight as totalWeight, -- 总重量(kg)
            t1.total_volume as totalVolume, -- 总体积(m³)
            IFNULL(t1.total_cargo_value,0) as totalAmount, -- 总金额
            t1.total_cargo_value/t1.total_number as price,
            t1.del_flag as delFlag,
            t2.code_type as orderType,
            t2.total_cw_pallet_number as cwPalletNumber,
            t2.total_lc_pallet_number as lcPalletNumber,
            t2.total_ld_pallet_number as ldPalletNumber,
            t2.order_code as orderNo,
            t1.total_cargo_value as totalAmount,
            t2.transaction_code as platformCode,
            t2.relate_code as relateCode, -- 入库单号
            t2.order_date as orderDate,  -- 订单时间
            t2.receiving_store_code as storeCode, -- 门店编码
            t2.receiving_store_name as receivingStore -- 收货门店
        FROM bms_storage_code_detail_info t1
        LEFT JOIN bms_storage_code_info t2 ON t2.pk_id = t1.main_pk_id AND t2.del_flag = 0
        LEFT JOIN mdm_skuinfo ms ON ms.sku_code = t1.sku_code AND ms.status=0
        WHERE t1.del_flag = 0
            AND t2.code_type = 3
            AND t2.relate_code IN
            <foreach item="id" collection="relateCodes" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>
    <select id="outWarehouseSummaryMain"
            resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
        cos.signing_date AS signingDate,
        cos.order_date AS orderDate,
        cos.expenses_code AS expensesCode,
        IFNULL(cos.total_cargo_value,0) AS cargoValue,
        '' AS automaticBillingRemark,
        IFNULL(cos.total_boxes,0) AS totalBoxes,
        IFNULL(cos.total_weight,0) AS weight,
        IFNULL(cos.total_weight,0)/1000 AS totalWeight,
        IFNULL(cos.total_volume,0) AS totalVolume,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.freight,0) else SUM(IFNULL(cos.freight,0)) END AS freight,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.outboundsorting_fee,0) else SUM(IFNULL(cos.outboundsorting_fee,0)) END AS outboundsortingFee,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.ultrafar_fee,0) else SUM(IFNULL(cos.ultrafar_fee,0)) END AS ultrafarFee,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.superframes_fee,0) else SUM(IFNULL(cos.superframes_fee,0)) END AS superframesFee,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.excess_fee,0) else SUM(IFNULL(cos.excess_fee,0)) END AS excessFee,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.reduce_fee,0) else SUM(IFNULL(cos.reduce_fee,0)) END AS reduceFee,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.adjust_fee,0) else SUM(IFNULL(cos.adjust_fee,0)) END AS adjustFee,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.shortbarge_fee,0) else SUM(IFNULL(cos.shortbarge_fee,0)) END AS shortbargeFee,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.return_fee,0) else SUM(IFNULL(cos.return_fee,0)) END AS returnFee,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.delivery_fee,0) else SUM(IFNULL(cos.delivery_fee,0)) END AS deliveryFee,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.exception_fee,0) else SUM(IFNULL(cos.exception_fee,0)) END AS exceptionFee,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.other_cost1,0) else SUM(IFNULL(cos.other_cost1,0)) END AS otherCost1,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.other_cost2,0) else SUM(IFNULL(cos.other_cost2,0)) END AS otherCost2,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.other_cost3,0) else SUM(IFNULL(cos.other_cost3,0)) END AS otherCost3,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.other_cost4,0) else SUM(IFNULL(cos.other_cost4,0)) END AS otherCost4,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.other_cost5,0) else SUM(IFNULL(cos.other_cost5,0)) END AS otherCost5,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.other_cost6,0) else SUM(IFNULL(cos.other_cost6,0)) END AS otherCost6,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.other_cost7,0) else SUM(IFNULL(cos.other_cost7,0)) END AS otherCost7,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.other_cost8,0) else SUM(IFNULL(cos.other_cost8,0)) END AS otherCost8,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.other_cost9,0) else SUM(IFNULL(cos.other_cost9,0)) END AS otherCost9,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.other_cost10,0) else SUM(IFNULL(cos.other_cost10,0)) END AS otherCost10,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.other_cost11,0) else SUM(IFNULL(cos.other_cost11,0)) END AS otherCost11,
        CASE WHEN cos.expenses_dimension in (2,3,4) THEN IFNULL(cos.other_cost12,0) else SUM(IFNULL(cos.other_cost12,0)) END AS otherCost12,
        case
        WHEN cos.expenses_dimension in (2,3,4)
        THEN IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
        +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
        +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
        else SUM(IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
        +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
        +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0))
        END otherSum,

        case
        WHEN cos.expenses_dimension in (2,3,4)
        THEN IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
        +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
        +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0)
        else SUM(IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
        +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
        +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0))
        END basicsSum,

        CASE WHEN
        cos.expenses_dimension in (2,3,4)
        THEN IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
        +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
        +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
        +IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
        +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
        +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0)
        else SUM(IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
        +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
        +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
        +IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
        +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
        +IFNULL(cos.exception_fee,0)+IFNULL(cos.reduce_fee,0))
        END sumAmt ,
        GROUP_CONCAT(cos.id SEPARATOR ',') AS id,
        cos.warehouse_code AS warehouseCode,
        wah.warehouse_name AS warehouseName,
        CASE WHEN (IFNULL(cos.other_cost1,0)!=0 OR IFNULL(cos.other_cost2,0)!=0 OR IFNULL(cos.other_cost3,0)!=0 OR IFNULL(cos.other_cost4,0)!=0 or
        IFNULL(cos.other_cost5,0)!=0 OR IFNULL(cos.other_cost6,0)!=0 OR IFNULL(cos.other_cost7,0)!=0 OR IFNULL(cos.other_cost8,0)!=0 or
        IFNULL(cos.other_cost9,0)!=0 OR IFNULL(cos.other_cost10,0)!=0 OR IFNULL(cos.other_cost11,0)!=0 OR IFNULL(cos.other_cost12,0)!=0 or
        IFNULL(cos.freight,0)!=0 OR IFNULL(cos.delivery_fee,0)!=0 OR IFNULL(cos.outboundsorting_fee,0)!=0 OR IFNULL(cos.shortbarge_fee,0)!=0 or
        IFNULL(cos.ultrafar_fee,0)!=0 OR IFNULL(cos.superframes_fee,0)!=0 OR IFNULL(cos.excess_fee,0)!=0 OR IFNULL(cos.return_fee,0)!=0 or
        IFNULL(cos.exception_fee,0)!=0) THEN 1 else 0 END AS hideHigh,
        cos.expenses_type AS orderType,
        cos.charge_type AS chargeType,
        GROUP_CONCAT((case
        WHEN cos.charge_type = 2
        THEN '手动计费'
        WHEN cos.charge_type = 1
        THEN '自动计费'
        else ''
        end) SEPARATOR ',') AS chargeTypeName
        ,cos.company_id AS companyId
        ,GROUP_CONCAT(cos.remarks SEPARATOR ',') AS remarks
        ,GROUP_CONCAT(cos.adjust_remark SEPARATOR ',') AS adjustRemark
        ,cos.total_number AS skuNumber
        FROM bms_ysbillmain a
        INNER JOIN bms_yscost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        INNER JOIN bms_ysexpenses_middle mid ON cos.main_code_id = mid.main_code_id AND mid.del_flag = 0 AND mid.code_type = 2
        LEFT JOIN mdm_warehouseinfo wah ON cos.warehouse_code = wah.warehouse_code
        LEFT JOIN bms_clientinfo cli ON cli.client_code = a.client_id
        WHERE a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
            AND cos.expenses_type IN ( 2 )
        GROUP BY cos.pk_id
        ORDER BY cos.pk_id ASC,cos.signing_date ASC
    </select>
    <select id="outWarehousDetailSummary"
            resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            t1.id AS id,
            t1.main_code_id AS ysbillId,
            t1.sku_code AS skuCode, -- 物料编码
            t1.sku_name AS skuName, -- 商品名称
            t1.total_boxes AS totalBoxes, -- 整箱数
            t1.total_odd_boxes AS oddBoxes, -- 拆零件数
            t1.box_rule AS boxType, -- 箱规
            t1.temperature_type AS temperatureType, -- 温区
            t1.total_number AS contentsNumber, -- 件数
            t1.total_weight AS totalWeight, -- 总重量(kg)
            t1.total_volume AS totalVolume, -- 总体积(m³)
            IFNULL(t1.total_cargo_value,0) AS totalAmount, -- 总金额
            t1.del_flag AS delFlag,
            t2.code_type AS orderType,
            t2.total_cw_pallet_number AS cwPalletNumber,
            t2.total_lc_pallet_number AS lcPalletNumber,
            t2.total_ld_pallet_number AS ldPalletNumber,
            t1.total_cargo_value AS totalAmount,
            t2.order_date AS orderDate,  -- 订单时间
            t2.receiving_store_code AS storeCode, -- 门店编码
            t2.receiving_store_name AS receivingStore, -- 收货门店
            t2.relate_code AS relateCode, -- 单号
            ms.unit,  -- 单位
            ms.specification  -- 规格
        FROM bms_storage_code_detail_info t1
        LEFT JOIN bms_storage_code_info t2 ON t2.pk_id = t1.main_pk_id AND t2.del_flag = 0
        LEFT JOIN mdm_skuinfo ms ON ms.sku_code=t1.sku_code AND ms.status=0
        WHERE t1.del_flag = 0
        AND t2.code_type = 2
        AND t2.relate_code IN
        <foreach item="id" collection="relateCodes" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getBillCodeById"
            resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            a.bill_code billCode
        FROM bms_ysbillmain a
        WHERE a.del_flag=0
          AND a.id = #{id} OR a.fatherid=#{id}
    </select>

    <select id="getBillCodeById2"
            resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            a.bill_code billCode
        FROM bms_ysbillmain a
        WHERE a.del_flag=0
        AND a.id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectBmsYsBillExtionFeeInfo" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsbillmainExportAll">
        SELECT
            a.bill_type billType
            ,ifnull(a.exception_fee,0) exceptionFee
            ,ifnull(a.un_claimes_amount,0) unClaimesAmount
        FROM bms_ysbillmain a
        WHERE a.merge_status=0
            AND a.id IN
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
    </select>

</mapper>