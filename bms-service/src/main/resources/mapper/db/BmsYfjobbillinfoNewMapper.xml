<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYfjobbillinfoNewMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsYfjobbillinfoNew" id="BmsYfjobbillinfoResult">
        <result property="id"    column="id"    />
        <result property="pkId"    column="pk_id"    />
        <result property="relateCode"    column="relate_code"    />
        <result property="schedulingBillCode"    column="scheduling_bill_code"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="companyId"    column="company_id"    />
        <result property="networkCode"    column="network_code"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="totalNumber"    column="total_number"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="cargoValue"    column="cargo_value"    />
        <result property="ifBaseStores"    column="if_base_stores"    />
        <result property="ifSuperBaseKilometer"    column="if_Super_base_kilometer"    />
        <result property="storeDistanceKilometer"    column="store_distance_kilometer"    />
        <result property="storeCode"    column="store_code"    />
        <result property="receivingStore"    column="receiving_store"    />
        <result property="createCode"    column="create_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="operCode"    column="oper_code"    />
        <result property="operBy"    column="oper_by"    />
        <result property="operDeptId"    column="oper_dept_id"    />
        <result property="operTime"    column="oper_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="nearStoreKm"    column="near_store_km"    />
        <result property="schedulingBillID"    column="schedulingBillID"    />
        <result property="destinationProvince"    column="order_province"    />
        <result property="destinationCity"    column="order_city"    />
        <result property="destinationArea"    column="order_district"    />
    </resultMap>

    <sql id="selectBmsYfjobbillinfoVo">
        select
        t1.order_province ,
        t1.order_city ,
        t1.order_district,
        t2.ID schedulingBillID, t1.id,t1. pk_id,t1. relate_code,t1. scheduling_bill_code,t1. client_code,t1. client_name,t1. company_id,t1. network_code,t1. total_boxes,t1. total_number,t1. total_weight,t1. total_volume,t1. cargo_value,t1. if_base_stores,t1. if_Super_base_kilometer,t1. store_distance_kilometer,t1. store_code,t1. receiving_store,t1. create_code,t1. create_by,t1. create_dept_id,t1. create_time,t1. oper_code,t1. oper_by,t1. oper_dept_id, t1.oper_time, t1.del_flag,t1.near_store_km
        from bms_yfjobbillinfo t1 left join bms_yfbillcodeinfo t2 on t1.scheduling_bill_code=t2.virtual_order_no
    </sql>


    <select id="selectBmsYfJobBillInfoListByCodes" parameterType="java.lang.String" resultMap="BmsYfjobbillinfoResult">
        <include refid="selectBmsYfjobbillinfoVo"/>
        where t1.del_flag=0 and t2.del_flag=0 and t2.ID in
        <foreach item="code" collection="schedulingBillID" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>


</mapper>