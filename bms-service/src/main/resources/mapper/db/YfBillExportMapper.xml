<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.YfBillExportMapper">

    <select id="getBillingInformation" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT IFNULL(a.ticket_amount, 0) AS ticketAmount,
            a.bill_code                AS billCode,
            a.bill_name                AS billName,
            a.bill_date                AS billDate,
            b.client_name              AS clientName,
            c.carrier_name             AS carrierName
        FROM bms_yfbillmain a
        LEFT JOIN bms_clientinfo b ON a.client_id = b.id
        LEFT JOIN bms_carrierinfo c ON a.carrier_id = c.id
        WHERE a.id = #{id}
    </select>

    <select id="selectBmsYfBillFeeTotalInfo"  resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYfbillmainExportAll">
        select
            GROUP_CONCAT(DISTINCT a.id) as idStr,
            GROUP_CONCAT(DISTINCT  a.billType) as billTypeStr,
            a.clientType,
            a.billDate,
            a.billType,
            a.expensesType,
            a.sheet1Name,
            a.billName,
            a.carrierName,
            a.feeSettlementDate,
            sum(IFNULL(a.glFeeCwAmount,0)) glFeeCwAmount,
            sum(IFNULL(a.glFeeLcAmount,0)) glFeeLcAmount,
            sum(IFNULL(a.glFeeLdAmount,0)) glFeeLdAmount,
            sum(IFNULL(a.ccFeeCwAmount,0)) ccFeeCwAmount,
            sum(IFNULL(a.ccFeeLcAmount,0)) ccFeeLcAmount,
            sum(IFNULL(a.ccFeeLdAmount,0)) ccFeeLdAmount,
            sum(IFNULL(a.fjFeeCwRkAmount,0)) fjFeeCwRkAmount,
            sum(IFNULL(a.fjFeeLcRkAmount,0)) fjFeeLcRkAmount,
            sum(IFNULL(a.fjFeeLdRkAmount,0)) fjFeeLdRkAmount,
            sum(IFNULL(a.fjFeeCwCkAmount,0)) fjFeeCwCkAmount,
            sum(IFNULL(a.fjFeeLcCkAmount,0)) fjFeeLcCkAmount,
            sum(IFNULL(a.fjFeeLdCkAmount,0)) fjFeeLdCkAmount,
            sum(IFNULL(a.fjFeeLcRkZhAmount,0)) fjFeeRkZhAmount,
            sum(IFNULL(a.fjFeeLdRkPhAmount,0)) fjFeeRkPhAmount,
            sum(IFNULL(a.fjFeeLcCkZhAmount,0)) fjFeeCkZhAmount,
            sum(IFNULL(a.fjFeeLdCkPhAmount,0)) fjFeeCkPhAmount,
            sum(IFNULL(a.ccShortbargeFee,0)) ccShortbargeFee,
            sum(IFNULL(a.ccReturnFee,0)) ccReturnFee,
            sum(IFNULL(a.ccOtherCost1,0)) ccOtherCost1,
            sum(IFNULL(a.ccOtherCost2,0)) ccOtherCost2,
            sum(IFNULL(a.ccOtherCost3,0)) ccOtherCost3,
            sum(IFNULL(a.ccOtherCost4,0)) ccOtherCost4,
            sum(IFNULL(a.ccOtherCost5,0)) ccOtherCost5,
            sum(IFNULL(a.ccOtherCost6,0)) ccOtherCost6,
            sum(IFNULL(a.ccOtherCost7,0)) ccOtherCost7,
            sum(IFNULL(a.ccOtherCost8,0)) ccOtherCost8,
            sum(IFNULL(a.ccOtherCost9,0)) ccOtherCost9,
            sum(IFNULL(a.ccOtherCost10,0)) ccOtherCost10,
            sum(IFNULL(a.ccOtherCost11,0)) ccOtherCost11,
            sum(IFNULL(a.ccOtherCost12,0)) ccOtherCost12,
            sum(IFNULL(a.psFreight,0)) psFreight,
            sum(IFNULL(a.psDeliveryFee,0)) psDeliveryFee,
            sum(IFNULL(a.psOutboundsortingFee,0)) psOutboundsortingFee,
            sum(IFNULL(a.psSuperframesFee,0)) psSuperframesFee,
            sum(IFNULL(a.psExcessFee,0)) psExcessFee,
            sum(IFNULL(a.psReduceFee,0)) psReduceFee,
            sum(IFNULL(a.psShortbargeFee,0)) psShortbargeFee,
            sum(IFNULL(a.psReturnFee,0)) psReturnFee,
            sum(IFNULL(a.psUltrafarFee,0)) psUltrafarFee,
            sum(IFNULL(a.psOtherCost1,0)) psOtherCost1,
            sum(IFNULL(a.psOtherCost2,0)) psOtherCost2,
            sum(IFNULL(a.psOtherCost3,0)) psOtherCost3,
            sum(IFNULL(a.psOtherCost4,0)) psOtherCost4,
            sum(IFNULL(a.psOtherCost5,0)) psOtherCost5,
            sum(IFNULL(a.psOtherCost6,0)) psOtherCost6,
            sum(IFNULL(a.psOtherCost7,0)) psOtherCost7,
            sum(IFNULL(a.psOtherCost8,0)) psOtherCost8,
            sum(IFNULL(a.psOtherCost9,0)) psOtherCost9,
            sum(IFNULL(a.psOtherCost10,0)) psOtherCost10,
            sum(IFNULL(a.psOtherCost11,0)) psOtherCost11,
            sum(IFNULL(a.psOtherCost12,0)) psOtherCost12,
            (sum(IFNULL(a.glFeeCwAmount,0)) + sum(IFNULL(a.glFeeLcAmount,0)) + sum(IFNULL(a.glFeeLdAmount,0)) + sum(IFNULL(a.ccFeeCwAmount,0)) + sum(IFNULL(a.ccFeeLcAmount,0)) + sum(IFNULL(a.ccFeeLdAmount,0)) +
             sum(IFNULL(a.fjFeeCwRkAmount,0)) + sum(IFNULL(a.fjFeeLcRkAmount,0)) + sum(IFNULL(a.fjFeeLdRkAmount,0)) + sum(IFNULL(a.fjFeeCwCkAmount,0)) + sum(IFNULL(a.fjFeeLcCkAmount,0)) +
             sum(IFNULL(a.ccShortbargeFee,0)) + sum(IFNULL(a.ccReturnFee,0)) +
             sum(IFNULL(a.fjFeeLdCkAmount,0)) +sum(IFNULL(a.fjFeeLcRkZhAmount,0)) + sum(IFNULL(a.fjFeeLdRkPhAmount,0)) + sum(IFNULL(a.fjFeeLcCkZhAmount,0)) + sum(IFNULL(a.fjFeeLdCkPhAmount,0)) +
             sum(IFNULL(a.ccOtherCost1,0))+ sum(IFNULL(a.ccOtherCost2,0)) + sum(IFNULL(a.ccOtherCost3,0)) + sum(IFNULL(a.ccOtherCost4,0)) + sum(IFNULL(a.ccOtherCost5,0)) +
             sum(IFNULL(a.ccOtherCost6,0)) + sum(IFNULL(a.ccOtherCost7,0)) + sum(IFNULL(a.ccOtherCost8,0))+ sum(IFNULL(a.ccOtherCost9,0))+ sum(IFNULL(a.ccOtherCost10,0)) + sum(IFNULL(a.ccOtherCost11,0)) +
             sum(IFNULL(a.ccOtherCost12,0))) as ccFeeSum,
             (sum(IFNULL(a.psFreight,0)) +    sum(IFNULL(a.psDeliveryFee,0)) + sum(IFNULL(a.psOutboundsortingFee,0)) + sum(IFNULL(a.psSuperframesFee,0)) + sum(IFNULL(a.psShortbargeFee,0))+
              sum(IFNULL(a.psExcessFee,0))  + sum(IFNULL(a.psReturnFee,0))   + sum(IFNULL(a.psUltrafarFee,0))        + sum(IFNULL(a.psOtherCost1,0))+
              sum(IFNULL(a.psOtherCost2,0)) + sum(IFNULL(a.psOtherCost3,0))  + sum(IFNULL(a.psOtherCost4,0))         + sum(IFNULL(a.psOtherCost5,0))     + sum(IFNULL(a.psOtherCost6,0)) +
              sum(IFNULL(a.psOtherCost7,0)) + sum(IFNULL(a.psOtherCost8,0))  + sum(IFNULL(a.psOtherCost9,0))         + sum(IFNULL(a.psOtherCost10,0))    + sum(IFNULL(a.psOtherCost11,0)) +
              sum(IFNULL(a.psOtherCost12,0)) +sum(IFNULL(a.psReduceFee,0))) as psFeeSum,
             ifnull(a.responsibleMoney,0) responsibleMoney
        from
            (
                SELECT
                    ys.id,
                    ys.bill_type as billType,
                    ys.bill_name as billName,
                    inf.client_type as clientType,
                    case when ysi.expenses_type=1 then 1
                    else 2 end as expensesType,
                    ys.responsible_money,
                    replace(ys.bill_date,"-","年") as billDate,
                    concat(IFNULL(cli.carrier_area,''),"地区",replace(ys.bill_date,"-","年"),"月份物流费用应付对账单")	 sheet1Name,
                    cli.carrier_name carrierName,
                    concat(DATE_FORMAT(DATE_SUB(STR_TO_DATE(concat("2022-02",'-',cli.accountperi),'%Y-%m-%d'),INTERVAL 1 MONTH),'%Y年%m月%d日'),"至",
                           DATE_FORMAT(STR_TO_DATE(concat("2022-02",'-',cli.accountperi+1),'%Y-%m-%d'),'%Y年%m月%d日')) feeSettlementDate,
                    case when IFNULL(ysi.expenses_attribute,3) = 3 and ysi.expenses_type in (2,3,4)  then IFNULL(ysi.ultrafar_fee,0) else 0 end glFeeCwAmount,
                    case when IFNULL(ysi.expenses_attribute,3) = 4 and ysi.expenses_type in (2,3,4)  then IFNULL(ysi.ultrafar_fee,0) else 0 end glFeeLcAmount,
                    case when IFNULL(ysi.expenses_attribute,3) = 5 and ysi.expenses_type in (2,3,4)  then IFNULL(ysi.ultrafar_fee,0) else 0 end glFeeLdAmount,
                    case when IFNULL(ysi.expenses_attribute,3) = 3 and ysi.expenses_type = 4  then IFNULL(ysi.freight,0) else 0 end ccFeeCwAmount,
                    case when IFNULL(ysi.expenses_attribute,3) = 4 and ysi.expenses_type = 4  then IFNULL(ysi.freight,0) else 0 end ccFeeLcAmount,
                    case when IFNULL(ysi.expenses_attribute,3) = 5 and ysi.expenses_type = 4  then IFNULL(ysi.freight,0) else 0 end ccFeeLdAmount,
                    case when IFNULL(ysi.expenses_attribute,3) = 3 and ysi.expenses_type = 3  then IFNULL(ysi.superframes_fee,0)+IFNULL(excess_fee,0) else 0 end fjFeeCwRkAmount,
                    case when IFNULL(ysi.expenses_attribute,3) = 4 and ysi.expenses_type = 3  then IFNULL(ysi.superframes_fee,0)+IFNULL(excess_fee,0) else 0 end fjFeeLcRkAmount,
                    case when IFNULL(ysi.expenses_attribute,3) = 5 and ysi.expenses_type = 3  then IFNULL(ysi.superframes_fee,0)+IFNULL(excess_fee,0) else 0 end fjFeeLdRkAmount,
                    case when IFNULL(ysi.expenses_attribute,3) = 3 and ysi.expenses_type = 2  then IFNULL(ysi.superframes_fee,0)+IFNULL(excess_fee,0) else 0 end fjFeeCwCkAmount,
                    case when IFNULL(ysi.expenses_attribute,3) = 4 and ysi.expenses_type = 2  then IFNULL(ysi.superframes_fee,0)+IFNULL(excess_fee,0) else 0 end fjFeeLcCkAmount,
                    case when IFNULL(ysi.expenses_attribute,3) = 5 and ysi.expenses_type = 2  then IFNULL(ysi.superframes_fee,0)+IFNULL(excess_fee,0) else 0 end fjFeeLdCkAmount,
                    case when IFNULL(ysi.expenses_attribute,4) in(1,2,3,4) and ysi.expenses_type = 3  then IFNULL(ysi.delivery_fee,0) else 0 end fjFeeLcRkZhAmount,
                    case when IFNULL(ysi.expenses_attribute,4) = 5 and ysi.expenses_type = 3  then IFNULL(ysi.delivery_fee,0) else 0 end fjFeeLdRkPhAmount,
                    case when IFNULL(ysi.expenses_attribute,4) = 4 and ysi.expenses_type = 2  then IFNULL(ysi.delivery_fee,0) else 0 end fjFeeLcCkZhAmount,
                    case when IFNULL(ysi.expenses_attribute,4) = 5 and ysi.expenses_type = 2  then IFNULL(ysi.delivery_fee,0) else 0 end fjFeeLdCkPhAmount,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.shortbarge_fee,0) else 0 end ccShortbargeFee,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.return_fee,0) else 0 end ccReturnFee,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost1,0) else 0 end ccOtherCost1,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost2,0) else 0 end ccOtherCost2,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost3,0) else 0 end ccOtherCost3,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost4,0) else 0 end ccOtherCost4,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost5,0) else 0 end ccOtherCost5,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost6,0) else 0 end ccOtherCost6,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost7,0) else 0 end ccOtherCost7,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost8,0) else 0 end ccOtherCost8,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost9,0) else 0 end ccOtherCost9,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost10,0) else 0 end ccOtherCost10,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost11,0) else 0 end ccOtherCost11,
                    case when ysi.expenses_type in(2,3,4) then IFNULL(ysi.other_cost12,0) else 0 end ccOtherCost12,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.freight,0) else 0 end  psFreight,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.delivery_fee,0) else 0 end  psDeliveryFee,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.outboundsorting_fee,0) else 0 end  psOutboundsortingFee,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.superframes_fee,0) else 0 end  psSuperframesFee,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.excess_fee,0) else 0 end  psExcessFee,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.reduce_fee,0) else 0 end  psReduceFee,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.shortbarge_fee,0) else 0 end  psShortbargeFee,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.return_fee,0) else 0 end  psReturnFee,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.ultrafar_fee,0) else 0 end  psUltrafarFee,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost1,0) else 0 end psOtherCost1,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost2,0) else 0 end psOtherCost2,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost3,0) else 0 end psOtherCost3,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost4,0) else 0 end psOtherCost4,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost5,0) else 0 end psOtherCost5,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost6,0) else 0 end psOtherCost6,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost7,0) else 0 end psOtherCost7,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost8,0) else 0 end psOtherCost8,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost9,0) else 0 end psOtherCost9,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost10,0) else 0 end psOtherCost10,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost11,0) else 0 end psOtherCost11,
                    case when ysi.expenses_type = 1 then IFNULL(ysi.other_cost12,0) else 0 end psOtherCost12,
                    sum(ifnull(ys.responsible_money,0)) responsibleMoney
                from bms_yfbillmain ys
                left join bms_yfcost_info ysi on ysi.bill_id = ys.id and ysi.del_flag = '0'
                left join bms_carrierinfo cli on ys.carrier_id = cli.id and cli.del_flag = '0'
                left join bms_clientinfo inf on inf.id = ys.client_id
                where 1=1
                   and (ys.id in
                <foreach collection="ids" item="ids" open="(" separator="," close=")">
                    #{ids}
                 </foreach> )
                GROUP BY ysi.id,ys.id
            ) a where 1=1

            -- 入库类型少 ultrafar_fee 超远费（订单）/管理处置费(库存单) 的费用？？？
            -- 入库类型少 delivery_fee 装卸费(出库单/入库单) 并且 expenses_attribute费用属性1重货2泡货3常温4冷藏5冷冻6恒温 为3的费用 ？？？
    </select>

    <select id="getGoupRouteinfoListByBillId"  resultType="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeinfo">
        SELECT
            a.lineCode,
            a.lineName,
            a.storeNum,
            a.totalNumber,
            a.totalWeight,
            a.totalVolume,
            a.cargoValue
        FROM (
            SELECT info.line_code                      AS lineCode,
                info.line_name                         AS lineName,
                info.base_stores                       AS storeNum,
                SUM(IFNULL(info.total_number, 0))      AS totalNumber,
                SUM(IFNULL(info.total_weight, 0))      AS totalWeight,
                SUM(IFNULL(info.total_volume, 0))      AS totalVolume,
                SUM(IFNULL(info.total_cargo_value, 0)) AS cargoValue
            FROM bms_yfbillmain a
            LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id
            LEFT JOIN bms_yfexpenses_middle mid ON cos.main_code_id = mid.main_code_id AND mid.code_type = 1 AND mid.del_flag = 0
            LEFT JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id
            WHERE cos.expenses_type = 1
            AND a.id IN
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
            GROUP BY info.tline_code
        ) a
        GROUP BY a.lineCode
    </select>

    <select id="getStorageByBillId"  resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
        cos.finish_date AS instorageTime,
        cos.warehouse_code AS warehouseCode,
        cos.warehouse_name AS warehouseName,
        SUM(IFNULL(info.total_day_number,0)) AS aqty,
        SUM(IFNULL(info.total_day_pallet_number,0)) AS palletNumber,
        SUM(IFNULL(info.pallet_rule,0)) AS palletRuler,
        IFNULL(cos.total_boxes,0) AS totalBoxes,
        IFNULL(cos.total_number,0) AS totalNumber,
        IFNULL(cos.total_weight,0) AS totalWeight,
        IFNULL(cos.total_volume,0) AS totalVolume,
        IFNULL(cos.excess_fee,0) AS excessFee,
        IFNULL(cos.exception_fee,0) AS exceptionFee,
        IFNULL(cos.shortbarge_fee,0) AS shortbargeFee,
        IFNULL(cos.return_fee,0) AS returnFee,
        IFNULL(cos.freight,0) AS freight,
        IFNULL(cos.ultrafar_fee,0) AS ultrafarFee,
        IFNULL(cos.delivery_fee,0) AS deliveryFee,
        IFNULL(cos.superframes_fee,0) AS superframesFee,
        IFNULL(cos.adjust_fee,0) AS adjustFee,
        IFNULL(cos.other_cost1,0) AS otherCost1,
        IFNULL(cos.other_cost2,0) AS otherCost2,
        IFNULL(cos.other_cost3,0) AS otherCost3,
        IFNULL(cos.other_cost4,0) AS otherCost4,
        IFNULL(cos.other_cost5,0) AS otherCost5,
        IFNULL(cos.other_cost6,0) AS otherCost6,
        IFNULL(cos.other_cost7,0) AS otherCost7,
        IFNULL(cos.other_cost8,0) AS otherCost8,
        IFNULL(cos.other_cost9,0) AS otherCost9,
        IFNULL(cos.other_cost10,0) AS otherCost10,
        IFNULL(cos.other_cost11,0) AS otherCost11,
        IFNULL(cos.other_cost12,0) AS otherCost12,
        IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
        +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
        +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0) AS otherSum,

        IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.adjust_fee,0)+IFNULL(cos.shortbarge_fee,0)
        +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
        +IFNULL(cos.exception_fee,0) AS basicsSum,

        IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
        +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
        +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
        +IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.adjust_fee,0)+IFNULL(cos.shortbarge_fee,0)
        +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
        +IFNULL(cos.exception_fee,0) AS sumAmt
        ,cos.is_increment AS isIncrement
        ,cos.fee_type_first AS feeTypeFirst
        ,'' AS feeTypeFirstName
        ,'' AS totalQuantity
        ,cos.total_extra_fee_price price
        ,cos.id
        FROM bms_yfbillmain a
        LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id
        LEFT JOIN bms_yfexpenses_middle mid ON cos.main_code_id = mid.main_code_id and mid.del_flag = 0 and mid.code_type = 2
        LEFT join bms_storage_code_info info ON mid.code_pk_id = info.pk_id
        left join mdm_warehouseinfo wah ON info.warehouse_code = wah.warehouse_code
        WHERE cos.expenses_type IN (4)
        AND a.id in
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        GROUP BY cos.id
        ORDER BY cos.id DESC,cos.business_time ASC
    </select>

    <select id="getStorageGoodsDetailByBillId" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT info.signing_date                                            AS instorageTime,
            info.temperature_type                                          AS temperatureType,
            cli.carrier_code                                               AS clientCode,
            cli.carrier_name                                               AS clientName,
            goodsinfo.sku_code                                             AS skuCode,
            sku.sku_name                                                   AS skuName,
            goodsinfo.product_date                                         AS productDate,
            IFNULL(cos.other_cost1, 0) + IFNULL(cos.other_cost2, 0) + IFNULL(cos.other_cost3, 0) + IFNULL(cos.other_cost4, 0)
            + IFNULL(cos.other_cost5, 0) + IFNULL(cos.other_cost6, 0) + IFNULL(cos.other_cost7, 0) +
            IFNULL(cos.other_cost8, 0)
            + IFNULL(cos.other_cost9, 0) + IFNULL(cos.other_cost10, 0) + IFNULL(cos.other_cost11, 0) +
            IFNULL(cos.other_cost12, 0)                                    AS otherSum,

            IFNULL(cos.freight, 0) + IFNULL(cos.delivery_fee, 0) + IFNULL(cos.outboundsorting_fee, 0) +
            IFNULL(cos.shortbarge_fee, 0)
            + IFNULL(cos.ultrafar_fee, 0) + IFNULL(cos.superframes_fee, 0) + IFNULL(cos.excess_fee, 0) +
            IFNULL(cos.return_fee, 0)
            + IFNULL(cos.reduce_fee, 0) + IFNULL(cos.exception_fee, 0) AS basicsSum,

            IFNULL(cos.other_cost1, 0) + IFNULL(cos.other_cost2, 0) + IFNULL(cos.other_cost3, 0) + IFNULL(cos.other_cost4, 0)
            + IFNULL(cos.other_cost5, 0) + IFNULL(cos.other_cost6, 0) + IFNULL(cos.other_cost7, 0) +
            IFNULL(cos.other_cost8, 0)
            + IFNULL(cos.other_cost9, 0) + IFNULL(cos.other_cost10, 0) + IFNULL(cos.other_cost11, 0) +
            IFNULL(cos.other_cost12, 0)
            + IFNULL(cos.freight, 0) + IFNULL(cos.delivery_fee, 0) + IFNULL(cos.outboundsorting_fee, 0) +
            IFNULL(cos.shortbarge_fee, 0)
            + IFNULL(cos.ultrafar_fee, 0) + IFNULL(cos.superframes_fee, 0) + IFNULL(cos.excess_fee, 0) +
            IFNULL(cos.return_fee, 0)
            + IFNULL(cos.reduce_fee, 0) + IFNULL(cos.exception_fee, 0) AS sumAmt
        FROM bms_yfbillmain a
        LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_yfexpenses_middle mid ON cos.main_code_id = mid.main_pk_id AND mid.del_flag = 0 AND mid.code_type = 2
        LEFT JOIN bms_storage_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN bms_storage_code_detail_info goodsinfo ON goodsinfo.main_pk_id = info.pk_id AND goodsinfo.del_flag = 0
        LEFT JOIN mdm_warehouseinfo wah ON info.warehouse_code = wah.warehouse_code
        LEFT JOIN bms_carrierinfo cli ON cli.carrier_code = info.carrier_code
        LEFT JOIN mdm_skuinfo sku ON sku.sku_code = goodsinfo.sku_code
        WHERE cos.expenses_type IN (4)
        AND a.id = #{id} OR a.fatherid=#{id}
        GROUP BY goodsinfo.id
        ORDER BY info.instorage_time ASC
    </select>

    <select id="getStorageinWarehouseGoodsDetailByBillId"  resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            info.signing_date AS signingDate,
            info.relate_code AS relateCode,
            sku.sku_code AS skuCode,
            sku.sku_name AS skuName,
            IFNULL(goodsinfo.total_volume,0) AS totalVolume,
            IFNULL(info.total_pallet_number,0) AS palletNumber,
            IFNULL(goodsinfo.total_boxes,0) AS totalBoxes,
            0 AS oddBoxes,
            sku.unit AS unit,
            sku.specification,
            IFNULL(goodsinfo.total_number,0) AS contentsNumber,
            IFNULL(goodsinfo.total_weight,0) AS weight,
            IFNULL(goodsinfo.total_volume,0) AS volume,
            IFNULL(goodsinfo.total_weight,IFNULL(goodsinfo.total_number,0)*IFNULL(goodsinfo.total_weight,0))/1000 AS totalWeight,
            IFNULL(cos.superframes_fee,0) AS superframesFee,
            IFNULL(cos.shortbarge_fee,0) AS shortbargeFee,
            IFNULL(cos.return_fee,0) AS returnFee,
            IFNULL(cos.freight,0) AS freight,
            IFNULL(cos.adjust_fee,0) AS adjustFee,
            IFNULL(cos.outboundsorting_fee,0) AS outboundsortingFee,
            IFNULL(cos.excess_fee,0) AS excessFee,
            IFNULL(cos.ultrafar_fee,0) AS ultrafarFee,
            IFNULL(cos.delivery_fee,0) AS deliveryFee,
            IFNULL(cos.exception_fee,0) AS exceptionFee,
            IFNULL(cos.other_cost1,0) AS otherCost1,
            IFNULL(cos.other_cost2,0) AS otherCost2,
            IFNULL(cos.other_cost3,0) AS otherCost3,
            IFNULL(cos.other_cost4,0) AS otherCost4,
            IFNULL(cos.other_cost5,0) AS otherCost5,
            IFNULL(cos.other_cost6,0) AS otherCost6,
            IFNULL(cos.other_cost7,0) AS otherCost7,
            IFNULL(cos.other_cost8,0) AS otherCost8,
            IFNULL(cos.other_cost9,0) AS otherCost9,
            IFNULL(cos.other_cost10,0) AS otherCost10,
            IFNULL(cos.other_cost11,0) AS otherCost11,
            IFNULL(cos.other_cost12,0) AS otherCost12,
            cos.oper_by operBy ,
            cos.remarks remarksJf,
            IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
            +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
            +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0) AS otherSum,

            IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
            +IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
            +IFNULL(cos.reduce_fee,0)+IFNULL(cos.exception_fee,0) AS basicsSum,

            IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
            +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
            +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
            +IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.adjust_fee,0)+IFNULL(cos.shortbarge_fee,0)
            +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
            +IFNULL(cos.exception_fee,0) AS sumAmt,
            cos.id
        FROM bms_yfbillmain a
        LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id
        LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.code_type = 2 AND mid.del_flag = 0
        LEFT JOIN bms_storage_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN bms_storage_code_detail_info goodsinfo ON goodsinfo.main_pk_id = info.pk_id AND goodsinfo.del_flag = 0
        LEFT JOIN mdm_warehouseinfo wah ON info.warehouse_code = wah.warehouse_code
        LEFT JOIN bms_carrierinfo cli ON cli.carrier_code = info.carrier_code
        LEFT JOIN mdm_skuinfo sku ON sku.sku_code = goodsinfo.sku_code
        WHERE cos.expenses_type IN ( 3 )
            AND a.id IN
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        GROUP BY goodsinfo.id,cos.id
        ORDER BY cos.id DESC
    </select>

    <select id="getStorageOutWarehouseGoodsDetailByBillId"  resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            info.signing_date AS signingDate,
            info.relate_code AS relateCode,
            sku.sku_code AS skuCode,
            sku.sku_name AS skuName,
            IFNULL(goodsinfo.total_volume,0) AS totalVolume,
            IFNULL(info.total_pallet_number,0) AS palletNumber,
            IFNULL(goodsinfo.total_boxes,0) AS totalBoxes,
            0 AS oddBoxes,
            sku.unit AS unit,
            sku.specification,
            IFNULL(goodsinfo.total_number,0) AS contentsNumber,
            IFNULL(goodsinfo.total_weight,0) AS weight,
            IFNULL(goodsinfo.total_volume,0) AS volume,
            IFNULL(goodsinfo.total_weight,IFNULL(goodsinfo.total_number,0)*IFNULL(goodsinfo.total_weight,0))/1000 AS totalWeight,
            IFNULL(cos.superframes_fee,0) AS superframesFee,
            IFNULL(cos.shortbarge_fee,0) AS shortbargeFee,
            IFNULL(cos.return_fee,0) AS returnFee,
            IFNULL(cos.freight,0) AS freight,
            IFNULL(cos.adjust_fee,0) AS adjustFee,
            IFNULL(cos.outboundsorting_fee,0) AS outboundsortingFee,
            IFNULL(cos.excess_fee,0) AS excessFee,
            IFNULL(cos.ultrafar_fee,0) AS ultrafarFee,
            IFNULL(cos.delivery_fee,0) AS deliveryFee,
            IFNULL(cos.exception_fee,0) AS exceptionFee,
            IFNULL(cos.other_cost1,0) AS otherCost1,
            IFNULL(cos.other_cost2,0) AS otherCost2,
            IFNULL(cos.other_cost3,0) AS otherCost3,
            IFNULL(cos.other_cost4,0) AS otherCost4,
            IFNULL(cos.other_cost5,0) AS otherCost5,
            IFNULL(cos.other_cost6,0) AS otherCost6,
            IFNULL(cos.other_cost7,0) AS otherCost7,
            IFNULL(cos.other_cost8,0) AS otherCost8,
            IFNULL(cos.other_cost9,0) AS otherCost9,
            IFNULL(cos.other_cost10,0) AS otherCost10,
            IFNULL(cos.other_cost11,0) AS otherCost11,
            IFNULL(cos.other_cost12,0) AS otherCost12,
            cos.oper_by operBy ,
            cos.remarks remarksJf,
            IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
            +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
            +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0) AS otherSum,

            IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.outboundsorting_fee,0)+IFNULL(cos.shortbarge_fee,0)
            +IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
            +IFNULL(cos.reduce_fee,0)+IFNULL(cos.exception_fee,0) AS basicsSum,

            IFNULL(cos.other_cost1,0)+IFNULL(cos.other_cost2,0)+IFNULL(cos.other_cost3,0)+IFNULL(cos.other_cost4,0)
            +IFNULL(cos.other_cost5,0)+IFNULL(cos.other_cost6,0)+IFNULL(cos.other_cost7,0)+IFNULL(cos.other_cost8,0)
            +IFNULL(cos.other_cost9,0)+IFNULL(cos.other_cost10,0)+IFNULL(cos.other_cost11,0)+IFNULL(cos.other_cost12,0)
            +IFNULL(cos.freight,0)+IFNULL(cos.delivery_fee,0)+IFNULL(cos.adjust_fee,0)+IFNULL(cos.shortbarge_fee,0)
            +IFNULL(cos.ultrafar_fee,0)+IFNULL(cos.superframes_fee,0)+IFNULL(cos.excess_fee,0)+IFNULL(cos.return_fee,0)
            +IFNULL(cos.exception_fee,0) AS sumAmt,
            cos.id
        FROM bms_yfbillmain a
        LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id
        LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.code_type = 2 AND mid.del_flag = 0
        LEFT JOIN bms_storage_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN bms_storage_code_detail_info goodsinfo ON goodsinfo.main_pk_id = info.pk_id AND goodsinfo.del_flag = 0
        LEFT JOIN mdm_warehouseinfo wah ON info.warehouse_code = wah.warehouse_code
        LEFT JOIN bms_carrierinfo cli ON cli.carrier_code = info.carrier_code
        LEFT JOIN mdm_skuinfo sku ON sku.sku_code = goodsinfo.sku_code
        WHERE cos.expenses_type IN ( 2 )
        AND a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        GROUP BY goodsinfo.id,cos.id
        ORDER BY cos.id DESC
    </select>

    <select id="getDistribution1GoodsDetailByBillId"  resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT a.id,
            info.line_code           AS     lineCode,
            info.line_name           AS     lineName,
            job.receiving_store_code AS     storeCode,
            info.finish_date                signingDate,
            job.receiving_store_name AS     receivingStore,
            count(1)                        officeTimes,
            sum(ifnull(job.total_boxes, 0)) totalBoxes
        FROM bms_yfbillmain a
        LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0 AND mid.code_type = 1
        LEFT JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN bms_job_code_info job ON job.main_pk_id = info.pk_id AND job.del_flag = 0
        LEFT JOIN bms_clientinfo cli ON cli.id = cos.client_id
        WHERE cos.expenses_type IN (1)
        AND a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        GROUP BY job.receiving_store_code
    </select>

    <select id="getDetailDays" parameterType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            DATE_FORMAT(info.finish_date,'%Y-%m-%d') signingDate,
            count(1) AS totalNumber
        FROM bms_yfbillmain a
        INNER JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0 AND mid.code_type = 1
        LEFT JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN bms_job_code_info job ON job.main_pk_id = info.pk_id
        LEFT JOIN bms_routeinfo rou ON info.line_code = rou.route_code
        LEFT JOIN bms_clientinfo cli on cli.id = cos.client_id
        WHERE cos.expenses_type IN ( 1 )
            AND (a.id = #{id} or a.fatherid=#{id})
            AND IFNULL(info.line_code,'')!=''
            AND job.receiving_store_code = #{storeCode}
        GROUP BY DATE_FORMAT(info.finish_date,'%Y-%m-%d')
    </select>

    <select id="getDistribution2GoodsDetailByBillId" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            info.scheduling_code AS schedulingBillCode, -- 调度单号
            CASE WHEN cos.charge_type = 1 THEN '自动计费'
                WHEN cos.charge_type = 2 THEN '手动计费'
                ELSE '' END
            AS chargeTypeName, -- 计费类型
            cos.expenses_code AS expensesCode, -- 计费单号
            DATE_FORMAT(cos.oper_time, '%Y-%m-%d %H:%i:%s') AS operTime, -- 计费时间
            CASE
                WHEN cos.expenses_mark = 1 THEN '正常费用'
                WHEN cos.expenses_mark = 2 THEN '冲销费'
                ELSE ''
            END AS feeFlagName, -- 费用标识
            cos.adjust_fee AS adjustFee, -- 调账费
            cos.adjust_remark AS adjustRemark, -- 调账备注
            info.virtual_scheduling_code AS virtualOrderNo, -- 虚拟调度单号
            CASE
                WHEN info.split_status = 1 THEN '未执行'
                WHEN info.split_status = 2 THEN '无需拆单'
                WHEN info.split_status = 3 THEN '已拆单'
                ELSE ''
            END AS projectQuoteName, -- 是否拆单
            DATE_FORMAT(info.create_time, '%Y-%m-%d %H:%i:%s') AS createTime, -- 新增时间
            DATE_FORMAT(info.dispatch_date, '%Y-%m-%d %H:%i:%s') AS dispatchDate, -- 配载日期
            DATE_FORMAT(info.finish_date, '%Y-%m-%d %H:%i:%s') AS finishDate, -- 完成时间
            info.company_id AS companyId, -- 单据所属公司ID
            info.carrier_name AS carrierName, -- 承运商名称
            info.carrier_code AS carrierCode, -- 承运商编码
            cli.client_name AS clientName, -- 客户名称
            info.line_code AS lineCode, -- 线路编码
            info.line_name AS lineName, -- 线路名称
            info.tms_line_code AS tlineCode, -- 干线路编码
            info.tms_line_name AS tlineName, -- 干线路名称
            SUM(info.pk_id) AS orderNumber, -- 订单数
            IFNULL(cos.freight, 0) AS freight, -- 运费
            IFNULL(cos.delivery_fee, 0) AS deliveryFee, -- 提货费
            IFNULL(cos.outboundsorting_fee, 0) AS outboundsortingFee, -- 送货费
            IFNULL(cos.shortbarge_fee, 0) AS shortbargeFee, -- 短驳费
            IFNULL(cos.ultrafar_fee, 0) AS ultrafarFee, -- 超远费
            IFNULL(cos.superframes_fee, 0) AS superframesFee, -- 超框费
            IFNULL(cos.excess_fee, 0) AS excessFee, -- 加点费
            IFNULL(cos.reduce_fee, 0) AS reduceFee, -- 减点费
            IFNULL(cos.return_fee, 0) AS returnFee, -- 装卸费
            IFNULL(cos.exception_fee, 0) AS exceptionFee, -- 异常赔付费
            IFNULL(cos.adjust_fee, 0) AS adjustFee, -- 调账费
            IFNULL(cos.other_cost1, 0) AS otherCost1, -- 其他费用1
            IFNULL(cos.other_cost2, 0) AS otherCost2, -- 其他费用2
            IFNULL(cos.other_cost3, 0) AS otherCost3, -- 其他费用3
            IFNULL(cos.other_cost4, 0) AS otherCost4, -- 其他费用4
            IFNULL(cos.other_cost5, 0) AS otherCost5, -- 其他费用5
            IFNULL(cos.other_cost6, 0) AS otherCost6, -- 其他费用6
            IFNULL(cos.other_cost7, 0) AS otherCost7, -- 其他费用7
            IFNULL(cos.other_cost8, 0) AS otherCost8, -- 其他费用8
            IFNULL(cos.other_cost9, 0) AS otherCost9, -- 其他费用9
            IFNULL(cos.other_cost10, 0) AS otherCost10, -- 其他费用10
            IFNULL(cos.other_cost11, 0) AS otherCost11, -- 其他费用11
            IFNULL(cos.other_cost12, 0) AS otherCost12, -- 其他费用12
            IFNULL(cos.other_cost1, 0) + IFNULL(cos.other_cost2, 0) + IFNULL(cos.other_cost3, 0) + IFNULL(cos.other_cost4, 0)
            + IFNULL(cos.other_cost5, 0) + IFNULL(cos.other_cost6, 0) + IFNULL(cos.other_cost7, 0) + IFNULL(cos.other_cost8, 0)
            + IFNULL(cos.other_cost9, 0) + IFNULL(cos.other_cost10, 0) + IFNULL(cos.other_cost11, 0) + IFNULL(cos.other_cost12, 0) AS otherSum, -- 其他费用总和
            IFNULL(cos.freight, 0) + IFNULL(cos.delivery_fee, 0) + IFNULL(cos.outboundsorting_fee, 0) + IFNULL(cos.shortbarge_fee, 0)
            + IFNULL(cos.ultrafar_fee, 0) + IFNULL(cos.superframes_fee, 0) + IFNULL(cos.excess_fee, 0) + IFNULL(cos.return_fee, 0)
            + IFNULL(cos.exception_fee, 0) + IFNULL(cos.reduce_fee, 0) AS basicsSum, -- 基础费用总和
            IFNULL(cos.other_cost1, 0) + IFNULL(cos.other_cost2, 0) + IFNULL(cos.other_cost3, 0) + IFNULL(cos.other_cost4, 0)
            + IFNULL(cos.other_cost5, 0) + IFNULL(cos.other_cost6, 0) + IFNULL(cos.other_cost7, 0) + IFNULL(cos.other_cost8, 0)
            + IFNULL(cos.other_cost9, 0) + IFNULL(cos.other_cost10, 0) + IFNULL(cos.other_cost11, 0) + IFNULL(cos.other_cost12, 0)
            + IFNULL(cos.freight, 0) + IFNULL(cos.delivery_fee, 0) + IFNULL(cos.outboundsorting_fee, 0) + IFNULL(cos.shortbarge_fee, 0)
            + IFNULL(cos.ultrafar_fee, 0) + IFNULL(cos.superframes_fee, 0) + IFNULL(cos.excess_fee, 0) + IFNULL(cos.return_fee, 0)
            + IFNULL(cos.exception_fee, 0) + IFNULL(cos.reduce_fee, 0) + IFNULL(cos.adjust_fee, 0) AS sumAmt, -- 总费用
            cos.oper_by AS operBy, -- 计费人姓名
            cos.remarks, -- 计费备注
            info.total_boxes AS totalBoxes, -- 总箱数
            info.total_number AS totalNumber, -- 总件数
            info.total_weight AS totalWeight, -- 总重量
            info.total_volume AS totalVolume, -- 总体积
            info.total_cargo_value AS cargoValue, -- 总货值
            info.car_model AS carModel, -- 车长
            info.driver, -- 司机
            info.originating_province AS provinceOrigin, -- 始发省
            info.originating_city AS originatingCity, -- 始发市
            info.originating_area AS originatingArea, -- 始发区
            info.destination_province AS destinationProvince, -- 目的省
            info.destination_city AS destinationCity, -- 目的市
            info.destination_area AS destinationArea, -- 目的区
            cos.bill_date AS billDate, -- 账期
            cos.id,
            cos.fee_type_first AS feeTypeFirst, -- 费用大类
            cos.is_increment AS isIncrement, -- 是否为增值费
            cos.total_extra_fee_number AS totalQuantity, -- 附加费总数量
            cos.expenses_dimension AS costDimension, -- 费用维度：1.单2.趟3.日4.月5品规
            cos.total_extra_fee_price AS price, -- 附加费单价
            '' AS automaticBillingRemark -- 自动计费备注
        FROM bms_yfbillmain a
        LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id
        LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.code_type = 1 AND mid.del_flag = 0
        LEFT JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN bms_clientinfo cli ON cli.id = info.client_id
        WHERE cos.expenses_type IN (1)
        AND a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        GROUP BY info.id, cos.id
        ORDER BY cos.id DESC, info.create_time DESC
    </select>



    <select id="distributionDetailsSummary"  resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT j1.receivingStore,
            j1.city,
            j1.lineName,
            j1.storeNum,
            sum(ifnull(j1.distributionNum, 0)) distributionNum,
            sum(j1.officeTimes)                officeTimes,
            j1.carModel,
            j1.originatingCity,
            j1.destinationCity
        FROM (
            SELECT
                job.receiving_store_name AS receivingStore,  -- 门店名称
                ''                       AS city,            -- 门店地址
                info.line_name           AS lineName,        -- 线路名称
                info.base_stores         AS storeNum,        -- 门店基数
                count(job.id)            AS distributionNum, -- 配送趟数
                1                        AS officeTimes,
                info.car_model           AS carModel,
                info.originating_city    AS originatingCity,
                info.destination_city    AS destinationCity
            FROM bms_yfbillmain a
            LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id
            LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.code_type = 1 AND mid.del_flag = 0
            LEFT JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id
            LEFT JOIN bms_job_code_info job ON job.main_pk_id = info.pk_id AND job.del_flag = 0
            WHERE cos.expenses_type IN (1)
            AND a.id IN
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
            GROUP BY info.scheduling_code, job.receiving_store_code
        ) j1
        GROUP BY j1.receivingStore
    </select>

    <select id="distributionDetailsSummary3" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            info.scheduling_code as schedulingBillCode, -- 调度单号
            info.virtual_scheduling_code as virtualOrderNo, -- 虚拟调度单号
            cli.client_code as clientCode, -- 客户编号
            cli.client_name as clientName, -- 客户名称
            info.line_code as lineCode, -- 线路编码
            info.line_name as lineName, -- 线路名称
            info.base_stores as baseStores, -- 门店基数
            COUNT(job.pk_id) as distributionNum, -- 作业单数
            info.total_kilometer as totalKilometer, -- 总里程
            info.total_out_office_times as bodyOfficeTimes, -- 基数外总店次
            info.total_in_office_times as headOfficeTimes,-- 基数内总店次
            info.delivery_mode as deliveryMode, -- 配送类型
            info.transport_type as transportType, -- 运输方式
            info.car_model as carModel, -- 车长
            info.finish_date as finishDate -- 完成时间
        FROM  bms_yfbillmain a
        LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0 AND mid.code_type = 1
        LEFT JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN bms_job_code_info job ON job.main_pk_id = info.pk_id AND job.del_flag = 0
        LEFT JOIN bms_clientinfo cli on cli.id = cos.client_id
        WHERE cos.expenses_type IN ( 1 )
            AND (a.id = #{id} or a.fatherid=#{id})
        GROUP BY info.scheduling_code,info.virtual_scheduling_code
    </select>

    <select id="distributionDetailsSummary4" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            info.line_code AS lineCode, -- 线路编码
            info.line_name as lineName, -- 线路名称
            0 as distributionNum, -- 期间报单天数
            DATE_FORMAT(info.finish_date,'%Y-%m-%01 00:00:00') as finishDateBegin, -- 完成时间
            DATE_FORMAT(info.finish_date,'%Y-%m-%31 23:59:59') as finishDateEnd -- 完成时间
        FROM  bms_yfbillmain a
        LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.code_type = 1 AND mid.del_flag = 0
        LEFT JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.id AND info.del_flag = 0
        LEFT JOIN bms_job_code_info job ON job.main_pk_id = info.pk_id AND job.del_flag = 0
        LEFT JOIN bms_clientinfo cli ON cli.id = cos.client_id
        WHERE cos.expenses_type IN ( 1 )
        AND (a.id = #{id} or a.fatherid=#{id})
        AND IFNULL(info.line_code,'')!=''
        GROUP BY info.line_code
    </select>

    <select id="distributionDetailsSummary5" parameterType="java.lang.Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            job.receiving_store_code as storeCode, -- 门店编码
            job.receiving_store_name as receivingStore, -- 门店名称
            info.line_code as lineCode, -- 线路编码
            count(info.id) as distributionNum, -- 期间报单天数
            job.base_mileage_status as ifSuperBaseKilometer, -- 是否超基数公里
            IFNULL(job.over_mileage,0) as storeDistanceKilometer, -- 超公里数
            job.base_store_status as ifBaseStores, -- 是否基数外门店
            DATE_FORMAT(info.finish_date,'%Y-%m-%01 00:00:00') as finishDateBegin, -- 完成时间
            case
                when day(last_day(info.finish_date)) =31 then DATE_FORMAT(info.finish_date,'%Y-%m-%31 23:59:59')
                when day(last_day(info.finish_date)) =30 then DATE_FORMAT(info.finish_date,'%Y-%m-%30 23:59:59')
                when day(last_day(info.finish_date)) =29 then DATE_FORMAT(info.finish_date,'%Y-%m-%29 23:59:59')
                when day(last_day(info.finish_date)) =28 then DATE_FORMAT(info.finish_date,'%Y-%m-%28 23:59:59')
                else ''
            end as  finishDateEnd -- 完成时间
        FROM bms_yfbillmain a
        LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0 AND mid.code_type = 1
        LEFT JOIN bms_dispatch_code_info info ON info.id = mid.code_pk_id
        LEFT JOIN bms_job_code_info job ON job.main_pk_id = info.pk_id AND job.del_flag = 0
        LEFT JOIN bms_clientinfo cli on cli.id = cos.client_id
        WHERE cos.expenses_type IN ( 1 )
            AND (a.id = #{id} or a.fatherid=#{id})
            AND (IFNULL(info.line_code,'')!='' OR IFNULL(info.tms_line_code,'')!='')
        GROUP BY job.store_code,DATE_FORMAT(info.finish_date,'%Y-%m')
    </select>


<!--    <select id="distributionDetailsSummary7" parameterType="Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">-->


<!--        select-->
<!--        a.lineCode,-->
<!--        a.lineName,-->
<!--        a.storeNum, &#45;&#45; 门店基数-->
<!--        count(a.dateCount) as peisonNum,-->
<!--        SUM(a.cjsNum) as cjsNum,-->
<!--         a.pstcNum &#45;&#45; 配送趟次-->
<!--        from (-->
<!--					select-->
<!--					case when IFNULL(info.tline_code,'')='' then info.line_code  else info.tline_code end as lineCode,-->
<!--					rou.route_name as lineName,-->
<!--					DATE_FORMAT(info.finish_date,'%Y-%m-%d') as dateCount,-->
<!--					 rou.store_num as storeNum, &#45;&#45; 门店基数-->
<!--					count(info.id) as peisonNum &#45;&#45; 配送天数-->
<!--					,SUM(IFNULL(info.head_office_times,0)) as cjsNum &#45;&#45; 基数内门店-->
<!--					,IFNULL(f.pstcNum,0) as pstcNum &#45;&#45; 配送趟次-->
<!--					from  bms_yfbillmain a-->
<!--					LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id-->
<!--					LEFT JOIN bms_yfexpenses_middle mid ON cos.id = mid.expenses_id-->
<!--					LEFT JOIN bms_yfbillcodeinfo info ON mid.yfbill_id = info.id-->
<!--					LEFT JOIN bms_routeinfo rou ON (case when IFNULL(info.tline_code,'')='' then info.line_code  else info.tline_code end) = rou.route_code-->
<!--					left join (-->
<!--							select-->
<!--							a.lineCode,-->
<!--							count(IFNULL(a.pstcNum,0)) as pstcNum-->
<!--							from (-->
<!--									select-->
<!--									case when IFNULL(info.tline_code,'')='' then info.line_code  else info.tline_code end as lineCode,-->
<!--									job.store_code as storeCode,-->
<!--									job.scheduling_bill_code as pstcNum-->
<!--									from  bms_yfbillmain a-->
<!--									LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id-->
<!--									LEFT JOIN bms_yfexpenses_middle mid ON cos.id = mid.expenses_id-->
<!--									LEFT JOIN bms_yfbillcodeinfo info ON mid.yfbill_id = info.id-->
<!--									LEFT JOIN bms_yfjobbillinfo job ON job.scheduling_bill_code = info.virtual_order_no-->
<!--									where-->
<!--									1=1-->
<!--									and (a.id =#{id} or a.fatherid=#{id})-->
<!--									and info.body_office_times = 0 &#45;&#45; 是否基数外门店0否1是-->
<!--									and (IFNULL(info.tline_code,'')!='' or IFNULL(info.line_code,'')!='')-->
<!--									group by (case when IFNULL(info.tline_code,'')='' then info.line_code  else info.tline_code end),job.store_code,job.scheduling_bill_code-->
<!--							) a-->
<!--							group by a.lineCode-->
<!--					) f on (case when IFNULL(info.tline_code,'')='' then info.line_code  else info.tline_code end)= f.lineCode-->
<!--					where-->
<!--					1=1-->
<!--					and (a.id =#{id} or a.fatherid=#{id})-->
<!--					and info.head_office_times > 0 &#45;&#45; 是否基数外门店0否1是-->
<!--					and (case when IFNULL(info.tline_code,'')='' then info.line_code  else info.tline_code end)!=''-->
<!--					group by (case when IFNULL(info.tline_code,'')='' then info.line_code  else info.tline_code end),DATE_FORMAT(info.finish_date,'%Y-%m-%d')-->
<!--        ) a-->
<!--        group by a.lineCode-->
<!--    </select>-->


    <select id="distributionDetailsSummary9" parameterType="Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        select
            a.temperatureType,
            a.signingDate,
            a.lineName,
            a.lineCode
        from (
                 SELECT
                     '1' AS temperatureType,
                     a.bill_date AS signingDate,
                     info.line_name AS lineName,
                     info.line_code AS lineCode
                 FROM  bms_yfbillmain a
                           INNER JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
                           INNER JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0 AND mid.code_type = 1
                           INNER JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id
                 WHERE (a.id = #{id} or a.fatherid = #{id})
                   AND cos.expenses_type IN ( 1 )
                   AND IFNULL(info.line_code,'')!=''
                 GROUP BY info.line_code
                 UNION ALL
                 SELECT
                     '2' AS temperatureType, -- 非常温
                     a.bill_date AS signingDate,
                     info.line_name AS lineName, -- 线路名称
                     info.line_code AS lineCode
                 FROM  bms_yfbillmain a
                     INNER JOIN bms_yfcost_info cos ON a.id = cos.bill_id
                     INNER JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.code_type = 1
                     INNER JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id
                 WHERE cos.expenses_type IN ( 1 )
                   AND (a.id = #{id} OR a.fatherid = #{id})
                   AND IFNULL(info.line_code,'')!=''
                 GROUP BY info.line_code
             )a
        ORDER BY a.lineCode,a.temperatureType
    </select>

    <select id="getDetailDays2" parameterType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            info.finish_date AS signingDate, -- 完成日期
            sum(IFNULL(goods.total_number,0)) AS totalNumber -- 总件数
        FROM bms_yfbillmain a
        LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.code_type = 1 AND mid.del_flag = 0
        LEFT JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id
        LEFT JOIN bms_job_code_info job ON job.main_pk_id = info.pk_id AND job.del_flag = 0
        LEFT JOIN bms_job_code_detail_info goods ON goods.main_pk_id = job.pk_id AND goods.del_flag = 0
        WHERE cos.expenses_type IN ( 1 )
        AND (a.id =#{id} or a.fatherid=#{id})
        AND IFNULL(info.line_code,'')!=''
        AND IFNULL(info.line_code,'') = #{lineCode}
        <if test="temperatureType!=null and temperatureType!='' and temperatureType== 1">
            AND IFNULL(goods.temperature_type,'')!=''
            AND IFNULL(goods.temperature_type,'')='CW'
        </if>
        <if test="temperatureType!=null and temperatureType!='' and temperatureType== 2">
            AND IFNULL(goods.temperature_type,'')!='CW'
        </if>
        GROUP BY info.finish_date
    </select>

    <!--账单数据汇总-->
    <select id="getGroupDistributionNumByBillId" parameterType="Long" resultType="com.bbyb.joy.bms.domain.dto.BmsYsbillcodeinfo">
        SELECT sum(t1.distributionStoreNum) distributionStoreNum,
               t1.id,
               t3.storeNum,
               t4.totalNumber,
               t4.totalWeight,
               t4.totalVolume,
               t4.cargoValue,
               t4.totalBoxes
        FROM (
            SELECT
              1 AS distributionStoreNum,
              a.id
            FROM bms_yfbillmain a
            LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
            LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0 AND mid.code_type = 1
            LEFT JOIN bms_dispatch_code_info info on mid.code_pk_id = info.pk_id
            LEFT JOIN bms_job_code_info byji on byji.main_pk_id = info.pk_id and byji.del_flag = 0
            WHERE a.del_flag = 0
                AND cos.expenses_type = 1
                AND (a.id = #{id} OR a.fatherid = #{id})
            GROUP BY info.scheduling_code, byji.receiving_store_code
         ) t1
        LEFT JOIN (
            SELECT
                sum(t2.storeNum) storeNum,
                t2.id
            FROM (
                 SELECT
                     1 as storeNum,
                     a.id
                 FROM bms_yfbillmain a
                          INNER JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
                          INNER JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.code_type = 1 AND mid.del_flag = 0
                          INNER JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id
                          INNER JOIN bms_job_code_info byji ON byji.main_pk_id = info.pk_id AND byji.del_flag = 0
                 WHERE a.del_flag = 0
                   AND cos.expenses_type = 1
                   AND (a.id = #{id} or a.fatherid=#{id})
                 GROUP BY byji.receiving_store_code
             ) t2
        ) t3 ON t1.id=t3.id
        LEFT JOIN (
            SELECT
                SUM(IFNULL(info.total_number,0)) AS totalNumber,
                SUM(IFNULL(info.total_weight,0)) AS totalWeight,
                SUM(IFNULL(info.total_volume,0)) AS totalVolume,
                SUM(IFNULL(info.total_cargo_value,0)) AS cargoValue,
                SUM(IFNULL(info.total_boxes,0)) AS totalBoxes,
                a.id
            FROM bms_yfbillmain a
            INNER JOIN bms_yfcost_info cos ON a.id = cos.bill_id  and cos.del_flag = 0
            INNER JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id and mid.del_flag=0 AND mid.code_type = 1
            INNER JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id
            WHERE a.del_flag=0
              AND (a.id = #{id} or a.fatherid= #{id})
              AND cos.expenses_type =1
        ) t4 ON t1.id=t4.id
    </select>

    <select id="newDistributionDetailsSummary7"  resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        <!--线路配送总店次-->
        SELECT
            sum(IFNULL(t1.cjsNum,0)) cjsNum, -- 线路配送总店次
            t1.lineCode,
            t1.lineName,
            t2.storeNum,  -- 覆盖门店数
            t2.pstcNum,  -- 配送趟次
            t3.bodyOfficeTimes, -- 基数外总店次
            t3.headOfficeTimes, -- 基数内总店次
            t3.officeTimes  -- 非基数总店次
        FROM (
            SELECT
                info.line_code AS lineCode,
                info.line_name AS lineName,
                1 AS cjsNum
            FROM  bms_yfbillmain a
            LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
            LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag=0 AND mid.code_type = 1
            LEFT JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id AND info.del_flag = 0
            LEFT JOIN bms_job_code_info byji ON byji.main_pk_id = info.pk_id AND byji.del_flag = 0
            WHERE a.id IN
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        GROUP BY info.line_code,info.scheduling_code,byji.receiving_store_code
        ) t1
        INNER JOIN (
        #     <!-- 覆盖门店数-->
            SELECT
                IFNULL(info.line_code,'')='' AS lineCode,
                count(DISTINCT byji.receiving_store_code) storeNum,  -- 覆盖门店数
                count(DISTINCT info.scheduling_code) pstcNum  -- 配送趟次
            FROM bms_yfbillmain a
            LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id
            LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0 AND mid.code_type = 1
            LEFT JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id AND info.del_flag=0
            LEFT JOIN bms_job_code_info byji ON byji.main_pk_id = info.pk_id AND byji.del_flag=0
            WHERE a.id IN
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
            GROUP BY info.line_code
        ) t2 on t1.lineCode=t2.lineCode
        INNER JOIN (
            SELECT
                c.lineCode,
                sum(CASE WHEN c.if_base_stores=0 THEN 1 ELSE 0 END)  headOfficeTimes,  -- 基数内总店次
                sum(CASE WHEN c.if_base_stores=1 THEN 1 ELSE 0 END)  bodyOfficeTimes,   -- 基数外总店次
                sum(CASE WHEN c.if_base_stores=-1 THEN 1 ELSE 0 END)  officeTimes  -- 非基数外总店次
            FROM (
                SELECT
                info.line_code AS lineCode,
                job.base_store_status AS if_base_stores
                FROM  bms_yfbillmain a
                LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
                LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag = 0 AND mid.code_type = 1
                LEFT JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id AND info.del_flag=0
                LEFT JOIN bms_job_code_info job on info.pk_id=job.main_pk_id
                WHERE a.id IN
                <foreach collection="ids" item="ids" open="(" separator="," close=")">
                    #{ids}
                </foreach>
                GROUP BY info.line_code
            ) c
            GROUP BY c.lineCode
        ) t3 ON t1.lineCode=t3.lineCode
        GROUP BY t1.lineCode
    </select>


    <select id="newDistributionDetailsSummary3"  resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            job.relate_code relateCode,  -- 作业单号
            info.virtual_scheduling_code as virtualOrderNo, -- 虚拟调度单号
            job.client_code as clientCode, -- 客户编号
            job.client_name as clientName, -- 客户名称
            job.company_id companyId,  -- 单据所属
            job.receiving_store_code storeCode,  -- 门店编码
            job.receiving_store_name receivingStore,  -- 门店名称
            ifnull(job.total_number,0) totalNumber,  -- 总件数
            ifnull(job.total_weight,0) totalWeight,  -- 总重量
            ifnull(job.total_volume,0) totalVolume,  -- 总体积
            ifnull(job.total_cargo_value,0) cargoValue,  -- 总货值
            ifnull(job.total_boxes,0) totalBoxes,  -- 总箱数
            CASE WHEN job.base_store_status=0 then '否' else '是' end as ifBaseStores,   -- 是否基数外门店0否1是
            CASE WHEN job.base_mileage_status=0 then'否' else '是' end as ifSuperBaseKilometer,  -- 是否超基数公里0否1是
            ifnull(job.over_mileage,0) storeDistanceKilometer,  -- 超公里数
            job.destination_province destinationProvince,  -- 目的省
            job.destination_city destinationCity,  -- 目的市
            job.destination_area destinationArea,  -- 目的区
            0  nearStoreKm  -- 距离最近门店公里数
        FROM  bms_yfbillmain a
        LEFT JOIN bms_yfcost_info cos ON a.id = cos.bill_id AND cos.del_flag = 0
        LEFT JOIN bms_yfexpenses_middle mid ON cos.main_pk_id = mid.main_pk_id AND mid.del_flag=0 AND mid.code_type = 1
        LEFT JOIN bms_dispatch_code_info info ON mid.code_pk_id = info.pk_id AND info.del_flag = 0
        LEFT JOIN bms_job_code_info job ON job.main_pk_id = info.pk_id AND job.del_flag = 0
        WHERE a.del_flag=0
        AND a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        AND cos.expenses_type IN ( 1 )
    </select>
    <select id="getBillCodeById"  resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT
            a.bill_code AS billCode
        FROM bms_yfbillmain a
        WHERE a.del_flag=0
        AND a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </select>

    <select id="selectBmsYfBillExtionFee" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYfbillmainExportAll">
        SELECT
            a.bill_type AS billType ,
            IFNULL(a.responsible_money,0) AS exceptionFee
        FROM bms_yfbillmain a
        WHERE a.merge_status = 0
        AND a.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </select>


    <select id="getValueAddedFeeByBillIdGroupBy" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT DATE_FORMAT(ad.creat_date, '%Y-%m-%d') AS operTime
            , ad.relate_code                         AS relateCode
            , ad.expenses_code                       AS expensesCode
            , cli.client_name                        AS clientName
            , SUM(IFNULL(ad.number, 0))              AS skuNumber
            , ''                                     AS unit
            , ''                                     AS itemName
            , SUM(IFNULL(ad.amount, 0))              AS amount
            , ad.remark                              AS remarks
            , ad.fee_belong                          AS feeBelong
        FROM bms_addedfee ad
        LEFT JOIN bms_yfbillmain bill ON bill.id = ad.bill_id
        LEFT JOIN bms_clientinfo cli ON cli.id = ad.client_code AND cli.del_flag = 0
        WHERE settle_type = 2
        AND bill.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        AND ad.del_flag = 0
        AND settle_type = 2
        GROUP BY ad.item_id, fee_belong
        ORDER BY ad.creat_date DESC
    </select>

    <select id="getValueAddedFeeByBillId" parameterType="Long" resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT ad.expenses_code                                   AS expensesCode,
            ad.relate_code                                     AS relateCode,
            DATE_FORMAT(ad.charging_date, '%Y-%m-%d %H:%i:%s') AS chargingDate,
            ad.item_olevel_id                                  AS itemOlevelId,
            ''                                                 AS itemOlevelName,
            ad.item_id                                         AS itemId,
            ''                                                 AS itemName,
            ad.amount                                          AS amount,
            IFNULL(ad.number, 0)                               AS skuNumber,
            ''                                                 AS unit,
            ad.fee_source                                      AS feeSource,
            ad.order_source                                    AS orderSource,
            DATE_FORMAT(ad.creat_date, '%Y-%m-%d')             AS operTime,
            ad.warehouse_code                                  AS warehouseCode,
            mw.warehouse_name                                  AS warehouseName,
            cli.client_name                                    AS clientName,
            IFNULL(ad.amount, 0)                               AS amount,
            ad.remark                                          AS remark,
            ad.fee_belong                                      AS feeBelong,
            CASE
                WHEN ad.fee_belong = 1 THEN '运输增值'
                WHEN ad.fee_belong = 2 THEN '仓储增值'
                ELSE ''
            END                                            AS feeBelongDesc
        FROM bms_addedfee ad
        LEFT JOIN bms_yfbillmain bill ON bill.id = ad.bill_id
        LEFT JOIN bms_clientinfo cli ON cli.client_code = ad.client_code AND cli.del_flag = 0
        LEFT JOIN mdm_warehouseinfo mw ON mw.warehouse_code = ad.warehouse_code
        WHERE ad.del_flag = 0
        AND bill.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        ORDER BY ad.item_id DESC, ad.creat_date DESC
    </select>

    <select id="getFixedExpensesByBillIdGroupBy"
            resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT fix.oper_time              AS operTime
            , fix.expenses_code          AS expensesCode
            , cli.client_name            AS clientName
            , ''                         AS itemName
            , SUM(IFNULL(fix.amount, 0)) AS amount
            , fix.remark                 AS remarks
        FROM pub_yf_fixedfee fix
        LEFT JOIN bms_yfbillmain bill ON bill.id = fix.bill_id
        LEFT JOIN bms_clientinfo cli ON cli.id = fix.client_id AND cli.del_flag = 0
        WHERE fix.del_flag = 0
        AND bill.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        GROUP BY fix.item_id
        ORDER BY fix.oper_time DESC
    </select>
    <select id="getFixedExpensesByBillId"
            resultType="com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo">
        SELECT fix.expenses_code     AS expensesCode,
            cli.client_name       AS clientName,
            fix.item_id           AS itemId,
            ''                    AS itemName,
            IFNULL(fix.amount, 0) AS amount,
            fix.frequency         AS frequency,
            fix.start_date        AS startDate,
            fix.end_date          AS endDate,
            fix.oper_time         AS operTime,
            cli.client_name       AS clientName,
            fix.remark            AS remarks
        FROM pub_yf_fixedfee fix
        LEFT JOIN bms_yfbillmain bill ON bill.id = fix.bill_id
        LEFT JOIN bms_clientinfo cli ON cli.id = fix.client_id AND cli.del_flag = 0
        WHERE fix.del_flag = 0
        AND bill.id IN
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        ORDER BY fix.item_id DESC, fix.oper_time DESC
    </select>
</mapper>
