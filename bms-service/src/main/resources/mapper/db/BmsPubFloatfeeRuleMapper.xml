<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsPubFloatfeeRuleMapper">
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsPubFloatfeeRule" id="BmsPubFloatfeeRuleMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
        <result property="ruleMainId" column="rule_main_id" jdbcType="VARCHAR"/>
        <result property="festivalName" column="festival_name" jdbcType="VARCHAR"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="ruleType" column="rule_type"/>
        <result property="startHour" column="start_hour"/>
        <result property="endHour" column="end_hour"/>
        <result property="ratio" column="ratio" jdbcType="DECIMAL"/>
        <result property="isEnable" column="is_enable" jdbcType="CHAR"/>
        <result property="operBy" column="oper_by" jdbcType="VARCHAR"/>
        <result property="operCode" column="oper_code" jdbcType="VARCHAR"/>
        <result property="operTime" column="oper_time"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="orderType" column="order_type" jdbcType="INTEGER"/>
        <result property="orderTypeDesc" column="order_type_desc" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultMap="BmsPubFloatfeeRuleMap">
        select
            id,rule_name,start_date,end_date,ratio,is_enable,oper_by,oper_code,oper_time,created_by,created_time,updated_by,updated_time,remark
        ,rule_type,start_hour,end_hour
        from pub_floatfee_rule
        where id = #{id}
    </select>


    <select id="searchListByRuleName" resultMap="BmsPubFloatfeeRuleMap">
        select id,rule_name,ratio,is_enable,oper_by,oper_code,oper_time,created_by,created_time,updated_by,updated_time,order_type,
        case when order_type = 1 then '按比例'
        when order_type = 2 then '按金额'
        else ''
        end as order_type_desc
        from pub_floatfee_rule_main
        <where>
            del_flag=0
            <if test="ruleName != null and ruleName != ''">
                and rule_name like #{ruleName}
            </if>
            <if test="orderType != null ">
                and order_type = #{orderType}
            </if>
        </where>
        ORDER BY oper_time DESC
    </select>
    <select id="queryByLimit" resultMap="BmsPubFloatfeeRuleMap" parameterType="com.bbyb.joy.bms.domain.dto.BmsPubFloatfeeRule">
        select id,rule_name,start_date,end_date,ratio,is_enable,oper_by,oper_code,oper_time,created_by,created_time,updated_by,updated_time,remark,rule_Type
        from pub_floatfee_rule
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="ruleName != null and ruleName != ''">
                and rule_name = #{ruleName}
            </if>
            <if test="startDate != null and startDate != ''">
                and start_date = #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                and end_date = #{endDate}
            </if>
            <if test="ratio != null and ratio != ''">
                and ratio = #{ratio}
            </if>
            <if test="isEnable != null and isEnable != ''">
                and is_enable = #{isEnable}
            </if>
            <if test="operBy != null and operBy != ''">
                and oper_by = #{operBy}
            </if>
            <if test="operCode != null and operCode != ''">
                and oper_code = #{operCode}
            </if>
            <if test="operTime != null and operTime != ''">
                and oper_time = #{operTime}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null and createdTime != ''">
                and created_time = #{createdTime}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and updated_by = #{updatedBy}
            </if>
            <if test="updatedTime != null and updatedTime != ''">
                and updated_time = #{updatedTime}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
        ORDER BY oper_time DESC
    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into pub_floatfee_rule(id,rule_name,start_date,end_date,rule_type,start_hour,end_hour,ratio,is_enable,oper_by,oper_code,oper_time,created_by,created_time,updated_by,updated_time,remark)
        values (#{id},#{ruleName},#{startDate},#{endDate},#{ruleType},#{startHour},#{endHour},#{ratio},#{isEnable},#{operBy},#{operCode},#{operTime},#{createdBy},#{createdTime},#{updatedBy},#{updatedTime},#{remark})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        pub_floatfee_rule(id,rule_main_id,festival_name, rule_type,
        rule_name,start_date,end_date,start_hour,end_hour,ratio,is_enable,oper_by,oper_code,oper_time,created_by,created_time,updated_by,updated_time,remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.ruleMainId},#{entity.festivalName}, #{entity.ruleType},
            #{entity.ruleName},#{entity.startDate},#{entity.endDate},#{entity.startHour},#{entity.endHour},#{entity.ratio},#{entity.isEnable},#{entity.operBy},#{entity.operCode},#{entity.operTime},#{entity.createdBy},#{entity.createdTime},#{entity.updatedBy},#{entity.updatedTime},#{entity.remark})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into pub_floatfee_rule(id,rule_name,start_date,end_date,ratio,is_enable,oper_by,oper_code,oper_time,created_by,created_time,updated_by,updated_time,remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.ruleName},#{entity.startDate},#{entity.endDate},#{entity.ratio},#{entity.isEnable},#{entity.operBy},#{entity.operCode},#{entity.operTime},#{entity.createdBy},#{entity.createdTime},#{entity.updatedBy},#{entity.updatedTime},#{entity.remark})
        </foreach>
        on duplicate key update
        id=values(id),
        rule_name=values(rule_name),
        start_date=values(start_date),
        end_date=values(end_date),
        ratio=values(ratio),
        is_enable=values(is_enable),
        oper_by=values(oper_by),
        oper_code=values(oper_code),
        oper_time=values(oper_time),
        created_by=values(created_by),
        created_time=values(created_time),
        updated_by=values(updated_by),
        updated_time=values(updated_time),
        remark=values(remark)
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.bbyb.joy.bms.domain.dto.BmsPubFloatfeeRule">
        update pub_floatfee_rule
        <set>
            <if test="ruleName != null and ruleName != ''">
                rule_name = #{ruleName},
            </if>
            <if test="startDate != null">
                start_date = #{startDate},
            </if>
            <if test="endDate != null">
                end_date = #{endDate},
            </if>
            <if test="ruleType != null">
                rule_type = #{ruleType},
            </if>
            <if test="startHour != null">
                start_hour = #{startHour},
            </if>
            <if test="endHour != null">
                end_hour = #{endHour},
            </if>
            <if test="ratio != null and ratio != ''">
                ratio = #{ratio},
            </if>
            <if test="isEnable != null and isEnable != ''">
                is_enable = #{isEnable},
            </if>
            <if test="operBy != null and operBy != ''">
                oper_by = #{operBy},
            </if>
            <if test="operCode != null and operCode != ''">
                oper_code = #{operCode},
            </if>
            <if test="operTime != null">
                oper_time = #{operTime},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="ruleMainId !=null and ruleMainId !=''">
                rule_main_id=#{ruleMainId},
            </if>
            <if test="festivalName !=null and festivalName !=''">
                festival_name=#{festivalName}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键作废-->
    <delete id="deleteById">
        delete from pub_floatfee_rule where id = #{id}
    </delete>

    <update id="deleteByRuleMainId" parameterType="java.util.Map">
        update pub_floatfee_rule set del_flag=1 where  rule_main_id=#{id};
    </update>

    <update id="deleteByRuleBatchMainId" parameterType="java.util.Map">
        update pub_floatfee_rule set del_flag=1 where  rule_main_id in
        <foreach collection="ids" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>;
    </update>



    <resultMap type="com.bbyb.joy.bms.domain.dto.dto.BmsMdmClientinfoDto" id="MdmClientinfoMap">
        <result property="clientCode" column="client_code" jdbcType="VARCHAR"/>
        <result property="clientName" column="client_name" jdbcType="VARCHAR"/>
    </resultMap>


    <!-- 查询所有用户信息 -->
    <select id="queryMdmClientInfo" resultMap="MdmClientinfoMap">
        select
            client_code,client_name
        from bms_clientinfo
        where IFNULL(del_flag,0)=0
          and IFNULL(is_enable,0)=0
    </select>


    <resultMap type="com.bbyb.joy.bms.domain.dto.dto.BmsExpenseItemByPubFloatFeeDto" id="BmsExpenseItemMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="itemCode" column="item_code" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="fatherId" column="father_id" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
    </resultMap>



    <select id="queryBmsExpenseItemUnitInfo" resultMap="BmsExpenseItemMap">
        SELECT
        pfs.id,
        pfs.item_code,
        pfs.item_name,
        pfs.father_id,
        pfs.del_flag
        FROM pub_fee_subject pfs
        WHERE  pfs.del_flag = 0 and  pfs.show_type = 4
        <if test="id !=null and id !=''">
            and father_id=#{id}
        </if>
    </select>

    <select id="queryList" parameterType="java.util.Map" resultMap="BmsPubFloatfeeRuleMap">
        select   rule.id,rule.rule_main_id,rule.festival_name, rule.rule_type,
        rule.rule_name,rule.start_date,rule.end_date,rule.start_hour,rule.end_hour,rule.ratio,main.is_enable,rule.oper_by,rule.oper_code,rule.oper_time,rule.created_by,rule.created_time,rule.updated_by,rule.updated_time,rule.remark
        from pub_floatfee_rule_main main left join pub_floatfee_rule rule on main.id=rule.rule_main_id where rule.del_flag=0 and main.del_flag=0
        <if test="ruleMainId !=null">
            and rule_main_id=#{ruleMainId}
        </if>
        <if test="orderType !=null">
            and main.order_type=#{orderType}
        </if>
        <if test="id !=null and id != ''">
            and main.id <![CDATA[<>]]>#{id}
        </if>;
    </select>

    <insert id="bindingAmountType" >
        insert  into  pub_floatfee_rule_amount (id,rule_id, client_id, subject_id, amount, unit, del_flag) values
        <foreach collection="amounts" item="entity" separator=",">
            (#{entity.id},#{entity.ruleId},#{entity.clientId},#{entity.subjectId},#{entity.amount},#{entity.unit},#{entity.delFlag})
        </foreach>
    </insert>

    <delete id="deletedAmountTypeByRuleId">
        delete from pub_floatfee_rule_amount where rule_id = #{ruleId}
    </delete>

    <select id="queryAmountType" resultMap="amountResult">
        select <include refid="amount"/> ,mc.client_name,  pfra.client_id
        ,dict_label as unitName,mc.client_name as client_name  from
        pub_floatfee_rule_amount pfra
        left join bms_clientinfo mc on mc.id=pfra.client_id
        left join sys_dict_data on dict_value=pfra.unit and dict_type='bms_float_unit' and status=0
        where rule_id=#{ruleId} and pfra.del_flag=0
    </select>

    <!-- 通用查询结果列 -->
    <sql id="amount">
        pfra.id, pfra.rule_id, pfra.client_code, pfra.subject_id, pfra.amount, pfra.unit, pfra.del_flag
    </sql>

    <resultMap id="amountResult" type="com.bbyb.joy.bms.domain.dto.PubFloatFeeRuleAmount">
        <id column="id" property="id" />
        <result column="rule_id" property="ruleId" />
        <result column="client_code" property="clientCode" />
        <result column="client_id" property="clientId" />
        <result column="client_name" property="clientName" />
        <result column="subject_id" property="subjectId" />
        <result column="amount" property="amount" />
        <result column="unit" property="unit" />
        <result column="del_flag" property="delFlag" />
        <result column="client_name" property="clientName" />
        <result column="item_name" property="itemName" />
        <result column="unitName" property="unitName" />
    </resultMap>

</mapper>