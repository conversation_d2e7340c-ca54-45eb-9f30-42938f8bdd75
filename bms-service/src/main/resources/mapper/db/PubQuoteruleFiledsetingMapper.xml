<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubQuoteruleFiledsetingMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.PubQuoteruleFiledseting" id="PubQuoteruleFiledsetingResult">
        <result property="id"    column="id"    />
        <result property="columnProperties"    column="column_properties"    />
        <result property="columnChinese"    column="column_chinese"    />
        <result property="columnEnglish"    column="column_english"    />
        <result property="columnType"    column="column_type"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPubQuoteruleFiledsetingVo">
        select id, column_properties, column_chinese, column_english, column_type, remark from pub_quoterule_filedseting
    </sql>

    <select id="selectPubQuoteruleFiledsetingList" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoteruleFiledseting" resultMap="PubQuoteruleFiledsetingResult">
        <include refid="selectPubQuoteruleFiledsetingVo"/>
        <where>
            <if test="columnProperties != null "> and column_properties = #{columnProperties}</if>
            <if test="columnChinese != null  and columnChinese != ''"> and column_chinese = #{columnChinese}</if>
            <if test="columnEnglish != null  and columnEnglish != ''"> and column_english = #{columnEnglish}</if>
            <if test="columnType != null "> and column_type = #{columnType}</if>
        </where>
    </select>
</mapper>