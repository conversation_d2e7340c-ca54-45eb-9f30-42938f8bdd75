<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsFixedExpenseInfoMapper">
  <resultMap id="BaseResultMap" type="com.bbyb.joy.bms.domain.dto.BmsFixedExpenseInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="expenses_code" jdbcType="VARCHAR" property="expensesCode" />
    <result column="rule_id" jdbcType="INTEGER" property="ruleId" />
    <result column="client_id" jdbcType="INTEGER" property="clientId" />
    <result column="carrier_code" jdbcType="VARCHAR" property="carrierCode" />
    <result column="settle_type" jdbcType="CHAR" property="settleType" />
    <result column="item_id" jdbcType="INTEGER" property="itemId" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="frequency" jdbcType="CHAR" property="frequency" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="bill_id" jdbcType="INTEGER" property="billId" />
    <result column="bill_code" jdbcType="VARCHAR" property="billCode"/>
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
    <result column="oper_code" jdbcType="VARCHAR" property="operCode" />
    <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <resultMap id="ListMap" type="com.bbyb.joy.bms.domain.dto.dto.BmsFixedExpenseDto">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="expenses_code" jdbcType="VARCHAR" property="expensesCode" />
    <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
    <result column="client_id" jdbcType="VARCHAR" property="clientId" />
    <result column="item_id" jdbcType="INTEGER" property="itemId" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="bill_id" jdbcType="INTEGER" property="billId" />
    <result column="bill_status" jdbcType="VARCHAR" property="billStatus"/>
    <result column="bill_code" jdbcType="VARCHAR" property="billCode"/>
    <result column="settle_type" jdbcType="CHAR" property="settleType" />
    <result column="frequency" jdbcType="CHAR" property="frequency"/>
    <result column="adjust_before_amount" jdbcType="DECIMAL" property="adjustBeforeAmount"/>
    <result column="adjust_before_remark" jdbcType="VARCHAR" property="adjustBeforeRemark"/>
    <result column="show_bill_code" jdbcType="VARCHAR" property="showBillCode"/>
    <result column="show_bill_id" jdbcType="INTEGER" property="showBillId"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, expenses_code, rule_id, client_id, carrier_code, settle_type, item_id, start_date,
    end_date, frequency, amount, bill_id, remark, oper_by, oper_code, oper_time, del_flag
  </sql>

  
  <select id="selectByParam" parameterType="com.bbyb.joy.bms.domain.dto.querybean.BmsFixedExpenseQueryBean" resultMap="ListMap">
      SELECT
          tb1.id,
          tb1.expenses_code,
          tb1.settle_type,
          tb2.client_name,
          tb1.frequency,
          tb1.client_id,
          tb1.item_id,
          CAST(tb1.amount as DECIMAL(18,2)) AS amount,
          tb1.start_date,
          tb1.end_date,
          tb1.oper_time,
          tb1.remark,
          tb1.bill_id,
          tb1.bill_code,
          IF( tb1.bill_id IS NULL, '未生成', '已生成' ) AS bill_status,
          CAST(tb1.adjust_before_amount as DECIMAL(18,2)) AS adjust_before_amount,
          tb1.adjust_before_remark,
          ifnull(tb1.show_bill_code,tb1.bill_code) as show_bill_code,
          ifnull(tb1.show_bill_id,tb1.bill_id) as show_bill_id
      FROM bms_fixedfee tb1
      LEFT JOIN bms_clientinfo tb2 ON tb1.client_id = tb2.id
    <where>
        tb1.del_flag = 0
        <if test="begin != null">
            and tb1.oper_time >= #{begin}
        </if>
        <if test="end != null">
            and tb1.oper_time &lt;= #{end}
        </if>
        <if test="clientId != null">
            and tb1.client_id = #{clientId}
        </if>
        <if test="frequency != null and frequency != ''">
            and tb1.frequency = #{frequency}
        </if>
        <if test="expensesCode != null and expensesCode != ''">
            and tb1.expenses_code = #{expensesCode}
        </if>
        <if test="itemId != null">
            and tb1.item_id = #{itemId}
        </if>
        <if test="billStatus == 1">
            and tb1.bill_id is not null
        </if>
        <if test="billStatus == 0">
            and tb1.bill_id is null
        </if>
        <if test="billId != null">
            and tb1.bill_id = #{billId}
        </if>
        <if test="clientIds != null and clientIds.size()>0">
            and tb1.client_id in
            <foreach collection="clientIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="carrierCodes != null and carrierCodes.size() > 0">
            and tb1.carrier_code in
            <foreach collection="carrierCodes"  item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="settleType != null and settleType != ''">
            and tb1.settle_type = #{settleType}
        </if>
        <if test="ids != null">
            and tb1.id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </where>
    order by tb1.id desc
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bms_fixedfee
        where id = #{id,jdbcType=INTEGER}
  </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.bbyb.joy.bms.domain.dto.BmsFixedExpenseInfo">
        update bms_fixedfee
        <set>
            <if test="expensesCode != null">
                expenses_code = #{expensesCode,jdbcType=VARCHAR},
            </if>
            <if test="ruleId != null">
                rule_id = #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="clientId != null">
                client_id = #{clientId,jdbcType=INTEGER},
            </if>
            <if test="carrierCode != null">
                carrier_code = #{carrierCode,jdbcType=VARCHAR},
            </if>
            <if test="settleType != null">
                settle_type = #{settleType,jdbcType=CHAR},
            </if>
            <if test="itemId != null">
                item_id = #{itemId,jdbcType=INTEGER},
            </if>
            <if test="startDate != null">
                start_date = #{startDate,jdbcType=TIMESTAMP},
            </if>
            <if test="endDate != null">
                end_date = #{endDate,jdbcType=TIMESTAMP},
            </if>
            <if test="frequency != null">
                frequency = #{frequency,jdbcType=CHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="billId != null">
                bill_id = #{billId,jdbcType=INTEGER},
            </if>
            <if test="billCode != null">
                bill_code = #{billCode,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operBy != null">
                oper_by = #{operBy,jdbcType=VARCHAR},
            </if>
            <if test="operCode != null">
                oper_code = #{operCode,jdbcType=VARCHAR},
            </if>
            <if test="operTime != null">
                oper_time = #{operTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateBatchByYs">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";" >
            update bms_fixedfee
            <set>
                <if test="item.amount != null">
                    amount = #{item.amount},
                </if>
                <if test="item.operBy != null">
                    oper_by = #{item.operBy},
                </if>
                <if test="item.operCode != null">
                    oper_code = #{item.operCode},
                </if>
                <if test="item.operTime != null">
                    oper_time = #{item.operTime},
                </if>
                <if test="item.delFlag != null">
                    del_flag = #{item.delFlag},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark},
                </if>
                <if test="item.adjustBeforeAmount != null">
                    adjust_before_amount = #{item.adjustBeforeAmount},
                </if>
                <if test="item.adjustBeforeRemark != null and item.adjustBeforeRemark != ''">
                    adjust_before_remark = #{item.adjustBeforeRemark},
                </if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <select id="selectForExistCheck" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM bms_fixedfee WHERE client_id = #{clientId} AND item_id = #{itemId}
                                    AND del_flag = 0 AND NOT (end_date &lt;= #{startDate} OR start_date >= #{endDate})
    </select>
    
    <select id="selectMaxCodeOfDay" resultType="java.lang.String">
        SELECT MAX(RIGHT(expenses_code,5))  AS num FROM bms_fixedfee WHERE expenses_code LIKE CONCAT('FF_', #{date}, '%')
    </select>

    <insert id="insertSelective" parameterType="com.bbyb.joy.bms.domain.dto.BmsFixedExpenseInfo">
        insert into bms_fixedfee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="expensesCode != null">
                expenses_code,
            </if>
            <if test="ruleId != null">
                rule_id,
            </if>
            <if test="clientId != null">
                client_id,
            </if>
            <if test="carrierCode != null">
                carrier_code,
            </if>
            <if test="settleType != null">
                settle_type,
            </if>
            <if test="itemId != null">
                item_id,
            </if>
            <if test="startDate != null">
                start_date,
            </if>
            <if test="endDate != null">
                end_date,
            </if>
            <if test="frequency != null">
                frequency,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="billId != null">
                bill_id,
            </if>
            <if test="billCode != null">
                bill_code,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="operBy != null">
                oper_by,
            </if>
            <if test="operCode != null">
                oper_code,
            </if>
            <if test="operTime != null">
                oper_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="adjustBeforeAmount != null">
                adjust_before_amount,
            </if>
            <if test="adjustBeforeRemark != null">
                adjust_before_remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="expensesCode != null">
                #{expensesCode,jdbcType=VARCHAR},
            </if>
            <if test="ruleId != null">
                #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="clientId != null">
                #{clientId,jdbcType=INTEGER},
            </if>
            <if test="carrierCode != null">
                #{carrierCode,jdbcType=VARCHAR},
            </if>
            <if test="settleType != null">
                #{settleType,jdbcType=CHAR},
            </if>
            <if test="itemId != null">
                #{itemId,jdbcType=INTEGER},
            </if>
            <if test="startDate != null">
                #{startDate,jdbcType=DATE},
            </if>
            <if test="endDate != null">
                #{endDate,jdbcType=DATE},
            </if>
            <if test="frequency != null">
                #{frequency,jdbcType=CHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="billId != null">
                #{billId,jdbcType=INTEGER},
            </if>
            <if test="billCode != null">
                #{billCode,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operBy != null">
                #{operBy,jdbcType=VARCHAR},
            </if>
            <if test="operCode != null">
                #{operCode,jdbcType=VARCHAR},
            </if>
            <if test="operTime != null">
                #{operTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="adjustBeforeAmount != null">
                #{adjustBeforeAmount,jdbcType=DECIMAL},
            </if>
            <if test="adjustBeforeRemark != null">
                #{adjustBeforeRemark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

  <insert id="insertBatch">
      insert into bms_fixedfee(id,expenses_code,rule_id,client_id,carrier_code,settle_type,item_id,start_date,end_date,frequency,amount,bill_id,bill_code,remark,oper_by,oper_code,oper_time,del_flag)
      values
      <foreach collection="entities" item="entity" separator=",">
          (#{entity.id},#{entity.expensesCode},#{entity.ruleId},#{entity.clientId},#{entity.carrierCode},#{entity.settleType},#{entity.itemId},#{entity.startDate},#{entity.endDate},#{entity.frequency},#{entity.amount},#{entity.billId},#{entity.billCode},#{entity.remark},#{entity.operBy},#{entity.operCode},#{entity.operTime},#{entity.delFlag})
      </foreach>
    </insert>

  <select id="selectByParam2" parameterType="com.bbyb.joy.bms.domain.dto.querybean.BmsFixedExpenseQueryBean" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsFixedExpenseDto">
      SELECT
          tb1.id,
          tb1.expenses_code as expensesCode,
          tb1.settle_type as settleType,
          tb2.client_name as clientName,
          tb1.frequency as frequency,
          tb1.client_id as clientId,
          tb1.item_id as itemId,
          '' as itemName,
          tb1.amount as amount,
          tb1.start_date as startDate,
          tb1.end_date as endDate,
          tb1.oper_time as operTime,
          tb1.remark,
          tb1.bill_id as billId,
          tb1.bill_code as billCode,
          IF( tb1.bill_id IS NULL, '未生成', '已生成' ) AS billStatus,
          tb1.adjust_before_amount as adjustBeforeAmount,
          tb1.adjust_before_remark as adjustBeforeRemark,
          DATE_FORMAT(tb1.start_date,'%Y-%m') as billDate
      FROM bms_fixedfee tb1
      LEFT JOIN bms_clientinfo tb2 ON tb1.client_id = tb2.id
      <where>
          tb1.del_flag = 0
          <if test="billId != null">
              and tb1.bill_id = #{billId}
          </if>
          <if test="billDate != null and billDate != ''">
              and date_format(tb1.end_date,'%Y-%m') = #{billDate}
          </if>
          <if test="settleType != null and settleType != ''">
              and tb1.settle_type = #{settleType}
          </if>
          <if test="billIds != null and billIds.size()>0">
              and tb1.bill_id in
              <foreach collection="billIds" item="id" open="(" separator="," close=")">
                  #{id}
              </foreach>
          </if>
      </where>
      order by tb1.id desc
    </select>
</mapper>