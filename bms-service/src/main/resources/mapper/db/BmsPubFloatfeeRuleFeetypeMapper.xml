<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="mapper.db.BmsPubFloatfeeRuleFeetypeMapper">
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsPubFloatfeeRuleFeetype" id="BmsPubFloatfeeRuleFeetypeMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="VARCHAR"/>
        <result property="subjectId" column="subject_id" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdName" column="created_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="DATE"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedName" column="updated_name" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="DATE"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.bbyb.joy.bms.domain.dto.dto.BmsPubFloatfeeRuleFeetypeDto" id="BmsPubFloatfeeRuleFeetypeDtoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="VARCHAR"/>
        <result property="subjectId" column="subject_id" jdbcType="VARCHAR"/>
        <result property="subjectName" column="subject_name" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdName" column="created_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="DATE"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedName" column="updated_name" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="DATE"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultMap="BmsPubFloatfeeRuleFeetypeMap">
        select
            id,rule_id,subject_id,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark
        from pub_floatfee_rule_feetype
        where id = #{id}
    </select>
    <select id="queryByRuleId" resultMap="BmsPubFloatfeeRuleFeetypeMap">
        select
            id,rule_id,subject_id,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark
        from pub_floatfee_rule_feetype
        where rule_id = #{ruleId}
    </select>
    <select id="queryDtoByRuleId" resultMap="BmsPubFloatfeeRuleFeetypeDtoMap">
        SELECT
            t1.id,
            t1.rule_id,
            t1.subject_id,
            t1.created_by,
            t1.created_name,
            t1.created_time,
            t1.updated_by,
            t1.updated_name,
            t1.updated_time,
            t1.remark
        FROM pub_floatfee_rule_feetype t1
        WHERE t1.rule_id = #{ruleId}
        ORDER BY t1.updated_time
    </select>


    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into pub_floatfee_rule_feetype(id,rule_id,subject_id,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark)
        values (#{id},#{ruleId},#{subjectId},#{createdBy},#{createdName},#{createdTime},#{updatedBy},#{updatedName},#{updatedTime},#{remark})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into pub_floatfee_rule_feetype(id,rule_id,subject_id,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.ruleId},#{entity.subjectId},#{entity.createdBy},#{entity.createdName},#{entity.createdTime},#{entity.updatedBy},#{entity.updatedName},#{entity.updatedTime},#{entity.remark})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into pub_floatfee_rule_feetype(id,rule_id,subject_id,created_by,created_name,created_time,updated_by,updated_name,updated_time,remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.ruleId},#{entity.subjectId},#{entity.createdBy},#{entity.createdName},#{entity.createdTime},#{entity.updatedBy},#{entity.updatedName},#{entity.updatedTime},#{entity.remark})
        </foreach>
        on duplicate key update
        id=values(id),
        rule_id=values(rule_id),
        subject_id=values(subject_id),
        created_by=values(created_by),
        created_name=values(created_name),
        created_time=values(created_time),
        updated_by=values(updated_by),
        updated_name=values(updated_name),
        updated_time=values(updated_time),
        remark=values(remark)
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update pub_floatfee_rule_feetype
        <set>
            <if test="id != null and id != ''">
                id = #{id},
            </if>
            <if test="ruleId != null and ruleId != ''">
                rule_id = #{ruleId},
            </if>
            <if test="subjectId != null and subjectId != ''">
                subject_id = #{subjectId},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdName != null and createdName != ''">
                created_name = #{createdName},
            </if>
            <if test="createdTime != null and createdTime != ''">
                created_time = #{createdTime},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedName != null and updatedName != ''">
                updated_name = #{updatedName},
            </if>
            <if test="updatedTime != null and updatedTime != ''">
                updated_time = #{updatedTime},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键作废-->
    <delete id="deleteById">
        delete from pub_floatfee_rule_feetype where id = #{id}
    </delete>

    <!--通过规则id作废-->
    <delete id="deleteByRuleIds">
        delete from pub_floatfee_rule_feetype
       where rule_id = #{ruleId}
    </delete>


</mapper>