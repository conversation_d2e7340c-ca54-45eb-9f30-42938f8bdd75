<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsYspurchaseFeeinfoMapper">

    <resultMap type="com.bbyb.joy.bms.domain.dto.settlement.BmsYspurchaseFeeinfo" id="BmsYspurchaseFeeinfoResult">
        <result property="id" column="id"/>
        <result property="ysbillId" column="ysbill_id"/>
        <result property="ysFeecode" column="ys_feecode"/>
        <result property="registerTime" column="register_time"/>
        <result property="registerrCompanyId" column="registerr_company_id"/>
        <result property="registerrCompanyname" column="registerr_companyname"/>
        <result property="yfFeecode" column="yf_feecode"/>
        <result property="skuName" column="sku_name"/>
        <result property="num" column="num"/>
        <result property="price" column="price"/>
        <result property="unit" column="unit"/>
        <result property="companyId" column="company_id"/>
        <result property="spec" column="spec"/>
        <result property="relationCode" column="client_code"/>
        <result property="relationName" column="client_name"/>
        <result property="totalAmount" column="ys_total_amount"/>
        <result property="remark" column="remark"/>
        <result property="createCode" column="create_code"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="createDeptId" column="create_dept_id"/>
        <result property="operDeptId" column="oper_dept_id"/>
        <result property="operCode" column="oper_code"/>
        <result property="operBy" column="oper_by"/>
        <result property="operTime" column="oper_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="billDate" column="bill_date"/>
        <result property="ysbillcode" column="ysbill_code"/>
        <result property="yfbillcode" column="yfbill_code"/>
        <result property="billMarking" column="bill_marking"/>
        <result property="hxState" column="hx_state"/>
        <result property="feeFlag" column="fee_flag"/>
        <result property="yfTotalAmount" column="yf_total_amount"/>
        <result property="creatFlag" column="creat_flag"/>
        <result property="suppleCode" column="carrier_code"/>
        <result property="suppleName" column="carrier_name"/>
        <result property="yfBillData" column="yfbill_data" />
        <result property="purchaseAmount"    column="purchase_amount"    />
        <result property="serviceAmount"    column="service_amount"    />
        <result property="adjustRemark" column="adjust_remark" />
    </resultMap>

    <sql id="selectBmsYspurchaseFeeinfoVo">
        SELECT
            f.id,
            f.ys_feecode,
            f.register_time,
            f.registerr_company_id,
            f.registerr_companyname,
            f.yf_feecode,
            f.sku_name,
            f.num,
            f.price,
            f.unit,
            f.company_id,
            f.spec,
            f.relation_code,
            IFNULL(f.total_amount,0) as total_amount,
            f.remark,
            f.create_code,
            f.create_by,
            f.create_time,
            f.create_dept_id,
            f.oper_dept_id,
            f.oper_code,
            f.oper_by,
            f.oper_time,
            f.del_flag ,
            IFNULL(f.bill_date,b.bill_date) as bill_date,
            f.fee_flag,
            b.bill_code as ysbill_code,
            b.bill_marking,
            b.hx_state,
            yf.yf_feecode,
			f.total_amount as ys_total_amount,
			yf.total_amount as yf_total_amount,
            f.creat_flag,
            mc.carrier_name , -- 承运商名称
            mc.carrier_code , -- 承运商编码
            client.client_name,
            client.client_code,
            yfb.bill_code as yfbill_code,
            yf.bill_date as yfbill_data, -- 应付费用账期
            f.purchase_amount,
            f.service_amount,
            f.adjust_remark
        FROM
            bms_yspurchase_feeinfo f
            LEFT JOIN bms_ysbillmain b ON b.id=f.ysbill_id
            LEFT JOIN bms_yfpurchase_feeinfo yf ON  yf.yf_feecode= f.yf_feecode
            LEFT JOIN bms_yfbillmain yfb ON yfb.id=yf.bill_id
            LEFT JOIN bms_carrierinfo mc ON mc.carrier_code=yf.relation_code
            LEFT JOIN bms_clientinfo client ON client.client_code=f.relation_code
    </sql>

    <select id="selectBmsYspurchaseFeeinfoList" parameterType="com.bbyb.joy.bms.domain.dto.settlement.BmsYspurchaseFeeinfo"
            resultMap="BmsYspurchaseFeeinfoResult">
        <include refid="selectBmsYspurchaseFeeinfoVo"/>
        <where>
            and f.del_flag=0 and (yf.del_flag=0 or yf.del_flag is null)
            <if test="ysFeecode != null  and ysFeecode != ''">and f.ys_feecode = #{ysFeecode}</if>
            <if test="params.beginFoundTime != null and params.beginFoundTime != '' and params.endFoundTime != null and params.endFoundTime != ''">
                and f.register_time between #{params.beginFoundTime} and #{params.endFoundTime}
            </if>
            <if test="CompanyIds != null and CompanyIds.length>0 ">
                and f.registerr_company_id in
                <foreach collection="CompanyIds" item="value" separator="," open="(" close=")">
                    #{value}
                </foreach>
            </if>
            <if test="clientList != null">
                and  client.client_code in
                <foreach collection="clientList" item="client" open="(" separator="," close=")">
                    #{client}
                </foreach>
            </if>
            <if test="registerrCompanyname != null  and registerrCompanyname != ''">and f.registerr_companyname like
                concat('%', #{registerrCompanyname}, '%')
            </if>
            <if test="ysFeecode != null  and ysFeecode != ''">and f.ys_feecode like concat('%',#{ysFeecode},'%') </if>
            <if test="skuName != null  and skuName != ''">and f.sku_name like concat('%', #{skuName}, '%')</if>
            <if test="num != null ">and f.num = #{num}</if>
            <if test="price != null ">and f.price = #{price}</if>
            <if test="unit != null  and unit != ''">and f.unit = #{unit}</if>
            <if test="companyId != null ">and f.company_id = #{companyId}</if>
            <if test="spec != null  and spec != ''">and f.spec = #{spec}</if>
            <if test="relationCode != null  and relationCode != ''">and client.client_code =  like concat('%',#{relationCode},'%')</if>
            <if test="relationName != null  and relationName != ''">and client.client_name like concat('%',
                #{relationName}, '%')
            </if>
            <if test="totalAmount != null ">and f.total_amount = #{totalAmount}</if>
            <if test="createCode != null  and createCode != ''">and f.create_code = #{createCode}</if>
            <if test="createDeptId != null ">and f.create_dept_id = #{createDeptId}</if>
            <if test="operDeptId != null ">and f.oper_dept_id = #{operDeptId}</if>
            <if test="operCode != null  and operCode != ''">and f.oper_code = #{operCode}</if>
            <if test="operBy != null  and operBy != ''">and f.oper_by = #{operBy}</if>
            <if test="operTime != null ">and f.oper_time = #{operTime}</if>
            <if test="billMarking!=null">and b.bill_marking=#{billMarking}</if>
            <if test="remark!=null and remark!='' ">and f.remark like concat('%',#{remark},'%')</if>
            <if test="feeFlag!=null ">and f.fee_flag=#{feeFlag}</if>
            <if test="creatFlag!=null">and f.creat_flag=#{creatFlag}</if>
            <if test="billDate!=null and billDate!=''">
                and f.bill_date=#{billDate}
            </if>
            <if test="suppleName!=null and suppleName!='' ">
                and mc.carrier_name like concat('%',#{suppleName},'%')
            </if>
        </where>
        order by f.id desc
    </select>

    <select id="selectBmsYspurchaseFeeinfoById" parameterType="java.lang.String" resultMap="BmsYspurchaseFeeinfoResult">
        <include refid="selectBmsYspurchaseFeeinfoVo"/>
        where f.id = #{id} and IFNULL(f.del_flag,0)=0 and IFNULL(yf.del_flag,0)=0
    </select>

    <insert id="insertBmsYspurchaseFeeinfo" parameterType="com.bbyb.joy.bms.domain.dto.settlement.BmsYspurchaseFeeinfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into bms_yspurchase_feeinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ysFeecode != null">ys_feecode,</if>
            <if test="feeFlag != null">fee_flag,</if>
            <if test="registerTime != null">register_time,</if>
            <if test="registerrCompanyId != null">registerr_company_id,</if>
            <if test="registerrCompanyname != null">registerr_companyname,</if>
            <if test="yfFeecode != null">yf_feecode,</if>
            <if test="skuName != null">sku_name,</if>
            <if test="num != null">num,</if>
            <if test="price != null">price,</if>
            <if test="unit != null">unit,</if>
            <if test="companyId != null">company_id,</if>
            <if test="spec != null">spec,</if>
            <if test="relationCode != null">relation_code,</if>
            <if test="relationName != null">relation_name,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="remark != null">remark,</if>
            <if test="adjustRemark != null">adjust_remark,</if>
            <if test="createCode != null">create_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="operDeptId != null">oper_dept_id,</if>
            <if test="operCode != null">oper_code,</if>
            <if test="operBy != null">oper_by,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="ysbillId!=null">ysbill_id,</if>
            <if test="billDate!=null and billDate!=''">bill_date,</if>
            <if test="purchaseAmount != null">purchase_amount,</if>
            <if test="serviceAmount != null">service_amount,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ysFeecode != null">#{ysFeecode},</if>
            <if test="feeFlag != null">#{feeFlag},</if>
            <if test="registerTime != null">#{registerTime},</if>
            <if test="registerrCompanyId != null">#{registerrCompanyId},</if>
            <if test="registerrCompanyname != null">#{registerrCompanyname},</if>
            <if test="yfFeecode != null">#{yfFeecode},</if>
            <if test="skuName != null">#{skuName},</if>
            <if test="num != null">#{num},</if>
            <if test="price != null">#{price},</if>
            <if test="unit != null">#{unit},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="spec != null">#{spec},</if>
            <if test="relationCode != null">#{relationCode},</if>
            <if test="relationName != null">#{relationName},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="adjustRemark != null">#{adjustRemark},</if>
            <if test="createCode != null">#{createCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="operDeptId != null">#{operDeptId},</if>
            <if test="operCode != null">#{operCode},</if>
            <if test="operBy != null">#{operBy},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="ysbillId!=null">#{ysbillId},</if>
            <if test="billDate!=null and billDate!=''">#{billDate},</if>
            <if test="purchaseAmount != null">#{purchaseAmount},</if>
            <if test="serviceAmount != null">#{serviceAmount},</if>
        </trim>
    </insert>

    <update id="updateBmsYspurchaseFeeinfo" parameterType="com.bbyb.joy.bms.domain.dto.settlement.BmsYspurchaseFeeinfo">
        update bms_yspurchase_feeinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="ysFeecode != null">ys_feecode = #{ysFeecode},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
            <if test="registerrCompanyId != null">registerr_company_id = #{registerrCompanyId},</if>
            <if test="registerrCompanyname != null">registerr_companyname = #{registerrCompanyname},</if>
            <if test="yfFeecode != null">yf_feecode = #{yfFeecode},</if>
            <if test="skuName != null">sku_name = #{skuName},</if>
            <if test="num != null">num = #{num},</if>
            <if test="price != null">price = #{price},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="spec != null">spec = #{spec},</if>
            <if test="relationCode != null">relation_code = #{relationCode},</if>
            <if test="relationName != null">relation_name = #{relationName},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="adjustRemark != null">adjust_remark = #{adjustRemark},</if>
            <if test="createCode != null">create_code = #{createCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="operDeptId != null">oper_dept_id = #{operDeptId},</if>
            <if test="operCode != null">oper_code = #{operCode},</if>
            <if test="operBy != null">oper_by = #{operBy},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="ysbillId!=null">ysbill_id = #{ysbillId},</if>
            <if test="purchaseAmount != null">purchase_amount = #{purchaseAmount},</if>
            <if test="serviceAmount != null">service_amount = #{serviceAmount},</if>
        </trim>
        where id = #{id} and del_flag=0
    </update>

    <delete id="deleteBmsYspurchaseFeeinfoById" parameterType="java.lang.String">
        update  bms_yspurchase_feeinfo set del_flag=1 where id = #{id}
    </delete>

    <delete id="deleteBmsYspurchaseFeeinfoByIds">
        update bms_yspurchase_feeinfo set del_flag=1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBmsYspurchaseFeeinfoStatusByIds">
        update bms_yspurchase_feeinfo set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateBmsYspurchaseFeeinfoStatusByCode">
        update bms_yspurchase_feeinfo set del_flag = #{status} where ys_feecode =#{ysFeecode}
    </update>

<!--    客户未生成账单或者未核销-->
    <select id="checkCancelCount" resultMap="BmsYspurchaseFeeinfoResult">
        select
            b.bill_code AS ysbill_code,
            ys.relation_code as client_code,
            b.del_flag  from bms_yspurchase_feeinfo AS ys
        left join bms_ysbillmain AS b ON ys.ysbill_id=b.id and b.del_flag=0
        <where>
            <if test="array!=null and array.length!=0">
                and ys.relation_code in
                <foreach collection="array" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            and b.id is null -- 未生成账单
        </where>

        union all
        select
            b.bill_code AS ysbill_code,
            ys.relation_code as client_code,
            b.del_flag
        from bms_yspurchase_feeinfo AS ys
        left join bms_ysbillmain AS b ON ys.ysbill_id=b.id and b.del_flag=0
        <where>
            <if test="array!=null and array.length!=0">
                and ys.relation_code in
                <foreach collection="array" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            and ys.del_flag=0
            and b.bill_type=1 -- 金融代采单
            and b.hx_state in(1,3)  -- 未核销
        </where>

    </select>
</mapper>