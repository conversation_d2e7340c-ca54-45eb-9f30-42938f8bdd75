<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsApplyRemarkDictMapper">
    <resultMap type="com.bbyb.joy.bms.domain.dto.BmsApplyRemarkDict" id="BmsApplyRemarkDictMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="businessType" column="business_type" jdbcType="VARCHAR"/>
        <result property="ruleType" column="rule_type" jdbcType="INTEGER"/>
        <result property="relationId" column="relation_id" jdbcType="VARCHAR"/>
        <result property="clientId" column="client_id" jdbcType="VARCHAR"/>
        <result property="carrierId" column="carrier_id" jdbcType="VARCHAR"/>
        <result property="feeType" column="fee_type" jdbcType="VARCHAR"/>
        <result property="area" column="area" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="automaticRemark" column="automatic_remark" jdbcType="TINYINT"/>
        <result property="createCode" column="create_code" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="operCode" column="oper_code" jdbcType="VARCHAR"/>
        <result property="operBy" column="oper_by" jdbcType="VARCHAR"/>
        <result property="operTime" column="oper_time" jdbcType="DATE"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

    <insert id="insertBatch">
        insert into bms_apply_remark_dict(id,business_type,rule_type,relation_id,client_id,carrier_id,fee_type,area,remark,automatic_remark,create_code,create_by,create_time,oper_code,oper_by,oper_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.businessType},#{entity.ruleType},#{entity.relationId},#{entity.clientId},#{entity.carrierId},#{entity.feeType},#{entity.area},#{entity.remark},#{entity.automaticRemark},#{entity.createCode},#{entity.createBy},#{entity.createTime},#{entity.operCode},#{entity.operBy},#{entity.operTime})
        </foreach>
    </insert>

    <update id="updateBatch">
        <foreach collection="entities" item="item" index="index" separator=";">
            update bms_apply_remark_dict
            <set>
                <if test="item.businessType != null">
                    business_type = #{item.businessType},
                </if>
                <if test="item.ruleType != null">
                    rule_type = #{item.ruleType},
                </if>
                <if test="item.relationId != null">
                    relation_id = #{item.relationId},
                </if>
                <if test="item.clientId != null">
                    client_id = #{item.clientId},
                </if>
                <if test="item.carrierId != null">
                    carrier_id = #{item.carrierId},
                </if>
                <if test="item.feeType != null">
                    fee_type = #{item.feeType},
                </if>
                <if test="item.area != null">
                    area = #{item.area},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark},
                </if>
                <if test="item.automaticRemark != null">
                    automatic_remark = #{item.automaticRemark},
                </if>
                <if test="item.operCode != null">
                    oper_code = #{item.operCode},
                </if>
                <if test="item.operBy != null">
                    oper_by = #{item.operBy},
                </if>
                <if test="item.operTime != null">
                    oper_time = #{item.operTime},
                </if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <update id="deleteBatch">
        UPDATE bms_apply_remark_dict
            SET del_flag=1
        WHERE del_flag = 0
        AND id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selByLimit" resultMap="BmsApplyRemarkDictMap">
        select
        id,business_type,rule_type,relation_id,client_id,carrier_id,fee_type,area,remark,automatic_remark,create_code,create_by,create_time,oper_code,oper_by,oper_time,del_flag
        from bms_apply_remark_dict
        <where>
            del_flag=0
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="businessType != null and businessType != ''">
                and business_type = #{businessType}
            </if>
            <if test="ruleType != null and ruleType != ''">
                and rule_type = #{ruleType}
            </if>
            <if test="relationId != null and relationId != ''">
                and relation_id = #{relationId}
            </if>
            <if test="clientId != null and clientId != ''">
                and client_id = #{clientId}
            </if>
            <if test="carrierId != null and carrierId != ''">
                and carrier_id = #{carrierId}
            </if>
            <if test="feeType != null and feeType != ''">
                and fee_type = #{feeType}
            </if>
            <if test="area != null and area != ''">
                and area = #{area}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="createCode != null and createCode != ''">
                and create_code = #{createCode}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="operCode != null and operCode != ''">
                and oper_code = #{operCode}
            </if>
            <if test="operBy != null and operBy != ''">
                and oper_by = #{operBy}
            </if>
            <if test="operTime != null and operTime != ''">
                and oper_time = #{operTime}
            </if>
        </where>
    </select>

    <update id="deleteBatchByRel">
        UPDATE bms_apply_remark_dict
        SET del_flag=1
        WHERE del_flag = 0
        AND relation_id = #{relationId}
        AND rule_type = #{ruleType}
    </update>




</mapper>