<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.PubQuotationClassificationdetailMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.PubQuotationClassificationdetail" id="PubQuotationClassificationdetailResult">
        <result property="id"    column="id"    />
        <result property="quoteruledetailId"    column="quoteruledetail_id"    />
        <result property="storeCode"    column="store_code"    />
        <result property="storeName"    column="store_name"    />
        <result property="deliverProvince"    column="deliver_province"    />
        <result property="deliverArea"    column="deliver_area"    />
        <result property="deliverCity"    column="deliver_city"    />
        <result property="receiveProvince"    column="receive_province"    />
        <result property="receiveArea"    column="receive_area"    />
        <result property="receiveCity"    column="receive_city"    />
        <result property="carmodel"    column="carmodel"    />
        <result property="carlong"    column="carlong"    />
        <result property="routeCode"    column="route_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="billType"    column="bill_type"    />
        <result property="temperatureZone"    column="temperature_zone"    />
        <result property="specifications"    column="specifications"    />
        <result property="category"    column="category"    />
        <result property="isRemovezero"    column="is_removezero"    />
        <result property="businessType"    column="business_type"    />
        <result property="minimumCharge"    column="minimum_charge"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="step"    column="step"    />
        <result property="singularLadder1"    column="singular_ladder1"    />
        <result property="singularLadder2"    column="singular_ladder2"    />
        <result property="singularLadder3"    column="singular_ladder3"    />
        <result property="singularLadder4"    column="singular_ladder4"    />
        <result property="singularLadder5"    column="singular_ladder5"    />
        <result property="singularLadder6"    column="singular_ladder6"    />
        <result property="boxLadder1"    column="box_ladder1"    />
        <result property="boxLadder2"    column="box_ladder2"    />
        <result property="boxLadder3"    column="box_ladder3"    />
        <result property="boxLadder4"    column="box_ladder4"    />
        <result property="boxLadder5"    column="box_ladder5"    />
        <result property="boxLadder6"    column="box_ladder6"    />
        <result property="weightLadder1"    column="weight_ladder1"    />
        <result property="weightLadder2"    column="weight_ladder2"    />
        <result property="weightLadder3"    column="weight_ladder3"    />
        <result property="weightLadder4"    column="weight_ladder4"    />
        <result property="weightLadder5"    column="weight_ladder5"    />
        <result property="weightLadder6"    column="weight_ladder6"    />
        <result property="volumeLadder1"    column="volume_ladder1"    />
        <result property="volumeLadder2"    column="volume_ladder2"    />
        <result property="volumeLadder3"    column="volume_ladder3"    />
        <result property="volumeLadder4"    column="volume_ladder4"    />
        <result property="volumeLadder5"    column="volume_ladder5"    />
        <result property="volumeLadder6"    column="volume_ladder6"    />
        <result property="kilometreLadder1"    column="kilometre_ladder1"    />
        <result property="kilometreLadder2"    column="kilometre_ladder2"    />
        <result property="kilometreLadder3"    column="kilometre_ladder3"    />
        <result property="kilometreLadder4"    column="kilometre_ladder4"    />
        <result property="kilometreLadder5"    column="kilometre_ladder5"    />
        <result property="kilometreLadder6"    column="kilometre_ladder6"    />
        <result property="ladderPrice1"    column="ladder_price1"    />
        <result property="ladderPrice2"    column="ladder_price2"    />
        <result property="ladderPrice3"    column="ladder_price3"    />
        <result property="ladderPrice4"    column="ladder_price4"    />
        <result property="ladderPrice5"    column="ladder_price5"    />
        <result property="ladderPrice6"    column="ladder_price6"    />
        <result property="percentage"    column="percentage"    />
        <result property="tornumLadder1"    column="tornum_ladder1"    />
        <result property="tornumLadder2"    column="tornum_ladder2"    />
        <result property="tornumLadder3"    column="tornum_ladder3"    />
        <result property="tornumLadder4"    column="tornum_ladder4"    />
        <result property="tornumLadder5"    column="tornum_ladder5"    />
        <result property="tornumLadder6"    column="tornum_ladder6"    />
        <result property="numberLadder1"    column="number_ladder1"    />
        <result property="numberLadder2"    column="number_ladder2"    />
        <result property="numberLadder3"    column="number_ladder3"    />
        <result property="numberLadder4"    column="number_ladder4"    />
        <result property="numberLadder5"    column="number_ladder5"    />
        <result property="numberLadder6"    column="number_ladder6"    />
        <result property="baseQuantity"    column="base_quantity"    />
        <result property="category1"    column="category1"    />
        <result property="category2"    column="category2"    />
        <result property="category3"    column="category3"    />
        <result property="category4"    column="category4"    />
        <result property="category5"    column="category5"    />
        <result property="category6"    column="category6"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemId1"    column="item_id1"    />
    </resultMap>

    <sql id="selectPubQuotationClassificationdetailVo">
        select id, quoteruledetail_id, store_code, store_name, IFNULL(deliver_province,'') deliver_province,
               IFNULL(deliver_area,'') deliver_area, IFNULL(deliver_city,'') deliver_city,
               IFNULL(receive_province,'') receive_province, IFNULL(receive_area,'') receive_area,
               IFNULL(receive_city,'') receive_city, carmodel, carlong, IFNULL(route_code,'') route_code, warehouse_name,
               bill_type, temperature_zone, specifications, category, is_removezero, business_type,
               minimum_charge, unit_price, step, singular_ladder1, singular_ladder2, singular_ladder3,
               singular_ladder4, singular_ladder5, singular_ladder6, box_ladder1, box_ladder2, box_ladder3,
               box_ladder4, box_ladder5, box_ladder6, weight_ladder1, weight_ladder2, weight_ladder3,
               weight_ladder4, weight_ladder5, weight_ladder6, volume_ladder1, volume_ladder2, volume_ladder3,
               volume_ladder4, volume_ladder5, volume_ladder6, kilometre_ladder1, kilometre_ladder2,
               kilometre_ladder3, kilometre_ladder4, kilometre_ladder5, kilometre_ladder6, ladder_price1,
               ladder_price2, ladder_price3, ladder_price4, ladder_price5, ladder_price6, percentage,
               tornum_ladder1, tornum_ladder2, tornum_ladder3, tornum_ladder4, tornum_ladder5, tornum_ladder6,
               number_ladder1, number_ladder2, number_ladder3, number_ladder4, number_ladder5, number_ladder6,
               base_quantity,
               category1, category2, category3, category4, category5, category6,item_id,item_id1
        from pub_quotation_classificationdetail
    </sql>


    
    <select id="selectPubQuotationClassificationdetailByquoteruledetailId" parameterType="String" resultMap="PubQuotationClassificationdetailResult">
        <include refid="selectPubQuotationClassificationdetailVo"/>
        where quoteruledetail_id = #{quoteruledetailId}
    </select>



    <select id="getPubClass" parameterType="com.bbyb.joy.bms.domain.dto.PubQuoteruleDetail" resultMap="PubQuotationClassificationdetailResult">
        <include refid="selectPubQuotationClassificationdetailVo"/>
        where 1=1
        <if test="deliverArea != null and deliverArea != '' "> and deliver_city like concat('%',#{deliverArea},'%')</if>
        <if test="receiveArea != null and receiveArea != '' "> and receive_city like concat('%',#{receiveArea},'%')</if>
        <if test="clientCode != null  and clientCode != ''"> and (number_ladder6 = #{clientCode} or category1 = #{clientCode} )</if>
        <if test="routeCode != null  and routeCode != ''"> and  route_code = #{routeCode}  </if>
        <if test="warehouseName != null  and warehouseName != ''"> and  warehouse_name = #{warehouseName}  </if>
    </select>


    <select id="selectPubQuotationClassificationdetailByquoteruledetailIds"  resultMap="PubQuotationClassificationdetailResult">
        select id, quoteruledetail_id,bill_type,is_removezero,business_type,
        trim(IFNULL(store_code,'')) store_code, trim(IFNULL(store_name,'')) store_name,
        case when IFNULL(deliver_province,'')='' and IFNULL(deliver_area,'')='' and IFNULL(deliver_city,'')='' then '全国' else trim(IFNULL(deliver_province,'')) end deliver_province,
        trim(IFNULL(deliver_area,'')) deliver_area, trim(IFNULL(deliver_city,'')) deliver_city,
        case when IFNULL(receive_province,'')='' and IFNULL(receive_area,'')='' and IFNULL(receive_city,'')='' then '全国' else trim(IFNULL(receive_province,'')) end receive_province,
        trim(IFNULL(receive_area,'')) receive_area,trim(IFNULL(receive_city,'')) receive_city,
        trim(IFNULL(carmodel,'')) carmodel, trim(IFNULL(carlong,'')) carlong,
        trim(IFNULL(route_code,'')) route_code,
        trim(IFNULL(warehouse_name,'')) warehouse_name,
        trim(IFNULL(temperature_zone,'')) temperature_zone,
        trim(IFNULL(specifications,'')) specifications,
        trim(IFNULL(category,'')) category,
        IFNULL(minimum_charge,0) minimum_charge,
        IFNULL(unit_price,0) unit_price,
        IFNULL(step,0) step,
        IFNULL(singular_ladder1,0) singular_ladder1,
        IFNULL(singular_ladder2,0) singular_ladder2,
        IFNULL(singular_ladder3,0) singular_ladder3,
        IFNULL(singular_ladder4,0) singular_ladder4,
        IFNULL(singular_ladder5,0) singular_ladder5,
        IFNULL(singular_ladder6,0) singular_ladder6,
        IFNULL(box_ladder1,0) box_ladder1,
        IFNULL(box_ladder2,0) box_ladder2,
        IFNULL(box_ladder3,0) box_ladder3,
        IFNULL(box_ladder4,0) box_ladder4,
        IFNULL(box_ladder5,0) box_ladder5,
        IFNULL(box_ladder6,0) box_ladder6,
        IFNULL(weight_ladder1,0) weight_ladder1,
        IFNULL(weight_ladder2,0) weight_ladder2,
        IFNULL(weight_ladder3,0) weight_ladder3,
        IFNULL(weight_ladder4,0) weight_ladder4,
        IFNULL(weight_ladder5,0) weight_ladder5,
        IFNULL(weight_ladder6,0) weight_ladder6,
        IFNULL(volume_ladder1,0) volume_ladder1,
        IFNULL(volume_ladder2,0) volume_ladder2,
        IFNULL(volume_ladder3,0) volume_ladder3,
        IFNULL(volume_ladder4,0) volume_ladder4,
        IFNULL(volume_ladder5,0) volume_ladder5,
        IFNULL(volume_ladder6,0) volume_ladder6,
        IFNULL(kilometre_ladder1,0) kilometre_ladder1,
        IFNULL(kilometre_ladder2,0) kilometre_ladder2,
        IFNULL(kilometre_ladder3,0) kilometre_ladder3,
        IFNULL(kilometre_ladder4,0) kilometre_ladder4,
        IFNULL(kilometre_ladder5,0) kilometre_ladder5,
        IFNULL(kilometre_ladder6,0) kilometre_ladder6,
        IFNULL(ladder_price1,0) ladder_price1,
        IFNULL(ladder_price2,0) ladder_price2,
        IFNULL(ladder_price3,0) ladder_price3,
        IFNULL(ladder_price4,0) ladder_price4,
        IFNULL(ladder_price5,0) ladder_price5,
        IFNULL(ladder_price6,0) ladder_price6,
        IFNULL(percentage,0) percentage,
        IFNULL(tornum_ladder1,0) tornum_ladder1,
        IFNULL(tornum_ladder2,0) tornum_ladder2,
        IFNULL(tornum_ladder3,0) tornum_ladder3,
        IFNULL(tornum_ladder4,0) tornum_ladder4,
        IFNULL(tornum_ladder5,0) tornum_ladder5,
        IFNULL(tornum_ladder6,0) tornum_ladder6,
        IFNULL(base_quantity,0) base_quantity,
        trim(IFNULL(number_ladder1,'')) number_ladder1,
        trim(IFNULL(number_ladder2,'')) number_ladder2,
        trim(IFNULL(number_ladder3,'')) number_ladder3,
        trim(IFNULL(number_ladder4,'')) number_ladder4,
        trim(IFNULL(number_ladder5,'')) number_ladder5,
        trim(IFNULL(number_ladder6,'')) number_ladder6,
        trim(IFNULL(category1,'')) category1,
        trim(IFNULL(category2,'')) category2,
        trim(IFNULL(category3,'')) category3,
        trim(IFNULL(category4,'')) category4,
        trim(IFNULL(category5,'')) category5,
        trim(IFNULL(category6,'')) category6,
        IFNULL(item_id,0) item_id,
        IFNULL(cost_unit,0) costUnit
        from pub_quotation_classificationdetail
        where quoteruledetail_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
        
    <insert id="insertPubQuotationClassificationdetail" parameterType="java.util.List">
        insert into pub_quotation_classificationdetail
        (
         quoteruledetail_id,
         store_code,
         store_name,
         deliver_province,
         deliver_area,
         deliver_city,
         receive_province,
         receive_area,
         receive_city,
         carmodel,
         carlong,
         route_code,
         warehouse_name,
         bill_type,
         temperature_zone,
         specifications,
         category,
         is_removezero,
         business_type,
         minimum_charge,
         unit_price,
         step,
         singular_ladder1,
         singular_ladder2,
         singular_ladder3,
         singular_ladder4,
         singular_ladder5,
         singular_ladder6,
         box_ladder1,
         box_ladder2,
         box_ladder3,
         box_ladder4,
         box_ladder5,
         box_ladder6,
         weight_ladder1,
         weight_ladder2,
         weight_ladder3,
         weight_ladder4,
         weight_ladder5,
         weight_ladder6,
         volume_ladder1,
         volume_ladder2,
         volume_ladder3,
         volume_ladder4,
         volume_ladder5,
         volume_ladder6,
         kilometre_ladder1,
         kilometre_ladder2,
         kilometre_ladder3,
         kilometre_ladder4,
         kilometre_ladder5,
         kilometre_ladder6,
         ladder_price1,
         ladder_price2,
         ladder_price3,
         ladder_price4,
         ladder_price5,
         ladder_price6,
         percentage,
         tornum_ladder1,
         tornum_ladder2,
         tornum_ladder3,
         tornum_ladder4,
         tornum_ladder5,
         tornum_ladder6,
         number_ladder1,
         number_ladder2,
         number_ladder3,
         number_ladder4,
         number_ladder5,
         number_ladder6,
         base_quantity,
         category1,
         category2,
         category3,
         category4,
         category5,
         category6,
         item_id,
         item_id1,
         cost_unit )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
            #{item.quoteruledetailId},
            #{item.storeCode},
            #{item.storeName},
            #{item.deliverProvince},
            #{item.deliverArea},
            #{item.deliverCity},
            #{item.receiveProvince},
            #{item.receiveArea},
            #{item.receiveCity},
            #{item.carmodel},
            #{item.carlong},
            #{item.routeCode},
            #{item.warehouseName},
            #{item.billType},
            #{item.temperatureZone},
            #{item.specifications},
            #{item.category},
            #{item.isRemovezero},
            #{item.businessType},
            #{item.minimumCharge},
            #{item.unitPrice},
            #{item.step},
            #{item.singularLadder1},
            #{item.singularLadder2},
            #{item.singularLadder3},
            #{item.singularLadder4},
            #{item.singularLadder5},
            #{item.singularLadder6},
            #{item.boxLadder1},
            #{item.boxLadder2},
            #{item.boxLadder3},
            #{item.boxLadder4},
            #{item.boxLadder5},
            #{item.boxLadder6},
            #{item.weightLadder1},
            #{item.weightLadder2},
            #{item.weightLadder3},
            #{item.weightLadder4},
            #{item.weightLadder5},
            #{item.weightLadder6},
            #{item.volumeLadder1},
            #{item.volumeLadder2},
            #{item.volumeLadder3},
            #{item.volumeLadder4},
            #{item.volumeLadder5},
            #{item.volumeLadder6},
            #{item.kilometreLadder1},
            #{item.kilometreLadder2},
            #{item.kilometreLadder3},
            #{item.kilometreLadder4},
            #{item.kilometreLadder5},
            #{item.kilometreLadder6},
            #{item.ladderPrice1},
            #{item.ladderPrice2},
            #{item.ladderPrice3},
            #{item.ladderPrice4},
            #{item.ladderPrice5},
            #{item.ladderPrice6},
            #{item.percentage},
            #{item.tornumLadder1},
            #{item.tornumLadder2},
            #{item.tornumLadder3},
            #{item.tornumLadder4},
            #{item.tornumLadder5},
            #{item.tornumLadder6},
            #{item.numberLadder1},
            #{item.numberLadder2},
            #{item.numberLadder3},
            #{item.numberLadder4},
            #{item.numberLadder5},
            #{item.numberLadder6},
            #{item.baseQuantity},
            #{item.category1},
            #{item.category2},
            #{item.category3},
            #{item.category4},
            #{item.category5},
            #{item.category6},
            #{item.itemId},
            #{item.itemId1},
            #{item.costUnit}
         )
        </foreach>
    </insert>


    <delete id="deletePubQuotationClassificationdetailByquoteruledetailId" parameterType="String">
        delete from pub_quotation_classificationdetail where quoteruledetail_id = #{quoteruledetailId}
    </delete>


    <sql id="selectPubQuotationClassificationdetailVo2">
        SELECT
            t1.id,
            t1.quoteruledetail_id,
            t1.store_code,
            t1.store_name,
            IFNULL( t1.deliver_province, '' ) deliver_province,
            IFNULL( t1.deliver_area, '' ) deliver_area,
            IFNULL( t1.deliver_city, '' ) deliver_city,
            IFNULL( t1.receive_province, '' ) receive_province,
            IFNULL( t1.receive_area, '' ) receive_area,
            IFNULL( t1.receive_city, '' ) receive_city,
            t1.carmodel,
            t1.carlong,
            IFNULL( t1.route_code, '' ) route_code,
            t1.warehouse_name,
            t1.bill_type,
            t1.temperature_zone,
            t1.specifications,
            t1.category,
            t1.is_removezero,
            t1.business_type,
            t1.minimum_charge,
            t1.unit_price,
            t1.step,
            t1.singular_ladder1,
            t1.singular_ladder2,
            t1.singular_ladder3,
            t1.singular_ladder4,
            t1.singular_ladder5,
            t1.singular_ladder6,
            t1.box_ladder1,
            t1.box_ladder2,
            t1.box_ladder3,
            t1.box_ladder4,
            t1.box_ladder5,
            t1.box_ladder6,
            t1.weight_ladder1,
            t1.weight_ladder2,
            t1.weight_ladder3,
            t1.weight_ladder4,
            t1.weight_ladder5,
            t1.weight_ladder6,
            t1.volume_ladder1,
            t1.volume_ladder2,
            t1.volume_ladder3,
            t1.volume_ladder4,
            t1.volume_ladder5,
            t1.volume_ladder6,
            t1.kilometre_ladder1,
            t1.kilometre_ladder2,
            t1.kilometre_ladder3,
            t1.kilometre_ladder4,
            t1.kilometre_ladder5,
            t1.kilometre_ladder6,
            t1.ladder_price1,
            t1.ladder_price2,
            t1.ladder_price3,
            t1.ladder_price4,
            t1.ladder_price5,
            t1.ladder_price6,
            t1.percentage,
            t1.tornum_ladder1,
            t1.tornum_ladder2,
            t1.tornum_ladder3,
            t1.tornum_ladder4,
            t1.tornum_ladder5,
            t1.tornum_ladder6,
            t1.number_ladder1,
            t1.number_ladder2,
            t1.number_ladder3,
            t1.number_ladder4,
            t1.number_ladder5,
            t1.number_ladder6,
            t1.base_quantity,
            t1.category1,
            t1.category2,
            t1.category3,
            t1.category4,
            t1.category5,
            t1.category6,
            t1.item_id,
            '' as itemIdName,
            t1.item_id1,
            '' as itemId1Name,
            t1.cost_unit as costUnit,
            '' as costUnitName
        FROM pub_quotation_classificationdetail t1
    </sql>


    <resultMap type="com.bbyb.joy.bms.domain.dto.PubQuotationClassificationdetail" id="PubQuotationClassificationdetailResult2">
        <result property="id"    column="id"    />
        <result property="quoteruledetailId"    column="quoteruledetail_id"    />
        <result property="storeCode"    column="store_code"    />
        <result property="storeName"    column="store_name"    />
        <result property="deliverProvince"    column="deliver_province"    />
        <result property="deliverArea"    column="deliver_area"    />
        <result property="deliverCity"    column="deliver_city"    />
        <result property="receiveProvince"    column="receive_province"    />
        <result property="receiveArea"    column="receive_area"    />
        <result property="receiveCity"    column="receive_city"    />
        <result property="carmodel"    column="carmodel"    />
        <result property="carlong"    column="carlong"    />
        <result property="routeCode"    column="route_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="billType"    column="bill_type"    />
        <result property="temperatureZone"    column="temperature_zone"    />
        <result property="specifications"    column="specifications"    />
        <result property="category"    column="category"    />
        <result property="isRemovezero"    column="is_removezero"    />
        <result property="businessType"    column="business_type"    />
        <result property="minimumCharge"    column="minimum_charge"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="step"    column="step"    />
        <result property="singularLadder1"    column="singular_ladder1"    />
        <result property="singularLadder2"    column="singular_ladder2"    />
        <result property="singularLadder3"    column="singular_ladder3"    />
        <result property="singularLadder4"    column="singular_ladder4"    />
        <result property="singularLadder5"    column="singular_ladder5"    />
        <result property="singularLadder6"    column="singular_ladder6"    />
        <result property="boxLadder1"    column="box_ladder1"    />
        <result property="boxLadder2"    column="box_ladder2"    />
        <result property="boxLadder3"    column="box_ladder3"    />
        <result property="boxLadder4"    column="box_ladder4"    />
        <result property="boxLadder5"    column="box_ladder5"    />
        <result property="boxLadder6"    column="box_ladder6"    />
        <result property="weightLadder1"    column="weight_ladder1"    />
        <result property="weightLadder2"    column="weight_ladder2"    />
        <result property="weightLadder3"    column="weight_ladder3"    />
        <result property="weightLadder4"    column="weight_ladder4"    />
        <result property="weightLadder5"    column="weight_ladder5"    />
        <result property="weightLadder6"    column="weight_ladder6"    />
        <result property="volumeLadder1"    column="volume_ladder1"    />
        <result property="volumeLadder2"    column="volume_ladder2"    />
        <result property="volumeLadder3"    column="volume_ladder3"    />
        <result property="volumeLadder4"    column="volume_ladder4"    />
        <result property="volumeLadder5"    column="volume_ladder5"    />
        <result property="volumeLadder6"    column="volume_ladder6"    />
        <result property="kilometreLadder1"    column="kilometre_ladder1"    />
        <result property="kilometreLadder2"    column="kilometre_ladder2"    />
        <result property="kilometreLadder3"    column="kilometre_ladder3"    />
        <result property="kilometreLadder4"    column="kilometre_ladder4"    />
        <result property="kilometreLadder5"    column="kilometre_ladder5"    />
        <result property="kilometreLadder6"    column="kilometre_ladder6"    />
        <result property="ladderPrice1"    column="ladder_price1"    />
        <result property="ladderPrice2"    column="ladder_price2"    />
        <result property="ladderPrice3"    column="ladder_price3"    />
        <result property="ladderPrice4"    column="ladder_price4"    />
        <result property="ladderPrice5"    column="ladder_price5"    />
        <result property="ladderPrice6"    column="ladder_price6"    />
        <result property="percentage"    column="percentage"    />
        <result property="tornumLadder1"    column="tornum_ladder1"    />
        <result property="tornumLadder2"    column="tornum_ladder2"    />
        <result property="tornumLadder3"    column="tornum_ladder3"    />
        <result property="tornumLadder4"    column="tornum_ladder4"    />
        <result property="tornumLadder5"    column="tornum_ladder5"    />
        <result property="tornumLadder6"    column="tornum_ladder6"    />
        <result property="numberLadder1"    column="number_ladder1"    />
        <result property="numberLadder2"    column="number_ladder2"    />
        <result property="numberLadder3"    column="number_ladder3"    />
        <result property="numberLadder4"    column="number_ladder4"    />
        <result property="numberLadder5"    column="number_ladder5"    />
        <result property="numberLadder6"    column="number_ladder6"    />
        <result property="baseQuantity"    column="base_quantity"    />
        <result property="category1"    column="category1"    />
        <result property="category2"    column="category2"    />
        <result property="category3"    column="category3"    />
        <result property="category4"    column="category4"    />
        <result property="category5"    column="category5"    />
        <result property="category6"    column="category6"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemIdName"    column="itemIdName"    />
        <result property="itemId1"    column="item_id1"    />
        <result property="itemId1Name"    column="itemId1Name"    />
        <result property="costUnit"    column="costUnit"    />
        <result property="costUnitName"    column="costUnitName"    />
    </resultMap>

    <select id="selectPubQuotationClassificationdetailByquoteruledetailId2" parameterType="String" resultMap="PubQuotationClassificationdetailResult2">
        <include refid="selectPubQuotationClassificationdetailVo2"/>
        where t1.quoteruledetail_id = #{quoteruledetailId}
    </select>
</mapper>