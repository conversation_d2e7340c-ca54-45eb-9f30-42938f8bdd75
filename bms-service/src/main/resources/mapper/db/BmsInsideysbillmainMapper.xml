<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.BmsInsideysbillmainMapper">
    <resultMap id="ListMap" type="com.bbyb.joy.bms.domain.dto.dto.BmsInsideysbillmainListDto">
        <id column="id" property="id" />
        <result column="billname" jdbcType="VARCHAR"  property="billname" />
        <result column="billdate" jdbcType="VARCHAR" property="billdate" />
        <result column="bill_unit" jdbcType="INTEGER"  property="billUnit" />
        <result column="bill_amount" jdbcType="DECIMAL"  property="billAmount" />
        <result column="yshx_username" jdbcType="DECIMAL"  property="yshxUsername" />
<!--        <result column="ys_amount" jdbcType="SMALLINT"  property="ysAmount" />-->
        <result column="receive_unit" jdbcType="SMALLINT"  property="receiveUnit" />
        <result column="pay_unit" jdbcType="SMALLINT"   property="payUnit" />
        <result column="bill_state" jdbcType="SMALLINT"   property="billState" />
        <result column="confirm_state" jdbcType="SMALLINT"   property="confirmState" />
        <result column="confirm_usercode" jdbcType="INTEGER" property="confirmUsercode" />
        <result column="billcode" jdbcType="VARCHAR"  property="billcode"/>
        <result column="remark" jdbcType="VARCHAR"  property="remarkI" />
        <result column="oper_by" jdbcType="VARCHAR" property="operBy" />
        <result column="oper_code" jdbcType="VARCHAR" property="operCode" />
        <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
        <result column="operTimeDt" jdbcType="TIMESTAMP" property="operTimeDt" />
        <result column="confirm_username" jdbcType="VARCHAR" property="confirmUsername" />
        <result column="confirm_time" jdbcType="TIMESTAMP"  property="confirmTime" />
        <result column="confirmTimeDt" jdbcType="TIMESTAMP"  property="confirmTimeDt" />
        <result column="confirm_remark" jdbcType="VARCHAR" property="confirmRemark" />
        <result column="yshx_state" jdbcType="SMALLINT" property="yshxState" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_code" jdbcType="VARCHAR" property="createCode" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="createTimeDt" jdbcType="TIMESTAMP" property="createTimeDt" />
        <result column="write_unit" jdbcType="INTEGER" property="writeUnit" />
        <result column="create_dept_id" jdbcType="INTEGER" property="createDeptId" />
        <result column="oper_dept_id" jdbcType="INTEGER" property="operDeptId" />
<!--        <result column="yshx_amount" jdbcType="INTEGER" property="yshxAmount" />-->
        <result column="yshx_Time" jdbcType="INTEGER" property="yshxTime" />
        <result column="yshxTimeDt" jdbcType="INTEGER" property="yshxTimeDt" />
    </resultMap>

    <resultMap id="insidereceivefeeMap" type="com.bbyb.joy.bms.domain.dto.dto.BmsInsidereceivefeeDto">
        <id column="id" property="id" />
        <result column="billname"   property="billname" />
        <result column="billcode"   property="billcode"/>
        <result column="billdate"   property="billdate"/>
        <result column="bill_amount"   property="billAmount"/>
        <result column="job_id"   property="jobId" />
        <result column="jobcode"   property="jobcode" />
        <result column="work_id"   property="workId" />
        <result column="workcode"   property="workcode" />
        <result column="bill_id"   property="billId" />
        <result column="get_unit"   property="getUnit" />
        <result column="pay_unit"   property="payUnit" />
        <result column="freight"   property="freight" />
        <result column="delivery_fee"  property="deliveryFee" />
        <result column="handling_fee"  property="handlingFee" />
        <result column="pick_fee"  property="pickFee" />
        <result column="ultrafar_fee"  property="ultrafarFee" />
        <result column="add_points_fee"  property="addPointsFee" />
        <result column="reduce_points_fee"  property="reducePointsFee" />
        <result column="hort_haul_fee"  property="hortHaulFee" />
        <result column="excess_basket_fee"  property="excessBasketFee" />
        <result column="other_cost1"  property="otherCost1" />
        <result column="other_cost2"  property="otherCost2" />
        <result column="other_cost3"  property="otherCost3" />
        <result column="other_cost4"  property="otherCost4" />
        <result column="other_cost5"  property="otherCost5" />
        <result column="other_cost6"  property="otherCost6" />
        <result column="other_cost7"  property="otherCost7" />
        <result column="other_cost8"  property="otherCost8" />
        <result column="other_cost9"  property="otherCost9" />
        <result column="other_cost10"  property="otherCost10" />
        <result column="other_cost11"  property="otherCost11" />
        <result column="other_cost12"  property="otherCost12" />
        <result column="total_number"  property="totalNumber" />
        <result column="total_weight"  property="totalWeight" />
        <result column="total_volume"  property="totalVolume" />
        <result column="basic_fees_total"  property="basicFeesTotal" />
        <result column="other_fees_total"  property="otherFeesTotal" />
        <result column="review_status"  property="reviewStatus" />
        <result column="remark"  property="remarkI" />

    </resultMap>
    <sql id="Base_Column_List">
    id,
    billname,
    billdate,
    bill_unit,
    bill_amount,
--     adjusted_amount,
--     ys_amount,
    receive_unit,
    pay_unit ,
    bill_state ,
    confirm_state,
    confirm_usercode,
    billcode,
    remark,
    oper_by,
    oper_code,
    oper_time,
    DATE_FORMAT(oper_time, '%Y-%m-%d %H:%i:%s') AS operTimeDt,
    confirm_username,
    confirm_time,
    DATE_FORMAT(confirm_time, '%Y-%m-%d %H:%i:%s') AS confirmTimeDt,
    confirm_remark,
    yshx_state,
    create_by,
    create_code,
    create_time,
    DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') AS createTimeDt,
    write_unit ,
    create_dept_id,
--     yshx_amount,
    yshx_Time,
    DATE_FORMAT(yshx_Time, '%Y-%m-%d %H:%i:%s') AS yshxTimeDt,
    yshx_username,
    oper_dept_id
    </sql>
    <sql id="Base_Column_insidereceivefeeList">
    b.id,
    a.billname,
    a.bill_amount,
    a.billdate,
    b.pay_unit,
    b.billcode,
    b.job_id,
    b.jobcode,
    b.work_id,
    b.workcode,
    b.bill_id,
    b.get_unit,
    b.freight,
    b.delivery_fee,
    b.handling_fee,
    b.pick_fee,
    b.ultrafar_fee,
    b.add_points_fee,
    b.reduce_points_fee,
    b.hort_haul_fee,
    b.excess_basket_fee,
    b.abnormal_fee,
    b.other_cost1,
    b.other_cost2,
    b.other_cost3,
    b.other_cost4,
    b.other_cost5,
    b.other_cost6,
    b.other_cost7,
    b.other_cost8,
    b.other_cost9,
    b.other_cost10,
    b.other_cost11,
    b.other_cost12,
    b.total_number,
    b.total_weight,
    b.total_volume,
    b.review_status,
    b.basic_fees_total,
    b.other_fees_total,
    b.remark
    </sql>
    <select id="getBmsInsideysbillmainList" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsInsideysbillmainListBean" resultMap="ListMap">
        select
        <include refid="Base_Column_List"/>
        from bms_insideysbillmain b
        <where>
            b.del_flag = 0
            <if test="billcode != null and billcode != ''">
                and b.billcode ${billcode}
            </if>
            <if test="receiveUnit != null and receiveUnit != ''">
                and b.receive_unit ${receiveUnit}
            </if>
            <if test="payUnit != null and payUnit != ''">
                and b.pay_unit ${payUnit}
            </if>
            <if test="confirmState != null and confirmState != ''">
                and b.confirm_state ${confirmState}
            </if>
            <if test="billDateStart != null and billDateStart != ''">
                and b.billdate ${billDateStart}
            </if>
            <if test="billDateEnd != null and billDateEnd != ''">
                and b.billdate ${billDateEnd}
            </if>
            <if test="billname != null and billname != ''">
                and b.billname ${billname}
            </if>
            <if test="billUnit != null and billUnit != ''">
                and b.bill_unit ${billUnit}
            </if>
            <if test="adjustedAmount != null and adjustedAmount != ''">
                and b.adjusted_amount ${adjustedAmount}
            </if>
            <if test="billState != null and billState != ''">
                and b.bill_state ${billState}
            </if>
            <if test="confirmUsercode != null and confirmUsercode != ''">
                and b.confirm_usercode ${confirmUsercode}
            </if>
            <if test="remarkI != null and remarkI != ''">
                and b.remark ${remarkI}
            </if>
            <if test="confirmUsername != null and confirmUsername != ''">
                and b.confirm_username ${confirmUsername}
            </if>
            <if test="confirmTimeStart != null and confirmTimeStart != ''">
                and b.confirm_time ${confirmTimeStart}
            </if>
            <if test="confirmTimeEnd != null and confirmTimeEnd != ''">
                and b.confirm_time ${confirmTimeEnd}
            </if>
            <if test="confirmRemark != null and confirmRemark != ''">
                and b.confirm_remark ${confirmRemark}
            </if>
            <if test="yshxState != null and yshxState != ''">
                and b.yshx_state ${yshxState}
            </if>
            <if test="createByI != null and createByI != ''">
                and b.create_by ${createByI}
            </if>
            <if test="createCode != null and createCode != ''">
                and b.create_code ${createCode}
            </if>
            <if test="createTimeStart != null and createTimeStart != ''">
                and b.create_time ${createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                and b.create_time ${createTimeEnd}
            </if>
            <if test="writeUnit != null and writeUnit != ''">
                and b.write_unit ${writeUnit}
            </if>
            <if test="createDeptId != null and createDeptId != ''">
                and b.create_dept_id ${createDeptId}
            </if>
            <if test="operTimeStart != null and operTimeStart != ''">
                and b.oper_time ${operTimeStart}
            </if>
            <if test="operTimeEnd != null and operTimeEnd != ''">
                and b.oper_time ${operTimeEnd}
            </if>
            <if test="operBy != null and operBy != ''">
                and b.oper_by ${operBy}
            </if>
            <if test="operCode != null and operCode != ''">
                and b.oper_code ${operCode}
            </if>
            <if test="operDeptId != null and operDeptId != ''">
                and b.oper_dept_id ${operDeptId}
            </if>
            <if test="yshxTimeStart != null and yshxTimeStart != ''">
                and b.yshx_time ${yshxTimeStart}
            </if>
            <if test="yshxTimeEnd != null and yshxTimeEnd != ''">
                and b.yshx_time ${yshxTimeEnd}
            </if>
            <if test="yshxUsername != null and yshxUsername != ''">
                and b.yshx_username ${yshxUsername}
            </if>
            <if test="billAmount != null and billAmount != ''">
                and b.bill_amount ${billAmount}
            </if>
        </where>
        order by b.create_time desc
    </select>
    <select id="getBmsInsideysbillmainInfo" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsInsideysbillmainListBean" resultMap="ListMap">
        select
        <include refid="Base_Column_List"/>
            from bms_insideysbillmain b
        <where>
            b.del_flag = 0 and b.id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <update id="remove" parameterType="java.util.Map">
        update bms_insideysbillmain set
            del_flag = 1,
                oper_dept_id = #{operDeptId},
                oper_code = #{operCode},
                oper_by = #{operBy},
                oper_time = #{operTime}
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="update" parameterType="com.bbyb.joy.bms.domain.dto.bill.BmsInsideysbillmainBean">
        update bms_insideysbillmain
        <trim prefix="SET" suffixOverrides=",">
            <if test="billName != null and billName != ''">
                billname = #{billName},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </trim>
         where id = #{id}

    </update>

    <select id="getbmsInsidereceivefeeList" parameterType="com.bbyb.joy.bms.domain.dto.dto.BmsInsidereceivefeeDto" resultMap="insidereceivefeeMap">
        select
        <include refid="Base_Column_insidereceivefeeList"/>
        from bms_insideysbillmain a left join bms_insidereceivefee b on a.billcode = b.billcode and b.del_flag = 0
        <where>
            a.del_flag = 0
            <if test="workcode != null and workcode != ''">
                and b.workcode ${workcode}
            </if>
            <if test="jobcode != null and jobcode != ''">
                and b.jobcode ${jobcode}
            </if>
            <if test="billAmount != null and billAmount != ''">
                and a.bill_amount ${billAmount}
            </if>
            <if test="getUnit != null and getUnit != ''">
                and b.get_unit ${getUnit}
            </if>
            <if test="payUnit != null and payUnit != ''">
                and b.pay_unit ${payUnit}
            </if>
            <if test="freight != null and freight != ''">
                and b.freight ${freight}
            </if>
            <if test="deliveryFee != null and deliveryFee != ''">
                and b.delivery_fee ${deliveryFee}
            </if>
            <if test="handlingFee != null and handlingFee != ''">
                and b.handling_fee ${handlingFee}
            </if>
            <if test="pickFee != null and pickFee != ''">
                and b.pick_fee ${pickFee}
            </if>
            <if test="ultrafarFee != null and ultrafarFee != ''">
                and b.ultrafar_fee ${ultrafarFee}
            </if>
            <if test="addPointsFee != null and addPointsFee != ''">
                and b.addPoints_fee ${addPointsFee}
            </if>
            <if test="reducePointsFee != null and reducePointsFee != ''">
                and b.reducePoints_fee ${reducePointsFee}
            </if>
            <if test="hortHaulFee != null and hortHaulFee != ''">
                and b.hortHaul_fee ${hortHaulFee}
            </if>
            <if test="excessBasketFee != null and excessBasketFee != ''">
                and b.excessBasket_fee ${excessBasketFee}
            </if>
            <if test="abnormalFee != null and abnormalFee != ''">
                and b.abnormal_fee ${abnormalFee}
            </if>
            <if test="otherCost1 != null and otherCost1 != ''">
                and b.other_cost1 ${otherCost1}
            </if>
            <if test="otherCost2 != null and otherCost2 != ''">
                and b.other_cost2 ${otherCost2}
            </if>
            <if test="otherCost3 != null and otherCost3 != ''">
                and b.other_cost3 ${otherCost3}
            </if>
            <if test="otherCost3 != null and otherCost3 != ''">
                and b.other_cost3 ${otherCost3}
            </if>
            <if test="otherCost4 != null and otherCost4 != ''">
                and b.other_cost4 ${otherCost4}
            </if>
            <if test="otherCost5 != null and otherCost5 != ''">
                and b.other_cost5 ${otherCost5}
            </if>
            <if test="otherCost6 != null and otherCost6 != ''">
                and b.other_cost6 ${otherCost6}
            </if>
            <if test="otherCost7 != null and otherCost7 != ''">
                and b.other_cost7 ${otherCost7}
            </if>
            <if test="otherCost8 != null and otherCost8 != ''">
                and b.other_cost8 ${otherCost8}
            </if>
            <if test="otherCost9 != null and otherCost9 != ''">
                and b.other_cost9 ${otherCost9}
            </if>
            <if test="otherCost10 != null and otherCost10 != ''">
                and b.other_cost10 ${otherCost10}
            </if>
            <if test="otherCost11 != null and otherCost11 != ''">
                and b.other_cost11 ${otherCost11}
            </if>
            <if test="otherCost12 != null and otherCost12 != ''">
                and b.other_cost12 ${otherCost12}
            </if>
            <if test="remarkI != null and remarkI != ''">
                and b.remark ${remarkI}
            </if>
            <if test="totalNumber != null and totalNumber != ''">
                and b.total_number ${totalNumber}
            </if>
            <if test="totalWeight != null and totalWeight != ''">
                and b.total_weight ${totalWeight}
            </if>
            <if test="totalVolume != null and totalVolume != ''">
                and b.total_volume ${totalVolume}
            </if>
            and b.billcode in
            <foreach collection="codes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        order by b.create_time desc
    </select>

    <update id="cancelVerification" parameterType="java.util.Map">
        update bms_insideysbillmain set
            yshx_state = #{type},
                oper_dept_id = #{operDeptId},
                oper_code = #{operCode},
                oper_by = #{operBy},
                oper_time = #{operTime},
                yshx_username = #{yshxUsername},
                yshx_time = #{yshxTime}
        <where>
            id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="billConfirmation" parameterType="java.util.Map">
        update bms_insideysbillmain
        <trim prefix="SET" suffixOverrides=",">
            confirm_state = #{type},
                confirm_remark = #{confirmRemark},
                confirm_usercode = #{confirmUsercode},
                confirm_username = #{confirmUsername},
                confirm_time = #{confirmTime},
            oper_dept_id = #{operDeptId},
            oper_code = #{operCode},
            oper_by = #{operBy},
            oper_time = #{operTime}
        </trim>
        <where>
            id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="removeDetail" parameterType="java.util.Map">
        update bms_insidereceivefee set
            bill_id = null,billcode = null,create_bill = 0,
                oper_dept_id = #{operDeptId},
                oper_code = #{operCode},
                oper_by = #{operBy},
                oper_time = #{operTime}
        where bill_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <select id="getRegistrationByIds" resultType="com.bbyb.joy.bms.domain.dto.BmsRegistrationDto" parameterType="java.util.Map">
        SELECT
        byj.id AS id,
        byj.relate_code AS relateCode,
        byj.total_boxes AS totalBoxes,
        byj.total_number AS totalNumber,
        byj.total_weight AS totalWeight,
        byj.total_volume AS totalVolume,
        byj.cargo_value AS cargoValue,
        byj.incharging_state AS inchargingState,
        byj.deliver_sitecode AS deliverSitecode,
        byj.deliver_address AS deliverAddress,
        byj.deliver_province AS deliverProvince,
        byj.deliver_city AS deliverCity,
        byj.deliver_district AS deliverDistrict,
        byj.deliver_sitename AS deliverSitename,
        byj.arrive_sitecode AS arriveSitecode,
        byj.arrive_address AS arriveAddress,
        byj.arrive_sitename AS arriveSitename,
        byj.merger_billcode AS mergerBillcode,
        byj.arrive_unit AS arriveUnit,
        byj.start_unit AS startUnit,
        byj.pay_unit AS payUnit,
        byj.workcode AS workcode,
        byf.car_code AS carCode,
        byf.driver AS driver,
        byf.carrier_name AS carrierName,
        byf.carrier_code AS carrierCode,
        byf.dispatch_date AS dispatchDate,
        DATE_FORMAT(byf.dispatch_date, '%Y-%m-%d %H:%i:%s') AS dispatchDateDt
        bys.client_code AS clientCode,
        bys.client_name AS clientName,
        bys.signing_date AS signingDate,
        DATE_FORMAT(bys.signing_date, '%Y-%m-%d %H:%i:%s') AS signingDateDt,
        bys.order_date AS orderDate,
        DATE_FORMAT(bys.order_date, '%Y-%m-%d %H:%i:%s') AS orderDateDt,
        bi.billcode AS billCode,
        bi.charging_type AS chargingType,
        bi.freight AS freight,
        bi.delivery_fee AS deliveryFee,
        bi.handling_fee AS handlingFee,
        bi.pick_fee AS pickFee,
        bi.ultrafar_fee AS ultrafarFee,
        bi.add_points_fee AS addPointsFee,
        bi.reduce_points_fee AS reducePointsFee,
        bi.hort_haul_fee AS hortHaulFee,
        bi.excess_basket_fee AS excessBasketFee,
        bi.abnormal_fee AS abnormalFee,
        bi.other_cost1 AS otherCost1,
        bi.other_cost2 AS otherCost2,
        bi.other_cost3 AS otherCost3,
        bi.other_cost4 AS otherCost4,
        bi.other_cost5 AS otherCost5,
        bi.other_cost6 AS otherCost6,
        bi.other_cost7 AS otherCost7,
        bi.other_cost8 AS otherCost8,
        bi.other_cost9 AS otherCost9,
        bi.other_cost10 AS otherCost10,
        bi.other_cost11 AS otherCost11,
        bi.other_cost12 AS otherCost12,
        bi.basic_fees_total AS basicFeesTotal,
        bi.other_fees_total AS otherFeesTotal,
        bi.oper_by AS operBy,
        bi.oper_time AS operTime,
        DATE_FORMAT(bi.oper_time, '%Y-%m-%d %H:%i:%s') AS operTimeDt,
        bi.oper_dept_id AS operDeptId,
        bi.create_by AS createByI,
        bi.create_time AS createTime,
        DATE_FORMAT(bi.create_time, '%Y-%m-%d %H:%i:%s') AS createTimeDt,
        bi.create_dept_id AS createDeptId,
        bi.review_status AS reviewStatus,
        bi.remark AS remarkI
        FROM
        bms_yfjobbillinfo byj
        LEFT JOIN
        bms_yfbillcodeinfo byf on byf.virtual_order_no = byj.scheduling_bill_code and byf.del_flag = 0
        LEFT JOIN
        bms_ysbillcodeinfo bys on byj.workcode = bys.relate_code and bys.del_flag = 0 and bys.code_type = 1
        LEFT JOIN
        bms_insidereceivefee bi on bi.jobcode = byj.relate_code and bi.del_flag = 0
        <where>
            byj.del_flag = 0 and byj.pay_unit = byj.start_unit and byf.transport_mode = 1
            and byj.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>

        </where>

    </select>


    <select id="getBmsFeeList" resultType="com.bbyb.joy.bms.domain.dto.dto.BmsInsidereceivefeeExportDto" parameterType="java.util.Map">
        select
        b.pay_unit AS payUnit,
        b.billcode AS billcode,
        b.bill_date AS billDate,
        b.jobcode AS jobcode,
        b.workcode AS workcode,
        b.get_unit AS getUnit,
        b.freight AS freight,
        b.delivery_fee AS deliveryFee,
        b.handling_fee AS handlingFee,
        b.pick_fee AS pickFee,
        b.ultrafar_fee AS ultrafarFee,
        b.add_points_fee AS addPointsFee,
        b.reduce_points_fee AS reducePointsFee,
        b.hort_haul_fee AS hortHaulFee,
        b.excess_basket_fee AS excessBasketFee,
        b.abnormal_fee AS abnormalFee,
        b.other_cost1 AS otherCost1,
        b.other_cost2 AS otherCost2,
        b.other_cost3 AS otherCost3,
        b.other_cost4 AS otherCost4,
        b.other_cost5 AS otherCost5,
        b.other_cost6 AS otherCost6,
        b.other_cost7 AS otherCost7,
        b.other_cost8 AS otherCost8,
        b.other_cost9 AS otherCost9,
        b.other_cost10 AS otherCost10,
        b.other_cost11 AS otherCost11,
        b.other_cost12 AS otherCost12,
        b.total_number AS totalNumber,
        b.total_weight AS totalWeight,
        b.remark AS remarkI,
        b.total_volume AS totalVolume,
        b.review_status AS reviewStatus,
        b.charging_type AS chargingType,
        b.create_bill AS createBill,
        b.basic_fees_total AS basicFeesTotal,
        b.other_fees_total AS otherFeesTotal
            from bms_insidereceivefee b
        <where>
            b.del_flag = 0 and b.job_id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <select id="getRegistrationList" resultType="com.bbyb.joy.bms.domain.dto.BmsRegistrationDto" parameterType="java.util.Map">
        SELECT
        j.id AS id,
        j.relate_code AS relateCode,
        j.order_code AS workcode,
        j.virtual_scheduling_code AS schedulingBillCode,
        j.total_boxes AS totalBoxes,
        j.total_number AS totalNumber,
        j.total_weight AS totalWeight,
        j.total_volume AS totalVolume,
        j.total_cargo_value AS cargoValue,
        j.incharging_state AS inchargingState,
        j.originating_address_code AS deliverSitecode,
        j.originating_address AS deliverAddress,
        j.originating_province AS deliverProvince,
        j.originating_city AS deliverCity,
        j.originating_area AS deliverDistrict,
        j.originating_address_name AS deliverSitename,
        j.destination_address_code AS arriveSitecode,
        j.destination_address AS arriveAddress,
        j.destination_address_name AS arriveSitename,
        j.merger_billcode AS mergerBillcode,
        j.destination_province AS orderProvince,
        j.destination_city AS orderCity,
        j.destination_area AS orderDistrict,
        j.destination_company_id AS arriveUnit,
        j.originating_company_id AS startUnit,
        j.pay_company_id AS payUnit,
        d.car_number AS carCode,
        d.driver AS driver,
        d.transport_type AS transportType,
        d.carrier_name AS carrierName,
        d.carrier_code AS carrierCode,
        d.dispatch_date AS dispatchDate,
        DATE_FORMAT(d.dispatch_date, '%Y-%m-%d %H:%i:%s') AS dispatchDateDt,
        d.start_date AS startDate,
        DATE_FORMAT(d.start_date, '%Y-%m-%d %H:%i:%s') AS startDateDt,
        j.client_code AS clientCode,
        j.client_name AS clientName,
        j.create_time AS orderDate,
        DATE_FORMAT(j.create_time, '%Y-%m-%d %H:%i:%s') AS orderDateDt,
        j.pk_id AS workId,
        j.remark AS remarkI,
        j.oper_by AS operBy,
        j.oper_time AS operTime,
        DATE_FORMAT(j.oper_time, '%Y-%m-%d %H:%i:%s') AS operTimeDt,
        j.oper_dept_id AS operDeptId,
        j.create_by AS createByI,
        j.create_time AS createTime,
        DATE_FORMAT(j.create_time, '%Y-%m-%d %H:%i:%s') AS createTimeDt,
        j.create_dept_id AS createDeptId,
        bi.id AS feeId,
        bi.billcode AS billCode,
        bi.charging_type AS chargingType,
        bi.freight AS freight,
        bi.delivery_fee AS deliveryFee,
        bi.handling_fee AS handlingFee,
        bi.pick_fee AS pickFee,
        bi.ultrafar_fee AS ultrafarFee,
        bi.add_points_fee AS addPointsFee,
        bi.reduce_points_fee AS reducePointsFee,
        bi.hort_haul_fee AS hortHaulFee,
        bi.excess_basket_fee AS excessBasketFee,
        bi.abnormal_fee AS abnormalFee,
        bi.other_cost1 AS otherCost1,
        bi.other_cost2 AS otherCost2,
        bi.other_cost3 AS otherCost3,
        bi.other_cost4 AS otherCost4,
        bi.other_cost5 AS otherCost5,
        bi.other_cost6 AS otherCost6,
        bi.other_cost7 AS otherCost7,
        bi.other_cost8 AS otherCost8,
        bi.other_cost9 AS otherCost9,
        bi.other_cost10 AS otherCost10,
        bi.other_cost11 AS otherCost11,
        bi.other_cost12 AS otherCost12,
        bi.basic_fees_total AS basicFeesTotal,
        bi.other_fees_total AS otherFeesTotal,
        bi.oper_by AS operBy,
        bi.oper_time AS operTime,
        ifnull(bi.create_bill,1) AS createBill,
        DATE_FORMAT(bi.oper_time, '%Y-%m-%d %H:%i:%s') AS operTimeDt,
        bi.oper_dept_id AS operDeptId,
        bi.create_by AS createByI,
        bi.create_time AS createTime,
        DATE_FORMAT(bi.create_time, '%Y-%m-%d %H:%i:%s') AS createTimeDt,
        bi.create_dept_id AS createDeptId,
        ifnull(bi.review_status,1) AS reviewStatus,
        bi.review_by AS reviewBy,
        bi.review_time AS reviewTime,
        DATE_FORMAT(bi.review_time, '%Y-%m-%d %H:%i:%s') AS reviewTimeDt,
        bi.remark AS remarkI
        FROM bms_job_code_info j
        LEFT JOIN bms_dispatch_code_info d ON j.virtual_scheduling_code = d.virtual_scheduling_code AND d.del_flag = 0
        LEFT JOIN bms_insidereceivefee bi on bi.jobcode = j.relate_code and bi.del_flag = 0
        <where>
            j.del_flag = 0 and j.pay_company_id != j.originating_company_id
            <if test="orderCity != null and orderCity != ''">
                AND j.destination_city ${orderCity}
            </if>
            <if test="workcode != null and workcode!=''">
                AND j.order_code ${workcode}
            </if>
            <if test="relateCode != null and relateCode!=''">
                AND j.relate_code ${relateCode}
            </if>
            <if test="schedulingBillCode != null and schedulingBillCode!=''">
                AND j.virtual_scheduling_code ${schedulingBillCode}
            </if>
            <if test="reviewStatusStr != null and reviewStatusStr != ''">
                and (
                <if test="reviewStatusStr == 1">
                    bi.review_status = #{reviewStatusStr} or bi.review_status is null
                </if>
                <if test="reviewStatusStr != 1">
                    bi.review_status = #{reviewStatusStr}
                </if>
                )
            </if>
            <if test="reviewTimeStart != null and reviewTimeStart != ''">
                and bi.review_time ${reviewTimeStart}
            </if>
            <if test="reviewTimeEnd != null and reviewTimeEnd != ''">
                and bi.review_time ${reviewTimeEnd}
            </if>
            <if test="orderDateStart != null and orderDateStart != ''">
                and j.create_time ${orderDateStart}
            </if>
            <if test="orderDateEnd != null and orderDateEnd != ''">
                and j.create_time ${orderDateEnd}
            </if>
            <if test="dispatchDateStart != null and dispatchDateStart != ''">
                and d.dispatch_date ${dispatchDateStart}
            </if>
            <if test="dispatchDateEnd != null and dispatchDateEnd != ''">
                and d.dispatch_date ${dispatchDateEnd}
            </if>
            <if test="startDateStart != null and startDateStart != ''">
                and d.start_date ${startDateStart}
            </if>
            <if test="startDateEnd != null and startDateEnd != ''">
                and d.start_date ${startDateEnd}
            </if>
            <if test="signingDateStart != null and signingDateStart != ''">
                and d.finish_date ${signingDateStart}
            </if>
            <if test="signingDateEnd != null and signingDateEnd != ''">
                and d.finish_date ${signingDateEnd}
            </if>
            <if test="createBillStr != null and createBillStr != ''">
                and (
                <if test="createBillStr == 1">
                    bi.create_bill = #{createBillStr} or bi.create_bill is null
                </if>
                <if test="createBillStr != 1">
                    bi.create_bill = #{createBillStr}
                </if>
                )
            </if>
            <if test="totalBoxes != null and totalBoxes != ''">
                AND j.total_boxes ${totalBoxes}
            </if>
            <if test="totalNumber != null and totalNumber != ''">
                AND j.total_number ${totalNumber}
            </if>
            <if test="totalWeight != null and totalWeight != ''">
                AND j.total_weight ${totalWeight}
            </if>
            <if test="totalVolume != null and totalVolume != ''">
                AND j.total_volume ${totalVolume}
            </if>
            <if test="cargoValue != null and cargoValue != ''">
                AND j.total_cargo_value ${cargoValue}
            </if>
            <if test="inchargingState != null and inchargingState != ''">
                AND j.incharging_state ${inchargingState}
            </if>
            <if test="deliverProvince != null and deliverProvince != ''">
                AND j.originating_province ${deliverProvince}
            </if>
            <if test="deliverCity != null and deliverCity != ''">
                AND j.originating_city ${deliverCity}
            </if>
            <if test="deliverDistrict != null and deliverDistrict != ''">
                AND j.originating_area ${deliverDistrict}
            </if>
            <if test="arriveProvince != null and arriveProvince != ''">
                AND j.destination_province ${arriveProvince}
            </if>
            <if test="arriveCity != null and arriveCity != ''">
                AND j.destination_city ${arriveCity}
            </if>
            <if test="arriveDistrict != null and arriveDistrict != ''">
                AND j.destination_area ${arriveDistrict}
            </if>
            <if test="carCode != null and carCode != ''">
                AND d.car_number ${carCode}
            </if>
            <if test="driver != null and driver != ''">
                AND d.driver ${driver}
            </if>
            <if test="carrierName != null and carrierName != ''">
                AND d.carrier_name ${carrierName}
            </if>
            <if test="transportType != null and transportType != ''">
                AND d.transport_type ${transportType}
            </if>
            <if test="payUnit != null and payUnit != ''">
                AND j.pay_company_id ${payUnit}
            </if>
            <if test="startUnit != null and startUnit!=''">
                AND j.originating_company_id ${startUnit}
            </if>
            <if test="inchargingState != null and inchargingState != ''">
                and j.incharging_state ${inchargingState}
            </if>
        </where>
        ORDER BY j.create_time DESC
    </select>


</mapper>
