<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.db.MdmSkuinfoMapper">
    
    <resultMap type="com.bbyb.joy.bms.domain.dto.MdmSkuinfo" id="MdmSkuinfoResult">
        <result property="id"    column="id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="skuCode"    column="sku_code"    />
        <result property="skuName"    column="sku_name"    />
        <result property="temperatureType"    column="temperature_type"    />
        <result property="unit"    column="unit"    />
        <result property="lenght"    column="lenght"    />
        <result property="width"    column="width"    />
        <result property="height"    column="height"    />
        <result property="weight"    column="weight"    />
        <result property="volume"    column="volume"    />
        <result property="skuTypeName"    column="sku_type_name"    />
        <result property="boxType"    column="box_type"    />
        <result property="boxTypeNum"    column="box_type_num"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="qualityManage"    column="quality_manage"    />
        <result property="qualityDay"    column="quality_day"    />
        <result property="batchManage"    column="batch_manage"    />
        <result property="status"    column="status"    />
        <result property="specification"    column="specification"    />

    </resultMap>

    <sql id="selectMdmSkuinfoVo">
        select id, client_code, sku_code, sku_name, temperature_type, unit, lenght, width, height, weight, volume, sku_type_name, box_type, box_type_num, del_flag,specification from mdm_skuinfo
    </sql>

    <select id="selectMdmSkuinfoList" parameterType="com.bbyb.joy.bms.domain.dto.MdmSkuinfo" resultMap="MdmSkuinfoResult">
        <include refid="selectMdmSkuinfoVo"/>
        <where>  
            <if test="clientCode != null  and clientCode != ''"> and client_code = #{clientCode}</if>
            <if test="skuCode != null  and skuCode != ''"> and sku_code = #{skuCode}</if>
            <if test="skuName != null  and skuName != ''"> and sku_name like concat('%', #{skuName}, '%')</if>
            <if test="temperatureType != null  and temperatureType != ''"> and temperature_type = #{temperatureType}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="lenght != null "> and lenght = #{lenght}</if>
            <if test="width != null "> and width = #{width}</if>
            <if test="height != null "> and height = #{height}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="volume != null "> and volume = #{volume}</if>
            <if test="skuTypeName != null  and skuTypeName != ''"> and sku_type_name like concat('%', #{skuTypeName}, '%')</if>
            <if test="boxType != null  and boxType != ''"> and box_type = #{boxType}</if>
            <if test="boxTypeNum != null  and boxTypeNum != ''"> and box_type_num = #{boxTypeNum}</if>
        </where>
    </select>
    
    <select id="selectMdmSkuinfoById" parameterType="java.lang.String" resultMap="MdmSkuinfoResult">
        <include refid="selectMdmSkuinfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMdmSkuinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmSkuinfo" useGeneratedKeys="true" keyProperty="id">
        insert into mdm_skuinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clientCode != null">client_code,</if>
            <if test="skuCode != null">sku_code,</if>
            <if test="skuName != null">sku_name,</if>
            <if test="temperatureType != null">temperature_type,</if>
            <if test="unit != null">unit,</if>
            <if test="lenght != null">lenght,</if>
            <if test="width != null">width,</if>
            <if test="height != null">height,</if>
            <if test="weight != null">weight,</if>
            <if test="volume != null">volume,</if>
            <if test="skuTypeName != null">sku_type_name,</if>
            <if test="boxType != null">box_type,</if>
            <if test="boxTypeNum != null">box_type_num,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="qualityManage != null">quality_manage,</if>
            <if test="qualityDay != null">quality_day,</if>
            <if test="batchManage != null">batch_manage,</if>
            <if test="status != null">status,</if>
            <if test="specification != null">specification,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clientCode != null">#{clientCode},</if>
            <if test="skuCode != null">#{skuCode},</if>
            <if test="skuName != null">#{skuName},</if>
            <if test="temperatureType != null">#{temperatureType},</if>
            <if test="unit != null">#{unit},</if>
            <if test="lenght != null">#{lenght},</if>
            <if test="width != null">#{width},</if>
            <if test="height != null">#{height},</if>
            <if test="weight != null">#{weight},</if>
            <if test="volume != null">#{volume},</if>
            <if test="skuTypeName != null">#{skuTypeName},</if>
            <if test="boxType != null">#{boxType},</if>
            <if test="boxTypeNum != null">#{boxTypeNum},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="qualityManage != null">#{qualityManage},</if>
            <if test="qualityDay != null">#{qualityDay},</if>
            <if test="batchManage != null">#{batchManage},</if>
            <if test="status != null">#{status},</if>
            <if test="specification != null">#{specification},</if>
         </trim>
    </insert>

    <update id="updateMdmSkuinfo" parameterType="com.bbyb.joy.bms.domain.dto.MdmSkuinfo">
        update mdm_skuinfo
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="skuCode != null">sku_code = #{skuCode},</if>
            <if test="skuName != null">sku_name = #{skuName},</if>
            <if test="temperatureType != null">temperature_type = #{temperatureType},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="lenght != null">lenght = #{lenght},</if>
            <if test="width != null">width = #{width},</if>
            <if test="height != null">height = #{height},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="volume != null">volume = #{volume},</if>
            <if test="skuTypeName != null">sku_type_name = #{skuTypeName},</if>
            <if test="boxType != null">box_type = #{boxType},</if>
            <if test="boxTypeNum != null">box_type_num = #{boxTypeNum},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="qualityManage != null">quality_manage = #{qualityManage},</if>
            <if test="qualityDay != null">quality_day = #{qualityDay},</if>
            <if test="batchManage != null">batch_manage = #{batchManage},</if>
            <if test="status != null">status = #{status},</if>
            <if test="specification != null">specification = #{specification},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMdmSkuinfoById" parameterType="java.lang.String">
        delete from mdm_skuinfo where id = #{id}
    </delete>

    <delete id="deleteMdmSkuinfoByIds">
        delete from mdm_skuinfo where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateMdmSkuinfoStatusByIds">
        update mdm_skuinfo set status = #{status} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectSkuList" resultMap="MdmSkuinfoResult">
        <include refid="selectMdmSkuinfoVo"/>
        where del_flag=0
        <if test="ids != null and ids.size>0 ">
            AND id in
            <foreach collection="ids" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
        <if test="codes != null and codes.size>0 ">
            AND sku_code in
            <foreach collection="codes" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
    </select>

    <select id="checkSkuCodeUnique" parameterType="java.lang.String" resultMap="MdmSkuinfoResult">
        select id, sku_code from mdm_skuinfo where del_flag=0 and sku_code=#{skuCode} limit 1
    </select>

</mapper>