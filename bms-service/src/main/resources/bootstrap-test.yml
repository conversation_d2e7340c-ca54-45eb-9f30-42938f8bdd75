spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  cloud:
    nacos:
      discovery:
        server-addr:
        username:
        password:
        namespace:
      config:
        server-addr:
        username:
        password:
        namespace:
  kafka:
    bootstrap-servers:
  redis:
    password:
    host:
    port:
seata:
  service:
    grouplist:
      default: