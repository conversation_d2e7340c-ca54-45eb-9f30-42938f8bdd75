server:
  port: 1036
spring:
  application:
    name: bms-service
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  profiles:
    active: local
  cloud:
    nacos:
      config:
        file-extension: yml
  kafka:
    producer:
      retries: 5
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: 1
    consumer:
      auto-commit-interval: 1S
      auto-offset-reset: earliest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      concurrency: 5
      ack-mode: manual_immediate
      missing-topics-fatal: false
  redis:
    database: 0
    jedis:
      pool:
        max-active: 8
        max-idle: 8
        time-between-eviction-runs: 60000
        min-idle: 2
    timeout: 60000
local:
  defaulSysCode: BMS
  checkWarehouse: false
server-config:
  httpurl:
wms:
  mdm:
    dicQuery:
    companySimpleList:
    queryByCustomerCode:
    queryCategoryInfo:
    sysUrlCommonPrefix:
    refreshLogicalConfig:
  base:
    queryDataPermissions:
esign:
  APP_ID:
  APP_key:
  HOST:
  SIGN_CALL_BACK:
management:
  endpoints:
    web:
      exposure:
        include: "health"
seata:
  service:
    vgroupMapping:
      default_tx_group: default
  dataSourceProxyMode: XA
  enable-auto-data-source-proxy: false
