package com.bbyb.joy.bms.support.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.bbyb.bms.model.po.BmsInterfaceRecvRecerdLogPOWithBLOBs;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.biz.*;
import com.bbyb.joy.bms.domain.dto.*;
import com.bbyb.joy.bms.domain.enums.InterfaceRecvRecerdEnum;
import com.bbyb.joy.bms.domain.enums.SysCodeEnum;
import com.bbyb.joy.bms.service.BmsInterfaceRecvRecerdLogService;
import com.bbyb.joy.constants.Constant;
import com.bbyb.joy.constants.Key;
import com.bbyb.joy.core.context.RedisContext;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.mdm.bean.MdmFeeSubjectBean;
import com.bbyb.joy.mdm.bean.company.ComPanyQueryBean;
import com.bbyb.joy.mdm.bean.dic.DicQueryBean;
import com.bbyb.joy.mdm.dto.CompanyDto;
import com.bbyb.joy.mdm.dto.DicDto;
import com.bbyb.joy.mdm.feign.CompanyFeignService;
import com.bbyb.joy.mdm.feign.DicFeignService;
import com.bbyb.joy.mdm.feign.MdmFeeSubjectFeignService;
import com.bbyb.joy.mdm.vo.MdmFeeSubjectVO;
import com.bbyb.mdm.model.po.PubIdreplacePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 基础数据文本转换通用工具类
 */
@Slf4j
@Component
public class TextConversionUtil {
    @Resource
    BmsInterfaceRecvRecerdLogService interfaceRecvRecerdLogService;
    @Resource
    private MdmClientinfoBiz clientinfoMapper;
    @Resource
    private MdmRouteinfoBiz routeinfoMapper;
    @Resource
    private PubSyslogicsetBiz pubSyslogicsetMapper;
    @Resource
    private MdmCarrierinfoBiz carrierinfoMapper;
    @Resource
    private MdmStoreinfoBiz storeinfoMapper;
    @Resource
    private MdmWarehouseinfoBiz warehouseinfoMapper;
    @Resource
    private MdmSkuinfoBiz skuinfoMapper;
    @Resource
    private UtilsDictDataBiz dictDataMapper;
    @Resource
    private MdmCityareaBiz mdmCityareaMapper;
    @Resource
    private PubQuotationClassificationdetailBiz pubQuotationClassificationdetailMapper;
    @Resource
    private DicFeignService dicFeignService;
    @Resource
    private MdmFeeSubjectFeignService mdmFeeSubjectFeignService;
    @Resource
    private RedisContext redisContext;
    @Resource
    private CompanyFeignService companyFeignService;

    /**
     * 根据客户编码或id查询客户信息
     */
    public Map<String, MdmClientinfo> selectClientinfoMap(List<String> ids, List<String> codes, List<String> names) {
        List<MdmClientinfo> clientinfos = clientinfoMapper.selectClientinfoList(RequestContext.getTenantId(), ids, codes, names);
        Map<String, MdmClientinfo> map = new HashMap<>(1);
        if (CollUtil.isNotEmpty(clientinfos) && CollUtil.isNotEmpty(names)) {
            map = clientinfos.stream().collect(Collectors.toMap(MdmClientinfo::getClientName, Function.identity()));
        }
        if (CollUtil.isNotEmpty(clientinfos) && CollUtil.isNotEmpty(codes)) {
            map = clientinfos.stream().collect(Collectors.toMap(MdmClientinfo::getClientCode, Function.identity()));
        }
        if (CollUtil.isNotEmpty(clientinfos) && CollUtil.isNotEmpty(ids)) {
            map = clientinfos.stream().collect(Collectors.toMap(e -> e.getId().toString(), Function.identity()));
        }
        return map;
    }

    /**
     * 根据客户编码或id查询客户信息
     */
    public Map<String, MdmClientinfo> selectClientinfoMap2(List<String> ids, List<String> codes, List<String> names, List<String> brandNames) {
        List<MdmClientinfo> clientinfos = clientinfoMapper.selectClientinfoList2(RequestContext.getTenantId(), ids, codes, names, brandNames);
        Map<String, MdmClientinfo> map = new HashMap<>(1);
        ;
        if (CollUtil.isNotEmpty(clientinfos) && CollUtil.isNotEmpty(names)) {
            map = clientinfos.stream().collect(Collectors.toMap(MdmClientinfo::getClientName, Function.identity()));
        }
        if (CollUtil.isNotEmpty(clientinfos) && CollUtil.isNotEmpty(brandNames)) {
            map = clientinfos.stream().collect(Collectors.toMap(MdmClientinfo::getBrandName, Function.identity()));
        }
        if (CollUtil.isNotEmpty(clientinfos) && CollUtil.isNotEmpty(codes)) {
            map = clientinfos.stream().collect(Collectors.toMap(MdmClientinfo::getClientCode, Function.identity()));
        }
        if (CollUtil.isNotEmpty(clientinfos) && CollUtil.isNotEmpty(ids)) {
            map = clientinfos.stream().collect(Collectors.toMap(e -> e.getId().toString(), Function.identity()));
        }
        return map;
    }


    /**
     * 根据客户编码或id查询客户信息
     */
    public List<MdmClientinfo> selectClientinfoList(List<String> ids) {
        return clientinfoMapper.selectClientinfoList(RequestContext.getTenantId(), ids, new ArrayList<>(), new ArrayList<>());
    }

    /**
     * 根据所属集团客户编码查询子客户信息
     */
    public List<MdmClientinfo> selectClientinfoGroupMap(List<String> codes) {
        return clientinfoMapper.selectClientinfoGroupList(RequestContext.getTenantId(), codes);
    }

    /**
     * 根据线路编码或id查询线路信息
     */
    public Map<String, MdmRouteinfo> selectRouteinfoMap(List<String> ids, List<String> codes) {
        List<MdmRouteinfo> routeinfos = routeinfoMapper.selectRouteinfoList(RequestContext.getTenantId(), ids, codes);
        Map<String, MdmRouteinfo> map = new HashMap<>(1);
        if (CollUtil.isNotEmpty(routeinfos) && CollUtil.isNotEmpty(codes)) {
            map = routeinfos.stream().collect(Collectors.toMap(MdmRouteinfo::getRouteCode, Function.identity()));
        }
        if (CollUtil.isNotEmpty(routeinfos) && CollUtil.isNotEmpty(ids)) {
            map = routeinfos.stream().collect(Collectors.toMap(e -> e.getId().toString(), Function.identity()));
        }
        return map;
    }


    /**
     * 根据省/市/区查询地址信息
     */
    public Map<String, MdmCityarea> selectCityareaMap(List<String> provinces, List<String> citys, List<String> areas) {
        List<MdmCityarea> mdmCityareaList = mdmCityareaMapper.selectCityInfoList(RequestContext.getTenantId(), provinces, citys, areas);
        Map<String, MdmCityarea> map = new HashMap<>(1);
        if (CollUtil.isNotEmpty(mdmCityareaList) && CollUtil.isNotEmpty(provinces)) {
            map = mdmCityareaList.stream().collect(Collectors.toMap(MdmCityarea::getProvince, Function.identity()));
        }
        if (CollUtil.isNotEmpty(mdmCityareaList) && CollUtil.isNotEmpty(citys)) {
            map = mdmCityareaList.stream().collect(Collectors.toMap(MdmCityarea::getCity, Function.identity()));
        }
        if (CollUtil.isNotEmpty(mdmCityareaList) && CollUtil.isNotEmpty(areas)) {
            map = mdmCityareaList.stream().collect(Collectors.toMap(MdmCityarea::getArea, Function.identity()));
        }

        return map;
    }

    /**
     * 根据承运商编码或id查询承运商信息
     */
    public Map<String, MdmCarrierinfo> selectCarrierinfoMap(List<String> ids, List<String> codes, List<String> names) {
        List<MdmCarrierinfo> carrierinfos = carrierinfoMapper.selectCarrierinfoList(ids, codes, names);
        Map<String, MdmCarrierinfo> map = new HashMap<>(1);
        if (CollUtil.isNotEmpty(carrierinfos) && CollUtil.isNotEmpty(names)) {
            map = carrierinfos.stream().collect(Collectors.toMap(MdmCarrierinfo::getCarrierName, Function.identity()));
        }
        if (CollUtil.isNotEmpty(carrierinfos) && CollUtil.isNotEmpty(codes)) {
            map = carrierinfos.stream().collect(Collectors.toMap(MdmCarrierinfo::getCarrierCode, Function.identity()));
        }
        if (CollUtil.isNotEmpty(carrierinfos) && CollUtil.isNotEmpty(ids)) {
            map = carrierinfos.stream().collect(Collectors.toMap(e -> e.getId().toString(), Function.identity()));
        }
        return map;
    }


    /**
     * 根据承运商编码或id查询承运商信息
     * resultType 1：承运商id, 2:承运商编码,3承运商名称
     */
    public Map<String, MdmCarrierinfo> selectCarrierinfoMap(List<String> ids, List<String> codes, List<String> names, Integer resultType) {
        List<MdmCarrierinfo> carrierinfos = carrierinfoMapper.selectCarrierinfoList(ids, codes, names);
        Map<String, MdmCarrierinfo> map = new HashMap<>(1);
        if (CollUtil.isNotEmpty(carrierinfos) && resultType.equals(3)) {
            map = carrierinfos.stream().collect(Collectors.toMap(MdmCarrierinfo::getCarrierName, Function.identity()));
        }
        if (CollUtil.isNotEmpty(carrierinfos) && resultType.equals(2)) {
            map = carrierinfos.stream().collect(Collectors.toMap(MdmCarrierinfo::getCarrierCode, Function.identity()));
        }
        if (CollUtil.isNotEmpty(carrierinfos) && resultType.equals(1)) {
            map = carrierinfos.stream().collect(Collectors.toMap(e -> e.getId().toString(), Function.identity()));
        }
        return map;
    }


    /**
     * 根据门店编码或id查询门店信息
     *
     * @param addrresAttribute 地址属性 1门店  2始发地址
     */
    public Map<String, MdmStoreinfo> selectStoreinfoMap(List<String> ids, List<String> codes, String addrresAttribute) {
        List<MdmStoreinfo> storeinfos = storeinfoMapper.selectStoreinfoList(RequestContext.getTenantId(), ids, codes, addrresAttribute);
        Map<String, MdmStoreinfo> map = new HashMap<>(1);
        ;
        if (CollUtil.isNotEmpty(storeinfos) && CollUtil.isNotEmpty(codes)) {
            map = storeinfos.stream().collect(Collectors.toMap(MdmStoreinfo::getStorecode, Function.identity()));
        }
        if (CollUtil.isNotEmpty(storeinfos) && CollUtil.isNotEmpty(ids)) {
            map = storeinfos.stream().collect(Collectors.toMap(e -> e.getId().toString(), Function.identity()));
        }
        return map;
    }

    /**
     * 根据门店名称查询门店信息
     *
     * @param addrresAttribute 地址属性 1门店  2始发地址
     */
    public Map<String, MdmStoreinfo> selectStoreinfoListByNames(List<String> names, String addrresAttribute) {
        List<MdmStoreinfo> storeinfos = storeinfoMapper.selectStoreinfoListByNames(RequestContext.getTenantId(), names, addrresAttribute);
        Map<String, MdmStoreinfo> map = new HashMap<>(1);
        if (CollUtil.isNotEmpty(storeinfos) && CollUtil.isNotEmpty(names)) {
            map = storeinfos.stream().collect(Collectors.toMap(MdmStoreinfo::getStorename, Function.identity()));
        }
        return map;
    }

    /**
     * 获取系统逻辑设置
     */
    public Map<String, PubSyslogicset> selectPubSyslogicsetMap(String name) {
        if (!StringUtils.isNotNull(name)) {
            return new HashMap<>(1);
        }
        List<PubSyslogicset> data = pubSyslogicsetMapper.selectPubSyslogicsetListBylogicName(RequestContext.getTenantId(), name.split(","));
        return CollUtil.isNotEmpty(data) ?
                data.stream().collect(Collectors.toMap(PubSyslogicset::getLogicName, g -> g)) :
                new HashMap<>(1);
    }

    /**
     * 部门信息
     */
    public Map<Long, String> getCompanyMap() {
        // 查询缓存
        ComPanyQueryBean comPanyQueryBean = new ComPanyQueryBean();
        comPanyQueryBean.setReturnTree(0);
        HashMap<Long, String> objectObjectHashMap = new HashMap<>();
        ResponseResult<List<CompanyDto>> rpcResult = companyFeignService.querySimple(comPanyQueryBean);
        if (rpcResult != null && rpcResult.getResult() != null) {
            rpcResult.getResult().forEach(c -> objectObjectHashMap.put(c.getId().longValue(), c.getName()));
        }
        return objectObjectHashMap;
    }


    /**
     * 根据机构ids/名称查询机构信息(无视当前用户权限)
     */
    public Map<Long, String> getCompanyMapRoot() {
        ComPanyQueryBean comPanyQueryBean = new ComPanyQueryBean();
        comPanyQueryBean.setReturnTree(0);
        comPanyQueryBean.setIsAuthority(1);
        Map<Long, String> objectObjectHashMap = new HashMap<>();
        ResponseResult<List<CompanyDto>> rpcResult = companyFeignService.querySimple(comPanyQueryBean);
        if (rpcResult != null && rpcResult.getResult() != null) {

            List<SysDept> depts = new ArrayList<>();
            for (CompanyDto companyDto : rpcResult.getResult()) {
                SysDept sysDept = new SysDept();
                sysDept.setDeptId(companyDto.getId().longValue());
                sysDept.setDeptName(companyDto.getName());
                depts.add(sysDept);
            }
            depts.forEach(c -> objectObjectHashMap.put(c.getDeptId(), c.getDeptName()));
        }
        return objectObjectHashMap;
    }


    /**
     * 部门信息
     */
    public Map<Integer, String> companyMap() {
        // 查询缓存
        ComPanyQueryBean comPanyQueryBean = new ComPanyQueryBean();
        comPanyQueryBean.setReturnTree(0);
        Map<Integer, String> objectObjectHashMap = new HashMap<>();
        ResponseResult<List<CompanyDto>> rpcResult = companyFeignService.querySimple(comPanyQueryBean);
        if (rpcResult != null && rpcResult.getResult() != null) {
            rpcResult.getResult().forEach(c -> objectObjectHashMap.put(c.getId(), c.getName()));
        }
        return objectObjectHashMap;
    }

    /**
     * 部门信息
     */
    public Map<Integer, String> companyMapRoot() {
        // 查询缓存
        ComPanyQueryBean comPanyQueryBean = new ComPanyQueryBean();
        comPanyQueryBean.setReturnTree(0);
        comPanyQueryBean.setIsAuthority(1);
        Map<Integer, String> objectObjectHashMap = new HashMap<>();
        ResponseResult<List<CompanyDto>> rpcResult = companyFeignService.querySimple(comPanyQueryBean);
        if (rpcResult != null && rpcResult.getResult() != null) {
            rpcResult.getResult().forEach(c -> objectObjectHashMap.put(c.getId(), c.getName()));
        }
        return objectObjectHashMap;
    }



    /**
     * 部门信息
     */
    public Map<String, Integer> companyNameMap() {
        // 查询缓存
        ComPanyQueryBean comPanyQueryBean = new ComPanyQueryBean();
        comPanyQueryBean.setReturnTree(0);
        Map<String, Integer> objectObjectHashMap = new HashMap<>();
        ResponseResult<List<CompanyDto>> rpcResult = companyFeignService.querySimple(comPanyQueryBean);
        if (rpcResult != null && rpcResult.getResult() != null) {
            rpcResult.getResult().forEach(c -> objectObjectHashMap.put(c.getName(), c.getId()));
        }
        return objectObjectHashMap;
    }


    /**
     * 部门信息
     */
    public Map<Long, String> getCompanyMapCode() {
        // 查询缓存
        ComPanyQueryBean comPanyQueryBean = new ComPanyQueryBean();
        comPanyQueryBean.setReturnTree(0);
        comPanyQueryBean.setIsAuthority(1);
        HashMap<Long, String> objectObjectHashMap = new HashMap<>();
        ResponseResult<List<CompanyDto>> rpcResult = companyFeignService.querySimple(comPanyQueryBean);
        if (rpcResult != null && rpcResult.getResult() != null) {
            rpcResult.getResult().forEach(c -> objectObjectHashMap.put(c.getId().longValue(), c.getCode()));
        }
        return objectObjectHashMap;
    }


    /**
     * 根据用户id获取当前账户信息
     */
    public UserBean selectLoginUserInfo(String token) {
        UserBean result = JSON.parseObject(redisContext.getValueByString(Key.CACHE_PC_TOKEN_PREFIX + token), UserBean.class);
        if (result != null && result.getToken() != null) {
            //将此token重新设置两个小时
            redisContext.setExpire(Key.CACHE_PC_TOKEN_PREFIX + token, 60 * 60 * 2);
        }
        return result;
    }


    /**
     * 根据类型获取数据字典
     *
     * @param type    类型 支持多个用逗号分割 例如: sys_oper_type,test_djj_1
     * @param mapType 返回map类型  传2返回Map<类型+名称，数字>,其他返回Map<类型+数字，名称>
     */
    public Map<String, String> getDictByType(String type, String mapType) {
        DicQueryBean queryBean = new DicQueryBean();
        queryBean.setTypenames(type);
        queryBean.setSystype(SysCodeEnum.BMS.getName());
        queryBean.setTypeNameQueryType(1);
        queryBean.setPage(1);
        queryBean.setSize(Integer.MAX_VALUE);
        ResponseResult<PagerDataBean<DicDto>> query = dicFeignService.query(queryBean);
        if (query != null && CollUtil.isNotEmpty(query.getResult().getRows())) {
            if ("2".equals(mapType)) {
                return query.getResult().getRows().stream()
                        .collect(Collectors.toMap(e->e.getTypename()+e.getItemname(), e->e.getValue().toString()));
            } else {
                return query.getResult().getRows().stream()
                        .collect(Collectors.toMap(e->e.getTypename()+e.getValue().toString(), PubIdreplacePO::getItemname));
            }
        }
        return Map.of();
    }


    /**
     * 根据类型获取数据字典
     *
     * @param type    类型 支持多个用逗号分割 例如: sys_oper_type,test_djj_1
     * @param mapType 返回map类型  传2返回Map<类型+名称，数字>,其他返回Map<类型+数字，名称>
     */
    public Map<String, DictUtilsBean> getDictByTypeForObj(String type, String mapType) {
        DictUtilsBean sysDict = new DictUtilsBean();
        sysDict.setDictType(type);
        List<DictUtilsBean> dictDataByName = dictDataMapper.getDictDataByName(RequestContext.getTenantId(), sysDict, null);
        if ("2".equals(mapType)) {
            return dictDataByName.stream().collect(Collectors.toMap(e -> e.getDictType() + e.getDictLabel(), e -> e));
        } else {
            return dictDataByName.stream().collect(Collectors.toMap(e -> e.getDictType() + e.getValue(), e -> e));
        }
    }

    /**
     * 根据类型获取数据字典
     *
     * @param type    类型 支持多个用逗号分割 例如: sys_oper_type,test_djj_1
     * @param mapType 返回map类型  传2返回Map<系统类型+类型+名称，数字>,其他返回Map<系统类型+类型+数字，名称>
     * @param sysType 系统类型，例如BMS,TMS,MDM
     */
    public Map<String, String> getDictByType(String type, String mapType, SysCodeEnum sysType) {

        DicQueryBean queryBean = new DicQueryBean();
        queryBean.setTypenames(type);
        queryBean.setSystype(sysType.getName());
        queryBean.setTypeNameQueryType(1);
        queryBean.setPage(1);
        queryBean.setSize(Integer.MAX_VALUE);
        ResponseResult<PagerDataBean<DicDto>> queryResult = dicFeignService.query(queryBean);

        if(queryResult!=null && queryResult.getResult()!=null && CollUtil.isNotEmpty(queryResult.getResult().getRows())) {
            List<DicDto> dictDataByName = queryResult.getResult().getRows();
            if ("2".equals(mapType)) {
                return dictDataByName.stream().collect(Collectors.toMap(e->e.getTypename()+e.getItemname(), e->e.getValue().toString()));
            } else {
                return dictDataByName.stream().collect(Collectors.toMap(e->e.getTypename()+e.getValue().toString(), PubIdreplacePO::getItemname));
            }
        }
        return Map.of();
    }


    /**
     * 根据类型获取数据字典
     *
     * @param type    类型 支持多个用逗号分割 例如: sys_oper_type,test_djj_1
     * @param mapType 返回map类型  传2返回Map<系统类型+类型+名称，数字>,其他返回Map<系统类型+类型+数字，名称>
     * @param sysType 系统类型，例如BMS,TMS,MDM
     */
    public Map<String, DictUtilsBean> getDictByTypeForObj(String type, String mapType, SysCodeEnum sysType) {
        DictUtilsBean sysDict = new DictUtilsBean();
        sysDict.setDictType(type);
        List<DictUtilsBean> dictDataByName = dictDataMapper.getDictDataByName(RequestContext.getTenantId(), sysDict, sysType.getValue());
        if ("2".equals(mapType)) {
            return dictDataByName.stream().collect(Collectors.toMap(e -> e.getSysType() + e.getDictType() + e.getDictLabel(), e -> e));
        } else if ("3".equals(mapType)) {
            return dictDataByName.stream().collect(Collectors.toMap(e -> e.getValue().toString(), e -> e));
        } else {
            return dictDataByName.stream().collect(Collectors.toMap(e -> e.getSysType() + e.getDictType() + e.getValue(), e -> e));
        }
    }


    /**
     * 根据类型获取数据字典
     *
     * @param type    类型 支持多个用逗号分割 例如: sys_oper_type,test_djj_1
     * @param mapType 返回map类型  传2返回Map<类型+名称，数字>,其他返回Map<类型+数字，名称>
     * @return key:code,value:name
     */
    public Map<String, String> getDictMapByType(String type, String mapType) {
        DictUtilsBean sysDict = new DictUtilsBean();
        sysDict.setDictType(type);
        List<DictUtilsBean> dictDataByName = dictDataMapper.getDictDataByName(RequestContext.getTenantId(), sysDict, null);
        if ("2".equals(mapType)) {
            return dictDataByName.stream().collect(Collectors.toMap(DictUtilsBean::getDictLabel, DictUtilsBean::getDictValue));
        } else {
            return dictDataByName.stream().collect(Collectors.toMap(DictUtilsBean::getDictValue, DictUtilsBean::getDictLabel));
        }
    }


    /**
     * 根据机构ids/名称查询机构信息
     *
     * @param type 1:ids 2:名称s
     */
    public Map<String, SysDept> getCompanyMap(String type, String idOrName) {
        ComPanyQueryBean comPanyQueryBean = new ComPanyQueryBean();
        comPanyQueryBean.setReturnTree(0);
        HashMap<String, SysDept> objectObjectHashMap = new HashMap<>();
        ResponseResult<List<CompanyDto>> rpcResult = companyFeignService.querySimple(comPanyQueryBean);
        if (rpcResult != null && rpcResult.getResult() != null) {

            List<SysDept> depts = new ArrayList<>();
            for (CompanyDto companyDto : rpcResult.getResult()) {
                SysDept sysDept = new SysDept();
                sysDept.setDeptId(companyDto.getId().longValue());
                sysDept.setDeptName(companyDto.getName());
                depts.add(sysDept);
            }

            if (Constant.C_ONE.equals(type)) {
                depts.forEach(c -> objectObjectHashMap.put(c.getDeptId().toString(), c));
            }
            if (Constant.C_TWO.equals(type)) {
                depts.forEach(c -> objectObjectHashMap.put(c.getDeptName(), c));
            }
        }
        return objectObjectHashMap;
    }

    /**
     * 根据机构ids/名称查询机构信息(无视当前用户权限)
     *
     * @param type 1:ids 2:名称s
     */
    public Map<String, SysDept> getCompanyMapRoot(String type, String idOrName) {
        ComPanyQueryBean comPanyQueryBean = new ComPanyQueryBean();
        comPanyQueryBean.setReturnTree(0);
        comPanyQueryBean.setIsAuthority(1);
        HashMap<String, SysDept> objectObjectHashMap = new HashMap<>();
        ResponseResult<List<CompanyDto>> rpcResult = companyFeignService.querySimple(comPanyQueryBean);
        if (rpcResult != null && rpcResult.getResult() != null) {

            List<SysDept> depts = new ArrayList<>();
            for (CompanyDto companyDto : rpcResult.getResult()) {
                SysDept sysDept = new SysDept();
                sysDept.setDeptId(companyDto.getId().longValue());
                sysDept.setDeptName(companyDto.getName());
                depts.add(sysDept);
            }

            if (Constant.C_ONE.equals(type)) {
                depts.forEach(c -> objectObjectHashMap.put(c.getDeptId().toString(), c));
            }
            if (Constant.C_TWO.equals(type)) {
                depts.forEach(c -> objectObjectHashMap.put(c.getDeptName(), c));
            }
        }
        return objectObjectHashMap;
    }



    /**
     * 根据仓库编码或id查询仓库信息
     */
    public Map<String, MdmWarehouseinfo> selectWarehouseMap(List<String> ids, List<String> codes, String name) {
        List<MdmWarehouseinfo> warehouseinfos = warehouseinfoMapper.selectWarehouseList(RequestContext.getTenantId(), ids, codes, name);
        Map<String, MdmWarehouseinfo> map = new HashMap<>(1);
        ;
        if (CollUtil.isNotEmpty(warehouseinfos) && CollUtil.isNotEmpty(codes)) {
            map = warehouseinfos.stream().collect(Collectors.toMap(MdmWarehouseinfo::getWarehouseCode, Function.identity()));
        }
        if (CollUtil.isNotEmpty(warehouseinfos) && (CollUtil.isNotEmpty(ids) || StrUtil.isNotEmpty(name))) {
            map = warehouseinfos.stream().collect(Collectors.toMap(e -> e.getId().toString(), Function.identity()));
        }
        return map;
    }

    /**
     * 根据仓库编码或id查询仓库信息
     */
    public Map<String, MdmWarehouseinfo> selectWarehouseMapByCodeNames(List<String> codes, List<String> names) {
        List<MdmWarehouseinfo> warehouseinfos = warehouseinfoMapper.selectWarehouseMapByCodeNames(RequestContext.getTenantId(), names, codes);
        Map<String, MdmWarehouseinfo> map = new HashMap<>(1);
        ;
        if (CollUtil.isNotEmpty(warehouseinfos) && CollUtil.isNotEmpty(codes)) {
            map = warehouseinfos.stream().collect(Collectors.toMap(MdmWarehouseinfo::getWarehouseCode, Function.identity()));
        }
        if (CollUtil.isNotEmpty(warehouseinfos) && CollUtil.isNotEmpty(names)) {
            map = warehouseinfos.stream().collect(Collectors.toMap(MdmWarehouseinfo::getWarehouseName, Function.identity()));
        }
        return map;
    }

    /**
     * 根据商品编码或id查询商品信息
     */
    public Map<String, MdmSkuinfo> selectSkuMap(List<String> ids, List<String> codes) {
        List<MdmSkuinfo> skuinfos = skuinfoMapper.selectSkuList(RequestContext.getTenantId(), ids, codes);
        Map<String, MdmSkuinfo> map = new HashMap<>(1);
        if (CollUtil.isNotEmpty(skuinfos) && CollUtil.isNotEmpty(codes)) {
            map = skuinfos.stream().collect(Collectors.toMap(MdmSkuinfo::getSkuCode, Function.identity()));
        }
        if (CollUtil.isNotEmpty(skuinfos) && CollUtil.isNotEmpty(ids)) {
            map = skuinfos.stream().collect(Collectors.toMap(e -> e.getId().toString(), Function.identity()));
        }
        return map;
    }

    /**
     * 获取当前用户机构含下级所有的机构id
     */
    public String getCompanyIds() {
        UserBean userVO = RequestContext.getUserInfo();
        ComPanyQueryBean comPanyQueryBean = new ComPanyQueryBean();
        comPanyQueryBean.setReturnTree(0);
        ResponseResult<List<CompanyDto>> rpcResult = companyFeignService.querySimple(comPanyQueryBean);
        if (rpcResult != null && rpcResult.getResult() != null) {
            return rpcResult.getResult().stream().map(CompanyDto::getId).map(String::valueOf).collect(Collectors.joining(",")) + "," + userVO.getDepartmenid();
        }
        return "";
    }

    public List<SysDept> recursion(List<SysDept> sysDept, List<SysDept> sysDepts) {
        List<SysDept> deptList = new ArrayList<>();
        for (SysDept dept : sysDept) {
            List<SysDept> collect = sysDepts.stream().filter(e -> dept.getDeptId().equals(e.getParentId())).collect(Collectors.toList());
            deptList.addAll(recursion(collect, sysDepts));
        }
        return deptList;
    }


    /**
     * 当前机构含下级 数组
     *
     * @return 机构含下级 数组
     */
    public String[] getCompanyIdsArr() {
        String companyIds = getCompanyIds();
        if (StrUtil.isNotEmpty(companyIds)) {
            return companyIds.trim().split(",");
        }
        return null;
    }


    /**
     * 当前机构含下级 数组
     *
     * @return 机构含下级 数组
     */
    public List<String> getCompanyIdsLis() {
        String companyIds = getCompanyIds();
        if (StrUtil.isNotEmpty(companyIds)) {
            return StrUtil.splitTrim(companyIds, ",");
        }
        return null;
    }



    /**
     * 根据商品编码或id查询商品信息
     */
    public List<PubQuotationClassificationdetail> getPubClass(PubQuoteruleDetail pubQuoteruleDetail) {
        List<PubQuotationClassificationdetail> listClass = new ArrayList<>();
        listClass = pubQuotationClassificationdetailMapper.getPubClass(RequestContext.getTenantId(), pubQuoteruleDetail);

        return listClass;
    }

    /**
     * 获取应收应付结算日期设置
     */
    public Map<Long, PubSettledateSetting> selectSettledateSettingMap(String billType) {
        List<PubSettledateSetting> data = pubSyslogicsetMapper.selectSettledateSetting(RequestContext.getTenantId(), billType);
        return CollUtil.isNotEmpty(data) ?
                data.stream().collect(Collectors.toMap(PubSettledateSetting::getClientId, g -> g)) :
                new HashMap<>(1);
    }


    /**
     * 根据id或编码或名称查询各类费用的费用项
     *
     * @param ids      费用项id
     * @param codes    费用项编码
     * @param names    费用项名称
     * @param level    层级 1级 2级 3级
     * @param showType 显示类型：1固定费2增值费3浮动计费
     */
    public Map<String, BmsExpenseItemInfo> selectPubFeeSubjectMap(List<String> ids, List<String> codes, List<String> names, String level, String showType) {

        MdmFeeSubjectBean queryBean = new MdmFeeSubjectBean();
        queryBean.setSysCode(SysCodeEnum.BMS.getValue());
        queryBean.setShowType((short) 2);
        if(CollUtil.isNotEmpty(ids)){
            List<Integer> idsFormat = ids.stream().map(Integer::valueOf).toList();
            queryBean.setSubjectIds(idsFormat);
        }
        if(CollUtil.isNotEmpty(codes)){
            queryBean.setItemCodes(codes);
        }
        queryBean.setLevel(Integer.valueOf(level));
        ResponseResult<List<MdmFeeSubjectVO>> listResponseResult = mdmFeeSubjectFeignService.queryList(queryBean);
        List<BmsExpenseItemInfo> pubFeeSubjects = new ArrayList<>();
        if(listResponseResult!=null && CollUtil.isNotEmpty(listResponseResult.getResult())){

            for (MdmFeeSubjectVO mdmFeeSubjectVO : listResponseResult.getResult()) {
                BmsExpenseItemInfo initData = new BmsExpenseItemInfo();
                initData.setId(mdmFeeSubjectVO.getId());
                initData.setItemCode(mdmFeeSubjectVO.getItemCode());
                initData.setItemName(mdmFeeSubjectVO.getItemName());
                initData.setLevel(mdmFeeSubjectVO.getLevel());
                initData.setShowType(mdmFeeSubjectVO.getShowType().intValue());
                initData.setBusinessType(mdmFeeSubjectVO.getBusinessType());
                initData.setAttrs(mdmFeeSubjectVO.getAttrs());
                pubFeeSubjects.add(initData);
            }
        }

        Map<String, BmsExpenseItemInfo> map = new HashMap<>(1);
        if (CollUtil.isNotEmpty(pubFeeSubjects) && CollUtil.isNotEmpty(ids)) {
            map = pubFeeSubjects.stream().collect(Collectors.toMap(k -> String.valueOf(k.getId().intValue()), Function.identity()));
        }
        if (CollUtil.isNotEmpty(pubFeeSubjects) && CollUtil.isNotEmpty(codes)) {
            map = pubFeeSubjects.stream().collect(Collectors.toMap(BmsExpenseItemInfo::getItemCode, Function.identity()));
        }
        if (CollUtil.isNotEmpty(pubFeeSubjects) && CollUtil.isNotEmpty(names)) {
            map = pubFeeSubjects.stream().collect(Collectors.toMap(BmsExpenseItemInfo::getItemName, Function.identity()));
        }
        return map;
    }

    /**
     * mdm字典数据查询
     *
     * @param resultType 1:typeName+value||2:typeName+itemName
     *                   return key: 1:sysCode+typeName+value||2:sysCode+typeName+itemName
     */
    public Map<String, DicDto> mdmDicMap(String typeNames, Integer resultType, SysCodeEnum... sysCodeEnum) {
        Map<String, DicDto> dicMap = new HashMap<>();
        if (StrUtil.isEmpty(typeNames) || sysCodeEnum == null) {
            return dicMap;
        }
        if (resultType == null) {
            resultType = 1;
        }
        DicQueryBean queryBean = new DicQueryBean();
        queryBean.setTypenames(typeNames);
        if (sysCodeEnum.length > 1) {
            queryBean.setSystypes(Stream.of(sysCodeEnum).map(SysCodeEnum::getValue).collect(Collectors.joining(",")));
        } else {
            queryBean.setSystype(sysCodeEnum[0].getValue());
        }
        queryBean.setTypeNameQueryType(1);
        queryBean.setPage(1);
        queryBean.setSize(Integer.MAX_VALUE);
        ResponseResult<PagerDataBean<DicDto>> listResponseResult = dicFeignService.query(queryBean);
        if (listResponseResult != null && CollUtil.isNotEmpty(listResponseResult.getResult().getRows())) {
            if (resultType == 1) {
                dicMap = listResponseResult.getResult().getRows().stream()
                        .collect(Collectors.toMap(k -> StrUtil.format("{}{}{}", k.getSystype(), k.getTypename(), k.getValue()), Function.identity(), (v1, v2) -> v1));
            } else {
                dicMap = listResponseResult.getResult().getRows().stream()
                        .collect(Collectors.toMap(k -> StrUtil.format("{}{}{}", k.getSystype(), k.getTypename(), k.getItemname()), Function.identity(), (v1, v2) -> v1));
            }
        }
        return dicMap;
    }

    /**
     * 系统级所有字典整合工具
     * resultType 1:typeName+value||2:typeName+itemName
     * return key: 1:sysCode+typeName+value||2:sysCode+typeName+itemName
     */
    public Map<String, DicDto> sysInitMdmDicMap() {

        Map<String, DicDto> dicMap = new HashMap<>();

        // 整合后的字典Map初始化（修正了方法调用）
        Map<String, DicDto> mdmDicMap = mdmDicMap("结算方式,运输类型,运输方式,车长,车型", 1, SysCodeEnum.PUB);
        Map<String, DicDto> omsDicMap = mdmDicMap("业务类型,运输方式,配送方式", 1, SysCodeEnum.OMS);
        Map<String, DicDto> tmsDicMap = mdmDicMap("内外调", 1, SysCodeEnum.TMS);
        Map<String, DicDto> wmsDicMap = mdmDicMap("好品坏品类型", 1, SysCodeEnum.WMS);
        Map<String, DicDto> bmsDicMap = mdmDicMap("订单来源,账单状态,计费状态,是否超时,是否自营,是否拒收,拒收责任方,是否基数外门店" +
                ",是否超基数公里,结算主体,结算方式设置,分摊方式,配送类型,单据类型,原始单据类型,出账是否到客户纬度,是否拆单" +
                ",应收费用单据类型,费用维度,费用标识,费用大类,费用属性,计费方式,温区类型", 1, SysCodeEnum.BMS);

        Map<String, DicDto> mdmDicMap2 = mdmDicMap("结算方式,运输类型,运输方式,车长,车型", 2, SysCodeEnum.PUB);
        Map<String, DicDto> omsDicMap2 = mdmDicMap("业务类型,运输方式,配送方式", 2, SysCodeEnum.OMS);
        Map<String, DicDto> tmsDicMap2 = mdmDicMap("内外调", 2, SysCodeEnum.TMS);
        Map<String, DicDto> wmsDicMap2 = mdmDicMap("好品坏品类型", 2, SysCodeEnum.WMS);
        Map<String, DicDto> bmsDicMap2 = mdmDicMap("订单来源,账单状态,计费状态,是否超时,是否自营,是否拒收,拒收责任方,是否基数外门店" +
                ",是否超基数公里,结算主体,结算方式设置,分摊方式,配送类型,单据类型,原始单据类型,出账是否到客户纬度,是否拆单" +
                ",应收费用单据类型,费用维度,费用标识,费用大类,费用属性,计费方式,温区类型", 2, SysCodeEnum.BMS);

        dicMap.putAll(mdmDicMap);
        dicMap.putAll(omsDicMap);
        dicMap.putAll(tmsDicMap);
        dicMap.putAll(wmsDicMap);
        dicMap.putAll(bmsDicMap);
        dicMap.putAll(mdmDicMap2);
        dicMap.putAll(omsDicMap2);
        dicMap.putAll(tmsDicMap2);
        dicMap.putAll(wmsDicMap2);
        dicMap.putAll(bmsDicMap2);
        return dicMap;
    }



    /**
     * 查询MDM单个字典数据信息
     *
     * @param typeName    字典选项名称
     * @param sysCodeEnum 系统类型
     * @return 对应字典信息
     */
    public List<DicDto> querySingleMdmDic(String typeName, SysCodeEnum sysCodeEnum) {
        DicQueryBean queryBean = new DicQueryBean();
        queryBean.setTypename(typeName);
        queryBean.setSystype(sysCodeEnum.getValue());
        queryBean.setTypeNameQueryType(1);
        queryBean.setPage(1);
        queryBean.setSize(Integer.MAX_VALUE);
        ResponseResult<PagerDataBean<DicDto>> listResponseResult = dicFeignService.query(queryBean);
        return listResponseResult.getResult().getRows();
    }


    /**
     * MDM公共费用字典数据查询
     * return key-1 value
     */
    public Map<Integer, MdmFeeSubjectVO> feeSubMap(List<Integer> subjectIds, SysCodeEnum sysCodeEnum) {
        Map<Integer, MdmFeeSubjectVO> feeSubjectMap = new HashMap<>();
        if (CollUtil.isEmpty(subjectIds)) {
            return feeSubjectMap;
        }
        MdmFeeSubjectBean queryBean = new MdmFeeSubjectBean();
        queryBean.setSysCode(sysCodeEnum.getValue());
        queryBean.setIsSelAttr(Boolean.TRUE);
        queryBean.setSubjectIds(subjectIds);
        ResponseResult<List<MdmFeeSubjectVO>> listResponseResult = mdmFeeSubjectFeignService.queryList(queryBean);
        if (listResponseResult.getResult() != null && CollUtil.isNotEmpty(listResponseResult.getResult())) {
            feeSubjectMap = listResponseResult.getResult().stream()
                    .collect(Collectors.toMap(MdmFeeSubjectVO::getId, Function.identity(), (v1, v2) -> v1));
        }
        return feeSubjectMap;
    }


    /**
     * 对接日志记录
     */
    public void interfaceLog(InterfaceRecvRecerdEnum recordEnum, String requestJson, String responseJson, String serviceCode) {
        int optMonth = Integer.parseInt(DateUtil.format(new Date(), "yyyyMM"));
        int optDay = Integer.parseInt(DateUtil.format(new Date(), "yyyyMMdd"));
        BmsInterfaceRecvRecerdLogPOWithBLOBs insertData = new BmsInterfaceRecvRecerdLogPOWithBLOBs();
        insertData.setUserCode(recordEnum.getUserCode());
        insertData.setServiceType(1);
        insertData.setServiceModule(recordEnum.getServiceModule());
        insertData.setInterfaceCode(recordEnum.getInterfaceCode());
        insertData.setInterfaceName(recordEnum.getInterfaceName());
        insertData.setServiceCode(serviceCode);
        insertData.setRequestJson(requestJson);
        insertData.setResponseJson(responseJson);
        insertData.setCreateTime(new Date());
        insertData.setOperTime(new Date());
        insertData.setOptMonth(optMonth);
        insertData.setOptDay(optDay);
        interfaceRecvRecerdLogService.insert(insertData);
    }


}
