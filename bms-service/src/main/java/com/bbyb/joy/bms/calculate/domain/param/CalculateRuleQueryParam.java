package com.bbyb.joy.bms.calculate.domain.param;

import com.bbyb.joy.bms.untis.ValidationTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class CalculateRuleQueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "关联ID客户||承运商不能为空" ,groups = {ValidationTypeEnum.SEL.class})
    @ApiModelProperty("关联ID客户||承运商")
    private List<Integer> relationIds;
    @NotEmpty(message = "明细报价id不能为空" ,groups = {ValidationTypeEnum.SEL2.class})
    @ApiModelProperty("明细报价id集合")
    private List<String> ruleIds;
    @ApiModelProperty("明细报价名称")
    private String ruleCode;
    @NotNull(message = "报价类型不能为空" ,groups = {ValidationTypeEnum.SEL.class})
    @ApiModelProperty("报价类型(1客户(应收),2承运商(应付))")
    private Integer ruleType;
    @ApiModelProperty(name = "开始时间")
    private String startDate;
    @ApiModelProperty(name = "结束时间")
    private String endDate;
    @ApiModelProperty(value = "单据类型:1运输单2出库单2入库单4库存单")
    private List<Integer> codeTypes;
    @NotEmpty(message = "业务类型不能为空" ,groups = {ValidationTypeEnum.SEL.class})
    @ApiModelProperty(value = "业务类型:1运输报价2仓储报价3仓配报价")
    private List<Integer> businessTypes;

}
