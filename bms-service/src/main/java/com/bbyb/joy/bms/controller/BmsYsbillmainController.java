package com.bbyb.joy.bms.controller;

import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.bill.service.BmsYsBillService;
import com.bbyb.joy.bms.domain.dto.BmsClaimsInfo;
import com.bbyb.joy.bms.domain.dto.BmsFixedExpenseInfo;
import com.bbyb.joy.bms.domain.dto.PubYsSignElectronicContractRequest;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.bill.*;
import com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsbillmainExport;
import com.bbyb.joy.bms.domain.dto.dto.*;
import com.bbyb.joy.bms.domain.dto.querybean.BmsFixedExpenseQueryBean;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.WarehouseInfo;
import com.bbyb.joy.bms.domain.dto.trends.BmsYsbillmainTreansModel;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsYsbillmainService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.exception.BusinessException;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.enums.PubNumEnum;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应收账单主表Controller
 *
 * <AUTHOR>
 */
@Api(tags = "应收账单主表接口")
@RestController
@RequestMapping("/system/ysbillmain")
@Slf4j
public class BmsYsbillmainController {

    @Resource
    BmsYsBillService billService;
    @Resource
    private IBmsYsbillmainService bmsYsbillmainService;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    UserRights userRights;


    /**
     * 查询是否锁账
     */
    @PostMapping("/accountLockingYn")
    public ResponseResult<Boolean> accountLockingYn() {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillmainService.accountLockingYn(token));
    }


    /**
     * 根据账单id 查询费用扩展信息 (调账)
     */
    @PostMapping("/getCostListBymainId")
    @MenuAuthority(code = "应收账单管理-调账")
    public ResponseResult<PagerDataBean<BmsYscostExtendDto>> getCostListBymainId(@RequestBody(required = false) BmsYscostExtendDto bmsYsbillmain) {
        String token = RequestContext.getToken();
        if (bmsYsbillmain.getBillId() == null) {
            return new ResponseResult<>(new PagerDataBean<>(new ArrayList<>(), 0, new PagerBean(1, 20)));
        }
        return bmsYsbillmainService.getCostListBymainId(token, bmsYsbillmain);
    }


    /**
     * 查询费用扩展信息 （对账）
     */
    @PostMapping("/getCostList")
    public ResponseResult<PagerDataBean<BmsYsbillmainTreansModel>> getCostList(@RequestBody(required = false) BmsYscostExtendDto bmsYsbillmain) {
        String token = RequestContext.getToken();
        List<BmsYsbillmainTreansModel> list = new ArrayList<>();
        SysDataPack sysDataPack = bmsYsbillmainService.getsysDataPack(token);
        List<ClientInfo> clientList = sysDataPack.getClientinfo();
        List<WarehouseInfo> warehouseInfos = sysDataPack.getWarehouseInfo();
        if (clientList.isEmpty() || warehouseInfos.isEmpty()) {
            return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(1, 20)));
        }
        bmsYsbillmain.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
        return bmsYsbillmainService.getCostList(token, bmsYsbillmain);
    }


    /**
     * 对账提交
     * map-feeType 对账费用类型 1:增值费、2:固定费 :其他费（涉及到运输对账）
     */
    @Log(title = "应收账单 对账", businessType = BusinessType.UPDATE)
    @PostMapping("/reconciliation")
    public ResponseResult<String> reconciliation(@RequestBody(required = false) Map<String, Object> map) {
        String token = RequestContext.getToken();
        ResponseResult<String> ajaxResult = new ResponseResult<>();
        if (!bmsYsbillmainService.accountLockingYn(token)) {
            return new ResponseResult<>(40005, "锁账后不能对账！");
        }
        try {
            String feeType = null != map.get("feeType") ? map.get("feeType").toString() : "";
            //对账-增值费
            if (StrUtil.equals(feeType, "1")) {
                String updateListStr = JSONObject.toJSONString(map.get("updateList"));
                List<BmsPubAddedfeeDto> optList = JSON.parseArray(updateListStr, BmsPubAddedfeeDto.class);
                BmsPubAddedfeeDto optData = new BmsPubAddedfeeDto();
                optData.setBillId(Integer.valueOf(map.get("billId").toString()));
                optData.setCommitType(map.get("commitType").toString());
                optData.setUpdateList(optList);
                ajaxResult = new ResponseResult<>(bmsYsbillmainService.reconciliationByAddedFee(token, optData));
                //对账-固定费
            } else if (StrUtil.equals(feeType, "2")) {
                String updateListStr = JSONObject.toJSONString(map.get("updateList"));
                List<BmsFixedExpenseInfo> optList = JSON.parseArray(updateListStr, BmsFixedExpenseInfo.class);
                BmsFixedExpenseQueryBean optData = new BmsFixedExpenseQueryBean();
                optData.setBillId(Integer.valueOf(map.get("billId").toString()));
                optData.setCommitType(map.get("commitType").toString());
                optData.setUpdateList(optList);
                ajaxResult = new ResponseResult<>(bmsYsbillmainService.reconciliationByFixedFee(token, optData));
            } else { //其他费涉及到运输对账
                String mainBeanStr = JSONObject.toJSONString(map.get("bmsYsbillmainBean"));
                List<BmsYsbillmainBean> bmsYsbillmainBean = JSONUtil.toList(mainBeanStr, BmsYsbillmainBean.class);
                ajaxResult = new ResponseResult<>(bmsYsbillmainService.reconciliation(token, bmsYsbillmainBean, map.get("billType").toString(), map.get("billId").toString(), map.get("commitType").toString()));
            }
        } catch (Exception e) {
            if(e instanceof BusinessException){
                throw new BusinessException(e.getMessage());
            }else if(e instanceof ValidateException){
                throw new BusinessException(e.getMessage());
            }else {
                log.info("对账失败:{}",e.getMessage());
                throw new BusinessException("对账失败!");
            }
        }
        return ajaxResult;
    }


    /**
     * 应收运输-对账获取运输单的修改方式
     * desc 1:单结算主体单结算方式,2:单结算主体多结算方式,3:多结算主体单结算方式,4:多结算主体多结算方式
     *
     * @param bean 费用单信息
     * @return 返回对应修改方式
     */
    @PostMapping("/reconciliationForSettleType")
    public ResponseResult<Integer> reconciliationForSettleType(@RequestBody(required = false) BmsYsbillmainBean bean) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillmainService.reconciliationForSettleType(token, bean));
    }


    @PostMapping("/reconciliation2")
    @MenuAuthority(code = "应收账单管理-对账")
    public ResponseResult<String> reconciliation2(@RequestBody(required = false) BmsYsReconciliationDto editData) {
        String token = RequestContext.getToken();
        billService.reconciliationHandler(token, editData);
        return new ResponseResult<>("操作成功");
    }


    /**
     * 应收账单 调账
     */
    @Log(title = "应收账单 调账", businessType = BusinessType.UPDATE)
    @PostMapping("/accountAdjustment")
    public ResponseResult<String> accountAdjustment(@RequestBody(required = false) Map<String, Object> map) {
        String token = RequestContext.getToken();
        if (!bmsYsbillmainService.accountLockingYn(token)) {
            return new ResponseResult<>(40005, "锁账后不能调账！");
        }
        String mainBeanStr = JSONObject.toJSONString(map.get("bmsYsbillmainBean"));
        List<BmsYsbillmainBean> bmsYsbillmainBean = JSON.parseArray(mainBeanStr, BmsYsbillmainBean.class);
        return new ResponseResult<>(bmsYsbillmainService.accountAdjustment(token, bmsYsbillmainBean, map.get("billType").toString(), map.get("billId").toString(), "2"));
    }

    /**
     * 根据账单id 查询费用明细
     */
    @PostMapping(value = "/queryDocument/{id}/{billType}/{codeType}")
    public ResponseResult<PagerDataBean<BmsYsbillmainBean>> queryDocument(@PathVariable("id") String id, @PathVariable("billType") Integer billType, @PathVariable("codeType") String codeType) {
        String token = RequestContext.getToken();
        BmsYsbillmain bmsYsbillmain = new BmsYsbillmain();
        bmsYsbillmain.setId(Long.valueOf(id));
        bmsYsbillmain.setBillType(billType);
        List<BmsYsbillmain> list = bmsYsbillmainService.selectBmsYsbillmainByFatherid(token, bmsYsbillmain.getId().toString()).getResult();
        List<String> codes = Arrays.asList(codeType.split(","));
        List<String> ids = new ArrayList<>();
        if (!list.isEmpty()) {
            for (BmsYsbillmain ysbillmain : list) {
                ids.add(ysbillmain.getId().toString());
            }
        } else {
            ids.add(bmsYsbillmain.getId().toString());
        }
        return bmsYsbillmainService.selectBillmainAndCostInfoList(token, bmsYsbillmain, codeType, list, ids, codes);
    }


    /**
     * 根据账单id 查询费用明细 汇总金额
     */
    @PostMapping(value = "/queryDocumentGetSumAmt/{id}/{billType}/{codeType}")
    public ResponseResult<BigDecimal> queryDocumentGetSumAmt(@PathVariable("id") String id, @PathVariable("billType") Integer billType, @PathVariable("codeType") String codeType) {
        String token = RequestContext.getToken();
        BmsYsbillmain bmsYsbillmain = new BmsYsbillmain();
        bmsYsbillmain.setId(Long.valueOf(id));
        bmsYsbillmain.setBillType(billType);
        List<BmsYsbillmain> list = bmsYsbillmainService.selectBmsYsbillmainByFatherid(token, bmsYsbillmain.getId().toString()).getResult();
        List<String> codes = Arrays.asList(codeType.split(","));
        List<String> ids = new ArrayList<>();
        if (!list.isEmpty()) {
            for (BmsYsbillmain ysbillmain : list) {
                ids.add(ysbillmain.getId().toString());
            }
        } else {
            ids.add(bmsYsbillmain.getId().toString());
        }
        return bmsYsbillmainService.selectBillmainAndCostInfoListGetSumAmt(token, bmsYsbillmain, codeType, list, ids, codes);
    }


    /**
     * 合并/取消合并
     */
    @Log(title = "修改应收单据主表", businessType = BusinessType.UPDATE)
    @PostMapping("/billConsolidation")
    @MenuAuthority(code = "应收账单管理-账单合并,应收账单管理-取消合并")
    public ResponseResult<String> billConsolidation2(@RequestBody BmsMerageOrCancelDto param) {
        String token = RequestContext.getToken();
        return bmsYsbillmainService.billConsolidation2(token, param);
    }


    /**
     * 提交、取消提交、审核、取消审核
     *
     * @param map 多选的账单信息
     */
    @Log(title = "修改应收单据主表", businessType = BusinessType.UPDATE)
    @PostMapping("/modifyStatus")
    @MenuAuthority(code = "应收账单管理-提交,应收账单管理-取消提交,应收账单管理-账单审核,应收账单管理-取消审核")
    public ResponseResult<String> modifyStatus(@RequestBody Map<String, Object> map) {
        String token = RequestContext.getToken();
        return bmsYsbillmainService.modifyStatus(token, map);
    }


    /***
     * 应收账单生成、批量生成应收账单
     */
    @Log(title = "应收单据主表", businessType = BusinessType.UPDATE)
    @PostMapping("/generateBill")
    public ResponseResult<String> generateBill(@RequestBody(required = false) Map<String, Object> map) {
        String token = RequestContext.getToken();
        if (!bmsYsbillmainService.accountLockingYn(token)) {
            return new ResponseResult<>(40005, "锁账后不能操作!");
        }
        return new ResponseResult<>(bmsYsbillmainService.generateBillBatch(map));
    }

    /**
     * 应收账单管理-作废账单
     */
    @Log(title = "应收账单主表", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateStatus")
    @MenuAuthority(code = "应收账单管理-作废账单")
    public ResponseResult<String> updateStatus(@RequestBody(required = false) Map<String, Object> idsAndStatus) {
        String token = RequestContext.getToken();
        return bmsYsbillmainService.updateBmsYsbillmainStatusByIds(token, idsAndStatus);
    }

    /**
     * 开票申请(提交)
     */
    @Log(title = "应收账单主表", businessType = BusinessType.UPDATE)
    @PostMapping("/apply")
    @MenuAuthority(code = "应收账单管理-开票申请")
    public ResponseResult<String> updateBmsTicketYsApply(@RequestBody(required = false) BmsYsTicketApplyDto ysTicketApplyDto) throws Exception {
        String token = RequestContext.getToken();
        return bmsYsbillmainService.updateBmsTicketYsApply2(token, ysTicketApplyDto);
    }

    /**
     * 开票申请 导入
     */
    @Log(title = "应收账单主表", businessType = BusinessType.UPDATE)
    @PostMapping("/importApply")
    public ResponseResult<String> importApply(@RequestBody(required = false) BmsYsinvoiceApplyInport bmsYsbillmain) throws Exception {
        String token = RequestContext.getToken();
        return bmsYsbillmainService.importApply(token, bmsYsbillmain);
    }

    /**
     * 取消开票申请
     */
    @Log(title = "取消开票申请", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateTicketApplyState")
    @MenuAuthority(code = "应收账单管理-取消开票申请")
    public ResponseResult<String> updateTicketApplyState(@RequestBody(required = false) Long[] ids) {
        String token = RequestContext.getToken();
        return bmsYsbillmainService.updateBmsTicketApplyStateByIds(token, ids);
    }


    /**
     * 查询应收账单主表列表
     */
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<BmsYsbillmainDto>> list(@RequestBody(required = false) BmsYsbillmain bmsYsbillmain) {
        String token = RequestContext.getToken();
        List<BmsYsbillmainDto> list = new ArrayList<>();

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
            new PermissionConfig(PermissionType.CLIENT.getCode(), "clientList", PermissionConfig.FieldType.LIST_STRING, "clientCode"),
            new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.LIST_STRING, "id")
        );
        bmsYsbillmain = userRights.applyPermissions(bmsYsbillmain, permissionConfigs);

        return bmsYsbillmainService.selectBmsYsbillmainList(token, bmsYsbillmain);
    }


    /**
     * 导出应收账单主表列表
     */
    @Log(title = "应收账单主表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @MenuAuthority(code = "应收账单管理-导出")
    public ResponseResult<String> export(@RequestBody(required = false) BmsYsbillmain bmsYsbillmain) {
        String token = RequestContext.getToken();
        SysDataPack sysDataPack = bmsYsbillmainService.getsysDataPack(token);
        List<ClientInfo> clientList = sysDataPack.getClientinfo();
        if (clientList.isEmpty()) {
            return new ResponseResult<>(40005, "你没有任何相关的客户权限！");
        }
        List<BmsYsbillmainExport> list = bmsYsbillmainService.selectBmsYsbillmainExportList(token, bmsYsbillmain).getResult();
        Map<String, List<BmsYsbillmainExport>> clienList = list.stream().collect(Collectors.groupingBy(BmsYsbillmainExport::getClientName));
        int i = 0;
        String cileName = "应收账单管理信息导出";
        for (String clien : clienList.keySet()) {
            i++;
        }
        if (PubNumEnum.one.getIntValue().equals(i)) {
            cileName = list.get(0).getClientName() + "_应收账单列表_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        }
        return exportUtil.getOutClassNew(token, cileName, "应收账单管理", BmsYsbillmainExport.class, userid -> {
            if (clientList.isEmpty()) {
                return null;
            }
            return list;
        });
    }

    /**
     * 获取应收账单子级账单信息
     */
    @PostMapping(value = "/getinfoByFatherId/{id}")
    public ResponseResult<List<BmsYsbillmain>> getInfoByFatherId(@PathVariable("id") String id) {
        String token = RequestContext.getToken();
        return bmsYsbillmainService.selectBmsYsbillmainByFatheridForCur(token, id);
    }


    /**
     * 获取应收账单子级账单信息
     */
    @PostMapping(value = "/getinfoByFatherCode/{billCode}")
    public ResponseResult<List<BmsYsbillmain>> getinfoByFatherCode(@PathVariable("billCode") String billCode) {
        String token = RequestContext.getToken();
        return bmsYsbillmainService.selectBmsYsbillmainByFatherCodeForCur(token, billCode);
    }


    /**
     * 获取应收账单主表详细信息
     */
    @PostMapping(value = "/{id}")
    public ResponseResult<BmsYsbillmainDto> getInfo(@PathVariable("id") String id) {
        String token = RequestContext.getToken();
        return bmsYsbillmainService.selectBmsYsbillmainById2(token, id);
    }


    /**
     * 获取应收账单主表详细信息
     */
    @PostMapping(value = "/getInfoByBillCode/{billCode}")
    public ResponseResult<BmsYsbillmainDto> getInfoByBillCode(@PathVariable("billCode") String billCode) {
        String token = RequestContext.getToken();
        return bmsYsbillmainService.selectBmsYsbillmainById3(token, billCode);
    }

    /**
     * 新增应收账单主表
     */
    @Log(title = "应收账单主表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public ResponseResult<Integer> add(@RequestBody(required = false) BmsYsbillmain bmsYsbillmain) {
        return new ResponseResult<>(bmsYsbillmainService.insertBmsYsbillmain(bmsYsbillmain));
    }

    /**
     * 修改应收账单主表
     */
    @Log(title = "应收账单主表", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Integer> edit(@RequestBody(required = false) BmsYsbillmain bmsYsbillmain) {
        UserBean userVO = RequestContext.getUserInfo();
        return new ResponseResult<>(bmsYsbillmainService.updateBmsYsbillmain(RequestContext.getTenantId(), bmsYsbillmain));
    }

    /**
     * 导入账单
     */
    @Log(title = "导入账单", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/importBill", produces = "application/json;charset=UTF-8")
    public ResponseResult<String> importBill(@RequestBody(required = false) List<BmsYsbillmainImportBean> list) {
        //获取token
        String token = RequestContext.getToken();
        return bmsYsbillmainService.importBill(token, list);
    }


    /**
     * 查询账单的开票申请金额
     */
    @Log(title = "查询账单的开票申请金额", businessType = BusinessType.UPDATE)
    @PostMapping("/queryInvoicingAmount")
    public ResponseResult<BillInvoicingAmountDto> queryInvoicingAmount(@RequestBody(required = false) BillInvoicingAmountBean bean) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillmainService.queryInvoicingAmount(token, bean));
    }


    /**
     * 开票申请审核
     */
    @Log(title = "开票申请审核", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/invoiceApplyAudit", produces = "application/json;charset=UTF-8")
    @MenuAuthority(code = "应收账单管理-开票申请审批")
    public ResponseResult<String> invoiceApplyAudit(@RequestBody(required = false) BmsYsinvoiceApply bean) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillmainService.invoiceApplyAudit(token, bean));
    }

    /**
     * 理赔对账提交
     * map-feeType 对账费用类型 1:增值费、2:固定费 :其他费（涉及到运输对账）
     */
    @Log(title = "理赔应收账单 对账", businessType = BusinessType.UPDATE)
    @PostMapping("/claimsReconciliation")
    public ResponseResult<String> claimsReconciliation(@RequestBody(required = false) Map<String,Object> map) {
        String token = RequestContext.getToken();
        ResponseResult<String> ajaxResult = new ResponseResult<>();
        if (!bmsYsbillmainService.accountLockingYn(token)) {
            return new ResponseResult<>(40005, "锁账后不能对账！");
        }
        String bmsYsbillClaims = JSONObject.toJSONString(map.get("bmsYsbillClaimsList"));
        List<BmsClaimsInfo> bmsYsbillClaimsList = JSON.parseArray(bmsYsbillClaims, BmsClaimsInfo.class);
        //commitType 1 保存  2保存并提交
        ajaxResult = new ResponseResult<>(bmsYsbillmainService.claimsReconciliation(token, bmsYsbillClaimsList, map.get("billId").toString(), map.get("commitType").toString()));
        return ajaxResult;
    }

    /**
     * 签署电子合同
     *
     * @param contractRequest 合同请求
     * @return {@link ResponseResult}
     */
    @PostMapping("/electronic/contract/sign")
    public ResponseResult<String> signStamp(@RequestBody PubYsSignElectronicContractRequest contractRequest) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillmainService.signStamp(token, contractRequest));
    }

    /**
     * 签署回调接口
     *
     * @param contractRequest 合同请求
     * @return {@link ResponseResult}
     */
    @PostMapping("/sign/call/back/{tenantId}")
    public ResponseResult<Boolean> signCallBack(@PathVariable("tenantId") String tenantId, @RequestBody PubYsSignElectronicContractRequest contractRequest) {
        return new ResponseResult<>(bmsYsbillmainService.signCallBack(tenantId, contractRequest));
    }

    /**
     * 查询内部印章
     *
     * @param contractRequest 合同请求
     * @return {@link ResponseResult}
     */
    @PostMapping("/internal/seal/query")
    public ResponseResult<JSONArray> queryInternalSeal(@RequestBody PubYsSignElectronicContractRequest contractRequest) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillmainService.queryInternalSeal(token, contractRequest));
    }

    /**
     * 查询签署文件下载链接
     *
     * @param contractRequest 合同请求
     * @return {@link ResponseResult}
     */
    @PostMapping("/electronic/link/query")
    public ResponseResult<String> querySignFileDownloadLink(HttpServletRequest request, @RequestBody PubYsSignElectronicContractRequest contractRequest) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillmainService.querySignFileDownloadLink(token, contractRequest.getOrderId()));
    }


}
