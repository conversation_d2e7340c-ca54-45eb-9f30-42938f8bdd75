package com.bbyb.joy.bms.controller;

import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.domain.dto.AutomaticBilling;
import com.bbyb.joy.bms.domain.dto.BillingParametersDto;
import com.bbyb.joy.bms.domain.dto.dto.BMSAutomaticLogImplementDto;
import com.bbyb.joy.bms.domain.enums.AutoStateEnum;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsAutoMaticService;
import com.bbyb.joy.bms.service.IYFAutomaticBilling;
import com.bbyb.joy.bms.service.IYSAutomaticBilling;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

/**
 * 自动计费Controller
 */
@Api(tags = "自动计费接口")
@RestController
@RequestMapping("/system/autoMatic")
public class AutoMaticController {

    @Resource
    TextConversionUtil textConversionUtil;
    @Qualifier("YSAutomaticBilling")
    @Resource
    private IYSAutomaticBilling ysAutomaticBilling;
    @Qualifier("YFAutomaticBilling")
    @Resource
    private IYFAutomaticBilling yfAutomaticBilling;
    @Resource
    private IBmsAutoMaticService bmsAutoMaticService;

    //验证是否可以操作
    private boolean verification(String module) {
        BMSAutomaticLogImplementDto dto = bmsAutoMaticService.selectAutomaticLogImplement(RequestContext.getToken(), module);
        //验证不通过
        return dto == null || dto.getAutossate() == null || !dto.getAutossate().equals(AutoStateEnum.EXECETING.getIntValue());
    }


    @Log(title = "应收运输自动计费(带时间条件)", businessType = BusinessType.UPDATE)
    @PostMapping("/YSfreightCharging")
    @MenuAuthority(code = "应收运输计费-自动计费")
    public ResponseResult<String> YsFreightCharging(HttpServletRequest request, @RequestBody BillingParametersDto automaticBilling) {
        String token = request.getHeader("Authorization");
        if (StrUtil.isEmpty(token)) {
            token = request.getParameter("token");
        }
        //验证
        if (!verification("应收运输自动计费")) {
            return new ResponseResult<>(40005, "上次服务未执行完成,请等待！");
        }
        //根据token查询用户信息
        //UserBean loginUserInfo = textConversionUtil.selectLoginUserInfo(token);
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        //新增
        String userName = loginUserInfo.getEmployeename();
        BMSAutomaticLogImplementDto dto = bmsAutoMaticService.insertAutomaticLogImplement(token, userName, "应收运输自动计费");
        if (dto == null) {
            return new ResponseResult<>(40005, "新增计划失败！");
        }
        //执行
        String finalToken = token;
        CompletableFuture<String> completableFuture = ysAutomaticBilling.freightCharging(finalToken, null, automaticBilling);
        completableFuture.thenApplyAsync(result -> modifyAutoMatic(dto, result, finalToken));
        return new ResponseResult<>("调用成功,请等待服务执行完成,再查询计费结果！");
    }


    //@PreAuthorize("@ss.hasPermi('system:autoMatic:YSoutboundOrderCharging')")
    @Log(title = "应收仓储自动计费(带时间条件)", businessType = BusinessType.UPDATE)
    @PostMapping("/YSoutboundOrderCharging")
    @MenuAuthority(code = "应收仓储计费-自动计费")
    public ResponseResult<String> YsOutboundOrderCharging(HttpServletRequest request, @RequestBody BillingParametersDto automaticBilling) {
        String token = request.getHeader("Authorization");
        if (StrUtil.isEmpty(token)) {
            token = request.getParameter("token");
        }
        //验证
        if (!verification("应收仓储自动计费")) {
            return new ResponseResult<>(40005, "上次服务未执行完成,请等待！");
        }
        //根据token查询用户信息
        //UserBean loginUserInfo = textConversionUtil.selectLoginUserInfo(token);
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        //新增
        String userName = loginUserInfo.getEmployeename();
        BMSAutomaticLogImplementDto dto = bmsAutoMaticService.insertAutomaticLogImplement(token, userName, "应收仓储自动计费");
        if (dto == null) {
            return new ResponseResult<>(40005, "新增计划失败！");
        }
        //执行
        String finalToken = token;
        CompletableFuture<String> completableFuture;

        CompletableFuture<String> completableFutureOut;
        CompletableFuture<String> completableFutureIn;
        CompletableFuture<String> completableFutureStock;

        if (automaticBilling.getCodeType() != null && "2".equals(automaticBilling.getCodeType())) {
            completableFuture = ysAutomaticBilling.outboundOrderCharging(finalToken, null, automaticBilling);
            completableFuture.thenApplyAsync(result -> modifyAutoMatic(dto, result, finalToken));
        } else if (automaticBilling.getCodeType() != null && "3".equals(automaticBilling.getCodeType())) {
            completableFuture = ysAutomaticBilling.warehousingOrderCharging(finalToken, null, automaticBilling);
            completableFuture.thenApplyAsync(result -> modifyAutoMatic(dto, result, finalToken));
        } else if (automaticBilling.getCodeType() != null && "4".equals(automaticBilling.getCodeType())) {
            completableFuture = ysAutomaticBilling.stockCharging(finalToken, null, automaticBilling);
            completableFuture.thenApplyAsync(result -> modifyAutoMatic(dto, result, finalToken));
        } else {
            completableFutureOut = ysAutomaticBilling.outboundOrderCharging(finalToken, null, automaticBilling);
            completableFutureIn = ysAutomaticBilling.warehousingOrderCharging(finalToken, null, automaticBilling);
            completableFutureStock = ysAutomaticBilling.stockCharging(finalToken, null, automaticBilling);
            CompletableFuture<Void> allOf = CompletableFuture.allOf(completableFutureOut, completableFutureIn, completableFutureStock);
            allOf.thenRun(() -> modifyAutoMatic(dto, null, finalToken));
        }
        return new ResponseResult<>("调用成功,请等待服务执行完成,再查询计费结果！");
    }


    @Log(title = "应付运输自动计费(带时间参数)", businessType = BusinessType.UPDATE)
    @PostMapping("/YFfreightCharging")
    @MenuAuthority(code = "应付运输计费-自动计费")
    public ResponseResult<String> YfFreightCharging(HttpServletRequest request, @RequestBody(required = false) BillingParametersDto automaticBilling) {
        String token = request.getHeader("Authorization");
        if (StrUtil.isEmpty(token)) {
            token = request.getParameter("token");
        }
        //验证
        if (!verification("应付运输自动计费")) {
            return new ResponseResult<>(40005, "上次服务未执行完成,请等待！");
        }
        //根据token查询用户信息
        //UserBean loginUserInfo = textConversionUtil.selectLoginUserInfo(token);
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        //新增
        String userName = loginUserInfo.getEmployeename();
        BMSAutomaticLogImplementDto dto = bmsAutoMaticService.insertAutomaticLogImplement(token, userName, "应付运输自动计费");
        if (dto == null) {
            return new ResponseResult<>(40005, "新增计划失败！");
        }
        //执行
        String finalToken = token;
        CompletableFuture<String> completableFuture = yfAutomaticBilling.freightCharging(finalToken, null, automaticBilling);
        //修改
        completableFuture.thenApplyAsync(result -> modifyAutoMatic(dto, result, finalToken));
        return new ResponseResult<>("调用成功,请等待服务执行完成,再查询计费结果！");
    }


    /**
     * 自动计费
     */
    //@PreAuthorize("@ss.hasPermi('system:autoMatic:YFoutboundOrderCharging')")
    @Log(title = "应付仓储自动计费(带时间参数)", businessType = BusinessType.UPDATE)
    @PostMapping("/YFoutboundOrderCharging")
    @MenuAuthority(code = "应付仓储计费-自动计费")
    public ResponseResult<String> YfOutboundOrderCharging(HttpServletRequest request, @RequestBody(required = false) BillingParametersDto automaticBilling) {
        String token = request.getHeader("Authorization");
        if (StrUtil.isEmpty(token)) {
            token = request.getParameter("token");
        }
        //验证
        if (!verification("应付仓储自动计费")) {
            return new ResponseResult<>(40005, "上次服务未执行完成,请等待！");
        }
        //根据token查询用户信息
        //UserBean loginUserInfo = textConversionUtil.selectLoginUserInfo(token);
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        //新增
        String userName = loginUserInfo.getEmployeename();
        BMSAutomaticLogImplementDto dto = bmsAutoMaticService.insertAutomaticLogImplement(token, userName, "应付仓储自动计费");
        if (dto == null) {
            return new ResponseResult<>(40005, "新增计划失败！");
        }
        //FIXME 2023-09-19 这里要注意前端传的codeType字典值 1:出库单,2:入库单,3:库存单;所以需要做一次转换
        if (automaticBilling != null && automaticBilling.getCodeType() != null) {
            switch (automaticBilling.getCodeType()) {
                case "1":
                    automaticBilling.setCodeType(BmsConstants.YF_OPT_TYPE_02_STR);
                    break;
                case "2":
                    automaticBilling.setCodeType(BmsConstants.YF_OPT_TYPE_03_STR);
                    break;
                case "3":
                    automaticBilling.setCodeType(BmsConstants.YF_OPT_TYPE_04_STR);
                    break;
                default:
            }
        }
        if (automaticBilling == null) {
            return new ResponseResult<>("调用成功,请等待服务执行完成,再查询计费结果！");
        }
        //执行
        String finalToken = token;
        CompletableFuture<String> completableFuture;
        CompletableFuture<String> completableFutureOut;
        CompletableFuture<String> completableFutureIn;
        CompletableFuture<String> completableFutureStock;
        if (automaticBilling.getCodeType() != null && "2".equals(automaticBilling.getCodeType())) {
            completableFuture = yfAutomaticBilling.outboundOrderCharging(finalToken, null, automaticBilling);
            completableFuture.thenApplyAsync(result -> modifyAutoMatic(dto, result, finalToken));
        } else if (automaticBilling.getCodeType() != null && "3".equals(automaticBilling.getCodeType())) {
            completableFuture = yfAutomaticBilling.warehousingOrderCharging(finalToken, null, automaticBilling);
            completableFuture.thenApplyAsync(result -> modifyAutoMatic(dto, result, finalToken));
        } else if (automaticBilling.getCodeType() != null && "4".equals(automaticBilling.getCodeType())) {
            completableFuture = yfAutomaticBilling.stockCharging(finalToken, null, automaticBilling);
            completableFuture.thenApplyAsync(result -> modifyAutoMatic(dto, result, finalToken));
        } else {
            completableFutureOut = yfAutomaticBilling.outboundOrderCharging(finalToken, null, automaticBilling);
            completableFutureIn = yfAutomaticBilling.warehousingOrderCharging(finalToken, null, automaticBilling);
            completableFutureStock = yfAutomaticBilling.stockCharging(finalToken, null, automaticBilling);
            CompletableFuture<Void> allOf = CompletableFuture.allOf(completableFutureOut, completableFutureIn, completableFutureStock);
            allOf.thenRun(() -> modifyAutoMatic(dto, null, finalToken));
        }
        return new ResponseResult<>("调用成功,请等待服务执行完成,再查询计费结果！");
    }

    private String modifyAutoMatic(BMSAutomaticLogImplementDto dto, String res, String finalToken) {
        //修改
        BMSAutomaticLogImplementDto upd = new BMSAutomaticLogImplementDto();
        upd.setGid(dto.getGid());
        upd.setAutossate(2);
        upd.setResultrek(res);
        upd.setEndtime(new Date());
        bmsAutoMaticService.updateAutomaticLogImplement(finalToken, upd);
        return res;
    }

    /**
     * 自动计费
     */
    //@PreAuthorize("@ss.hasPermi('system:autoMatic:YSvalueAddedFee')")
    @Log(title = "应收增值费自动计费", businessType = BusinessType.UPDATE)
    @PostMapping("/YSvalueAddedFee")
    public ResponseResult<String> YsValueAddedFee(HttpServletRequest request, @RequestBody(required = false) AutomaticBilling automaticBilling) {
        String token = request.getHeader("Authorization");
        if (StrUtil.isEmpty(token)) {
            token = request.getParameter("token");
        }
        //验证
        if (!verification("应收仓储自动计费")) {
            return new ResponseResult<>(40005, "上次服务未执行完成,请等待！");
        }
        //根据token查询用户信息
        //UserBean loginUserInfo = textConversionUtil.selectLoginUserInfo(token);
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        //新增
        String userName = loginUserInfo.getEmployeename();
        BMSAutomaticLogImplementDto dto = bmsAutoMaticService.insertAutomaticLogImplement(token, userName, "应收仓储自动计费");
        if (dto == null) {
            return new ResponseResult<>(40005, "新增计划失败！");
        }
        //执行
        String finalToken = token;
//        // 目前只开发了库存的已计费，再次计费
//        CompletableFuture<String> completableFuture = ysAutomaticBilling.ysAddedServiceOrder(finalToken, null, automaticBilling.getStartDate(),
//                automaticBilling.getEndDate());
//        //修改
//        completableFuture.thenApplyAsync(result -> modifyAutoMatic(dto, result, finalToken));
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                String res = ysAutomaticBilling.ysAddedServiceOrder(finalToken, null, automaticBilling.getStartDate(),automaticBilling.getEndDate());
                modifyAutoMatic(dto, res, finalToken);
            }
        });
        thread.start();
        return new ResponseResult<>("调用成功,请等待服务执行完成,再查询计费结果！");
    }

    //@PreAuthorize("@ss.hasPermi('system:autoMatic:YFvalueAddedFee')")
    @Log(title = "应付增值费自动计费", businessType = BusinessType.UPDATE)
    @PostMapping("/YFvalueAddedFee")
    @MenuAuthority(code = "应付增值费管理-自动计费")
    public ResponseResult<String> YfValueAddedFee(HttpServletRequest request, @RequestBody(required = false) AutomaticBilling automaticBilling) {
        String token = request.getHeader("Authorization");
        if (StrUtil.isEmpty(token)) {
            token = request.getParameter("token");
        }
        //验证
        if (!verification("应付仓储自动计费")) {
            return new ResponseResult<>(40005, "上次服务未执行完成,请等待！");
        }
        //根据token查询用户信息
        //UserBean loginUserInfo = textConversionUtil.selectLoginUserInfo(token);
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        //新增
        String userName = loginUserInfo.getEmployeename();
        BMSAutomaticLogImplementDto dto = bmsAutoMaticService.insertAutomaticLogImplement(token, userName, "应收仓储自动计费");
        if (dto == null) {
            return new ResponseResult<>(40005, "新增计划失败！");
        }
        //执行
        String finalToken = token;
        // 目前只开发了库存的已计费，再次计费
//        CompletableFuture<String> completableFuture = yfAutomaticBilling.yfAddedServiceOrder(finalToken, null, automaticBilling.getStartDate(),
//                automaticBilling.getEndDate());
//        completableFuture.thenApplyAsync(result -> modifyAutoMatic(dto, result, finalToken));
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                String res = yfAutomaticBilling.yfAddedServiceOrder(finalToken, null, automaticBilling.getStartDate(),automaticBilling.getEndDate());
                modifyAutoMatic(dto, res, finalToken);
            }
        });
        thread.start();
        return new ResponseResult<>("调用成功,请等待服务执行完成,再查询计费结果！");
    }


//    //验证是否可以操作
//    private boolean verificationByEstimate(String module){
//        BmsEstimateCostInfo dto = bmsAutoMaticService.selectAutomaticLogImplementByEstimate(module);
//        if(dto==null||(dto!=null && dto.getEstimateBillingState()!=null && dto.getEstimateBillingState().equals(BmsConstants.ESTIMATE_BILLING_STATE_1))){
//            //验证不通过
//            return false;
//        }
//        return true;
//    }
//
//
//    @Log(title = "应收预估自动计费", businessType = BusinessType.UPDATE)
//    @PostMapping("/estimateYSAutoAutoMatic")
//    public AjaxResult estimateYSAutoAutoMatic(@RequestBody BillingParametersDto automaticBilling){
//        if(StrUtil.isEmpty(automaticBilling.getStartDate())
//            || StrUtil.isEmpty(automaticBilling.getEndDate())){
//            return AjaxResult.error("参数不全!");
//        }
//
//
//        //验证
//        if(!verificationByEstimate(BmsConstants.ESTIMATE_BILLING_MOUDLE_YS_MONTH)){
//            return AjaxResult.error("上次服务未执行完成,请等待！");
//        }
//
//        //预估状态更新为操作中
//        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
//        //参数校验
//        boolean aBoolean = updateEstimateState(automaticBilling.getStartDate()
//                , automaticBilling.getEndDate()
//                , BmsConstants.ESTIMATE_BILLING_MOUDLE_YS_MONTH
//                , Integer.valueOf(BmsConstants.ESTIMATE_BILLING_STATE_1)
//                , user);
//        if(!aBoolean){
//            return AjaxResult.error("预估计费失败！");
//        }
//
//        //执行
//        Thread thread = new Thread(new Runnable() {
//            @Override
//            public void run() {
//                //清空预估费用数据
//                ysAutoCostEstimate.truncateDate();;
//
//                //开始计费
//                ysAutoCostEstimate.estimateFreightCharging(null,automaticBilling,automaticBilling.getBillDate());
//                ysAutoCostEstimate.estimateOutboundOrderCharging(null,automaticBilling, automaticBilling.getBillDate());
//                ysAutoCostEstimate.estimateInBoundCharging(null,automaticBilling,automaticBilling.getBillDate());
//                ysAutoCostEstimate.estimateStockCharging(null,automaticBilling,automaticBilling.getBillDate());
//
//                //预估状态更新为已完成
//                updateEstimateState(automaticBilling.getStartDate()
//                        , automaticBilling.getEndDate()
//                        , BmsConstants.ESTIMATE_BILLING_MOUDLE_YS_MONTH
//                        , Integer.valueOf(BmsConstants.ESTIMATE_BILLING_STATE_0)
//                        , user);
//            }
//        });
//        thread.start();
//        return AjaxResult.success("调用成功,请等待服务执行完成,再查询计费结果！");
//    }
//
//
//    /**
//     * 更新记录状态
//     * @param moudle
//     * @param billingState
//     * @param user
//     */
//    public boolean updateEstimateState(String startDate,String endDate,String moudle,Integer billingState,SysUser user){
//        boolean returnData=false;
//        //TODO 1:更新预估状态表
//        //TODO 1:更新预估计费记录表
//        BmsEstimateCostInfo beginParam = new BmsEstimateCostInfo();
//        beginParam.setBillingMoudle(moudle);
//        beginParam.setEstimateBillingState(billingState.toString());
//        beginParam.setBillingStartTime(DateUtil.parse(startDate, RegexUtil.NORM_DATETIME_PATTERN));
//        beginParam.setBillingEndTime(DateUtil.parse(endDate,RegexUtil.NORM_DATETIME_PATTERN));
//        beginParam.setOperCode(user.getEmployeename());
//        beginParam.setOperBy(user.getUserName());
//        beginParam.setOperTime(DateUtil.now());
//        returnData = bmsAutoMaticService.updateAutomaticLogImplementByEstimate(beginParam);
//
//        BmsEstimateCostRecordInfo costRecordInfoParam = new BmsEstimateCostRecordInfo();
//        costRecordInfoParam.setEstimateBillingState(String.valueOf(billingState));
//        costRecordInfoParam.setBillingStartTime(DateUtil.parse(startDate, RegexUtil.NORM_DATETIME_PATTERN));
//        costRecordInfoParam.setBillingEndTime(DateUtil.parse(endDate,RegexUtil.NORM_DATETIME_PATTERN));
//        costRecordInfoParam.setBillingMoudle(moudle);
//        costRecordInfoParam.setCreateCode(user.getEmployeename());
//        costRecordInfoParam.setCreateBy(user.getUserName());
//        costRecordInfoParam.setCreateTime(new Date());
//        costRecordInfoParam.setOperCode(user.getEmployeename());
//        costRecordInfoParam.setOperBy(user.getUserName());
//        costRecordInfoParam.setOperTime(new Date());
//        bmsAutoMaticService.insertEstimateRecordImplementByEstimate(costRecordInfoParam);
//        return returnData;
//    }
//
//
//
//    @Log(title = "应收预估自动计费", businessType = BusinessType.UPDATE)
//    @PostMapping("/getYSEstimateLast")
//    public AjaxResult getYSEstimateLast(@RequestBody BmsEstimateCostInfo data){
//        if(data==null||StrUtil.isEmpty(data.getBillingMoudle())){
//            return AjaxResult.error("参数不全!");
//        }
//        BmsEstimateCostInfo bmsEstimateCostInfo = ysAutoCostEstimate.selCurCostInfo(data.getBillingMoudle());
//        return AjaxResult.success(bmsEstimateCostInfo);
//    }


}
