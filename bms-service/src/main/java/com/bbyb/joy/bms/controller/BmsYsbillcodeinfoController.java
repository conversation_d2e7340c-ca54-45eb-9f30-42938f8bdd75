package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.BmsYsbillcodeDetailinfo;
import com.bbyb.joy.bms.domain.dto.MdmWarehouseinfo;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.charginglogic.OrderBillingBean;
import com.bbyb.joy.bms.domain.dto.dto.*;
import com.bbyb.joy.bms.domain.dto.querybean.BmsYsbillcodeinfoBean;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.WarehouseInfo;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsYsbillcodeDetailinfoService;
import com.bbyb.joy.bms.service.IBmsYsbillcodeinfoService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应收单据信息主Controller
 */
@Api(tags = "应收运输计费主接口")
@RestController
@RequestMapping("/system/ysbillcodeinfo")
public class BmsYsbillcodeinfoController {

    @Resource
    private IBmsYsbillcodeinfoService bmsYsbillcodeinfoService;
    @Resource
    private IBmsYsbillcodeDetailinfoService bmsYsbillcodeDetailinfoService;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private UserRights userRights;
    @Resource
    private ExportUtil exportUtil;


    /**
     * 订单计费导入订单
     */
    @ApiOperation(value = "订单计费导入", response = MultipartFile.class)
    @PostMapping("/importOrder")
    @MenuAuthority(code = "应收运输计费-导入订单")
    public ResponseResult<String> importOrder(@RequestBody MultipartFile file) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeinfoService.importOrder(token, file, "1"));
    }

    /**
     * 订单计费分页查询
     */
    @ApiOperation(value = "订单计费分页查询", response = BmsYsbillcodeDto.class)
    @PostMapping("/selectOrderBill")
    @MenuAuthority(code = "应收运输计费,应收运输计费-批量查询,应收运输计费-费用账单")
    public ResponseResult<PagerDataBean<BmsYsbillcodeDto>> selectOrderBill(@RequestBody BmsYsbillcodeinfoBean bean) {
        String token = RequestContext.getToken();
        List<BmsYsbillcodeDto> list = new ArrayList<>();
        // 根据仓库名称查询仓库编码
        if (StrUtil.isNotEmpty(bean.getWarehouseName())) {
            Map<String, MdmWarehouseinfo> warehouseMap = textConversionUtil.selectWarehouseMap(null, null, bean.getWarehouseName());
            if (warehouseMap.isEmpty()) {
                return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(bean.getPage(), bean.getSize())));
            } else {
                bean.setWarehouseCode(warehouseMap.values().stream().map(MdmWarehouseinfo::getWarehouseCode).collect(Collectors.joining(",")));
            }
        }
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        //加权限
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client,warehouse");
        List<ClientInfo> clientInfos = sysDataPack.getClientinfo();
        bean.setClientId(clientInfos.stream().map(k -> k.getId().toString()).collect(Collectors.joining(",")));
        bean.setClientCode(clientInfos.stream().map(ClientInfo::getClientCode).collect(Collectors.joining(",")));
        if (StrUtil.isEmpty(bean.getClientCode())) {
            return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(bean.getPage(), bean.getSize())));
        }
        return new ResponseResult<>(bmsYsbillcodeinfoService.selectOrderBill(token, bean));
    }


    @ApiOperation(value = "订单计费分页查询", response = BmsYsbillcodeDto.class)
    @PostMapping("/billed/selectOrderBill")
    @MenuAuthority(code = "应收运输计费,应收运输计费-批量查询,应收运输计费-费用账单")
    public ResponseResult<PagerDataBean<BmsYsbillcodeDto>> selectOrderBilled(@RequestBody BmsYsbillcodeinfoBean bean) {
        String token = RequestContext.getToken();
        List<BmsYsbillcodeDto> list = new ArrayList<>();
        // 根据仓库名称查询仓库编码
        if (StrUtil.isNotEmpty(bean.getWarehouseName())) {
            Map<String, MdmWarehouseinfo> warehouseMap = textConversionUtil.selectWarehouseMap(null, null, bean.getWarehouseName());
            if (warehouseMap.isEmpty()) {
                return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(bean.getPage(), bean.getSize())));
            } else {
                bean.setWarehouseCode(warehouseMap.values().stream().map(MdmWarehouseinfo::getWarehouseCode).collect(Collectors.joining(",")));
            }
        }
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        //加权限
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client,warehouse");
        List<ClientInfo> clientInfos = sysDataPack.getClientinfo();
        bean.setClientId(clientInfos.stream().map(k -> k.getId().toString()).collect(Collectors.joining(",")));
        bean.setClientCode(clientInfos.stream().map(ClientInfo::getClientCode).collect(Collectors.joining(",")));
        if (StrUtil.isEmpty(bean.getClientCode())) {
            return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(bean.getPage(), bean.getSize())));
        }
        return new ResponseResult<>(bmsYsbillcodeinfoService.selectOrderBilled(token, bean));
    }


    /**
     * 修改计费
     */
    @PostMapping("/updateCost")
    @MenuAuthority(code = "应收运输计费-修改费用")
    public ResponseResult<String> updateCost(@RequestBody BmsYsOrderBillDto bean) {
        String token = RequestContext.getToken();
        return bmsYsbillcodeinfoService.updateCost2(token, bean) > 0 ? new ResponseResult<>("操作成功!") : new ResponseResult<>("操作失败!");
    }

    /**
     * 导入计费
     */
    @ApiOperation(value = "导入计费", response = OrderBillingBean.class)
    @PostMapping("/importCost")
    @MenuAuthority(code = "应收运输计费-导入计费")
    public ResponseResult<String> importCost(@RequestBody List<OrderBillingBean> list) {
        String token = RequestContext.getToken();
        if (CollUtil.isEmpty(list)) {
            return new ResponseResult<>(400401, "未获取到导入的费用，请核查导入数据！");
        }
        // FIXME 由于service应收仓储和运输公用，所以运输这块的单据类型要手动赋值
        list.forEach(obj -> obj.setCodeTypeName("运输订单"));
        return new ResponseResult<>(bmsYsbillcodeinfoService.importCost(token, list));
    }

    /**
     * 取消计费
     */
    @ApiOperation(value = "取消计费", response = OrderBillingBean.class)
    @PostMapping("/cancelCost")
    @MenuAuthority(code = "应收运输计费-取消计费")
    public ResponseResult<String> cancelCost(@RequestBody List<OrderBillingBean> list) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeinfoService.cancelCost(token, list));
    }

    /**
     * 生成账单
     */
    @ApiOperation(value = "生成账单", response = BmsYsbillBtnDto.class)
    @Log(title = "生成账单", businessType = BusinessType.INSERT)
    @PostMapping("/generateBill")
    @MenuAuthority(code = "应收运输计费-批量生成账单")
    public ResponseResult<String> generateBill(@RequestBody BmsYsbillBtnDto bmsYsbillBtnDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeinfoService.generateBill(token, bmsYsbillBtnDto));
    }

    /**
     * 批量生成账单
     */
    @ApiOperation(value = "批量生成账单", response = BmsYsbillBtnDto.class)
    @Log(title = "批量生成账单", businessType = BusinessType.INSERT)
    @PostMapping("/generateBillBatch")
    public ResponseResult<String> generateBillBatch(@RequestBody BmsYsbillBtnDto bmsYsbillBtnDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeinfoService.generateBillBatch(token, bmsYsbillBtnDto));
    }

    /**
     * 订单计费明细查询
     */
    @ApiOperation(value = "订单计费明细查询", response = BmsYsbillcodeDetailinfoDto.class)
    @PostMapping("/selectOrderDetail")
    @MenuAuthority(code = "应收仓储计费-查询详情")
    public ResponseResult<List<BmsYsbillcodeDetailinfoDto>> selectOrderDetail(@RequestBody BmsYsbillcodeDetailinfo bmsYsbillcodeDetailinfo) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeDetailinfoService.selectBmsYsbillcodeDetailinfoList(token, bmsYsbillcodeDetailinfo));
    }

    /**
     * 订单计费导出
     */
    @ApiOperation(value = "订单计费导出", response = BmsYsbillcodeDto.class)
    @PostMapping("/export")
    public ResponseResult<String> export(@RequestBody BmsYsbillcodeinfoBean bean) {
        String token = RequestContext.getToken();
        if (StrUtil.isNotEmpty(bean.getWarehouseName())) {
            Map<String, MdmWarehouseinfo> warehouseMap = textConversionUtil.selectWarehouseMap(null, null, bean.getWarehouseName());
            if (warehouseMap.isEmpty()) {
                return null;
            } else {
                bean.setWarehouseCode(warehouseMap.values().stream().map(MdmWarehouseinfo::getWarehouseCode).collect(Collectors.joining(",")));
            }
        }
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client,warehouse");
        List<ClientInfo> clientInfos = sysDataPack.getClientinfo();
        List<WarehouseInfo> warehouseInfos = sysDataPack.getWarehouseInfo();
        bean.setClientCode(clientInfos.stream().map(ClientInfo::getClientCode).collect(Collectors.joining(",")));
        bean.setWarehouseCode(warehouseInfos.stream().map(WarehouseInfo::getWarehouseCode).collect(Collectors.joining(",")));
        PagerDataBean<BmsYsbillcodeDto> list = bmsYsbillcodeinfoService.selectOrderBill(token, bean);
        String dateTime = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss").format(new Date());
        String fileNameMsg = "应收运输计费_" + dateTime;
        boolean f = true;
        String msg = list.getRows().get(0).getClientCode();
        for (BmsYsbillcodeDto bmsYsbillcodeDto : list.getRows()) {
            if (!Objects.equals(bmsYsbillcodeDto.getClientCode(), msg)) {
                f = false;
                break;
            }
        }
        if (f) {
            fileNameMsg = list.getRows().get(0).getClientName() + "_" + fileNameMsg;
        }
        return exportUtil.getOutClassNewSheets2(token, fileNameMsg, "应收运输计费", "ysbillcodeinfo", BmsYfbillcodeDto.class, bean, userid -> {
            // 根据仓库名称查询仓库编码
            if (StrUtil.isEmpty(bean.getWarehouseCode()) || StrUtil.isEmpty(bean.getClientCode())) {
                return null;
            }
            return list.getRows();
        });
    }

    /**
     * 订单计费导出(已计费)
     *
     * @param bean 豆
     * @return {@link ResponseResult}<{@link String}>
     */
    @PostMapping("/billed/export")
    public ResponseResult<String> exportBilled(@RequestBody BmsYsbillcodeinfoBean bean) {
        String token = RequestContext.getToken();
        if (StrUtil.isNotEmpty(bean.getWarehouseName())) {
            Map<String, MdmWarehouseinfo> warehouseMap = textConversionUtil.selectWarehouseMap(null, null, bean.getWarehouseName());
            if (warehouseMap.isEmpty()) {
                return null;
            } else {
                bean.setWarehouseCode(warehouseMap.values().stream().map(MdmWarehouseinfo::getWarehouseCode).collect(Collectors.joining(",")));
            }
        }
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client,warehouse");
        List<ClientInfo> clientInfos = sysDataPack.getClientinfo();
        List<WarehouseInfo> warehouseInfos = sysDataPack.getWarehouseInfo();
        bean.setClientCode(clientInfos.stream().map(ClientInfo::getClientCode).collect(Collectors.joining(",")));
        bean.setWarehouseCode(warehouseInfos.stream().map(WarehouseInfo::getWarehouseCode).collect(Collectors.joining(",")));

        PagerDataBean<BmsYsbillcodeDto> list = bmsYsbillcodeinfoService.selectOrderBilled(token, bean);
        String dateTime = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss").format(new Date());
        String fileNameMsg = "应收运输计费_" + dateTime;
        boolean f = true;
        String msg = list.getRows().get(0).getClientCode();
        for (BmsYsbillcodeDto bmsYsbillcodeDto : list.getRows()) {
            if (!Objects.equals(bmsYsbillcodeDto.getClientCode(), msg)) {
                f = false;
                break;
            }
        }
        if (f) {
            fileNameMsg = list.getRows().get(0).getClientName() + "_" + fileNameMsg;
        }
        return exportUtil.getOutClassNewSheetsBilled(token, fileNameMsg, "应收运输计费", "ysbillcodeinfo", BmsYfbillcodeDto.class, bean, userid -> {
            // 根据仓库名称查询仓库编码
            if (StrUtil.isEmpty(bean.getWarehouseCode()) || StrUtil.isEmpty(bean.getClientCode())) {
                return null;
            }
            return list.getRows();
        });
    }

    /**
     * 根据计费id查询单据信息
     */
    @ApiOperation(value = "根据计费id查询单据信息", response = BmsYsbillcodeDto.class)
    @PostMapping("/selectOrderByExpensesId")
    public ResponseResult<List<BmsYsbillcodeDto>> selectOrderByExpensesId(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (json == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        String expensesId = JSONObject.parseObject(json).getString("expensesId");
        return new ResponseResult<>(bmsYsbillcodeinfoService.selectOrderByExpensesId(token, expensesId));
    }

    /**
     * 根据计费id查询单据明细信息
     */
    @ApiOperation(value = "根据计费id查询单据明细信息", response = BmsYsbillcodeDetailinfo.class)
    @PostMapping("/selectBmsYsbillcodeDetailinfoByYsbillId")
    public ResponseResult<List<BmsYsbillcodeDetailinfo>> selectBmsYsbillcodeDetailinfoByYsbillId(@RequestBody List<String> ysbillIds) {
        if (CollUtil.isEmpty(ysbillIds)) {
            return new ResponseResult<>(new ArrayList<>());
        }
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeinfoService.selectBmsYsbillcodeDetailinfoByYsbillId(token, ysbillIds));
    }


    @ApiOperation(value = "根据业务单号作废导入单据", response = BmsYsbillcodeDetailinfo.class)
    @PostMapping("/deleteImportData")
    @MenuAuthority(code = "应收运输计费-作废,应收仓储计费-作废")
    public ResponseResult<String> deleteImportData(@RequestBody BmsYsbillcodeDto bmsYsbillcodeDto) {
        if (bmsYsbillcodeDto == null) {
            return new ResponseResult<>(400401, "未获取到作废信息");
        }
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeinfoService.deleteImportData(token, bmsYsbillcodeDto.getList()));
    }


}
