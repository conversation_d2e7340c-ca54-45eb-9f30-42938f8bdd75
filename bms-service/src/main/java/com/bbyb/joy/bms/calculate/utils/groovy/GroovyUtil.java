package com.bbyb.joy.bms.calculate.utils.groovy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bms.calculate.domain.dto.code.day.CalculateDayDto;
import com.bbyb.joy.bms.calculate.domain.result.CalculateResult;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyCalculateResult;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyMatchResult;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyProcessResult;
import com.google.common.util.concurrent.AtomicDouble;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import groovy.lang.Script;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Component
public class GroovyUtil {


    private static final String SUCCESS = "success";

    /**
     * 规则检查方法
     * @param code 单据信息
     * @param detail 单据详情信息
     * @param detail2 单据详情信息2
     * @param factor 计算因子参数信息
     * @param regularExpression 计费规则表达式
     * @return Pair<Boolean, String> key:是否通过,value:成功为success,失败为失败原因
     */
    public Pair<Boolean, String> checkRule(Object code,
                                           Object detail,
                                           Object detail2,
                                           Object factor,
                                           String regularExpression) {
        try {
            // 参数校验
            if (StrUtil.isBlank(regularExpression)) {
                return Pair.of(false, "计费规则表达式不能为空");
            }

            // 创建Groovy绑定对象
            Binding binding = new Binding();
            binding.setVariable("code", code);
            binding.setVariable("detail", detail);
            binding.setVariable("detail2", detail2);
            binding.setVariable("rule", factor);

            // 创建Groovy执行环境
            GroovyShell shell = new GroovyShell(binding);

            try {
                // 执行规则表达式
                Object result = shell.evaluate(regularExpression);

                // 检查执行结果
                if (result != null) {
                    return Pair.of(true, SUCCESS);
                } else {
                    return Pair.of(false, "规则执行结果为空");
                }
            } catch (Exception e) {
                log.error("规则执行异常, expression:{}, error:{}", regularExpression, e.getMessage());
                return Pair.of(false, "规则执行异常: " + e.getMessage());
            }

        } catch (Exception e) {
            log.error("规则检查异常, error:{}", e.getMessage());
            return cn.hutool.core.lang.Pair.of(false, "规则检查异常: " + e.getMessage());
        }
    }

    /**
     * 规则检查方法重载 - 适用于天维度计费
     */
    public Pair<Boolean, String> checkRule(CalculateDayDto code, String regularExpression) {
        return checkRule(code, null, null, null, regularExpression);
    }


    /**
     * 匹配最优计算因子信息
     * @param code 单据信息
     * @param detail 单据详情信息
     * @param detail2 单据详情信息2
     * @param factors 计算因子参数信息列表
     * @param regularExpression 计费规则表达式
     * @return GroovyMatchResult 包含最优因子、执行结果和消息
     * 调用示例:
     * GroovyMatchResult matchResult = groovyUtil.matchBestFactor(code, detail, detail2, factors, "return factor.weight");
     *  if (matchResult.isSuccess()) {
     *      Object bestFactor = matchResult.getResult();
     *      // 处理最优因子...
     *  }
     */
    public GroovyMatchResult matchBestFactor(Object code,
                                             Object detail,
                                             Object detail2,
                                             List<?> factors,
                                             String regularExpression) {
        GroovyMatchResult result = new GroovyMatchResult();
        result.setSuccess(false);

        try {
            // 参数校验
            if (StrUtil.isBlank(regularExpression)) {
                result.setMsg("计费规则表达式不能为空");
                return result;
            }
            if (factors == null || factors.isEmpty()) {
                result.setMsg("计算因子列表不能为空");
                return result;
            }

            // 预编译Groovy脚本
            GroovyShell shell = new GroovyShell();
            Script script = shell.parse(regularExpression);

            // 创建共享的绑定对象
            Binding binding = new Binding();
            binding.setVariable("code", code);
            binding.setVariable("detail", detail);
            binding.setVariable("detail2", detail2);

            AtomicReference<Object> bestFactor = new AtomicReference<>();
            AtomicDouble maxWeight = new AtomicDouble(Double.MIN_VALUE);
            AtomicBoolean hasMatch = new AtomicBoolean(false);

            // 使用并行流处理大数据量
            if (factors.size() > 50) {
                factors.parallelStream().forEach(factor -> {
                    binding.setVariable("rule", factor);
                    script.setBinding(binding);
                    Object evalResult = script.run();

                    synchronized (this) {
                        if (evalResult instanceof Number) {
                            double currentWeight = ((Number) evalResult).doubleValue();
                            if (currentWeight > maxWeight.get()) {
                                maxWeight.set(currentWeight);
                                bestFactor.set(factor);
                                hasMatch.set(true);
                            }
                        }
                    }
                });
            } else {
                for (Object factor : factors) {
                    binding.setVariable("rule", factor);
                    script.setBinding(binding);
                    Object evalResult = script.run();

                    if (evalResult instanceof Number) {
                        double currentWeight = ((Number) evalResult).doubleValue();
                        if (currentWeight > maxWeight.get()) {
                            maxWeight.set(currentWeight);
                            bestFactor.set(factor);
                            hasMatch.set(true);
                        }
                    }
                }
            }

            if (hasMatch.get()) {
                result.setSuccess(true);
                result.setResult(bestFactor.get());
                result.setMsg("匹配成功");
                result.setCode(200);
            } else {
                result.setMsg("未能匹配到有效的计算因子");
                result.setCode(404);
            }
        } catch (Exception e) {
            log.error("匹配最优计算因子异常", e);
            result.setMsg("匹配异常: " + e.getMessage());
            result.setCode(500);
        }
        return result;
    }





    /**
     * 获取计费结果
     * @param code 单据信息
     * @param detail 单据详情信息
     * @param detail2 单据详情信息2
     * @param factor 计算因子参数信息
     * @param regularExpression 计费规则表达式
     * @return GroovyCalculateResult 包含计费结果和额外信息
     */
    public GroovyCalculateResult calculateFee(Object code,
                                              Object detail,
                                              Object detail2,
                                              Object factor,
                                              String regularExpression) {
        GroovyCalculateResult result = new GroovyCalculateResult();
        result.setSuccess(false);
        result.setMsg("初始状态");

        try {
            // 参数校验
            if (StrUtil.isBlank(regularExpression)) {
                result.setMsg("计费规则表达式不能为空");
                return result;
            }
            if (factor == null) {
                result.setMsg("计算因子不能为空");
                return result;
            }

            // 创建Groovy绑定对象
            Binding binding = new Binding();
            binding.setVariable("code", BeanUtil.beanToMap(code));
            binding.setVariable("detail", detail);
            binding.setVariable("detail2", detail2);
            binding.setVariable("rule", factor);
            binding.setVariable("result", new CalculateResult());

            // 创建Groovy执行环境
            GroovyShell shell = new GroovyShell(binding);

            // 执行计费规则表达式
            Object evalResult = shell.evaluate(regularExpression);

            if (evalResult instanceof GroovyCalculateResult) {
                result = BeanUtil.toBean(evalResult, GroovyCalculateResult.class);
            } else {
                // 单一结果
                BigDecimal decimalResult = (BigDecimal) evalResult;
                result.setResult(decimalResult);
            }

            result.setSuccess(true);
            result.setMsg("计费成功");
            result.setCode(200);
        } catch (Exception e) {
            log.error("计费异常, expression:{}, error:{}", regularExpression, e.getMessage());
            result.setMsg("计费异常: " + e.getMessage());
            result.setCode(500);
            e.getStackTrace();
        }

        return result;
    }




    /**
     * 获取计费过程结果
     * @param code 单据信息
     * @param detail 单据详情信息
     * @param detail2 单据详情信息2
     * @param factor 计算因子参数信息
     * @param regularProcessExpression 计费过程表达式
     * @return GroovyProcessResult 动态计费过程信息输出
     */
    public GroovyProcessResult calculateProcess(Object code,
                                            Object detail,
                                            Object detail2,
                                            Object factor,
                                            String regularProcessExpression) {
        GroovyProcessResult result = new GroovyProcessResult();
        result.setSuccess(false);
        result.setMsg("初始状态");

        try {
            // 参数校验
            if (StrUtil.isBlank(regularProcessExpression)) {
                result.setMsg("计费过程表达式不能为空");
                return result;
            }
            if (factor == null) {
                result.setMsg("计算因子不能为空");
                return result;
            }

            // 创建Groovy绑定对象
            Binding binding = new Binding();
            binding.setVariable("code", code);
            binding.setVariable("detail", detail);
            binding.setVariable("detail2", detail2);
            binding.setVariable("rule", factor);
            binding.setVariable("result", new CalculateResult());

            // 创建Groovy执行环境
            GroovyShell shell = new GroovyShell(binding);

            // 执行计费规则表达式
            Object evalResult = shell.evaluate(regularProcessExpression);

            if (evalResult instanceof GroovyCalculateResult) {
                result = BeanUtil.toBean(evalResult, GroovyProcessResult.class);
            } else {
                // 单一结果
                result.setResult(evalResult.toString());
            }
            result.setSuccess(true);
            result.setMsg("计费过程成功");
            result.setCode(200);
        } catch (Exception e) {
            log.error("计费过程异常, expression:{}, error:{}", regularProcessExpression, e.getMessage());
            result.setMsg("计费过程异常: " + e.getMessage());
            result.setResult("计费过程异常: " + e.getMessage());
            result.setCode(500);
        }

        return result;
    }


}
