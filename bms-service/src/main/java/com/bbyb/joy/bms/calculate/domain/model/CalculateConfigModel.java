package com.bbyb.joy.bms.calculate.domain.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
public class CalculateConfigModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户维度并行计费最小客户数")
    private Integer relationTaskMin;
    @ApiModelProperty(value = "客户维度并行计费最大客户数")
    private Integer relationTaskMax;
    @ApiModelProperty(value = "合同维度并行计费最小客户数")
    private Integer ruleTaskMin;
    @ApiModelProperty(value = "合同维度并行计费最大客户数")
    private Integer ruleTaskMax;

}
