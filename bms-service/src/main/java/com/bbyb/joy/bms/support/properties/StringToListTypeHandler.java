package com.bbyb.joy.bms.support.properties;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * MyBatis查询字符串已逗号拼接需要拆分集合的转换
 * 例:
 * public class Product {
 *     private Long id;
 *     #@TableField(typeHandler = StringToListTypeHandler.class)
 *     private List<String> tags;
 *     // getters and setters...
 * }
 */
public class StringToListTypeHandler extends BaseTypeHandler<List<String>> {




    // 1. 预处理语句设置参数时调用（必须实现）
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, String.join(",", parameter));
    }
    // 2. 通过列名获取结果时调用（必须实现）
    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return value == null ? null : Arrays.asList(value.split(","));
    }
    // 3. 通过列索引获取结果时调用（必须实现）
    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return Collections.emptyList();
    }
    // 4. 存储过程调用后获取结果时调用（必须实现）
    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return Collections.emptyList();
    }

}
