package com.bbyb.joy.bms.calculate.utils;


import com.bbyb.joy.bms.domain.enums.DateTypeEnum;
import com.bbyb.joy.bms.domain.enums.ExpensesDimensionEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 计费工具类
 */
public class CalculateUtil {


    /**
     * 报价分组条件 pub_quoterule_template.group_rule
     * 1|线路|lineCode
     * 2|网点|networkCode
     * 3|始发省份|originatingProvince
     * 4|始发城市|originatingCity
     * 5|始发区县|originatingArea
     * 6|目的省份|destinationProvince
     * 7|目的城市|destinationCity
     * 8|目的区县|destinationArea
     * 9|门店|receivingStoreCode
     * 12|客户|clientId
     * 13|配载时间|dispatchDate
     * 14|订单时间|orderDate
     * 15|签收时间|signingDate
     * 16|完成时间|finishDate
     * 入参数据格式(例子):#12#,#11#,#13#,#10#
     */
    public final static String[] GROUP_TYPE_ALL_FIELDS = {
            "lineCode", "networkCode", "originatingProvince", "originatingCity",
            "originatingArea","destinationProvince","destinationCity","destinationArea",
            "receivingStoreCode","clientId","dispatchDate","orderDate","signingDate","finishDate"
    };


    /**
     * 生成分组key所需字段
     * @param groupType 报价合同信息
     * @return 获取报价配置的group_type的字段
     */
    public static String[] generateFields(String groupType,ExpensesDimensionEnum dimensionEnum){
        List<String> groupTypeList = new ArrayList<>();

        String[] groupTypeArr = groupType.split(",");
        for (String s : groupTypeArr) {
            switch (s){
                case "#1#":
                    groupTypeList.add("lineCode");
                    break;
                case "#2#":
                    groupTypeList.add("networkCode");
                    break;
                case "#3#":
                    groupTypeList.add("originatingProvince");
                    break;
                case "#4#":
                    groupTypeList.add("originatingCity");
                    break;
                case "#5#":
                    groupTypeList.add("originatingArea");
                    break;
                case "#6#":
                    groupTypeList.add("destinationProvince");
                    break;
                case "#7#":
                    groupTypeList.add("destinationCity");
                    break;
                case "#8#":
                    groupTypeList.add("destinationArea");
                    break;
                case "#9#":
                    groupTypeList.add("receivingStoreCode");
                    break;
                case "#12#":
                    groupTypeList.add("clientId");
                    break;
                case "#13#": case "#14#":
                    if(dimensionEnum.getCode().equals(ExpensesDimensionEnum.DAY_DIMENSION.getCode())){
                        groupTypeList.add("businessBeginDay");
                    }
                    if(dimensionEnum.getCode().equals(ExpensesDimensionEnum.MONTH_DIMENSION.getCode())){
                        groupTypeList.add("businessBeginMonth");
                    }
                    break;
                case "#15#": case "#16#":
                    if(dimensionEnum.getCode().equals(ExpensesDimensionEnum.DAY_DIMENSION.getCode())){
                        groupTypeList.add("businessBeginDay");
                    }
                    if(dimensionEnum.getCode().equals(ExpensesDimensionEnum.MONTH_DIMENSION.getCode())){
                        groupTypeList.add("businessEndMonth");
                    }
                    break;
                case "#18#" :
                    groupTypeList.add("temperatureType");
                    break;
            }
        }
        if(dimensionEnum.getCode().equals(ExpensesDimensionEnum.DAY_DIMENSION.getCode())){
            groupTypeList.add("businessTimeDay");
        }
        if(dimensionEnum.getCode().equals(ExpensesDimensionEnum.MONTH_DIMENSION.getCode())){
            groupTypeList.add("businessTimeMonth");
        }
        return groupTypeList.toArray(new String[0]);
    }


    /**
     * 根据不同的分组条件生成key
     */
    public static <T> String generateKey(T obj, String[] fieldNames) {
        StringBuilder result = new StringBuilder();
        try {
            // 获取对象的所有字段
            Field[] fields = obj.getClass().getDeclaredFields();
            // 遍历指定的字段名
            for (String fieldName : fieldNames) {
                // 查找对象中是否有此字段
                for (Field field : fields) {
                    if (field.getName().equals(fieldName)) {
                        field.setAccessible(true);  // 设置字段可访问
                        Object value = field.get(obj);  // 获取字段的值
                        result.append(value != null ? "_" + value.toString() : "_NULL");  // 拼接字段值
                        break;
                    }
                }
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        return result.toString().replaceAll("[\\s:]+", "");
    }









    public static final String BUSINESS_DATE_FORMAT_MONTH = "yyyy-MM";
    public static final String BUSINESS_DATE_FORMAT_DAY = "yyyy-MM-dd";
    public static final String BUSINESS_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    /**
     * 获取计费账期设置
     * @param startDate 业务时间（订单/配载时间）
     * @param endDate 完成时间（签收/完成时间）
     * @param costDateDimension 计费时间维度(1订单/配载日期,2签收/完成日期)
     * @return "账期"
     */
    public static BuildBusinessTimeModel billDateSetting(Date startDate, Date endDate, Integer costDateDimension) {
        SimpleDateFormat sdf = new SimpleDateFormat(BUSINESS_DATE_FORMAT_MONTH);
        SimpleDateFormat sdf2 = new SimpleDateFormat(BUSINESS_DATE_FORMAT);
        SimpleDateFormat sdf3 = new SimpleDateFormat(BUSINESS_DATE_FORMAT_DAY);

        Date businessTime = null;             // 根据客户||承运商设置获取对应的业务时间(yyyy-MM-dd HH:mm:ss)
        String businessTimeStr = "";          // 根据客户||承运商设置获取对应的业务时间(yyyy-MM-dd HH:mm:ss)
        String businessTimeMonth = "";        // 根据客户||承运商设置获取对应的业务时间(yyyy-MM)
        String businessTimeDay = "";          // 根据客户||承运商设置获取对应的业务时间(yyyy-MM-dd)
        Date businessBeginTime = null;        // 订单日期||配载日期(yyyy-MM-dd HH:mm:ss)
        String businessBeginStr = "";         // 订单日期||配载日期(yyyy-MM-dd HH:mm:ss)
        String businessBeginMonth = "";       // 订单日期||配载日期(yyyy-MM)
        String businessBeginDay = "";         // 订单日期||配载日期(yyyy-MM-dd)
        Date businessEndTime = null;          // 签收日期||完成日期(yyyy-MM-dd HH:mm:ss)
        String businessEndStr = "";           // 签收日期||完成日期(yyyy-MM-dd HH:mm:ss)
        String businessEndMonth = "";         // 签收日期||完成日期(yyyy-MM)
        String businessEndDay = "";           // 签收日期||完成日期(yyyy-MM-dd)

        if (costDateDimension.equals(DateTypeEnum.BUSINESS_TIME.getIntValue())) {

            if (startDate != null) {
                businessTimeMonth = sdf.format(startDate);
                businessTimeStr = sdf2.format(startDate);
                businessTimeDay = sdf3.format(startDate);
                businessTime = startDate;

                businessBeginTime = startDate;
                businessBeginStr = sdf2.format(startDate);
                businessBeginMonth = sdf.format(startDate);
                businessBeginDay = sdf3.format(startDate);
            }
            if (endDate != null) {

                businessEndTime = endDate;
                businessEndStr = sdf2.format(endDate);
                businessEndMonth = sdf.format(endDate);
                businessEndDay = sdf3.format(endDate);
            }
        }
        if (costDateDimension.equals(DateTypeEnum.FINISH_TIME.getIntValue())) {
            if (startDate != null) {

                businessBeginTime = startDate;
                businessBeginStr = sdf2.format(startDate);
                businessBeginMonth = sdf.format(startDate);
                businessBeginDay = sdf3.format(startDate);
            }
            if (endDate != null) {

                businessTimeMonth = sdf.format(endDate);
                businessTimeStr = sdf2.format(endDate);
                businessTimeDay = sdf3.format(endDate);
                businessTime = endDate;

                businessEndTime = endDate;
                businessEndStr = sdf2.format(endDate);
                businessEndMonth = sdf.format(endDate);
                businessEndDay = sdf3.format(endDate);
            }
        }
        return BuildBusinessTimeModel.builder()
                .businessTime(businessTime)
                .businessTimeStr(businessTimeStr)
                .businessTimeMonth(businessTimeMonth)
                .businessTimeDay(businessTimeDay)
                .businessBeginTime(businessBeginTime)
                .businessBeginStr(businessBeginStr)
                .businessBeginMonth(businessBeginMonth)
                .businessBeginDay(businessBeginDay)
                .businessEndTime(businessEndTime)
                .businessEndStr(businessEndStr)
                .businessEndMonth(businessEndMonth)
                .businessEndDay(businessEndDay)
                .build();
    }


    /**
     * 业务日期出炉结果
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BuildBusinessTimeModel{

        private Date businessTime;             // 根据客户||承运商设置获取对应的业务时间(yyyy-MM-dd HH:mm:ss)
        private String businessTimeStr;        // 根据客户||承运商设置获取对应的业务时间(yyyy-MM-dd HH:mm:ss)
        private String businessTimeDay;        // 根据客户||承运商设置获取对应的业务时间(yyyy-MM-dd)
        private String businessTimeMonth;      // 根据客户||承运商设置获取对应的业务时间(yyyy-MM)
        private Date businessBeginTime;        // 订单日期||配载日期(yyyy-MM-dd HH:mm:ss)
        private String businessBeginStr;       // 订单日期||配载日期(yyyy-MM-dd HH:mm:ss)
        private String businessBeginMonth;     // 订单日期||配载日期(yyyy-MM)
        private String businessBeginDay;       // 订单日期||配载日期(yyyy-MM-dd)
        private Date businessEndTime;          // 签收日期||完成日期(yyyy-MM-dd HH:mm:ss)
        private String businessEndStr;         // 签收日期||完成日期(yyyy-MM-dd HH:mm:ss)
        private String businessEndMonth;       // 签收日期||完成日期(yyyy-MM)
        private String businessEndDay;         // 签收日期||完成日期(yyyy-MM-dd)

    }



}
