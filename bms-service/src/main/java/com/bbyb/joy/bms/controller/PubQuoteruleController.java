package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.PubQuoterule;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleCodeNameDto;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.domain.enums.RuleTypeEnum;
import com.bbyb.joy.bms.service.IPubQuoteruleService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报价管理主Controller
 */
@Slf4j
@RestController
@RequestMapping("/system/quoterule")
public class PubQuoteruleController {

    @Resource
    private IPubQuoteruleService pubQuoteruleService;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    private UserRights userRights;
    @Resource
    private TextConversionUtil textConversionUtil;

    /**
     * 查询报价管理主列表
     */
    @PostMapping("/list")
    @MenuAuthority(code = "承运商报价管理")
    public ResponseResult<PagerDataBean<PubQuoterule>> list(@RequestBody(required = false) PubQuoterule pubQuoterule) {
        String token = RequestContext.getToken();
        if (pubQuoterule==null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        List<PubQuoterule> list = new ArrayList<>();
        if (cn.hutool.core.util.StrUtil.isEmpty(pubQuoterule.getCompanyIds())) {
            pubQuoterule.setCompanyIds(textConversionUtil.getCompanyIds());
        }

        // permission
        List<PermissionConfig> permissionConfigs = new ArrayList<>();
        permissionConfigs.add(new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.STRING, "id"));
        if (pubQuoterule.getRuleType() != null && pubQuoterule.getRuleType().equals(RuleTypeEnum.TRANS.getIntValue())) {
            permissionConfigs.add(new PermissionConfig(PermissionType.CARRIER.getCode(), "clientList", PermissionConfig.FieldType.LIST_STRING, "clientCode"));
        }
        if (pubQuoterule.getRuleType() != null && pubQuoterule.getRuleType().equals(RuleTypeEnum.STORAGE.getIntValue())) {
            permissionConfigs.add(new PermissionConfig(PermissionType.CARRIER.getCode(), "carrierList", PermissionConfig.FieldType.LIST_STRING, "carrierCode"));
        }
        pubQuoterule = userRights.applyPermissions(pubQuoterule, permissionConfigs);

        return new ResponseResult<>(pubQuoteruleService.selectPubQuoteruleList(token, pubQuoterule));
    }

    /**
     * 查询报价管理编码名称集合
     */
    @PostMapping("/getCodeNamelist")
    public ResponseResult<List<PubQuoteruleCodeNameDto>> getCodeName(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(json)) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        Integer ruleType = 0;
        if (StrUtil.isNotEmpty(json)) {
            ruleType = JSONObject.parseObject(json).getInteger("ruleType");
        }
        return new ResponseResult<>(pubQuoteruleService.selectPubQuoteruleCode(token, ruleType));
    }

    /**
     * 导出报价管理主列表
     */
    @PostMapping("/export")
    public ResponseResult<String> export(@RequestBody(required = false) PubQuoterule pubQuoterule) {
        String token = RequestContext.getToken();
        if (cn.hutool.core.util.StrUtil.isEmpty(pubQuoterule.getCompanyIds())) {
            pubQuoterule.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        return exportUtil.getOutClassNew(token, "报价管理导出", "报价管理", PubQuoterule.class, userId -> {
            SysDataPack sysDataPack = userRights.getRightsByToken(token, "client,carrier");
            List<ClientInfo> clientList = sysDataPack.getClientinfo();
            List<Carrierinfo> carrierList = sysDataPack.getCarrierInfo();
            if (pubQuoterule.getRuleType() != null && pubQuoterule.getRuleType().equals(RuleTypeEnum.TRANS.getIntValue())) {
                if (CollUtil.isEmpty(clientList)) {
                    return null;
                }
                pubQuoterule.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
            }
            if (pubQuoterule.getRuleType() != null && pubQuoterule.getRuleType().equals(RuleTypeEnum.STORAGE.getIntValue())) {
                if (CollUtil.isEmpty(carrierList)) {
                    return null;
                }
                pubQuoterule.setCarrierList(carrierList.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toList()));
            }
            return pubQuoteruleService.selectPubQuoteruleList(token, pubQuoterule).getRows();
        });

    }


    /**
     * 获取报价管理主详细信息
     */
    @PostMapping(value = "/{id}")
    public ResponseResult<PubQuoterule> getInfo(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(json)) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        String id = "";
        if (StrUtil.isNotEmpty(json)) {
            id = JSONObject.parseObject(json).getString("id");
        }
        return new ResponseResult<>(pubQuoteruleService.selectPubQuoteruleById(token, id));
    }

    /**
     * 新增报价管理主
     */
    @PostMapping
    @MenuAuthority(code = "承运商报价管理-新增")
    public ResponseResult<String> add(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(json)) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        PubQuoterule pubQuoterule = JSONObject.parseObject(json, PubQuoterule.class);

        if (StrUtil.isEmpty(pubQuoterule.getRuleCode())) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "报价编码不能为空");
        }
        if (StrUtil.isEmpty(pubQuoterule.getRuleName())) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "报价名称不能为空");
        }
        if (pubQuoterule.getStartTime() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "报价生效时间不能为空");
        }
        if (pubQuoterule.getEndTime() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "报价失效时间不能为空");
        }
        if (pubQuoterule.getWarningTime() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "预警日期不能为空");
        }
        if (pubQuoterule.getRuleType() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "报价类型不能为空");
        }
        if (pubQuoterule.getRelationId() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "客户信息或承运商信息不能为空");
        }
        if (pubQuoterule.getBusinessType() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "业务类型不能为空");
        }
        if (StrUtil.isEmpty(pubQuoterule.getUserCompanyId())) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "使用机构不能为空");
        }
        if ("1".equals(this.pubQuoteruleService.checkRuleCodeUnique(token, pubQuoterule))) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "新增报价'" + pubQuoterule.getRuleCode() + "'失败，报价编码已存在");
        } else {
            return new ResponseResult<>(pubQuoteruleService.insertPubQuoterule(token, pubQuoterule));
        }
    }

    /**
     * 修改报价管理主
     */
    @PutMapping
    @MenuAuthority(code = "承运商报价管理-修改")
    public ResponseResult<String> edit(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(json)) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        PubQuoterule pubQuoterule = JSONObject.parseObject(json, PubQuoterule.class);

        if (StrUtil.isEmpty(pubQuoterule.getRuleCode())) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "报价编码不能为空");
        }
        if (StrUtil.isEmpty(pubQuoterule.getRuleName())) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "报价名称不能为空");
        }
        if (pubQuoterule.getStartTime() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "报价生效时间不能为空");
        }
        if (pubQuoterule.getEndTime() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "报价失效时间不能为空");
        }
        if (pubQuoterule.getWarningTime() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "预警日期不能为空");
        }
        if (pubQuoterule.getRuleType() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "报价类型不能为空");
        }
        if (pubQuoterule.getRelationId() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "客户信息或承运商信息不能为空");
        }
        if (pubQuoterule.getBusinessType() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "业务类型不能为空");
        }
        if (StrUtil.isEmpty(pubQuoterule.getUserCompanyId())) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "使用机构不能为空");
        }
        String check = pubQuoteruleService.checkRuleCodeUnique(token, pubQuoterule);
        if (StrUtil.isNotEmpty(check) && "1".equals(check)) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "修改报价'" + pubQuoterule.getRuleCode() + "'失败，报价编码已存在");
        } else {
            return new ResponseResult<>(pubQuoteruleService.updatePubQuoterule(token, pubQuoterule));
        }
    }

    /**
     * 物理作废报价管理主
     */
    @DeleteMapping("/{ids}")
    @MenuAuthority(code = "承运商报价管理-作废")
    public ResponseResult<Integer> remove(@PathVariable String[] ids) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(pubQuoteruleService.deletePubQuoteruleByIds(token, ids));
    }

    /**
     * 报价启用禁用
     */
    @RequestMapping(value = "/updateStatus", method = RequestMethod.PUT, produces = "application/json;charset=UTF-8")
//    @MenuAuthority(code = "承运商报价管理-启用,承运商报价管理-禁用")
    public ResponseResult<Integer> updateStatus(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(json)) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        PubQuoterule pubQuoterule = JSONObject.parseObject(json, PubQuoterule.class);
        return new ResponseResult<>(pubQuoteruleService.updatePubQuoteruleByIds(token, pubQuoterule));
    }

}