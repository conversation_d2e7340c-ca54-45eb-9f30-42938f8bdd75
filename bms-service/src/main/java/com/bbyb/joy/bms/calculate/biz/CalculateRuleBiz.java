package com.bbyb.joy.bms.calculate.biz;

import com.bbyb.joy.bms.calculate.domain.dto.rule.CalculateFactorInfoDto;
import com.bbyb.joy.bms.calculate.domain.dto.rule.CalculateRuleInfoDto;
import com.bbyb.joy.bms.calculate.domain.param.CalculateRuleQueryParam;

import java.util.List;

public interface CalculateRuleBiz {

    /**
     * 查询有效报价信息(明细维度)
     * @param param 查询参数
     * @return 报价信息
     */
    List<CalculateRuleInfoDto> queryValidList(CalculateRuleQueryParam param);


    /**
     * 查询计算因子信息
     * @param param 查询参数
     * @return 对应报价的计算因子信息
     */
    List<CalculateFactorInfoDto> queryFactorsList(CalculateRuleQueryParam param);


}
