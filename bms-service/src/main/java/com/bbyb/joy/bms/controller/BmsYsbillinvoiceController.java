package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.bill.BmsYsbillinvoice;
import com.bbyb.joy.bms.domain.dto.dto.BmsYsbillinvoiceDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYsmainAndInvoiceDto;
import com.bbyb.joy.bms.domain.dto.querybean.BmsYsmainAndInvoiceBean;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsYsbillinvoiceService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.enums.PubNumEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应收账单发票信息Controller
 *
 * <AUTHOR>
 */
@Api(tags = "应收账单发票信息接口")
@RestController
@RequestMapping("/system/ysbillinvoice")
public class BmsYsbillinvoiceController {
    @Resource
    private IBmsYsbillinvoiceService bmsYsbillinvoiceService;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private UserRights userRights;
    @Resource
    private ExportUtil exportUtil;

    /**
     * 应收账单开票保存接口
     */
    @Log(title = "应收账单开票保存接口", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @MenuAuthority(code = "应收开票管理-开票")
    public ResponseResult<String> add(@RequestBody(required = false) List<BmsYsbillinvoiceDto> bmsYsbillinvoice) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillinvoiceService.insertBatchBmsYsbillinvoice(token, bmsYsbillinvoice));
    }

    /**
     * 逻辑作废/启用应收账单发票信息
     */
    @Log(title = "应收账单发票信息", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateStatus")
    @MenuAuthority(code = "应收开票管理-作废")
    public ResponseResult<String> updateStatus(@RequestBody(required = false) Long[] ids) {
        String token = RequestContext.getToken();
        int ret = bmsYsbillinvoiceService.updateBmsYsbillinvoiceStatusByIds(token, ids);
        if (PubNumEnum.zero.getIntValue().equals(ret)) {
            return new ResponseResult<>(40005, "未找到需要操作的数据");
        } else if (PubNumEnum.fone.getIntValue().equals(ret)) {
            return new ResponseResult<>(40005, "系统错误，请联系管理员");
        } else if (PubNumEnum.two.getIntValue().equals(ret)) {
            return new ResponseResult<>(40005, "当前账单已核销不可作废");
        } else if (PubNumEnum.three.getIntValue().equals(ret)) {
            return new ResponseResult<>(40005, "存在已作废的数据，请确认");
        }
        return new ResponseResult<>("操作成功");
    }

    /**
     * 修改查询应收账单发票信息列表
     */
    @PostMapping("/list")
    @MenuAuthority(code = "应收开票管理-修改")
    public ResponseResult<PagerDataBean<BmsYsbillinvoice>> list(@RequestBody(required = false) BmsYsbillinvoice bmsYsbillinvoice) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillinvoiceService.selectBmsYsbillinvoiceList(token, bmsYsbillinvoice));
    }

    /**
     * 导出发票信息列表
     */
    @Log(title = "导出发票详细信息列表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportlist")
    @MenuAuthority(code = "应收开票管理-导出")
    public ResponseResult<String> exportlist(@RequestBody(required = false) BmsYsbillinvoice bmsYsbillinvoice) {
        String token = RequestContext.getToken();
        return exportUtil.getOutClassNew(token, "发票详细信息导出", "发票管理", BmsYsbillinvoice.class, userId -> {
            return bmsYsbillinvoiceService.selectBmsYsbillinvoiceList(token, bmsYsbillinvoice).getRows();
        });
    }


    /**
     * 应收发票主页面列表查询
     */
    @ApiOperation(value = "应收发票主页面列表查询", response = BmsYsmainAndInvoiceDto.class)
    @PostMapping("/selectYsmainAndInvoice")
    public ResponseResult<PagerDataBean<BmsYsmainAndInvoiceDto>> selectYsmainAndInvoice(@RequestBody(required = false) BmsYsmainAndInvoiceBean bean) {

        String token = RequestContext.getToken();

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CLIENT.getCode(), "clientList", PermissionConfig.FieldType.LIST_STRING, "clientCode"),
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.STRING, "id")
        );
        bean = userRights.applyPermissions(bean, permissionConfigs);

        return new ResponseResult<>(bmsYsbillinvoiceService.selectYsmainAndInvoiceList(token, bean));
    }

    /**
     * 导出发票信息列表
     */
    @Log(title = "导出发票信息列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public ResponseResult<String> export(@RequestBody(required = false) BmsYsmainAndInvoiceBean bmsYsbillinvoice) {
        String token = RequestContext.getToken();
        if (cn.hutool.core.util.StrUtil.isEmpty(bmsYsbillinvoice.getCompanyIds())) {
            bmsYsbillinvoice.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        return exportUtil.getOutClassNew2(token, "应收开票导出", "应收开票", BmsYsmainAndInvoiceDto.class, userId -> {
            SysDataPack sysDataPack = userRights.getRightsByToken(token, "client");
            List<ClientInfo> clientList = sysDataPack.getClientinfo();
            if (CollUtil.isEmpty(clientList)) {
                return null;
            }
            bmsYsbillinvoice.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
            return bmsYsbillinvoiceService.selectYsmainAndInvoiceList(token, bmsYsbillinvoice).getRows();
        });
    }


}
