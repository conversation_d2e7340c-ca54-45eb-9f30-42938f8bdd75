package com.bbyb.joy.bms.support.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.bbyb.bms.model.po.BmsYfcostMainInfoPO;
import com.bbyb.bms.model.po.BmsYfexpensesMiddleSharePO;
import com.bbyb.bms.model.po.BmsYfjobbillinfoPO;
import com.bbyb.joy.bms.domain.dto.*;
import com.bbyb.joy.bms.domain.dto.charginglogic.AutoYfCodeInfo;
import com.bbyb.joy.bms.domain.dto.charginglogic.AutoYsCodeInfo;
import com.bbyb.joy.bms.domain.dto.dto.BmsYfCostMainInfo;
import groovy.lang.Tuple3;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 算术操作
 */
public class AutoMaticMathUtil {


    /**
     * 应收
     * 运输费用字段名称数组
     * 包含运费、配送费、超远费等各类费用字段
     */
    public static final String[] YS_COST_FIELDS = {
            "freight", "deliveryFee", "ultrafarFee", "superframesFee", "excessFee",
            "reduceFee", "outboundsortingFee", "shortbargeFee", "returnFee", "exceptionFee",
            "otherCost1", "otherCost2", "otherCost3", "otherCost4", "otherCost5",
            "otherCost6", "otherCost7", "otherCost8", "otherCost9", "otherCost10",
            "otherCost11", "otherCost12"
    };


    /**
     * 应付
     * 运输费用字段名称数组
     * 包含运费、配送费、超远费等各类费用字段
     */
    public static final String[] YF_COST_FIELDS = {
            "freight", "deliveryFee", "ultrafarFee", "superframesFee", "exceptionFee", "excessFee",
            "reduceFee", "outboundsortingFee", "shortbargeFee", "returnFee", "exceptionFee",
            "otherCost1", "otherCost2", "otherCost3", "otherCost4", "otherCost5",
            "otherCost6", "otherCost7", "otherCost8", "otherCost9", "otherCost10",
            "otherCost11", "otherCost12"
    };


    /**
     * 金额分摊(返回数据对应到单)
     *
     * @param shareType    分摊类型 1:按订单(平均),2:按重量,3:按体积
     * @param mainCostInfo 总费用信息
     * @param resultType   1:key:codeId,2:key:relateCode
     * @return value1:按订单的返回结果,value2:按体积的返回结果,value3:按重量的返回结果
     */
    public static Map<String, BmsYscostInfo> splitAmountByCode(Integer shareType
            , BmsYscostMainInfo mainCostInfo
            , List<AutoYsCodeInfo> orderInfos
            , Integer resultType) {
        Map<String, BmsYscostInfo> returnData = new HashMap<>();
        if (shareType.equals(1)) {
            return splitAmountByOrder2(mainCostInfo, orderInfos, resultType);
        }
        if (shareType.equals(2)) {
            return splitAmountByWeight2(mainCostInfo, orderInfos, resultType);
        }
        if (shareType.equals(3)) {
            return splitAmountByVolumn2(mainCostInfo, orderInfos, resultType);
        }
        return returnData;
    }


    /**
     * 金额分摊
     *
     * @param shareType   分摊类型 1:按订单(平均),2:按体积,3:按重量
     * @param totalAmount 总金额
     * @param orderSize   订单数
     * @param weights     重量集
     * @param volumns     体积集
     * @return value1:按订单的返回结果,value2:按体积的返回结果,value3:按重量的返回结果
     */
    public static Tuple3<List<BigDecimal>, Map<BigDecimal, BigDecimal>, Map<BigDecimal, BigDecimal>> splitAmount(Integer shareType
            , BigDecimal totalAmount
            , int orderSize
            , List<BigDecimal> weights
            , List<BigDecimal> volumns) {
        if (shareType.equals(1)) {
            List<BigDecimal> bigDecimals = splitAmountByOrder(totalAmount, orderSize);
            return new Tuple3<>(bigDecimals, null, null);
        }
        if (shareType.equals(2)) {
            Map<BigDecimal, BigDecimal> bigDecimalBigDecimalMap = splitAmountByWeightOrVolumn(totalAmount, volumns);
            return new Tuple3<>(null, bigDecimalBigDecimalMap, null);
        }
        if (shareType.equals(3)) {
            Map<BigDecimal, BigDecimal> bigDecimalBigDecimalMap = splitAmountByWeightOrVolumn(totalAmount, weights);
            return new Tuple3<>(null, null, bigDecimalBigDecimalMap);
        }
        return new Tuple3<>(null, null, null);
    }


    /**
     * 按重量||体积分摊
     *
     * @param totalAmount 总金额
     * @param weights     重量集合
     * @return 按重量分摊的金额
     */
    public static Map<BigDecimal, BigDecimal> splitAmountByWeightOrVolumn(BigDecimal totalAmount, List<BigDecimal> weights) {
        Map<BigDecimal, BigDecimal> amountMap = new HashMap<>();
        BigDecimal totalWeight = BigDecimal.ZERO;
        for (BigDecimal weight : weights) {
            totalWeight = totalWeight.add(weight);
        }
        for (BigDecimal weight : weights) {
            BigDecimal amount = totalAmount.multiply(weight).divide(totalWeight, 2, RoundingMode.DOWN);
            amountMap.put(weight, amount);
        }
        BigDecimal remainingAmount = totalAmount.subtract(amountMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
            for (int i = 0; i < remainingAmount.multiply(BigDecimal.valueOf(100)).intValue(); i++) {
                BigDecimal weight = weights.get(i % weights.size());
                BigDecimal amount = amountMap.get(weight).add(BigDecimal.valueOf(0.01));
                amountMap.put(weight, amount);
            }
        }
        return amountMap;
    }


    /**
     * 按订单分摊
     *
     * @param totalAmount    总金额
     * @param numberOfOrders 单据条数
     * @return 分摊金额
     */
    public static List<BigDecimal> splitAmountByOrder(BigDecimal totalAmount, int numberOfOrders) {
        List<BigDecimal> amounts = new ArrayList<>();
        BigDecimal amountPerOrder = totalAmount.divide(BigDecimal.valueOf(numberOfOrders), 2, RoundingMode.DOWN);
        BigDecimal remainingAmount = totalAmount;
        for (int i = 0; i < numberOfOrders; i++) {
            BigDecimal amount = amountPerOrder.setScale(2, RoundingMode.DOWN);
            amounts.add(amount);
            remainingAmount = remainingAmount.subtract(amount);
        }
        if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
            for (int i = 0; i < remainingAmount.multiply(BigDecimal.valueOf(100)).intValue(); i++) {
                amounts.set(i % amounts.size(), amounts.get(i % amounts.size()).add(BigDecimal.valueOf(0.01)));
            }
        }
        return amounts;
    }


    /**
     * 按单分摊金额
     *
     * @param costInfo   总费用明细
     * @param orderInfos 涉及订单信息
     * @param resultType 处理结果类型
     * @return key:单据id或者/code
     */
    public static Map<String, BmsYscostInfo> splitAmountByOrder2(BmsYscostMainInfo costInfo
            , List<AutoYsCodeInfo> orderInfos
            , Integer resultType) {
        Map<String, BmsYscostInfo> returnData = new LinkedHashMap<>();
        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            AutoYsCodeInfo orderInfo = orderInfos.get(0);
            BmsYscostInfo shareInfo = new BmsYscostInfo();
            shareInfo.setFreight(costInfo.getFreight());
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee());
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee());
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee());
            shareInfo.setExcessFee(costInfo.getExcessFee());
            shareInfo.setReduceFee(costInfo.getReduceFee());
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee());
            shareInfo.setReturnFee(costInfo.getReturnFee());
            shareInfo.setExceptionFee(costInfo.getExceptionFee());
            shareInfo.setOtherCost1(costInfo.getOtherCost1());
            shareInfo.setOtherCost2(costInfo.getOtherCost2());
            shareInfo.setOtherCost3(costInfo.getOtherCost3());
            shareInfo.setOtherCost4(costInfo.getOtherCost4());
            shareInfo.setOtherCost5(costInfo.getOtherCost5());
            shareInfo.setOtherCost6(costInfo.getOtherCost6());
            shareInfo.setOtherCost7(costInfo.getOtherCost7());
            shareInfo.setOtherCost8(costInfo.getOtherCost8());
            shareInfo.setOtherCost9(costInfo.getOtherCost9());
            shareInfo.setOtherCost10(costInfo.getOtherCost10());
            shareInfo.setOtherCost11(costInfo.getOtherCost11());
            shareInfo.setOtherCost12(costInfo.getOtherCost12());
            shareInfo.setShareType(1);

            shareInfo.setShareAmount(costInfo.getSumFee() != null ? costInfo.getSumFee() : BigDecimal.ZERO);
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), shareInfo);
            return returnData;
        }
        //金额分摊(向下取整，避免出现最后一条金额非累加)
        BigDecimal avgfreightAmount = BigDecimal.ZERO;
        BigDecimal avgDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal avgUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal avgSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal avgExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal avgReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal avgOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal avgShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal avgReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal avgExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal avgOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost12Amount = BigDecimal.ZERO;

        if (!CollUtil.isEmpty(orderInfos)) {
            avgfreightAmount = costInfo.getFreight().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgDeliveryFeeAmount = costInfo.getDeliveryFee().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgUltrafarFeeAmount = costInfo.getUltrafarFee().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgSuperframesFeeAmount = costInfo.getSuperframesFee().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgExcessFeeAmount = costInfo.getExcessFee().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgReduceFeeAmount = costInfo.getReduceFee().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOutboundsortingFeeAmount = costInfo.getOutboundsortingFee().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgShortbargeFeeAmount = costInfo.getShortbargeFee().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgReturnFeeAmount = costInfo.getReturnFee().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgExceptionFeeAmount = costInfo.getExceptionFee().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost1Amount = costInfo.getOtherCost1().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost2Amount = costInfo.getOtherCost2().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost3Amount = costInfo.getOtherCost3().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost4Amount = costInfo.getOtherCost4().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost5Amount = costInfo.getOtherCost5().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost6Amount = costInfo.getOtherCost6().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost7Amount = costInfo.getOtherCost7().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost8Amount = costInfo.getOtherCost8().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost9Amount = costInfo.getOtherCost9().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost10Amount = costInfo.getOtherCost10().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost11Amount = costInfo.getOtherCost11().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost12Amount = costInfo.getOtherCost12().divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
        }
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;

        for (int i = 0; i < orderInfos.size(); i++) {
            AutoYsCodeInfo orderInfo = orderInfos.get(i);
            BmsYscostInfo shareInfo = new BmsYscostInfo();
            shareInfo.setFreight(avgfreightAmount);
            shareInfo.setDeliveryFee(avgDeliveryFeeAmount);
            shareInfo.setUltrafarFee(avgUltrafarFeeAmount);
            shareInfo.setSuperframesFee(avgSuperframesFeeAmount);
            shareInfo.setExcessFee(avgExcessFeeAmount);
            shareInfo.setReduceFee(avgReduceFeeAmount);
            shareInfo.setOutboundsortingFee(avgOutboundsortingFeeAmount);
            shareInfo.setShortbargeFee(avgShortbargeFeeAmount);
            shareInfo.setReturnFee(avgReturnFeeAmount);
            shareInfo.setExceptionFee(avgExceptionFeeAmount);
            shareInfo.setOtherCost1(avgOtherCost1Amount);
            shareInfo.setOtherCost2(avgOtherCost2Amount);
            shareInfo.setOtherCost3(avgOtherCost3Amount);
            shareInfo.setOtherCost4(avgOtherCost4Amount);
            shareInfo.setOtherCost5(avgOtherCost5Amount);
            shareInfo.setOtherCost6(avgOtherCost6Amount);
            shareInfo.setOtherCost7(avgOtherCost7Amount);
            shareInfo.setOtherCost8(avgOtherCost8Amount);
            shareInfo.setOtherCost9(avgOtherCost9Amount);
            shareInfo.setOtherCost10(avgOtherCost10Amount);
            shareInfo.setOtherCost11(avgOtherCost11Amount);
            shareInfo.setOtherCost12(avgOtherCost12Amount);
            shareInfo.setShareType(1);
            shareInfo.setShareAmount(avgfreightAmount
                    .add(avgDeliveryFeeAmount)
                    .add(avgUltrafarFeeAmount)
                    .add(avgSuperframesFeeAmount)
                    .add(avgExcessFeeAmount)
                    .add(avgReduceFeeAmount)
                    .add(avgOutboundsortingFeeAmount)
                    .add(avgShortbargeFeeAmount)
                    .add(avgReturnFeeAmount)
                    .add(avgExceptionFeeAmount)
                    .add(avgOtherCost1Amount)
                    .add(avgOtherCost2Amount)
                    .add(avgOtherCost3Amount)
                    .add(avgOtherCost4Amount)
                    .add(avgOtherCost5Amount)
                    .add(avgOtherCost6Amount)
                    .add(avgOtherCost7Amount)
                    .add(avgOtherCost8Amount)
                    .add(avgOtherCost9Amount)
                    .add(avgOtherCost10Amount)
                    .add(avgOtherCost11Amount)
                    .add(avgOtherCost12Amount)
            );
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), shareInfo);
            if (i < orderInfos.size() - 1) {
                runningFreightAmount = runningFreightAmount.add(avgfreightAmount);
                runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(avgDeliveryFeeAmount);
                runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(avgUltrafarFeeAmount);
                runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(avgSuperframesFeeAmount);
                runningExcessFeeAmount = runningExcessFeeAmount.add(avgExcessFeeAmount);
                runningReduceFeeAmount = runningReduceFeeAmount.add(avgReduceFeeAmount);
                runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(avgOutboundsortingFeeAmount);
                runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(avgShortbargeFeeAmount);
                runningReturnFeeAmount = runningReturnFeeAmount.add(avgReturnFeeAmount);
                runningExceptionFeeAmount = runningExceptionFeeAmount.add(avgExceptionFeeAmount);
                runningOtherCost1Amount = runningOtherCost1Amount.add(avgOtherCost1Amount);
                runningOtherCost2Amount = runningOtherCost2Amount.add(avgOtherCost2Amount);
                runningOtherCost3Amount = runningOtherCost3Amount.add(avgOtherCost3Amount);
                runningOtherCost4Amount = runningOtherCost4Amount.add(avgOtherCost4Amount);
                runningOtherCost5Amount = runningOtherCost5Amount.add(avgOtherCost5Amount);
                runningOtherCost6Amount = runningOtherCost6Amount.add(avgOtherCost6Amount);
                runningOtherCost7Amount = runningOtherCost7Amount.add(avgOtherCost7Amount);
                runningOtherCost8Amount = runningOtherCost8Amount.add(avgOtherCost8Amount);
                runningOtherCost9Amount = runningOtherCost9Amount.add(avgOtherCost9Amount);
                runningOtherCost10Amount = runningOtherCost10Amount.add(avgOtherCost10Amount);
                runningOtherCost11Amount = runningOtherCost11Amount.add(avgOtherCost11Amount);
                runningOtherCost12Amount = runningOtherCost12Amount.add(avgOtherCost12Amount);
                idx++;
            }
        }
        //拿到最后一个单据信息，将计算求于数均给到最后一条
        AutoYsCodeInfo lastOrderInfo = orderInfos.get(idx);
        BmsYscostInfo lastShareInfo = new BmsYscostInfo();
        lastShareInfo.setFreight(costInfo.getFreight().subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(costInfo.getDeliveryFee().subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(costInfo.getUltrafarFee().subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(costInfo.getSuperframesFee().subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(costInfo.getExcessFee().subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(costInfo.getReduceFee().subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(costInfo.getShortbargeFee().subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(costInfo.getReturnFee().subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(costInfo.getExceptionFee().subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(costInfo.getOtherCost1().subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(costInfo.getOtherCost2().subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(costInfo.getOtherCost3().subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(costInfo.getOtherCost4().subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(costInfo.getOtherCost5().subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(costInfo.getOtherCost6().subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(costInfo.getOtherCost7().subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(costInfo.getOtherCost8().subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(costInfo.getOtherCost9().subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(costInfo.getOtherCost10().subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(costInfo.getOtherCost11().subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(costInfo.getOtherCost12().subtract(runningOtherCost12Amount));
        returnData.put(resultType.equals(2) ? lastOrderInfo.getRelateCode() : lastOrderInfo.getId(), lastShareInfo);
        return returnData;
    }

    /**
     * 按重量金额分摊
     *
     * @param costInfo   费用信息
     * @param orderInfos 单据信息
     * @param resultType 1:按单号id，2:按单号
     * @return 按重量分摊的金额
     */
    public static Map<String, BmsYscostInfo> splitAmountByWeight2(BmsYscostMainInfo costInfo
            , List<AutoYsCodeInfo> orderInfos
            , Integer resultType) {
        Map<String, BmsYscostInfo> returnData = new LinkedHashMap<>();
        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            AutoYsCodeInfo orderInfo = orderInfos.get(0);
            BmsYscostInfo shareInfo = new BmsYscostInfo();
            shareInfo.setFreight(costInfo.getFreight());
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee());
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee());
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee());
            shareInfo.setExcessFee(costInfo.getExcessFee());
            shareInfo.setReduceFee(costInfo.getReduceFee());
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee());
            shareInfo.setReturnFee(costInfo.getReturnFee());
            shareInfo.setExceptionFee(costInfo.getExceptionFee());
            shareInfo.setOtherCost1(costInfo.getOtherCost1());
            shareInfo.setOtherCost2(costInfo.getOtherCost2());
            shareInfo.setOtherCost3(costInfo.getOtherCost3());
            shareInfo.setOtherCost4(costInfo.getOtherCost4());
            shareInfo.setOtherCost5(costInfo.getOtherCost5());
            shareInfo.setOtherCost6(costInfo.getOtherCost6());
            shareInfo.setOtherCost7(costInfo.getOtherCost7());
            shareInfo.setOtherCost8(costInfo.getOtherCost8());
            shareInfo.setOtherCost9(costInfo.getOtherCost9());
            shareInfo.setOtherCost10(costInfo.getOtherCost10());
            shareInfo.setOtherCost11(costInfo.getOtherCost11());
            shareInfo.setOtherCost12(costInfo.getOtherCost12());
            shareInfo.setShareType(2);
            shareInfo.setShareAmount(costInfo.getSumFee());
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), shareInfo);
            return returnData;
        }

        BigDecimal totalWeight = BigDecimal.ZERO;
        //获取总重量
        for (AutoYsCodeInfo orderInfo : orderInfos) {
            totalWeight = totalWeight.add(orderInfo.getTotalWeight() != null ? orderInfo.getTotalWeight() : BigDecimal.ZERO);
        }

        //分摊金额
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;
        for (int i = 0; i < orderInfos.size(); i++) {
            AutoYsCodeInfo orderInfo = orderInfos.get(i);
            BigDecimal weightRadio = orderInfo.getTotalWeight().divide(totalWeight, 2, RoundingMode.DOWN);

            BmsYscostInfo shareInfo = new BmsYscostInfo();
            shareInfo.setFreight(costInfo.getFreight().multiply(weightRadio));
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee().multiply(weightRadio));
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee().multiply(weightRadio));
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee().multiply(weightRadio));
            shareInfo.setExcessFee(costInfo.getExcessFee().multiply(weightRadio));
            shareInfo.setReduceFee(costInfo.getReduceFee().multiply(weightRadio));
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().multiply(weightRadio));
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee().multiply(weightRadio));
            shareInfo.setReturnFee(costInfo.getReturnFee().multiply(weightRadio));
            shareInfo.setExceptionFee(costInfo.getExceptionFee().multiply(weightRadio));
            shareInfo.setOtherCost1(costInfo.getOtherCost1().multiply(weightRadio));
            shareInfo.setOtherCost2(costInfo.getOtherCost2().multiply(weightRadio));
            shareInfo.setOtherCost3(costInfo.getOtherCost3().multiply(weightRadio));
            shareInfo.setOtherCost4(costInfo.getOtherCost4().multiply(weightRadio));
            shareInfo.setOtherCost5(costInfo.getOtherCost5().multiply(weightRadio));
            shareInfo.setOtherCost6(costInfo.getOtherCost6().multiply(weightRadio));
            shareInfo.setOtherCost7(costInfo.getOtherCost7().multiply(weightRadio));
            shareInfo.setOtherCost8(costInfo.getOtherCost8().multiply(weightRadio));
            shareInfo.setOtherCost9(costInfo.getOtherCost9().multiply(weightRadio));
            shareInfo.setOtherCost10(costInfo.getOtherCost10().multiply(weightRadio));
            shareInfo.setOtherCost11(costInfo.getOtherCost11().multiply(weightRadio));
            shareInfo.setOtherCost12(costInfo.getOtherCost12().multiply(weightRadio));
            shareInfo.setShareType(1);
            shareInfo.setShareAmount(shareInfo.getFreight()
                    .add(shareInfo.getDeliveryFee())
                    .add(shareInfo.getUltrafarFee())
                    .add(shareInfo.getSuperframesFee())
                    .add(shareInfo.getExcessFee())
                    .add(shareInfo.getReduceFee())
                    .add(shareInfo.getOutboundsortingFee())
                    .add(shareInfo.getShortbargeFee())
                    .add(shareInfo.getReturnFee())
                    .add(shareInfo.getExceptionFee())
                    .add(shareInfo.getOtherCost1())
                    .add(shareInfo.getOtherCost2())
                    .add(shareInfo.getOtherCost3())
                    .add(shareInfo.getOtherCost4())
                    .add(shareInfo.getOtherCost5())
                    .add(shareInfo.getOtherCost6())
                    .add(shareInfo.getOtherCost7())
                    .add(shareInfo.getOtherCost8())
                    .add(shareInfo.getOtherCost9())
                    .add(shareInfo.getOtherCost10())
                    .add(shareInfo.getOtherCost11())
                    .add(shareInfo.getOtherCost12())
            );
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), shareInfo);
            //倒数第二条
            if (i < orderInfos.size() - 1) {
                runningFreightAmount = runningFreightAmount.add(shareInfo.getFreight());
                runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(shareInfo.getDeliveryFee());
                runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(shareInfo.getUltrafarFee());
                runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(shareInfo.getSuperframesFee());
                runningExcessFeeAmount = runningExcessFeeAmount.add(shareInfo.getExcessFee());
                runningReduceFeeAmount = runningReduceFeeAmount.add(shareInfo.getReduceFee());
                runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(shareInfo.getOutboundsortingFee());
                runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(shareInfo.getShortbargeFee());
                runningReturnFeeAmount = runningReturnFeeAmount.add(shareInfo.getReturnFee());
                runningExceptionFeeAmount = runningExceptionFeeAmount.add(shareInfo.getExceptionFee());
                runningOtherCost1Amount = runningOtherCost1Amount.add(shareInfo.getOtherCost1());
                runningOtherCost2Amount = runningOtherCost2Amount.add(shareInfo.getOtherCost2());
                runningOtherCost3Amount = runningOtherCost3Amount.add(shareInfo.getOtherCost3());
                runningOtherCost4Amount = runningOtherCost4Amount.add(shareInfo.getOtherCost4());
                runningOtherCost5Amount = runningOtherCost5Amount.add(shareInfo.getOtherCost5());
                runningOtherCost6Amount = runningOtherCost6Amount.add(shareInfo.getOtherCost6());
                runningOtherCost7Amount = runningOtherCost7Amount.add(shareInfo.getOtherCost7());
                runningOtherCost8Amount = runningOtherCost8Amount.add(shareInfo.getOtherCost8());
                runningOtherCost9Amount = runningOtherCost9Amount.add(shareInfo.getOtherCost9());
                runningOtherCost10Amount = runningOtherCost10Amount.add(shareInfo.getOtherCost10());
                runningOtherCost11Amount = runningOtherCost11Amount.add(shareInfo.getOtherCost11());
                runningOtherCost12Amount = runningOtherCost12Amount.add(shareInfo.getOtherCost12());
                idx++;
            }
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        AutoYsCodeInfo lastOrderInfo = orderInfos.get(idx);
        BmsYscostInfo lastShareInfo = new BmsYscostInfo();
        lastShareInfo.setFreight(costInfo.getFreight().subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(costInfo.getDeliveryFee().subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(costInfo.getUltrafarFee().subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(costInfo.getSuperframesFee().subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(costInfo.getExcessFee().subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(costInfo.getReduceFee().subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(costInfo.getShortbargeFee().subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(costInfo.getReturnFee().subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(costInfo.getExceptionFee().subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(costInfo.getOtherCost1().subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(costInfo.getOtherCost2().subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(costInfo.getOtherCost3().subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(costInfo.getOtherCost4().subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(costInfo.getOtherCost5().subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(costInfo.getOtherCost6().subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(costInfo.getOtherCost7().subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(costInfo.getOtherCost8().subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(costInfo.getOtherCost9().subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(costInfo.getOtherCost10().subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(costInfo.getOtherCost11().subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(costInfo.getOtherCost12().subtract(runningOtherCost12Amount));
        returnData.put(resultType.equals(2) ? lastOrderInfo.getRelateCode() : lastOrderInfo.getId(), lastShareInfo);
        return returnData;
    }


    /**
     * 按重量金额分摊
     *
     * @param totalAmount 总金额
     * @param orderInfos  单据信息
     * @return 按重量分摊的金额
     */
    public static Map<String, BigDecimal> splitAmountByWeight(BigDecimal totalAmount, List<AutoYsCodeInfo> orderInfos, Integer resultType) {
        Map<String, BigDecimal> returnData = new LinkedHashMap<>();
        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            AutoYsCodeInfo orderInfo = orderInfos.get(0);
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), totalAmount);
            return returnData;
        }

        BigDecimal totalWeight = BigDecimal.ZERO;
        //获取总重量
        for (AutoYsCodeInfo orderInfo : orderInfos) {
            totalWeight = totalWeight.add(orderInfo.getTotalWeight() != null ? orderInfo.getTotalWeight() : BigDecimal.ZERO);
        }

        //分摊金额
        int idx = 0;
        BigDecimal runningAmount = BigDecimal.ZERO;
        for (int i = 0; i < orderInfos.size(); i++) {
            AutoYsCodeInfo orderInfo = orderInfos.get(i);
            BigDecimal avgAmount = totalAmount.multiply(orderInfo.getTotalWeight()).divide(totalWeight, 2, RoundingMode.DOWN);
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), avgAmount);
            //倒数第二条
            if (i < orderInfos.size() - 1) {
                runningAmount = runningAmount.add(avgAmount);
            }
            idx++;
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        AutoYsCodeInfo lastOrderInfo = orderInfos.get(idx);
        BigDecimal lastAvgAmount = totalAmount.subtract(runningAmount);
        returnData.put(resultType.equals(2) ? lastOrderInfo.getRelateCode() : lastOrderInfo.getId(), lastAvgAmount);
        return returnData;
    }


    /**
     * 按体积金额分摊
     *
     * @param totalAmount 总金额
     * @param orderInfos  单据信息
     * @return 按体积分摊的金额
     */
    public static Map<String, BigDecimal> splitAmountByVolumn(BigDecimal totalAmount, List<AutoYsCodeInfo> orderInfos, Integer resultType) {
        Map<String, BigDecimal> returnData = new LinkedHashMap<>();
        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            AutoYsCodeInfo orderInfo = orderInfos.get(0);
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), totalAmount);
            return returnData;
        }

        BigDecimal totalVolumn = BigDecimal.ZERO;
        //获取总重量
        for (AutoYsCodeInfo orderInfo : orderInfos) {
            totalVolumn = totalVolumn.add(orderInfo.getTotalVolume() != null ? orderInfo.getTotalVolume() : BigDecimal.ZERO);
        }

        //分摊金额
        int idx = 0;
        BigDecimal runningAmount = BigDecimal.ZERO;
        for (int i = 0; i < orderInfos.size(); i++) {
            AutoYsCodeInfo orderInfo = orderInfos.get(i);
            BigDecimal avgAmount = totalAmount.multiply(orderInfo.getTotalVolume()).divide(totalVolumn, 2, RoundingMode.DOWN);
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), avgAmount);
            //倒数第二条
            if (i < orderInfos.size() - 1) {
                runningAmount = runningAmount.add(avgAmount);
            }
            idx++;
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        AutoYsCodeInfo lastOrderInfo = orderInfos.get(idx);
        BigDecimal lastAvgAmount = totalAmount.subtract(runningAmount);
        returnData.put(resultType.equals(2) ? lastOrderInfo.getRelateCode() : lastOrderInfo.getId(), lastAvgAmount);
        return returnData;
    }


    /**
     * 按体积金额分摊
     *
     * @param costInfo   主费用明细
     * @param orderInfos 单据信息
     * @param resultType 1:key单据id 2:单据单号
     * @return 按体积分摊的金额
     */
    public static Map<String, BmsYscostInfo> splitAmountByVolumn2(BmsYscostMainInfo costInfo
            , List<AutoYsCodeInfo> orderInfos
            , Integer resultType) {
        Map<String, BmsYscostInfo> returnData = new LinkedHashMap<>();
        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            AutoYsCodeInfo orderInfo = orderInfos.get(0);
            BmsYscostInfo shareInfo = new BmsYscostInfo();
            shareInfo.setFreight(costInfo.getFreight());
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee());
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee());
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee());
            shareInfo.setExcessFee(costInfo.getExcessFee());
            shareInfo.setReduceFee(costInfo.getReduceFee());
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee());
            shareInfo.setReturnFee(costInfo.getReturnFee());
            shareInfo.setExceptionFee(costInfo.getExceptionFee());
            shareInfo.setOtherCost1(costInfo.getOtherCost1());
            shareInfo.setOtherCost2(costInfo.getOtherCost2());
            shareInfo.setOtherCost3(costInfo.getOtherCost3());
            shareInfo.setOtherCost4(costInfo.getOtherCost4());
            shareInfo.setOtherCost5(costInfo.getOtherCost5());
            shareInfo.setOtherCost6(costInfo.getOtherCost6());
            shareInfo.setOtherCost7(costInfo.getOtherCost7());
            shareInfo.setOtherCost8(costInfo.getOtherCost8());
            shareInfo.setOtherCost9(costInfo.getOtherCost9());
            shareInfo.setOtherCost10(costInfo.getOtherCost10());
            shareInfo.setOtherCost11(costInfo.getOtherCost11());
            shareInfo.setOtherCost12(costInfo.getOtherCost12());
            shareInfo.setShareType(3);
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), shareInfo);
            return returnData;
        }

        BigDecimal totalVolume = BigDecimal.ZERO;
        //获取总重量
        for (AutoYsCodeInfo orderInfo : orderInfos) {
            totalVolume = totalVolume.add(orderInfo.getTotalVolume() != null ? orderInfo.getTotalVolume() : BigDecimal.ZERO);
        }

        //分摊金额
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;
        for (int i = 0; i < orderInfos.size(); i++) {
            AutoYsCodeInfo orderInfo = orderInfos.get(i);
            BigDecimal volumnRadio = orderInfo.getTotalWeight().divide(totalVolume, 2, RoundingMode.DOWN);
            BmsYscostInfo shareInfo = new BmsYscostInfo();
            shareInfo.setFreight(costInfo.getFreight().multiply(volumnRadio));
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee().multiply(volumnRadio));
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee().multiply(volumnRadio));
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee().multiply(volumnRadio));
            shareInfo.setExcessFee(costInfo.getExcessFee().multiply(volumnRadio));
            shareInfo.setReduceFee(costInfo.getReduceFee().multiply(volumnRadio));
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().multiply(volumnRadio));
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee().multiply(volumnRadio));
            shareInfo.setReturnFee(costInfo.getReturnFee().multiply(volumnRadio));
            shareInfo.setExceptionFee(costInfo.getExceptionFee().multiply(volumnRadio));
            shareInfo.setOtherCost1(costInfo.getOtherCost1().multiply(volumnRadio));
            shareInfo.setOtherCost2(costInfo.getOtherCost2().multiply(volumnRadio));
            shareInfo.setOtherCost3(costInfo.getOtherCost3().multiply(volumnRadio));
            shareInfo.setOtherCost4(costInfo.getOtherCost4().multiply(volumnRadio));
            shareInfo.setOtherCost5(costInfo.getOtherCost5().multiply(volumnRadio));
            shareInfo.setOtherCost6(costInfo.getOtherCost6().multiply(volumnRadio));
            shareInfo.setOtherCost7(costInfo.getOtherCost7().multiply(volumnRadio));
            shareInfo.setOtherCost8(costInfo.getOtherCost8().multiply(volumnRadio));
            shareInfo.setOtherCost9(costInfo.getOtherCost9().multiply(volumnRadio));
            shareInfo.setOtherCost10(costInfo.getOtherCost10().multiply(volumnRadio));
            shareInfo.setOtherCost11(costInfo.getOtherCost11().multiply(volumnRadio));
            shareInfo.setOtherCost12(costInfo.getOtherCost12().multiply(volumnRadio));
            shareInfo.setShareType(3);
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), shareInfo);
            //倒数第二条
            if (i < orderInfos.size() - 1) {
                runningFreightAmount = runningFreightAmount.add(shareInfo.getFreight());
                runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(shareInfo.getDeliveryFee());
                runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(shareInfo.getUltrafarFee());
                runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(shareInfo.getSuperframesFee());
                runningExcessFeeAmount = runningExcessFeeAmount.add(shareInfo.getExcessFee());
                runningReduceFeeAmount = runningReduceFeeAmount.add(shareInfo.getReduceFee());
                runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(shareInfo.getOutboundsortingFee());
                runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(shareInfo.getShortbargeFee());
                runningReturnFeeAmount = runningReturnFeeAmount.add(shareInfo.getReturnFee());
                runningExceptionFeeAmount = runningExceptionFeeAmount.add(shareInfo.getExceptionFee());
                runningOtherCost1Amount = runningOtherCost1Amount.add(shareInfo.getOtherCost1());
                runningOtherCost2Amount = runningOtherCost2Amount.add(shareInfo.getOtherCost2());
                runningOtherCost3Amount = runningOtherCost3Amount.add(shareInfo.getOtherCost3());
                runningOtherCost4Amount = runningOtherCost4Amount.add(shareInfo.getOtherCost4());
                runningOtherCost5Amount = runningOtherCost5Amount.add(shareInfo.getOtherCost5());
                runningOtherCost6Amount = runningOtherCost6Amount.add(shareInfo.getOtherCost6());
                runningOtherCost7Amount = runningOtherCost7Amount.add(shareInfo.getOtherCost7());
                runningOtherCost8Amount = runningOtherCost8Amount.add(shareInfo.getOtherCost8());
                runningOtherCost9Amount = runningOtherCost9Amount.add(shareInfo.getOtherCost9());
                runningOtherCost10Amount = runningOtherCost10Amount.add(shareInfo.getOtherCost10());
                runningOtherCost11Amount = runningOtherCost11Amount.add(shareInfo.getOtherCost11());
                runningOtherCost12Amount = runningOtherCost12Amount.add(shareInfo.getOtherCost12());
                idx++;
            }
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        AutoYsCodeInfo lastOrderInfo = orderInfos.get(idx);
        BmsYscostInfo lastShareInfo = new BmsYscostInfo();
        lastShareInfo.setFreight(costInfo.getFreight().subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(costInfo.getDeliveryFee().subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(costInfo.getUltrafarFee().subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(costInfo.getSuperframesFee().subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(costInfo.getExcessFee().subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(costInfo.getReduceFee().subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(costInfo.getShortbargeFee().subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(costInfo.getReturnFee().subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(costInfo.getExceptionFee().subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(costInfo.getOtherCost1().subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(costInfo.getOtherCost2().subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(costInfo.getOtherCost3().subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(costInfo.getOtherCost4().subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(costInfo.getOtherCost5().subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(costInfo.getOtherCost6().subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(costInfo.getOtherCost7().subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(costInfo.getOtherCost8().subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(costInfo.getOtherCost9().subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(costInfo.getOtherCost10().subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(costInfo.getOtherCost11().subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(costInfo.getOtherCost12().subtract(runningOtherCost12Amount));
        returnData.put(resultType.equals(2) ? lastOrderInfo.getRelateCode() : lastOrderInfo.getId(), lastShareInfo);
        return returnData;
    }


    /**
     * 按照分摊类型 重新赋值金额数据-应收
     *
     * @param shareType    分摊类型
     * @param mainCostInfo 主表信息
     * @param orderInfos   中间表信息
     * @return {@link List}<{@link BmsYsexpensesMiddle}>
     */
    public static List<BmsYsexpensesMiddle> reassignMiddleAmount(Integer shareType, BmsYscostMainInfo mainCostInfo, List<BmsYsexpensesMiddle> orderInfos) {
        if (CollUtil.isEmpty(orderInfos)) {
            return orderInfos;
        }
        if (shareType.equals(1)) {
            return reassignMiddleAmountByOrder(mainCostInfo, orderInfos);
        }
        if (shareType.equals(2)) {
            return reassignMiddleAmountByWeight(mainCostInfo, orderInfos);
        }
        if (shareType.equals(3)) {
            return reassignMiddleAmountByVolumn(mainCostInfo, orderInfos);
        }
        return orderInfos;
    }

    /**
     * 按照分摊类型 重新赋值金额数据-应付
     *
     * @param shareType    分摊类型
     * @param mainCostInfo 主表信息
     * @param orderInfos   中间表信息
     * @return {@link List}<{@link BmsYsexpensesMiddle}>
     */
    public static List<BmsYfexpensesMiddle> reassignMiddleAmountYf(Integer shareType, BmsYfCostMainInfo mainCostInfo, List<BmsYfexpensesMiddle> orderInfos) {
        if (CollUtil.isEmpty(orderInfos)) {
            return orderInfos;
        }
        if (shareType.equals(1)) {
            return reassignMiddleAmountByOrderYf(mainCostInfo, orderInfos);
        }
        if (shareType.equals(2)) {
            return reassignMiddleAmountByWeightYf(mainCostInfo, orderInfos);
        }
        if (shareType.equals(3)) {
            return reassignMiddleAmountByVolumnYf(mainCostInfo, orderInfos);
        }
        return orderInfos;
    }


    /**
     * 按单重新计算费用数据-应收
     *
     * @param costInfo    成本信息
     * @param middleInfos 中
     * @return {@link List}<{@link BmsYsexpensesMiddle}>
     */
    public static List<BmsYsexpensesMiddle> reassignMiddleAmountByOrder(BmsYscostMainInfo costInfo, List<BmsYsexpensesMiddle> middleInfos) {
        List<BmsYsexpensesMiddle> returnData = new LinkedList<>();
        //单条数据无需分摊
        if (middleInfos.size() == 1) {
            BmsYsexpensesMiddle middle = middleInfos.get(0);
            middle.setFreight(costInfo.getFreight());
            middle.setDeliveryFee(costInfo.getDeliveryFee());
            middle.setUltrafarFee(costInfo.getUltrafarFee());
            middle.setSuperframesFee(costInfo.getSuperframesFee());
            middle.setExcessFee(costInfo.getExcessFee());
            middle.setReduceFee(costInfo.getReduceFee());
            middle.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            middle.setShortbargeFee(costInfo.getShortbargeFee());
            middle.setReturnFee(costInfo.getReturnFee());
            middle.setExceptionFee(costInfo.getExceptionFee());
            middle.setOtherCost1(costInfo.getOtherCost1());
            middle.setOtherCost2(costInfo.getOtherCost2());
            middle.setOtherCost3(costInfo.getOtherCost3());
            middle.setOtherCost4(costInfo.getOtherCost4());
            middle.setOtherCost5(costInfo.getOtherCost5());
            middle.setOtherCost6(costInfo.getOtherCost6());
            middle.setOtherCost7(costInfo.getOtherCost7());
            middle.setOtherCost8(costInfo.getOtherCost8());
            middle.setOtherCost9(costInfo.getOtherCost9());
            middle.setOtherCost10(costInfo.getOtherCost10());
            middle.setOtherCost11(costInfo.getOtherCost11());
            middle.setOtherCost12(costInfo.getOtherCost12());
            middle.setShareType(1);
            middle.setShareAmount(costInfo.getFreight()
                    .add(costInfo.getDeliveryFee())
                    .add(costInfo.getUltrafarFee())
                    .add(costInfo.getSuperframesFee())
                    .add(costInfo.getExcessFee())
                    .add(costInfo.getReduceFee())
                    .add(costInfo.getOutboundsortingFee())
                    .add(costInfo.getShortbargeFee())
                    .add(costInfo.getReturnFee())
                    .add(costInfo.getExceptionFee())
                    .add(costInfo.getOtherCost1())
                    .add(costInfo.getOtherCost2())
                    .add(costInfo.getOtherCost3())
                    .add(costInfo.getOtherCost4())
                    .add(costInfo.getOtherCost5())
                    .add(costInfo.getOtherCost6())
                    .add(costInfo.getOtherCost7())
                    .add(costInfo.getOtherCost8())
                    .add(costInfo.getOtherCost9())
                    .add(costInfo.getOtherCost10())
                    .add(costInfo.getOtherCost11())
                    .add(costInfo.getOtherCost12())
            );
            returnData.add(middle);
            return returnData;
        }
        //金额分摊(向下取整，避免出现最后一条金额非累加)
        BigDecimal avgfreightAmount = BigDecimal.ZERO;
        BigDecimal avgDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal avgUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal avgSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal avgExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal avgReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal avgOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal avgShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal avgReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal avgExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal avgOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost12Amount = BigDecimal.ZERO;

        if (!CollUtil.isEmpty(middleInfos)) {
            avgfreightAmount = costInfo.getFreight().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgDeliveryFeeAmount = costInfo.getDeliveryFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgUltrafarFeeAmount = costInfo.getUltrafarFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgSuperframesFeeAmount = costInfo.getSuperframesFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgExcessFeeAmount = costInfo.getExcessFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgReduceFeeAmount = costInfo.getReduceFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOutboundsortingFeeAmount = costInfo.getOutboundsortingFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgShortbargeFeeAmount = costInfo.getShortbargeFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgReturnFeeAmount = costInfo.getReturnFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgExceptionFeeAmount = costInfo.getExceptionFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost1Amount = costInfo.getOtherCost1().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost2Amount = costInfo.getOtherCost2().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost3Amount = costInfo.getOtherCost3().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost4Amount = costInfo.getOtherCost4().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost5Amount = costInfo.getOtherCost5().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost6Amount = costInfo.getOtherCost6().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost7Amount = costInfo.getOtherCost7().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost8Amount = costInfo.getOtherCost8().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost9Amount = costInfo.getOtherCost9().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost10Amount = costInfo.getOtherCost10().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost11Amount = costInfo.getOtherCost11().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost12Amount = costInfo.getOtherCost12().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
        }
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;

        for (int i = 0; i < middleInfos.size(); i++) {
            BmsYsexpensesMiddle orderInfo = middleInfos.get(i);
            orderInfo.setFreight(avgfreightAmount);
            orderInfo.setDeliveryFee(avgDeliveryFeeAmount);
            orderInfo.setUltrafarFee(avgUltrafarFeeAmount);
            orderInfo.setSuperframesFee(avgSuperframesFeeAmount);
            orderInfo.setExcessFee(avgExcessFeeAmount);
            orderInfo.setReduceFee(avgReduceFeeAmount);
            orderInfo.setOutboundsortingFee(avgOutboundsortingFeeAmount);
            orderInfo.setShortbargeFee(avgShortbargeFeeAmount);
            orderInfo.setReturnFee(avgReturnFeeAmount);
            orderInfo.setExceptionFee(avgExceptionFeeAmount);
            orderInfo.setOtherCost1(avgOtherCost1Amount);
            orderInfo.setOtherCost2(avgOtherCost2Amount);
            orderInfo.setOtherCost3(avgOtherCost3Amount);
            orderInfo.setOtherCost4(avgOtherCost4Amount);
            orderInfo.setOtherCost5(avgOtherCost5Amount);
            orderInfo.setOtherCost6(avgOtherCost6Amount);
            orderInfo.setOtherCost7(avgOtherCost7Amount);
            orderInfo.setOtherCost8(avgOtherCost8Amount);
            orderInfo.setOtherCost9(avgOtherCost9Amount);
            orderInfo.setOtherCost10(avgOtherCost10Amount);
            orderInfo.setOtherCost11(avgOtherCost11Amount);
            orderInfo.setOtherCost12(avgOtherCost12Amount);
            orderInfo.setShareType(1);
            orderInfo.setShareAmount(avgfreightAmount
                    .add(avgDeliveryFeeAmount)
                    .add(avgUltrafarFeeAmount)
                    .add(avgSuperframesFeeAmount)
                    .add(avgExcessFeeAmount)
                    .add(avgReduceFeeAmount)
                    .add(avgOutboundsortingFeeAmount)
                    .add(avgShortbargeFeeAmount)
                    .add(avgReturnFeeAmount)
                    .add(avgExceptionFeeAmount)
                    .add(avgOtherCost1Amount)
                    .add(avgOtherCost2Amount)
                    .add(avgOtherCost3Amount)
                    .add(avgOtherCost4Amount)
                    .add(avgOtherCost5Amount)
                    .add(avgOtherCost6Amount)
                    .add(avgOtherCost7Amount)
                    .add(avgOtherCost8Amount)
                    .add(avgOtherCost9Amount)
                    .add(avgOtherCost10Amount)
                    .add(avgOtherCost11Amount)
                    .add(avgOtherCost12Amount)
            );
            if (i < middleInfos.size() - 1) {
                runningFreightAmount = runningFreightAmount.add(avgfreightAmount);
                runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(avgDeliveryFeeAmount);
                runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(avgUltrafarFeeAmount);
                runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(avgSuperframesFeeAmount);
                runningExcessFeeAmount = runningExcessFeeAmount.add(avgExcessFeeAmount);
                runningReduceFeeAmount = runningReduceFeeAmount.add(avgReduceFeeAmount);
                runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(avgOutboundsortingFeeAmount);
                runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(avgShortbargeFeeAmount);
                runningReturnFeeAmount = runningReturnFeeAmount.add(avgReturnFeeAmount);
                runningExceptionFeeAmount = runningExceptionFeeAmount.add(avgExceptionFeeAmount);
                runningOtherCost1Amount = runningOtherCost1Amount.add(avgOtherCost1Amount);
                runningOtherCost2Amount = runningOtherCost2Amount.add(avgOtherCost2Amount);
                runningOtherCost3Amount = runningOtherCost3Amount.add(avgOtherCost3Amount);
                runningOtherCost4Amount = runningOtherCost4Amount.add(avgOtherCost4Amount);
                runningOtherCost5Amount = runningOtherCost5Amount.add(avgOtherCost5Amount);
                runningOtherCost6Amount = runningOtherCost6Amount.add(avgOtherCost6Amount);
                runningOtherCost7Amount = runningOtherCost7Amount.add(avgOtherCost7Amount);
                runningOtherCost8Amount = runningOtherCost8Amount.add(avgOtherCost8Amount);
                runningOtherCost9Amount = runningOtherCost9Amount.add(avgOtherCost9Amount);
                runningOtherCost10Amount = runningOtherCost10Amount.add(avgOtherCost10Amount);
                runningOtherCost11Amount = runningOtherCost11Amount.add(avgOtherCost11Amount);
                runningOtherCost12Amount = runningOtherCost12Amount.add(avgOtherCost12Amount);
                idx++;
            }
        }
        //拿到最后一个单据信息，将计算求于数均给到最后一条
        BmsYsexpensesMiddle lastOrderInfo = middleInfos.get(idx);
        lastOrderInfo.setFreight(costInfo.getFreight().subtract(runningFreightAmount));
        lastOrderInfo.setDeliveryFee(costInfo.getDeliveryFee().subtract(runningDeliveryFeeAmount));
        lastOrderInfo.setUltrafarFee(costInfo.getUltrafarFee().subtract(runningUltrafarFeeAmount));
        lastOrderInfo.setSuperframesFee(costInfo.getSuperframesFee().subtract(runningSuperframesFeeAmount));
        lastOrderInfo.setExcessFee(costInfo.getExcessFee().subtract(runningExcessFeeAmount));
        lastOrderInfo.setReduceFee(costInfo.getReduceFee().subtract(runningReduceFeeAmount));
        lastOrderInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().subtract(runningOutboundsortingFeeAmount));
        lastOrderInfo.setShortbargeFee(costInfo.getShortbargeFee().subtract(runningShortbargeFeeAmount));
        lastOrderInfo.setReturnFee(costInfo.getReturnFee().subtract(runningReturnFeeAmount));
        lastOrderInfo.setExceptionFee(costInfo.getExceptionFee().subtract(runningExceptionFeeAmount));
        lastOrderInfo.setOtherCost1(costInfo.getOtherCost1().subtract(runningOtherCost1Amount));
        lastOrderInfo.setOtherCost2(costInfo.getOtherCost2().subtract(runningOtherCost2Amount));
        lastOrderInfo.setOtherCost3(costInfo.getOtherCost3().subtract(runningOtherCost3Amount));
        lastOrderInfo.setOtherCost4(costInfo.getOtherCost4().subtract(runningOtherCost4Amount));
        lastOrderInfo.setOtherCost5(costInfo.getOtherCost5().subtract(runningOtherCost5Amount));
        lastOrderInfo.setOtherCost6(costInfo.getOtherCost6().subtract(runningOtherCost6Amount));
        lastOrderInfo.setOtherCost7(costInfo.getOtherCost7().subtract(runningOtherCost7Amount));
        lastOrderInfo.setOtherCost8(costInfo.getOtherCost8().subtract(runningOtherCost8Amount));
        lastOrderInfo.setOtherCost9(costInfo.getOtherCost9().subtract(runningOtherCost9Amount));
        lastOrderInfo.setOtherCost10(costInfo.getOtherCost10().subtract(runningOtherCost10Amount));
        lastOrderInfo.setOtherCost11(costInfo.getOtherCost11().subtract(runningOtherCost11Amount));
        lastOrderInfo.setOtherCost12(costInfo.getOtherCost12().subtract(runningOtherCost12Amount));
        returnData.addAll(middleInfos);
        return returnData;
    }

    /**
     * 按单重新计算费用数据-应付
     *
     * @param costInfo    成本信息
     * @param middleInfos 中
     * @return {@link List}<{@link BmsYsexpensesMiddle}>
     */
    public static List<BmsYfexpensesMiddle> reassignMiddleAmountByOrderYf(BmsYfCostMainInfo costInfo, List<BmsYfexpensesMiddle> middleInfos) {
        List<BmsYfexpensesMiddle> returnData = new LinkedList<>();
        //单条数据无需分摊
        if (middleInfos.size() == 1) {
            BmsYfexpensesMiddle middle = middleInfos.get(0);
            middle.setFreight(costInfo.getFreight());
            middle.setDeliveryFee(costInfo.getDeliveryFee());
            middle.setUltrafarFee(costInfo.getUltrafarFee());
            middle.setSuperframesFee(costInfo.getSuperframesFee());
            middle.setExcessFee(costInfo.getExcessFee());
            middle.setReduceFee(costInfo.getReduceFee());
            middle.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            middle.setShortbargeFee(costInfo.getShortbargeFee());
            middle.setReturnFee(costInfo.getReturnFee());
            middle.setExceptionFee(costInfo.getExceptionFee());
            middle.setOtherCost1(costInfo.getOtherCost1());
            middle.setOtherCost2(costInfo.getOtherCost2());
            middle.setOtherCost3(costInfo.getOtherCost3());
            middle.setOtherCost4(costInfo.getOtherCost4());
            middle.setOtherCost5(costInfo.getOtherCost5());
            middle.setOtherCost6(costInfo.getOtherCost6());
            middle.setOtherCost7(costInfo.getOtherCost7());
            middle.setOtherCost8(costInfo.getOtherCost8());
            middle.setOtherCost9(costInfo.getOtherCost9());
            middle.setOtherCost10(costInfo.getOtherCost10());
            middle.setOtherCost11(costInfo.getOtherCost11());
            middle.setOtherCost12(costInfo.getOtherCost12());
            middle.setShareType(1);
            middle.setShareAmount(costInfo.getFreight()
                    .add(costInfo.getDeliveryFee())
                    .add(costInfo.getUltrafarFee())
                    .add(costInfo.getSuperframesFee())
                    .add(costInfo.getExcessFee())
                    .add(costInfo.getReduceFee())
                    .add(costInfo.getOutboundsortingFee())
                    .add(costInfo.getShortbargeFee())
                    .add(costInfo.getReturnFee())
                    .add(costInfo.getExceptionFee())
                    .add(costInfo.getOtherCost1())
                    .add(costInfo.getOtherCost2())
                    .add(costInfo.getOtherCost3())
                    .add(costInfo.getOtherCost4())
                    .add(costInfo.getOtherCost5())
                    .add(costInfo.getOtherCost6())
                    .add(costInfo.getOtherCost7())
                    .add(costInfo.getOtherCost8())
                    .add(costInfo.getOtherCost9())
                    .add(costInfo.getOtherCost10())
                    .add(costInfo.getOtherCost11())
                    .add(costInfo.getOtherCost12())
            );
            returnData.add(middle);
            return returnData;
        }
        //金额分摊(向下取整，避免出现最后一条金额非累加)
        BigDecimal avgfreightAmount = BigDecimal.ZERO;
        BigDecimal avgDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal avgUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal avgSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal avgExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal avgReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal avgOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal avgShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal avgReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal avgExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal avgOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost12Amount = BigDecimal.ZERO;

        if (!CollUtil.isEmpty(middleInfos)) {
            avgfreightAmount = costInfo.getFreight().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgDeliveryFeeAmount = costInfo.getDeliveryFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgUltrafarFeeAmount = costInfo.getUltrafarFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgSuperframesFeeAmount = costInfo.getSuperframesFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgExcessFeeAmount = costInfo.getExcessFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgReduceFeeAmount = costInfo.getReduceFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOutboundsortingFeeAmount = costInfo.getOutboundsortingFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgShortbargeFeeAmount = costInfo.getShortbargeFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgReturnFeeAmount = costInfo.getReturnFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgExceptionFeeAmount = costInfo.getExceptionFee().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost1Amount = costInfo.getOtherCost1().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost2Amount = costInfo.getOtherCost2().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost3Amount = costInfo.getOtherCost3().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost4Amount = costInfo.getOtherCost4().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost5Amount = costInfo.getOtherCost5().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost6Amount = costInfo.getOtherCost6().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost7Amount = costInfo.getOtherCost7().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost8Amount = costInfo.getOtherCost8().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost9Amount = costInfo.getOtherCost9().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost10Amount = costInfo.getOtherCost10().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost11Amount = costInfo.getOtherCost11().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost12Amount = costInfo.getOtherCost12().divide(BigDecimal.valueOf(middleInfos.size()), 2, RoundingMode.DOWN);
        }
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;

        for (int i = 0; i < middleInfos.size(); i++) {
            BmsYfexpensesMiddle orderInfo = middleInfos.get(i);
            orderInfo.setFreight(avgfreightAmount);
            orderInfo.setDeliveryFee(avgDeliveryFeeAmount);
            orderInfo.setUltrafarFee(avgUltrafarFeeAmount);
            orderInfo.setSuperframesFee(avgSuperframesFeeAmount);
            orderInfo.setExcessFee(avgExcessFeeAmount);
            orderInfo.setReduceFee(avgReduceFeeAmount);
            orderInfo.setOutboundsortingFee(avgOutboundsortingFeeAmount);
            orderInfo.setShortbargeFee(avgShortbargeFeeAmount);
            orderInfo.setReturnFee(avgReturnFeeAmount);
            orderInfo.setExceptionFee(avgExceptionFeeAmount);
            orderInfo.setOtherCost1(avgOtherCost1Amount);
            orderInfo.setOtherCost2(avgOtherCost2Amount);
            orderInfo.setOtherCost3(avgOtherCost3Amount);
            orderInfo.setOtherCost4(avgOtherCost4Amount);
            orderInfo.setOtherCost5(avgOtherCost5Amount);
            orderInfo.setOtherCost6(avgOtherCost6Amount);
            orderInfo.setOtherCost7(avgOtherCost7Amount);
            orderInfo.setOtherCost8(avgOtherCost8Amount);
            orderInfo.setOtherCost9(avgOtherCost9Amount);
            orderInfo.setOtherCost10(avgOtherCost10Amount);
            orderInfo.setOtherCost11(avgOtherCost11Amount);
            orderInfo.setOtherCost12(avgOtherCost12Amount);
            orderInfo.setShareType(1);
            orderInfo.setShareAmount(avgfreightAmount
                    .add(avgDeliveryFeeAmount)
                    .add(avgUltrafarFeeAmount)
                    .add(avgSuperframesFeeAmount)
                    .add(avgExcessFeeAmount)
                    .add(avgReduceFeeAmount)
                    .add(avgOutboundsortingFeeAmount)
                    .add(avgShortbargeFeeAmount)
                    .add(avgReturnFeeAmount)
                    .add(avgExceptionFeeAmount)
                    .add(avgOtherCost1Amount)
                    .add(avgOtherCost2Amount)
                    .add(avgOtherCost3Amount)
                    .add(avgOtherCost4Amount)
                    .add(avgOtherCost5Amount)
                    .add(avgOtherCost6Amount)
                    .add(avgOtherCost7Amount)
                    .add(avgOtherCost8Amount)
                    .add(avgOtherCost9Amount)
                    .add(avgOtherCost10Amount)
                    .add(avgOtherCost11Amount)
                    .add(avgOtherCost12Amount)
            );
            if (i < middleInfos.size() - 1) {
                runningFreightAmount = runningFreightAmount.add(avgfreightAmount);
                runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(avgDeliveryFeeAmount);
                runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(avgUltrafarFeeAmount);
                runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(avgSuperframesFeeAmount);
                runningExcessFeeAmount = runningExcessFeeAmount.add(avgExcessFeeAmount);
                runningReduceFeeAmount = runningReduceFeeAmount.add(avgReduceFeeAmount);
                runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(avgOutboundsortingFeeAmount);
                runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(avgShortbargeFeeAmount);
                runningReturnFeeAmount = runningReturnFeeAmount.add(avgReturnFeeAmount);
                runningExceptionFeeAmount = runningExceptionFeeAmount.add(avgExceptionFeeAmount);
                runningOtherCost1Amount = runningOtherCost1Amount.add(avgOtherCost1Amount);
                runningOtherCost2Amount = runningOtherCost2Amount.add(avgOtherCost2Amount);
                runningOtherCost3Amount = runningOtherCost3Amount.add(avgOtherCost3Amount);
                runningOtherCost4Amount = runningOtherCost4Amount.add(avgOtherCost4Amount);
                runningOtherCost5Amount = runningOtherCost5Amount.add(avgOtherCost5Amount);
                runningOtherCost6Amount = runningOtherCost6Amount.add(avgOtherCost6Amount);
                runningOtherCost7Amount = runningOtherCost7Amount.add(avgOtherCost7Amount);
                runningOtherCost8Amount = runningOtherCost8Amount.add(avgOtherCost8Amount);
                runningOtherCost9Amount = runningOtherCost9Amount.add(avgOtherCost9Amount);
                runningOtherCost10Amount = runningOtherCost10Amount.add(avgOtherCost10Amount);
                runningOtherCost11Amount = runningOtherCost11Amount.add(avgOtherCost11Amount);
                runningOtherCost12Amount = runningOtherCost12Amount.add(avgOtherCost12Amount);
                idx++;
            }
        }
        //拿到最后一个单据信息，将计算求于数均给到最后一条
        BmsYfexpensesMiddle lastOrderInfo = middleInfos.get(idx);
        lastOrderInfo.setFreight(costInfo.getFreight().subtract(runningFreightAmount));
        lastOrderInfo.setDeliveryFee(costInfo.getDeliveryFee().subtract(runningDeliveryFeeAmount));
        lastOrderInfo.setUltrafarFee(costInfo.getUltrafarFee().subtract(runningUltrafarFeeAmount));
        lastOrderInfo.setSuperframesFee(costInfo.getSuperframesFee().subtract(runningSuperframesFeeAmount));
        lastOrderInfo.setExcessFee(costInfo.getExcessFee().subtract(runningExcessFeeAmount));
        lastOrderInfo.setReduceFee(costInfo.getReduceFee().subtract(runningReduceFeeAmount));
        lastOrderInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().subtract(runningOutboundsortingFeeAmount));
        lastOrderInfo.setShortbargeFee(costInfo.getShortbargeFee().subtract(runningShortbargeFeeAmount));
        lastOrderInfo.setReturnFee(costInfo.getReturnFee().subtract(runningReturnFeeAmount));
        lastOrderInfo.setExceptionFee(costInfo.getExceptionFee().subtract(runningExceptionFeeAmount));
        lastOrderInfo.setOtherCost1(costInfo.getOtherCost1().subtract(runningOtherCost1Amount));
        lastOrderInfo.setOtherCost2(costInfo.getOtherCost2().subtract(runningOtherCost2Amount));
        lastOrderInfo.setOtherCost3(costInfo.getOtherCost3().subtract(runningOtherCost3Amount));
        lastOrderInfo.setOtherCost4(costInfo.getOtherCost4().subtract(runningOtherCost4Amount));
        lastOrderInfo.setOtherCost5(costInfo.getOtherCost5().subtract(runningOtherCost5Amount));
        lastOrderInfo.setOtherCost6(costInfo.getOtherCost6().subtract(runningOtherCost6Amount));
        lastOrderInfo.setOtherCost7(costInfo.getOtherCost7().subtract(runningOtherCost7Amount));
        lastOrderInfo.setOtherCost8(costInfo.getOtherCost8().subtract(runningOtherCost8Amount));
        lastOrderInfo.setOtherCost9(costInfo.getOtherCost9().subtract(runningOtherCost9Amount));
        lastOrderInfo.setOtherCost10(costInfo.getOtherCost10().subtract(runningOtherCost10Amount));
        lastOrderInfo.setOtherCost11(costInfo.getOtherCost11().subtract(runningOtherCost11Amount));
        lastOrderInfo.setOtherCost12(costInfo.getOtherCost12().subtract(runningOtherCost12Amount));
        returnData.addAll(middleInfos);
        return returnData;
    }

    /**
     * 按重量重新计算费用数据-应收
     *
     * @param costInfo    成本信息
     * @param middleInfos 订单信息
     * @return {@link List}<{@link BmsYsexpensesMiddle}>
     */
    public static List<BmsYsexpensesMiddle> reassignMiddleAmountByWeight(BmsYscostMainInfo costInfo, List<BmsYsexpensesMiddle> middleInfos) {
        List<BmsYsexpensesMiddle> returnData = new ArrayList<>();
        //单条数据无需分摊
        if (middleInfos.size() == 1) {
            BmsYsexpensesMiddle orderInfo = middleInfos.get(0);
            orderInfo.setFreight(costInfo.getFreight());
            orderInfo.setDeliveryFee(costInfo.getDeliveryFee());
            orderInfo.setUltrafarFee(costInfo.getUltrafarFee());
            orderInfo.setSuperframesFee(costInfo.getSuperframesFee());
            orderInfo.setExcessFee(costInfo.getExcessFee());
            orderInfo.setReduceFee(costInfo.getReduceFee());
            orderInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            orderInfo.setShortbargeFee(costInfo.getShortbargeFee());
            orderInfo.setReturnFee(costInfo.getReturnFee());
            orderInfo.setExceptionFee(costInfo.getExceptionFee());
            orderInfo.setOtherCost1(costInfo.getOtherCost1());
            orderInfo.setOtherCost2(costInfo.getOtherCost2());
            orderInfo.setOtherCost3(costInfo.getOtherCost3());
            orderInfo.setOtherCost4(costInfo.getOtherCost4());
            orderInfo.setOtherCost5(costInfo.getOtherCost5());
            orderInfo.setOtherCost6(costInfo.getOtherCost6());
            orderInfo.setOtherCost7(costInfo.getOtherCost7());
            orderInfo.setOtherCost8(costInfo.getOtherCost8());
            orderInfo.setOtherCost9(costInfo.getOtherCost9());
            orderInfo.setOtherCost10(costInfo.getOtherCost10());
            orderInfo.setOtherCost11(costInfo.getOtherCost11());
            orderInfo.setOtherCost12(costInfo.getOtherCost12());
            orderInfo.setShareType(2);
            orderInfo.setShareAmount(costInfo.getFreight()
                    .add(costInfo.getDeliveryFee())
                    .add(costInfo.getUltrafarFee())
                    .add(costInfo.getSuperframesFee())
                    .add(costInfo.getExcessFee())
                    .add(costInfo.getReduceFee())
                    .add(costInfo.getOutboundsortingFee())
                    .add(costInfo.getShortbargeFee())
                    .add(costInfo.getReturnFee())
                    .add(costInfo.getExceptionFee())
                    .add(costInfo.getOtherCost1())
                    .add(costInfo.getOtherCost2())
                    .add(costInfo.getOtherCost3())
                    .add(costInfo.getOtherCost4())
                    .add(costInfo.getOtherCost5())
                    .add(costInfo.getOtherCost6())
                    .add(costInfo.getOtherCost7())
                    .add(costInfo.getOtherCost8())
                    .add(costInfo.getOtherCost9())
                    .add(costInfo.getOtherCost10())
                    .add(costInfo.getOtherCost11())
                    .add(costInfo.getOtherCost12())
            );
            returnData.add(orderInfo);
            return returnData;
        }

        BigDecimal totalWeight = BigDecimal.ZERO;
        //获取总重量
        for (BmsYsexpensesMiddle orderInfo : middleInfos) {
            totalWeight = totalWeight.add(orderInfo.getTotalWeight() != null ? orderInfo.getTotalWeight() : BigDecimal.ZERO);
        }

        //分摊金额
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;
        for (int i = 0; i < middleInfos.size(); i++) {
            BmsYsexpensesMiddle orderInfo = middleInfos.get(i);
            BigDecimal weightRadio = orderInfo.getTotalWeight().divide(totalWeight, 2, RoundingMode.DOWN);
            orderInfo.setFreight(costInfo.getFreight().multiply(weightRadio));
            orderInfo.setDeliveryFee(costInfo.getDeliveryFee().multiply(weightRadio));
            orderInfo.setUltrafarFee(costInfo.getUltrafarFee().multiply(weightRadio));
            orderInfo.setSuperframesFee(costInfo.getSuperframesFee().multiply(weightRadio));
            orderInfo.setExcessFee(costInfo.getExcessFee().multiply(weightRadio));
            orderInfo.setReduceFee(costInfo.getReduceFee().multiply(weightRadio));
            orderInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().multiply(weightRadio));
            orderInfo.setShortbargeFee(costInfo.getShortbargeFee().multiply(weightRadio));
            orderInfo.setReturnFee(costInfo.getReturnFee().multiply(weightRadio));
            orderInfo.setExceptionFee(costInfo.getExceptionFee().multiply(weightRadio));
            orderInfo.setOtherCost1(costInfo.getOtherCost1().multiply(weightRadio));
            orderInfo.setOtherCost2(costInfo.getOtherCost2().multiply(weightRadio));
            orderInfo.setOtherCost3(costInfo.getOtherCost3().multiply(weightRadio));
            orderInfo.setOtherCost4(costInfo.getOtherCost4().multiply(weightRadio));
            orderInfo.setOtherCost5(costInfo.getOtherCost5().multiply(weightRadio));
            orderInfo.setOtherCost6(costInfo.getOtherCost6().multiply(weightRadio));
            orderInfo.setOtherCost7(costInfo.getOtherCost7().multiply(weightRadio));
            orderInfo.setOtherCost8(costInfo.getOtherCost8().multiply(weightRadio));
            orderInfo.setOtherCost9(costInfo.getOtherCost9().multiply(weightRadio));
            orderInfo.setOtherCost10(costInfo.getOtherCost10().multiply(weightRadio));
            orderInfo.setOtherCost11(costInfo.getOtherCost11().multiply(weightRadio));
            orderInfo.setOtherCost12(costInfo.getOtherCost12().multiply(weightRadio));
            orderInfo.setShareType(2);
            orderInfo.setShareAmount(orderInfo.getFreight()
                    .add(orderInfo.getDeliveryFee())
                    .add(orderInfo.getUltrafarFee())
                    .add(orderInfo.getSuperframesFee())
                    .add(orderInfo.getExcessFee())
                    .add(orderInfo.getReduceFee())
                    .add(orderInfo.getOutboundsortingFee())
                    .add(orderInfo.getShortbargeFee())
                    .add(orderInfo.getReturnFee())
                    .add(orderInfo.getExceptionFee())
                    .add(orderInfo.getOtherCost1())
                    .add(orderInfo.getOtherCost2())
                    .add(orderInfo.getOtherCost3())
                    .add(orderInfo.getOtherCost4())
                    .add(orderInfo.getOtherCost5())
                    .add(orderInfo.getOtherCost6())
                    .add(orderInfo.getOtherCost7())
                    .add(orderInfo.getOtherCost8())
                    .add(orderInfo.getOtherCost9())
                    .add(orderInfo.getOtherCost10())
                    .add(orderInfo.getOtherCost11())
                    .add(orderInfo.getOtherCost12())
            );
            //倒数第二条
            if (i < middleInfos.size() - 1) {
                runningFreightAmount = runningFreightAmount.add(orderInfo.getFreight());
                runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(orderInfo.getDeliveryFee());
                runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(orderInfo.getUltrafarFee());
                runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(orderInfo.getSuperframesFee());
                runningExcessFeeAmount = runningExcessFeeAmount.add(orderInfo.getExcessFee());
                runningReduceFeeAmount = runningReduceFeeAmount.add(orderInfo.getReduceFee());
                runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(orderInfo.getOutboundsortingFee());
                runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(orderInfo.getShortbargeFee());
                runningReturnFeeAmount = runningReturnFeeAmount.add(orderInfo.getReturnFee());
                runningExceptionFeeAmount = runningExceptionFeeAmount.add(orderInfo.getExceptionFee());
                runningOtherCost1Amount = runningOtherCost1Amount.add(orderInfo.getOtherCost1());
                runningOtherCost2Amount = runningOtherCost2Amount.add(orderInfo.getOtherCost2());
                runningOtherCost3Amount = runningOtherCost3Amount.add(orderInfo.getOtherCost3());
                runningOtherCost4Amount = runningOtherCost4Amount.add(orderInfo.getOtherCost4());
                runningOtherCost5Amount = runningOtherCost5Amount.add(orderInfo.getOtherCost5());
                runningOtherCost6Amount = runningOtherCost6Amount.add(orderInfo.getOtherCost6());
                runningOtherCost7Amount = runningOtherCost7Amount.add(orderInfo.getOtherCost7());
                runningOtherCost8Amount = runningOtherCost8Amount.add(orderInfo.getOtherCost8());
                runningOtherCost9Amount = runningOtherCost9Amount.add(orderInfo.getOtherCost9());
                runningOtherCost10Amount = runningOtherCost10Amount.add(orderInfo.getOtherCost10());
                runningOtherCost11Amount = runningOtherCost11Amount.add(orderInfo.getOtherCost11());
                runningOtherCost12Amount = runningOtherCost12Amount.add(orderInfo.getOtherCost12());
                idx++;
            }
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        BmsYsexpensesMiddle lastOrderInfo = middleInfos.get(idx);
        lastOrderInfo.setFreight(costInfo.getFreight().subtract(runningFreightAmount));
        lastOrderInfo.setDeliveryFee(costInfo.getDeliveryFee().subtract(runningDeliveryFeeAmount));
        lastOrderInfo.setUltrafarFee(costInfo.getUltrafarFee().subtract(runningUltrafarFeeAmount));
        lastOrderInfo.setSuperframesFee(costInfo.getSuperframesFee().subtract(runningSuperframesFeeAmount));
        lastOrderInfo.setExcessFee(costInfo.getExcessFee().subtract(runningExcessFeeAmount));
        lastOrderInfo.setReduceFee(costInfo.getReduceFee().subtract(runningReduceFeeAmount));
        lastOrderInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().subtract(runningOutboundsortingFeeAmount));
        lastOrderInfo.setShortbargeFee(costInfo.getShortbargeFee().subtract(runningShortbargeFeeAmount));
        lastOrderInfo.setReturnFee(costInfo.getReturnFee().subtract(runningReturnFeeAmount));
        lastOrderInfo.setExceptionFee(costInfo.getExceptionFee().subtract(runningExceptionFeeAmount));
        lastOrderInfo.setOtherCost1(costInfo.getOtherCost1().subtract(runningOtherCost1Amount));
        lastOrderInfo.setOtherCost2(costInfo.getOtherCost2().subtract(runningOtherCost2Amount));
        lastOrderInfo.setOtherCost3(costInfo.getOtherCost3().subtract(runningOtherCost3Amount));
        lastOrderInfo.setOtherCost4(costInfo.getOtherCost4().subtract(runningOtherCost4Amount));
        lastOrderInfo.setOtherCost5(costInfo.getOtherCost5().subtract(runningOtherCost5Amount));
        lastOrderInfo.setOtherCost6(costInfo.getOtherCost6().subtract(runningOtherCost6Amount));
        lastOrderInfo.setOtherCost7(costInfo.getOtherCost7().subtract(runningOtherCost7Amount));
        lastOrderInfo.setOtherCost8(costInfo.getOtherCost8().subtract(runningOtherCost8Amount));
        lastOrderInfo.setOtherCost9(costInfo.getOtherCost9().subtract(runningOtherCost9Amount));
        lastOrderInfo.setOtherCost10(costInfo.getOtherCost10().subtract(runningOtherCost10Amount));
        lastOrderInfo.setOtherCost11(costInfo.getOtherCost11().subtract(runningOtherCost11Amount));
        lastOrderInfo.setOtherCost12(costInfo.getOtherCost12().subtract(runningOtherCost12Amount));
        returnData.addAll(middleInfos);
        return returnData;
    }

    /**
     * 按重量重新计算费用数据-应付
     *
     * @param costInfo    成本信息
     * @param middleInfos 订单信息
     * @return {@link List}<{@link BmsYsexpensesMiddle}>
     */
    public static List<BmsYfexpensesMiddle> reassignMiddleAmountByWeightYf(BmsYfCostMainInfo costInfo, List<BmsYfexpensesMiddle> middleInfos) {
        List<BmsYfexpensesMiddle> returnData = new ArrayList<>();
        //单条数据无需分摊
        if (middleInfos.size() == 1) {
            BmsYfexpensesMiddle orderInfo = middleInfos.get(0);
            orderInfo.setFreight(costInfo.getFreight());
            orderInfo.setDeliveryFee(costInfo.getDeliveryFee());
            orderInfo.setUltrafarFee(costInfo.getUltrafarFee());
            orderInfo.setSuperframesFee(costInfo.getSuperframesFee());
            orderInfo.setExcessFee(costInfo.getExcessFee());
            orderInfo.setReduceFee(costInfo.getReduceFee());
            orderInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            orderInfo.setShortbargeFee(costInfo.getShortbargeFee());
            orderInfo.setReturnFee(costInfo.getReturnFee());
            orderInfo.setExceptionFee(costInfo.getExceptionFee());
            orderInfo.setOtherCost1(costInfo.getOtherCost1());
            orderInfo.setOtherCost2(costInfo.getOtherCost2());
            orderInfo.setOtherCost3(costInfo.getOtherCost3());
            orderInfo.setOtherCost4(costInfo.getOtherCost4());
            orderInfo.setOtherCost5(costInfo.getOtherCost5());
            orderInfo.setOtherCost6(costInfo.getOtherCost6());
            orderInfo.setOtherCost7(costInfo.getOtherCost7());
            orderInfo.setOtherCost8(costInfo.getOtherCost8());
            orderInfo.setOtherCost9(costInfo.getOtherCost9());
            orderInfo.setOtherCost10(costInfo.getOtherCost10());
            orderInfo.setOtherCost11(costInfo.getOtherCost11());
            orderInfo.setOtherCost12(costInfo.getOtherCost12());
            orderInfo.setShareType(2);
            orderInfo.setShareAmount(costInfo.getFreight()
                    .add(costInfo.getDeliveryFee())
                    .add(costInfo.getUltrafarFee())
                    .add(costInfo.getSuperframesFee())
                    .add(costInfo.getExcessFee())
                    .add(costInfo.getReduceFee())
                    .add(costInfo.getOutboundsortingFee())
                    .add(costInfo.getShortbargeFee())
                    .add(costInfo.getReturnFee())
                    .add(costInfo.getExceptionFee())
                    .add(costInfo.getOtherCost1())
                    .add(costInfo.getOtherCost2())
                    .add(costInfo.getOtherCost3())
                    .add(costInfo.getOtherCost4())
                    .add(costInfo.getOtherCost5())
                    .add(costInfo.getOtherCost6())
                    .add(costInfo.getOtherCost7())
                    .add(costInfo.getOtherCost8())
                    .add(costInfo.getOtherCost9())
                    .add(costInfo.getOtherCost10())
                    .add(costInfo.getOtherCost11())
                    .add(costInfo.getOtherCost12())
            );
            returnData.add(orderInfo);
            return returnData;
        }

        BigDecimal totalWeight = BigDecimal.ZERO;
        //获取总重量
        for (BmsYfexpensesMiddle orderInfo : middleInfos) {
            totalWeight = totalWeight.add(orderInfo.getTotalWeight() != null ? orderInfo.getTotalWeight() : BigDecimal.ZERO);
        }

        //分摊金额
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;
        for (int i = 0; i < middleInfos.size(); i++) {
            BmsYfexpensesMiddle orderInfo = middleInfos.get(i);
            BigDecimal weightRadio = orderInfo.getTotalWeight().divide(totalWeight, 2, RoundingMode.DOWN);
            orderInfo.setFreight(costInfo.getFreight().multiply(weightRadio));
            orderInfo.setDeliveryFee(costInfo.getDeliveryFee().multiply(weightRadio));
            orderInfo.setUltrafarFee(costInfo.getUltrafarFee().multiply(weightRadio));
            orderInfo.setSuperframesFee(costInfo.getSuperframesFee().multiply(weightRadio));
            orderInfo.setExcessFee(costInfo.getExcessFee().multiply(weightRadio));
            orderInfo.setReduceFee(costInfo.getReduceFee().multiply(weightRadio));
            orderInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().multiply(weightRadio));
            orderInfo.setShortbargeFee(costInfo.getShortbargeFee().multiply(weightRadio));
            orderInfo.setReturnFee(costInfo.getReturnFee().multiply(weightRadio));
            orderInfo.setExceptionFee(costInfo.getExceptionFee().multiply(weightRadio));
            orderInfo.setOtherCost1(costInfo.getOtherCost1().multiply(weightRadio));
            orderInfo.setOtherCost2(costInfo.getOtherCost2().multiply(weightRadio));
            orderInfo.setOtherCost3(costInfo.getOtherCost3().multiply(weightRadio));
            orderInfo.setOtherCost4(costInfo.getOtherCost4().multiply(weightRadio));
            orderInfo.setOtherCost5(costInfo.getOtherCost5().multiply(weightRadio));
            orderInfo.setOtherCost6(costInfo.getOtherCost6().multiply(weightRadio));
            orderInfo.setOtherCost7(costInfo.getOtherCost7().multiply(weightRadio));
            orderInfo.setOtherCost8(costInfo.getOtherCost8().multiply(weightRadio));
            orderInfo.setOtherCost9(costInfo.getOtherCost9().multiply(weightRadio));
            orderInfo.setOtherCost10(costInfo.getOtherCost10().multiply(weightRadio));
            orderInfo.setOtherCost11(costInfo.getOtherCost11().multiply(weightRadio));
            orderInfo.setOtherCost12(costInfo.getOtherCost12().multiply(weightRadio));
            orderInfo.setShareType(2);
            orderInfo.setShareAmount(orderInfo.getFreight()
                    .add(orderInfo.getDeliveryFee())
                    .add(orderInfo.getUltrafarFee())
                    .add(orderInfo.getSuperframesFee())
                    .add(orderInfo.getExcessFee())
                    .add(orderInfo.getReduceFee())
                    .add(orderInfo.getOutboundsortingFee())
                    .add(orderInfo.getShortbargeFee())
                    .add(orderInfo.getReturnFee())
                    .add(orderInfo.getExceptionFee())
                    .add(orderInfo.getOtherCost1())
                    .add(orderInfo.getOtherCost2())
                    .add(orderInfo.getOtherCost3())
                    .add(orderInfo.getOtherCost4())
                    .add(orderInfo.getOtherCost5())
                    .add(orderInfo.getOtherCost6())
                    .add(orderInfo.getOtherCost7())
                    .add(orderInfo.getOtherCost8())
                    .add(orderInfo.getOtherCost9())
                    .add(orderInfo.getOtherCost10())
                    .add(orderInfo.getOtherCost11())
                    .add(orderInfo.getOtherCost12())
            );
            //倒数第二条
            if (i < middleInfos.size() - 1) {
                runningFreightAmount = runningFreightAmount.add(orderInfo.getFreight());
                runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(orderInfo.getDeliveryFee());
                runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(orderInfo.getUltrafarFee());
                runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(orderInfo.getSuperframesFee());
                runningExcessFeeAmount = runningExcessFeeAmount.add(orderInfo.getExcessFee());
                runningReduceFeeAmount = runningReduceFeeAmount.add(orderInfo.getReduceFee());
                runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(orderInfo.getOutboundsortingFee());
                runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(orderInfo.getShortbargeFee());
                runningReturnFeeAmount = runningReturnFeeAmount.add(orderInfo.getReturnFee());
                runningExceptionFeeAmount = runningExceptionFeeAmount.add(orderInfo.getExceptionFee());
                runningOtherCost1Amount = runningOtherCost1Amount.add(orderInfo.getOtherCost1());
                runningOtherCost2Amount = runningOtherCost2Amount.add(orderInfo.getOtherCost2());
                runningOtherCost3Amount = runningOtherCost3Amount.add(orderInfo.getOtherCost3());
                runningOtherCost4Amount = runningOtherCost4Amount.add(orderInfo.getOtherCost4());
                runningOtherCost5Amount = runningOtherCost5Amount.add(orderInfo.getOtherCost5());
                runningOtherCost6Amount = runningOtherCost6Amount.add(orderInfo.getOtherCost6());
                runningOtherCost7Amount = runningOtherCost7Amount.add(orderInfo.getOtherCost7());
                runningOtherCost8Amount = runningOtherCost8Amount.add(orderInfo.getOtherCost8());
                runningOtherCost9Amount = runningOtherCost9Amount.add(orderInfo.getOtherCost9());
                runningOtherCost10Amount = runningOtherCost10Amount.add(orderInfo.getOtherCost10());
                runningOtherCost11Amount = runningOtherCost11Amount.add(orderInfo.getOtherCost11());
                runningOtherCost12Amount = runningOtherCost12Amount.add(orderInfo.getOtherCost12());
                idx++;
            }
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        BmsYfexpensesMiddle lastOrderInfo = middleInfos.get(idx);
        lastOrderInfo.setFreight(costInfo.getFreight().subtract(runningFreightAmount));
        lastOrderInfo.setDeliveryFee(costInfo.getDeliveryFee().subtract(runningDeliveryFeeAmount));
        lastOrderInfo.setUltrafarFee(costInfo.getUltrafarFee().subtract(runningUltrafarFeeAmount));
        lastOrderInfo.setSuperframesFee(costInfo.getSuperframesFee().subtract(runningSuperframesFeeAmount));
        lastOrderInfo.setExcessFee(costInfo.getExcessFee().subtract(runningExcessFeeAmount));
        lastOrderInfo.setReduceFee(costInfo.getReduceFee().subtract(runningReduceFeeAmount));
        lastOrderInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().subtract(runningOutboundsortingFeeAmount));
        lastOrderInfo.setShortbargeFee(costInfo.getShortbargeFee().subtract(runningShortbargeFeeAmount));
        lastOrderInfo.setReturnFee(costInfo.getReturnFee().subtract(runningReturnFeeAmount));
        lastOrderInfo.setExceptionFee(costInfo.getExceptionFee().subtract(runningExceptionFeeAmount));
        lastOrderInfo.setOtherCost1(costInfo.getOtherCost1().subtract(runningOtherCost1Amount));
        lastOrderInfo.setOtherCost2(costInfo.getOtherCost2().subtract(runningOtherCost2Amount));
        lastOrderInfo.setOtherCost3(costInfo.getOtherCost3().subtract(runningOtherCost3Amount));
        lastOrderInfo.setOtherCost4(costInfo.getOtherCost4().subtract(runningOtherCost4Amount));
        lastOrderInfo.setOtherCost5(costInfo.getOtherCost5().subtract(runningOtherCost5Amount));
        lastOrderInfo.setOtherCost6(costInfo.getOtherCost6().subtract(runningOtherCost6Amount));
        lastOrderInfo.setOtherCost7(costInfo.getOtherCost7().subtract(runningOtherCost7Amount));
        lastOrderInfo.setOtherCost8(costInfo.getOtherCost8().subtract(runningOtherCost8Amount));
        lastOrderInfo.setOtherCost9(costInfo.getOtherCost9().subtract(runningOtherCost9Amount));
        lastOrderInfo.setOtherCost10(costInfo.getOtherCost10().subtract(runningOtherCost10Amount));
        lastOrderInfo.setOtherCost11(costInfo.getOtherCost11().subtract(runningOtherCost11Amount));
        lastOrderInfo.setOtherCost12(costInfo.getOtherCost12().subtract(runningOtherCost12Amount));
        returnData.addAll(middleInfos);
        return returnData;
    }

    /**
     * 按体积重新计算费用数据-应收
     *
     * @param costInfo    成本信息
     * @param middleInfos 订单信息
     * @return {@link List}<{@link BmsYsexpensesMiddle}>
     */
    public static List<BmsYsexpensesMiddle> reassignMiddleAmountByVolumn(BmsYscostMainInfo costInfo, List<BmsYsexpensesMiddle> middleInfos) {
        List<BmsYsexpensesMiddle> returnData = new ArrayList<>();
        //单条数据无需分摊
        if (middleInfos.size() == 1) {
            BmsYsexpensesMiddle shareInfo = middleInfos.get(0);
            shareInfo.setFreight(costInfo.getFreight());
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee());
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee());
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee());
            shareInfo.setExcessFee(costInfo.getExcessFee());
            shareInfo.setReduceFee(costInfo.getReduceFee());
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee());
            shareInfo.setReturnFee(costInfo.getReturnFee());
            shareInfo.setExceptionFee(costInfo.getExceptionFee());
            shareInfo.setOtherCost1(costInfo.getOtherCost1());
            shareInfo.setOtherCost2(costInfo.getOtherCost2());
            shareInfo.setOtherCost3(costInfo.getOtherCost3());
            shareInfo.setOtherCost4(costInfo.getOtherCost4());
            shareInfo.setOtherCost5(costInfo.getOtherCost5());
            shareInfo.setOtherCost6(costInfo.getOtherCost6());
            shareInfo.setOtherCost7(costInfo.getOtherCost7());
            shareInfo.setOtherCost8(costInfo.getOtherCost8());
            shareInfo.setOtherCost9(costInfo.getOtherCost9());
            shareInfo.setOtherCost10(costInfo.getOtherCost10());
            shareInfo.setOtherCost11(costInfo.getOtherCost11());
            shareInfo.setOtherCost12(costInfo.getOtherCost12());
            shareInfo.setShareType(3);
            returnData.add(shareInfo);
            return returnData;
        }
        BigDecimal totalVolume = BigDecimal.ZERO;
        //获取总重量
        for (BmsYsexpensesMiddle orderInfo : middleInfos) {
            totalVolume = totalVolume.add(orderInfo.getTotalVolume() != null ? orderInfo.getTotalVolume() : BigDecimal.ZERO);
        }
        //分摊金额
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;
        for (int i = 0; i < middleInfos.size(); i++) {
            BmsYsexpensesMiddle shareInfo = middleInfos.get(i);
            BigDecimal volumnRadio = shareInfo.getTotalWeight().divide(totalVolume, 2, RoundingMode.DOWN);
            shareInfo.setFreight(costInfo.getFreight().multiply(volumnRadio));
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee().multiply(volumnRadio));
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee().multiply(volumnRadio));
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee().multiply(volumnRadio));
            shareInfo.setExcessFee(costInfo.getExcessFee().multiply(volumnRadio));
            shareInfo.setReduceFee(costInfo.getReduceFee().multiply(volumnRadio));
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().multiply(volumnRadio));
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee().multiply(volumnRadio));
            shareInfo.setReturnFee(costInfo.getReturnFee().multiply(volumnRadio));
            shareInfo.setExceptionFee(costInfo.getExceptionFee().multiply(volumnRadio));
            shareInfo.setOtherCost1(costInfo.getOtherCost1().multiply(volumnRadio));
            shareInfo.setOtherCost2(costInfo.getOtherCost2().multiply(volumnRadio));
            shareInfo.setOtherCost3(costInfo.getOtherCost3().multiply(volumnRadio));
            shareInfo.setOtherCost4(costInfo.getOtherCost4().multiply(volumnRadio));
            shareInfo.setOtherCost5(costInfo.getOtherCost5().multiply(volumnRadio));
            shareInfo.setOtherCost6(costInfo.getOtherCost6().multiply(volumnRadio));
            shareInfo.setOtherCost7(costInfo.getOtherCost7().multiply(volumnRadio));
            shareInfo.setOtherCost8(costInfo.getOtherCost8().multiply(volumnRadio));
            shareInfo.setOtherCost9(costInfo.getOtherCost9().multiply(volumnRadio));
            shareInfo.setOtherCost10(costInfo.getOtherCost10().multiply(volumnRadio));
            shareInfo.setOtherCost11(costInfo.getOtherCost11().multiply(volumnRadio));
            shareInfo.setOtherCost12(costInfo.getOtherCost12().multiply(volumnRadio));
            shareInfo.setShareType(3);
            //倒数第二条
            if (i < middleInfos.size() - 1) {
                runningFreightAmount = runningFreightAmount.add(shareInfo.getFreight());
                runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(shareInfo.getDeliveryFee());
                runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(shareInfo.getUltrafarFee());
                runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(shareInfo.getSuperframesFee());
                runningExcessFeeAmount = runningExcessFeeAmount.add(shareInfo.getExcessFee());
                runningReduceFeeAmount = runningReduceFeeAmount.add(shareInfo.getReduceFee());
                runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(shareInfo.getOutboundsortingFee());
                runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(shareInfo.getShortbargeFee());
                runningReturnFeeAmount = runningReturnFeeAmount.add(shareInfo.getReturnFee());
                runningExceptionFeeAmount = runningExceptionFeeAmount.add(shareInfo.getExceptionFee());
                runningOtherCost1Amount = runningOtherCost1Amount.add(shareInfo.getOtherCost1());
                runningOtherCost2Amount = runningOtherCost2Amount.add(shareInfo.getOtherCost2());
                runningOtherCost3Amount = runningOtherCost3Amount.add(shareInfo.getOtherCost3());
                runningOtherCost4Amount = runningOtherCost4Amount.add(shareInfo.getOtherCost4());
                runningOtherCost5Amount = runningOtherCost5Amount.add(shareInfo.getOtherCost5());
                runningOtherCost6Amount = runningOtherCost6Amount.add(shareInfo.getOtherCost6());
                runningOtherCost7Amount = runningOtherCost7Amount.add(shareInfo.getOtherCost7());
                runningOtherCost8Amount = runningOtherCost8Amount.add(shareInfo.getOtherCost8());
                runningOtherCost9Amount = runningOtherCost9Amount.add(shareInfo.getOtherCost9());
                runningOtherCost10Amount = runningOtherCost10Amount.add(shareInfo.getOtherCost10());
                runningOtherCost11Amount = runningOtherCost11Amount.add(shareInfo.getOtherCost11());
                runningOtherCost12Amount = runningOtherCost12Amount.add(shareInfo.getOtherCost12());
                idx++;
            }
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        BmsYsexpensesMiddle lastShareInfo = middleInfos.get(idx);
        lastShareInfo.setFreight(costInfo.getFreight().subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(costInfo.getDeliveryFee().subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(costInfo.getUltrafarFee().subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(costInfo.getSuperframesFee().subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(costInfo.getExcessFee().subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(costInfo.getReduceFee().subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(costInfo.getShortbargeFee().subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(costInfo.getReturnFee().subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(costInfo.getExceptionFee().subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(costInfo.getOtherCost1().subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(costInfo.getOtherCost2().subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(costInfo.getOtherCost3().subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(costInfo.getOtherCost4().subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(costInfo.getOtherCost5().subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(costInfo.getOtherCost6().subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(costInfo.getOtherCost7().subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(costInfo.getOtherCost8().subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(costInfo.getOtherCost9().subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(costInfo.getOtherCost10().subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(costInfo.getOtherCost11().subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(costInfo.getOtherCost12().subtract(runningOtherCost12Amount));
        returnData.addAll(middleInfos);
        return returnData;
    }

    /**
     * 按体积重新计算费用数据-应付
     *
     * @param costInfo    成本信息
     * @param middleInfos 订单信息
     * @return {@link List}<{@link BmsYsexpensesMiddle}>
     */
    public static List<BmsYfexpensesMiddle> reassignMiddleAmountByVolumnYf(BmsYfCostMainInfo costInfo, List<BmsYfexpensesMiddle> middleInfos) {
        List<BmsYfexpensesMiddle> returnData = new ArrayList<>();
        //单条数据无需分摊
        if (middleInfos.size() == 1) {
            BmsYfexpensesMiddle shareInfo = middleInfos.get(0);
            shareInfo.setFreight(costInfo.getFreight());
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee());
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee());
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee());
            shareInfo.setExcessFee(costInfo.getExcessFee());
            shareInfo.setReduceFee(costInfo.getReduceFee());
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee());
            shareInfo.setReturnFee(costInfo.getReturnFee());
            shareInfo.setExceptionFee(costInfo.getExceptionFee());
            shareInfo.setOtherCost1(costInfo.getOtherCost1());
            shareInfo.setOtherCost2(costInfo.getOtherCost2());
            shareInfo.setOtherCost3(costInfo.getOtherCost3());
            shareInfo.setOtherCost4(costInfo.getOtherCost4());
            shareInfo.setOtherCost5(costInfo.getOtherCost5());
            shareInfo.setOtherCost6(costInfo.getOtherCost6());
            shareInfo.setOtherCost7(costInfo.getOtherCost7());
            shareInfo.setOtherCost8(costInfo.getOtherCost8());
            shareInfo.setOtherCost9(costInfo.getOtherCost9());
            shareInfo.setOtherCost10(costInfo.getOtherCost10());
            shareInfo.setOtherCost11(costInfo.getOtherCost11());
            shareInfo.setOtherCost12(costInfo.getOtherCost12());
            shareInfo.setShareType(3);
            returnData.add(shareInfo);
            return returnData;
        }
        BigDecimal totalVolume = BigDecimal.ZERO;
        //获取总重量
        for (BmsYfexpensesMiddle orderInfo : middleInfos) {
            totalVolume = totalVolume.add(orderInfo.getTotalVolume() != null ? orderInfo.getTotalVolume() : BigDecimal.ZERO);
        }
        //分摊金额
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;
        for (int i = 0; i < middleInfos.size(); i++) {
            BmsYfexpensesMiddle shareInfo = middleInfos.get(i);
            BigDecimal volumnRadio = shareInfo.getTotalWeight().divide(totalVolume, 2, RoundingMode.DOWN);
            shareInfo.setFreight(costInfo.getFreight().multiply(volumnRadio));
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee().multiply(volumnRadio));
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee().multiply(volumnRadio));
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee().multiply(volumnRadio));
            shareInfo.setExcessFee(costInfo.getExcessFee().multiply(volumnRadio));
            shareInfo.setReduceFee(costInfo.getReduceFee().multiply(volumnRadio));
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().multiply(volumnRadio));
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee().multiply(volumnRadio));
            shareInfo.setReturnFee(costInfo.getReturnFee().multiply(volumnRadio));
            shareInfo.setExceptionFee(costInfo.getExceptionFee().multiply(volumnRadio));
            shareInfo.setOtherCost1(costInfo.getOtherCost1().multiply(volumnRadio));
            shareInfo.setOtherCost2(costInfo.getOtherCost2().multiply(volumnRadio));
            shareInfo.setOtherCost3(costInfo.getOtherCost3().multiply(volumnRadio));
            shareInfo.setOtherCost4(costInfo.getOtherCost4().multiply(volumnRadio));
            shareInfo.setOtherCost5(costInfo.getOtherCost5().multiply(volumnRadio));
            shareInfo.setOtherCost6(costInfo.getOtherCost6().multiply(volumnRadio));
            shareInfo.setOtherCost7(costInfo.getOtherCost7().multiply(volumnRadio));
            shareInfo.setOtherCost8(costInfo.getOtherCost8().multiply(volumnRadio));
            shareInfo.setOtherCost9(costInfo.getOtherCost9().multiply(volumnRadio));
            shareInfo.setOtherCost10(costInfo.getOtherCost10().multiply(volumnRadio));
            shareInfo.setOtherCost11(costInfo.getOtherCost11().multiply(volumnRadio));
            shareInfo.setOtherCost12(costInfo.getOtherCost12().multiply(volumnRadio));
            shareInfo.setShareType(3);
            //倒数第二条
            if (i < middleInfos.size() - 1) {
                runningFreightAmount = runningFreightAmount.add(shareInfo.getFreight());
                runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(shareInfo.getDeliveryFee());
                runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(shareInfo.getUltrafarFee());
                runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(shareInfo.getSuperframesFee());
                runningExcessFeeAmount = runningExcessFeeAmount.add(shareInfo.getExcessFee());
                runningReduceFeeAmount = runningReduceFeeAmount.add(shareInfo.getReduceFee());
                runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(shareInfo.getOutboundsortingFee());
                runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(shareInfo.getShortbargeFee());
                runningReturnFeeAmount = runningReturnFeeAmount.add(shareInfo.getReturnFee());
                runningExceptionFeeAmount = runningExceptionFeeAmount.add(shareInfo.getExceptionFee());
                runningOtherCost1Amount = runningOtherCost1Amount.add(shareInfo.getOtherCost1());
                runningOtherCost2Amount = runningOtherCost2Amount.add(shareInfo.getOtherCost2());
                runningOtherCost3Amount = runningOtherCost3Amount.add(shareInfo.getOtherCost3());
                runningOtherCost4Amount = runningOtherCost4Amount.add(shareInfo.getOtherCost4());
                runningOtherCost5Amount = runningOtherCost5Amount.add(shareInfo.getOtherCost5());
                runningOtherCost6Amount = runningOtherCost6Amount.add(shareInfo.getOtherCost6());
                runningOtherCost7Amount = runningOtherCost7Amount.add(shareInfo.getOtherCost7());
                runningOtherCost8Amount = runningOtherCost8Amount.add(shareInfo.getOtherCost8());
                runningOtherCost9Amount = runningOtherCost9Amount.add(shareInfo.getOtherCost9());
                runningOtherCost10Amount = runningOtherCost10Amount.add(shareInfo.getOtherCost10());
                runningOtherCost11Amount = runningOtherCost11Amount.add(shareInfo.getOtherCost11());
                runningOtherCost12Amount = runningOtherCost12Amount.add(shareInfo.getOtherCost12());
                idx++;
            }
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        BmsYfexpensesMiddle lastShareInfo = middleInfos.get(idx);
        lastShareInfo.setFreight(costInfo.getFreight().subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(costInfo.getDeliveryFee().subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(costInfo.getUltrafarFee().subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(costInfo.getSuperframesFee().subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(costInfo.getExcessFee().subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(costInfo.getReduceFee().subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee().subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(costInfo.getShortbargeFee().subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(costInfo.getReturnFee().subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(costInfo.getExceptionFee().subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(costInfo.getOtherCost1().subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(costInfo.getOtherCost2().subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(costInfo.getOtherCost3().subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(costInfo.getOtherCost4().subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(costInfo.getOtherCost5().subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(costInfo.getOtherCost6().subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(costInfo.getOtherCost7().subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(costInfo.getOtherCost8().subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(costInfo.getOtherCost9().subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(costInfo.getOtherCost10().subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(costInfo.getOtherCost11().subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(costInfo.getOtherCost12().subtract(runningOtherCost12Amount));
        returnData.addAll(middleInfos);
        return returnData;
    }


    /**
     * 应付金额分摊-按单||作业单
     *
     * @param shareType    分摊类型 1:按订单(平均),2:按重量,3:按体积
     * @param mainCodeInfo 主金额信息
     * @param codes        分摊的单据信息
     * @param resultType   1:key:codeId,2:key:relateCode
     * @return value1:按订单分摊结果，value2:按体积分摊结果,value3:按重量分摊结果
     */
    public static Map<String, BmsYfcostInfo> splitAmountYfCodes(Integer shareType
            , BmsYfCostMainInfo mainCodeInfo
            , List<AutoYfCodeInfo> codes
            , Integer resultType) {
        if (shareType.equals(1)) {
            return splitAmountByYfOrder2(mainCodeInfo, codes, resultType);
        }
        if (shareType.equals(2)) {
            return splitAmountByYfWeight2(mainCodeInfo, codes, resultType);
        }
        if (shareType.equals(3)) {
            return splitAmountByYfVolumn2(mainCodeInfo, codes, resultType);
        }
        return new HashMap<>();
    }


    /**
     * 按单分摊金额-应付
     *
     * @param costInfo   总费用明细
     * @param orderInfos 涉及订单信息
     * @param resultType 处理结果类型
     * @return key:单据id或者/code
     */
    public static Map<String, BmsYfcostInfo> splitAmountByYfOrder2(BmsYfCostMainInfo costInfo
            , List<AutoYfCodeInfo> orderInfos
            , Integer resultType) {
        Map<String, BmsYfcostInfo> returnData = new LinkedHashMap<>();
        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            AutoYfCodeInfo orderInfo = orderInfos.get(0);
            BmsYfcostInfo shareInfo = new BmsYfcostInfo();
            shareInfo.setFreight(costInfo.getFreight() != null ? costInfo.getFreight() : BigDecimal.ZERO);
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee() != null ? costInfo.getDeliveryFee() : BigDecimal.ZERO);
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee() != null ? costInfo.getUltrafarFee() : BigDecimal.ZERO);
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee() != null ? costInfo.getSuperframesFee() : BigDecimal.ZERO);
            shareInfo.setExcessFee(costInfo.getExcessFee() != null ? costInfo.getExcessFee() : BigDecimal.ZERO);
            shareInfo.setReduceFee(costInfo.getReduceFee() != null ? costInfo.getReduceFee() : BigDecimal.ZERO);
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee() != null ? costInfo.getOutboundsortingFee() : BigDecimal.ZERO);
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee() != null ? costInfo.getShortbargeFee() : BigDecimal.ZERO);
            shareInfo.setReturnFee(costInfo.getReturnFee() != null ? costInfo.getReturnFee() : BigDecimal.ZERO);
            shareInfo.setExceptionFee(costInfo.getExceptionFee() != null ? costInfo.getExceptionFee() : BigDecimal.ZERO);
            shareInfo.setOtherCost1(costInfo.getOtherCost1() != null ? costInfo.getOtherCost1() : BigDecimal.ZERO);
            shareInfo.setOtherCost2(costInfo.getOtherCost2() != null ? costInfo.getOtherCost2() : BigDecimal.ZERO);
            shareInfo.setOtherCost3(costInfo.getOtherCost3() != null ? costInfo.getOtherCost3() : BigDecimal.ZERO);
            shareInfo.setOtherCost4(costInfo.getOtherCost4() != null ? costInfo.getOtherCost4() : BigDecimal.ZERO);
            shareInfo.setOtherCost5(costInfo.getOtherCost5() != null ? costInfo.getOtherCost5() : BigDecimal.ZERO);
            shareInfo.setOtherCost6(costInfo.getOtherCost6() != null ? costInfo.getOtherCost6() : BigDecimal.ZERO);
            shareInfo.setOtherCost7(costInfo.getOtherCost7() != null ? costInfo.getOtherCost7() : BigDecimal.ZERO);
            shareInfo.setOtherCost8(costInfo.getOtherCost8() != null ? costInfo.getOtherCost8() : BigDecimal.ZERO);
            shareInfo.setOtherCost9(costInfo.getOtherCost9() != null ? costInfo.getOtherCost9() : BigDecimal.ZERO);
            shareInfo.setOtherCost10(costInfo.getOtherCost10() != null ? costInfo.getOtherCost10() : BigDecimal.ZERO);
            shareInfo.setOtherCost11(costInfo.getOtherCost11() != null ? costInfo.getOtherCost11() : BigDecimal.ZERO);
            shareInfo.setOtherCost12(costInfo.getOtherCost12() != null ? costInfo.getOtherCost12() : BigDecimal.ZERO);
            shareInfo.setShareType(1);
            shareInfo.setShareAmount(costInfo.getSumFee() != null ? costInfo.getSumFee() : BigDecimal.ZERO);
            // 应付要额外加一个作业单id赋值
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getYfbillId(), shareInfo);
            return returnData;
        }
        //金额分摊(向下取整，避免出现最后一条金额非累加)
        BigDecimal avgfreightAmount = BigDecimal.ZERO;
        BigDecimal avgDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal avgUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal avgSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal avgExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal avgReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal avgOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal avgShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal avgReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal avgExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal avgOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost12Amount = BigDecimal.ZERO;

        BigDecimal sumFreight = costInfo.getFreight() == null ? BigDecimal.ZERO : costInfo.getFreight();
        BigDecimal sumDeliveryFee = costInfo.getDeliveryFee() == null ? BigDecimal.ZERO : costInfo.getDeliveryFee();
        BigDecimal sumUltrafarFee = costInfo.getUltrafarFee() == null ? BigDecimal.ZERO : costInfo.getUltrafarFee();
        BigDecimal sumSuperframesFee = costInfo.getSuperframesFee() == null ? BigDecimal.ZERO : costInfo.getSuperframesFee();
        BigDecimal sumExcessFee = costInfo.getExcessFee() == null ? BigDecimal.ZERO : costInfo.getExcessFee();
        BigDecimal sumReduceFee = costInfo.getReduceFee() == null ? BigDecimal.ZERO : costInfo.getReduceFee();
        BigDecimal sumOutboundsortingFee = costInfo.getOutboundsortingFee() == null ? BigDecimal.ZERO : costInfo.getOutboundsortingFee();
        BigDecimal sumShortbargeFee = costInfo.getShortbargeFee() == null ? BigDecimal.ZERO : costInfo.getShortbargeFee();
        BigDecimal sumReturnFee = costInfo.getReturnFee() == null ? BigDecimal.ZERO : costInfo.getReturnFee();
        BigDecimal sumExceptionFee = costInfo.getExceptionFee() == null ? BigDecimal.ZERO : costInfo.getExceptionFee();
        BigDecimal sumOtherCost1 = costInfo.getOtherCost1() == null ? BigDecimal.ZERO : costInfo.getOtherCost1();
        BigDecimal sumOtherCost2 = costInfo.getOtherCost2() == null ? BigDecimal.ZERO : costInfo.getOtherCost2();
        BigDecimal sumOtherCost3 = costInfo.getOtherCost3() == null ? BigDecimal.ZERO : costInfo.getOtherCost3();
        BigDecimal sumOtherCost4 = costInfo.getOtherCost4() == null ? BigDecimal.ZERO : costInfo.getOtherCost4();
        BigDecimal sumOtherCost5 = costInfo.getOtherCost5() == null ? BigDecimal.ZERO : costInfo.getOtherCost5();
        BigDecimal sumOtherCost6 = costInfo.getOtherCost6() == null ? BigDecimal.ZERO : costInfo.getOtherCost6();
        BigDecimal sumOtherCost7 = costInfo.getOtherCost7() == null ? BigDecimal.ZERO : costInfo.getOtherCost7();
        BigDecimal sumOtherCost8 = costInfo.getOtherCost8() == null ? BigDecimal.ZERO : costInfo.getOtherCost8();
        BigDecimal sumOtherCost9 = costInfo.getOtherCost9() == null ? BigDecimal.ZERO : costInfo.getOtherCost9();
        BigDecimal sumOtherCost10 = costInfo.getOtherCost10() == null ? BigDecimal.ZERO : costInfo.getOtherCost10();
        BigDecimal sumOtherCost11 = costInfo.getOtherCost11() == null ? BigDecimal.ZERO : costInfo.getOtherCost11();
        BigDecimal sumOtherCost12 = costInfo.getOtherCost12() == null ? BigDecimal.ZERO : costInfo.getOtherCost12();


        if (!CollUtil.isEmpty(orderInfos)) {
            avgfreightAmount = sumFreight.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgDeliveryFeeAmount = sumDeliveryFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgUltrafarFeeAmount = sumUltrafarFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgSuperframesFeeAmount = sumSuperframesFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgExcessFeeAmount = sumExcessFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgReduceFeeAmount = sumReduceFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOutboundsortingFeeAmount = sumOutboundsortingFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgShortbargeFeeAmount = sumShortbargeFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgReturnFeeAmount = sumReturnFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgExceptionFeeAmount = sumExceptionFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost1Amount = sumOtherCost1.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost2Amount = sumOtherCost2.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost3Amount = sumOtherCost3.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost4Amount = sumOtherCost4.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost5Amount = sumOtherCost5.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost6Amount = sumOtherCost6.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost7Amount = sumOtherCost7.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost8Amount = sumOtherCost8.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost9Amount = sumOtherCost9.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost10Amount = sumOtherCost10.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost11Amount = sumOtherCost11.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost12Amount = sumOtherCost12.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
        }
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;

        for (int i = 0; i < orderInfos.size(); i++) {
            if (i == orderInfos.size() - 1) {
                idx = i;
                continue;
            }
            AutoYfCodeInfo orderInfo = orderInfos.get(i);
            BmsYfcostInfo shareInfo = new BmsYfcostInfo();
            shareInfo.setFreight(avgfreightAmount);
            shareInfo.setDeliveryFee(avgDeliveryFeeAmount);
            shareInfo.setUltrafarFee(avgUltrafarFeeAmount);
            shareInfo.setSuperframesFee(avgSuperframesFeeAmount);
            shareInfo.setExcessFee(avgExcessFeeAmount);
            shareInfo.setReduceFee(avgReduceFeeAmount);
            shareInfo.setOutboundsortingFee(avgOutboundsortingFeeAmount);
            shareInfo.setShortbargeFee(avgShortbargeFeeAmount);
            shareInfo.setReturnFee(avgReturnFeeAmount);
            shareInfo.setExceptionFee(avgExceptionFeeAmount);
            shareInfo.setOtherCost1(avgOtherCost1Amount);
            shareInfo.setOtherCost2(avgOtherCost2Amount);
            shareInfo.setOtherCost3(avgOtherCost3Amount);
            shareInfo.setOtherCost4(avgOtherCost4Amount);
            shareInfo.setOtherCost5(avgOtherCost5Amount);
            shareInfo.setOtherCost6(avgOtherCost6Amount);
            shareInfo.setOtherCost7(avgOtherCost7Amount);
            shareInfo.setOtherCost8(avgOtherCost8Amount);
            shareInfo.setOtherCost9(avgOtherCost9Amount);
            shareInfo.setOtherCost10(avgOtherCost10Amount);
            shareInfo.setOtherCost11(avgOtherCost11Amount);
            shareInfo.setOtherCost12(avgOtherCost12Amount);
            shareInfo.setShareType(1);
            shareInfo.setShareAmount(avgfreightAmount
                    .add(avgDeliveryFeeAmount)
                    .add(avgUltrafarFeeAmount)
                    .add(avgSuperframesFeeAmount)
                    .add(avgExcessFeeAmount)
                    .add(avgReduceFeeAmount)
                    .add(avgOutboundsortingFeeAmount)
                    .add(avgShortbargeFeeAmount)
                    .add(avgReturnFeeAmount)
                    .add(avgExceptionFeeAmount)
                    .add(avgOtherCost1Amount)
                    .add(avgOtherCost2Amount)
                    .add(avgOtherCost3Amount)
                    .add(avgOtherCost4Amount)
                    .add(avgOtherCost5Amount)
                    .add(avgOtherCost6Amount)
                    .add(avgOtherCost7Amount)
                    .add(avgOtherCost8Amount)
                    .add(avgOtherCost9Amount)
                    .add(avgOtherCost10Amount)
                    .add(avgOtherCost11Amount)
                    .add(avgOtherCost12Amount)
            );
            runningFreightAmount = runningFreightAmount.add(avgfreightAmount);
            runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(avgDeliveryFeeAmount);
            runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(avgUltrafarFeeAmount);
            runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(avgSuperframesFeeAmount);
            runningExcessFeeAmount = runningExcessFeeAmount.add(avgExcessFeeAmount);
            runningReduceFeeAmount = runningReduceFeeAmount.add(avgReduceFeeAmount);
            runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(avgOutboundsortingFeeAmount);
            runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(avgShortbargeFeeAmount);
            runningReturnFeeAmount = runningReturnFeeAmount.add(avgReturnFeeAmount);
            runningExceptionFeeAmount = runningExceptionFeeAmount.add(avgExceptionFeeAmount);
            runningOtherCost1Amount = runningOtherCost1Amount.add(avgOtherCost1Amount);
            runningOtherCost2Amount = runningOtherCost2Amount.add(avgOtherCost2Amount);
            runningOtherCost3Amount = runningOtherCost3Amount.add(avgOtherCost3Amount);
            runningOtherCost4Amount = runningOtherCost4Amount.add(avgOtherCost4Amount);
            runningOtherCost5Amount = runningOtherCost5Amount.add(avgOtherCost5Amount);
            runningOtherCost6Amount = runningOtherCost6Amount.add(avgOtherCost6Amount);
            runningOtherCost7Amount = runningOtherCost7Amount.add(avgOtherCost7Amount);
            runningOtherCost8Amount = runningOtherCost8Amount.add(avgOtherCost8Amount);
            runningOtherCost9Amount = runningOtherCost9Amount.add(avgOtherCost9Amount);
            runningOtherCost10Amount = runningOtherCost10Amount.add(avgOtherCost10Amount);
            runningOtherCost11Amount = runningOtherCost11Amount.add(avgOtherCost11Amount);
            runningOtherCost12Amount = runningOtherCost12Amount.add(avgOtherCost12Amount);
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getYfbillId(), shareInfo);
        }
        //拿到最后一个单据信息，将计算求于数均给到最后一条
        AutoYfCodeInfo lastOrderInfo = orderInfos.get(idx);
        BmsYfcostInfo lastShareInfo = new BmsYfcostInfo();
        lastShareInfo.setFreight(sumFreight.subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(sumDeliveryFee.subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(sumUltrafarFee.subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(sumSuperframesFee.subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(sumExcessFee.subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(sumReduceFee.subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(sumOutboundsortingFee.subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(sumShortbargeFee.subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(sumReturnFee.subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(sumExceptionFee.subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(sumOtherCost1.subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(sumOtherCost2.subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(sumOtherCost3.subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(sumOtherCost4.subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(sumOtherCost5.subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(sumOtherCost6.subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(sumOtherCost7.subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(sumOtherCost8.subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(sumOtherCost9.subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(sumOtherCost10.subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(sumOtherCost11.subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(sumOtherCost12.subtract(runningOtherCost12Amount));
        returnData.put(resultType.equals(2) ? lastOrderInfo.getRelateCode() : lastOrderInfo.getYfbillId(), lastShareInfo);
        return returnData;
    }


    /**
     * 按单分摊金额-应付
     *
     * @param costInfo         总费用明细
     * @param middleShareInfos 涉及订单信息
     * @param resultType       处理结果类型
     * @return key:单据id或者/code
     */
    public static Map<String, BmsYfexpensesMiddleShare> splitAmountByYfOrderShare2(BmsYfCostMainInfo costInfo
            , List<BmsYfexpensesMiddleShare> middleShareInfos
            , Integer resultType) {
        Map<String, BmsYfexpensesMiddleShare> returnData = new LinkedHashMap<>();
        //单条数据无需分摊
        if (middleShareInfos.size() == 1) {
            BmsYfexpensesMiddleShare orderInfo = middleShareInfos.get(0);
            BmsYfexpensesMiddleShare shareInfo = new BmsYfexpensesMiddleShare();
            shareInfo.setFreight(costInfo.getFreight() != null ? costInfo.getFreight() : BigDecimal.ZERO);
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee() != null ? costInfo.getDeliveryFee() : BigDecimal.ZERO);
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee() != null ? costInfo.getUltrafarFee() : BigDecimal.ZERO);
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee() != null ? costInfo.getSuperframesFee() : BigDecimal.ZERO);
            shareInfo.setExcessFee(costInfo.getExcessFee() != null ? costInfo.getExcessFee() : BigDecimal.ZERO);
            shareInfo.setReduceFee(costInfo.getReduceFee() != null ? costInfo.getReduceFee() : BigDecimal.ZERO);
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee() != null ? costInfo.getOutboundsortingFee() : BigDecimal.ZERO);
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee() != null ? costInfo.getShortbargeFee() : BigDecimal.ZERO);
            shareInfo.setReturnFee(costInfo.getReturnFee() != null ? costInfo.getReturnFee() : BigDecimal.ZERO);
            shareInfo.setExceptionFee(costInfo.getExceptionFee() != null ? costInfo.getExceptionFee() : BigDecimal.ZERO);
            shareInfo.setOtherCost1(costInfo.getOtherCost1() != null ? costInfo.getOtherCost1() : BigDecimal.ZERO);
            shareInfo.setOtherCost2(costInfo.getOtherCost2() != null ? costInfo.getOtherCost2() : BigDecimal.ZERO);
            shareInfo.setOtherCost3(costInfo.getOtherCost3() != null ? costInfo.getOtherCost3() : BigDecimal.ZERO);
            shareInfo.setOtherCost4(costInfo.getOtherCost4() != null ? costInfo.getOtherCost4() : BigDecimal.ZERO);
            shareInfo.setOtherCost5(costInfo.getOtherCost5() != null ? costInfo.getOtherCost5() : BigDecimal.ZERO);
            shareInfo.setOtherCost6(costInfo.getOtherCost6() != null ? costInfo.getOtherCost6() : BigDecimal.ZERO);
            shareInfo.setOtherCost7(costInfo.getOtherCost7() != null ? costInfo.getOtherCost7() : BigDecimal.ZERO);
            shareInfo.setOtherCost8(costInfo.getOtherCost8() != null ? costInfo.getOtherCost8() : BigDecimal.ZERO);
            shareInfo.setOtherCost9(costInfo.getOtherCost9() != null ? costInfo.getOtherCost9() : BigDecimal.ZERO);
            shareInfo.setOtherCost10(costInfo.getOtherCost10() != null ? costInfo.getOtherCost10() : BigDecimal.ZERO);
            shareInfo.setOtherCost11(costInfo.getOtherCost11() != null ? costInfo.getOtherCost11() : BigDecimal.ZERO);
            shareInfo.setOtherCost12(costInfo.getOtherCost12() != null ? costInfo.getOtherCost12() : BigDecimal.ZERO);
            shareInfo.setShareType(1);
            shareInfo.setShareAmount(costInfo.getSumFee() != null ? costInfo.getSumFee() : BigDecimal.ZERO);
            // 应付要额外加一个作业单id赋值
            switch (resultType) {
                case 1:
                    returnData.put(orderInfo.getYfbillId(), shareInfo);
                    break;
                case 2:
                    returnData.put(orderInfo.getRelateCode(), shareInfo);
                    break;
                case 3:
                    returnData.put(orderInfo.getId().toString(), shareInfo);
                    break;
            }
            return returnData;
        }
        //金额分摊(向下取整，避免出现最后一条金额非累加)
        BigDecimal avgfreightAmount = BigDecimal.ZERO;
        BigDecimal avgDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal avgUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal avgSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal avgExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal avgReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal avgOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal avgShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal avgReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal avgExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal avgOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost12Amount = BigDecimal.ZERO;

        BigDecimal sumFreight = costInfo.getFreight() == null ? BigDecimal.ZERO : costInfo.getFreight();
        BigDecimal sumDeliveryFee = costInfo.getDeliveryFee() == null ? BigDecimal.ZERO : costInfo.getDeliveryFee();
        BigDecimal sumUltrafarFee = costInfo.getUltrafarFee() == null ? BigDecimal.ZERO : costInfo.getUltrafarFee();
        BigDecimal sumSuperframesFee = costInfo.getSuperframesFee() == null ? BigDecimal.ZERO : costInfo.getSuperframesFee();
        BigDecimal sumExcessFee = costInfo.getExcessFee() == null ? BigDecimal.ZERO : costInfo.getExcessFee();
        BigDecimal sumReduceFee = costInfo.getReduceFee() == null ? BigDecimal.ZERO : costInfo.getReduceFee();
        BigDecimal sumOutboundsortingFee = costInfo.getOutboundsortingFee() == null ? BigDecimal.ZERO : costInfo.getOutboundsortingFee();
        BigDecimal sumShortbargeFee = costInfo.getShortbargeFee() == null ? BigDecimal.ZERO : costInfo.getShortbargeFee();
        BigDecimal sumReturnFee = costInfo.getReturnFee() == null ? BigDecimal.ZERO : costInfo.getReturnFee();
        BigDecimal sumExceptionFee = costInfo.getExceptionFee() == null ? BigDecimal.ZERO : costInfo.getExceptionFee();
        BigDecimal sumOtherCost1 = costInfo.getOtherCost1() == null ? BigDecimal.ZERO : costInfo.getOtherCost1();
        BigDecimal sumOtherCost2 = costInfo.getOtherCost2() == null ? BigDecimal.ZERO : costInfo.getOtherCost2();
        BigDecimal sumOtherCost3 = costInfo.getOtherCost3() == null ? BigDecimal.ZERO : costInfo.getOtherCost3();
        BigDecimal sumOtherCost4 = costInfo.getOtherCost4() == null ? BigDecimal.ZERO : costInfo.getOtherCost4();
        BigDecimal sumOtherCost5 = costInfo.getOtherCost5() == null ? BigDecimal.ZERO : costInfo.getOtherCost5();
        BigDecimal sumOtherCost6 = costInfo.getOtherCost6() == null ? BigDecimal.ZERO : costInfo.getOtherCost6();
        BigDecimal sumOtherCost7 = costInfo.getOtherCost7() == null ? BigDecimal.ZERO : costInfo.getOtherCost7();
        BigDecimal sumOtherCost8 = costInfo.getOtherCost8() == null ? BigDecimal.ZERO : costInfo.getOtherCost8();
        BigDecimal sumOtherCost9 = costInfo.getOtherCost9() == null ? BigDecimal.ZERO : costInfo.getOtherCost9();
        BigDecimal sumOtherCost10 = costInfo.getOtherCost10() == null ? BigDecimal.ZERO : costInfo.getOtherCost10();
        BigDecimal sumOtherCost11 = costInfo.getOtherCost11() == null ? BigDecimal.ZERO : costInfo.getOtherCost11();
        BigDecimal sumOtherCost12 = costInfo.getOtherCost12() == null ? BigDecimal.ZERO : costInfo.getOtherCost12();


        if (!CollUtil.isEmpty(middleShareInfos)) {
            avgfreightAmount = sumFreight.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgDeliveryFeeAmount = sumDeliveryFee.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgUltrafarFeeAmount = sumUltrafarFee.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgSuperframesFeeAmount = sumSuperframesFee.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgExcessFeeAmount = sumExcessFee.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgReduceFeeAmount = sumReduceFee.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOutboundsortingFeeAmount = sumOutboundsortingFee.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgShortbargeFeeAmount = sumShortbargeFee.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgReturnFeeAmount = sumReturnFee.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgExceptionFeeAmount = sumExceptionFee.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost1Amount = sumOtherCost1.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost2Amount = sumOtherCost2.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost3Amount = sumOtherCost3.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost4Amount = sumOtherCost4.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost5Amount = sumOtherCost5.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost6Amount = sumOtherCost6.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost7Amount = sumOtherCost7.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost8Amount = sumOtherCost8.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost9Amount = sumOtherCost9.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost10Amount = sumOtherCost10.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost11Amount = sumOtherCost11.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost12Amount = sumOtherCost12.divide(BigDecimal.valueOf(middleShareInfos.size()), 2, RoundingMode.DOWN);
        }
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;

        for (int i = 0; i < middleShareInfos.size(); i++) {
            if (i == middleShareInfos.size() - 1) {
                idx = i;
                continue;
            }
            BmsYfexpensesMiddleShare orderInfo = middleShareInfos.get(i);
            BmsYfexpensesMiddleShare shareInfo = new BmsYfexpensesMiddleShare();
            shareInfo.setFreight(avgfreightAmount);
            shareInfo.setDeliveryFee(avgDeliveryFeeAmount);
            shareInfo.setUltrafarFee(avgUltrafarFeeAmount);
            shareInfo.setSuperframesFee(avgSuperframesFeeAmount);
            shareInfo.setExcessFee(avgExcessFeeAmount);
            shareInfo.setReduceFee(avgReduceFeeAmount);
            shareInfo.setOutboundsortingFee(avgOutboundsortingFeeAmount);
            shareInfo.setShortbargeFee(avgShortbargeFeeAmount);
            shareInfo.setReturnFee(avgReturnFeeAmount);
            shareInfo.setExceptionFee(avgExceptionFeeAmount);
            shareInfo.setOtherCost1(avgOtherCost1Amount);
            shareInfo.setOtherCost2(avgOtherCost2Amount);
            shareInfo.setOtherCost3(avgOtherCost3Amount);
            shareInfo.setOtherCost4(avgOtherCost4Amount);
            shareInfo.setOtherCost5(avgOtherCost5Amount);
            shareInfo.setOtherCost6(avgOtherCost6Amount);
            shareInfo.setOtherCost7(avgOtherCost7Amount);
            shareInfo.setOtherCost8(avgOtherCost8Amount);
            shareInfo.setOtherCost9(avgOtherCost9Amount);
            shareInfo.setOtherCost10(avgOtherCost10Amount);
            shareInfo.setOtherCost11(avgOtherCost11Amount);
            shareInfo.setOtherCost12(avgOtherCost12Amount);
            shareInfo.setShareType(1);
            shareInfo.setShareAmount(avgfreightAmount
                    .add(avgDeliveryFeeAmount)
                    .add(avgUltrafarFeeAmount)
                    .add(avgSuperframesFeeAmount)
                    .add(avgExcessFeeAmount)
                    .add(avgReduceFeeAmount)
                    .add(avgOutboundsortingFeeAmount)
                    .add(avgShortbargeFeeAmount)
                    .add(avgReturnFeeAmount)
                    .add(avgExceptionFeeAmount)
                    .add(avgOtherCost1Amount)
                    .add(avgOtherCost2Amount)
                    .add(avgOtherCost3Amount)
                    .add(avgOtherCost4Amount)
                    .add(avgOtherCost5Amount)
                    .add(avgOtherCost6Amount)
                    .add(avgOtherCost7Amount)
                    .add(avgOtherCost8Amount)
                    .add(avgOtherCost9Amount)
                    .add(avgOtherCost10Amount)
                    .add(avgOtherCost11Amount)
                    .add(avgOtherCost12Amount)
            );
            runningFreightAmount = runningFreightAmount.add(avgfreightAmount);
            runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(avgDeliveryFeeAmount);
            runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(avgUltrafarFeeAmount);
            runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(avgSuperframesFeeAmount);
            runningExcessFeeAmount = runningExcessFeeAmount.add(avgExcessFeeAmount);
            runningReduceFeeAmount = runningReduceFeeAmount.add(avgReduceFeeAmount);
            runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(avgOutboundsortingFeeAmount);
            runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(avgShortbargeFeeAmount);
            runningReturnFeeAmount = runningReturnFeeAmount.add(avgReturnFeeAmount);
            runningExceptionFeeAmount = runningExceptionFeeAmount.add(avgExceptionFeeAmount);
            runningOtherCost1Amount = runningOtherCost1Amount.add(avgOtherCost1Amount);
            runningOtherCost2Amount = runningOtherCost2Amount.add(avgOtherCost2Amount);
            runningOtherCost3Amount = runningOtherCost3Amount.add(avgOtherCost3Amount);
            runningOtherCost4Amount = runningOtherCost4Amount.add(avgOtherCost4Amount);
            runningOtherCost5Amount = runningOtherCost5Amount.add(avgOtherCost5Amount);
            runningOtherCost6Amount = runningOtherCost6Amount.add(avgOtherCost6Amount);
            runningOtherCost7Amount = runningOtherCost7Amount.add(avgOtherCost7Amount);
            runningOtherCost8Amount = runningOtherCost8Amount.add(avgOtherCost8Amount);
            runningOtherCost9Amount = runningOtherCost9Amount.add(avgOtherCost9Amount);
            runningOtherCost10Amount = runningOtherCost10Amount.add(avgOtherCost10Amount);
            runningOtherCost11Amount = runningOtherCost11Amount.add(avgOtherCost11Amount);
            runningOtherCost12Amount = runningOtherCost12Amount.add(avgOtherCost12Amount);
            // 应付要额外加一个作业单id赋值
            switch (resultType) {
                case 1:
                    returnData.put(orderInfo.getYfbillId(), shareInfo);
                    break;
                case 2:
                    returnData.put(orderInfo.getRelateCode(), shareInfo);
                    break;
                case 3:
                    returnData.put(orderInfo.getId().toString(), shareInfo);
                    break;
            }
        }
        //拿到最后一个单据信息，将计算求于数均给到最后一条
        BmsYfexpensesMiddleShare lastOrderInfo = middleShareInfos.get(idx);
        BmsYfexpensesMiddleShare lastShareInfo = new BmsYfexpensesMiddleShare();
        lastShareInfo.setFreight(sumFreight.subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(sumDeliveryFee.subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(sumUltrafarFee.subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(sumSuperframesFee.subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(sumExcessFee.subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(sumReduceFee.subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(sumOutboundsortingFee.subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(sumShortbargeFee.subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(sumReturnFee.subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(sumExceptionFee.subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(sumOtherCost1.subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(sumOtherCost2.subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(sumOtherCost3.subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(sumOtherCost4.subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(sumOtherCost5.subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(sumOtherCost6.subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(sumOtherCost7.subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(sumOtherCost8.subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(sumOtherCost9.subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(sumOtherCost10.subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(sumOtherCost11.subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(sumOtherCost12.subtract(runningOtherCost12Amount));
        // 应付要额外加一个作业单id赋值
        switch (resultType) {
            case 1:
                returnData.put(lastOrderInfo.getYfbillId(), lastShareInfo);
                break;
            case 2:
                returnData.put(lastOrderInfo.getRelateCode(), lastShareInfo);
                break;
            case 3:
                returnData.put(lastOrderInfo.getId().toString(), lastShareInfo);
                break;
        }
        return returnData;
    }


    /**
     * 按重量金额分摊-应付
     *
     * @param costInfo   费用信息
     * @param orderInfos 单据信息
     * @param resultType 1:按单号id，2:按单号
     * @return 按重量分摊的金额
     */
    public static Map<String, BmsYfcostInfo> splitAmountByYfWeight2(BmsYfCostMainInfo costInfo
            , List<AutoYfCodeInfo> orderInfos
            , Integer resultType) {
        Map<String, BmsYfcostInfo> returnData = new LinkedHashMap<>();

        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            AutoYfCodeInfo orderInfo = orderInfos.get(0);
            BmsYfcostInfo shareInfo = new BmsYfcostInfo();
            shareInfo.setFreight(costInfo.getFreight());
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee());
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee());
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee());
            shareInfo.setExcessFee(costInfo.getExcessFee());
            shareInfo.setReduceFee(costInfo.getReduceFee());
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee());
            shareInfo.setReturnFee(costInfo.getReturnFee());
            shareInfo.setExceptionFee(costInfo.getExceptionFee());
            shareInfo.setOtherCost1(costInfo.getOtherCost1());
            shareInfo.setOtherCost2(costInfo.getOtherCost2());
            shareInfo.setOtherCost3(costInfo.getOtherCost3());
            shareInfo.setOtherCost4(costInfo.getOtherCost4());
            shareInfo.setOtherCost5(costInfo.getOtherCost5());
            shareInfo.setOtherCost6(costInfo.getOtherCost6());
            shareInfo.setOtherCost7(costInfo.getOtherCost7());
            shareInfo.setOtherCost8(costInfo.getOtherCost8());
            shareInfo.setOtherCost9(costInfo.getOtherCost9());
            shareInfo.setOtherCost10(costInfo.getOtherCost10());
            shareInfo.setOtherCost11(costInfo.getOtherCost11());
            shareInfo.setOtherCost12(costInfo.getOtherCost12());
            shareInfo.setShareType(2);
            shareInfo.setShareAmount(costInfo.getSumFee());
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getYfbillId(), shareInfo);
            return returnData;
        }

        BigDecimal totalWeight = BigDecimal.ZERO;
        //获取总重量
        for (AutoYfCodeInfo orderInfo : orderInfos) {
            totalWeight = totalWeight.add(orderInfo.getTotalWeight() != null ? orderInfo.getTotalWeight() : BigDecimal.ZERO);
        }

        //主费用信息
        BigDecimal sumFreight = costInfo.getFreight() == null ? BigDecimal.ZERO : costInfo.getFreight();
        BigDecimal sumDeliveryFee = costInfo.getDeliveryFee() == null ? BigDecimal.ZERO : costInfo.getDeliveryFee();
        BigDecimal sumUltrafarFee = costInfo.getUltrafarFee() == null ? BigDecimal.ZERO : costInfo.getUltrafarFee();
        BigDecimal sumSuperframesFee = costInfo.getSuperframesFee() == null ? BigDecimal.ZERO : costInfo.getSuperframesFee();
        BigDecimal sumExcessFee = costInfo.getExcessFee() == null ? BigDecimal.ZERO : costInfo.getExcessFee();
        BigDecimal sumReduceFee = costInfo.getReduceFee() == null ? BigDecimal.ZERO : costInfo.getReduceFee();
        BigDecimal sumOutboundsortingFee = costInfo.getOutboundsortingFee() == null ? BigDecimal.ZERO : costInfo.getOutboundsortingFee();
        BigDecimal sumShortbargeFee = costInfo.getShortbargeFee() == null ? BigDecimal.ZERO : costInfo.getShortbargeFee();
        BigDecimal sumReturnFee = costInfo.getReturnFee() == null ? BigDecimal.ZERO : costInfo.getReturnFee();
        BigDecimal sumExceptionFee = costInfo.getExceptionFee() == null ? BigDecimal.ZERO : costInfo.getExceptionFee();
        BigDecimal sumOtherCost1 = costInfo.getOtherCost1() == null ? BigDecimal.ZERO : costInfo.getOtherCost1();
        BigDecimal sumOtherCost2 = costInfo.getOtherCost2() == null ? BigDecimal.ZERO : costInfo.getOtherCost2();
        BigDecimal sumOtherCost3 = costInfo.getOtherCost3() == null ? BigDecimal.ZERO : costInfo.getOtherCost3();
        BigDecimal sumOtherCost4 = costInfo.getOtherCost4() == null ? BigDecimal.ZERO : costInfo.getOtherCost4();
        BigDecimal sumOtherCost5 = costInfo.getOtherCost5() == null ? BigDecimal.ZERO : costInfo.getOtherCost5();
        BigDecimal sumOtherCost6 = costInfo.getOtherCost6() == null ? BigDecimal.ZERO : costInfo.getOtherCost6();
        BigDecimal sumOtherCost7 = costInfo.getOtherCost7() == null ? BigDecimal.ZERO : costInfo.getOtherCost7();
        BigDecimal sumOtherCost8 = costInfo.getOtherCost8() == null ? BigDecimal.ZERO : costInfo.getOtherCost8();
        BigDecimal sumOtherCost9 = costInfo.getOtherCost9() == null ? BigDecimal.ZERO : costInfo.getOtherCost9();
        BigDecimal sumOtherCost10 = costInfo.getOtherCost10() == null ? BigDecimal.ZERO : costInfo.getOtherCost10();
        BigDecimal sumOtherCost11 = costInfo.getOtherCost11() == null ? BigDecimal.ZERO : costInfo.getOtherCost11();
        BigDecimal sumOtherCost12 = costInfo.getOtherCost12() == null ? BigDecimal.ZERO : costInfo.getOtherCost12();


        //分摊金额
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;
        for (int i = 0; i < orderInfos.size(); i++) {
            if (i == orderInfos.size() - 1) {
                idx = i;
                continue;
            }
            AutoYfCodeInfo orderInfo = orderInfos.get(i);
            BigDecimal weightRadio = orderInfo.getTotalWeight().divide(totalWeight, 2, RoundingMode.DOWN);
            BmsYfcostInfo shareInfo = new BmsYfcostInfo();
            shareInfo.setFreight(sumFreight.multiply(weightRadio));
            shareInfo.setDeliveryFee(sumDeliveryFee.multiply(weightRadio));
            shareInfo.setUltrafarFee(sumUltrafarFee.multiply(weightRadio));
            shareInfo.setSuperframesFee(sumSuperframesFee.multiply(weightRadio));
            shareInfo.setExcessFee(sumExcessFee.multiply(weightRadio));
            shareInfo.setReduceFee(sumReduceFee.multiply(weightRadio));
            shareInfo.setOutboundsortingFee(sumOutboundsortingFee.multiply(weightRadio));
            shareInfo.setShortbargeFee(sumShortbargeFee.multiply(weightRadio));
            shareInfo.setReturnFee(sumReturnFee.multiply(weightRadio));
            shareInfo.setExceptionFee(sumExceptionFee.multiply(weightRadio));
            shareInfo.setOtherCost1(sumOtherCost1.multiply(weightRadio));
            shareInfo.setOtherCost2(sumOtherCost2.multiply(weightRadio));
            shareInfo.setOtherCost3(sumOtherCost3.multiply(weightRadio));
            shareInfo.setOtherCost4(sumOtherCost4.multiply(weightRadio));
            shareInfo.setOtherCost5(sumOtherCost5.multiply(weightRadio));
            shareInfo.setOtherCost6(sumOtherCost6.multiply(weightRadio));
            shareInfo.setOtherCost7(sumOtherCost7.multiply(weightRadio));
            shareInfo.setOtherCost8(sumOtherCost8.multiply(weightRadio));
            shareInfo.setOtherCost9(sumOtherCost9.multiply(weightRadio));
            shareInfo.setOtherCost10(sumOtherCost10.multiply(weightRadio));
            shareInfo.setOtherCost11(sumOtherCost11.multiply(weightRadio));
            shareInfo.setOtherCost12(sumOtherCost12.multiply(weightRadio));
            shareInfo.setShareType(1);
            shareInfo.setShareAmount(shareInfo.getFreight()
                    .add(shareInfo.getDeliveryFee())
                    .add(shareInfo.getUltrafarFee())
                    .add(shareInfo.getSuperframesFee())
                    .add(shareInfo.getExcessFee())
                    .add(shareInfo.getReduceFee())
                    .add(shareInfo.getOutboundsortingFee())
                    .add(shareInfo.getShortbargeFee())
                    .add(shareInfo.getReturnFee())
                    .add(shareInfo.getExceptionFee())
                    .add(shareInfo.getOtherCost1())
                    .add(shareInfo.getOtherCost2())
                    .add(shareInfo.getOtherCost3())
                    .add(shareInfo.getOtherCost4())
                    .add(shareInfo.getOtherCost5())
                    .add(shareInfo.getOtherCost6())
                    .add(shareInfo.getOtherCost7())
                    .add(shareInfo.getOtherCost8())
                    .add(shareInfo.getOtherCost9())
                    .add(shareInfo.getOtherCost10())
                    .add(shareInfo.getOtherCost11())
                    .add(shareInfo.getOtherCost12())
            );
            runningFreightAmount = runningFreightAmount.add(shareInfo.getFreight());
            runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(shareInfo.getDeliveryFee());
            runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(shareInfo.getUltrafarFee());
            runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(shareInfo.getSuperframesFee());
            runningExcessFeeAmount = runningExcessFeeAmount.add(shareInfo.getExcessFee());
            runningReduceFeeAmount = runningReduceFeeAmount.add(shareInfo.getReduceFee());
            runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(shareInfo.getOutboundsortingFee());
            runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(shareInfo.getShortbargeFee());
            runningReturnFeeAmount = runningReturnFeeAmount.add(shareInfo.getReturnFee());
            runningExceptionFeeAmount = runningExceptionFeeAmount.add(shareInfo.getExceptionFee());
            runningOtherCost1Amount = runningOtherCost1Amount.add(shareInfo.getOtherCost1());
            runningOtherCost2Amount = runningOtherCost2Amount.add(shareInfo.getOtherCost2());
            runningOtherCost3Amount = runningOtherCost3Amount.add(shareInfo.getOtherCost3());
            runningOtherCost4Amount = runningOtherCost4Amount.add(shareInfo.getOtherCost4());
            runningOtherCost5Amount = runningOtherCost5Amount.add(shareInfo.getOtherCost5());
            runningOtherCost6Amount = runningOtherCost6Amount.add(shareInfo.getOtherCost6());
            runningOtherCost7Amount = runningOtherCost7Amount.add(shareInfo.getOtherCost7());
            runningOtherCost8Amount = runningOtherCost8Amount.add(shareInfo.getOtherCost8());
            runningOtherCost9Amount = runningOtherCost9Amount.add(shareInfo.getOtherCost9());
            runningOtherCost10Amount = runningOtherCost10Amount.add(shareInfo.getOtherCost10());
            runningOtherCost11Amount = runningOtherCost11Amount.add(shareInfo.getOtherCost11());
            runningOtherCost12Amount = runningOtherCost12Amount.add(shareInfo.getOtherCost12());
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getYfbillId(), shareInfo);
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        AutoYfCodeInfo lastOrderInfo = orderInfos.get(idx);
        BmsYfcostInfo lastShareInfo = new BmsYfcostInfo();
        lastShareInfo.setFreight(sumFreight.subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(sumDeliveryFee.subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(sumUltrafarFee.subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(sumSuperframesFee.subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(sumExcessFee.subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(sumReduceFee.subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(sumOutboundsortingFee.subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(sumShortbargeFee.subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(sumReturnFee.subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(sumExceptionFee.subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(sumOtherCost1.subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(sumOtherCost2.subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(sumOtherCost3.subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(sumOtherCost4.subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(sumOtherCost5.subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(sumOtherCost6.subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(sumOtherCost7.subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(sumOtherCost8.subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(sumOtherCost9.subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(sumOtherCost10.subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(sumOtherCost11.subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(sumOtherCost12.subtract(runningOtherCost12Amount));
        returnData.put(resultType.equals(2) ? lastOrderInfo.getRelateCode() : lastOrderInfo.getYfbillId(), lastShareInfo);
        return returnData;
    }


    /**
     * 按重量金额分摊-应付
     *
     * @param costInfo   费用信息
     * @param orderInfos 单据信息
     * @param resultType 1:按单号id，2:按单号
     * @return 按重量分摊的金额
     */
    public static Map<String, BmsYfexpensesMiddleShare> splitAmountByYfShareWeight2(BmsYfCostMainInfo costInfo
            , List<BmsYfexpensesMiddleShare> orderInfos
            , Integer resultType) {
        Map<String, BmsYfexpensesMiddleShare> returnData = new LinkedHashMap<>();

        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            BmsYfexpensesMiddleShare orderInfo = orderInfos.get(0);
            BmsYfexpensesMiddleShare shareInfo = new BmsYfexpensesMiddleShare();
            shareInfo.setFreight(costInfo.getFreight());
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee());
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee());
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee());
            shareInfo.setExcessFee(costInfo.getExcessFee());
            shareInfo.setReduceFee(costInfo.getReduceFee());
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee());
            shareInfo.setReturnFee(costInfo.getReturnFee());
            shareInfo.setExceptionFee(costInfo.getExceptionFee());
            shareInfo.setOtherCost1(costInfo.getOtherCost1());
            shareInfo.setOtherCost2(costInfo.getOtherCost2());
            shareInfo.setOtherCost3(costInfo.getOtherCost3());
            shareInfo.setOtherCost4(costInfo.getOtherCost4());
            shareInfo.setOtherCost5(costInfo.getOtherCost5());
            shareInfo.setOtherCost6(costInfo.getOtherCost6());
            shareInfo.setOtherCost7(costInfo.getOtherCost7());
            shareInfo.setOtherCost8(costInfo.getOtherCost8());
            shareInfo.setOtherCost9(costInfo.getOtherCost9());
            shareInfo.setOtherCost10(costInfo.getOtherCost10());
            shareInfo.setOtherCost11(costInfo.getOtherCost11());
            shareInfo.setOtherCost12(costInfo.getOtherCost12());
            shareInfo.setShareType(2);
            shareInfo.setShareAmount(costInfo.getSumFee());
            // 应付要额外加一个作业单id赋值
            switch (resultType) {
                case 1:
                    returnData.put(orderInfo.getYfbillId(), shareInfo);
                    break;
                case 2:
                    returnData.put(orderInfo.getRelateCode(), shareInfo);
                    break;
                case 3:
                    returnData.put(orderInfo.getId().toString(), shareInfo);
                    break;
            }
            return returnData;
        }

        BigDecimal totalWeight = BigDecimal.ZERO;
        //获取总重量
        for (BmsYfexpensesMiddleShare orderInfo : orderInfos) {
            totalWeight = totalWeight.add(orderInfo.getTotalWeight() != null ? orderInfo.getTotalWeight() : BigDecimal.ZERO);
        }

        //主费用信息
        BigDecimal sumFreight = costInfo.getFreight() == null ? BigDecimal.ZERO : costInfo.getFreight();
        BigDecimal sumDeliveryFee = costInfo.getDeliveryFee() == null ? BigDecimal.ZERO : costInfo.getDeliveryFee();
        BigDecimal sumUltrafarFee = costInfo.getUltrafarFee() == null ? BigDecimal.ZERO : costInfo.getUltrafarFee();
        BigDecimal sumSuperframesFee = costInfo.getSuperframesFee() == null ? BigDecimal.ZERO : costInfo.getSuperframesFee();
        BigDecimal sumExcessFee = costInfo.getExcessFee() == null ? BigDecimal.ZERO : costInfo.getExcessFee();
        BigDecimal sumReduceFee = costInfo.getReduceFee() == null ? BigDecimal.ZERO : costInfo.getReduceFee();
        BigDecimal sumOutboundsortingFee = costInfo.getOutboundsortingFee() == null ? BigDecimal.ZERO : costInfo.getOutboundsortingFee();
        BigDecimal sumShortbargeFee = costInfo.getShortbargeFee() == null ? BigDecimal.ZERO : costInfo.getShortbargeFee();
        BigDecimal sumReturnFee = costInfo.getReturnFee() == null ? BigDecimal.ZERO : costInfo.getReturnFee();
        BigDecimal sumExceptionFee = costInfo.getExceptionFee() == null ? BigDecimal.ZERO : costInfo.getExceptionFee();
        BigDecimal sumOtherCost1 = costInfo.getOtherCost1() == null ? BigDecimal.ZERO : costInfo.getOtherCost1();
        BigDecimal sumOtherCost2 = costInfo.getOtherCost2() == null ? BigDecimal.ZERO : costInfo.getOtherCost2();
        BigDecimal sumOtherCost3 = costInfo.getOtherCost3() == null ? BigDecimal.ZERO : costInfo.getOtherCost3();
        BigDecimal sumOtherCost4 = costInfo.getOtherCost4() == null ? BigDecimal.ZERO : costInfo.getOtherCost4();
        BigDecimal sumOtherCost5 = costInfo.getOtherCost5() == null ? BigDecimal.ZERO : costInfo.getOtherCost5();
        BigDecimal sumOtherCost6 = costInfo.getOtherCost6() == null ? BigDecimal.ZERO : costInfo.getOtherCost6();
        BigDecimal sumOtherCost7 = costInfo.getOtherCost7() == null ? BigDecimal.ZERO : costInfo.getOtherCost7();
        BigDecimal sumOtherCost8 = costInfo.getOtherCost8() == null ? BigDecimal.ZERO : costInfo.getOtherCost8();
        BigDecimal sumOtherCost9 = costInfo.getOtherCost9() == null ? BigDecimal.ZERO : costInfo.getOtherCost9();
        BigDecimal sumOtherCost10 = costInfo.getOtherCost10() == null ? BigDecimal.ZERO : costInfo.getOtherCost10();
        BigDecimal sumOtherCost11 = costInfo.getOtherCost11() == null ? BigDecimal.ZERO : costInfo.getOtherCost11();
        BigDecimal sumOtherCost12 = costInfo.getOtherCost12() == null ? BigDecimal.ZERO : costInfo.getOtherCost12();


        //分摊金额
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;
        for (int i = 0; i < orderInfos.size(); i++) {
            if (i == orderInfos.size() - 1) {
                idx = i;
                continue;
            }
            BmsYfexpensesMiddleShare orderInfo = orderInfos.get(i);
            BigDecimal weightRadio = orderInfo.getTotalWeight().divide(totalWeight, 2, RoundingMode.DOWN);
            BmsYfexpensesMiddleShare shareInfo = new BmsYfexpensesMiddleShare();
            shareInfo.setFreight(sumFreight.multiply(weightRadio));
            shareInfo.setDeliveryFee(sumDeliveryFee.multiply(weightRadio));
            shareInfo.setUltrafarFee(sumUltrafarFee.multiply(weightRadio));
            shareInfo.setSuperframesFee(sumSuperframesFee.multiply(weightRadio));
            shareInfo.setExcessFee(sumExcessFee.multiply(weightRadio));
            shareInfo.setReduceFee(sumReduceFee.multiply(weightRadio));
            shareInfo.setOutboundsortingFee(sumOutboundsortingFee.multiply(weightRadio));
            shareInfo.setShortbargeFee(sumShortbargeFee.multiply(weightRadio));
            shareInfo.setReturnFee(sumReturnFee.multiply(weightRadio));
            shareInfo.setExceptionFee(sumExceptionFee.multiply(weightRadio));
            shareInfo.setOtherCost1(sumOtherCost1.multiply(weightRadio));
            shareInfo.setOtherCost2(sumOtherCost2.multiply(weightRadio));
            shareInfo.setOtherCost3(sumOtherCost3.multiply(weightRadio));
            shareInfo.setOtherCost4(sumOtherCost4.multiply(weightRadio));
            shareInfo.setOtherCost5(sumOtherCost5.multiply(weightRadio));
            shareInfo.setOtherCost6(sumOtherCost6.multiply(weightRadio));
            shareInfo.setOtherCost7(sumOtherCost7.multiply(weightRadio));
            shareInfo.setOtherCost8(sumOtherCost8.multiply(weightRadio));
            shareInfo.setOtherCost9(sumOtherCost9.multiply(weightRadio));
            shareInfo.setOtherCost10(sumOtherCost10.multiply(weightRadio));
            shareInfo.setOtherCost11(sumOtherCost11.multiply(weightRadio));
            shareInfo.setOtherCost12(sumOtherCost12.multiply(weightRadio));
            shareInfo.setShareType(1);
            shareInfo.setShareAmount(shareInfo.getFreight()
                    .add(shareInfo.getDeliveryFee())
                    .add(shareInfo.getUltrafarFee())
                    .add(shareInfo.getSuperframesFee())
                    .add(shareInfo.getExcessFee())
                    .add(shareInfo.getReduceFee())
                    .add(shareInfo.getOutboundsortingFee())
                    .add(shareInfo.getShortbargeFee())
                    .add(shareInfo.getReturnFee())
                    .add(shareInfo.getExceptionFee())
                    .add(shareInfo.getOtherCost1())
                    .add(shareInfo.getOtherCost2())
                    .add(shareInfo.getOtherCost3())
                    .add(shareInfo.getOtherCost4())
                    .add(shareInfo.getOtherCost5())
                    .add(shareInfo.getOtherCost6())
                    .add(shareInfo.getOtherCost7())
                    .add(shareInfo.getOtherCost8())
                    .add(shareInfo.getOtherCost9())
                    .add(shareInfo.getOtherCost10())
                    .add(shareInfo.getOtherCost11())
                    .add(shareInfo.getOtherCost12())
            );
            runningFreightAmount = runningFreightAmount.add(shareInfo.getFreight());
            runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(shareInfo.getDeliveryFee());
            runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(shareInfo.getUltrafarFee());
            runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(shareInfo.getSuperframesFee());
            runningExcessFeeAmount = runningExcessFeeAmount.add(shareInfo.getExcessFee());
            runningReduceFeeAmount = runningReduceFeeAmount.add(shareInfo.getReduceFee());
            runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(shareInfo.getOutboundsortingFee());
            runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(shareInfo.getShortbargeFee());
            runningReturnFeeAmount = runningReturnFeeAmount.add(shareInfo.getReturnFee());
            runningExceptionFeeAmount = runningExceptionFeeAmount.add(shareInfo.getExceptionFee());
            runningOtherCost1Amount = runningOtherCost1Amount.add(shareInfo.getOtherCost1());
            runningOtherCost2Amount = runningOtherCost2Amount.add(shareInfo.getOtherCost2());
            runningOtherCost3Amount = runningOtherCost3Amount.add(shareInfo.getOtherCost3());
            runningOtherCost4Amount = runningOtherCost4Amount.add(shareInfo.getOtherCost4());
            runningOtherCost5Amount = runningOtherCost5Amount.add(shareInfo.getOtherCost5());
            runningOtherCost6Amount = runningOtherCost6Amount.add(shareInfo.getOtherCost6());
            runningOtherCost7Amount = runningOtherCost7Amount.add(shareInfo.getOtherCost7());
            runningOtherCost8Amount = runningOtherCost8Amount.add(shareInfo.getOtherCost8());
            runningOtherCost9Amount = runningOtherCost9Amount.add(shareInfo.getOtherCost9());
            runningOtherCost10Amount = runningOtherCost10Amount.add(shareInfo.getOtherCost10());
            runningOtherCost11Amount = runningOtherCost11Amount.add(shareInfo.getOtherCost11());
            runningOtherCost12Amount = runningOtherCost12Amount.add(shareInfo.getOtherCost12());
            // 应付要额外加一个作业单id赋值
            switch (resultType) {
                case 1:
                    returnData.put(orderInfo.getYfbillId(), shareInfo);
                    break;
                case 2:
                    returnData.put(orderInfo.getRelateCode(), shareInfo);
                    break;
                case 3:
                    returnData.put(orderInfo.getId().toString(), shareInfo);
                    break;
            }
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        BmsYfexpensesMiddleShare lastOrderInfo = orderInfos.get(idx);
        BmsYfexpensesMiddleShare lastShareInfo = new BmsYfexpensesMiddleShare();
        lastShareInfo.setFreight(sumFreight.subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(sumDeliveryFee.subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(sumUltrafarFee.subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(sumSuperframesFee.subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(sumExcessFee.subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(sumReduceFee.subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(sumOutboundsortingFee.subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(sumShortbargeFee.subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(sumReturnFee.subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(sumExceptionFee.subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(sumOtherCost1.subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(sumOtherCost2.subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(sumOtherCost3.subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(sumOtherCost4.subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(sumOtherCost5.subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(sumOtherCost6.subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(sumOtherCost7.subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(sumOtherCost8.subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(sumOtherCost9.subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(sumOtherCost10.subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(sumOtherCost11.subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(sumOtherCost12.subtract(runningOtherCost12Amount));
        // 应付要额外加一个作业单id赋值
        switch (resultType) {
            case 1:
                returnData.put(lastOrderInfo.getYfbillId(), lastOrderInfo);
                break;
            case 2:
                returnData.put(lastOrderInfo.getRelateCode(), lastOrderInfo);
                break;
            case 3:
                returnData.put(lastOrderInfo.getId().toString(), lastOrderInfo);
                break;
        }
        return returnData;
    }


    /**
     * 按体积金额分摊
     *
     * @param costInfo   主费用明细
     * @param orderInfos 单据信息
     * @param resultType 1:key单据id 2:单据单号
     * @return 按体积分摊的金额
     */
    public static Map<String, BmsYfcostInfo> splitAmountByYfVolumn2(BmsYfCostMainInfo costInfo
            , List<AutoYfCodeInfo> orderInfos
            , Integer resultType) {
        Map<String, BmsYfcostInfo> returnData = new LinkedHashMap<>();
        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            AutoYfCodeInfo orderInfo = orderInfos.get(0);
            BmsYfcostInfo shareInfo = new BmsYfcostInfo();
            shareInfo.setFreight(costInfo.getFreight());
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee());
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee());
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee());
            shareInfo.setExcessFee(costInfo.getExcessFee());
            shareInfo.setReduceFee(costInfo.getReduceFee());
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee());
            shareInfo.setReturnFee(costInfo.getReturnFee());
            shareInfo.setExceptionFee(costInfo.getExceptionFee());
            shareInfo.setOtherCost1(costInfo.getOtherCost1());
            shareInfo.setOtherCost2(costInfo.getOtherCost2());
            shareInfo.setOtherCost3(costInfo.getOtherCost3());
            shareInfo.setOtherCost4(costInfo.getOtherCost4());
            shareInfo.setOtherCost5(costInfo.getOtherCost5());
            shareInfo.setOtherCost6(costInfo.getOtherCost6());
            shareInfo.setOtherCost7(costInfo.getOtherCost7());
            shareInfo.setOtherCost8(costInfo.getOtherCost8());
            shareInfo.setOtherCost9(costInfo.getOtherCost9());
            shareInfo.setOtherCost10(costInfo.getOtherCost10());
            shareInfo.setOtherCost11(costInfo.getOtherCost11());
            shareInfo.setOtherCost12(costInfo.getOtherCost12());
            shareInfo.setShareType(3);
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getYfbillId(), shareInfo);
            return returnData;
        }


        //主费用信息
        BigDecimal sumFreight = costInfo.getFreight() == null ? BigDecimal.ZERO : costInfo.getFreight();
        BigDecimal sumDeliveryFee = costInfo.getDeliveryFee() == null ? BigDecimal.ZERO : costInfo.getDeliveryFee();
        BigDecimal sumUltrafarFee = costInfo.getUltrafarFee() == null ? BigDecimal.ZERO : costInfo.getUltrafarFee();
        BigDecimal sumSuperframesFee = costInfo.getSuperframesFee() == null ? BigDecimal.ZERO : costInfo.getSuperframesFee();
        BigDecimal sumExcessFee = costInfo.getExcessFee() == null ? BigDecimal.ZERO : costInfo.getExcessFee();
        BigDecimal sumReduceFee = costInfo.getReduceFee() == null ? BigDecimal.ZERO : costInfo.getReduceFee();
        BigDecimal sumOutboundsortingFee = costInfo.getOutboundsortingFee() == null ? BigDecimal.ZERO : costInfo.getOutboundsortingFee();
        BigDecimal sumShortbargeFee = costInfo.getShortbargeFee() == null ? BigDecimal.ZERO : costInfo.getShortbargeFee();
        BigDecimal sumReturnFee = costInfo.getReturnFee() == null ? BigDecimal.ZERO : costInfo.getReturnFee();
        BigDecimal sumExceptionFee = costInfo.getExceptionFee() == null ? BigDecimal.ZERO : costInfo.getExceptionFee();
        BigDecimal sumOtherCost1 = costInfo.getOtherCost1() == null ? BigDecimal.ZERO : costInfo.getOtherCost1();
        BigDecimal sumOtherCost2 = costInfo.getOtherCost2() == null ? BigDecimal.ZERO : costInfo.getOtherCost2();
        BigDecimal sumOtherCost3 = costInfo.getOtherCost3() == null ? BigDecimal.ZERO : costInfo.getOtherCost3();
        BigDecimal sumOtherCost4 = costInfo.getOtherCost4() == null ? BigDecimal.ZERO : costInfo.getOtherCost4();
        BigDecimal sumOtherCost5 = costInfo.getOtherCost5() == null ? BigDecimal.ZERO : costInfo.getOtherCost5();
        BigDecimal sumOtherCost6 = costInfo.getOtherCost6() == null ? BigDecimal.ZERO : costInfo.getOtherCost6();
        BigDecimal sumOtherCost7 = costInfo.getOtherCost7() == null ? BigDecimal.ZERO : costInfo.getOtherCost7();
        BigDecimal sumOtherCost8 = costInfo.getOtherCost8() == null ? BigDecimal.ZERO : costInfo.getOtherCost8();
        BigDecimal sumOtherCost9 = costInfo.getOtherCost9() == null ? BigDecimal.ZERO : costInfo.getOtherCost9();
        BigDecimal sumOtherCost10 = costInfo.getOtherCost10() == null ? BigDecimal.ZERO : costInfo.getOtherCost10();
        BigDecimal sumOtherCost11 = costInfo.getOtherCost11() == null ? BigDecimal.ZERO : costInfo.getOtherCost11();
        BigDecimal sumOtherCost12 = costInfo.getOtherCost12() == null ? BigDecimal.ZERO : costInfo.getOtherCost12();


        BigDecimal totalVolume = BigDecimal.ZERO;
        //获取总重量
        for (AutoYfCodeInfo orderInfo : orderInfos) {
            totalVolume = totalVolume.add(orderInfo.getTotalVolume() != null ? orderInfo.getTotalVolume() : BigDecimal.ZERO);
        }

        //分摊金额
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;
        for (int i = 0; i < orderInfos.size(); i++) {
            if (i == orderInfos.size() - 1) {
                idx = i;
                continue;
            }
            AutoYfCodeInfo orderInfo = orderInfos.get(i);
            BigDecimal volumnRadio = orderInfo.getTotalVolume().divide(totalVolume, 2, RoundingMode.DOWN);
            BmsYfcostInfo shareInfo = new BmsYfcostInfo();
            shareInfo.setFreight(sumFreight.multiply(volumnRadio));
            shareInfo.setDeliveryFee(sumDeliveryFee.multiply(volumnRadio));
            shareInfo.setUltrafarFee(sumUltrafarFee.multiply(volumnRadio));
            shareInfo.setSuperframesFee(sumSuperframesFee.multiply(volumnRadio));
            shareInfo.setExcessFee(sumExcessFee.multiply(volumnRadio));
            shareInfo.setReduceFee(sumReduceFee.multiply(volumnRadio));
            shareInfo.setOutboundsortingFee(sumOutboundsortingFee.multiply(volumnRadio));
            shareInfo.setShortbargeFee(sumShortbargeFee.multiply(volumnRadio));
            shareInfo.setReturnFee(sumReturnFee.multiply(volumnRadio));
            shareInfo.setExceptionFee(sumExceptionFee.multiply(volumnRadio));
            shareInfo.setOtherCost1(sumOtherCost1.multiply(volumnRadio));
            shareInfo.setOtherCost2(sumOtherCost2.multiply(volumnRadio));
            shareInfo.setOtherCost3(sumOtherCost3.multiply(volumnRadio));
            shareInfo.setOtherCost4(sumOtherCost4.multiply(volumnRadio));
            shareInfo.setOtherCost5(sumOtherCost5.multiply(volumnRadio));
            shareInfo.setOtherCost6(sumOtherCost6.multiply(volumnRadio));
            shareInfo.setOtherCost7(sumOtherCost7.multiply(volumnRadio));
            shareInfo.setOtherCost8(sumOtherCost8.multiply(volumnRadio));
            shareInfo.setOtherCost9(sumOtherCost9.multiply(volumnRadio));
            shareInfo.setOtherCost10(sumOtherCost10.multiply(volumnRadio));
            shareInfo.setOtherCost11(sumOtherCost11.multiply(volumnRadio));
            shareInfo.setOtherCost12(sumOtherCost12.multiply(volumnRadio));
            shareInfo.setShareType(3);

            runningFreightAmount = runningFreightAmount.add(shareInfo.getFreight());
            runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(shareInfo.getDeliveryFee());
            runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(shareInfo.getUltrafarFee());
            runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(shareInfo.getSuperframesFee());
            runningExcessFeeAmount = runningExcessFeeAmount.add(shareInfo.getExcessFee());
            runningReduceFeeAmount = runningReduceFeeAmount.add(shareInfo.getReduceFee());
            runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(shareInfo.getOutboundsortingFee());
            runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(shareInfo.getShortbargeFee());
            runningReturnFeeAmount = runningReturnFeeAmount.add(shareInfo.getReturnFee());
            runningExceptionFeeAmount = runningExceptionFeeAmount.add(shareInfo.getExceptionFee());
            runningOtherCost1Amount = runningOtherCost1Amount.add(shareInfo.getOtherCost1());
            runningOtherCost2Amount = runningOtherCost2Amount.add(shareInfo.getOtherCost2());
            runningOtherCost3Amount = runningOtherCost3Amount.add(shareInfo.getOtherCost3());
            runningOtherCost4Amount = runningOtherCost4Amount.add(shareInfo.getOtherCost4());
            runningOtherCost5Amount = runningOtherCost5Amount.add(shareInfo.getOtherCost5());
            runningOtherCost6Amount = runningOtherCost6Amount.add(shareInfo.getOtherCost6());
            runningOtherCost7Amount = runningOtherCost7Amount.add(shareInfo.getOtherCost7());
            runningOtherCost8Amount = runningOtherCost8Amount.add(shareInfo.getOtherCost8());
            runningOtherCost9Amount = runningOtherCost9Amount.add(shareInfo.getOtherCost9());
            runningOtherCost10Amount = runningOtherCost10Amount.add(shareInfo.getOtherCost10());
            runningOtherCost11Amount = runningOtherCost11Amount.add(shareInfo.getOtherCost11());
            runningOtherCost12Amount = runningOtherCost12Amount.add(shareInfo.getOtherCost12());
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getYfbillId(), shareInfo);
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        AutoYfCodeInfo lastOrderInfo = orderInfos.get(idx);
        BmsYfcostInfo lastShareInfo = new BmsYfcostInfo();
        lastShareInfo.setFreight(sumFreight.subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(sumDeliveryFee.subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(sumUltrafarFee.subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(sumSuperframesFee.subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(sumExcessFee.subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(sumReduceFee.subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(sumOutboundsortingFee.subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(sumShortbargeFee.subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(sumReturnFee.subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(sumExceptionFee.subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(sumOtherCost1.subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(sumOtherCost2.subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(sumOtherCost3.subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(sumOtherCost4.subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(sumOtherCost5.subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(sumOtherCost6.subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(sumOtherCost7.subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(sumOtherCost8.subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(sumOtherCost9.subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(sumOtherCost10.subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(sumOtherCost11.subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(sumOtherCost12.subtract(runningOtherCost12Amount));
        returnData.put(resultType.equals(2) ? lastOrderInfo.getRelateCode() : lastOrderInfo.getYfbillId(), lastShareInfo);
        return returnData;
    }


    /**
     * 按体积金额分摊
     *
     * @param costInfo   主费用明细
     * @param orderInfos 单据信息
     * @param resultType 1:key单据id 2:单据单号
     * @return 按体积分摊的金额
     */
    public static Map<String, BmsYfexpensesMiddleShare> splitAmountByYfShareVolumn2(BmsYfCostMainInfo costInfo
            , List<BmsYfexpensesMiddleShare> orderInfos
            , Integer resultType) {
        Map<String, BmsYfexpensesMiddleShare> returnData = new LinkedHashMap<>();
        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            BmsYfexpensesMiddleShare orderInfo = orderInfos.get(0);
            BmsYfexpensesMiddleShare shareInfo = new BmsYfexpensesMiddleShare();
            shareInfo.setFreight(costInfo.getFreight());
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee());
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee());
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee());
            shareInfo.setExcessFee(costInfo.getExcessFee());
            shareInfo.setReduceFee(costInfo.getReduceFee());
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee());
            shareInfo.setReturnFee(costInfo.getReturnFee());
            shareInfo.setExceptionFee(costInfo.getExceptionFee());
            shareInfo.setOtherCost1(costInfo.getOtherCost1());
            shareInfo.setOtherCost2(costInfo.getOtherCost2());
            shareInfo.setOtherCost3(costInfo.getOtherCost3());
            shareInfo.setOtherCost4(costInfo.getOtherCost4());
            shareInfo.setOtherCost5(costInfo.getOtherCost5());
            shareInfo.setOtherCost6(costInfo.getOtherCost6());
            shareInfo.setOtherCost7(costInfo.getOtherCost7());
            shareInfo.setOtherCost8(costInfo.getOtherCost8());
            shareInfo.setOtherCost9(costInfo.getOtherCost9());
            shareInfo.setOtherCost10(costInfo.getOtherCost10());
            shareInfo.setOtherCost11(costInfo.getOtherCost11());
            shareInfo.setOtherCost12(costInfo.getOtherCost12());
            shareInfo.setShareType(3);
            // 应付要额外加一个作业单id赋值
            switch (resultType) {
                case 1:
                    returnData.put(orderInfo.getYfbillId(), shareInfo);
                    break;
                case 2:
                    returnData.put(orderInfo.getRelateCode(), shareInfo);
                    break;
                case 3:
                    returnData.put(orderInfo.getId().toString(), shareInfo);
                    break;
            }
            return returnData;
        }


        //主费用信息
        BigDecimal sumFreight = costInfo.getFreight() == null ? BigDecimal.ZERO : costInfo.getFreight();
        BigDecimal sumDeliveryFee = costInfo.getDeliveryFee() == null ? BigDecimal.ZERO : costInfo.getDeliveryFee();
        BigDecimal sumUltrafarFee = costInfo.getUltrafarFee() == null ? BigDecimal.ZERO : costInfo.getUltrafarFee();
        BigDecimal sumSuperframesFee = costInfo.getSuperframesFee() == null ? BigDecimal.ZERO : costInfo.getSuperframesFee();
        BigDecimal sumExcessFee = costInfo.getExcessFee() == null ? BigDecimal.ZERO : costInfo.getExcessFee();
        BigDecimal sumReduceFee = costInfo.getReduceFee() == null ? BigDecimal.ZERO : costInfo.getReduceFee();
        BigDecimal sumOutboundsortingFee = costInfo.getOutboundsortingFee() == null ? BigDecimal.ZERO : costInfo.getOutboundsortingFee();
        BigDecimal sumShortbargeFee = costInfo.getShortbargeFee() == null ? BigDecimal.ZERO : costInfo.getShortbargeFee();
        BigDecimal sumReturnFee = costInfo.getReturnFee() == null ? BigDecimal.ZERO : costInfo.getReturnFee();
        BigDecimal sumExceptionFee = costInfo.getExceptionFee() == null ? BigDecimal.ZERO : costInfo.getExceptionFee();
        BigDecimal sumOtherCost1 = costInfo.getOtherCost1() == null ? BigDecimal.ZERO : costInfo.getOtherCost1();
        BigDecimal sumOtherCost2 = costInfo.getOtherCost2() == null ? BigDecimal.ZERO : costInfo.getOtherCost2();
        BigDecimal sumOtherCost3 = costInfo.getOtherCost3() == null ? BigDecimal.ZERO : costInfo.getOtherCost3();
        BigDecimal sumOtherCost4 = costInfo.getOtherCost4() == null ? BigDecimal.ZERO : costInfo.getOtherCost4();
        BigDecimal sumOtherCost5 = costInfo.getOtherCost5() == null ? BigDecimal.ZERO : costInfo.getOtherCost5();
        BigDecimal sumOtherCost6 = costInfo.getOtherCost6() == null ? BigDecimal.ZERO : costInfo.getOtherCost6();
        BigDecimal sumOtherCost7 = costInfo.getOtherCost7() == null ? BigDecimal.ZERO : costInfo.getOtherCost7();
        BigDecimal sumOtherCost8 = costInfo.getOtherCost8() == null ? BigDecimal.ZERO : costInfo.getOtherCost8();
        BigDecimal sumOtherCost9 = costInfo.getOtherCost9() == null ? BigDecimal.ZERO : costInfo.getOtherCost9();
        BigDecimal sumOtherCost10 = costInfo.getOtherCost10() == null ? BigDecimal.ZERO : costInfo.getOtherCost10();
        BigDecimal sumOtherCost11 = costInfo.getOtherCost11() == null ? BigDecimal.ZERO : costInfo.getOtherCost11();
        BigDecimal sumOtherCost12 = costInfo.getOtherCost12() == null ? BigDecimal.ZERO : costInfo.getOtherCost12();


        BigDecimal totalVolume = BigDecimal.ZERO;
        //获取总重量
        for (BmsYfexpensesMiddleShare orderInfo : orderInfos) {
            totalVolume = totalVolume.add(orderInfo.getTotalVolume() != null ? orderInfo.getTotalVolume() : BigDecimal.ZERO);
        }

        //分摊金额
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;
        for (int i = 0; i < orderInfos.size(); i++) {
            if (i == orderInfos.size() - 1) {
                idx = i;
                continue;
            }
            BmsYfexpensesMiddleShare orderInfo = orderInfos.get(i);
            BigDecimal volumnRadio = orderInfo.getTotalVolume().divide(totalVolume, 2, RoundingMode.DOWN);
            BmsYfexpensesMiddleShare shareInfo = new BmsYfexpensesMiddleShare();
            shareInfo.setFreight(sumFreight.multiply(volumnRadio));
            shareInfo.setDeliveryFee(sumDeliveryFee.multiply(volumnRadio));
            shareInfo.setUltrafarFee(sumUltrafarFee.multiply(volumnRadio));
            shareInfo.setSuperframesFee(sumSuperframesFee.multiply(volumnRadio));
            shareInfo.setExcessFee(sumExcessFee.multiply(volumnRadio));
            shareInfo.setReduceFee(sumReduceFee.multiply(volumnRadio));
            shareInfo.setOutboundsortingFee(sumOutboundsortingFee.multiply(volumnRadio));
            shareInfo.setShortbargeFee(sumShortbargeFee.multiply(volumnRadio));
            shareInfo.setReturnFee(sumReturnFee.multiply(volumnRadio));
            shareInfo.setExceptionFee(sumExceptionFee.multiply(volumnRadio));
            shareInfo.setOtherCost1(sumOtherCost1.multiply(volumnRadio));
            shareInfo.setOtherCost2(sumOtherCost2.multiply(volumnRadio));
            shareInfo.setOtherCost3(sumOtherCost3.multiply(volumnRadio));
            shareInfo.setOtherCost4(sumOtherCost4.multiply(volumnRadio));
            shareInfo.setOtherCost5(sumOtherCost5.multiply(volumnRadio));
            shareInfo.setOtherCost6(sumOtherCost6.multiply(volumnRadio));
            shareInfo.setOtherCost7(sumOtherCost7.multiply(volumnRadio));
            shareInfo.setOtherCost8(sumOtherCost8.multiply(volumnRadio));
            shareInfo.setOtherCost9(sumOtherCost9.multiply(volumnRadio));
            shareInfo.setOtherCost10(sumOtherCost10.multiply(volumnRadio));
            shareInfo.setOtherCost11(sumOtherCost11.multiply(volumnRadio));
            shareInfo.setOtherCost12(sumOtherCost12.multiply(volumnRadio));
            shareInfo.setShareType(3);

            runningFreightAmount = runningFreightAmount.add(shareInfo.getFreight());
            runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(shareInfo.getDeliveryFee());
            runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(shareInfo.getUltrafarFee());
            runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(shareInfo.getSuperframesFee());
            runningExcessFeeAmount = runningExcessFeeAmount.add(shareInfo.getExcessFee());
            runningReduceFeeAmount = runningReduceFeeAmount.add(shareInfo.getReduceFee());
            runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(shareInfo.getOutboundsortingFee());
            runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(shareInfo.getShortbargeFee());
            runningReturnFeeAmount = runningReturnFeeAmount.add(shareInfo.getReturnFee());
            runningExceptionFeeAmount = runningExceptionFeeAmount.add(shareInfo.getExceptionFee());
            runningOtherCost1Amount = runningOtherCost1Amount.add(shareInfo.getOtherCost1());
            runningOtherCost2Amount = runningOtherCost2Amount.add(shareInfo.getOtherCost2());
            runningOtherCost3Amount = runningOtherCost3Amount.add(shareInfo.getOtherCost3());
            runningOtherCost4Amount = runningOtherCost4Amount.add(shareInfo.getOtherCost4());
            runningOtherCost5Amount = runningOtherCost5Amount.add(shareInfo.getOtherCost5());
            runningOtherCost6Amount = runningOtherCost6Amount.add(shareInfo.getOtherCost6());
            runningOtherCost7Amount = runningOtherCost7Amount.add(shareInfo.getOtherCost7());
            runningOtherCost8Amount = runningOtherCost8Amount.add(shareInfo.getOtherCost8());
            runningOtherCost9Amount = runningOtherCost9Amount.add(shareInfo.getOtherCost9());
            runningOtherCost10Amount = runningOtherCost10Amount.add(shareInfo.getOtherCost10());
            runningOtherCost11Amount = runningOtherCost11Amount.add(shareInfo.getOtherCost11());
            runningOtherCost12Amount = runningOtherCost12Amount.add(shareInfo.getOtherCost12());
            // 应付要额外加一个作业单id赋值
            switch (resultType) {
                case 1:
                    returnData.put(orderInfo.getYfbillId(), shareInfo);
                    break;
                case 2:
                    returnData.put(orderInfo.getRelateCode(), shareInfo);
                    break;
                case 3:
                    returnData.put(orderInfo.getId().toString(), shareInfo);
                    break;
            }
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        BmsYfexpensesMiddleShare lastOrderInfo = orderInfos.get(idx);
        BmsYfexpensesMiddleShare lastShareInfo = new BmsYfexpensesMiddleShare();
        lastShareInfo.setFreight(sumFreight.subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(sumDeliveryFee.subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(sumUltrafarFee.subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(sumSuperframesFee.subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(sumExcessFee.subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(sumReduceFee.subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(sumOutboundsortingFee.subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(sumShortbargeFee.subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(sumReturnFee.subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(sumExceptionFee.subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(sumOtherCost1.subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(sumOtherCost2.subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(sumOtherCost3.subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(sumOtherCost4.subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(sumOtherCost5.subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(sumOtherCost6.subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(sumOtherCost7.subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(sumOtherCost8.subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(sumOtherCost9.subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(sumOtherCost10.subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(sumOtherCost11.subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(sumOtherCost12.subtract(runningOtherCost12Amount));
        // 应付要额外加一个作业单id赋值
        switch (resultType) {
            case 1:
                returnData.put(lastOrderInfo.getYfbillId(), lastOrderInfo);
                break;
            case 2:
                returnData.put(lastOrderInfo.getRelateCode(), lastOrderInfo);
                break;
            case 3:
                returnData.put(lastOrderInfo.getId().toString(), lastOrderInfo);
                break;
        }
        return returnData;
    }


    /**
     * 应付金额分摊-按单||作业单
     *
     * @param shareType    分摊类型 1:按订单(平均),2:按重量,3:按体积
     * @param mainCodeInfo 主金额信息
     * @param codes        分摊的作业单信息
     * @param resultType   1:key:codeId,2:key:relateCode,3:middleShareId
     * @return value1:按订单分摊结果，value2:按体积分摊结果,value3:按重量分摊结果
     */
    public static Map<String, BmsYfexpensesMiddleShare> splitAmountYfShareCodes(Integer shareType
            , BmsYfCostMainInfo mainCodeInfo
            , List<BmsYfexpensesMiddleShare> codes
            , Integer resultType) {
        if (shareType.equals(1)) {
            return splitAmountByYfOrderShare2(mainCodeInfo, codes, resultType);
        }
        if (shareType.equals(2)) {
            return splitAmountByYfShareWeight2(mainCodeInfo, codes, resultType);
        }
        if (shareType.equals(3)) {
            return splitAmountByYfShareVolumn2(mainCodeInfo, codes, resultType);
        }
        return new HashMap<>();
    }


    /**
     * 应付金额分摊-按单||作业单
     *
     * @param shareType    分摊类型 1:按订单(平均),2:按重量,3:按体积
     * @param mainCodeInfo 主金额信息
     * @param codes        分摊的作业单据信息
     * @param resultType   1:key:codeId,2:key:relateCode
     * @return value1:按订单分摊结果，value2:按体积分摊结果,value3:按重量分摊结果
     */
    public static Map<String, BmsYfexpensesMiddleSharePO> splitAmountYfCodes(Integer shareType
            , BmsYfcostMainInfoPO mainCodeInfo
            , List<BmsYfjobbillinfoPO> codes
            , Integer resultType) {
        if (shareType.equals(1)) {
            return splitAmountByYfOrder2(mainCodeInfo, codes, resultType);
        }
        if (shareType.equals(2)) {
            return splitAmountByYfWeight2(mainCodeInfo, codes, resultType);
        }
        if (shareType.equals(3)) {
            return splitAmountByYfVolumn2(mainCodeInfo, codes, resultType);
        }
        return new HashMap<>();
    }

    /**
     * 按单分摊金额-应付
     *
     * @param costInfo   总费用明细
     * @param orderInfos 涉及订单信息
     * @param resultType 处理结果类型
     * @return key:单据id或者/code
     */
    public static Map<String, BmsYfexpensesMiddleSharePO> splitAmountByYfOrder2(BmsYfcostMainInfoPO costInfo
            , List<BmsYfjobbillinfoPO> orderInfos
            , Integer resultType) {
        Map<String, BmsYfexpensesMiddleSharePO> returnData = new LinkedHashMap<>();
        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            BmsYfjobbillinfoPO orderInfo = orderInfos.get(0);
            BmsYfexpensesMiddleSharePO shareInfo = new BmsYfexpensesMiddleSharePO();
            shareInfo.setFreight(costInfo.getFreight() != null ? costInfo.getFreight() : BigDecimal.ZERO);
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee() != null ? costInfo.getDeliveryFee() : BigDecimal.ZERO);
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee() != null ? costInfo.getUltrafarFee() : BigDecimal.ZERO);
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee() != null ? costInfo.getSuperframesFee() : BigDecimal.ZERO);
            shareInfo.setExcessFee(costInfo.getExcessFee() != null ? costInfo.getExcessFee() : BigDecimal.ZERO);
            shareInfo.setReduceFee(costInfo.getReduceFee() != null ? costInfo.getReduceFee() : BigDecimal.ZERO);
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee() != null ? costInfo.getOutboundsortingFee() : BigDecimal.ZERO);
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee() != null ? costInfo.getShortbargeFee() : BigDecimal.ZERO);
            shareInfo.setReturnFee(costInfo.getReturnFee() != null ? costInfo.getReturnFee() : BigDecimal.ZERO);
            shareInfo.setExceptionFee(costInfo.getExceptionFee() != null ? costInfo.getExceptionFee() : BigDecimal.ZERO);
            shareInfo.setOtherCost1(costInfo.getOtherCost1() != null ? costInfo.getOtherCost1() : BigDecimal.ZERO);
            shareInfo.setOtherCost2(costInfo.getOtherCost2() != null ? costInfo.getOtherCost2() : BigDecimal.ZERO);
            shareInfo.setOtherCost3(costInfo.getOtherCost3() != null ? costInfo.getOtherCost3() : BigDecimal.ZERO);
            shareInfo.setOtherCost4(costInfo.getOtherCost4() != null ? costInfo.getOtherCost4() : BigDecimal.ZERO);
            shareInfo.setOtherCost5(costInfo.getOtherCost5() != null ? costInfo.getOtherCost5() : BigDecimal.ZERO);
            shareInfo.setOtherCost6(costInfo.getOtherCost6() != null ? costInfo.getOtherCost6() : BigDecimal.ZERO);
            shareInfo.setOtherCost7(costInfo.getOtherCost7() != null ? costInfo.getOtherCost7() : BigDecimal.ZERO);
            shareInfo.setOtherCost8(costInfo.getOtherCost8() != null ? costInfo.getOtherCost8() : BigDecimal.ZERO);
            shareInfo.setOtherCost9(costInfo.getOtherCost9() != null ? costInfo.getOtherCost9() : BigDecimal.ZERO);
            shareInfo.setOtherCost10(costInfo.getOtherCost10() != null ? costInfo.getOtherCost10() : BigDecimal.ZERO);
            shareInfo.setOtherCost11(costInfo.getOtherCost11() != null ? costInfo.getOtherCost11() : BigDecimal.ZERO);
            shareInfo.setOtherCost12(costInfo.getOtherCost12() != null ? costInfo.getOtherCost12() : BigDecimal.ZERO);
            shareInfo.setShareType(1);
            shareInfo.setSettleAmount(costInfo.getSettleAmount() != null ? costInfo.getSettleAmount() : BigDecimal.ZERO);
            // 应付要额外加一个作业单id赋值
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), shareInfo);
            return returnData;
        }
        //金额分摊(向下取整，避免出现最后一条金额非累加)
        BigDecimal avgfreightAmount = BigDecimal.ZERO;
        BigDecimal avgDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal avgUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal avgSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal avgExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal avgReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal avgOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal avgShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal avgReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal avgExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal avgOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal avgOtherCost12Amount = BigDecimal.ZERO;

        BigDecimal sumFreight = costInfo.getFreight() == null ? BigDecimal.ZERO : costInfo.getFreight();
        BigDecimal sumDeliveryFee = costInfo.getDeliveryFee() == null ? BigDecimal.ZERO : costInfo.getDeliveryFee();
        BigDecimal sumUltrafarFee = costInfo.getUltrafarFee() == null ? BigDecimal.ZERO : costInfo.getUltrafarFee();
        BigDecimal sumSuperframesFee = costInfo.getSuperframesFee() == null ? BigDecimal.ZERO : costInfo.getSuperframesFee();
        BigDecimal sumExcessFee = costInfo.getExcessFee() == null ? BigDecimal.ZERO : costInfo.getExcessFee();
        BigDecimal sumReduceFee = costInfo.getReduceFee() == null ? BigDecimal.ZERO : costInfo.getReduceFee();
        BigDecimal sumOutboundsortingFee = costInfo.getOutboundsortingFee() == null ? BigDecimal.ZERO : costInfo.getOutboundsortingFee();
        BigDecimal sumShortbargeFee = costInfo.getShortbargeFee() == null ? BigDecimal.ZERO : costInfo.getShortbargeFee();
        BigDecimal sumReturnFee = costInfo.getReturnFee() == null ? BigDecimal.ZERO : costInfo.getReturnFee();
        BigDecimal sumExceptionFee = costInfo.getExceptionFee() == null ? BigDecimal.ZERO : costInfo.getExceptionFee();
        BigDecimal sumOtherCost1 = costInfo.getOtherCost1() == null ? BigDecimal.ZERO : costInfo.getOtherCost1();
        BigDecimal sumOtherCost2 = costInfo.getOtherCost2() == null ? BigDecimal.ZERO : costInfo.getOtherCost2();
        BigDecimal sumOtherCost3 = costInfo.getOtherCost3() == null ? BigDecimal.ZERO : costInfo.getOtherCost3();
        BigDecimal sumOtherCost4 = costInfo.getOtherCost4() == null ? BigDecimal.ZERO : costInfo.getOtherCost4();
        BigDecimal sumOtherCost5 = costInfo.getOtherCost5() == null ? BigDecimal.ZERO : costInfo.getOtherCost5();
        BigDecimal sumOtherCost6 = costInfo.getOtherCost6() == null ? BigDecimal.ZERO : costInfo.getOtherCost6();
        BigDecimal sumOtherCost7 = costInfo.getOtherCost7() == null ? BigDecimal.ZERO : costInfo.getOtherCost7();
        BigDecimal sumOtherCost8 = costInfo.getOtherCost8() == null ? BigDecimal.ZERO : costInfo.getOtherCost8();
        BigDecimal sumOtherCost9 = costInfo.getOtherCost9() == null ? BigDecimal.ZERO : costInfo.getOtherCost9();
        BigDecimal sumOtherCost10 = costInfo.getOtherCost10() == null ? BigDecimal.ZERO : costInfo.getOtherCost10();
        BigDecimal sumOtherCost11 = costInfo.getOtherCost11() == null ? BigDecimal.ZERO : costInfo.getOtherCost11();
        BigDecimal sumOtherCost12 = costInfo.getOtherCost12() == null ? BigDecimal.ZERO : costInfo.getOtherCost12();


        if (!CollUtil.isEmpty(orderInfos)) {
            avgfreightAmount = sumFreight.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgDeliveryFeeAmount = sumDeliveryFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgUltrafarFeeAmount = sumUltrafarFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgSuperframesFeeAmount = sumSuperframesFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgExcessFeeAmount = sumExcessFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgReduceFeeAmount = sumReduceFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOutboundsortingFeeAmount = sumOutboundsortingFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgShortbargeFeeAmount = sumShortbargeFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            ;
            avgReturnFeeAmount = sumReturnFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgExceptionFeeAmount = sumExceptionFee.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost1Amount = sumOtherCost1.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost2Amount = sumOtherCost2.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost3Amount = sumOtherCost3.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost4Amount = sumOtherCost4.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost5Amount = sumOtherCost5.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost6Amount = sumOtherCost6.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost7Amount = sumOtherCost7.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost8Amount = sumOtherCost8.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost9Amount = sumOtherCost9.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost10Amount = sumOtherCost10.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost11Amount = sumOtherCost11.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
            avgOtherCost12Amount = sumOtherCost12.divide(BigDecimal.valueOf(orderInfos.size()), 2, RoundingMode.DOWN);
        }
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;

        for (int i = 0; i < orderInfos.size(); i++) {
            if (i == orderInfos.size() - 1) {
                idx = i;
                continue;
            }
            BmsYfjobbillinfoPO orderInfo = orderInfos.get(i);
            BmsYfexpensesMiddleSharePO shareInfo = new BmsYfexpensesMiddleSharePO();
            shareInfo.setFreight(avgfreightAmount);
            shareInfo.setDeliveryFee(avgDeliveryFeeAmount);
            shareInfo.setUltrafarFee(avgUltrafarFeeAmount);
            shareInfo.setSuperframesFee(avgSuperframesFeeAmount);
            shareInfo.setExcessFee(avgExcessFeeAmount);
            shareInfo.setReduceFee(avgReduceFeeAmount);
            shareInfo.setOutboundsortingFee(avgOutboundsortingFeeAmount);
            shareInfo.setShortbargeFee(avgShortbargeFeeAmount);
            shareInfo.setReturnFee(avgReturnFeeAmount);
            shareInfo.setExceptionFee(avgExceptionFeeAmount);
            shareInfo.setOtherCost1(avgOtherCost1Amount);
            shareInfo.setOtherCost2(avgOtherCost2Amount);
            shareInfo.setOtherCost3(avgOtherCost3Amount);
            shareInfo.setOtherCost4(avgOtherCost4Amount);
            shareInfo.setOtherCost5(avgOtherCost5Amount);
            shareInfo.setOtherCost6(avgOtherCost6Amount);
            shareInfo.setOtherCost7(avgOtherCost7Amount);
            shareInfo.setOtherCost8(avgOtherCost8Amount);
            shareInfo.setOtherCost9(avgOtherCost9Amount);
            shareInfo.setOtherCost10(avgOtherCost10Amount);
            shareInfo.setOtherCost11(avgOtherCost11Amount);
            shareInfo.setOtherCost12(avgOtherCost12Amount);
            shareInfo.setShareType(1);
            shareInfo.setSettleAmount(avgfreightAmount
                    .add(avgDeliveryFeeAmount)
                    .add(avgUltrafarFeeAmount)
                    .add(avgSuperframesFeeAmount)
                    .add(avgExcessFeeAmount)
                    .add(avgReduceFeeAmount)
                    .add(avgOutboundsortingFeeAmount)
                    .add(avgShortbargeFeeAmount)
                    .add(avgReturnFeeAmount)
                    .add(avgExceptionFeeAmount)
                    .add(avgOtherCost1Amount)
                    .add(avgOtherCost2Amount)
                    .add(avgOtherCost3Amount)
                    .add(avgOtherCost4Amount)
                    .add(avgOtherCost5Amount)
                    .add(avgOtherCost6Amount)
                    .add(avgOtherCost7Amount)
                    .add(avgOtherCost8Amount)
                    .add(avgOtherCost9Amount)
                    .add(avgOtherCost10Amount)
                    .add(avgOtherCost11Amount)
                    .add(avgOtherCost12Amount)
            );
            runningFreightAmount = runningFreightAmount.add(avgfreightAmount);
            runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(avgDeliveryFeeAmount);
            runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(avgUltrafarFeeAmount);
            runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(avgSuperframesFeeAmount);
            runningExcessFeeAmount = runningExcessFeeAmount.add(avgExcessFeeAmount);
            runningReduceFeeAmount = runningReduceFeeAmount.add(avgReduceFeeAmount);
            runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(avgOutboundsortingFeeAmount);
            runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(avgShortbargeFeeAmount);
            runningReturnFeeAmount = runningReturnFeeAmount.add(avgReturnFeeAmount);
            runningExceptionFeeAmount = runningExceptionFeeAmount.add(avgExceptionFeeAmount);
            runningOtherCost1Amount = runningOtherCost1Amount.add(avgOtherCost1Amount);
            runningOtherCost2Amount = runningOtherCost2Amount.add(avgOtherCost2Amount);
            runningOtherCost3Amount = runningOtherCost3Amount.add(avgOtherCost3Amount);
            runningOtherCost4Amount = runningOtherCost4Amount.add(avgOtherCost4Amount);
            runningOtherCost5Amount = runningOtherCost5Amount.add(avgOtherCost5Amount);
            runningOtherCost6Amount = runningOtherCost6Amount.add(avgOtherCost6Amount);
            runningOtherCost7Amount = runningOtherCost7Amount.add(avgOtherCost7Amount);
            runningOtherCost8Amount = runningOtherCost8Amount.add(avgOtherCost8Amount);
            runningOtherCost9Amount = runningOtherCost9Amount.add(avgOtherCost9Amount);
            runningOtherCost10Amount = runningOtherCost10Amount.add(avgOtherCost10Amount);
            runningOtherCost11Amount = runningOtherCost11Amount.add(avgOtherCost11Amount);
            runningOtherCost12Amount = runningOtherCost12Amount.add(avgOtherCost12Amount);
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), shareInfo);
        }
        //拿到最后一个单据信息，将计算求于数均给到最后一条
        BmsYfjobbillinfoPO lastOrderInfo = orderInfos.get(idx);
        BmsYfexpensesMiddleSharePO lastShareInfo = new BmsYfexpensesMiddleSharePO();
        lastShareInfo.setFreight(sumFreight.subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(sumDeliveryFee.subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(sumUltrafarFee.subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(sumSuperframesFee.subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(sumExcessFee.subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(sumReduceFee.subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(sumOutboundsortingFee.subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(sumShortbargeFee.subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(sumReturnFee.subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(sumExceptionFee.subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(sumOtherCost1.subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(sumOtherCost2.subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(sumOtherCost3.subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(sumOtherCost4.subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(sumOtherCost5.subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(sumOtherCost6.subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(sumOtherCost7.subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(sumOtherCost8.subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(sumOtherCost9.subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(sumOtherCost10.subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(sumOtherCost11.subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(sumOtherCost12.subtract(runningOtherCost12Amount));
        returnData.put(resultType.equals(2) ? lastOrderInfo.getRelateCode() : lastOrderInfo.getId(), lastShareInfo);
        return returnData;
    }


    /**
     * 按重量金额分摊-应付
     *
     * @param costInfo   费用信息
     * @param orderInfos 单据信息
     * @param resultType 1:按单号id，2:按单号
     * @return 按重量分摊的金额
     */
    public static Map<String, BmsYfexpensesMiddleSharePO> splitAmountByYfWeight2(BmsYfcostMainInfoPO costInfo
            , List<BmsYfjobbillinfoPO> orderInfos
            , Integer resultType) {
        Map<String, BmsYfexpensesMiddleSharePO> returnData = new LinkedHashMap<>();

        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            BmsYfjobbillinfoPO orderInfo = orderInfos.get(0);
            BmsYfexpensesMiddleSharePO shareInfo = new BmsYfexpensesMiddleSharePO();
            shareInfo.setFreight(costInfo.getFreight());
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee());
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee());
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee());
            shareInfo.setExcessFee(costInfo.getExcessFee());
            shareInfo.setReduceFee(costInfo.getReduceFee());
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee());
            shareInfo.setReturnFee(costInfo.getReturnFee());
            shareInfo.setExceptionFee(costInfo.getExceptionFee());
            shareInfo.setOtherCost1(costInfo.getOtherCost1());
            shareInfo.setOtherCost2(costInfo.getOtherCost2());
            shareInfo.setOtherCost3(costInfo.getOtherCost3());
            shareInfo.setOtherCost4(costInfo.getOtherCost4());
            shareInfo.setOtherCost5(costInfo.getOtherCost5());
            shareInfo.setOtherCost6(costInfo.getOtherCost6());
            shareInfo.setOtherCost7(costInfo.getOtherCost7());
            shareInfo.setOtherCost8(costInfo.getOtherCost8());
            shareInfo.setOtherCost9(costInfo.getOtherCost9());
            shareInfo.setOtherCost10(costInfo.getOtherCost10());
            shareInfo.setOtherCost11(costInfo.getOtherCost11());
            shareInfo.setOtherCost12(costInfo.getOtherCost12());
            shareInfo.setShareType(2);
            shareInfo.setSettleAmount(costInfo.getSettleAmount());
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), shareInfo);
            return returnData;
        }

        BigDecimal totalWeight = BigDecimal.ZERO;
        //获取总重量
        for (BmsYfjobbillinfoPO orderInfo : orderInfos) {
            totalWeight = totalWeight.add(orderInfo.getTotalWeight() != null ? orderInfo.getTotalWeight() : BigDecimal.ZERO);
        }

        //主费用信息
        BigDecimal sumFreight = costInfo.getFreight() == null ? BigDecimal.ZERO : costInfo.getFreight();
        BigDecimal sumDeliveryFee = costInfo.getDeliveryFee() == null ? BigDecimal.ZERO : costInfo.getDeliveryFee();
        BigDecimal sumUltrafarFee = costInfo.getUltrafarFee() == null ? BigDecimal.ZERO : costInfo.getUltrafarFee();
        BigDecimal sumSuperframesFee = costInfo.getSuperframesFee() == null ? BigDecimal.ZERO : costInfo.getSuperframesFee();
        BigDecimal sumExcessFee = costInfo.getExcessFee() == null ? BigDecimal.ZERO : costInfo.getExcessFee();
        BigDecimal sumReduceFee = costInfo.getReduceFee() == null ? BigDecimal.ZERO : costInfo.getReduceFee();
        BigDecimal sumOutboundsortingFee = costInfo.getOutboundsortingFee() == null ? BigDecimal.ZERO : costInfo.getOutboundsortingFee();
        BigDecimal sumShortbargeFee = costInfo.getShortbargeFee() == null ? BigDecimal.ZERO : costInfo.getShortbargeFee();
        BigDecimal sumReturnFee = costInfo.getReturnFee() == null ? BigDecimal.ZERO : costInfo.getReturnFee();
        BigDecimal sumExceptionFee = costInfo.getExceptionFee() == null ? BigDecimal.ZERO : costInfo.getExceptionFee();
        BigDecimal sumOtherCost1 = costInfo.getOtherCost1() == null ? BigDecimal.ZERO : costInfo.getOtherCost1();
        BigDecimal sumOtherCost2 = costInfo.getOtherCost2() == null ? BigDecimal.ZERO : costInfo.getOtherCost2();
        BigDecimal sumOtherCost3 = costInfo.getOtherCost3() == null ? BigDecimal.ZERO : costInfo.getOtherCost3();
        BigDecimal sumOtherCost4 = costInfo.getOtherCost4() == null ? BigDecimal.ZERO : costInfo.getOtherCost4();
        BigDecimal sumOtherCost5 = costInfo.getOtherCost5() == null ? BigDecimal.ZERO : costInfo.getOtherCost5();
        BigDecimal sumOtherCost6 = costInfo.getOtherCost6() == null ? BigDecimal.ZERO : costInfo.getOtherCost6();
        BigDecimal sumOtherCost7 = costInfo.getOtherCost7() == null ? BigDecimal.ZERO : costInfo.getOtherCost7();
        BigDecimal sumOtherCost8 = costInfo.getOtherCost8() == null ? BigDecimal.ZERO : costInfo.getOtherCost8();
        BigDecimal sumOtherCost9 = costInfo.getOtherCost9() == null ? BigDecimal.ZERO : costInfo.getOtherCost9();
        BigDecimal sumOtherCost10 = costInfo.getOtherCost10() == null ? BigDecimal.ZERO : costInfo.getOtherCost10();
        BigDecimal sumOtherCost11 = costInfo.getOtherCost11() == null ? BigDecimal.ZERO : costInfo.getOtherCost11();
        BigDecimal sumOtherCost12 = costInfo.getOtherCost12() == null ? BigDecimal.ZERO : costInfo.getOtherCost12();


        //分摊金额
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;
        for (int i = 0; i < orderInfos.size(); i++) {
            if (i == orderInfos.size() - 1) {
                idx = i;
                continue;
            }
            BmsYfjobbillinfoPO orderInfo = orderInfos.get(i);
            BigDecimal weightRadio = orderInfo.getTotalWeight().divide(totalWeight, 2, RoundingMode.DOWN);
            BmsYfexpensesMiddleSharePO shareInfo = new BmsYfexpensesMiddleSharePO();
            shareInfo.setFreight(sumFreight.multiply(weightRadio));
            shareInfo.setDeliveryFee(sumDeliveryFee.multiply(weightRadio));
            shareInfo.setUltrafarFee(sumUltrafarFee.multiply(weightRadio));
            shareInfo.setSuperframesFee(sumSuperframesFee.multiply(weightRadio));
            shareInfo.setExcessFee(sumExcessFee.multiply(weightRadio));
            shareInfo.setReduceFee(sumReduceFee.multiply(weightRadio));
            shareInfo.setOutboundsortingFee(sumOutboundsortingFee.multiply(weightRadio));
            shareInfo.setShortbargeFee(sumShortbargeFee.multiply(weightRadio));
            shareInfo.setReturnFee(sumReturnFee.multiply(weightRadio));
            shareInfo.setExceptionFee(sumExceptionFee.multiply(weightRadio));
            shareInfo.setOtherCost1(sumOtherCost1.multiply(weightRadio));
            shareInfo.setOtherCost2(sumOtherCost2.multiply(weightRadio));
            shareInfo.setOtherCost3(sumOtherCost3.multiply(weightRadio));
            shareInfo.setOtherCost4(sumOtherCost4.multiply(weightRadio));
            shareInfo.setOtherCost5(sumOtherCost5.multiply(weightRadio));
            shareInfo.setOtherCost6(sumOtherCost6.multiply(weightRadio));
            shareInfo.setOtherCost7(sumOtherCost7.multiply(weightRadio));
            shareInfo.setOtherCost8(sumOtherCost8.multiply(weightRadio));
            shareInfo.setOtherCost9(sumOtherCost9.multiply(weightRadio));
            shareInfo.setOtherCost10(sumOtherCost10.multiply(weightRadio));
            shareInfo.setOtherCost11(sumOtherCost11.multiply(weightRadio));
            shareInfo.setOtherCost12(sumOtherCost12.multiply(weightRadio));
            shareInfo.setShareType(1);
            shareInfo.setSettleAmount(shareInfo.getFreight()
                    .add(shareInfo.getDeliveryFee())
                    .add(shareInfo.getUltrafarFee())
                    .add(shareInfo.getSuperframesFee())
                    .add(shareInfo.getExcessFee())
                    .add(shareInfo.getReduceFee())
                    .add(shareInfo.getOutboundsortingFee())
                    .add(shareInfo.getShortbargeFee())
                    .add(shareInfo.getReturnFee())
                    .add(shareInfo.getExceptionFee())
                    .add(shareInfo.getOtherCost1())
                    .add(shareInfo.getOtherCost2())
                    .add(shareInfo.getOtherCost3())
                    .add(shareInfo.getOtherCost4())
                    .add(shareInfo.getOtherCost5())
                    .add(shareInfo.getOtherCost6())
                    .add(shareInfo.getOtherCost7())
                    .add(shareInfo.getOtherCost8())
                    .add(shareInfo.getOtherCost9())
                    .add(shareInfo.getOtherCost10())
                    .add(shareInfo.getOtherCost11())
                    .add(shareInfo.getOtherCost12())
            );
            runningFreightAmount = runningFreightAmount.add(shareInfo.getFreight());
            runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(shareInfo.getDeliveryFee());
            runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(shareInfo.getUltrafarFee());
            runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(shareInfo.getSuperframesFee());
            runningExcessFeeAmount = runningExcessFeeAmount.add(shareInfo.getExcessFee());
            runningReduceFeeAmount = runningReduceFeeAmount.add(shareInfo.getReduceFee());
            runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(shareInfo.getOutboundsortingFee());
            runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(shareInfo.getShortbargeFee());
            runningReturnFeeAmount = runningReturnFeeAmount.add(shareInfo.getReturnFee());
            runningExceptionFeeAmount = runningExceptionFeeAmount.add(shareInfo.getExceptionFee());
            runningOtherCost1Amount = runningOtherCost1Amount.add(shareInfo.getOtherCost1());
            runningOtherCost2Amount = runningOtherCost2Amount.add(shareInfo.getOtherCost2());
            runningOtherCost3Amount = runningOtherCost3Amount.add(shareInfo.getOtherCost3());
            runningOtherCost4Amount = runningOtherCost4Amount.add(shareInfo.getOtherCost4());
            runningOtherCost5Amount = runningOtherCost5Amount.add(shareInfo.getOtherCost5());
            runningOtherCost6Amount = runningOtherCost6Amount.add(shareInfo.getOtherCost6());
            runningOtherCost7Amount = runningOtherCost7Amount.add(shareInfo.getOtherCost7());
            runningOtherCost8Amount = runningOtherCost8Amount.add(shareInfo.getOtherCost8());
            runningOtherCost9Amount = runningOtherCost9Amount.add(shareInfo.getOtherCost9());
            runningOtherCost10Amount = runningOtherCost10Amount.add(shareInfo.getOtherCost10());
            runningOtherCost11Amount = runningOtherCost11Amount.add(shareInfo.getOtherCost11());
            runningOtherCost12Amount = runningOtherCost12Amount.add(shareInfo.getOtherCost12());
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), shareInfo);
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        BmsYfjobbillinfoPO lastOrderInfo = orderInfos.get(idx);
        BmsYfexpensesMiddleSharePO lastShareInfo = new BmsYfexpensesMiddleSharePO();
        lastShareInfo.setFreight(sumFreight.subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(sumDeliveryFee.subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(sumUltrafarFee.subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(sumSuperframesFee.subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(sumExcessFee.subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(sumReduceFee.subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(sumOutboundsortingFee.subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(sumShortbargeFee.subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(sumReturnFee.subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(sumExceptionFee.subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(sumOtherCost1.subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(sumOtherCost2.subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(sumOtherCost3.subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(sumOtherCost4.subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(sumOtherCost5.subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(sumOtherCost6.subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(sumOtherCost7.subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(sumOtherCost8.subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(sumOtherCost9.subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(sumOtherCost10.subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(sumOtherCost11.subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(sumOtherCost12.subtract(runningOtherCost12Amount));
        returnData.put(resultType.equals(2) ? lastOrderInfo.getRelateCode() : lastOrderInfo.getId(), lastShareInfo);
        return returnData;
    }


    /**
     * 按体积金额分摊
     *
     * @param costInfo   主费用明细
     * @param orderInfos 单据信息
     * @param resultType 1:key单据id 2:单据单号
     * @return 按体积分摊的金额
     */
    public static Map<String, BmsYfexpensesMiddleSharePO> splitAmountByYfVolumn2(BmsYfcostMainInfoPO costInfo
            , List<BmsYfjobbillinfoPO> orderInfos
            , Integer resultType) {
        Map<String, BmsYfexpensesMiddleSharePO> returnData = new LinkedHashMap<>();
        //单条数据无需分摊
        if (orderInfos.size() == 1) {
            BmsYfjobbillinfoPO orderInfo = orderInfos.get(0);
            BmsYfexpensesMiddleSharePO shareInfo = new BmsYfexpensesMiddleSharePO();
            shareInfo.setFreight(costInfo.getFreight());
            shareInfo.setDeliveryFee(costInfo.getDeliveryFee());
            shareInfo.setUltrafarFee(costInfo.getUltrafarFee());
            shareInfo.setSuperframesFee(costInfo.getSuperframesFee());
            shareInfo.setExcessFee(costInfo.getExcessFee());
            shareInfo.setReduceFee(costInfo.getReduceFee());
            shareInfo.setOutboundsortingFee(costInfo.getOutboundsortingFee());
            shareInfo.setShortbargeFee(costInfo.getShortbargeFee());
            shareInfo.setReturnFee(costInfo.getReturnFee());
            shareInfo.setExceptionFee(costInfo.getExceptionFee());
            shareInfo.setOtherCost1(costInfo.getOtherCost1());
            shareInfo.setOtherCost2(costInfo.getOtherCost2());
            shareInfo.setOtherCost3(costInfo.getOtherCost3());
            shareInfo.setOtherCost4(costInfo.getOtherCost4());
            shareInfo.setOtherCost5(costInfo.getOtherCost5());
            shareInfo.setOtherCost6(costInfo.getOtherCost6());
            shareInfo.setOtherCost7(costInfo.getOtherCost7());
            shareInfo.setOtherCost8(costInfo.getOtherCost8());
            shareInfo.setOtherCost9(costInfo.getOtherCost9());
            shareInfo.setOtherCost10(costInfo.getOtherCost10());
            shareInfo.setOtherCost11(costInfo.getOtherCost11());
            shareInfo.setOtherCost12(costInfo.getOtherCost12());
            shareInfo.setShareType(3);
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), shareInfo);
            return returnData;
        }


        //主费用信息
        BigDecimal sumFreight = costInfo.getFreight() == null ? BigDecimal.ZERO : costInfo.getFreight();
        BigDecimal sumDeliveryFee = costInfo.getDeliveryFee() == null ? BigDecimal.ZERO : costInfo.getDeliveryFee();
        BigDecimal sumUltrafarFee = costInfo.getUltrafarFee() == null ? BigDecimal.ZERO : costInfo.getUltrafarFee();
        BigDecimal sumSuperframesFee = costInfo.getSuperframesFee() == null ? BigDecimal.ZERO : costInfo.getSuperframesFee();
        BigDecimal sumExcessFee = costInfo.getExcessFee() == null ? BigDecimal.ZERO : costInfo.getExcessFee();
        BigDecimal sumReduceFee = costInfo.getReduceFee() == null ? BigDecimal.ZERO : costInfo.getReduceFee();
        BigDecimal sumOutboundsortingFee = costInfo.getOutboundsortingFee() == null ? BigDecimal.ZERO : costInfo.getOutboundsortingFee();
        BigDecimal sumShortbargeFee = costInfo.getShortbargeFee() == null ? BigDecimal.ZERO : costInfo.getShortbargeFee();
        BigDecimal sumReturnFee = costInfo.getReturnFee() == null ? BigDecimal.ZERO : costInfo.getReturnFee();
        BigDecimal sumExceptionFee = costInfo.getExceptionFee() == null ? BigDecimal.ZERO : costInfo.getExceptionFee();
        BigDecimal sumOtherCost1 = costInfo.getOtherCost1() == null ? BigDecimal.ZERO : costInfo.getOtherCost1();
        BigDecimal sumOtherCost2 = costInfo.getOtherCost2() == null ? BigDecimal.ZERO : costInfo.getOtherCost2();
        BigDecimal sumOtherCost3 = costInfo.getOtherCost3() == null ? BigDecimal.ZERO : costInfo.getOtherCost3();
        BigDecimal sumOtherCost4 = costInfo.getOtherCost4() == null ? BigDecimal.ZERO : costInfo.getOtherCost4();
        BigDecimal sumOtherCost5 = costInfo.getOtherCost5() == null ? BigDecimal.ZERO : costInfo.getOtherCost5();
        BigDecimal sumOtherCost6 = costInfo.getOtherCost6() == null ? BigDecimal.ZERO : costInfo.getOtherCost6();
        BigDecimal sumOtherCost7 = costInfo.getOtherCost7() == null ? BigDecimal.ZERO : costInfo.getOtherCost7();
        BigDecimal sumOtherCost8 = costInfo.getOtherCost8() == null ? BigDecimal.ZERO : costInfo.getOtherCost8();
        BigDecimal sumOtherCost9 = costInfo.getOtherCost9() == null ? BigDecimal.ZERO : costInfo.getOtherCost9();
        BigDecimal sumOtherCost10 = costInfo.getOtherCost10() == null ? BigDecimal.ZERO : costInfo.getOtherCost10();
        BigDecimal sumOtherCost11 = costInfo.getOtherCost11() == null ? BigDecimal.ZERO : costInfo.getOtherCost11();
        BigDecimal sumOtherCost12 = costInfo.getOtherCost12() == null ? BigDecimal.ZERO : costInfo.getOtherCost12();


        BigDecimal totalVolume = BigDecimal.ZERO;
        //获取总重量
        for (BmsYfjobbillinfoPO orderInfo : orderInfos) {
            totalVolume = totalVolume.add(orderInfo.getTotalVolume() != null ? orderInfo.getTotalVolume() : BigDecimal.ZERO);
        }

        //分摊金额
        int idx = 0;
        BigDecimal runningFreightAmount = BigDecimal.ZERO;
        BigDecimal runningDeliveryFeeAmount = BigDecimal.ZERO;
        BigDecimal runningUltrafarFeeAmount = BigDecimal.ZERO;
        BigDecimal runningSuperframesFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExcessFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReduceFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOutboundsortingFeeAmount = BigDecimal.ZERO;
        BigDecimal runningShortbargeFeeAmount = BigDecimal.ZERO;
        BigDecimal runningReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal runningExceptionFeeAmount = BigDecimal.ZERO;
        BigDecimal runningOtherCost1Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost2Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost3Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost4Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost5Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost6Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost7Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost8Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost9Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost10Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost11Amount = BigDecimal.ZERO;
        BigDecimal runningOtherCost12Amount = BigDecimal.ZERO;
        for (int i = 0; i < orderInfos.size(); i++) {
            if (i == orderInfos.size() - 1) {
                idx = i;
                continue;
            }
            BmsYfjobbillinfoPO orderInfo = orderInfos.get(i);
            BigDecimal volumnRadio = orderInfo.getTotalVolume().divide(totalVolume, 2, RoundingMode.DOWN);
            BmsYfexpensesMiddleSharePO shareInfo = new BmsYfexpensesMiddleSharePO();
            shareInfo.setFreight(sumFreight.multiply(volumnRadio));
            shareInfo.setDeliveryFee(sumDeliveryFee.multiply(volumnRadio));
            shareInfo.setUltrafarFee(sumUltrafarFee.multiply(volumnRadio));
            shareInfo.setSuperframesFee(sumSuperframesFee.multiply(volumnRadio));
            shareInfo.setExcessFee(sumExcessFee.multiply(volumnRadio));
            shareInfo.setReduceFee(sumReduceFee.multiply(volumnRadio));
            shareInfo.setOutboundsortingFee(sumOutboundsortingFee.multiply(volumnRadio));
            shareInfo.setShortbargeFee(sumShortbargeFee.multiply(volumnRadio));
            shareInfo.setReturnFee(sumReturnFee.multiply(volumnRadio));
            shareInfo.setExceptionFee(sumExceptionFee.multiply(volumnRadio));
            shareInfo.setOtherCost1(sumOtherCost1.multiply(volumnRadio));
            shareInfo.setOtherCost2(sumOtherCost2.multiply(volumnRadio));
            shareInfo.setOtherCost3(sumOtherCost3.multiply(volumnRadio));
            shareInfo.setOtherCost4(sumOtherCost4.multiply(volumnRadio));
            shareInfo.setOtherCost5(sumOtherCost5.multiply(volumnRadio));
            shareInfo.setOtherCost6(sumOtherCost6.multiply(volumnRadio));
            shareInfo.setOtherCost7(sumOtherCost7.multiply(volumnRadio));
            shareInfo.setOtherCost8(sumOtherCost8.multiply(volumnRadio));
            shareInfo.setOtherCost9(sumOtherCost9.multiply(volumnRadio));
            shareInfo.setOtherCost10(sumOtherCost10.multiply(volumnRadio));
            shareInfo.setOtherCost11(sumOtherCost11.multiply(volumnRadio));
            shareInfo.setOtherCost12(sumOtherCost12.multiply(volumnRadio));
            shareInfo.setShareType(3);

            runningFreightAmount = runningFreightAmount.add(shareInfo.getFreight());
            runningDeliveryFeeAmount = runningDeliveryFeeAmount.add(shareInfo.getDeliveryFee());
            runningUltrafarFeeAmount = runningUltrafarFeeAmount.add(shareInfo.getUltrafarFee());
            runningSuperframesFeeAmount = runningSuperframesFeeAmount.add(shareInfo.getSuperframesFee());
            runningExcessFeeAmount = runningExcessFeeAmount.add(shareInfo.getExcessFee());
            runningReduceFeeAmount = runningReduceFeeAmount.add(shareInfo.getReduceFee());
            runningOutboundsortingFeeAmount = runningOutboundsortingFeeAmount.add(shareInfo.getOutboundsortingFee());
            runningShortbargeFeeAmount = runningShortbargeFeeAmount.add(shareInfo.getShortbargeFee());
            runningReturnFeeAmount = runningReturnFeeAmount.add(shareInfo.getReturnFee());
            runningExceptionFeeAmount = runningExceptionFeeAmount.add(shareInfo.getExceptionFee());
            runningOtherCost1Amount = runningOtherCost1Amount.add(shareInfo.getOtherCost1());
            runningOtherCost2Amount = runningOtherCost2Amount.add(shareInfo.getOtherCost2());
            runningOtherCost3Amount = runningOtherCost3Amount.add(shareInfo.getOtherCost3());
            runningOtherCost4Amount = runningOtherCost4Amount.add(shareInfo.getOtherCost4());
            runningOtherCost5Amount = runningOtherCost5Amount.add(shareInfo.getOtherCost5());
            runningOtherCost6Amount = runningOtherCost6Amount.add(shareInfo.getOtherCost6());
            runningOtherCost7Amount = runningOtherCost7Amount.add(shareInfo.getOtherCost7());
            runningOtherCost8Amount = runningOtherCost8Amount.add(shareInfo.getOtherCost8());
            runningOtherCost9Amount = runningOtherCost9Amount.add(shareInfo.getOtherCost9());
            runningOtherCost10Amount = runningOtherCost10Amount.add(shareInfo.getOtherCost10());
            runningOtherCost11Amount = runningOtherCost11Amount.add(shareInfo.getOtherCost11());
            runningOtherCost12Amount = runningOtherCost12Amount.add(shareInfo.getOtherCost12());
            returnData.put(resultType.equals(2) ? orderInfo.getRelateCode() : orderInfo.getId(), shareInfo);
        }

        //拿到最后一个单据信息，将计算求于数均给到最后一条
        BmsYfjobbillinfoPO lastOrderInfo = orderInfos.get(idx);
        BmsYfexpensesMiddleSharePO lastShareInfo = new BmsYfexpensesMiddleSharePO();
        lastShareInfo.setFreight(sumFreight.subtract(runningFreightAmount));
        lastShareInfo.setDeliveryFee(sumDeliveryFee.subtract(runningDeliveryFeeAmount));
        lastShareInfo.setUltrafarFee(sumUltrafarFee.subtract(runningUltrafarFeeAmount));
        lastShareInfo.setSuperframesFee(sumSuperframesFee.subtract(runningSuperframesFeeAmount));
        lastShareInfo.setExcessFee(sumExcessFee.subtract(runningExcessFeeAmount));
        lastShareInfo.setReduceFee(sumReduceFee.subtract(runningReduceFeeAmount));
        lastShareInfo.setOutboundsortingFee(sumOutboundsortingFee.subtract(runningOutboundsortingFeeAmount));
        lastShareInfo.setShortbargeFee(sumShortbargeFee.subtract(runningShortbargeFeeAmount));
        lastShareInfo.setReturnFee(sumReturnFee.subtract(runningReturnFeeAmount));
        lastShareInfo.setExceptionFee(sumExceptionFee.subtract(runningExceptionFeeAmount));
        lastShareInfo.setOtherCost1(sumOtherCost1.subtract(runningOtherCost1Amount));
        lastShareInfo.setOtherCost2(sumOtherCost2.subtract(runningOtherCost2Amount));
        lastShareInfo.setOtherCost3(sumOtherCost3.subtract(runningOtherCost3Amount));
        lastShareInfo.setOtherCost4(sumOtherCost4.subtract(runningOtherCost4Amount));
        lastShareInfo.setOtherCost5(sumOtherCost5.subtract(runningOtherCost5Amount));
        lastShareInfo.setOtherCost6(sumOtherCost6.subtract(runningOtherCost6Amount));
        lastShareInfo.setOtherCost7(sumOtherCost7.subtract(runningOtherCost7Amount));
        lastShareInfo.setOtherCost8(sumOtherCost8.subtract(runningOtherCost8Amount));
        lastShareInfo.setOtherCost9(sumOtherCost9.subtract(runningOtherCost9Amount));
        lastShareInfo.setOtherCost10(sumOtherCost10.subtract(runningOtherCost10Amount));
        lastShareInfo.setOtherCost11(sumOtherCost11.subtract(runningOtherCost11Amount));
        lastShareInfo.setOtherCost12(sumOtherCost12.subtract(runningOtherCost12Amount));
        returnData.put(resultType.equals(2) ? lastOrderInfo.getRelateCode() : lastOrderInfo.getId(), lastShareInfo);
        return returnData;
    }


    /**
     * 计算应付费用总和
     * 汇总对象中所有应付费用字段的金额
     * 注意:如果新增费用字段，映射值字段一定要加 YF_COST_FIELDS
     *
     * @param obj 包含费用字段的对象
     * @param <T> 对象类型
     * @return 费用总和
     */
    public static <T> BigDecimal yfSumCost(T obj) {
        if (obj == null) {
            return BigDecimal.ZERO;
        }

        Map<String, Object> objMap = BeanUtil.beanToMap(obj);
        BigDecimal totalCost = BigDecimal.ZERO;

        for (String fieldName : YF_COST_FIELDS) {
            Object value = objMap.get(fieldName);
            if (value != null) {
                if (value instanceof BigDecimal) {
                    totalCost = totalCost.add((BigDecimal) value);
                } else if (value instanceof Number) {
                    totalCost = totalCost.add(BigDecimal.valueOf(((Number) value).doubleValue()));
                }
            }
        }
        return totalCost;
    }


    /**
     * 初始化费用字段值
     * 将指定字段数组中的费用字段初始化为指定值
     *
     * @param obj       需要初始化的对象
     * @param fields    需要初始化的字段数组
     * @param initValue 初始化的值，默认为0
     * @param <T>       对象类型
     */
    public static <T> void initCostFields(T obj, String[] fields, BigDecimal initValue) {
        if (obj == null) {
            return;
        }

        if (initValue == null) {
            initValue = BigDecimal.ZERO;
        }

        Map<String, Object> objMap = BeanUtil.beanToMap(obj);
        for (String fieldName : fields) {
            objMap.put(fieldName, initValue);
        }
        BeanUtil.copyProperties(BeanUtil.toBean(objMap, obj.getClass()), obj);
    }

    /**
     * 初始化费用字段值为0
     * 将指定字段数组中的费用字段初始化为0
     *
     * @param obj    需要初始化的对象
     * @param fields 需要初始化的字段数组
     * @param <T>    对象类型
     */
    public static <T> void initCostFields(T obj, String[] fields) {
        initCostFields(obj, fields, BigDecimal.ZERO);
    }


    /**
     * 不同对象类型的费用汇总计算
     * 将明细费用列表中的各项费用汇总到总费用对象中对应的字段
     *
     * @param summary 费用汇总对象
     * @param details 费用明细列表
     * @param clazz   返回对象类型
     * @param fields  需要汇总的字段数组
     * @param <T>     汇总对象类型
     * @param <R>     明细对象类型
     * @return 汇总后的新对象
     */
    public static <T, R> T aggregateCost(T summary, List<R> details, Class<T> clazz, String[] fields) {
        if (summary == null) {
            try {
                summary = clazz.newInstance();
            } catch (Exception e) {
                throw new RuntimeException("Failed to create instance", e);
            }
        }

        if (details == null || details.isEmpty()) {
            return summary;
        }

        Map<String, Object> summaryMap = BeanUtil.beanToMap(summary);
        Map<String, BigDecimal> sumMap = new HashMap<>(fields.length);

        // 初始化汇总Map
        for (String field : fields) {
            sumMap.put(field, BigDecimal.ZERO);
        }

        // 一次循环处理所有字段
        for (R detail : details) {
            Map<String, Object> detailMap = BeanUtil.beanToMap(detail);

            for (String fieldName : fields) {
                Object value = detailMap.get(fieldName);
                if (value != null) {
                    BigDecimal currentSum = sumMap.get(fieldName);
                    if (value instanceof BigDecimal) {
                        currentSum = currentSum.add((BigDecimal) value);
                    } else if (value instanceof Number) {
                        currentSum = currentSum.add(BigDecimal.valueOf(((Number) value).doubleValue()));
                    }
                    sumMap.put(fieldName, currentSum);
                }
            }
        }

        // 将汇总结果放入summaryMap
        summaryMap.putAll(sumMap);

        return BeanUtil.toBean(summaryMap, clazz);
    }


    /**
     * 复制费用字段值
     * 将源对象的费用字段值复制到目标对象对应的字段中
     *
     * @param source 源对象
     * @param target 目标对象
     * @param fields 需要复制的字段数组
     * @param <S>    源对象类型
     * @param <T>    目标对象类型
     */
    public static <S, T> void copyCostFields(S source, T target, String[] fields) {
        if (source == null || target == null) {
            return;
        }

        Map<String, Object> sourceMap = BeanUtil.beanToMap(source);
        Map<String, Object> targetMap = BeanUtil.beanToMap(target);

        for (String fieldName : fields) {
            Object value = sourceMap.get(fieldName);
            if (value != null) {
                targetMap.put(fieldName, value);
            }
        }

        BeanUtil.copyProperties(BeanUtil.toBean(targetMap, target.getClass()), target);
    }


}
