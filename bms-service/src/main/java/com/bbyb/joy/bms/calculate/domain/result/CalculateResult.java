package com.bbyb.joy.bms.calculate.domain.result;

import lombok.*;

import java.io.Serializable;

/**
 * 计费结果
 * 计费维度+计费字段+
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CalculateResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 单据相关结果
     */
    private CalculateCodeResult codeResult;

    /**
     * 计费相关结果
     */
    private CalculateCostResult costResult;



}
