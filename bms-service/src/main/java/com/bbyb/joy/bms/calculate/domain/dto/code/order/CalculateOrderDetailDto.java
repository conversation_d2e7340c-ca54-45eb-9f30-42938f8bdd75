package com.bbyb.joy.bms.calculate.domain.dto.code.order;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单维度详情实体
 * 该类的是单据进入计费的单据详情信息
 * 该类整合了BMS所有业务单据类型的通用字段
 * 后端拓展单据业务如果有新的单据业务属性计费需要就需要在该类进行拓展补充
 * ## 字段含义补充:
 * 1、(重要)所有业务属性字段不再使用type的code,全部要转译为对应的label,便于维护报价
 * 2、(重要)那基于1带来额外的补充,需要补充对应的业务属性转译的字典的维护源头(要确保一定准确)
 * 3、(重要)日期业务字段全部转移为字符串,groovy运行引用就不再需要处理时间
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateOrderDetailDto implements Serializable {

    public static final long serialVersionUID = 1L;

    public String id;                     // id(uuid)
    public Integer pkId;                  // 自增键

    // 关联信息
    public String mainCodeId;             // 单据主表id(uuid)
    public Integer mainPkId;              // 单据主表id(自增id)
    private Integer schedulingPkId;        // 调度单pkId

    // 商品信息
    public String skuCode;                // SKU编码(商品编码)
    public String skuName;                // SKU名称(商品名称)
    public String temperatureType;        // 温区类型编码
    //    public Integer goodType;              // 好品坏品类型（1好品 2坏品）
    public String goodTypeName;
    public String lpnCode;                // 托盘编码

    // 数量信息
    public BigDecimal totalBoxes;         // 总箱数
    public BigDecimal totalOddBoxes;      // 总箱数(零头总箱数)
    public BigDecimal totalNumber;        // 总件数
    public BigDecimal totalWeight;        // 总重量(kg)
    public BigDecimal totalVolume;        // 总体积(m³)
    public BigDecimal totalAmount;        // 总金额(元)
    public BigDecimal totalCargoValue;    // 总货值

    // 规格信息
    public String boxRule;                // 箱规

    // 日期信息
    public String productDate;            // 生产日期(yyyy-MM-dd HH:mm:ss)
    public String expireDate;             // 过期日期(yyyy-MM-dd HH:mm:ss)
    public String inboundDate;            // 入库日期(yyyy-MM-dd HH:mm:ss)

    // 其他信息
    public String batch;                  // 批次
    public String remark;                 // 备注

    // 基础字段
    public Integer optMonth;              // 月分区
    public Integer optDay;                // 日分区

}
