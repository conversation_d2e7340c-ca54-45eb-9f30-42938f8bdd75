package com.bbyb.joy.bms.calculate.domain.dto.code.month;

import com.bbyb.joy.bms.calculate.domain.dto.code.batch.CalculateBatchDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 月维度实体
 * 该类的是月维度进入计费的单据信息
 * 该类整合了BMS所有业务单据类型的月维度通用字段
 * 后端拓展单据业务如果有新的单据业务属性计费需要就需要在该类进行拓展补充
 * ## 字段含义补充:
 * 1、(重要)所有业务属性字段不再使用type的code,全部要转译为对应的label,便于维护报价
 * 2、(重要)那基于1带来额外的补充,需要补充对应的业务属性转译的字典的维护源头(要确保一定准确)
 * 3、(重要)日期业务字段全部转移为字符串,groovy运行引用就不再需要处理时间
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateMonthDto extends CalculateBatchDto implements Serializable {

    private static final long serialVersionUID = 1L;


    // 时间信息
    public String businessBeginMonth;     // 订单日期||配载日期(yyyy-MM)
    public String businessEndMonth;         // 签收日期||完成日期(yyyy-MM)

    /*
     *********************以下是明细维度的********************************
     */
    /**
     * 明细1-品温区维度汇总
     * 涉及温区维度的件重体信息以及品数汇总
     * key:温区(CW,LD,LC),value:该维度汇总信息
     */
    public Map<String, CalculateMonthDetailDto> temperatureDimensionDetailMap = new HashMap<>();
    /**
     * 明细1-品维度汇总
     * 根据sku维度的件重体信息以及品数汇总
     * key:skuCode,value:该维度汇总信息
     */
    public Map<String,CalculateMonthDetailDto> skuDimensionDetailMap = new HashMap<>();
    /*
     *********************以下是明细2维度的********************************
     */
    /**
     * 明细2-门店维度汇总
     * 根据维度2的件重体信息汇总
     * key:storeCode,value:该维度汇总信息
     */
    public Map<String, CalculateMonthDetail2Dto> storeDimensionDetailMap = new HashMap<>();
    /**
     * 明细2-客户维度汇总
     * 根据维度2的件重体信息汇总
     * key:clientId,value:该维度汇总信息
     */
    public Map<String, CalculateMonthDetail2Dto> clientDimensionDetailMap = new HashMap<>();


}
