package com.bbyb.joy.bms.scheduled.bill.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.bbyb.bms.model.po.PubAutomaticLogImplementPO;
import com.bbyb.joy.bms.calculate.domain.param.CalculateDebuggerParam;
import com.bbyb.joy.bms.calculate.service.CalculateService;
import com.bbyb.joy.bms.code.domain.param.BmsDispatchCodeInfoParam;
import com.bbyb.joy.bms.code.domain.param.BmsTransCodeInfoParam;
import com.bbyb.joy.bms.code.domain.param.BmsYfStorageCodeInfoParam;
import com.bbyb.joy.bms.code.domain.param.BmsYsStorageCodeInfoParam;
import com.bbyb.joy.bms.domain.enums.AutoCalculateModuleEnum;
import com.bbyb.joy.bms.scheduled.bill.service.CalculateScheduleService;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import com.bbyb.joy.bms.support.exception.BusinessException;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.ValidationUtil;
import com.bbyb.joy.bms.support.utils.thread.ThreadPool2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class CalculateScheduleServiceImpl  implements CalculateScheduleService {


    @Resource
    CalculateService calculateService;
    @Resource
    private ThreadPool2 threadPool2;
    @Resource
    private TextConversionUtil textConversionUtil;


    @Override
    public void ysAutoCalculate() {

        log.info("BMS-应收自动计费定时任务开始:{}", DateUtil.now());

        Map<String, Integer> companyMap = textConversionUtil.companyNameMap();
        List<Integer> companyIds = companyMap.values().stream().distinct().toList();
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        String currentTime = now.format(formatter);
        // 获取往前2个月的时间
        LocalDateTime twoMonthsAgo = now.minusMonths(2);
        String twoMonthsAgoTime = twoMonthsAgo.format(formatter);

        BmsTransCodeInfoParam transParam = new BmsTransCodeInfoParam();
        transParam.setCompanyIds(companyIds);
        transParam.setHandlerType(1);
        transParam.setDateType(2);
        transParam.setStartDate(twoMonthsAgoTime);
        transParam.setEndDate(currentTime);

        tranCalculate(transParam);

        BmsYsStorageCodeInfoParam storageCodeInfoParam = new BmsYsStorageCodeInfoParam();
        storageCodeInfoParam.setCompanyIds(companyIds);
        storageCodeInfoParam.setHandlerType(21);
        storageCodeInfoParam.setDateType(2);
        storageCodeInfoParam.setStartDate(twoMonthsAgoTime);
        storageCodeInfoParam.setEndDate(currentTime);
        storageCodeInfoParam.setCostMode(BmsConstants.COST_MODE_01);
        ysStorageCalculate(storageCodeInfoParam);

    }






    public void tranCalculate(BmsTransCodeInfoParam param){
        if(calculateService.checkLock(AutoCalculateModuleEnum.TRANS)){
            throw new BusinessException("上次服务未执行完成,请等待！");
        }

        Thread thread = new Thread(()->{

            PubAutomaticLogImplementPO automaticTask = calculateService.lock(AutoCalculateModuleEnum.TRANS);

            CompletableFuture<Void> future = CompletableFuture.completedFuture(null);
            future = future.thenCompose(v -> {
                BmsTransCodeInfoParam param2 = BeanUtil.copyProperties(param, BmsTransCodeInfoParam.class);
                return CompletableFuture.runAsync(() -> {
                    calculateService.transCalculate(param2, CalculateDebuggerParam.builder().build());
                }, threadPool2.getExecutor());
            });

            // 等待所有任务按顺序完成(不然写费用表会出现幻读，导致费用表写错)
            try {
                future.get(); // 阻塞等待完成
            } catch (Exception e) {
                log.error("串行计费任务执行异常", e);
                throw new BusinessException("计费任务执行失败: " + e.getCause().getMessage());
            }finally {
                calculateService.unlock(automaticTask);
            }
        });
        thread.start();
    }

    public void ysStorageCalculate(BmsYsStorageCodeInfoParam param){
        if(calculateService.checkLock(AutoCalculateModuleEnum.STORAGE_YS)){
            throw new BusinessException("上次服务未执行完成,请等待！");
        }

        Thread thread2 = new Thread(()->{

            param.setCostMode(BmsConstants.COST_MODE_01);
            String checkMsg = ValidationUtil.validate(param);
            if(StrUtil.isNotEmpty(checkMsg)){
                throw new BusinessException(checkMsg);
            }

            PubAutomaticLogImplementPO automaticTask = calculateService.lock(AutoCalculateModuleEnum.STORAGE_YS);

            List<Integer> codeTypes = CollUtil.isEmpty(param.getCodeTypes()) ? new ArrayList<>(BmsConstants.CODE_TYPE_STORAGE_SETS):param.getCodeTypes();
            CompletableFuture<Void> future = CompletableFuture.completedFuture(null);

            // 串行执行，每个任务等待前一个完成
            if(codeTypes.contains(BmsConstants.CODE_TYPE_2)){
                future = future.thenCompose(v -> {
                    BmsYsStorageCodeInfoParam param2 = BeanUtil.copyProperties(param, BmsYsStorageCodeInfoParam.class);
                    param2.setCodeTypes(List.of(BmsConstants.CODE_TYPE_2));
                    return CompletableFuture.runAsync(() -> {
                        calculateService.ysStorageCalculate(param2, CalculateDebuggerParam.builder().build());
                    }, threadPool2.getExecutor());
                });
            }
            if(codeTypes.contains(BmsConstants.CODE_TYPE_3)){
                future = future.thenCompose(v -> {
                    BmsYsStorageCodeInfoParam param3 = BeanUtil.copyProperties(param, BmsYsStorageCodeInfoParam.class);
                    param3.setCodeTypes(List.of(BmsConstants.CODE_TYPE_3));
                    return CompletableFuture.runAsync(() -> {
                        calculateService.ysStorageCalculate(param3, CalculateDebuggerParam.builder().build());
                    }, threadPool2.getExecutor());
                });
            }
            if(codeTypes.contains(BmsConstants.CODE_TYPE_4)){
                future = future.thenCompose(v -> {
                    BmsYsStorageCodeInfoParam param4 = BeanUtil.copyProperties(param, BmsYsStorageCodeInfoParam.class);
                    param4.setCodeTypes(List.of(BmsConstants.CODE_TYPE_4));
                    return CompletableFuture.runAsync(() -> {
                        calculateService.ysStorageCalculate(param4, CalculateDebuggerParam.builder().build());
                    }, threadPool2.getExecutor());
                });
            }

            // 等待所有任务按顺序完成(不然写费用表会出现幻读，导致费用表写错)
            try {
                future.handle((result, throwable) -> {
                    if (throwable != null) {
                        log.error("串行计费任务执行异常", throwable);
                    } else {
                        log.info("计费任务执行成功");
                    }
                    return null; // 返回 null，表示无论成功失败都继续
                }).get();
            } catch (Exception e) {
                log.error("串行计费任务执行异常", e);
                throw new BusinessException("计费任务执行失败: " + e.getCause().getMessage());
            }finally {
                calculateService.unlock(automaticTask);
            }
        });

        thread2.start();
    }



    @Override
    public void yfAutoCalculate() {

        log.info("BMS-应付自动计费定时任务开始:{}", DateUtil.now());

        Map<String, Integer> companyMap = textConversionUtil.companyNameMap();
        List<Integer> companyIds = companyMap.values().stream().distinct().toList();
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        String currentTime = now.format(formatter);
        // 获取往前2个月的时间
        LocalDateTime twoMonthsAgo = now.minusMonths(2);
        String twoMonthsAgoTime = twoMonthsAgo.format(formatter);

        BmsDispatchCodeInfoParam dispatchParam = new BmsDispatchCodeInfoParam();
        dispatchParam.setHandlerType(3);
        dispatchParam.setCompanyIds(companyIds);
        dispatchParam.setDateType(2);
        dispatchParam.setStartDate(twoMonthsAgoTime);
        dispatchParam.setEndDate(currentTime);

        dispatchCalculate(dispatchParam);


        BmsYfStorageCodeInfoParam storageCodeInfoParam = new BmsYfStorageCodeInfoParam();
        storageCodeInfoParam.setHandlerType(22);
        storageCodeInfoParam.setCompanyIds(companyIds);
        storageCodeInfoParam.setDateType(2);
        storageCodeInfoParam.setStartDate(twoMonthsAgoTime);
        storageCodeInfoParam.setEndDate(currentTime);
        storageCodeInfoParam.setCostMode(BmsConstants.COST_MODE_02);
        yfStorageCalculate(storageCodeInfoParam);
    }



    public void dispatchCalculate(BmsDispatchCodeInfoParam param){
        if(calculateService.checkLock(AutoCalculateModuleEnum.DISPATCH_YF)){
            throw new BusinessException("上次服务未执行完成,请等待！");
        }

        Thread thread = new Thread(()->{

            PubAutomaticLogImplementPO automaticTask = calculateService.lock(AutoCalculateModuleEnum.DISPATCH_YF);

            CompletableFuture<Void> future = CompletableFuture.completedFuture(null);

            future = future.thenCompose(v -> {
                BmsDispatchCodeInfoParam param2 = BeanUtil.copyProperties(param, BmsDispatchCodeInfoParam.class);
                return CompletableFuture.runAsync(() -> {
                    calculateService.dispatchCalculate(param, CalculateDebuggerParam.builder().build());
                }, threadPool2.getExecutor());
            });

            // 等待所有任务按顺序完成(不然写费用表会出现幻读，导致费用表写错)
            try {
                future.get(); // 阻塞等待完成
            } catch (Exception e) {
                log.error("串行计费任务执行异常", e);
                throw new BusinessException("计费任务执行失败: " + e.getCause().getMessage());
            }finally {
                calculateService.unlock(automaticTask);
            }
        });

        thread.start();
    }


    public void yfStorageCalculate(BmsYfStorageCodeInfoParam param){

        if(calculateService.checkLock(AutoCalculateModuleEnum.STORAGE_YF)){
            throw new BusinessException("上次服务未执行完成,请等待！");
        }

        Thread thread2 = new Thread(()->{

            PubAutomaticLogImplementPO automaticTask = calculateService.lock(AutoCalculateModuleEnum.STORAGE_YF);

            param.setCostMode(BmsConstants.COST_MODE_02);
            String checkMsg = ValidationUtil.validate(param);
            if(StrUtil.isNotEmpty(checkMsg)){
                throw new BusinessException(checkMsg);
            }
            List<Integer> codeTypes = CollUtil.isEmpty(param.getCodeTypes()) ? new ArrayList<>(BmsConstants.CODE_TYPE_STORAGE_SETS):param.getCodeTypes();

            CompletableFuture<Void> future = CompletableFuture.completedFuture(null);

            // 串行执行，每个任务等待前一个完成
            if(codeTypes.contains(BmsConstants.CODE_TYPE_2)){
                future = future.thenCompose(v -> {
                    BmsYfStorageCodeInfoParam param2 = BeanUtil.toBean(param, BmsYfStorageCodeInfoParam.class);
                    param2.setCodeTypes(List.of(BmsConstants.CODE_TYPE_2));
                    return CompletableFuture.runAsync(() -> {
                        calculateService.yfStorageCalculate(param2, CalculateDebuggerParam.builder().build());
                    }, threadPool2.getExecutor());
                });
            }
            if(codeTypes.contains(BmsConstants.CODE_TYPE_3)){
                future = future.thenCompose(v -> {
                    BmsYfStorageCodeInfoParam param3 = BeanUtil.toBean(param, BmsYfStorageCodeInfoParam.class);
                    param3.setCodeTypes(List.of(BmsConstants.CODE_TYPE_3));
                    return CompletableFuture.runAsync(() -> {
                        calculateService.yfStorageCalculate(param3, CalculateDebuggerParam.builder().build());
                    }, threadPool2.getExecutor());
                });
            }
            if(codeTypes.contains(BmsConstants.CODE_TYPE_4)){
                future = future.thenCompose(v -> {
                    BmsYfStorageCodeInfoParam param4 = BeanUtil.copyProperties(param, BmsYfStorageCodeInfoParam.class);
                    param4.setCodeTypes(List.of(BmsConstants.CODE_TYPE_4));
                    return CompletableFuture.runAsync(() -> {
                        calculateService.yfStorageCalculate(param4, CalculateDebuggerParam.builder().build());
                    }, threadPool2.getExecutor());
                });
            }
            // 等待所有任务按顺序完成(不然写费用表会出现幻读，导致费用表写错)
            try {
                future.handle((result, throwable) -> {
                    if (throwable != null) {
                        log.error("串行计费任务执行异常", throwable);
                    } else {
                        log.info("计费任务执行成功");
                    }
                    return null; // 返回 null，表示无论成功失败都继续
                }).get();
            } catch (Exception e) {
                log.error("串行计费任务执行异常", e);
                throw new BusinessException("计费任务执行失败: " + e.getCause().getMessage());
            }finally {
                calculateService.unlock(automaticTask);
            }
        });

        thread2.start();

    }

}
