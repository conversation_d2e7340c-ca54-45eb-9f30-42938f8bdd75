package com.bbyb.joy.bms.calculate.component;

import com.bbyb.joy.bms.calculate.convert.OrderExpandBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 业务单据转换为计费单据服务
 *
 */
@Service
public class OrderConvertComponent {

    /**
     * #@Resource
     * private OrderConvertService orderConvertService;
     * public void processDispatchOrders() {
     *     // 获取调度单据数据
     *     List<DispatchSyncCodeInfo> codes = getDispatchCodes();
     *     List<BmsJobCodeDetailInfo> details = getJobDetails();
     *     List<BmsJobCodeInfo> details2 = getJobCodes();
     *     // 使用构建器转换单据
     *     List<CalculateOrderExpandDto> expandDtos = orderConvertService.builder()
     *         .initCodes(codes, DispatchSyncCodeInfo.class)
     *         .initDetail(details, BmsJobCodeDetailInfo.class)
     *         .initDetail2(details2, BmsJobCodeInfo.class)
     *         .build();
     *     // 处理转换后的单据
     *     processCalculateOrders(expandDtos);
     * }
     */

    @Resource
    private OrderExpandBuilder orderExpandBuilder;

    /**
     * 创建构建器
     * @return 构建器实例
     */
    public OrderExpandBuilder.Builder builder() {
        return orderExpandBuilder.builder();
    }

}
