//package com.bbyb.joy.bms.support.configuration.config;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.task.TaskDecorator;
//import org.springframework.scheduling.annotation.AsyncConfigurer;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.web.context.request.RequestAttributes;
//import org.springframework.web.context.request.RequestContextHolder;
//
//import java.util.concurrent.ThreadPoolExecutor;
//
/// **
// * 线程池配置
// **/
//@Configuration
//public class ThreadPoolConfig implements AsyncConfigurer {
//
//    // 核心线程池大小
//    private int corePoolSize = 50;
//
//    // 最大可创建的线程数
//    private int maxPoolSize = 200;
//
//    // 队列最大长度
//    private int queueCapacity = 1000;
//
//    // 线程池维护线程所允许的空闲时间
//    private int keepAliveSeconds = 300;
//
//    @Bean("asyncTaskExecutor")
//    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        executor.setMaxPoolSize(maxPoolSize);
//        executor.setCorePoolSize(corePoolSize);
//        executor.setQueueCapacity(queueCapacity);
//        executor.setKeepAliveSeconds(keepAliveSeconds);
//        executor.setTaskDecorator(new ThreadTaskDecorator());
//        // 线程池对拒绝任务(无线程可用)的处理策略
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        return executor;
//    }
//
//    class ThreadTaskDecorator implements TaskDecorator {
//
//        @Override
//        public Runnable decorate(Runnable runnable) {
//            try {
//                RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
//                return () -> {
//                    try {
//                        RequestContextHolder.setRequestAttributes(attributes);
//                        runnable.run();
//                    } finally {
//                        RequestContextHolder.resetRequestAttributes();
//                    }
//                };
//            } catch (IllegalStateException e) {
//                return runnable;
//            }
//        }
//
//
//    }
//
//
//}