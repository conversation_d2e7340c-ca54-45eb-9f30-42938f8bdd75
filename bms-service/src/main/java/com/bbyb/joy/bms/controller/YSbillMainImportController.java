package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.biz.BmsYsbillmainBiz;
import com.bbyb.joy.bms.biz.MdmClientinfoBiz;
import com.bbyb.joy.bms.domain.dto.MdmClientinfo;
import com.bbyb.joy.bms.domain.dto.SysDept;
import com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain;
import com.bbyb.joy.bms.domain.dto.export.BmsYsImportDto;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.domain.enums.FileTypeEnum;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.utils.HttpClient;
import com.bbyb.joy.bms.support.utils.StringUtils;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.getCode.CodeCategory;
import com.bbyb.joy.bms.support.utils.getCode.CodeUtils;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.enums.PubNumEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "应收账单导入")
@RestController
@RequestMapping("/system/ysbillImport")
public class YSbillMainImportController {
    @Resource
    MdmClientinfoBiz mdmClientinfoMapper;
    @Resource
    BmsYsbillmainBiz bmsYsbillmainMapper;
    @Resource
    TextConversionUtil textConversionUtil;
    @Resource
    CodeUtils codeUtils;

    /**
     * 应收账单导入解析入口
     *
     * @param fileList 文件集合
     * @param fileType 导入文件类型 1运输 2仓储 3仓配
     * @return 返回
     */
    @ApiOperation(value = "账单导入", response = MultipartFile.class)
    @Log(title = "账单导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importBill/{fileType}")
    public ResponseResult<List<BmsYsbillmain>> importOrder(HttpServletRequest request, @RequestBody(required = false) List<MultipartFile> fileList, @PathVariable("fileType") Integer fileType) {
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        BmsYsImportDto bmsYsImportDto = new BmsYsImportDto();
        bmsYsImportDto.setFileType(fileType);
        bmsYsImportDto.setFileList(fileList);
        List<BmsYsbillmain> bmsYsbillmainList = new ArrayList<>();

        /*
         * 1。解析文件
         * 2。校验文件信息
         * 3。获取账单部分信息
         */
        List<String> errList = importOrderPublic(loginUserInfo.getTenantid().toString(), bmsYsImportDto, bmsYsbillmainList);
        // 如果有错误信息，就直接返回
        String result = CollUtil.isNotEmpty(errList) ? StringUtils.join(errList, ",") : "";
        if (StrUtil.isEmpty(result)) {
            // 账单的默认值
            setBillInfo(loginUserInfo, bmsYsbillmainList, bmsYsImportDto.getFileType());
            // 新增账单
            //bmsYsbillmainMapper.insertBmsYsbillmainList(bmsYsbillmainList);
            // 获取机构名称
            Map<String, SysDept> companyMap = textConversionUtil.getCompanyMap("1", bmsYsbillmainList.stream().filter(e -> e.getCompanyId() != null).map(e -> e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
            bmsYsbillmainList.forEach(e -> {
                if (e.getCompanyId() != null && companyMap.containsKey(e.getCompanyId().toString())) {
                    e.setCompanyName(companyMap.get(e.getCompanyId().toString()).getDeptName());
                }
            });
            return new ResponseResult<>(bmsYsbillmainList);
        } else {
            return new ResponseResult<>(40005, result);
        }
    }

    /**
     * 应收账单新增
     *
     * @param bmsYsbillmainList 账单集合
     * @return 返回
     */
    @ApiOperation(value = "应收账单新增", response = MultipartFile.class)
    //@PreAuthorize("@ss.hasPermi('system:yfbillcodeinfo:importOrder')")
    @Log(title = "应收账单新增", businessType = BusinessType.IMPORT)
    @PostMapping("/importBill/insertYSBill")
    public ResponseResult<String> importOrder(HttpServletRequest request, @RequestBody List<BmsYsbillmain> bmsYsbillmainList) {
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        // 校验参数
        String errMsg = checkBillInsertParam(loginUserInfo, bmsYsbillmainList);
        if (StrUtil.isEmpty(errMsg)) {
            // 新增账单
            bmsYsbillmainMapper.insertBmsYsbillmainList(loginUserInfo.getTenantid().toString(), bmsYsbillmainList);
            return new ResponseResult<>("操作成功");
        }
        return new ResponseResult<>(40005, errMsg);
    }

    /**
     * 导入文件解析
     *
     * @param bmsYsImportDto    入参
     * @param bmsYsbillmainList 要新增的应收账单信息（赋一些必填值）
     * @return 错误信息
     */
    public List<String> importOrderPublic(String tenantid, BmsYsImportDto bmsYsImportDto, List<BmsYsbillmain> bmsYsbillmainList) {
        List<String> errList = new ArrayList<>();
        if (CollUtil.isEmpty(bmsYsImportDto.getFileList())) {
            errList.add("导入文件不可为空！");
            return errList;
        }
        if (bmsYsImportDto.getFileType() == null) {
            errList.add("导入文件类型不可为空！");
            return errList;
        }
        // 导入文件的入参
        List<MultipartFile> fileList = bmsYsImportDto.getFileList();

        /*
         * 第一步：循环导入的文件
         * 第二步：循环每个文件的第一个sheet
         * 第三步：循环每一行
         * 第四步：循环每一列
         * 第五步：根据导入的文件类型（运输、仓储、仓配）来取对应的值
         */
        for (MultipartFile multipartFile : fileList) {
            BmsYsbillmain bmsYsbillmain = new BmsYsbillmain();
            Workbook wbs;
            try {
                wbs = new HSSFWorkbook(multipartFile.getInputStream());
            } catch (Exception e) {
                try {
                    wbs = new XSSFWorkbook(multipartFile.getInputStream());
                } catch (IOException ex) {
                    log.error("系统异常", e);
                    throw new RuntimeException(ex);

                }
                //logger.error("系统异常", e);
            }
            if (wbs.getNumberOfSheets() == 0) {
                errList.add(multipartFile.getOriginalFilename() + "文件不存在工作表");
                continue;
            }
            //循环sheet数量
            int sheetSize = wbs.getNumberOfSheets();
            for (int s = 0; s < sheetSize; s++) {
                if (s > 0) {
                    // 目前只看第一个sheet
                    break;
                }
                // 获取第一个sheet
                Sheet sheet = wbs.getSheetAt(s);
                // 获取最大行数
                Integer rownum = sheet.getPhysicalNumberOfRows();
                //如果只有标题则跳过该tab
                if (rownum.compareTo(1) == 0) {
                    errList.add(multipartFile.getOriginalFilename() + "只有标题");
                    continue;
                }
                // 循环每一行
                for (int i = 0; i < rownum; i++) {
                    Row row = sheet.getRow(i);
                    //剔除空行
                    if (row == null || row.getCell(0) == null || "".equals(row.getCell(0).toString())) {
                        continue;
                    }
                    //循环每一列
                    for (int cellNum = 0; cellNum < row.getLastCellNum(); cellNum++) {
                        //账单名称
                        if (i == 0 && cellNum == 0) {
                            // row.getCell(cellNum).getStringCellValue(); // 字段名称
                            String billName = HttpClient.getCellValue(row.getCell(cellNum)).toString();
                            if (StrUtil.isEmpty(billName)) {
                                errList.add(multipartFile.getOriginalFilename() + "账单标题不可为空！");
                            }
                            bmsYsbillmain.setBillName(billName);
                        }
                        // 客户名称
                        if (PubNumEnum.one.getIntValue().equals(i) && PubNumEnum.two.getIntValue().equals(cellNum)) {
                            String clientName = HttpClient.getCellValue(row.getCell(cellNum)).toString();
                            if (StrUtil.isEmpty(clientName)) {
                                errList.add(multipartFile.getOriginalFilename() + "客户名称不可为空！");
                            }
                            bmsYsbillmain.setClientName(clientName);
                        }
                        // 费用结算期
                        if (PubNumEnum.one.getIntValue().equals(i) && PubNumEnum.five.getIntValue().equals(cellNum)) {
                            String billDate = HttpClient.getCellValue(row.getCell(cellNum)).toString();
                            if (StrUtil.isEmpty(billDate)) {
                                errList.add(multipartFile.getOriginalFilename() + "费用结算期不可为空！");
                            }
                            bmsYsbillmain.setBillDate(billDate);
                        }
                        // 取运输的总费用
                        if (i == 28 && PubNumEnum.four.getIntValue().equals(cellNum) && bmsYsImportDto.getFileType().equals(FileTypeEnum.TRAN.getIntValue())) {
                            String sumAmt = HttpClient.getCellValue(row.getCell(cellNum)).toString();
                            if (StrUtil.isEmpty(sumAmt)) {
                                errList.add(multipartFile.getOriginalFilename() + "运输总金额不可为空！");
                            } else {
                                bmsYsbillmain.setAdjustedAmount(new BigDecimal(sumAmt));
                            }

                        }
                        // 取仓储的总费用
                        if (i == 34 && PubNumEnum.four.getIntValue().equals(cellNum) && bmsYsImportDto.getFileType().equals(FileTypeEnum.STOCK.getIntValue())) {
                            String sumAmt = HttpClient.getCellValue(row.getCell(cellNum)).toString();
                            if (StrUtil.isEmpty(sumAmt)) {
                                errList.add(multipartFile.getOriginalFilename() + "仓储总金额不可为空！");
                            } else {
                                bmsYsbillmain.setAdjustedAmount(new BigDecimal(sumAmt));
                            }
                        }
                        // 取仓配的金额
                        if (bmsYsImportDto.getFileType().equals(FileTypeEnum.TRAN_STOCK.getIntValue())) {
                            // 仓储金额
                            if (i == 34 && PubNumEnum.four.getIntValue().equals(cellNum) && bmsYsImportDto.getFileType().equals(FileTypeEnum.STOCK.getIntValue())) {
                                String sumAmt = HttpClient.getCellValue(row.getCell(cellNum)).toString();
                                if (StrUtil.isEmpty(sumAmt)) {
                                    errList.add(multipartFile.getOriginalFilename() + "仓储总金额不可为空！");
                                } else {
                                    bmsYsbillmain.setAdjustedAmount(new BigDecimal(sumAmt));
                                }
                            }
                            // 运输金额
                            if (i == 61 && PubNumEnum.four.getIntValue().equals(cellNum)) {
                                String sumAmt = HttpClient.getCellValue(row.getCell(cellNum)).toString();
                                if (StrUtil.isEmpty(sumAmt)) {
                                    errList.add(multipartFile.getOriginalFilename() + "运输总金额不可为空！");
                                } else {
                                    bmsYsbillmain.setAdjustedAmount(bmsYsbillmain.getAdjustedAmount() != null ? bmsYsbillmain.getAdjustedAmount().add(new BigDecimal(sumAmt)) : new BigDecimal(sumAmt));
                                }
                            }
                        }
                    }
                }
            }
            bmsYsbillmainList.add(bmsYsbillmain);
            // 校验账单数据
            checkParam(tenantid, bmsYsbillmain, errList);
            // 导入的文件名称
            bmsYsbillmain.setFlieName(multipartFile.getOriginalFilename());
            // 导入账单的错误信息
            bmsYsbillmain.setErrMsg(CollUtil.isNotEmpty(errList) ? StringUtils.join(errList, ",") : "");
        }
        return errList;
    }

    /**
     * 校验账单数据
     * 部分账单信息赋值
     *
     * @param bmsYsbillmain 账单信息
     * @param errList       错误信息
     */
    public void checkParam(String tenantid, BmsYsbillmain bmsYsbillmain, List<String> errList) {
        // 查询客户名称是否存在
        MdmClientinfo mdmClientinfo = mdmClientinfoMapper.selectClientinfoByCode(tenantid, bmsYsbillmain.getClientName());
        if (mdmClientinfo == null || StrUtil.isEmpty(mdmClientinfo.getClientCode())) {
            errList.add(bmsYsbillmain.getClientName() + "客户名称在系统中不存在");
        } else {
            bmsYsbillmain.setClientId(Math.toIntExact(mdmClientinfo.getId()));
            bmsYsbillmain.setClientCode(mdmClientinfo.getClientCode());
        }
        // 账期赋值
        if (bmsYsbillmain.getBillDate() != null && bmsYsbillmain.getBillDate().contains("至")) {
            String billDate = bmsYsbillmain.getBillDate().split("至")[0];
            SimpleDateFormat smld = new SimpleDateFormat("yyyy年MM月dd日");
            SimpleDateFormat smldBill = new SimpleDateFormat("yyyy-MM");
            try {
                Date billTime = smld.parse(billDate);
                bmsYsbillmain.setBillDate(smldBill.format(billTime));
            } catch (Exception e) {
                log.error("系统异常", e);
                errList.add(bmsYsbillmain.getBillDate() + "费用结算期时间格式不正确");
            }
        } else {
            errList.add(bmsYsbillmain.getBillDate() + "费用结算期格式不正确");
        }
        // 抽取地区，根据地区查找机构
        if (bmsYsbillmain.getBillName().contains("【") && bmsYsbillmain.getBillName().contains("】")) {
            String dqStr = bmsYsbillmain.getBillName().substring(bmsYsbillmain.getBillName().indexOf("【"), bmsYsbillmain.getBillName().indexOf("】"));
            dqStr = dqStr.replace("【", "").replace("】", "");
            MdmClientinfo network = mdmClientinfoMapper.selectNetworkByBillName(tenantid, dqStr);
            if (network != null && network.getCompanyId() != null) {
                bmsYsbillmain.setCompanyId(network.getCompanyId());
            } else {
                errList.add(dqStr + "在系统中不存在");
            }
        } else {
            errList.add(bmsYsbillmain.getBillName() + "地区格式不正确");
        }


    }

    /**
     * 账单默认值赋予
     *
     * @param list     账单信息
     * @param flieTyoe 业务信息
     */
    public void setBillInfo(UserBean loginUserInfo, List<BmsYsbillmain> list, Integer flieTyoe) {
        String tenantId = Optional.of(loginUserInfo).map(UserBean::getTenantid).map(String::valueOf).orElse("");
        // 账单赋默认值
        for (BmsYsbillmain bmsYsbillmain : list) {
            bmsYsbillmain.setBillType(flieTyoe + 1); // 账单类型 取缓存 0:业务账单 1：金融代采账单 2.运输账单 3.仓储账单 4.仓配账单
            bmsYsbillmain.setBillSource(2); // 账单来源： 1：系统生成 2：人工导入
            bmsYsbillmain.setBillCode(codeUtils.getCode(CodeCategory.YSCode, tenantId)); // 账单编号
            bmsYsbillmain.setBillMarking(1);
            bmsYsbillmain.setVotes(0L);// 票数
            bmsYsbillmain.setBillAmount(bmsYsbillmain.getAdjustedAmount()); // 账单金额
            bmsYsbillmain.setYsAmount(bmsYsbillmain.getAdjustedAmount()); // 应收金额
            bmsYsbillmain.setBillState(2); // 账单状态 1已生成未提交2已提交未审核
            bmsYsbillmain.setOperDeptId(loginUserInfo.getCompanyid().intValue());
            bmsYsbillmain.setOperCode(loginUserInfo.getEmployeename());
            bmsYsbillmain.setOperBy(loginUserInfo.getEmployeename());
            bmsYsbillmain.setOperTime(new Date());
            bmsYsbillmain.setCreateCode(loginUserInfo.getEmployeename());
            bmsYsbillmain.setCreateBy(loginUserInfo.getEmployeename());
            bmsYsbillmain.setCreateDeptId(loginUserInfo.getCompanyid().intValue());
            bmsYsbillmain.setCreateTime(new Date());
        }
    }


    /**
     * 新增账单时，校验账单信息
     *
     * @param bmsYsbillmainList 账单集合
     * @return 错误信息
     */
    public String checkBillInsertParam(UserBean loginUserInfo, List<BmsYsbillmain> bmsYsbillmainList) {
        List<String> errList = new ArrayList<>();
        for (BmsYsbillmain bmsYsbillmain : bmsYsbillmainList) {
            if (StrUtil.isEmpty(bmsYsbillmain.getBillName())) {
                errList.add("账单名称不可为空");
            }
            if (StrUtil.isEmpty(bmsYsbillmain.getBillDate())) {
                errList.add("账期不可为空");
            }
            if (bmsYsbillmain.getClientId() == null) {
                errList.add("客户信息不可为空");
            }
            if (bmsYsbillmain.getCompanyId() == null) {
                errList.add("机构不可为空");
            }
            if (bmsYsbillmain.getAdjustedAmount() == null) {
                errList.add("账单金额不可为空");
            }
            if (bmsYsbillmain.getCompanyId() == null) {
                errList.add("所属机构不可为空");
            }
            if (bmsYsbillmain.getBillCode() == null) {
                errList.add("账单编号不可为空");
            }
            bmsYsbillmain.setAuditState(1);
            bmsYsbillmain.setTicketState(1);
            bmsYsbillmain.setHxState(1);
            bmsYsbillmain.setDelFlag(String.valueOf(0));
            bmsYsbillmain.setSubmitStatus(String.valueOf(1));
            bmsYsbillmain.setMergeStatus(String.valueOf(0));
            bmsYsbillmain.setOperDeptId(loginUserInfo.getCompanyid().intValue());
            bmsYsbillmain.setOperCode(loginUserInfo.getEmployeename());
            bmsYsbillmain.setOperBy(loginUserInfo.getEmployeename());
            bmsYsbillmain.setOperTime(new Date());
            bmsYsbillmain.setCreateTime(new Date());
            bmsYsbillmain.setCreateCode(loginUserInfo.getEmployeename());
            bmsYsbillmain.setCreateBy(loginUserInfo.getEmployeename());
            bmsYsbillmain.setCreateDeptId(loginUserInfo.getCompanyid().intValue());
        }
        return CollUtil.isNotEmpty(errList) ? StringUtils.join(errList, ",") : "";
    }
}
