package com.bbyb.joy.bms.calculate.service;

import cn.hutool.core.lang.Pair;
import com.bbyb.joy.bms.calculate.domain.model.CalculateFactorInfoModel;
import com.bbyb.joy.bms.calculate.domain.param.CalculateRuleQueryParam;

import java.util.List;
import java.util.Map;

/**
 * 自动计费报价合同相关业务
 */
public interface CalculateRuleService {


    /**
     * 查询有效的报价合同信息
     * Map:key:relationId(客户id||承运商id),value:报价信息
     */
    Pair<String, Map<Integer,List<CalculateFactorInfoModel>>> queryValidResult(CalculateRuleQueryParam param);


    /**
     * 查询有效的报价合同信息
     * Map:key:relationId(客户id||承运商id),value:报价信息
     */
    List<CalculateFactorInfoModel> queryValidList(CalculateRuleQueryParam param);


}
