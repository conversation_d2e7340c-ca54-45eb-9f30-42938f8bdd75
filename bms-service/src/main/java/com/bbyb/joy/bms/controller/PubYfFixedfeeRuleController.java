package com.bbyb.joy.bms.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.domain.dto.PubYfFixedfeeRule;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.dto.*;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IPubYfFixedfeeRuleService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 应付固定费用规则Controller
 */
@Api(tags = "应付固定费用规则接口")
@RestController
@RequestMapping("/system/pubYfFixedfeeRule")
public class PubYfFixedfeeRuleController {

    @Resource
    private IPubYfFixedfeeRuleService pubYfFixedfeeRuleService;
    @Resource
    private UserRights userRights;
    @Resource
    private ExportUtil exportUtil;

    /**
     * 查询应付固定费用规则列表
     */
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<PubYfFixedfeeRuleExtendDto>> list(@RequestBody(required = false) PubYfFixedfeeRuleDto pubYfFixedfeeRuleDto) {
        String token = RequestContext.getToken();
        UserBean loginUserInfo = RequestContext.getUserInfo();
        Long userId = Long.valueOf(loginUserInfo.getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
        List<ClientInfo> clientInfos = sysDataPack.getClientinfo();
        List<Long> clientIds = clientInfos.stream().map(ClientInfo::getId).collect(Collectors.toList());
        pubYfFixedfeeRuleDto.setClientIds(clientIds);
        return new ResponseResult<>(pubYfFixedfeeRuleService.list(token, pubYfFixedfeeRuleDto));
    }

    /**
     * 作废应付固定费用规则
     */
    @PostMapping("/del")
    public ResponseResult<String> delById(@RequestBody(required = false) List<Long> ids) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(pubYfFixedfeeRuleService.delById(token, ids));
    }

    /**
     * 新增应付固定费用规则
     */
    @PostMapping("/addPubYfFixedfeeRule")
    @MenuAuthority(code = "应付固定费规则-新增")
    public ResponseResult<String> add(@RequestBody(required = false) PubYfFixedfeeRuleDto pubYfFixedfeeRuleDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(pubYfFixedfeeRuleService.insert(token, pubYfFixedfeeRuleDto));
    }

    /**
     * 查询详细信息
     *
     * @param id id
     */
    @PostMapping("/detail/{id}")
    public ResponseResult<PubYfFixedfeeRuleExtendDto> queryDetail(@PathVariable("id") String id) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(pubYfFixedfeeRuleService.queryDetail(token, id));
    }

    /**
     * 修改应付固定费用规则
     */
    @PostMapping("/updatePubYfFixedfeeRule")
    @MenuAuthority(code = "应付固定费规则-修改")
    public ResponseResult<String> update(@RequestBody(required = false) PubYfFixedfeeRuleDto pubYfFixedfeeRuleDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(pubYfFixedfeeRuleService.update(token, pubYfFixedfeeRuleDto));
    }


    /**
     * 导出应付固定费用规则列表
     */
    //导出应付固定费用规则列表
    ////@PreAuthorize("@ss.hasPermi('system:rule:export')")
    @Log(title = "应付固定费用规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @MenuAuthority(code = "应付固定费管理-导出全部")
    public ResponseResult<String> export(@RequestBody(required = false) PubYfFixedfeeQueryDto pubYfFixedfeeQueryDto) {
        String token = RequestContext.getToken();
        List<PubYfFixedfeeDto> pubYfFixedfeeDtos = pubYfFixedfeeRuleService.pubYfFixedfeeList(token, pubYfFixedfeeQueryDto).getRows();
        List<PubYfFixedfeeExportDto> exportDtos = new ArrayList<>();
        pubYfFixedfeeDtos.forEach(item -> {
            PubYfFixedfeeExportDto pubYfFixedfeeExportDto = new PubYfFixedfeeExportDto();
            BeanUtil.copyProperties(item, pubYfFixedfeeExportDto);
            pubYfFixedfeeExportDto.setRuleTypeName(StrUtil.isNotEmpty(item.getRuleTypeName()) ? item.getRuleTypeName() + "账单" : null);
            exportDtos.add(pubYfFixedfeeExportDto);

        });
        return exportUtil.getOutClassNew(token, "应付固定费用管理导出", "应付固定费用管理",
                PubYfFixedfeeExportDto.class, id -> exportDtos);
    }

    /**
     * 获取应付固定费用规则详细信息
     */
    @PostMapping(value = "/{id}")
    public ResponseResult<PubYfFixedfeeRule> getInfo(@PathVariable("id") String id) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(pubYfFixedfeeRuleService.selectPubYfFixedfeeRuleById(token, id));
    }


    /**
     * 修改应付固定费用规则
     */
    @Log(title = "应付固定费用规则", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public ResponseResult<Integer> edit(@RequestBody(required = false) PubYfFixedfeeRule pubYfFixedfeeRule) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(pubYfFixedfeeRuleService.updatePubYfFixedfeeRule(token, pubYfFixedfeeRule));
    }

    /**
     * 物理作废应付固定费用规则
     */
    @Log(title = "应付固定费用规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @MenuAuthority(code = "")
    public ResponseResult<Integer> remove(@PathVariable String[] ids) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(pubYfFixedfeeRuleService.deletePubYfFixedfeeRuleByIds(token, ids));
    }

    /**
     * 逻辑作废/启用应付固定费用规则
     */
    @Log(title = "应付固定费用规则", businessType = BusinessType.UPDATE)
    @PostMapping("/delbyId")
    @MenuAuthority(code = "应付固定费规则-作废")
    public ResponseResult<Integer> updateStatus(@RequestBody(required = false) Map<String, Object> idsAndStatus) {
        String token = RequestContext.getToken();
        List<String> ids = (ArrayList<String>) idsAndStatus.get("ids");
        String status = String.valueOf(idsAndStatus.get("status"));
        return new ResponseResult<>(pubYfFixedfeeRuleService.updatePubYfFixedfeeRuleStatusByIds(token, ids, status));
    }

    @PostMapping("/createPubYfFixedfee")
    public ResponseResult<String> createPubYfFixedfee() {
//        pubYfFixedfeeRuleService.regularlyGenerateBills();
        return new ResponseResult<>("成功");
    }


    @PostMapping("/queryByBillId/{billId}")
    public ResponseResult<List<PubYfFixedfeeDto>> queryByBillId(@PathVariable("billId") Integer billId) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(pubYfFixedfeeRuleService.queryByBillId(token, billId));
    }

    /**
     * 查询应付固定费列表
     */
    @PostMapping("/pubYfFixedfeeList")
    public ResponseResult<PagerDataBean<PubYfFixedfeeDto>> pubYfFixedfeeList(@RequestBody(required = false) PubYfFixedfeeQueryDto pubYfFixedfeeQueryDto) {
        String token = RequestContext.getToken();
        UserBean userVO = RequestContext.getUserInfo();
        Long userId = Long.valueOf(userVO.getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
        List<ClientInfo> clientInfos = sysDataPack.getClientinfo();
        List<Long> clientIds = clientInfos.stream().map(ClientInfo::getId).collect(Collectors.toList());
        pubYfFixedfeeQueryDto.setClientIds(clientIds);
        return new ResponseResult<>(pubYfFixedfeeRuleService.pubYfFixedfeeList(token, pubYfFixedfeeQueryDto));
    }

    //作废应付固定费
    @PostMapping("/delpubYfFixedfee")
    @MenuAuthority(code = "应付固定费管理-作废")
    public ResponseResult<String> delpubYfFixedfeeById(@RequestBody(required = false) List<Long> ids) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(pubYfFixedfeeRuleService.delpubYfFixedfeeById(token, ids));
    }

    /**
     * 生成固定费
     */
    @PostMapping("/generageBills")
    public ResponseResult<String> generageBills() {
        String token = RequestContext.getToken();
        pubYfFixedfeeRuleService.generageBills(token);
        return new ResponseResult<>();
    }
}
