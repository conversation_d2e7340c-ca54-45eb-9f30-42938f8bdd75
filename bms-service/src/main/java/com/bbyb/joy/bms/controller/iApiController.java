package com.bbyb.joy.bms.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.bbyb.bms.model.po.*;
import com.bbyb.joy.bms.domain.RequestApiDto;
import com.bbyb.joy.bms.service.IApiService;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import com.bbyb.joy.bms.support.datesource.BmsDs;
import com.bbyb.joy.utils.utils.SnowFlowUtils;
import com.google.common.collect.Lists;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 接口对接Service 接口
 */
@RestController
@RequestMapping(value = "/apiService")
public class iApiController {

    @Resource
    private IApiService iApiService;


    /**
     * 接口对接
     */
    @PostMapping(value = "/requestApi")
    public String requestApi(@RequestBody RequestApiDto requestApiDto) {
        return (iApiService.requestApi(requestApiDto));
    }


    @GetMapping(value = "/mockApi")
    public void mockApi() {
        List<String> mainIds = Lists.newArrayList("650403552660951040");

        //费用主数据
        BmsYscostMainInfoPOExample mainInfoPOExample = new BmsYscostMainInfoPOExample();
        mainInfoPOExample.createCriteria()
                .andDelFlagEqualTo(BmsConstants.DEL_FLAG_0)
                .andIdIn(mainIds);
        BmsYscostMainInfoPO mainInfoPO = BmsDs.instance().WMSMybatis().mapper().selectFirstByExample(mainInfoPOExample);
        mainInfoPO.setId(null);
        mainInfoPO.setPkId(null);

        //费用结算信息
        BmsYscostInfoPOExample costInfoPOExample = new BmsYscostInfoPOExample();
        costInfoPOExample.createCriteria()
                .andDelFlagEqualTo(BmsConstants.DEL_FLAG_0)
                .andMainCodeIdIn(mainIds);
        BmsYscostInfoPO costInfo = BmsDs.instance().WMSMybatis().mapper().selectFirstByExample(costInfoPOExample);
        costInfo.setId(null);
        costInfo.setPkId(null);

        //中间表
        BmsYsexpensesMiddlePOExample middleInfoPOExample = new BmsYsexpensesMiddlePOExample();
        middleInfoPOExample.createCriteria()
                .andDelFlagEqualTo(BmsConstants.DEL_FLAG_0)
                .andMainCodeIdIn(mainIds);
        BmsYsexpensesMiddlePO middlePO = BmsDs.instance().WMSMybatis().mapper().selectFirstByExample(middleInfoPOExample);
        middlePO.setId(null);

        //拓展表
        BmsYscostExtendPOExample extendInfoPOExample = new BmsYscostExtendPOExample();
        extendInfoPOExample.createCriteria()
                .andDelFlagEqualTo(BmsConstants.DEL_FLAG_0.toString())
                .andMainExpenseIdIn(mainIds);
        BmsYscostExtendPO extendPO = BmsDs.instance().WMSMybatis().mapper().selectFirstByExample(extendInfoPOExample);
        extendPO.setId(null);


        List<BmsYscostMainInfoPO> insertMainInfoPOS = Lists.newArrayList();
        List<BmsYscostInfoPO> insertInfoPOS = Lists.newArrayList();
        List<BmsYscostExtendPOWithBLOBs> insertExtendPOS = Lists.newArrayList();
        List<BmsYsexpensesMiddlePO> insertMiddlePOS = Lists.newArrayList();

        int size = 20000;
        Date startNow = new Date();
        String startNowStr = DateUtil.now();
        for (int i = 0; i < size; i++) {
            String mainId = SnowFlowUtils.nextId();
            BmsYscostMainInfoPO newMainInfoPO = BeanUtil.toBean(mainInfoPO, BmsYscostMainInfoPO.class);
            newMainInfoPO.setId(mainId);
            newMainInfoPO.setCreateTime(startNow);
            newMainInfoPO.setCreateBy("MOCK数据");
            insertMainInfoPOS.add(newMainInfoPO);

            BmsYscostInfoPO newCostInfoPO = BeanUtil.toBean(costInfo, BmsYscostInfoPO.class);
            newCostInfoPO.setId(SnowFlowUtils.nextId());
            newCostInfoPO.setMainCodeId(mainId);
            newCostInfoPO.setOperTime(startNow);
            newCostInfoPO.setOperBy("MOCK数据");
            insertInfoPOS.add(newCostInfoPO);

            BmsYscostExtendPOWithBLOBs newExtendPO = BeanUtil.toBean(extendPO, BmsYscostExtendPOWithBLOBs.class);
            newExtendPO.setMainExpenseId(mainId);
            newExtendPO.setWarehouseName("MOCK数据");
            insertExtendPOS.add(newExtendPO);

            BmsYsexpensesMiddlePO newMiddlePO = BeanUtil.toBean(middlePO, BmsYsexpensesMiddlePO.class);
            newMiddlePO.setMainCodeId(mainId);
            insertMiddlePOS.add(newMiddlePO);
        }
        String endNowStr = DateUtil.now();


        List<List<BmsYscostMainInfoPO>> mainSplit = ListUtil.split(insertMainInfoPOS, 1000);
        for (List<BmsYscostMainInfoPO> mainInfoPOS : mainSplit) {
            BmsDs.instance().WMSMybatis().mapper().batchInsert(mainInfoPOS);
        }

        List<List<BmsYscostInfoPO>> costSplit = ListUtil.split(insertInfoPOS, 1000);
        for (List<BmsYscostInfoPO> bmsYscostInfoPOS : costSplit) {
            BmsDs.instance().WMSMybatis().mapper().batchInsert(bmsYscostInfoPOS);
        }

        List<List<BmsYscostExtendPOWithBLOBs>> splitExtend = ListUtil.split(insertExtendPOS, 1000);
        for (List<BmsYscostExtendPOWithBLOBs> bmsYscostExtendPOS : splitExtend) {
            BmsDs.instance().WMSMybatis().mapper().batchInsert(bmsYscostExtendPOS);
        }

        List<List<BmsYsexpensesMiddlePO>> splitMiddle = ListUtil.split(insertMiddlePOS, 1000);
        for (List<BmsYsexpensesMiddlePO> middlePOS : splitMiddle) {
            BmsDs.instance().WMSMybatis().mapper().batchInsert(middlePOS);
        }

        System.out.println("开始时间:" + startNowStr);
        System.out.println("结束时间:" + endNowStr);


    }


}
