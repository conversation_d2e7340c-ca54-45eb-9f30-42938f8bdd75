package com.bbyb.joy.bms.calculate.convert;

import java.util.List;

/**
 * 单据转换器接口
 * @param <S> 源类型
 * @param <T> 目标类型
 */
public interface OrderConverter<S, T> {


    /**
     * 将源对象列表转换为目标对象列表
     * @param sourceList 源对象列表
     * @return 目标对象列表
     */
    List<T> convert(List<S> sourceList);


    /**
     * 获取源类型
     * @return 源类型Class对象
     */
    Class<S> getSourceType();


    /**
     * 获取目标类型
     * @return 目标类型Class对象
     */
    Class<T> getTargetType();



}
