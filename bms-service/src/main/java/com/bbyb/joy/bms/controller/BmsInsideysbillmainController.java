package com.bbyb.joy.bms.controller;

import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.BmsRegistrationDto;
import com.bbyb.joy.bms.domain.dto.bill.*;
import com.bbyb.joy.bms.domain.dto.dto.BmsInsidereceivefeeDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsInsidereceivefeeExportDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsInsideysbillmainListDto;
import com.bbyb.joy.bms.domain.enums.ConfirmStateEnum;
import com.bbyb.joy.bms.service.BmsInsideysbillmainService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Api(tags = "内部结算账单")
@RestController
@RequestMapping("/system/Insideysbillmain")
public class BmsInsideysbillmainController {

    @Resource
    BmsInsideysbillmainService bmsInsideysbillmainService;
    @Resource
    private ExportUtil exportUtil;

    /**
     * 内部结算账单列表
     *
     * @param insideysbillDto 内部结算账单列表查询条件
     * @return 返回RpcResult对象，包含分页后的内部结算账单列表数据
     */
    @PostMapping("/list")
    @MenuAuthority(code = "内部结算账单管理-导出全部,内部账单应付确认-导出全部,内部结算账单管理,内部账单应付确认")
    public ResponseResult<PagerDataBean<BmsInsideysbillmainListDto>> list(@RequestBody(required = false) BmsInsideysbillmainListBean insideysbillDto) {
        if (insideysbillDto == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.list(RequestContext.getToken(),insideysbillDto));
    }

    /**
     * 获取内部结算账单详情
     *
     * @param bean 内部结算账单查询条件Bean对象
     * @return 包含分页后的内部结算账单详情的RpcResult对象
     */
    @PostMapping("/getInfo")
    public ResponseResult<PagerDataBean<BmsInsidereceivefeeDto>> getInfo(@RequestBody(required = false) BmsInsidereceivefeeBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        if (bean.getCodes() == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "codes不可为空");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.getInfo(bean, RequestContext.getToken()));
    }

    /**
     * 内部结算账单作废
     *
     * @param bean 内部结算账单作废条件Bean对象
     * @return RpcResult对象，表示作废结果
     */
    @PostMapping("/del")
    @MenuAuthority(code = "内部结算账单管理-作废")
    public ResponseResult<String> remove(@RequestBody(required = false) BmsInsidereceivefeeBean bean) {
        if (bean == null || bean.getIds() == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.remove(bean, RequestContext.getToken()));
    }

    /**
     * 内部结算账单修改
     *
     * @param insideysbillDto 内部结算账单修改条件Bean对象
     * @return 修改结果的RpcResult对象
     */
    @PostMapping("/update")
    @MenuAuthority(code = "内部结算账单管理-修改")
    public ResponseResult<String> update(@RequestBody BmsInsideysbillmainBean insideysbillDto) {
        if (insideysbillDto == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.update(insideysbillDto, RequestContext.getToken()));
    }


    /**
     * 内部结算账单明细导出
     *
     * @param bean 内部结算账单明细导出条件Bean对象，可以为空
     * @return RpcResult对象，包含导出的文件信息
     */
    @PostMapping("/exportDetail")
    @MenuAuthority(code = "内部结算账单管理-导出明细,内部账单应付确认-导出明细")
    public ResponseResult<String> exportDetail(@RequestBody(required = false) BmsInsidereceivefeeBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        List<BmsInsidereceivefeeDto> bmsInsidereceivefeeDtos = bmsInsideysbillmainService.getExportDetail(bean, RequestContext.getToken());
        String dateTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        return exportUtil.getOutClassNew(RequestContext.getToken(), "导出_内部结算账单明细_" + dateTime, "内部结算账单管理",
                BmsInsidereceivefeeDto.class, a -> bmsInsidereceivefeeDtos);
    }


    /**
     * 内部结算取消账单核销
     *
     * @param bean 内部结算账单取消核销条件Bean对象，可以为空
     * @return RpcResult对象，包含取消账单核销结果
     */
    @PostMapping("/cancelVerification")
    @MenuAuthority(code = "内部结算账单管理-核销,内部结算账单管理-取消核销")
    public ResponseResult<String> cancelVerification(@RequestBody(required = false) BmsInsidereceivefeeBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        if (bean.getIds() == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "请选择账单");
        }
        if (bean.getType() == null || bean.getType() >= 3) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "请输入核销状态");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.cancelVerification(bean, RequestContext.getToken()));
    }

    /**
     * 内部结算取消账单确认
     *
     * @param bean 内部结算账单确认条件Bean对象，可以为空
     * @return RpcResult对象，包含账单确认结果
     */
    @PostMapping("/billConfirmation")
    @MenuAuthority(code = "内部账单应付确认-确认,内部账单应付确认-取消确认")
    public ResponseResult<String> billConfirmation(@RequestBody(required = false) BmsInsidereceivefeeBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        if (bean.getIds() == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "请选择账单");
        }
        if (bean.getType() == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "请输入确认状态");
        }
        if (bean.getType().equals(ConfirmStateEnum.REJECTED.getIntValue()) && StrUtil.isBlank(bean.getConfirmRemark())) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "请输入驳回原因");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.billConfirmation(bean, RequestContext.getToken()));
    }


    /**
     * 内部应收计费登记
     *
     * @param bean 内部应收计费登记条件Bean对象，可以为空
     * @return RpcResult对象，包含分页后的内部应收计费登记列表数据
     */
    @PostMapping("/billRegistration")
    @MenuAuthority(code = "内部应收费用登记-手工计费,内部应收费用登记-导出全部,内部结算应付复核-修改计费,内部结算应付复核-导出全部,内部应收费用登记,内部结算应付复核")
    public ResponseResult<PagerDataBean<BmsRegistrationDto>> billRegistration(@RequestBody(required = false) BmsRegistrationBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.getRegistrationList(RequestContext.getToken(),bean));
    }


    /**
     * 内部应收计费登记合并
     *
     * @param bean 内部应收计费登记合并条件Bean对象，可以为空
     * @return RpcResult对象，包含合并结果
     */
    @PostMapping("/billMerge")
    @MenuAuthority(code = "内部应收费用登记-合并")
    public ResponseResult<String> billMerge(@RequestBody(required = false) BillGenerationBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        if (bean.getIds() == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "请选择你要合并的数据！");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.billMerge(bean, RequestContext.getToken()));
    }


    /**
     * 内部应收计费登记取消合并
     *
     * @param bean 内部应收计费登记取消合并条件Bean对象，可以为空
     * @return RpcResult对象，包含取消合并结果
     */
    @PostMapping("/unBillMerge")
    @MenuAuthority(code = "内部应收费用登记-取消合并")
    public ResponseResult<String> unBillMerge(@RequestBody(required = false) BillGenerationBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        if (bean.getIds() == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "请选择你要取消合并的数据！");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.unBillMerge(bean, RequestContext.getToken()));
    }

    /**
     * 内部应收计费登记取消计费
     *
     * @param bean 内部应收计费登记取消计费条件Bean对象，可以为空
     * @return RpcResult对象，包含取消计费结果
     */
    @PostMapping("/cancelBilling")
    @MenuAuthority(code = "内部应收费用登记-取消计费")
    public ResponseResult<String> cancelBilling(@RequestBody(required = false) BillGenerationBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        if (bean.getIds() == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "请选择你要取消计费的数据！");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.cancelBilling(bean, RequestContext.getToken()));
    }


    /**
     * 内部应收计费登记账单生成
     *
     * @param bean 内部应收计费登记账单生成条件Bean对象，可以为空
     * @return RpcResult对象，包含账单生成结果
     */
    @PostMapping("/billGeneration")
    @MenuAuthority(code = "内部应收费用登记-账单生成")
    public ResponseResult<String> billGeneration(@RequestBody(required = false) BillGenerationBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        if (bean.getFeeIds() == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "请选择你要取消生成的数据！");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.billGeneration(bean, RequestContext.getToken()));
    }

    /**
     * 内部应收计费登记费用导出
     *
     * @param bean 内部应收计费登记费用导出条件Bean对象，可以为空
     * @return RpcResult对象，包含导出结果
     */
    @PostMapping("/billExport")
    @MenuAuthority(code = "内部应收费用登记-导出")
    public ResponseResult<String> billExport(@RequestBody(required = false) BmsRegistrationBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        List<BmsInsidereceivefeeExportDto> listRpcResult = bmsInsideysbillmainService.billExport(bean, RequestContext.getToken());
        String dateTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        return exportUtil.getOutClassNew(RequestContext.getToken(), "导出_内部应收计费登记费用_" + dateTime, "内部应收计费登记费用",
                BmsInsidereceivefeeExportDto.class, a -> listRpcResult);
    }


    /**
     * 内部应收计费费用登记
     *
     * @param bean 内部应收计费费用登记Bean对象，可以为空
     * @return RpcResult对象，包含登记结果
     */
    @PostMapping("/costRegistration")
    public ResponseResult<String> registration(@RequestBody(required = false) BillGenerationBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        if (bean.getBmsBillRegistrationDtoList() == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "费用信息不可为空");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.registration(bean, RequestContext.getToken()));
    }

    /**
     * 内部应收计费导入
     *
     * @param bean 内部应收计费导入Bean对象，可以为空
     * @return RpcResult对象，包含导入结果
     */
    @PostMapping("/billImport")
    @MenuAuthority(code = "内部应收费用登记-导入计费")
    public ResponseResult<String> billImport(@RequestBody(required = false) BillGenerationBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        if (bean.getBmsBillRegistrationDtoList() == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "费用信息不可为空");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.billImport(bean, RequestContext.getToken()));
    }


    /**
     * 内部应收计费复核
     *
     * @param bean 内部应收计费复核条件Bean对象，可以为空
     * @return RpcResult对象，包含复核结果
     */
    @PostMapping("/billCheck")
    @MenuAuthority(code = "内部结算应付复核-批量复核,内部结算应付复核-取消复核")
    public ResponseResult<String> billCheck(@RequestBody(required = false) BillGenerationBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "参数不可为空");
        }
        if (bean.getFeeIds() == null) {
            return new ResponseResult<>(ServiceError.REQUEST_PARAMETER_ERROR.getCode(), "请选择你要复核的数据！");
        }
        return new ResponseResult<>(bmsInsideysbillmainService.billCheck(bean, RequestContext.getToken()));
    }
}
