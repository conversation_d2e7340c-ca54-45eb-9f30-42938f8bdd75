package com.bbyb.joy.bms.calculate.service;

import com.bbyb.joy.bms.calculate.domain.param.CalculateDbParam;
import com.bbyb.joy.bms.calculate.domain.result.CalculateYfDbResult;
import com.bbyb.joy.bms.calculate.domain.result.CalculateYsDbResult;

import java.util.List;

/**
 * 计费事后操作
 */
public interface CalculateAfterService {

    /**
     * 应收计费结果处理
     * 客户维度
     * @param param 计费结果
     * @return 应收维度计费结果DB整合
     */
    CalculateYsDbResult ysDbCalculate(CalculateDbParam param);
    /**
     * 应付计费结果处理
     * 客户维度
     * @param param 计费结果
     * @return 应付维度计费结果DB整合
     */
    CalculateYfDbResult yfDbCalculate(CalculateDbParam param);
    /**
     * DB后费用数据校准
     * 费用数据校准
     * 处理middle表，middle_share表,record表的单据以及费用单的缺失id
     * @param codePkIds 单据pkId
     * 策略类型:1:运输单,21:仓储单(应收),22:仓储单(应付),3:调度单
     */
    void calculateDataDalibration(Integer handlerType, List<Integer> codePkIds);

}
