package com.bbyb.joy.bms.calculate.domain.dto.code.order;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单维度详情实体2(有些单据详情不止一个详情信息,所以需要另外一个)
 * 该类的是单据进入计费的单据详情信息
 * 已使用的单据(作业单)
 * 该类整合了BMS所有业务单据类型的通用字段
 * 后端拓展单据业务如果有新的单据业务属性计费需要就需要在该类进行拓展补充
 * ## 字段含义补充:
 * 1、(重要)所有业务属性字段不再使用type的code,全部要转译为对应的label,便于维护报价
 * 2、(重要)那基于1带来额外的补充,需要补充对应的业务属性转译的字典的维护源头(要确保一定准确)
 * 3、(重要)日期业务字段全部转移为字符串,groovy运行引用就不再需要处理时间
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateOrderDetail2Dto implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;                     // id(uuid)
    private Integer pkId;                  // 自增键
    private String code;                   // 单号

    // 单据信息
    private String mainCodeId;             // 单据主表id(uuid) 这里指的作业单id
    private Integer mainPkId;              // 单据主表id(自增id) 这里指的作业单pkId
    private Integer schedulingPkId;        // 调度单pkId


    // 客户信息
    private String clientCode;             // 客户编码
    private String clientName;             // 客户名称

    // 机构信息
    private String companyName;             // 所属机构id
    private String originatingCompanyName;  // 始发机构id
    private String destinationCompanyName;  // 目的机构id
    private String payCompanyName;          // 付款机构id
    private String networkCode;             // 网点编码

    // 门店信息
    private String receivingStoreCode;     // 收货门店编码
    private String receivingStoreName;     // 收货门店名称
    //    private Integer baseStoreStatus;       // 是否基数外门店(0否1是)
    private String baseStoreStatusName;
    //    private Integer baseMileageStatus;     // 是否超基数公里(0否1是)
    private String baseMileageStatusName;
    private BigDecimal overMileage;        // 超公里数

    // 数量信息
    private BigDecimal totalBoxes;         // 总箱数
    private BigDecimal totalNumber;        // 总件数
    private BigDecimal totalWeight;        // 总重量(kg)
    private BigDecimal totalVolume;        // 总体积(m³)
    private BigDecimal totalCargoValue;    // 总货值

    // 始发地信息
    private String originatingProvince;    // 始发省
    private String originatingCity;        // 始发市
    private String originatingArea;        // 始发区
    private String originatingAddress;     // 始发详细地址
    private String originatingAddressCode; // 始发地编码
    private String originatingAddressName; // 始发地名称

    // 目的地信息
    private String destinationProvince;    // 目的省
    private String destinationCity;        // 目的市
    private String destinationArea;        // 目的区
    private String destinationAddress;     // 目的详细地址
    private String destinationAddressCode; // 目的地编码
    private String destinationAddressName; // 目的地名称

    // 基础字段
    private String remark;                 // 备注
    private Integer optMonth;              // 月分区
    private Integer optDay;                // 日分区

}
