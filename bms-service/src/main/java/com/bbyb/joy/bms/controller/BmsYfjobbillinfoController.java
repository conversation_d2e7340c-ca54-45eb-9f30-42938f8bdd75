package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.BmsYfjobbillinfo;
import com.bbyb.joy.bms.domain.dto.FileBaseDto;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsYfjobbillinfoService;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.utils.ExcelUtil;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 应付作业单Controller
 *
 * <AUTHOR>
 */
@Api(tags = "应付作业单接口")
@RestController
@RequestMapping("/system/yfjobbillinfo")
public class BmsYfjobbillinfoController {

    @Resource
    private IBmsYfjobbillinfoService bmsYfjobbillinfoService;

    /**
     * 查询应付作业单列表
     */
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<BmsYfjobbillinfo>> list(@RequestBody(required = false) BmsYfjobbillinfo bmsYfjobbillinfo) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfjobbillinfoService.selectBmsYfjobbillinfoList(token, bmsYfjobbillinfo));
    }


    /**
     * 导出应付作业单列表
     */
    @Log(title = "应付作业单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public ResponseResult<String> export(@RequestBody(required = false) BmsYfjobbillinfo bmsYfjobbillinfo) {
        String token = RequestContext.getToken();
        List<BmsYfjobbillinfo> list = bmsYfjobbillinfoService.selectBmsYfjobbillinfoList(token, bmsYfjobbillinfo).getRows();
        ExcelUtil<BmsYfjobbillinfo> util = new ExcelUtil<BmsYfjobbillinfo>(BmsYfjobbillinfo.class);
        FileBaseDto fileBaseDto = util.exportExcel(list, "应付作业单数据");
        return new ResponseResult<>(true, 0, fileBaseDto.getFileName(), fileBaseDto.getFileName());
    }

    /**
     * 获取应付作业单详细信息
     */
    @PostMapping(value = "/{id}")
    public ResponseResult<BmsYfjobbillinfo> getInfo(@PathVariable("id") String id) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfjobbillinfoService.selectBmsYfjobbillinfoById(token, id));
    }

    /**
     * 新增应付作业单
     */
    @Log(title = "应付作业单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public ResponseResult<Integer> add(@RequestBody BmsYfjobbillinfo bmsYfjobbillinfo) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfjobbillinfoService.insertBmsYfjobbillinfo(token, bmsYfjobbillinfo));
    }

    /**
     * 修改应付作业单
     */
    @Log(title = "应付作业单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public ResponseResult<Integer> edit(@RequestBody BmsYfjobbillinfo bmsYfjobbillinfo) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfjobbillinfoService.updateBmsYfjobbillinfo(token, bmsYfjobbillinfo));
    }

    /**
     * 物理作废应付作业单
     */
    @Log(title = "应付作业单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public ResponseResult<Integer> remove(@PathVariable String[] ids) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfjobbillinfoService.deleteBmsYfjobbillinfoByIds(token, ids));
    }

    /**
     * 逻辑作废/启用应付作业单
     */
    @Log(title = "应付作业单", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateStatus")
    public ResponseResult<Integer> updateStatus(@RequestBody Map<String, Object> idsAndStatus) {
        String token = RequestContext.getToken();
        List<String> idsList = (ArrayList<String>) idsAndStatus.get("ids");
        String[] ids = idsList.toArray(new String[0]);
        String status = String.valueOf(idsAndStatus.get("status"));
        return new ResponseResult<>(bmsYfjobbillinfoService.updateBmsYfjobbillinfoStatusByIds(token, ids, status));
    }

    /**
     * 根据调度单号查询作业单信息
     */
    @PostMapping("/selectYfjobbillinfoByYfCodes")
    public ResponseResult<List<BmsYfjobbillinfo>> selectYfjobbillinfoByYfCodes(@RequestBody(required = false) List<String> codes) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfjobbillinfoService.selectYfjobbillinfoByYsbillId(token, codes));
    }

}
