package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.PubQuoteruleFiledseting;
import com.bbyb.joy.bms.service.IPubQuoteruleFiledsetingService;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 报价规则列设置Controller
 */
@Api(tags = "报价规则列设置接口")
@RestController
@RequestMapping("/system/filedseting")
public class PubQuoteruleFiledsetingController {

    @Resource
    private IPubQuoteruleFiledsetingService pubQuoteruleFiledsetingService;

    /**
     * 查询报价规则列设置列表
     */
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<PubQuoteruleFiledseting>> list(@RequestBody(required = false) PubQuoteruleFiledseting pubQuoteruleFiledseting) {
        String token = RequestContext.getToken();
        if (pubQuoteruleFiledseting == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(pubQuoteruleFiledsetingService.selectPubQuoteruleFiledsetingList(token, pubQuoteruleFiledseting));
    }
}