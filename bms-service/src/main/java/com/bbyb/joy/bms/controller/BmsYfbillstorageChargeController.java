package com.bbyb.joy.bms.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.MdmWarehouseinfo;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.charginglogic.DispatchBillingBean;
import com.bbyb.joy.bms.domain.dto.dto.BmsYfbillBtnDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDto;
import com.bbyb.joy.bms.domain.dto.dto.DRYfcostInfoDto;
import com.bbyb.joy.bms.domain.dto.querybean.BmsYfbillcodeinfoBean;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.WarehouseInfo;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsYfbillcodeinfoService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.exception.BusinessException;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应付单据信息主Controller
 */
@Api(tags = "应付仓储计费主接口")
@RestController
@RequestMapping("/system/yfbillcodeinfo2")
public class BmsYfbillstorageChargeController {

    @Resource
    private IBmsYfbillcodeinfoService bmsYfbillcodeinfoService;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private UserRights userRights;
    @Resource
    private ExportUtil exportUtil;

    /**
     * 仓储计费分页查询
     */
    @PostMapping("/selectStorageBill")
    @MenuAuthority(code = "应付仓储计费-批量查询")
    public ResponseResult<PagerDataBean<BmsYfstockcodeDto>> selectStorageBill(@RequestBody BmsYfbillcodeinfoBean bean) {
        String token = RequestContext.getToken();
        List<BmsYfstockcodeDto> list = new ArrayList<>();
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "carrier,warehouse");
        List<Carrierinfo> carrierInfos = sysDataPack.getCarrierInfo();
        List<WarehouseInfo> warehouseInfos = sysDataPack.getWarehouseInfo();
        bean.setCarrierCode(carrierInfos.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.joining(",")));
        if (StrUtil.isEmpty(bean.getWarehouseCode())) {
            bean.setWarehouseCode(warehouseInfos.stream().map(WarehouseInfo::getWarehouseCode).collect(Collectors.joining(",")));
        }
        if (StrUtil.isEmpty(bean.getCarrierCode()) || StrUtil.isEmpty(bean.getWarehouseCode())) {
            return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(bean.getPage(), bean.getSize())));
        }
        // 根据仓库名称查询仓库编码
        if (StrUtil.isNotEmpty(bean.getWarehouseName())) {
            Map<String, MdmWarehouseinfo> warehouseMap = textConversionUtil.selectWarehouseMap(null, null, bean.getWarehouseName());
            if (warehouseMap.isEmpty()) {
                return new ResponseResult<>(new PagerDataBean<>());
            } else {
                List<String> s = warehouseMap.values().stream().map(MdmWarehouseinfo::getWarehouseCode).collect(Collectors.toList());
                List<String> ss = Arrays.asList(bean.getWarehouseCode().split(","));
                String warehouseCode = s.stream().filter(ss::contains).collect(Collectors.joining(","));
                if (StrUtil.isEmpty(warehouseCode)) {
                    return new ResponseResult<>(new PagerDataBean<>());
                }
                bean.setWarehouseCode(warehouseCode);
            }
        }
        return new ResponseResult<>(bmsYfbillcodeinfoService.selectStorageBill(token, bean));
    }

    /**
     * 仓储计费导入
     */
    @PostMapping("/importStorageBill")
    @MenuAuthority(code = "应付仓储计费-导入单据")
    public ResponseResult<String> importStorageBill(@RequestBody MultipartFile file) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillcodeinfoService.importStorageBill(token, file));
    }

    /**
     * 修改计费
     */
    @PostMapping("/updateCost")
    @MenuAuthority(code = "应付仓储计费-修改费用")
    public ResponseResult<String> updateCost(@RequestBody DispatchBillingBean bean) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillcodeinfoService.updateCost(token, bean));
    }

    /**
     * 导入计费
     */
    @PostMapping("/importCost")
    @MenuAuthority(code = "应付仓储计费-导入计费")
    public ResponseResult<String> importCost(@RequestBody DRYfcostInfoDto dto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillcodeinfoService.importCost(token, dto));
    }

    /**
     * 取消计费
     */
    @PostMapping("/cancelCost")
    @MenuAuthority(code = "应付仓储计费-取消计费")
    public ResponseResult<String> cancelCost(@RequestBody List<DispatchBillingBean> list) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillcodeinfoService.cancelCost(token, list));
    }

    /**
     * 仓储计费导出
     */
    @PostMapping("/exporttStorageBill")
    @MenuAuthority(code = "应付仓储计费-导出")
    public ResponseResult<String> exporttStorageBill(@RequestBody BmsYfbillcodeinfoBean bean) {
        String token = RequestContext.getToken();
        Optional.ofNullable(bean).map(BmsYfbillcodeinfoBean::getCarrierName).filter(StrUtil::isNotBlank).orElseThrow(() -> new BusinessException("请先选择承运商"));
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "carrier,warehouse");
        List<Carrierinfo> carrierInfos = sysDataPack.getCarrierInfo();
        List<WarehouseInfo> warehouseInfos = sysDataPack.getWarehouseInfo();
        bean.setCarrierCode(carrierInfos.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.joining(",")));
        bean.setWarehouseCompanyId(warehouseInfos.stream().map(e -> e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        PagerDataBean<BmsYfstockcodeDto> list = bmsYfbillcodeinfoService.selectStorageBill(token, bean);
        Map<String, List<BmsYfstockcodeDto>> yfstockMap = list.getRows().stream()
                .collect(Collectors.groupingBy(BmsYfstockcodeDto::getClientCode));
        String dateTime = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss").format(new Date());
        String fileNameMsg = "应付仓储计费_" + dateTime;
        if (yfstockMap.size() == 1) {
            fileNameMsg = list.getRows().get(0).getClientName() + "_" + fileNameMsg;
        }
        return exportUtil.getOutClassNewSheets2(token, fileNameMsg, "应付仓储计费", "yfbillcodeinfo2", BmsYfbillcodeDto.class, bean, userid -> {
            if (StrUtil.isEmpty(bean.getCarrierCode()) || StrUtil.isEmpty(bean.getWarehouseCompanyId())) {
                return null;
            }
            return list.getRows();
        });
    }

    /**
     * 生成账单
     */
    @Log(title = "生成账单", businessType = BusinessType.INSERT)
    @PostMapping("/generateBill")
    @MenuAuthority(code = "应付仓储计费-批量生成账单")
    public ResponseResult<String> generateBill(@RequestBody(required = false) BmsYfbillBtnDto bmsYfbillBtnDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillcodeinfoService.generateBill(token, bmsYfbillBtnDto));
    }

    /**
     * 批量生成账单
     */
    @Log(title = "批量生成账单", businessType = BusinessType.INSERT)
    @PostMapping("/generateBillBatch")
    @MenuAuthority(code = "应付仓储计费-按条件生成账单")
    public ResponseResult<String> generateBillBatch(@RequestBody(required = false) BmsYfbillBtnDto bmsYfbillBtnDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillcodeinfoService.generateBillBatch(token, bmsYfbillBtnDto));
    }

    /**
     * 根据计费id查询单据信息
     */
    @PostMapping("/selectStorageByExpensesId")
    public ResponseResult<List<BmsYfstockcodeDto>> selectStorageByExpensesId(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (json == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        String expensesId = JSONObject.parseObject(json).getString("expensesId");
        return new ResponseResult<>(bmsYfbillcodeinfoService.selectStorageByExpensesId(token, expensesId));
    }
}
