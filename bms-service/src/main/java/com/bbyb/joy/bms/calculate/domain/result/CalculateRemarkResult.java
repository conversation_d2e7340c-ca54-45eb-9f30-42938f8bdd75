package com.bbyb.joy.bms.calculate.domain.result;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CalculateRemarkResult implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 单据类型(1:运输单,2:仓储单)
     */
    private Integer codeType;

    /**
     * 计费是否成功,0:成功，1:失败(只关注计费公式，和计费过程没有关系)
     */
    private Integer calculateFlag;

    /**
     * 计费结果
     */
    private BigDecimal calculateResult;

    /**
     * 计费备注
     */
    private String calculateRemark;

    /**
     * 计费过程结果(因为计费过程也是调用groovy生成，所以存在成功或者失败，成功或失败都往计费过程存放即可)
     */
    private String calculateProcessResult;

    /**
     * 费用维度(单，月，日，品)
     */
    private Integer expensesDimension;

    /**
     * 费用维度(单，月，日，品)
     */
    private String expensesDimensionName;

    /**
     * 报价子合同id:pub_quoterule_detail.pk_id
     */
    private Integer ruleDetailPkId;

    /**
     * 报价子合同
     */
    private String ruleDetailName;

    /**
     * 计费规则主id:pub_quoterule.pk_id
     */
    private Integer ruleMainPkId;

    /**
     * 报价主合同
     */
    private String ruleMainName;

}
