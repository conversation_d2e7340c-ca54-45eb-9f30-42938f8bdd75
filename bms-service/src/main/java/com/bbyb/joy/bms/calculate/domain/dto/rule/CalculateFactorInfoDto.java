package com.bbyb.joy.bms.calculate.domain.dto.rule;

import com.bbyb.joy.bms.support.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 计算因子信息
 * 字段映射表：pub_quotation_classificationdetail
 */
@Data
public class CalculateFactorInfoDto implements Serializable {

    public static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    public Long id;

    @ApiModelProperty(value = "报价明细表id")
    @Excel(name = "报价明细表id")
    public String quoteruledetailId;

    @ApiModelProperty(value = "门店编码")
    @Excel(name = "门店编码")
    public String storeCode;

    @ApiModelProperty(value = "门店名称")
    @Excel(name = "门店名称")
    public String storeName;

    @ApiModelProperty(value = "始发省份")
    @Excel(name = "始发省份")
    public String deliverProvince;

    @ApiModelProperty(value = "始发城市")
    @Excel(name = "始发城市")
    public String deliverArea;

    @ApiModelProperty(value = "始发区县")
    @Excel(name = "始发区县")
    public String deliverCity;

    @ApiModelProperty(value = "到达省份")
    @Excel(name = "到达省份")
    public String receiveProvince;

    @ApiModelProperty(value = "到达城市")
    @Excel(name = "到达城市")
    public String receiveArea;

    @ApiModelProperty(value = "到达区县")
    @Excel(name = "到达区县")
    public String receiveCity;

    @ApiModelProperty(value = "车型")
    @Excel(name = "车型")
    public String carmodel;

    @ApiModelProperty(value = "车长")
    @Excel(name = "车长")
    public String carlong;

    @ApiModelProperty(value = "线路编码")
    @Excel(name = "线路编码")
    public String routeCode;

    @ApiModelProperty(value = "仓库名称")
    @Excel(name = "仓库名称")
    public String warehouseName;

    @ApiModelProperty(value = "单据类型")
    @Excel(name = "单据类型")
    public Integer billType;

    @ApiModelProperty(value = "温区")
    @Excel(name = "温区")
    public String temperatureZone;

    @ApiModelProperty(value = "规格")
    @Excel(name = "规格")
    public String specifications;

    @ApiModelProperty(value = "品类")
    @Excel(name = "品类")
    public String category;

    @ApiModelProperty(value = "是否拆零0否1是")
    @Excel(name = "是否拆零0否1是")
    public String isRemovezero;

    @ApiModelProperty(value = "业务类型1城市配送2城际干线3城市仓储4仓配一体，取缓存")
    @Excel(name = "业务类型1城市配送2城际干线3城市仓储4仓配一体，取缓存")
    public Integer businessType;

    @ApiModelProperty(value = "最低收费")
    @Excel(name = "最低收费")
    public BigDecimal minimumCharge;

    @ApiModelProperty(value = "单价")
    @Excel(name = "单价")
    public BigDecimal unitPrice;

    @ApiModelProperty(value = "步长")
    @Excel(name = "步长")
    public BigDecimal step;

    @ApiModelProperty(value = "单数阶梯1")
    @Excel(name = "单数阶梯1")
    public BigDecimal singularLadder1;

    @ApiModelProperty(value = "单数阶梯2")
    @Excel(name = "单数阶梯2")
    public BigDecimal singularLadder2;

    @ApiModelProperty(value = "单数阶梯3")
    @Excel(name = "单数阶梯3")
    public BigDecimal singularLadder3;

    @ApiModelProperty(value = "单数阶梯4")
    @Excel(name = "单数阶梯4")
    public BigDecimal singularLadder4;

    @ApiModelProperty(value = "单数阶梯5")
    @Excel(name = "单数阶梯5")
    public BigDecimal singularLadder5;

    @ApiModelProperty(value = "单数阶梯6")
    @Excel(name = "单数阶梯6")
    public BigDecimal singularLadder6;

    @ApiModelProperty(value = "箱数阶梯1")
    @Excel(name = "箱数阶梯1")
    public BigDecimal boxLadder1;

    @ApiModelProperty(value = "箱数阶梯2")
    @Excel(name = "箱数阶梯2")
    public BigDecimal boxLadder2;

    @ApiModelProperty(value = "箱数阶梯3")
    @Excel(name = "箱数阶梯3")
    public BigDecimal boxLadder3;

    @ApiModelProperty(value = "箱数阶梯4")
    @Excel(name = "箱数阶梯4")
    public BigDecimal boxLadder4;

    @ApiModelProperty(value = "箱数阶梯5")
    @Excel(name = "箱数阶梯5")
    public BigDecimal boxLadder5;

    @ApiModelProperty(value = "箱数阶梯6")
    @Excel(name = "箱数阶梯6")
    public BigDecimal boxLadder6;

    @ApiModelProperty(value = "重量阶梯1")
    @Excel(name = "重量阶梯1")
    public BigDecimal weightLadder1;

    @ApiModelProperty(value = "重量阶梯2")
    @Excel(name = "重量阶梯2")
    public BigDecimal weightLadder2;

    @ApiModelProperty(value = "重量阶梯3")
    @Excel(name = "重量阶梯3")
    public BigDecimal weightLadder3;

    @ApiModelProperty(value = "重量阶梯4")
    @Excel(name = "重量阶梯4")
    public BigDecimal weightLadder4;

    @ApiModelProperty(value = "重量阶梯5")
    @Excel(name = "重量阶梯5")
    public BigDecimal weightLadder5;

    @ApiModelProperty(value = "重量阶梯6")
    @Excel(name = "重量阶梯6")
    public BigDecimal weightLadder6;

    @ApiModelProperty(value = "体积阶梯1")
    @Excel(name = "体积阶梯1")
    public BigDecimal volumeLadder1;

    @ApiModelProperty(value = "体积阶梯2")
    @Excel(name = "体积阶梯2")
    public BigDecimal volumeLadder2;

    @ApiModelProperty(value = "体积阶梯3")
    @Excel(name = "体积阶梯3")
    public BigDecimal volumeLadder3;

    @ApiModelProperty(value = "体积阶梯4")
    @Excel(name = "体积阶梯4")
    public BigDecimal volumeLadder4;

    @ApiModelProperty(value = "体积阶梯5")
    @Excel(name = "体积阶梯5")
    public BigDecimal volumeLadder5;

    @ApiModelProperty(value = "体积阶梯6")
    @Excel(name = "体积阶梯6")
    public BigDecimal volumeLadder6;

    @ApiModelProperty(value = "公里阶梯1")
    @Excel(name = "公里阶梯1")
    public BigDecimal kilometreLadder1;

    @ApiModelProperty(value = "公里阶梯2")
    @Excel(name = "公里阶梯2")
    public BigDecimal kilometreLadder2;

    @ApiModelProperty(value = "公里阶梯3")
    @Excel(name = "公里阶梯3")
    public BigDecimal kilometreLadder3;

    @ApiModelProperty(value = "公里阶梯4")
    @Excel(name = "公里阶梯4")
    public BigDecimal kilometreLadder4;

    @ApiModelProperty(value = "公里阶梯5")
    @Excel(name = "公里阶梯5")
    public BigDecimal kilometreLadder5;

    @ApiModelProperty(value = "公里阶梯6")
    @Excel(name = "公里阶梯6")
    public BigDecimal kilometreLadder6;

    @ApiModelProperty(value = "阶梯1单价")
    @Excel(name = "阶梯1单价")
    public BigDecimal ladderPrice1;

    @ApiModelProperty(value = "阶梯2单价")
    @Excel(name = "阶梯2单价")
    public BigDecimal ladderPrice2;

    @ApiModelProperty(value = "阶梯3单价")
    @Excel(name = "阶梯3单价")
    public BigDecimal ladderPrice3;

    @ApiModelProperty(value = "阶梯4单价")
    @Excel(name = "阶梯4单价")
    public BigDecimal ladderPrice4;

    @ApiModelProperty(value = "阶梯5单价")
    @Excel(name = "阶梯5单价")
    public BigDecimal ladderPrice5;

    @ApiModelProperty(value = "阶梯6单价")
    @Excel(name = "阶梯6单价")
    public BigDecimal ladderPrice6;

    @ApiModelProperty(value = "百分比")
    @Excel(name = "百分比")
    public BigDecimal percentage;

    @ApiModelProperty(value = "托数阶梯1")
    @Excel(name = "托数阶梯1")
    public BigDecimal tornumLadder1;

    @ApiModelProperty(value = "托数阶梯2")
    @Excel(name = "托数阶梯2")
    public BigDecimal tornumLadder2;

    @ApiModelProperty(value = "托数阶梯3")
    @Excel(name = "托数阶梯3")
    public BigDecimal tornumLadder3;

    @ApiModelProperty(value = "托数阶梯4")
    @Excel(name = "托数阶梯4")
    public BigDecimal tornumLadder4;

    @ApiModelProperty(value = "托数阶梯5")
    @Excel(name = "托数阶梯5")
    public BigDecimal tornumLadder5;

    @ApiModelProperty(value = "托数阶梯6")
    @Excel(name = "托数阶梯6")
    public BigDecimal tornumLadder6;

    @ApiModelProperty(value = "件数阶梯1")
    @Excel(name = "件数阶梯1")
    public String numberLadder1;

    @ApiModelProperty(value = "件数阶梯2")
    @Excel(name = "件数阶梯2")
    public String numberLadder2;

    @ApiModelProperty(value = "件数阶梯3")
    @Excel(name = "件数阶梯3")
    public String numberLadder3;

    @ApiModelProperty(value = "件数阶梯4")
    @Excel(name = "件数阶梯4")
    public String numberLadder4;

    @ApiModelProperty(value = "件数阶梯5")
    @Excel(name = "件数阶梯5")
    public String numberLadder5;

    @ApiModelProperty(value = "件数阶梯6")
    @Excel(name = "件数阶梯6")
    public String numberLadder6;

    @ApiModelProperty(value = "基础数据")
    @Excel(name = "基础数据")
    public Integer baseQuantity;

    @ApiModelProperty(value = "品类1")
    @Excel(name = "品类1")
    public String category1;

    @ApiModelProperty(value = "品类2")
    @Excel(name = "品类2")
    public String category2;

    @ApiModelProperty(value = "品类3")
    @Excel(name = "品类3")
    public String category3;

    @ApiModelProperty(value = "品类4")
    @Excel(name = "品类4")
    public String category4;

    @ApiModelProperty(value = "品类5")
    @Excel(name = "品类5")
    public String category5;

    @ApiModelProperty(value = "品类6")
    @Excel(name = "品类6")
    public String category6;

    /**
     * 增值费用类型
     */
    public Integer itemId;


    public String itemIdName;

    public String itemId1;

    public String itemId1Name;

    @ApiModelProperty(name = "计费单位", notes = "")
    public Integer costUnit;

    @ApiModelProperty(name = "计费单位", notes = "")
    public String costUnitName;

}
