package com.bbyb.joy.bms.calculate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bbyb.bms.model.po.*;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.calculate.component.OrderConvertComponent;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderExpandDto;
import com.bbyb.joy.bms.calculate.domain.model.CalculateConfigModel;
import com.bbyb.joy.bms.calculate.domain.model.CalculateFactorInfoModel;
import com.bbyb.joy.bms.calculate.domain.param.CalculateDbParam;
import com.bbyb.joy.bms.calculate.domain.param.CalculateDebuggerParam;
import com.bbyb.joy.bms.calculate.domain.param.CalculateRuleQueryParam;
import com.bbyb.joy.bms.calculate.domain.result.CalculateResult;
import com.bbyb.joy.bms.calculate.domain.result.CalculateYfDbResult;
import com.bbyb.joy.bms.calculate.domain.result.CalculateYsDbResult;
import com.bbyb.joy.bms.calculate.service.*;
import com.bbyb.joy.bms.code.domain.dto.*;
import com.bbyb.joy.bms.code.domain.param.BmsDispatchCodeInfoParam;
import com.bbyb.joy.bms.code.domain.param.BmsTransCodeInfoParam;
import com.bbyb.joy.bms.code.domain.param.BmsYfStorageCodeInfoParam;
import com.bbyb.joy.bms.code.domain.param.BmsYsStorageCodeInfoParam;
import com.bbyb.joy.bms.code.service.BmsCodeService;
import com.bbyb.joy.bms.cost.domain.dto.BmsYfMainFeeInfoDto;
import com.bbyb.joy.bms.cost.domain.dto.BmsYsMainFeeInfoDto;
import com.bbyb.joy.bms.domain.enums.AutoCalculateModuleEnum;
import com.bbyb.joy.bms.domain.enums.AutoStateEnum;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import com.bbyb.joy.bms.support.datesource.BmsDs;
import com.bbyb.joy.bms.support.exception.BusinessException;
import com.bbyb.joy.bms.support.utils.ValidationUtil;
import com.bbyb.joy.core.annotation.DataSourceSwitchAnnotation;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.utils.utils.SnowFlowUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CalculateServiceImpl implements CalculateService {

    @Resource
    CalculateDimensionService dimensionService;
    @Resource
    OrderConvertComponent convertComponent;
    @Resource
    BmsCodeService codeService;
    @Resource
    CalculateRuleService ruleService;
    @Resource
    CalculateConfigService configService;
    @Resource
    CalculateAfterService afterService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public PubAutomaticLogImplementPO lock(AutoCalculateModuleEnum moduleEnum) {

        UserBean userInfo = RequestContext.getUserInfo();
        String gid = SnowFlowUtils.nextId();

        PubAutomaticLogImplementPO buildData = new PubAutomaticLogImplementPO();
        buildData.setModule(moduleEnum.getModule());
        buildData.setAutossate(AutoStateEnum.EXECETING.getValue());
        buildData.setOperator(userInfo.getEmployeename());
        buildData.setStarttime(new Date());
        buildData.setGid(gid);

        BmsDs.instance().WMSMybatis().mapper().insertSelective(buildData);

        return buildData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unlock(PubAutomaticLogImplementPO param) {

        PubAutomaticLogImplementPOExample editExample = new PubAutomaticLogImplementPOExample();
        editExample.createCriteria()
                .andGidEqualTo(param.getGid());
        PubAutomaticLogImplementPO editData = new PubAutomaticLogImplementPO();
        editData.setAutossate(AutoStateEnum.EXECETED.getValue());
        editData.setEndtime(new Date());

        BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(editData, editExample);
    }

    @Override
    public boolean checkLock(AutoCalculateModuleEnum moduleEnum) {

        PubAutomaticLogImplementPOExample queryExample = new PubAutomaticLogImplementPOExample();
        queryExample.createCriteria()
                .andAutossateEqualTo(AutoStateEnum.EXECETING.getValue())
                .andModuleEqualTo(moduleEnum.getModule());

        PubAutomaticLogImplementPO automaticTask = BmsDs.instance().WMSMybatis().mapper().selectFirstByExample(queryExample);

        return automaticTask != null;
    }


    @Override
    public CalculateYsDbResult calculate(List<CalculateOrderExpandDto> codes, List<CalculateFactorInfoModel> ruleLisParam, CalculateDebuggerParam debuggerParam) {

        List<CalculateResult> orderResult = new ArrayList<>();
        List<CalculateResult> dayResult = new ArrayList<>();
        List<CalculateResult> monthResult = new ArrayList<>();

        for (CalculateFactorInfoModel factorModel : ruleLisParam) {

            switch (factorModel.getRuleInfo().getConsolidationRule()){
                case 1:
                    List<CalculateResult> calculateOrderResults = dimensionService.calculateOrder(codes, factorModel);
                    orderResult.addAll(calculateOrderResults);
                    break;
                case 3:
                    List<CalculateResult> calculateDayResults = dimensionService.calculateDay(codes, factorModel);
                    dayResult.addAll(calculateDayResults);
                    break;
                case 4:
                    List<CalculateResult> calculateMonthResults = dimensionService.calculateMonth(codes, factorModel);
                    monthResult.addAll(calculateMonthResults);
                    break;
            }
        }

        CalculateDbParam buildDbData = CalculateDbParam.builder()
                .orderResult(orderResult)
                .dayResult(dayResult)
                .monthResult(monthResult)
                .build();
        return afterService.ysDbCalculate(buildDbData);
    }

    @Override
    @DataSourceSwitchAnnotation
    public void transCalculate(BmsTransCodeInfoParam param, CalculateDebuggerParam debuggerParam) {

        log.info("运输自动计费-开始");
        log.info("运输自动计费-入参: {}", JSONUtil.toJsonStr(param));

        String checkMsg = ValidationUtil.validate(param);
        if(StrUtil.isNotEmpty(checkMsg)){
            String errMsg = StrUtil.format("运输自动计费-ERROR_MESSAGE:{}", checkMsg);
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }

        log.info("运输自动计费-获取自动计费配置");
        CalculateConfigModel config = configService.config();
        log.info(config.toString());
        log.info("运输自动计费-获取自动计费配置结束");

        param.setLoadDetailFlag(Boolean.FALSE);
        param.setCostStatus(Lists.newArrayList(BmsConstants.COST_STATUS_0,BmsConstants.COST_STATUS_3));
        List<BmsTransCodeInfoDto> codes = codeService.queryTransList(param);

        if(CollUtil.isEmpty(codes)){
            String errMsg = StrUtil.format("运输自动计费-ERROR_MESSAGE:{}",ServiceError.CALCULATE_NO_CODE.getMessage());
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }
        log.info("运输自动计费-自动计费查询relationId结束,查询条数:{}", codes.size());

        List<Integer> releationIds = codes.stream()
                .map(BmsTransCodeInfoDto::getClientId)
                .distinct()
                .collect(Collectors.toList());

        if(CollUtil.isEmpty(releationIds)){
            String errMsg = StrUtil.format("运输自动计费-ERROR_MESSAGE:{}",ServiceError.CALCULATE_NO_CODE.getMessage());
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }
        log.info("运输自动计费-自动计费查询relationId条数:{}", releationIds.size());

        //内存释放(这里如果按月查询数据量会很大,提前释放)
        codes.clear();

        //报价相关信息
        CalculateRuleQueryParam ruleQueryParam = CalculateRuleQueryParam.builder()
                .relationIds(releationIds)
                .businessTypes(Lists.newArrayList(BmsConstants.BUSINESS_TYPE_1, BmsConstants.BUSINESS_TYPE_3))
                .ruleType(BmsConstants.RULE_TYPE_1)
                .startDate(param.getStartDate())
                .endDate(param.getEndDate())
                .codeTypes(Lists.newArrayList(BmsConstants.CODE_TYPE_1))
                .build();
        if(StrUtil.isNotEmpty(debuggerParam.getRuleCode())){
            ruleQueryParam.setRuleCode(debuggerParam.getRuleCode());
        }
        Pair<String, Map<Integer, List<CalculateFactorInfoModel>>> ruleResult = ruleService.queryValidResult(ruleQueryParam);
        if(StrUtil.isNotEmpty(ruleResult.getKey())){
            String errMsg = StrUtil.format("运输自动计费-ERROR_MESSAGE:{}",ruleResult.getKey());
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }
        Map<Integer, List<CalculateFactorInfoModel>> ruleMap = ruleResult.getValue();

        //关联维度的总任务数
        int relationTask = ruleMap.size();

        ThreadPoolExecutor relationExector = ExecutorBuilder.create()
                .setCorePoolSize(config.getRelationTaskMin())
                .setMaxPoolSize(config.getRelationTaskMax())
                .setWorkQueue(new LinkedBlockingQueue<>(relationTask))
                .build();

        // 获取当前请求上下文
        RequestAttributes currentRequestAttributes = RequestContextHolder.getRequestAttributes();

        for (Integer releationId : releationIds) {
            if(!ruleMap.containsKey(releationId)){
                continue;
            }
            param.setClientId(releationId);

            BmsTransCodeInfoParam initParam = BeanUtil.toBean(param, BmsTransCodeInfoParam.class);
            List<CalculateFactorInfoModel> initRuleParam = BeanUtil.copyToList(ruleMap.get(releationId),CalculateFactorInfoModel.class);
            CalculateDebuggerParam initDebuggerParam = BeanUtil.toBean(debuggerParam, CalculateDebuggerParam.class);
            relationExector.execute(()->{
                try {
                    // 在线程中设置请求上下文
                    RequestContextHolder.setRequestAttributes(currentRequestAttributes);
                    relationTransDimensionTask(initParam, initDebuggerParam,initRuleParam);
                } finally {
                    // 清理请求上下文
                    RequestContextHolder.resetRequestAttributes();
                }
            });
        }

    }


    /**
     * 关联ID维度(运输)
     * @param param 单据查询参数
     * @param debuggerParam 调试参数
     * @param ruleLisParam 关联ID维度的所有报价合同
     * TODO 客户报价&&客户订单信息
     */
    public void relationTransDimensionTask(BmsTransCodeInfoParam param
            , CalculateDebuggerParam debuggerParam
            , List<CalculateFactorInfoModel> ruleLisParam){

        //单据信息(转换)
        param.setLoadDetailFlag(Boolean.TRUE);
        param.setLoadConversionFlag(Boolean.TRUE);
        List<BmsTransCodeInfoDto> oriCodes = codeService.queryTransList(param);
        List<BmsTransCodeDetailInfoDto> oriDetails = oriCodes.stream()
                .map(BmsTransCodeInfoDto::getDetails)
                .filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .toList();

        log.info("单据转换开始:{}条数",oriDetails.size());
        List<CalculateOrderExpandDto> codes = convertComponent.builder()
                .initCodeType(BmsConstants.MIDDLE_CODE_TYPE_01)
                .initHandlerType(param.getHandlerType())
                .initCodes(oriCodes, BmsTransCodeInfoDto.class)
                .initDetail(oriDetails, BmsTransCodeDetailInfoDto.class)
                .build();
        log.info("单据转换结束");

        List<CalculateResult> orderResult = new ArrayList<>();
        List<CalculateResult> dayResult = new ArrayList<>();
        List<CalculateResult> monthResult = new ArrayList<>();

        for (CalculateFactorInfoModel factorModel : ruleLisParam) {

            switch (factorModel.getRuleInfo().getConsolidationRule()){
                case 1:
                    List<CalculateResult> calculateOrderResults = dimensionService.calculateOrder(codes, factorModel);
                    orderResult.addAll(calculateOrderResults);
                    break;
                case 3:
                    List<CalculateResult> calculateDayResults = dimensionService.calculateDay(codes, factorModel);
                    dayResult.addAll(calculateDayResults);
                    break;
                case 4:
                    List<CalculateResult> calculateMonthResults = dimensionService.calculateMonth(codes, factorModel);
                    monthResult.addAll(calculateMonthResults);
                    break;
            }

        }

        CalculateDbParam buildDbData = CalculateDbParam.builder()
                .relationId(param.getClientId())
                .orderResult(orderResult)
                .dayResult(dayResult)
                .monthResult(monthResult)
                .build();

        CalculateYsDbResult dbResult = afterService.ysDbCalculate(buildDbData);

        List<Integer> codePkIds = dbResult.getCodePkIds();
        List<Integer> successCodePkIds = dbResult.getSuccessCodePkIds();
        List<Integer> failCodePkIds = dbResult.getFailCodePkIds();
        List<BmsYsCostRecordPO> insertCostRecordPOs = dbResult.getInsertCostRecordPOs();
        List<BmsYscostMainInfoPO> insertCostMainInfoPOs = dbResult.getInsertCostMainInfoPOs();
        List<BmsYscostInfoPO> insertCostInfoPOs = dbResult.getInsertCostInfoPOs();
        List<BmsYsexpensesMiddlePO> insertMiddlePOs = dbResult.getInsertMiddlePOs();

        UserBean userInfo = RequestContext.getUserInfo();

        /*
         * db操作流程
         * 主要要按照该顺序操作
         * 1、清理订单对应的计费记录(计费失败的会有计费记录)
         * 2、更新订单状态
         * 3、新增费用主
         * 4、新增结算费(新增前将主费用的pk_id刷入)
         * 5、分摊表(新增前将主费用的pk_id刷入)
         * 6、插入订单计费记录(新增前将主费用的pk_id刷入)
         * 7、费用数据校准
         */
        if(CollUtil.isNotEmpty(codePkIds)){

            BmsYsCostRecordPOExample delRecordQueryParam = new BmsYsCostRecordPOExample();
            delRecordQueryParam.createCriteria()
                    .andCodeTypeEqualTo(BmsConstants.MIDDLE_CODE_TYPE_01)
                    .andCodePkIdIn(codePkIds);
            BmsYsCostRecordPO delRecordData = new BmsYsCostRecordPO();
            delRecordData.setDelFlag(BmsConstants.DEL_FLAG_1);
            delRecordData.setOperCode(userInfo.getEmployeecode().toString());
            delRecordData.setOperBy(userInfo.getEmployeename());
            delRecordData.setOperTime(new Date());
            BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(delRecordData, delRecordQueryParam);
        }

        if(CollUtil.isNotEmpty(successCodePkIds)){

            BmsTransCodeInfoPOExample editSuccessQueryParam = new BmsTransCodeInfoPOExample();
            editSuccessQueryParam.createCriteria()
                    .andPkIdIn(successCodePkIds);
            BmsTransCodeInfoPO editSuccessData = new BmsTransCodeInfoPO();
            editSuccessData.setCostStatus(BmsConstants.COST_STATUS_1);
            BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(editSuccessData, editSuccessQueryParam);
        }

        if(CollUtil.isNotEmpty(failCodePkIds)){

            BmsTransCodeInfoPOExample editFailQueryParam = new BmsTransCodeInfoPOExample();
            editFailQueryParam.createCriteria()
                    .andPkIdIn(failCodePkIds);
            BmsTransCodeInfoPO editFailQueryData = new BmsTransCodeInfoPO();
            editFailQueryData.setCostStatus(BmsConstants.COST_STATUS_3);
            BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(editFailQueryData, editFailQueryParam);
        }

        //费用DB
        if(CollUtil.isNotEmpty(insertCostMainInfoPOs)){
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertCostMainInfoPOs);
            if(CollUtil.isNotEmpty(insertCostMainInfoPOs)){
                //key:id,value:fee
                Map<String, BmsYscostMainInfoPO> mainCostMap = insertCostMainInfoPOs.stream()
                        .collect(Collectors.toMap(BmsYscostMainInfoPO::getId, Function.identity(), (v1, v2) -> v2));

                for (BmsYscostInfoPO insertCostInfoPO : insertCostInfoPOs) {
                    if(mainCostMap.containsKey(insertCostInfoPO.getMainCodeId())){
                        insertCostInfoPO.setMainPkId(mainCostMap.get(insertCostInfoPO.getMainCodeId()).getPkId());
                    }
                }
                for (BmsYsexpensesMiddlePO insertMiddlePO : insertMiddlePOs) {
                    if(mainCostMap.containsKey(insertMiddlePO.getMainCodeId())){
                        insertMiddlePO.setMainPkId(mainCostMap.get(insertMiddlePO.getMainCodeId()).getPkId());
                    }
                }
                BmsDs.instance().WMSMybatis().mapper().batchInsert(insertCostInfoPOs);
                BmsDs.instance().WMSMybatis().mapper().batchInsert(insertMiddlePOs);
            }
        }

        // 计费记录
        if(CollUtil.isNotEmpty(insertCostRecordPOs)){
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertCostRecordPOs);
        }

        // 费用数据校准
        if(CollUtil.isNotEmpty(codePkIds)){
            afterService.calculateDataDalibration(param.getHandlerType(),codePkIds);
        }

    }









    @Override
    @DataSourceSwitchAnnotation
    public void ysStorageCalculate(BmsYsStorageCodeInfoParam param, CalculateDebuggerParam debuggerParam) {

        log.info("应收仓储自动计费-开始");
        log.info("应收仓储自动计费-入参: {}", JSONUtil.toJsonStr(param));

        String checkMsg = ValidationUtil.validate(param);
        if(StrUtil.isNotEmpty(checkMsg)){
            log.info("应收仓储自动计费-ERROR_MESSAGE:{}",checkMsg);
            throw new BusinessException(StrUtil.format("应收仓储自动计费-ERROR_MESSAGE:{}",checkMsg));
        }

        log.info("应收仓储自动计费-获取自动计费配置");
        CalculateConfigModel config = configService.config();
        log.info(config.toString());
        log.info("应收仓储自动计费-获取自动计费配置结束");

        param.setLoadDetailFlag(Boolean.FALSE);
        param.setLoadConversionFlag(Boolean.FALSE);
        param.setLoadFeeFlag(Boolean.FALSE);
        param.setCostStatus(List.of(BmsConstants.COST_STATUS_0,BmsConstants.COST_STATUS_3));
        List<BmsStorageCodeInfoDto<BmsYsMainFeeInfoDto>> codes = codeService.queryYsStorageList(param);

        if(CollUtil.isEmpty(codes)){
            String errMsg = StrUtil.format("应收仓储自动计费-ERROR_MESSAGE:{}", ServiceError.CALCULATE_NO_CODE.getMessage());
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }
        log.info("应收仓储自动计费-自动计费查询relationId结束,查询条数:{}", codes.size());

        List<Integer> releationIds = codes.stream()
                .map(BmsStorageCodeInfoDto::getClientId)
                .distinct()
                .collect(Collectors.toList());

        if(CollUtil.isEmpty(releationIds)){
            String errMsg = StrUtil.format("应收仓储自动计费-ERROR_MESSAGE:{}", ServiceError.CALCULATE_NO_CODE.getMessage());
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }
        log.info("应收仓储自动计费-自动计费查询relationId条数:{}", releationIds.size());

        //内存释放(这里如果按月查询数据量会很大,提前释放)
        codes.clear();

        //报价相关信息
        CalculateRuleQueryParam ruleQueryParam = CalculateRuleQueryParam.builder()
                .relationIds(releationIds)
                .ruleType(BmsConstants.RULE_TYPE_1)
                .businessTypes(List.of(BmsConstants.BUSINESS_TYPE_2,BmsConstants.BUSINESS_TYPE_3))
                .startDate(param.getStartDate())
                .endDate(param.getEndDate())
                .codeTypes(param.getCodeTypes())
                .build();
        if(StrUtil.isNotEmpty(debuggerParam.getRuleCode())){
            ruleQueryParam.setRuleCode(debuggerParam.getRuleCode());
        }
        Pair<String, Map<Integer, List<CalculateFactorInfoModel>>> ruleResult = ruleService.queryValidResult(ruleQueryParam);
        if(StrUtil.isNotEmpty(ruleResult.getKey())){
            String errMsg = StrUtil.format("应收仓储自动计费-ERROR_MESSAGE:{}", ruleResult.getKey());
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }
        Map<Integer, List<CalculateFactorInfoModel>> ruleMap = ruleResult.getValue();

        //关联维度的总任务数
        int relationTask = releationIds.size();

        ThreadPoolExecutor relationExector = ExecutorBuilder.create()
                .setCorePoolSize(config.getRelationTaskMin())
                .setMaxPoolSize(config.getRelationTaskMax())
                .setWorkQueue(new LinkedBlockingQueue<>(relationTask))
                .build();

        // 获取当前请求上下文
        RequestAttributes currentRequestAttributes = RequestContextHolder.getRequestAttributes();

        for (Integer releationId : releationIds) {
            if(!ruleMap.containsKey(releationId)){
                continue;
            }
            param.setClientId(releationId);
            BmsYsStorageCodeInfoParam initParam = BeanUtil.toBean(param, BmsYsStorageCodeInfoParam.class);
            List<CalculateFactorInfoModel> initRuleParam = BeanUtil.copyToList(ruleMap.get(releationId), CalculateFactorInfoModel.class);
            CalculateDebuggerParam initDebuggerParam = BeanUtil.toBean(debuggerParam, CalculateDebuggerParam.class);

            relationExector.execute(()->{
                try {
                    // 在线程中设置请求上下文
                    RequestContextHolder.setRequestAttributes(currentRequestAttributes);

                    relationYsStorageDimensionTask(initParam, initDebuggerParam,initRuleParam);
                } finally {
                    // 清理请求上下文
                    RequestContextHolder.resetRequestAttributes();
                }
            });
        }

    }


    /**
     * 关联ID纬度(应收-仓储)
     */
    @SuppressWarnings("rawtypes")
    public void relationYsStorageDimensionTask(BmsYsStorageCodeInfoParam param
            , CalculateDebuggerParam debuggerParam
            , List<CalculateFactorInfoModel> ruleLisParam){

        //单据信息(转换)
        param.setLoadDetailFlag(Boolean.TRUE);
        param.setLoadDetailFlag(Boolean.TRUE);
        List<BmsStorageCodeInfoDto<BmsYsMainFeeInfoDto>> oriCodes = codeService.queryYsStorageList(param);
        List<BmsStorageCodeDetailInfoDto> oriDetail = oriCodes.stream().map(BmsStorageCodeInfoDto::getDetails)
                .filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .toList();

        List<BmsStorageCodeInfoDto> convertList = BeanUtil.copyToList(oriCodes, BmsStorageCodeInfoDto.class);
        List<CalculateOrderExpandDto> codes = convertComponent.builder()
                .initCodeType(BmsConstants.MIDDLE_CODE_TYPE_02)
                .initHandlerType(param.getHandlerType())
                .initCodes(convertList, BmsStorageCodeInfoDto.class)
                .initDetail(oriDetail, BmsStorageCodeDetailInfoDto.class)
                .build();

        List<CalculateResult> orderResult = new ArrayList<>();
        List<CalculateResult> dayResult = new ArrayList<>();
        List<CalculateResult> monthResult = new ArrayList<>();

        for (CalculateFactorInfoModel factorModel : ruleLisParam) {

            switch (factorModel.getRuleInfo().getConsolidationRule()){
                case 1:
                    List<CalculateResult> calculateOrderResults = dimensionService.calculateOrder(codes, factorModel);
                    orderResult.addAll(calculateOrderResults);
                    break;
                case 3:
                    List<CalculateResult> calculateDayResults = dimensionService.calculateDay(codes, factorModel);
                    dayResult.addAll(calculateDayResults);
                    break;
                case 4:
                    List<CalculateResult> calculateMonthResults = dimensionService.calculateMonth(codes, factorModel);
                    monthResult.addAll(calculateMonthResults);
                    break;
            }

        }

        CalculateDbParam buildDbData = CalculateDbParam.builder()
                .relationId(param.getClientId())
                .orderResult(orderResult)
                .dayResult(dayResult)
                .monthResult(monthResult)
                .build();

        CalculateYsDbResult dbResult = afterService.ysDbCalculate(buildDbData);


        List<Integer> codePkIds = dbResult.getCodePkIds();
        List<Integer> successCodePkIds = dbResult.getSuccessCodePkIds();
        List<Integer> failCodePkIds = dbResult.getFailCodePkIds();
        List<BmsYsCostRecordPO> insertCostRecordPOs = dbResult.getInsertCostRecordPOs();
        List<BmsYscostMainInfoPO> insertCostMainInfoPOs = dbResult.getInsertCostMainInfoPOs();
        List<BmsYscostInfoPO> insertCostInfoPOs = dbResult.getInsertCostInfoPOs();
        List<BmsYsexpensesMiddlePO> insertMiddlePOs = dbResult.getInsertMiddlePOs();

        UserBean userInfo = RequestContext.getUserInfo();

        /*
         * db操作流程
         * 1、清理订单对应的计费记录(计费失败的会有计费记录)
         * 2、更新订单状态
         * 3、新增费用主
         * 4、新增结算费(新增前将主费用的pk_id刷入)
         * 5、分摊表(新增前将主费用的pk_id刷入)
         * 6、插入订单计费记录(新增前将主费用的pk_id刷入)
         * 7、费用数据校准
         */
        if(CollUtil.isNotEmpty(codePkIds)){
            BmsYsCostRecordPOExample delRecordQueryParam = new BmsYsCostRecordPOExample();
            delRecordQueryParam.createCriteria()
                    .andCodeTypeEqualTo(BmsConstants.MIDDLE_CODE_TYPE_02)
                    .andCodePkIdIn(codePkIds);
            BmsYsCostRecordPO delRecordData = new BmsYsCostRecordPO();
            delRecordData.setDelFlag(BmsConstants.DEL_FLAG_1);
            delRecordData.setOperCode(userInfo.getEmployeecode().toString());
            delRecordData.setOperBy(userInfo.getEmployeename());
            delRecordData.setOperTime(new Date());
            BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(delRecordData, delRecordQueryParam);
        }

        if(CollUtil.isNotEmpty(successCodePkIds)){
            BmsStorageCodeInfoPOExample editSuccessQueryParam = new BmsStorageCodeInfoPOExample();
            editSuccessQueryParam.createCriteria()
                    .andPkIdIn(successCodePkIds);
            BmsStorageCodeInfoPO editSuccessData = new BmsStorageCodeInfoPO();
            editSuccessData.setYsCostStatus(BmsConstants.COST_STATUS_1);
            BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(editSuccessData, editSuccessQueryParam);
        }

        if(CollUtil.isNotEmpty(failCodePkIds)){
            BmsStorageCodeInfoPOExample editFailQueryParam = new BmsStorageCodeInfoPOExample();
            editFailQueryParam.createCriteria()
                    .andPkIdIn(failCodePkIds);
            BmsStorageCodeInfoPO editFailQueryData = new BmsStorageCodeInfoPO();
            editFailQueryData.setYsCostStatus(BmsConstants.COST_STATUS_3);
            BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(editFailQueryData, editFailQueryParam);
        }

        //费用DB
        if(CollUtil.isNotEmpty(insertCostInfoPOs)){
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertCostMainInfoPOs);
            if(CollUtil.isNotEmpty(insertCostMainInfoPOs)){
                //key:id,value:fee
                Map<String, BmsYscostMainInfoPO> mainCostMap = insertCostMainInfoPOs.stream()
                        .collect(Collectors.toMap(BmsYscostMainInfoPO::getId, Function.identity(), (v1, v2) -> v2));

                for (BmsYscostInfoPO insertCostInfoPO : insertCostInfoPOs) {
                    if(mainCostMap.containsKey(insertCostInfoPO.getMainCodeId())){
                        insertCostInfoPO.setMainPkId(mainCostMap.get(insertCostInfoPO.getMainCodeId()).getPkId());
                    }
                }
                for (BmsYsexpensesMiddlePO insertMiddlePO : insertMiddlePOs) {
                    if(mainCostMap.containsKey(insertMiddlePO.getMainCodeId())){
                        insertMiddlePO.setMainPkId(mainCostMap.get(insertMiddlePO.getMainCodeId()).getPkId());
                    }
                }
                for (BmsYsexpensesMiddlePO insertMiddlePO : insertMiddlePOs) {
                    if(mainCostMap.containsKey(insertMiddlePO.getMainCodeId())){
                        insertMiddlePO.setMainPkId(mainCostMap.get(insertMiddlePO.getMainCodeId()).getPkId());
                    }
                }
            }
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertCostInfoPOs);
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertMiddlePOs);
        }

        // 消费记录
        if(CollUtil.isNotEmpty(insertCostRecordPOs)){
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertCostRecordPOs);
        }
        // 费用数据校准
        if(CollUtil.isNotEmpty(codePkIds)){
            afterService.calculateDataDalibration(param.getHandlerType(),codePkIds);
        }

    }



    @Override
    @DataSourceSwitchAnnotation
    public void yfStorageCalculate(BmsYfStorageCodeInfoParam param, CalculateDebuggerParam debuggerParam) {

        log.info("应付仓储自动计费-开始");
        log.info("应付仓储自动计费-入参: {}", JSONUtil.toJsonStr(param));

        String checkMsg = ValidationUtil.validate(param);
        if(StrUtil.isNotEmpty(checkMsg)){
            String errMsg = StrUtil.format("应付仓储自动计费-ERROR_MESSAGE:{}", checkMsg);
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }

        log.info("应付仓储自动计费-获取自动计费配置");
        CalculateConfigModel config = configService.config();
        log.info(config.toString());
        log.info("应付仓储自动计费-获取自动计费配置结束");

        param.setLoadDetailFlag(Boolean.FALSE);
        param.setLoadConversionFlag(Boolean.FALSE);
        param.setLoadFeeFlag(Boolean.FALSE);
        param.setCostStatus(List.of(BmsConstants.COST_STATUS_0,BmsConstants.COST_STATUS_3));
        List<BmsStorageCodeInfoDto<BmsYfMainFeeInfoDto>> codes = codeService.queryYfStorageList(param);

        if(CollUtil.isEmpty(codes)){
            String errMsg = StrUtil.format("应付仓储自动计费-ERROR_MESSAGE:{}", ServiceError.CALCULATE_NO_CODE.getMessage());
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }
        log.info("应付仓储自动计费-自动计费查询relationId结束,查询条数:{}", codes.size());

        List<Integer> releationIds = codes.stream()
                .map(BmsStorageCodeInfoDto::getCarrierId)
                .distinct()
                .collect(Collectors.toList());

        if(CollUtil.isEmpty(releationIds)){
            String errMsg = StrUtil.format("应付仓储自动计费-ERROR_MESSAGE:{}", ServiceError.CALCULATE_NO_CODE.getMessage());
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }
        log.info("应付仓储自动计费-自动计费查询relationId条数:{}", releationIds.size());

        //内存释放(这里如果按月查询数据量会很大,提前释放)
        codes.clear();

        //报价相关信息
        CalculateRuleQueryParam ruleQueryParam = CalculateRuleQueryParam.builder()
                .relationIds(releationIds)
                .ruleType(BmsConstants.RULE_TYPE_2)
                .businessTypes(List.of(BmsConstants.BUSINESS_TYPE_2,BmsConstants.BUSINESS_TYPE_3))
                .startDate(param.getStartDate())
                .endDate(param.getEndDate())
                .codeTypes(param.getCodeTypes())
                .build();
        if(StrUtil.isNotEmpty(debuggerParam.getRuleCode())){
            ruleQueryParam.setRuleCode(debuggerParam.getRuleCode());
        }
        Pair<String, Map<Integer, List<CalculateFactorInfoModel>>> ruleResult = ruleService.queryValidResult(ruleQueryParam);
        if(StrUtil.isNotEmpty(ruleResult.getKey())){
            String errMsg = StrUtil.format("应付仓储自动计费-ERROR_MESSAGE:{}",ruleResult.getKey());
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }
        Map<Integer, List<CalculateFactorInfoModel>> ruleMap = ruleResult.getValue();

        //关联维度的总任务数
        int relationTask = releationIds.size();

        ThreadPoolExecutor relationExector = ExecutorBuilder.create()
                .setCorePoolSize(config.getRelationTaskMin())
                .setMaxPoolSize(config.getRelationTaskMax())
                .setWorkQueue(new LinkedBlockingQueue<>(relationTask))
                .build();

        // 获取当前请求上下文
        RequestAttributes currentRequestAttributes = RequestContextHolder.getRequestAttributes();

        for (Integer releationId : releationIds) {
            if(!ruleMap.containsKey(releationId)){
                continue;
            }
            param.setCarrierId(releationId);

            BmsYfStorageCodeInfoParam initParam = BeanUtil.toBean(param, BmsYfStorageCodeInfoParam.class);
            List<CalculateFactorInfoModel> initRuleParam = BeanUtil.copyToList(ruleMap.get(releationId), CalculateFactorInfoModel.class);
            CalculateDebuggerParam initDebuggerParam = BeanUtil.toBean(debuggerParam, CalculateDebuggerParam.class);

            relationExector.execute(()->{
                try {
                    // 在线程中设置请求上下文
                    RequestContextHolder.setRequestAttributes(currentRequestAttributes);
                    relationYfStorageDimensionTask(initParam, initDebuggerParam,initRuleParam);
                } finally {
                    // 清理请求上下文
                    RequestContextHolder.resetRequestAttributes();
                }
            });
        }

    }


    /**
     * 关联ID纬度(应付-仓储)
     */
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("rawtypes")
    public void relationYfStorageDimensionTask(BmsYfStorageCodeInfoParam param
            , CalculateDebuggerParam debuggerParam
            , List<CalculateFactorInfoModel> ruleLisParam){

        //单据信息(转换)
        param.setLoadConversionFlag(Boolean.TRUE);
        param.setLoadDetailFlag(Boolean.TRUE);
        List<BmsStorageCodeInfoDto<BmsYfMainFeeInfoDto>> oriCodes = codeService.queryYfStorageList(param);
        List<BmsStorageCodeDetailInfoDto> oriDetail = oriCodes.stream().map(BmsStorageCodeInfoDto::getDetails)
                .filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .toList();
        List<BmsStorageCodeInfoDto> convertList = BeanUtil.copyToList(oriCodes, BmsStorageCodeInfoDto.class);
        List<CalculateOrderExpandDto> codes = convertComponent.builder()
                .initCodeType(BmsConstants.MIDDLE_CODE_TYPE_02)
                .initHandlerType(param.getHandlerType())
                .initCodes(convertList, BmsStorageCodeInfoDto.class)
                .initDetail(oriDetail, BmsStorageCodeDetailInfoDto.class)
                .build();

        List<CalculateResult> orderResult = new ArrayList<>();
        List<CalculateResult> dayResult = new ArrayList<>();
        List<CalculateResult> monthResult = new ArrayList<>();

        for (CalculateFactorInfoModel factorModel : ruleLisParam) {

            switch (factorModel.getRuleInfo().getConsolidationRule()){
                case 1:
                    List<CalculateResult> calculateOrderResults = dimensionService.calculateOrder(codes, factorModel);
                    orderResult.addAll(calculateOrderResults);
                    break;
                case 3:
                    List<CalculateResult> calculateDayResults = dimensionService.calculateDay(codes, factorModel);
                    dayResult.addAll(calculateDayResults);
                    break;
                case 4:
                    List<CalculateResult> calculateMonthResults = dimensionService.calculateMonth(codes, factorModel);
                    monthResult.addAll(calculateMonthResults);
                    break;
            }
        }

        CalculateDbParam buildDbData = CalculateDbParam.builder()
                .relationId(param.getClientId())
                .orderResult(orderResult)
                .dayResult(dayResult)
                .monthResult(monthResult)
                .build();

        CalculateYfDbResult dbResult = afterService.yfDbCalculate(buildDbData);

        List<Integer> codePkIds = dbResult.getCodePkIds();
        List<Integer> successCodePkIds = dbResult.getSuccessCodePkIds();
        List<Integer> failCodePkIds = dbResult.getFailCodePkIds();
        List<BmsYfCostRecordPO> insertCostRecordPOs = dbResult.getInsertCostRecordPOs();
        List<BmsYfcostMainInfoPO> insertCostMainInfoPOs = dbResult.getInsertCostMainInfoPOs();
        List<BmsYfcostInfoPO> insertCostInfoPOs = dbResult.getInsertCostInfoPOs();
        List<BmsYfexpensesMiddlePO> insertMiddlePOs = dbResult.getInsertMiddlePOs();

        UserBean userInfo = RequestContext.getUserInfo();

        /*
         * db操作流程
         * 1、清理订单对应的计费记录(计费失败的会有计费记录)
         * 2、更新订单状态
         * 3、新增费用主
         * 4、新增结算费(新增前将主费用的pk_id刷入)
         * 5、分摊表(新增前将主费用的pk_id刷入)
         * 6、插入订单计费记录(新增前将主费用的pk_id刷入)
         * 7、费用数据校准
         */

        if(CollUtil.isNotEmpty(codePkIds)){
            BmsYfCostRecordPOExample delRecordQueryParam = new BmsYfCostRecordPOExample();
            delRecordQueryParam.createCriteria()
                    .andCodeTypeEqualTo(BmsConstants.MIDDLE_CODE_TYPE_02)
                    .andCodePkIdIn(codePkIds);
            BmsYfCostRecordPO delRecordData = new BmsYfCostRecordPO();
            delRecordData.setDelFlag(BmsConstants.DEL_FLAG_1);
            delRecordData.setOperCode(userInfo.getEmployeecode().toString());
            delRecordData.setOperBy(userInfo.getEmployeename());
            delRecordData.setOperTime(new Date());
            BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(delRecordData, delRecordQueryParam);
        }

        if(CollUtil.isNotEmpty(successCodePkIds)){
            BmsStorageCodeInfoPOExample editSuccessQueryParam = new BmsStorageCodeInfoPOExample();
            editSuccessQueryParam.createCriteria()
                    .andPkIdIn(successCodePkIds);
            BmsStorageCodeInfoPO editSuccessData = new BmsStorageCodeInfoPO();
            editSuccessData.setYfCostStatus(BmsConstants.COST_STATUS_1);
            BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(editSuccessData, editSuccessQueryParam);
        }

        if(CollUtil.isNotEmpty(failCodePkIds)){
            BmsStorageCodeInfoPOExample editFailQueryParam = new BmsStorageCodeInfoPOExample();
            editFailQueryParam.createCriteria()
                    .andPkIdIn(failCodePkIds);
            BmsStorageCodeInfoPO editFailQueryData = new BmsStorageCodeInfoPO();
            editFailQueryData.setYfCostStatus(BmsConstants.COST_STATUS_3);
            BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(editFailQueryData, editFailQueryParam);
        }

        //费用DB
        if(CollUtil.isNotEmpty(insertCostMainInfoPOs)){
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertCostMainInfoPOs);
            if(CollUtil.isNotEmpty(insertCostMainInfoPOs)){
                //key:id,value:fee
                Map<String, BmsYfcostMainInfoPO> mainCostMap = insertCostMainInfoPOs.stream()
                        .collect(Collectors.toMap(BmsYfcostMainInfoPO::getId, Function.identity(), (v1, v2) -> v2));

                for (BmsYfcostInfoPO insertCostInfoPO : insertCostInfoPOs) {
                    if(mainCostMap.containsKey(insertCostInfoPO.getMainCodeId())){
                        insertCostInfoPO.setMainPkId(mainCostMap.get(insertCostInfoPO.getMainCodeId()).getPkId());
                    }
                }
                for (BmsYfexpensesMiddlePO insertMiddlePO : insertMiddlePOs) {
                    if(mainCostMap.containsKey(insertMiddlePO.getMainCodeId())){
                        insertMiddlePO.setMainPkId(mainCostMap.get(insertMiddlePO.getMainCodeId()).getPkId());
                    }
                }
                for (BmsYfexpensesMiddlePO insertMiddlePO : insertMiddlePOs) {
                    if(mainCostMap.containsKey(insertMiddlePO.getMainCodeId())){
                        insertMiddlePO.setMainPkId(mainCostMap.get(insertMiddlePO.getMainCodeId()).getPkId());
                    }
                }

            }
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertCostInfoPOs);
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertMiddlePOs);
        }

        // 消费记录
        if(CollUtil.isNotEmpty(insertCostRecordPOs)){
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertCostRecordPOs);
        }
        // 费用数据校准
        if(CollUtil.isNotEmpty(codePkIds)){
            afterService.calculateDataDalibration(param.getHandlerType(),codePkIds);
        }

    }


    @Override
    @DataSourceSwitchAnnotation
    public void dispatchCalculate(BmsDispatchCodeInfoParam param, CalculateDebuggerParam debuggerParam) {

        log.info("调度单自动计费-开始");
        log.info("调度单自动计费-入参: {}", JSONUtil.toJsonStr(param));

        String checkMsg = ValidationUtil.validate(param);
        if (StrUtil.isNotEmpty(checkMsg)) {
            String errMsg = StrUtil.format("调度单自动计费-ERROR_MESSAGE:{}", checkMsg);
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }

        log.info("调度单自动计费-获取自动计费配置");
        CalculateConfigModel config = configService.config();
        log.info(config.toString());
        log.info("调度单自动计费-获取自动计费配置结束");

        param.setLoadDetailFlag(Boolean.FALSE);
        param.setLoadConversionFlag(Boolean.FALSE);
        param.setLoadFeeFlag(Boolean.FALSE);
        param.setCostStatus(List.of(BmsConstants.COST_STATUS_0,BmsConstants.COST_STATUS_3));
        List<BmsDispatchCodeInfoDto> codes = codeService.queryDispatchList(param);

        if (CollUtil.isEmpty(codes)) {
            String errMsg = StrUtil.format("调度单自动计费-ERROR_MESSAGE:{}", ServiceError.CALCULATE_NO_CODE.getMessage());
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }
        log.info("调度单自动计费-自动计费查询relationId结束,查询条数:{}", codes.size());

        List<Integer> releationIds = codes.stream()
                .map(BmsDispatchCodeInfoDto::getCarrierId)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(releationIds)) {
            String errMsg = StrUtil.format("调度单自动计费-ERROR_MESSAGE:{}", ServiceError.CALCULATE_NO_CODE.getMessage());
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }
        log.info("调度单自动计费-自动计费查询relationId条数:{}", releationIds.size());

        //内存释放(这里如果按月查询数据量会很大,提前释放)
        codes.clear();

        //报价相关信息
        CalculateRuleQueryParam ruleQueryParam = CalculateRuleQueryParam.builder()
                .relationIds(releationIds)
                .ruleType(BmsConstants.RULE_TYPE_2)
                .businessTypes(List.of(BmsConstants.BUSINESS_TYPE_1,BmsConstants.BUSINESS_TYPE_3))
                .startDate(param.getStartDate())
                .endDate(param.getEndDate())
                .codeTypes(List.of(BmsConstants.CODE_TYPE_1))
                .build();
        if (StrUtil.isNotEmpty(debuggerParam.getRuleCode())) {
            ruleQueryParam.setRuleCode(debuggerParam.getRuleCode());
        }
        Pair<String, Map<Integer, List<CalculateFactorInfoModel>>> ruleResult = ruleService.queryValidResult(ruleQueryParam);
        if (StrUtil.isNotEmpty(ruleResult.getKey())) {
            String errMsg = StrUtil.format("调度单自动计费-ERROR_MESSAGE:{}", ruleResult.getKey());
            log.info(errMsg);
            throw new BusinessException(errMsg);
        }
        Map<Integer, List<CalculateFactorInfoModel>> ruleMap = ruleResult.getValue();

        //关联维度的总任务数
        int relationTask = releationIds.size();

        ThreadPoolExecutor relationExector = ExecutorBuilder.create()
                .setCorePoolSize(config.getRelationTaskMin())
                .setMaxPoolSize(config.getRelationTaskMax())
                .setWorkQueue(new LinkedBlockingQueue<>(relationTask))
                .build();

        // 获取当前请求上下文
        RequestAttributes currentRequestAttributes = RequestContextHolder.getRequestAttributes();

        for (Integer releationId : releationIds) {
            if (!ruleMap.containsKey(releationId)) {
                continue;
            }
            param.setCarrierId(releationId);
            BmsDispatchCodeInfoParam initParam = BeanUtil.toBean(param, BmsDispatchCodeInfoParam.class);
            List<CalculateFactorInfoModel> initRuleParam = BeanUtil.copyToList(ruleMap.get(releationId), CalculateFactorInfoModel.class);
            CalculateDebuggerParam initDebuggerParam = BeanUtil.toBean(debuggerParam, CalculateDebuggerParam.class);
            relationExector.execute(() -> {
                try {
                    // 在线程中设置请求上下文
                    RequestContextHolder.setRequestAttributes(currentRequestAttributes);

                    relationDispatchDimensionTask(initParam, initDebuggerParam, initRuleParam);
                } finally {
                    // 清理请求上下文
                    RequestContextHolder.resetRequestAttributes();
                }
            });
        }

    }


    /**
     * 关联ID纬度(调度单)
     */
    public void relationDispatchDimensionTask(BmsDispatchCodeInfoParam param
            , CalculateDebuggerParam debuggerParam
            , List<CalculateFactorInfoModel> ruleLisParam){
        log.info("调度单自动计费-承运商【{}】-开始",param.getCarrierId().toString());
        log.info("报价分级数据:{}",JSONUtil.toJsonStr(ruleLisParam));

        //单据信息(转换)
        param.setLoadConversionFlag(Boolean.TRUE);
        param.setLoadDetailFlag(Boolean.TRUE);
        List<BmsDispatchCodeInfoDto> oriCodes = codeService.queryDispatchList(param);
        List<BmsJobCodeDetailInfoDto> oriDetail = oriCodes.stream().map(BmsDispatchCodeInfoDto::getDetails)
                .filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .toList().stream()
                .map(BmsJobCodeInfoDto::getDetails)
                .filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .toList();
        List<BmsJobCodeInfoDto> oriDetail2 = oriCodes.stream().map(BmsDispatchCodeInfoDto::getDetails)
                .filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .toList();

        log.info("调度单自动计费-承运商【{}】,该次计费条数:【{}】",param.getCarrierId().toString(),oriCodes.size());
        List<BmsDispatchCodeInfoDto> convertList = BeanUtil.copyToList(oriCodes, BmsDispatchCodeInfoDto.class);
        List<CalculateOrderExpandDto> codes = convertComponent.builder()
                .initHandlerType(param.getHandlerType())
                .initCodeType(BmsConstants.MIDDLE_CODE_TYPE_01)
                .initCodes(convertList, BmsDispatchCodeInfoDto.class)
                .initDetail(oriDetail, BmsJobCodeDetailInfoDto.class)
                .initDetail2(oriDetail2, BmsJobCodeInfoDto.class)
                .build();

        log.info("调度单自动计费-转计费模型数据,转换条数:【{}】",codes.size());

        List<CalculateResult> orderResult = new ArrayList<>();
        List<CalculateResult> dayResult = new ArrayList<>();
        List<CalculateResult> monthResult = new ArrayList<>();

        for (CalculateFactorInfoModel factorModel : ruleLisParam) {

            switch (factorModel.getRuleInfo().getConsolidationRule()){
                case 1: case 2:
                    List<CalculateResult> calculateOrderResults = dimensionService.calculateOrder(codes, factorModel);
                    orderResult.addAll(calculateOrderResults);
                    break;
                case 3:
                    List<CalculateResult> calculateDayResults = dimensionService.calculateDay(codes, factorModel);
                    dayResult.addAll(calculateDayResults);
                    break;
                case 4:
                    List<CalculateResult> calculateMonthResults = dimensionService.calculateMonth(codes, factorModel);
                    monthResult.addAll(calculateMonthResults);
                    break;
            }

        }

        log.info("调度单自动计费-groovy-匹配计费结束");
        CalculateDbParam buildDbData = CalculateDbParam.builder()
                .relationId(param.getClientId())
                .orderResult(orderResult)
                .dayResult(dayResult)
                .monthResult(monthResult)
                .build();

        CalculateYfDbResult dbResult = afterService.yfDbCalculate(buildDbData);

        log.info("调度单自动计费-费用维度数据组装结束");

        List<Integer> codePkIds = dbResult.getCodePkIds();
        List<Integer> successCodePkIds = dbResult.getSuccessCodePkIds();
        List<Integer> failCodePkIds = dbResult.getFailCodePkIds();
        List<BmsYfCostRecordPO> insertCostRecordPOs = dbResult.getInsertCostRecordPOs();
        List<BmsYfcostMainInfoPO> insertCostMainInfoPOs = dbResult.getInsertCostMainInfoPOs();
        List<BmsYfcostInfoPO> insertCostInfoPOs = dbResult.getInsertCostInfoPOs();
        List<BmsYfexpensesMiddlePO> insertMiddlePOs = dbResult.getInsertMiddlePOs();
        List<BmsYfexpensesMiddleSharePO> insertMiddleSharePOs = dbResult.getInsertMiddleSharePOs();

        UserBean userInfo = RequestContext.getUserInfo();

        /*
         * db操作流程
         * 1、清理订单对应的计费记录(计费失败的会有计费记录)
         * 2、更新订单状态
         * 3、新增费用主
         * 4、新增结算费(新增前将主费用的pk_id刷入)
         * 5、分摊表(新增前将主费用的pk_id刷入)
         * 6、插入订单计费记录(新增前将主费用的pk_id刷入)
         * 7、费用数据校准
         */
        log.info("调度单自动计费-DB操作开始");
        log.info("涉及操作单据数{}",codePkIds.size());
        if(CollUtil.isNotEmpty(codePkIds)){
            BmsYfCostRecordPOExample delRecordQueryParam = new BmsYfCostRecordPOExample();
            delRecordQueryParam.createCriteria()
                    .andCodeTypeEqualTo(BmsConstants.MIDDLE_CODE_TYPE_01)
                    .andCodePkIdIn(codePkIds);
            BmsYfCostRecordPO delRecordData = new BmsYfCostRecordPO();
            delRecordData.setDelFlag(BmsConstants.DEL_FLAG_1);
            delRecordData.setOperCode(userInfo.getEmployeecode().toString());
            delRecordData.setOperBy(userInfo.getEmployeename());
            delRecordData.setOperTime(new Date());
            BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(delRecordData, delRecordQueryParam);
        }

        if(CollUtil.isNotEmpty(successCodePkIds)){
            BmsDispatchCodeInfoPOExample editSuccessQueryParam = new BmsDispatchCodeInfoPOExample();
            editSuccessQueryParam.createCriteria()
                    .andPkIdIn(successCodePkIds);
            BmsDispatchCodeInfoPO editSuccessData = new BmsDispatchCodeInfoPO();
            editSuccessData.setCostStatus(BmsConstants.COST_STATUS_1);
            BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(editSuccessData, editSuccessQueryParam);
        }

        if(CollUtil.isNotEmpty(failCodePkIds)){
            BmsDispatchCodeInfoPOExample editFailQueryParam = new BmsDispatchCodeInfoPOExample();
            editFailQueryParam.createCriteria()
                    .andPkIdIn(failCodePkIds);
            BmsDispatchCodeInfoPO editFailQueryData = new BmsDispatchCodeInfoPO();
            editFailQueryData.setCostStatus(BmsConstants.COST_STATUS_3);
            BmsDs.instance().WMSMybatis().mapper().updateByExampleSelective(editFailQueryData, editFailQueryParam);
        }

        //费用DB
        if(CollUtil.isNotEmpty(insertCostMainInfoPOs)){
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertCostMainInfoPOs);
            if(CollUtil.isNotEmpty(insertCostMainInfoPOs)){
                //key:id,value:fee
                Map<String, BmsYfcostMainInfoPO> mainCostMap = insertCostMainInfoPOs.stream()
                        .collect(Collectors.toMap(BmsYfcostMainInfoPO::getId, Function.identity(), (v1, v2) -> v2));

                for (BmsYfcostInfoPO insertCostInfoPO : insertCostInfoPOs) {
                    if(mainCostMap.containsKey(insertCostInfoPO.getMainCodeId())){
                        insertCostInfoPO.setMainPkId(mainCostMap.get(insertCostInfoPO.getMainCodeId()).getPkId());
                    }
                }
                for (BmsYfexpensesMiddlePO insertMiddlePO : insertMiddlePOs) {
                    if(mainCostMap.containsKey(insertMiddlePO.getMainCodeId())){
                        insertMiddlePO.setMainPkId(mainCostMap.get(insertMiddlePO.getMainCodeId()).getPkId());
                    }
                }
                for (BmsYfexpensesMiddleSharePO insertMiddleSharePO : insertMiddleSharePOs) {
                    if(mainCostMap.containsKey(insertMiddleSharePO.getMainCodeId())){
                        insertMiddleSharePO.setMainPkId(mainCostMap.get(insertMiddleSharePO.getMainCodeId()).getPkId());
                    }
                }
            }
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertCostInfoPOs);
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertMiddlePOs);
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertMiddleSharePOs);
        }
        // 消费记录
        if(CollUtil.isNotEmpty(insertCostRecordPOs)){
            BmsDs.instance().WMSMybatis().mapper().batchInsert(insertCostRecordPOs);
        }
        // 费用数据校准
        if(CollUtil.isNotEmpty(codePkIds)){
            afterService.calculateDataDalibration(param.getHandlerType(),codePkIds);
        }
        log.info("调度单自动计费-承运商【{}】-结束",param.getCarrierId().toString());
    }



}
