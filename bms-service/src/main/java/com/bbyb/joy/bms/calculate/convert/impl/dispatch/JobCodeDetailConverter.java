package com.bbyb.joy.bms.calculate.convert.impl.dispatch;

import cn.hutool.core.bean.BeanUtil;
import com.bbyb.joy.bms.calculate.convert.OrderConverter;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDetailDto;
import com.bbyb.joy.bms.code.domain.dto.BmsJobCodeDetailInfoDto;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
public class JobCodeDetailConverter implements OrderConverter<BmsJobCodeDetailInfoDto, CalculateOrderDetailDto> {

    @Override
    public List<CalculateOrderDetailDto> convert(List<BmsJobCodeDetailInfoDto> sourceList) {
        if(sourceList == null || sourceList.isEmpty()){
            return Collections.emptyList();
        }

        List<CalculateOrderDetailDto> targetList = new ArrayList<>(sourceList.size());
        for (BmsJobCodeDetailInfoDto source : sourceList) {
            CalculateOrderDetailDto target = BeanUtil.toBean(source, CalculateOrderDetailDto.class);
            // 特殊字段处理
            if(target.getTotalBoxes() == null){
                target.setTotalBoxes(BigDecimal.ZERO);
            }
            if(target.getTotalNumber() == null){
                target.setTotalNumber(BigDecimal.ZERO);
            }
            if(target.getTotalWeight() == null){
                target.setTotalWeight(BigDecimal.ZERO);
            }
            if(target.getTotalVolume() == null){
                target.setTotalVolume(BigDecimal.ZERO);
            }
            targetList.add(target);
        }

        return targetList;
    }

    @Override
    public Class<BmsJobCodeDetailInfoDto> getSourceType() {
        return BmsJobCodeDetailInfoDto.class;
    }

    @Override
    public Class<CalculateOrderDetailDto> getTargetType() {
        return CalculateOrderDetailDto.class;
    }
}
