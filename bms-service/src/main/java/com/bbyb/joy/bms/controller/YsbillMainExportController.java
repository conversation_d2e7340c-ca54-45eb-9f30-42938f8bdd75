package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.biz.MdmClientInvoiceinfoBiz;
import com.bbyb.joy.bms.biz.MdmClientinfoBiz;
import com.bbyb.joy.bms.biz.StateYsBiz;
import com.bbyb.joy.bms.domain.dto.*;
import com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain;
import com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo;
import com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsbillmainExportAll;
import com.bbyb.joy.bms.domain.dto.bmsysbillexport.OtherFeeInfo;
import com.bbyb.joy.bms.domain.enums.BillTypeEnum;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.domain.enums.InvoiceModeEnum;
import com.bbyb.joy.bms.service.IBmsClaimsInfoService;
import com.bbyb.joy.bms.service.IBmsPubAddedfeeService;
import com.bbyb.joy.bms.service.IBmsYsbillmainService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import com.bbyb.joy.bms.support.exception.BusinessException;
import com.bbyb.joy.bms.support.utils.ExcelExportUtil;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.enums.PubNumEnum;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFPrintSetup;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.awt.Color;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "应收账单导出")
@RestController
@RequestMapping("/system/ysbillExport")
public class YsbillMainExportController {
    //单据类型5：增值单
    private static final String FIVE = "5";
    @Resource
    private StateYsBiz ysState;
    @Resource
    private MdmClientinfoBiz mdmClientinfoMapper;
    @Resource
    private MdmClientInvoiceinfoBiz mdmClientInvoiceinfoMapper;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    private IBmsClaimsInfoService bmsClaimsInfoService;
    @Resource
    private IBmsYsbillmainService bmsYsbillmainService;
    @Resource
    private IBmsPubAddedfeeService bmsPubAddedfeeService;

    /**
     * 账单摘要
     * 第一个sheet 账单汇总
     *
     * @param hssfWorkbook           hssf工作簿
     * @param bmsYsbillmainExportAll bms ysbillmain导出全部
     * @param dicMap                 dicMap
     * @param id                     id
     * @param sons                   子账单集合  可能为空
     * @return {@link SXSSFWorkbook}
     */
    public SXSSFWorkbook billSummary(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, Long id, List<Long> sons) {
        String tenantid = RequestContext.getTenantId();
        // 第一个sheet
        Sheet sheet1 = hssfWorkbook.createSheet("账单汇总");
        printA4Z(sheet1, true);
        //表头样式
        XSSFCellStyle headerStyle = ExcelExportUtil.getDefaultTitleStyle(hssfWorkbook);
        // 背景色的设定
        headerStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        final int[] rowNum = {0};
        //设置列宽
        for (int i = 0; i < 9; i++) {
            sheet1.setColumnWidth(i, 3200);
        }
        MdmClientinfo clientinfo = mdmClientinfoMapper.selectMdmClientinfoById(tenantid, bmsYsbillmainExportAll.getClientId());
        String clientName = clientinfo.getClientName();
        List<MdmClientInvoiceinfo> invList = mdmClientInvoiceinfoMapper.selectMdmClientInvoiceinfoByClientId(tenantid, clientinfo.getId().toString());
        if (CollUtil.isNotEmpty(invList)) {
            if (invList.size() == 1) {
                clientName = invList.get(0).getOpeningName();
            } else {
                List<MdmClientInvoiceinfo> invList2 = mdmClientInvoiceinfoMapper.selectMdmClientInvoiceinfoByClientIdlimit(tenantid, clientinfo.getId().toString(), bmsYsbillmainExportAll.getCompanyId());
                if (CollUtil.isNotEmpty(invList2)) {
                    clientName = invList2.get(0).getOpeningName();
                } else {
                    clientName = invList.get(0).getOpeningName();
                }
            }
        }
        //第一行
        Row r0 = sheet1.createRow(rowNum[0]++);
        r0.setHeight((short) 800);
        Cell c00 = r0.createCell(0);
        String billName = bmsYsbillmainExportAll.getBillName() == null ? "" : bmsYsbillmainExportAll.getBillName();
        if (StrUtil.isNotEmpty(billName) && billName.contains("】")) {
            billName = billName.substring(billName.indexOf("】") + 1);
            if (billName.contains("应收对账单")) {
                billName = billName.substring(0, billName.length() - 5) + "应收账单";
            }
        }
        if (bmsYsbillmainExportAll.getBillTypeStr() != null && bmsYsbillmainExportAll.getBillTypeStr().equals(FIVE)) {
            if (StrUtil.isNotEmpty(billName)) {
                billName = billName + "应收增值对账单";
            }
        }
        c00.setCellValue(billName);
        c00.setCellStyle(headerStyle);
        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));

        //第二行
        Row r1 = sheet1.createRow(rowNum[0]++);
        r1.setHeight((short) 500);
        String[] rowFirst = {"客户名称", "", clientName, "", "费用结算期", getfeeDayStr(bmsYsbillmainExportAll.getBillDate(), StrUtil.isEmpty(clientinfo.getPaymentDays()) ? 31 : Integer.parseInt(clientinfo.getPaymentDays()), bmsYsbillmainExportAll.getEndDate()), "", "", ""};
        for (int i = 0; i < rowFirst.length; i++) {
            Cell tempCell = r1.createCell(i);
            tempCell.setCellValue(rowFirst[i]);
            if (PubNumEnum.zero.getIntValue().equals(i) || PubNumEnum.three.getIntValue().equals(i)) {
                tempCell.setCellStyle(commonStyle2);
            } else {
                tempCell.setCellStyle(commonStyle);
            }

        }
        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(1, 1, 0, 1));
        sheet1.addMergedRegion(new CellRangeAddress(1, 1, 2, 3));
        sheet1.addMergedRegion(new CellRangeAddress(1, 1, 5, 8));
        List<String> otherFeeNames = new ArrayList<>();
        List<String> otherFeeNamesYs = new ArrayList<>();
        //仓储账单其他款项明细列
        if (bmsYsbillmainExportAll.getCcUnClaimesAmount() != null && bmsYsbillmainExportAll.getCcUnClaimesAmount().compareTo(BigDecimal.ZERO) != 0) {
            otherFeeNames.add("未赔付理赔费");
        }
        //运输账单其他款项明细列
        if (bmsYsbillmainExportAll.getYsUnClaimesAmount() != null && bmsYsbillmainExportAll.getYsUnClaimesAmount().compareTo(BigDecimal.ZERO) != 0) {
            otherFeeNamesYs.add("未赔付理赔费");
        }
        otherFeeNames.add("客诉理赔费");
        otherFeeNames.add("otherCost1");
        otherFeeNames.add("otherCost2");
        otherFeeNames.add("otherCost3");
        otherFeeNames.add("otherCost4");
        otherFeeNames.add("otherCost5");
        otherFeeNames.add("otherCost6");
        otherFeeNames.add("otherCost7");
        otherFeeNames.add("otherCost8");
        otherFeeNames.add("otherCost9");
        otherFeeNames.add("otherCost10");
        otherFeeNames.add("otherCost11");
        otherFeeNames.add("otherCost12");
        otherFeeNamesYs.add("客诉理赔费");
        otherFeeNamesYs.add("otherCost1");
        otherFeeNamesYs.add("otherCost2");
        otherFeeNamesYs.add("otherCost3");
        otherFeeNamesYs.add("otherCost4");
        otherFeeNamesYs.add("otherCost5");
        otherFeeNamesYs.add("otherCost6");
        otherFeeNamesYs.add("otherCost7");
        otherFeeNamesYs.add("otherCost8");
        otherFeeNamesYs.add("otherCost9");
        otherFeeNamesYs.add("otherCost10");
        otherFeeNamesYs.add("otherCost11");
        otherFeeNamesYs.add("otherCost12");
        //查询所有增值费
        List<BmsYsBillExportInfo> adAmont = ysState.getValueAddedFeeByBillIdGroupBy(tenantid, sons, 1);
        /* 如果仓储合计费为0 ，那么就隐藏仓储费*/
        //第三行
        if ((bmsYsbillmainExportAll.getBillType() != null && BmsConstants.GENERATE_BILL_TYPE_STROCK_SETS.contains(bmsYsbillmainExportAll.getBillType())) || (bmsYsbillmainExportAll.getCcFeeSum() != null && bmsYsbillmainExportAll.getCcFeeSum().compareTo(BigDecimal.ZERO) != 0)) {
            Row r2 = sheet1.createRow(rowNum[0]++);
            r2.setHeight((short) 500);
            String[] rowSecond = {"仓储服务部分", "", "服务项目", "", "类型", "金额", "", "", ""};
            for (int i = 0; i < rowSecond.length; i++) {
                Cell tempCell = r2.createCell(i);
                tempCell.setCellValue(rowSecond[i]);
                if (i == 0) {
                    tempCell.setCellStyle(commonStyle2);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }

            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(2, 2, 2, 3));
            sheet1.addMergedRegion(new CellRangeAddress(2, 2, 5, 8));

            //第四行
            Row r3 = sheet1.createRow(rowNum[0]++);
            r3.setHeight((short) 500);
            String[] row3 = {"", "", "管理处置费", "", "常温", bmsYsbillmainExportAll.getGlFeeCwAmount() != null ? bmsYsbillmainExportAll.getGlFeeCwAmount().toString() : "0", "", "", ""};
            for (int i = 0; i < row3.length; i++) {
                Cell tempCell = r3.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row3[i]) || new BigDecimal(row3[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row3[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row3[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(3, 3, 5, 8));

            //第5行
            Row r5 = sheet1.createRow(rowNum[0]++);
            r5.setHeight((short) 500);
            String[] row5 = {"", "", "", "", "冷藏", bmsYsbillmainExportAll.getGlFeeLcAmount().toString(), "", "", ""};
            for (int i = 0; i < row5.length; i++) {
                Cell tempCell = r5.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row5[i]) || new BigDecimal(row5[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row5[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row5[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(4, 4, 5, 8));

            //第6行
            Row r6 = sheet1.createRow(rowNum[0]++);
            r6.setHeight((short) 500);
            String[] row6 = {"", "", "", "", "冷冻", bmsYsbillmainExportAll.getGlFeeLdAmount().toString(), "", "", ""};
            for (int i = 0; i < row6.length; i++) {
                Cell tempCell = r6.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row6[i]) || new BigDecimal(row6[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row6[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row6[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(5, 5, 5, 8));
            sheet1.addMergedRegion(new CellRangeAddress(3, 5, 2, 3));

            //第7行
            Row r7 = sheet1.createRow(rowNum[0]++);
            r7.setHeight((short) 500);
            String[] row7 = {"", "", "存储服务费", "", "常温", bmsYsbillmainExportAll.getCcFeeCwAmount().toString(), "", "", ""};
            for (int i = 0; i < row7.length; i++) {
                Cell tempCell = r7.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row7[i]) || new BigDecimal(row7[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row7[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row7[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }

            sheet1.addMergedRegion(new CellRangeAddress(6, 6, 5, 8));

            //第8行
            Row r8 = sheet1.createRow(rowNum[0]++);
            r8.setHeight((short) 500);
            String[] row8 = {"", "", "", "", "冷藏", bmsYsbillmainExportAll.getCcFeeLcAmount().toString(), "", "", ""};
            for (int i = 0; i < row8.length; i++) {
                Cell tempCell = r8.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row8[i]) || new BigDecimal(row8[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row8[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row8[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(7, 7, 5, 8));

            //第9行
            Row r9 = sheet1.createRow(rowNum[0]++);
            r9.setHeight((short) 500);
            String[] row9 = {"", "", "", "", "冷冻", bmsYsbillmainExportAll.getCcFeeLdAmount().toString(), "", "", ""};
            for (int i = 0; i < row9.length; i++) {
                Cell tempCell = r9.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row9[i]) || new BigDecimal(row9[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row9[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row9[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格

            sheet1.addMergedRegion(new CellRangeAddress(8, 8, 5, 8));
            sheet1.addMergedRegion(new CellRangeAddress(6, 8, 2, 3));
            //sheet1.addMergedRegion(new CellRangeAddress(6, 8, 3, 3));

            //第9行
            Row r10 = sheet1.createRow(rowNum[0]++);
            r10.setHeight((short) 500);
            String[] row10 = {"", "", "分拣操作费", "", "入库操作", "金额", "", "", ""};
            for (int i = 0; i < row10.length; i++) {
                Cell tempCell = r10.createCell(i);
                tempCell.setCellValue(row10[i]);
                tempCell.setCellStyle(commonStyle);
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(9, 9, 5, 8));

            //第10行
            Row r11 = sheet1.createRow(rowNum[0]++);
            r11.setHeight((short) 500);
            String[] row11 = {"", "", "", "", "常温", bmsYsbillmainExportAll.getFjFeeCwRkAmount() == null ? "0" : String.valueOf(bmsYsbillmainExportAll.getFjFeeCwRkAmount().add(bmsYsbillmainExportAll.getRkShortbargeFeeCW())), "", "", ""};
            for (int i = 0; i < row11.length; i++) {
                Cell tempCell = r11.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row11[i]) || new BigDecimal(row11[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row11[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row11[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(10, 10, 5, 8));

            //第11行
            Row r12 = sheet1.createRow(rowNum[0]++);
            r12.setHeight((short) 500);
            String[] row12 = {"", "", "", "", "冷藏", bmsYsbillmainExportAll.getFjFeeLcRkAmount() == null ? "0" : String.valueOf(bmsYsbillmainExportAll.getFjFeeLcRkAmount().add(bmsYsbillmainExportAll.getRkShortbargeFeeLC())), "", "", ""};
            for (int i = 0; i < row12.length; i++) {
                Cell tempCell = r12.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row12[i]) || new BigDecimal(row12[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row12[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row12[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(11, 11, 5, 8));

            //第12行
            Row r13 = sheet1.createRow(rowNum[0]++);
            r13.setHeight((short) 500);
            String[] row13 = {"", "", "", "", "冷冻", bmsYsbillmainExportAll.getFjFeeLdRkAmount() == null ? "0" : String.valueOf(bmsYsbillmainExportAll.getFjFeeLdRkAmount().add(bmsYsbillmainExportAll.getRkShortbargeFeeLD())), "", "", ""};
            for (int i = 0; i < row13.length; i++) {
                Cell tempCell = r13.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row13[i]) || new BigDecimal(row13[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row13[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row13[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(12, 12, 5, 8));

            //第13行
            Row r14 = sheet1.createRow(rowNum[0]++);
            r14.setHeight((short) 500);
            String[] row14 = {"", "", "", "", "出库操作", "金额", "", "", ""};
            for (int i = 0; i < row14.length; i++) {
                Cell tempCell = r14.createCell(i);
                tempCell.setCellValue(row14[i]);
                tempCell.setCellStyle(commonStyle);
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(13, 13, 5, 8));

            //第14行
            Row r15 = sheet1.createRow(rowNum[0]++);
            r15.setHeight((short) 500);
            String[] row15 = {"", "", "", "", "常温", bmsYsbillmainExportAll.getFjFeeCwCkAmount() == null ? "0" : String.valueOf(bmsYsbillmainExportAll.getFjFeeCwCkAmount().add(bmsYsbillmainExportAll.getCcShortbargeFeeCW())), "", "", ""};
            for (int i = 0; i < row15.length; i++) {
                Cell tempCell = r15.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row15[i]) || new BigDecimal(row15[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row15[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row15[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(14, 14, 5, 8));

            //第15行
            Row r16 = sheet1.createRow(rowNum[0]++);
            r16.setHeight((short) 500);
            String[] row16 = {"", "", "", "", "冷藏", bmsYsbillmainExportAll.getFjFeeLcCkAmount() == null ? "0" : String.valueOf(bmsYsbillmainExportAll.getFjFeeLcCkAmount().add(bmsYsbillmainExportAll.getCcShortbargeFeeLC())), "", "", ""};
            for (int i = 0; i < row16.length; i++) {
                Cell tempCell = r16.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row16[i]) || new BigDecimal(row16[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row16[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row16[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(15, 15, 5, 8));

            //第16行
            Row r17 = sheet1.createRow(rowNum[0]++);
            r17.setHeight((short) 500);
            String[] row17 = {"", "", "", "", "冷冻", bmsYsbillmainExportAll.getFjFeeLdCkAmount() == null ? "0" : String.valueOf(bmsYsbillmainExportAll.getFjFeeLdCkAmount().add(bmsYsbillmainExportAll.getCcShortbargeFeeLD())), "", "", ""};
            for (int i = 0; i < row17.length; i++) {
                Cell tempCell = r17.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row17[i]) || new BigDecimal(row17[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row17[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row17[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(16, 16, 5, 8));
            sheet1.addMergedRegion(new CellRangeAddress(9, 16, 2, 3));

            //第17行
            Row r18 = sheet1.createRow(rowNum[0]++);
            r18.setHeight((short) 500);
            String[] row18 = {"", "", "装卸服务费", "", "入库装卸服务费", bmsYsbillmainExportAll.getFjFeeRkZhAmount() == null ? "0" : String.valueOf(bmsYsbillmainExportAll.getFjFeeRkZhAmount()), "", "", ""};
            for (int i = 0; i < row18.length; i++) {
                Cell tempCell = r18.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row18[i]) || new BigDecimal(row18[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row18[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row18[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(17, 17, 5, 8));

            //第18行
            Row r19 = sheet1.createRow(rowNum[0]++);
            r19.setHeight((short) 500);
            String[] row19 = {"", "", "", "", "出库装卸服务费", bmsYsbillmainExportAll.getFjFeeRkPhAmount() == null ? "0" : String.valueOf(bmsYsbillmainExportAll.getFjFeeRkPhAmount()), "", "", ""};
            for (int i = 0; i < row19.length; i++) {
                Cell tempCell = r19.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row19[i]) || new BigDecimal(row19[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row19[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    }
                } else {
                    tempCell.setCellValue(row19[i]);
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(18, 18, 5, 8));
            sheet1.addMergedRegion(new CellRangeAddress(17, 18, 2, 3));

            //第19行
            Row r20z = sheet1.createRow(rowNum[0]++);
            r20z.setHeight((short) 500);
            String[] row20z = {"", "", "制单费", "", "", bmsYsbillmainExportAll.getCcReturnFee() == null ? "0" : String.valueOf(bmsYsbillmainExportAll.getCcReturnFee()), "", "", ""};
            for (int i = 0; i < row20z.length; i++) {
                Cell tempCell = r20z.createCell(i);
                if (PubNumEnum.five.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row20z[i]) || new BigDecimal(row20z[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                    } else {
                        tempCell.setCellValue(new BigDecimal(row20z[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    }
                } else {
                    tempCell.setCellValue(row20z[i]);
                }
                tempCell.setCellStyle(commonStyle);
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(19, 19, 2, 3));
            sheet1.addMergedRegion(new CellRangeAddress(19, 19, 5, 8));
            sheet1.addMergedRegion(new CellRangeAddress(2, 19, 0, 1));

            //第21行
            Row r22 = sheet1.createRow(rowNum[0]++);
            r22.setHeight((short) 500);
            String[] row22 = {"其他款项明细", "", "增值服务费部分（根据每月实际情况收取）", "", "新增/扣款事件明细", "新增或扣款金额", "", "", ""};
            for (int i = 0; i < row22.length; i++) {
                Cell tempCell = r22.createCell(i);
                tempCell.setCellValue(row22[i]);
                if (i == 0) {
                    tempCell.setCellStyle(commonStyle2);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(20, 20, 5, 8));

            List<OtherFeeInfo> otherFeeInfoList = new ArrayList<>();
            for (String name : otherFeeNames) {
                OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
                otherFeeInfo.setOtherName(name);
                if (StrUtil.isNotEmpty(dicMap.get("ys_storage_othercost" + name))) {
                    otherFeeInfo.setOtherName(dicMap.get("ys_storage_othercost" + name));
                }
                switch (name) {
                    case "未赔付理赔费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcUnClaimesAmount());
                        break;
                    case "客诉理赔费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcExceptionFee());
                        break;
                    case "otherCost1":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost1());
                        break;
                    case "otherCost2":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost2());
                        break;
                    case "otherCost3":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost3());
                        break;
                    case "otherCost4":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost4());
                        break;
                    case "otherCost5":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost5());
                        break;
                    case "otherCost6":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost6());
                        break;
                    case "otherCost7":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost7());
                        break;
                    case "otherCost8":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost8());
                        break;
                    case "otherCost9":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost9());
                        break;
                    case "otherCost10":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost10());
                        break;
                    case "otherCost11":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost11());
                        break;
                    case "otherCost12":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost12());
                        break;
                    default:
                        otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
                }
                otherFeeInfoList.add(otherFeeInfo);
            }

            for (OtherFeeInfo otherFeeInfo : otherFeeInfoList) {

                Row r23 = sheet1.createRow(rowNum[0]++);
                r23.setHeight((short) 500);
                String[] row23 = {"", "", "", "", otherFeeInfo.getOtherName(), otherFeeInfo.getOtherFee() == null ? "0" : otherFeeInfo.getOtherFee().toString(), "", "", ""};
                for (int i = 0; i < row23.length; i++) {
                    Cell tempCell = r23.createCell(i);
                    if (PubNumEnum.five.getIntValue().equals(i)) {
                        if (StrUtil.isEmpty(row23[i]) || new BigDecimal(row23[i]).compareTo(BigDecimal.ZERO) == 0) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(new BigDecimal(row23[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                            commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        }
                    } else {
                        tempCell.setCellValue(row23[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 5, 8));
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(20, 33, 0, 1));
            sheet1.addMergedRegion(new CellRangeAddress(20, 33, 2, 3));
            //仓储
            BigDecimal sumAmountC = new BigDecimal(0);
            //仓配业务时展示的增值费
            if (bmsYsbillmainExportAll.getBillType() != null && bmsYsbillmainExportAll.getBillType().equals(BmsConstants.STORAGE_TRANSPORT_4)) {
                if (CollUtil.isNotEmpty(adAmont)) {
                    int gdzzrow = 0;
                    int biegin = rowNum[0];
                    gdzzrow = rowNum[0];
                    int zzrow = rowNum[0];
                    Map<Integer, List<BmsYsBillExportInfo>> collect = adAmont.stream().collect(
                            Collectors.groupingBy(BmsYsBillExportInfo::getFeeBelong));
                    //仓储增值费
                    if (collect.get(2) != null && !collect.get(2).isEmpty()) {
                        Row row58z = sheet1.createRow(rowNum[0]++);
                        zzrow = rowNum[0];
                        gdzzrow = gdzzrow == 0 ? rowNum[0] : gdzzrow;
                        String[] row59z = {"其他款项明细", "", "增值服务费部分_仓储", "", "费用项", "金额", "", "", ""};
                        for (int i = 0; i < row59z.length; i++) {
                            Cell tempCell = row58z.createCell(i);
                            tempCell.setCellValue(row59z[i]);
                            tempCell.setCellStyle(i == 0 ? commonStyle2 : commonStyle);
                        }
                        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 5, 8));
                        for (BmsYsBillExportInfo bmsYsBillExportInfo : collect.get(2)) {
                            Row r59zz = sheet1.createRow(rowNum[0]++);
                            sumAmountC = sumAmountC.add(bmsYsBillExportInfo.getAmount());
                            //sumAmount=sumAmount.add(bmsYsBillExportInfo.getAmount());
                            r59zz.setHeight((short) 500);
                            String[] row59zz = {"", "", "", "", bmsYsBillExportInfo.getItemName(), bmsYsBillExportInfo.getAmount().toString(), "", "", ""};
                            for (int i = 0; i < row59zz.length; i++) {
                                Cell tempCell = r59zz.createCell(i);
                                if (PubNumEnum.five.getIntValue().equals(i)) {
                                    if (StrUtil.isEmpty(row59zz[i]) || new BigDecimal(row59zz[i]).compareTo(BigDecimal.ZERO) == 0) {
                                        tempCell.setCellValue("");
                                        tempCell.setCellStyle(commonStyle);
                                    } else {
                                        tempCell.setCellValue(new BigDecimal(row59zz[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                                        tempCell.setCellStyle(commonStyle);
                                    }
                                } else {
                                    tempCell.setCellValue(row59zz[i]);
                                    tempCell.setCellStyle(commonStyle);
                                }
                            }
                            //合并单元格
                            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 5, 8));
                        }
                        sheet1.addMergedRegion(new CellRangeAddress(zzrow - 1, rowNum[0] - 1, 2, 3));
                        bmsYsbillmainExportAll.setCcFeeSum(
                                bmsYsbillmainExportAll.getCcFeeSum() == null ? null : bmsYsbillmainExportAll.getCcFeeSum().add(sumAmountC));
                        sheet1.addMergedRegion(new CellRangeAddress(biegin, rowNum[0] - 1, 0, 1));
                    }
                }
            }
            // 第35行
            Row r36 = sheet1.createRow(rowNum[0]++);
            r36.setHeight((short) 500);
            String[] row36 = {"仓储费用合计:", "", "", "", bmsYsbillmainExportAll.getCcFeeSum() == null ? "0" : String.valueOf(bmsYsbillmainExportAll.getCcFeeSum()), "", "", "", ""};
            for (int i = 0; i < row36.length; i++) {
                Cell tempCell = r36.createCell(i);
                if (PubNumEnum.four.getIntValue().equals(i)) {
                    if (StrUtil.isEmpty(row36[i]) || new BigDecimal(row36[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                    } else {
                        tempCell.setCellValue(new BigDecimal(row36[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    }
                } else {
                    tempCell.setCellValue(row36[i]);
                }
                tempCell.setCellStyle(commonStyle);
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 0, 3));
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 4, 8));
        }

        /* 当运输费合计为0时，隐藏运输费用明细*/
        if ((bmsYsbillmainExportAll.getBillType() != null && BmsConstants.GENERATE_BILL_TYPE_TRANS_SETS.contains(bmsYsbillmainExportAll.getBillType())) || (bmsYsbillmainExportAll.getPsFeeSum() != null && bmsYsbillmainExportAll.getPsFeeSum().compareTo(BigDecimal.ZERO) != 0)) {
            // 第36行
            int psRow = rowNum[0]++;
            Row r37 = sheet1.createRow(psRow);
            r37.setHeight((short) 800);
            String[] row37 = {"配送服务部分", "", "当期账单覆盖门店数", "配送店次", "总件数", "总箱数", "总重量", "总体积", "总货值"};

            //所有费用列的下标(处理保留2位小数的问题)
            Set<Integer> feeColumns = new HashSet<>(Collections.singletonList(8));


            for (int i = 0; i < row37.length; i++) {
                Cell tempCell = r37.createCell(i);
                tempCell.setCellValue(row37[i]);
                if (feeColumns.contains(i)) {
                    commonStyle2.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle2);
                } else {
                    tempCell.setCellStyle(commonStyle2);
                }
            }

            // 查询线路分组信息
            List<BmsYsbillcodeinfo> routeinfoList = ysState.getGoupRouteinfoListByBillId(tenantid, id);
            //第N行
            for (int i = 0; i < routeinfoList.size(); i++) {
                // 线路名称
                String routeName = routeinfoList.get(i).getLineName();
                // 门店数量
                String storeNum = routeinfoList.get(i).getStoreNum();
                // 门店数量
                String dcStoreNum = routeinfoList.get(i).getDcStoreNum();
                // 配送门店数量
                String distributionStoreNum = routeinfoList.get(i).getDistributionStoreNum();
                // 总件数
                String sumGoods = routeinfoList.get(i).getTotalNumber() != null ? routeinfoList.get(i).getTotalNumber().toString() : "";
                // 总箱数
                String sumBox = routeinfoList.get(i).getTotalBoxes() != null ? routeinfoList.get(i).getTotalBoxes().toString() : "";
                // 总重量
                String sumWeight = routeinfoList.get(i).getTotalWeight() != null ? routeinfoList.get(i).getTotalWeight().toString() : "";
                // 总体积
                String sumVolume = routeinfoList.get(i).getTotalVolume() != null ? routeinfoList.get(i).getTotalVolume().toString() : "";
                // 总货值
                String totalValue = routeinfoList.get(i).getCargoValue() != null ? routeinfoList.get(i).getCargoValue().toString() : "";

                //数据
                Row rn = sheet1.createRow(rowNum[0]++);
                rn.setHeight((short) 700);
                String[] rowRns = {"", "", distributionStoreNum, dcStoreNum, sumGoods, sumBox, sumWeight, sumVolume, totalValue};

                //所有费用列的下标(处理保留2位小数的问题)
                Set<Integer> feeColumns2 = new HashSet<>(Collections.singletonList(8));

                for (int j = 0; j < rowRns.length; j++) {
                    Cell tempCell = rn.createCell(j);
                    if (j > 1) {
                        if (StrUtil.isEmpty(rowRns[j]) || new BigDecimal(rowRns[j]).compareTo(BigDecimal.ZERO) == 0) {
                            tempCell.setCellValue("");
                        } else {
                            if (feeColumns2.contains(j)) {
                                tempCell.setCellValue(new BigDecimal(rowRns[j]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                            } else {
                                tempCell.setCellValue(rowRns[j]);
                            }
                        }
                    }
                    if (feeColumns2.contains(i)) {
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellStyle(commonStyle);
                    }
                }
            }

            Row r37n1 = sheet1.createRow(rowNum[0]++);
            r37n1.setHeight((short) 800);
            String[] row37n1 = {"", "", "费用项", "", "金额", "", "", "", ""};
            for (int i = 0; i < row37n1.length; i++) {
                Cell tempCell = r37n1.createCell(i);
                tempCell.setCellValue(row37n1[i]);
                tempCell.setCellStyle(commonStyle2);
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 2, 3));
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 4, 8));

            String[] ccFeeNames = {"运费", "提货费", "送货费", "超筐费", "加点费", "减点费", "短驳费", "装卸费", "超远费"};

            List<OtherFeeInfo> ccFeeInfoList = new ArrayList<>();
            for (String name : ccFeeNames) {
                OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
                otherFeeInfo.setOtherName(name);
                switch (name) {
                    case "运费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsFreight());
                        break;
                    case "提货费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsDeliveryFee());
                        break;
                    case "送货费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOutboundsortingFee());
                        break;
                    case "超筐费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsSuperframesFee());
                        break;
                    case "加点费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsExcessFee());
                        break;
                    case "减点费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsReduceFee());
                        break;
                    case "短驳费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsShortbargeFee());
                        break;
                    case "装卸费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsReturnFee());
                        break;
                    case "超远费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsUltrafarFee());
                        break;
                    default:
                        otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
                }
                ccFeeInfoList.add(otherFeeInfo);
            }

            for (OtherFeeInfo ccFeeInfo : ccFeeInfoList) {
                Row r37n2 = sheet1.createRow(rowNum[0]++);
                r37n2.setHeight((short) 500);
                String[] row37n2 = {"", "", ccFeeInfo.getOtherName(), "", String.valueOf(ccFeeInfo.getOtherFee()), "", "", "", ""};
                for (int i = 0; i < row37n2.length; i++) {
                    Cell tempCell = r37n2.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        if (StrUtil.isEmpty(row37n2[i]) || new BigDecimal(row37n2[i]).compareTo(BigDecimal.ZERO) == 0) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(new BigDecimal(row37n2[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        }
                    } else {
                        tempCell.setCellValue(row37n2[i]);
                    }
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle);
                    } else {
                        tempCell.setCellStyle(commonStyle);
                    }
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 2, 3));
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 4, 8));
            }

            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(psRow, rowNum[0] - 1, 0, 1));


            //第N行
            int orderOtherFeeStart = rowNum[0];
            Row r44 = sheet1.createRow(rowNum[0]++);
            r44.setHeight((short) 500);
            String[] row44 = {"其他款项明细", "", "新增/扣款事件明细", "", "新增或扣款金额", "", "", "", ""};
            for (int i = 0; i < row44.length; i++) {
                Cell tempCell = r44.createCell(i);
                tempCell.setCellValue(row44[i]);
                if (i == 0) {
                    tempCell.setCellStyle(commonStyle2);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }

            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 2, 3));
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 4, 8));

            List<OtherFeeInfo> orderOtherFeeInfoList = new ArrayList<>();
            for (String name : otherFeeNamesYs) {
                OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
                otherFeeInfo.setOtherName(name);
                if (StrUtil.isNotEmpty(dicMap.get("ys_transport_othercost" + name))) {
                    otherFeeInfo.setOtherName(dicMap.get("ys_transport_othercost" + name));
                }
                switch (name) {
                    case "未赔付理赔费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getYsUnClaimesAmount());
                        break;
                    case "客诉理赔费":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsExceptionFee());
                        break;
                    case "otherCost1":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOtherCost1());
                        break;
                    case "otherCost2":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOtherCost2());
                        break;
                    case "otherCost3":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOtherCost3());
                        break;
                    case "otherCost4":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOtherCost4());
                        break;
                    case "otherCost5":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOtherCost5());
                        break;
                    case "otherCost6":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOtherCost6());
                        break;
                    case "otherCost7":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOtherCost7());
                        break;
                    case "otherCost8":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOtherCost8());
                        break;
                    case "otherCost9":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOtherCost9());
                        break;
                    case "otherCost10":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOtherCost10());
                        break;
                    case "otherCost11":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOtherCost11());
                        break;
                    case "otherCost12":
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getPsOtherCost12());
                        break;
                    default:
                        otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
                }
                orderOtherFeeInfoList.add(otherFeeInfo);
            }

            for (OtherFeeInfo otherFeeInfo : orderOtherFeeInfoList) {
                Row rn = sheet1.createRow(rowNum[0]++);
                rn.setHeight((short) 500);
                String[] rowRns = {"", "", otherFeeInfo.getOtherName(), "", String.valueOf(otherFeeInfo.getOtherFee()), "", "", "", ""};
                for (int i = 0; i < rowRns.length; i++) {
                    Cell tempCell = rn.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        if (StrUtil.isEmpty(rowRns[i]) || new BigDecimal(rowRns[i]).compareTo(BigDecimal.ZERO) == 0) {
                            tempCell.setCellValue("");
                            tempCell.setCellStyle(commonStyle);
                        } else {
                            tempCell.setCellValue(new BigDecimal(rowRns[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                            commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                            tempCell.setCellStyle(commonStyle);
                        }
                    } else {
                        tempCell.setCellValue(rowRns[i]);
                        tempCell.setCellStyle(commonStyle);
                    }
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 2, 3));
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 4, 8));
            }

            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(orderOtherFeeStart, rowNum[0] - 1, 0, 1));
            //仓配业务时展示的增值费
            if (bmsYsbillmainExportAll.getBillType() != null && bmsYsbillmainExportAll.getBillType().equals(BmsConstants.STORAGE_TRANSPORT_4)) {
                // 增值费用明细
                if (adAmont != null && !adAmont.isEmpty()) {
                    int biegin = rowNum[0];
                    int gdzzrow = 0;
                    gdzzrow = rowNum[0];
                    int zzrow = rowNum[0];
                    Map<Integer, List<BmsYsBillExportInfo>> collect = adAmont.stream().collect(Collectors.groupingBy(BmsYsBillExportInfo::getFeeBelong));
                    //运输
                    BigDecimal sumAmountY = new BigDecimal(0);
                    //运输增值
                    if (collect.get(1) != null && !collect.get(1).isEmpty()) {
                        Row r59 = sheet1.createRow(rowNum[0]++);
                        zzrow = rowNum[0];
                        r59.setHeight((short) 500);
                        String[] row59 = {"其他款项明细", "", "增值服务费部分_运输", "", "费用项", "金额", "", "", ""};
                        for (int i = 0; i < row59.length; i++) {
                            Cell tempCell = r59.createCell(i);
                            tempCell.setCellValue(row59[i]);
                            tempCell.setCellStyle(i == 0 ? commonStyle2 : commonStyle);
                        }
                        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 5, 8));
                        for (BmsYsBillExportInfo bmsYsBillExportInfo : collect.get(1)) {
                            Row r59zz = sheet1.createRow(rowNum[0]++);
                            sumAmountY = sumAmountY.add(bmsYsBillExportInfo.getAmount());
                            r59zz.setHeight((short) 500);
                            String[] row59zz = {"", "", "", "", bmsYsBillExportInfo.getItemName(), bmsYsBillExportInfo.getAmount().toString(), "", "", ""};
                            for (int i = 0; i < row59zz.length; i++) {
                                Cell tempCell = r59zz.createCell(i);
                                if (PubNumEnum.five.getIntValue().equals(i)) {
                                    if (StrUtil.isEmpty(row59zz[i]) || new BigDecimal(row59zz[i]).compareTo(BigDecimal.ZERO) == 0) {
                                        tempCell.setCellValue("");
                                        tempCell.setCellStyle(commonStyle);
                                    } else {
                                        tempCell.setCellValue(new BigDecimal(row59zz[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                                        commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                                        tempCell.setCellStyle(commonStyle);
                                    }
                                } else {
                                    tempCell.setCellValue(row59zz[i]);
                                    tempCell.setCellStyle(commonStyle);
                                }
                            }
                            //合并单元格
                            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 5, 8));
                        }
                        sheet1.addMergedRegion(new CellRangeAddress(zzrow - 1, rowNum[0] - 1, 2, 3));
                        bmsYsbillmainExportAll.setPsFeeSum(bmsYsbillmainExportAll.getPsFeeSum() == null ? BigDecimal.ZERO :
                                bmsYsbillmainExportAll.getPsFeeSum().add(sumAmountY));
                        sheet1.addMergedRegion(new CellRangeAddress(biegin, rowNum[0] - 1, 0, 1));
                    }
                }
            }

            // 第N行
            Row r58 = sheet1.createRow(rowNum[0]++);
            r58.setHeight((short) 500);
            String[] row58 = {"配送费用合计:", "", "", "", bmsYsbillmainExportAll.getPsFeeSum().setScale(2, RoundingMode.HALF_UP).toString(), "", "", "", ""};
            for (int i = 0; i < row58.length; i++) {
                Cell tempCell = r58.createCell(i);
                tempCell.setCellValue(row58[i]);
                if (PubNumEnum.four.getIntValue().equals(i)) {
                    tempCell.setCellValue(Double.parseDouble(row58[i]));
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 0, 3));
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 4, 8));

        }
        //查询固定费用
        List<BmsYsBillExportInfo> gdAmont = ysState.getFixedExpensesByBillIdGroupBy(tenantid, sons);

        int gdzzrow = 0;
        BigDecimal sumAmount = new BigDecimal(0);
        //仓储
        BigDecimal sumAmountC = new BigDecimal(0);
        //运输
        BigDecimal sumAmountY = new BigDecimal(0);
        // 固定费用明细
        if (gdAmont != null && !gdAmont.isEmpty()) {
            Row r59 = sheet1.createRow(rowNum[0]++);
            gdzzrow = rowNum[0];
            r59.setHeight((short) 500);
            String[] row59 = {"其他款项明细", "", "固定费用明细", "", "新增/扣款事件明细", "新增或扣款金额", "", "", ""};
            for (int i = 0; i < row59.length; i++) {
                Cell tempCell = r59.createCell(i);
                tempCell.setCellValue(row59[i]);
                tempCell.setCellStyle(i == 0 ? commonStyle2 : commonStyle);
            }
            //合并单元格
            //sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 2, 3));
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 5, 8));
            for (BmsYsBillExportInfo bmsYsBillExportInfo : gdAmont) {
                sumAmount = sumAmount.add(bmsYsBillExportInfo.getAmount());
                //固定费默认在仓储
                sumAmountC = sumAmountC.add(bmsYsBillExportInfo.getAmount());
                Row r59gd = sheet1.createRow(rowNum[0]++);
                r59gd.setHeight((short) 500);
                String[] row59gd = {"", "", "", "", bmsYsBillExportInfo.getItemName(), bmsYsBillExportInfo.getAmount().toString(), "", "", ""};
                for (int i = 0; i < row59gd.length; i++) {
                    Cell tempCell = r59gd.createCell(i);
                    if (PubNumEnum.five.getIntValue().equals(i)) {
                        if (StrUtil.isEmpty(row59gd[i]) || new BigDecimal(row59gd[i]).compareTo(BigDecimal.ZERO) == 0) {
                            tempCell.setCellValue("");
                            tempCell.setCellStyle(commonStyle);
                        } else {
                            tempCell.setCellValue(new BigDecimal(row59gd[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                            commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                            tempCell.setCellStyle(commonStyle);
                        }
                    } else {
                        tempCell.setCellValue(row59gd[i]);
                        tempCell.setCellStyle(commonStyle);
                    }
                }
                //合并单元格
                //sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 2, 3));
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 5, 8));
            }
            sheet1.addMergedRegion(new CellRangeAddress(gdzzrow - 1, rowNum[0] - 1, 0, 1));
            sheet1.addMergedRegion(new CellRangeAddress(gdzzrow - 1, rowNum[0] - 1, 2, 3));
        }
        // 增值费用明细
        //仓配业务的 增值费不在这显示 已经在上面做仓储&运输分开展示的处理了
        if (bmsYsbillmainExportAll.getBillType() != null && !bmsYsbillmainExportAll.getBillType().equals(BmsConstants.STORAGE_TRANSPORT_4)) {
            if (adAmont != null && !adAmont.isEmpty()) {
                int biegin = rowNum[0];
                gdzzrow = gdzzrow == 0 ? rowNum[0] : gdzzrow;
                int zzrow = rowNum[0];
                Map<Integer, List<BmsYsBillExportInfo>> collect = adAmont.stream().collect(Collectors.groupingBy(BmsYsBillExportInfo::getFeeBelong));
                //运输增值
                if (collect.get(1) != null && !collect.get(1).isEmpty()) {
                    Row r59 = sheet1.createRow(rowNum[0]++);
                    zzrow = rowNum[0];
                    gdzzrow = gdzzrow == 0 ? rowNum[0] : gdzzrow;
                    r59.setHeight((short) 500);
                    String[] row59 = {"其他款项明细", "", "增值服务费部分_运输", "", "费用项", "金额", "", "", ""};
                    for (int i = 0; i < row59.length; i++) {
                        Cell tempCell = r59.createCell(i);
                        tempCell.setCellValue(row59[i]);
                        tempCell.setCellStyle(i == 0 ? commonStyle2 : commonStyle);
                    }
                    sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 5, 8));
                    for (BmsYsBillExportInfo bmsYsBillExportInfo : collect.get(1)) {
                        Row r59zz = sheet1.createRow(rowNum[0]++);
                        sumAmountY = sumAmountY.add(bmsYsBillExportInfo.getAmount());
                        sumAmount = sumAmount.add(bmsYsBillExportInfo.getAmount());
                        r59zz.setHeight((short) 500);
                        String[] row59zz = {"", "", "", "", bmsYsBillExportInfo.getItemName(), bmsYsBillExportInfo.getAmount().toString(), "", "", ""};
                        for (int i = 0; i < row59zz.length; i++) {
                            Cell tempCell = r59zz.createCell(i);
                            if (PubNumEnum.five.getIntValue().equals(i)) {
                                if (StrUtil.isEmpty(row59zz[i]) || new BigDecimal(row59zz[i]).compareTo(BigDecimal.ZERO) == 0) {
                                    tempCell.setCellValue("");
                                    tempCell.setCellStyle(commonStyle);
                                } else {
                                    tempCell.setCellValue(new BigDecimal(row59zz[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                                    tempCell.setCellStyle(commonStyle);
                                }
                            } else {
                                tempCell.setCellValue(row59zz[i]);
                                tempCell.setCellStyle(commonStyle);
                            }
                        }
                        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 5, 8));
                    }
                    sheet1.addMergedRegion(new CellRangeAddress(zzrow - 1, rowNum[0] - 1, 2, 3));
                }
                //仓储增值费
                if (collect.get(2) != null && !collect.get(2).isEmpty()) {
                    Row row58z = sheet1.createRow(rowNum[0]++);
                    zzrow = rowNum[0];
                    gdzzrow = gdzzrow == 0 ? rowNum[0] : gdzzrow;
                    String[] row59z = {"其他款项明细", "", "增值服务费部分_仓储", "", "费用项", "金额", "", "", ""};
                    for (int i = 0; i < row59z.length; i++) {
                        Cell tempCell = row58z.createCell(i);
                        tempCell.setCellValue(row59z[i]);
                        tempCell.setCellStyle(i == 0 ? commonStyle2 : commonStyle);
                    }
                    sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 5, 8));
                    for (BmsYsBillExportInfo bmsYsBillExportInfo : collect.get(2)) {
                        Row r59zz = sheet1.createRow(rowNum[0]++);
                        sumAmountC = sumAmountC.add(bmsYsBillExportInfo.getAmount());
                        sumAmount = sumAmount.add(bmsYsBillExportInfo.getAmount());
                        r59zz.setHeight((short) 500);
                        String[] row59zz = {"", "", "", "", bmsYsBillExportInfo.getItemName(), bmsYsBillExportInfo.getAmount().toString(), "", "", ""};
                        for (int i = 0; i < row59zz.length; i++) {
                            Cell tempCell = r59zz.createCell(i);
                            if (PubNumEnum.five.getIntValue().equals(i)) {
                                if (StrUtil.isEmpty(row59zz[i]) || new BigDecimal(row59zz[i]).compareTo(BigDecimal.ZERO) == 0) {
                                    tempCell.setCellValue("");
                                    tempCell.setCellStyle(commonStyle);
                                } else {
                                    tempCell.setCellValue(new BigDecimal(row59zz[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                                    tempCell.setCellStyle(commonStyle);
                                }
                            } else {
                                tempCell.setCellValue(row59zz[i]);
                                tempCell.setCellStyle(commonStyle);
                            }
                        }
                        //合并单元格
                        //sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 2, 3));
                        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 5, 8));
                    }
                    sheet1.addMergedRegion(new CellRangeAddress(zzrow - 1, rowNum[0] - 1, 2, 3));
                }
                sheet1.addMergedRegion(new CellRangeAddress(biegin, rowNum[0] - 1, 0, 1));
            }
        }
        // 费用合计
        Row r58 = sheet1.createRow(rowNum[0]++);
        r58.setHeight((short) 500);
        String[] row58 = {"费用合计:", "", "", "", String.valueOf(sumAmount.add(bmsYsbillmainExportAll.getPsFeeSum()).add(bmsYsbillmainExportAll.getCcFeeSum())), "", "", "", ""};
        for (int i = 0; i < row58.length; i++) {
            Cell tempCell = r58.createCell(i);
            if (PubNumEnum.four.getIntValue().equals(i)) {
                if (StrUtil.isEmpty(row58[i]) || new BigDecimal(row58[i]).compareTo(BigDecimal.ZERO) == 0) {
                    tempCell.setCellValue("");
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellValue(new BigDecimal(row58[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle);
                }
            } else {
                tempCell.setCellValue(row58[i]);
                tempCell.setCellStyle(commonStyle);
            }
        }
        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 0, 3));
        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 4, 8));

        // 第N行
        int ticketStratrowNum = rowNum[0];

        //开单金额
        BigDecimal billSumAmt = bmsYsbillmainExportAll.getAdjustedAmount() != null ? bmsYsbillmainExportAll.getAdjustedAmount() : BigDecimal.ZERO;
        //应收金额
        BigDecimal jeYsAmount = (bmsYsbillmainExportAll.getAdjustedAmount() != null ? bmsYsbillmainExportAll.getAdjustedAmount() : BigDecimal.ZERO).subtract(bmsYsbillmainExportAll.getExceptionFee() != null ? bmsYsbillmainExportAll.getExceptionFee() : BigDecimal.ZERO);
        //实收金额
        BigDecimal jeAdjustedAmount = bmsYsbillmainExportAll.getAdjustedAmount() != null ? bmsYsbillmainExportAll.getAdjustedAmount() : BigDecimal.ZERO;
        //异常赔付
        BigDecimal jeExceptionFee = (bmsYsbillmainExportAll.getPsExceptionFee() != null ? bmsYsbillmainExportAll.getPsExceptionFee() : BigDecimal.ZERO).add(bmsYsbillmainExportAll.getCcExceptionFee() != null ? bmsYsbillmainExportAll.getCcExceptionFee() : BigDecimal.ZERO);
        //开票金额 (判断是否包含客诉金额)
        BigDecimal invoicingAmount = "0".equals(bmsYsbillmainExportAll.getIscontainsExceFee()) ? billSumAmt.add(jeExceptionFee) : billSumAmt;
        List<String> longs = sons.stream().map(String::valueOf).collect(Collectors.toList());
        //增值账单专属逻辑
        BigDecimal decimalY = BigDecimal.ZERO;
        BigDecimal decimalC = BigDecimal.ZERO;
        if (bmsYsbillmainExportAll.getBillTypeStr() != null && bmsYsbillmainExportAll.getBillTypeStr().equals(FIVE)) {
            Map<Integer, List<BmsPubAddedfee>> map = bmsPubAddedfeeService.queryAllPubAddFeeByBillIdBatch(tenantid, longs, 1);
            if (!map.isEmpty()) {
                //运值单
                List<BmsPubAddedfee> addedfeesY = map.get(1);
                if (addedfeesY != null && !addedfeesY.isEmpty()) {
                    decimalY = addedfeesY.stream().map(BmsPubAddedfee::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO,
                            BigDecimal::add);
                }
                //仓值单
                List<BmsPubAddedfee> addedfeesC = map.get(2);
                if (addedfeesC != null && !addedfeesC.isEmpty()) {
                    decimalC = addedfeesC.stream().map(BmsPubAddedfee::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO,
                            BigDecimal::add);
                }
            }
        }
        // 开票信息->循环赋值
        if (clientinfo.getInvoiceMode() != null && clientinfo.getInvoiceMode().equals(InvoiceModeEnum.RADIO.getIntValue())) {
            // 按比例开票
            //注意 按比例开票按照业务逻辑需要按照 应收金额 按照比例取值，客诉金额，实收金额，开票金额按照账单类型不按比例赋值 仓储/运输/仓配
            Row r59 = sheet1.createRow(rowNum[0]++);
            r59.setHeight((short) 500);
            String[] row59 = {"按比例开票", "", "开票项目", "", "开票比例", "应收金额", "客诉理赔费", "实收金额", "开票金额"};
            for (int i = 0; i < row59.length; i++) {
                Cell tempCell = r59.createCell(i);
                tempCell.setCellValue(row59[i]);
                tempCell.setCellStyle(commonStyle2);
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 2, 3));
            String[] ticketFeeNames = {"仓储费", "运输费", "信息费", "平台费", "品牌费", "派送服务费", "供应链服务", "GPS租赁", "装卸服务费", "耗材费", "空运费"};
            List<OtherFeeInfo> ticketFeeInfoList = new ArrayList<>();
            BigDecimal sumAmt = new BigDecimal("0");
            BigDecimal sumAmtnot = new BigDecimal("0");
            BigDecimal invoicingsumAmt = new BigDecimal("0");
            for (String name : ticketFeeNames) {
                OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
                otherFeeInfo.setOtherName(name);
                switch (name) {
                    case "运输费":
                        otherFeeInfo.setOther1((BigDecimal.valueOf(clientinfo.getFreightRatio()).multiply(new BigDecimal(100))) + "%");
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getBillState() >= 4 ? bmsYsbillmainExportAll.getFreightFee() : new BigDecimal(0));
                        otherFeeInfo.setOtherFee1(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getFreightRatio() == null ? 0 : clientinfo.getFreightRatio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee2(jeAdjustedAmount.multiply(BigDecimal.valueOf(clientinfo.getFreightRatio() == null ? 0 : clientinfo.getFreightRatio())).setScale(2, RoundingMode.HALF_UP));
                        if (bmsYsbillmainExportAll.getBillType() != null && bmsYsbillmainExportAll.getBillType().equals(2)) { //运输账单
                            otherFeeInfo.setOtherFee3(jeExceptionFee.setScale(2, RoundingMode.HALF_UP));
                        } else if (bmsYsbillmainExportAll.getBillType() != null && bmsYsbillmainExportAll.getBillType().equals(4)) {//仓配账单
                            otherFeeInfo.setOtherFee3(bmsYsbillmainExportAll.getPsExceptionFee() != null ? bmsYsbillmainExportAll.getPsExceptionFee() : BigDecimal.ZERO);
                        } else {  //其余单
                            otherFeeInfo.setOtherFee3(BigDecimal.ZERO);
                        }
                        sumAmtnot = sumAmtnot.add(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getFreightRatio() == null ? 0 : clientinfo.getFreightRatio())).setScale(2, RoundingMode.HALF_UP));
                        invoicingsumAmt = invoicingsumAmt.add(otherFeeInfo.getOtherFee());
                        sumAmt = sumAmt.add(billSumAmt.multiply(BigDecimal.valueOf(clientinfo.getFreightRatio() == null ? 0 : clientinfo.getFreightRatio())).setScale(2, RoundingMode.HALF_UP));
                        break;
                    case "仓储费":
                        otherFeeInfo.setOther1((BigDecimal.valueOf(clientinfo.getStorageRatio()).multiply(new BigDecimal(100))) + "%");
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getBillState() >= 4 ? bmsYsbillmainExportAll.getStoragefee() : new BigDecimal(0));
                        otherFeeInfo.setOtherFee1(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getStorageRatio() == null ? 0 : clientinfo.getStorageRatio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee2(jeAdjustedAmount.multiply(BigDecimal.valueOf(clientinfo.getStorageRatio() == null ? 0 : clientinfo.getStorageRatio())).setScale(2, RoundingMode.HALF_UP));
                        if (bmsYsbillmainExportAll.getBillType() != null && bmsYsbillmainExportAll.getBillType().equals(3)) { //仓储账单
                            otherFeeInfo.setOtherFee3(jeExceptionFee.setScale(2, RoundingMode.HALF_UP));
                        } else if (bmsYsbillmainExportAll.getBillType() != null && bmsYsbillmainExportAll.getBillType().equals(4)) { //仓配账单
                            otherFeeInfo.setOtherFee3(bmsYsbillmainExportAll.getCcExceptionFee() != null ? bmsYsbillmainExportAll.getCcExceptionFee() : BigDecimal.ZERO);
                        } else { //其余单
                            otherFeeInfo.setOtherFee3(BigDecimal.ZERO);
                        }
                        sumAmtnot = sumAmtnot.add(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getStorageRatio() == null ? 0 : clientinfo.getStorageRatio())).setScale(2, RoundingMode.HALF_UP));
                        invoicingsumAmt = invoicingsumAmt.add(otherFeeInfo.getOtherFee());
                        sumAmt = sumAmt.add(billSumAmt.multiply(BigDecimal.valueOf(clientinfo.getStorageRatio() == null ? 0 : clientinfo.getStorageRatio())).setScale(2, RoundingMode.HALF_UP));
                        break;
                    case "信息费":
                        otherFeeInfo.setOther1((BigDecimal.valueOf(clientinfo.getMessageRatio()).multiply(new BigDecimal(100))) + "%");
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getBillState() >= 4 ? bmsYsbillmainExportAll.getMessageFee() : new BigDecimal(0));
                        otherFeeInfo.setOtherFee1(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getMessageRatio() == null ? 0 : clientinfo.getMessageRatio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee2(jeAdjustedAmount.multiply(BigDecimal.valueOf(clientinfo.getMessageRatio() == null ? 0 : clientinfo.getMessageRatio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee3(BigDecimal.ZERO);
                        sumAmtnot = sumAmtnot.add(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getMessageRatio() == null ? 0 : clientinfo.getMessageRatio())).setScale(2, RoundingMode.HALF_UP));
                        invoicingsumAmt = invoicingsumAmt.add(otherFeeInfo.getOtherFee());
                        sumAmt = sumAmt.add(billSumAmt.multiply(BigDecimal.valueOf(clientinfo.getMessageRatio() == null ? 0 : clientinfo.getMessageRatio())).setScale(2, RoundingMode.HALF_UP));
                        break;
                    case "平台费":
                        otherFeeInfo.setOther1((BigDecimal.valueOf(clientinfo.getPlatformRatio()).multiply(new BigDecimal(100))) + "%");
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getBillState() >= 4 ? bmsYsbillmainExportAll.getPlatformFee() : new BigDecimal(0));
                        otherFeeInfo.setOtherFee1(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getPlatformRatio() == null ? 0 : clientinfo.getPlatformRatio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee2(jeAdjustedAmount.multiply(BigDecimal.valueOf(clientinfo.getPlatformRatio() == null ? 0 : clientinfo.getPlatformRatio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee3(BigDecimal.ZERO);
                        sumAmtnot = sumAmtnot.add(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getPlatformRatio() == null ? 0 : clientinfo.getPlatformRatio())).setScale(2, RoundingMode.HALF_UP));
                        invoicingsumAmt = invoicingsumAmt.add(otherFeeInfo.getOtherFee());
                        sumAmt = sumAmt.add(billSumAmt.multiply(BigDecimal.valueOf(clientinfo.getPlatformRatio() == null ? 0 : clientinfo.getPlatformRatio())).setScale(2, RoundingMode.HALF_UP));
                        break;
                    case "品牌费":
                        otherFeeInfo.setOther1((BigDecimal.valueOf(clientinfo.getBrandRatio()).multiply(new BigDecimal(100))) + "%");
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getBillState() >= 4 ? bmsYsbillmainExportAll.getBrandFee() : new BigDecimal(0));
                        otherFeeInfo.setOtherFee1(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getBrandRatio() == null ? 0 : clientinfo.getBrandRatio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee2(jeAdjustedAmount.multiply(BigDecimal.valueOf(clientinfo.getBrandRatio() == null ? 0 : clientinfo.getBrandRatio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee3(BigDecimal.ZERO);
                        sumAmtnot = sumAmtnot.add(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getBrandRatio() == null ? 0 : clientinfo.getBrandRatio())).setScale(2, RoundingMode.HALF_UP));
                        invoicingsumAmt = invoicingsumAmt.add(otherFeeInfo.getOtherFee());
                        sumAmt = sumAmt.add(billSumAmt.multiply(BigDecimal.valueOf(clientinfo.getBrandRatio() == null ? 0 : clientinfo.getBrandRatio())).setScale(2, RoundingMode.HALF_UP));
                        break;
                    case "派送服务费":
                        otherFeeInfo.setOther1((BigDecimal.valueOf(clientinfo.getDeliveryRatio()).multiply(new BigDecimal(100))) + "%");
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getBillState() >= 4 ? bmsYsbillmainExportAll.getDeliveryFee() : new BigDecimal(0));
                        otherFeeInfo.setOtherFee1(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getDeliveryRatio() == null ? 0 : clientinfo.getDeliveryRatio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee2(jeAdjustedAmount.multiply(BigDecimal.valueOf(clientinfo.getDeliveryRatio() == null ? 0 : clientinfo.getDeliveryRatio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee3(BigDecimal.ZERO);
                        sumAmtnot = sumAmtnot.add(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getDeliveryRatio() == null ? 0 : clientinfo.getDeliveryRatio())).setScale(2, RoundingMode.HALF_UP));
                        invoicingsumAmt = invoicingsumAmt.add(otherFeeInfo.getOtherFee());
                        sumAmt = sumAmt.add(billSumAmt.multiply(BigDecimal.valueOf(clientinfo.getDeliveryRatio() == null ? 0 : clientinfo.getDeliveryRatio())).setScale(2, RoundingMode.HALF_UP));
                        break;
                    case "供应链服务":
                        otherFeeInfo.setOther1((BigDecimal.valueOf(clientinfo.getScmRadio()).multiply(new BigDecimal(100))) + "%");
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getBillState() >= 4 ? bmsYsbillmainExportAll.getScmFee() : new BigDecimal(0));
                        otherFeeInfo.setOtherFee1(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getScmRadio() == null ? 0 : clientinfo.getScmRadio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee2(jeAdjustedAmount.multiply(BigDecimal.valueOf(clientinfo.getScmRadio() == null ? 0 : clientinfo.getScmRadio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee3(BigDecimal.ZERO);
                        sumAmtnot = sumAmtnot.add(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getScmRadio() == null ? 0 : clientinfo.getScmRadio())).setScale(2, RoundingMode.HALF_UP));
                        invoicingsumAmt = invoicingsumAmt.add(otherFeeInfo.getOtherFee());
                        sumAmt = sumAmt.add(billSumAmt.multiply(BigDecimal.valueOf(clientinfo.getScmRadio() == null ? 0 : clientinfo.getScmRadio())).setScale(2, RoundingMode.HALF_UP));
                        break;
                    case "GPS租赁":
                        otherFeeInfo.setOther1((BigDecimal.valueOf(clientinfo.getGpsRadio()).multiply(new BigDecimal(100))) + "%");
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getBillState() >= 4 ? bmsYsbillmainExportAll.getGpsFee() : new BigDecimal(0));
                        otherFeeInfo.setOtherFee1(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getGpsRadio() == null ? 0 : clientinfo.getGpsRadio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee2(jeAdjustedAmount.multiply(BigDecimal.valueOf(clientinfo.getGpsRadio() == null ? 0 : clientinfo.getGpsRadio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee3(BigDecimal.ZERO);
                        sumAmtnot = sumAmtnot.add(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getGpsRadio() == null ? 0 : clientinfo.getGpsRadio())).setScale(2, RoundingMode.HALF_UP));
                        invoicingsumAmt = invoicingsumAmt.add(otherFeeInfo.getOtherFee());
                        sumAmt = sumAmt.add(billSumAmt.multiply(BigDecimal.valueOf(clientinfo.getGpsRadio() == null ? 0 : clientinfo.getGpsRadio())).setScale(2, RoundingMode.HALF_UP));
                        break;
                    case "装卸服务费":
                        otherFeeInfo.setOther1((BigDecimal.valueOf(clientinfo.getUnloadRadio()).multiply(new BigDecimal(100))) + "%");
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getBillState() >= 4 ? bmsYsbillmainExportAll.getUnloadFee() : new BigDecimal(0));
                        otherFeeInfo.setOtherFee1(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getUnloadRadio() == null ? 0 : clientinfo.getUnloadRadio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee2(jeAdjustedAmount.multiply(BigDecimal.valueOf(clientinfo.getUnloadRadio() == null ? 0 : clientinfo.getUnloadRadio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee3(BigDecimal.ZERO);
                        sumAmtnot = sumAmtnot.add(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getUnloadRadio() == null ? 0 : clientinfo.getUnloadRadio())).setScale(2, RoundingMode.HALF_UP));
                        invoicingsumAmt = invoicingsumAmt.add(otherFeeInfo.getOtherFee());
                        sumAmt = sumAmt.add(billSumAmt.multiply(BigDecimal.valueOf(clientinfo.getUnloadRadio() == null ? 0 : clientinfo.getUnloadRadio())).setScale(2, RoundingMode.HALF_UP));
                        break;
                    case "耗材费":
                        otherFeeInfo.setOther1((BigDecimal.valueOf(clientinfo.getConsumablesRadio()).multiply(new BigDecimal(100))) + "%");
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getBillState() >= 4 ? bmsYsbillmainExportAll.getConsumablesFee() : new BigDecimal(0));
                        otherFeeInfo.setOtherFee1(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getConsumablesRadio() == null ? 0 : clientinfo.getConsumablesRadio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee2(jeAdjustedAmount.multiply(BigDecimal.valueOf(clientinfo.getConsumablesRadio() == null ? 0 : clientinfo.getConsumablesRadio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee3(BigDecimal.ZERO);
                        sumAmtnot = sumAmtnot.add(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getConsumablesRadio() == null ? 0 : clientinfo.getConsumablesRadio())).setScale(2, RoundingMode.HALF_UP));
                        invoicingsumAmt = invoicingsumAmt.add(otherFeeInfo.getOtherFee());
                        sumAmt = sumAmt.add(billSumAmt.multiply(BigDecimal.valueOf(clientinfo.getConsumablesRadio() == null ? 0 : clientinfo.getConsumablesRadio())).setScale(2, RoundingMode.HALF_UP));
                        break;
                    case "空运费":
                        otherFeeInfo.setOther1((BigDecimal.valueOf(clientinfo.getAirtransportRadio()).multiply(new BigDecimal(100))) + "%");
                        otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getBillState() >= 4 ? bmsYsbillmainExportAll.getAirtransportFee() : new BigDecimal(0));
                        otherFeeInfo.setOtherFee1(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getAirtransportRadio() == null ? 0 : clientinfo.getAirtransportRadio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee2(jeAdjustedAmount.multiply(BigDecimal.valueOf(clientinfo.getAirtransportRadio() == null ? 0 : clientinfo.getAirtransportRadio())).setScale(2, RoundingMode.HALF_UP));
                        otherFeeInfo.setOtherFee3(BigDecimal.ZERO);
                        sumAmtnot = sumAmtnot.add(jeYsAmount.multiply(BigDecimal.valueOf(clientinfo.getAirtransportRadio() == null ? 0 : clientinfo.getAirtransportRadio())).setScale(2, RoundingMode.HALF_UP));
                        invoicingsumAmt = invoicingsumAmt.add(otherFeeInfo.getOtherFee());
                        sumAmt = sumAmt.add(billSumAmt.multiply(BigDecimal.valueOf(clientinfo.getAirtransportRadio() == null ? 0 : clientinfo.getAirtransportRadio())).setScale(2, RoundingMode.HALF_UP));
                        break;
                }
                ticketFeeInfoList.add(otherFeeInfo);
            }
            for (OtherFeeInfo ticketFeeInfo : ticketFeeInfoList) {
                Row r37n2 = sheet1.createRow(rowNum[0]++);
                r37n2.setHeight((short) 500);
                String[] row37n2 = {"", "", ticketFeeInfo.getOtherName(), "", ticketFeeInfo.getOther1(),
                        String.valueOf(ticketFeeInfo.getOtherFee1()),
                        String.valueOf(ticketFeeInfo.getOtherFee3()),
                        String.valueOf(ticketFeeInfo.getOtherFee2()),
                        bmsYsbillmainExportAll.getBillState() >= 4 ? String.valueOf(ticketFeeInfo.getOtherFee()) : "0"};
                for (int i = 0; i < row37n2.length; i++) {
                    Cell tempCell = r37n2.createCell(i);
                    if (i >= 5) {
                        if (StrUtil.isEmpty(row37n2[i]) || new BigDecimal(row37n2[i]).compareTo(BigDecimal.ZERO) == 0) {
                            tempCell.setCellValue("");
                            tempCell.setCellStyle(commonStyle);
                        } else {
                            tempCell.setCellValue(new BigDecimal(row37n2[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                            commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                            tempCell.setCellStyle(commonStyle);
                        }
                    } else {
                        tempCell.setCellValue(row37n2[i]);
                        tempCell.setCellStyle(commonStyle);
                    }
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 2, 3));
            }
            sheet1.addMergedRegion(new CellRangeAddress(ticketStratrowNum, rowNum[0] - 1, 0, 1));
            // 第N行
            Row r62 = sheet1.createRow(rowNum[0]++);
            r62.setHeight((short) 500);

            String[] row62 = {"总费用合计（元）:", "", "", "", "应收金额:" + sumAmtnot.setScale(2, RoundingMode.HALF_UP).toString() + "，开票比例计算汇总：" + invoicingsumAmt.setScale(2, RoundingMode.HALF_UP).toString(), "", "", "", ""};
            for (int i = 0; i < row62.length; i++) {
                Cell tempCell = r62.createCell(i);
                tempCell.setCellValue(row62[i]);
                if (i == 0) {
                    tempCell.setCellStyle(commonStyle2);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 0, 3));
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 4, 8));
        } else {
            /* 按金额开票*/
            Row r59 = sheet1.createRow(rowNum[0]++);
            r59.setHeight((short) 500);
            String[] row59 = {"按金额开票", "", "开票项目", "", "税率", "应收金额", "客诉理赔费", "实收金额", "开票金额"};
            if (bmsYsbillmainExportAll.getBillTypeStr() != null && bmsYsbillmainExportAll.getBillTypeStr().equals(FIVE)) {
                row59 = new String[]{"按金额开票", "", "开票项目", "", "税率", "应收金额", "实收金额", "开票金额", ""};
            }
            for (int i = 0; i < row59.length; i++) {
                Cell tempCell = r59.createCell(i);
                tempCell.setCellValue(row59[i]);
                tempCell.setCellStyle(commonStyle2);
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 2, 3));
            if (bmsYsbillmainExportAll.getBillTypeStr() != null && bmsYsbillmainExportAll.getBillTypeStr().equals(FIVE)) {
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 7, 8));
            }
            Row r60 = sheet1.createRow(rowNum[0]++);
            r60.setHeight((short) 500);
            String yskpAmt = clientinfo.getTaxRate() == null ? "0" : String.valueOf(bmsYsbillmainExportAll.getPsFeeSum().multiply(clientinfo.getTaxRate()).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP));
            String cckpAmt = clientinfo.getStorageTaxRate() == null ? "0" : String.valueOf(bmsYsbillmainExportAll.getCcFeeSum().multiply(clientinfo.getStorageTaxRate()).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP));

            // 仓储增值费或者仓配账单
            BigDecimal ccadsumAmt = (bmsYsbillmainExportAll.getBillType().equals(BillTypeEnum.STOCK_BILL.getIntValue()) || bmsYsbillmainExportAll.getBillType().equals(BillTypeEnum.TRANSTOCK_BILL.getIntValue())) ? sumAmountC : new BigDecimal(0);
            //运输增值费
            BigDecimal ysadsumAmt = bmsYsbillmainExportAll.getBillType() != 3 ? sumAmountY : new BigDecimal(0);
            //应收金额=不含客诉(异常赔付费)，按照开票比例计算得来:
            String ysYsAmount = "0";
            //实收金额=包含客诉(异常赔付费)费用:
            String ysSsAmount = "0";
            //客诉金=异常赔付金额
            String ysYsExceptionFee = "0";

            //客诉金=异常赔付金额
            String invoicingAmt = "0";

            //账单类型为运输单
            ysYsAmount = bmsYsbillmainExportAll.getPsFeeSumAll() == null ? "0" : ysadsumAmt.add(bmsYsbillmainExportAll.getPsFeeSumAll()).toString();
            ysSsAmount = bmsYsbillmainExportAll.getPsFeeSum() == null ? "0" : ysadsumAmt.add(bmsYsbillmainExportAll.getPsFeeSum()).toString();
            ysYsExceptionFee = bmsYsbillmainExportAll.getPsExceptionFee() == null ? "0" : bmsYsbillmainExportAll.getPsExceptionFee().toString();
            invoicingAmt = "0".equals(bmsYsbillmainExportAll.getIscontainsExceFee()) ? new BigDecimal(ysYsAmount).add(new BigDecimal(ysYsExceptionFee)).toString() : ysYsAmount;

            //TODO 开票金额:字段取决于“开票申请”是否包含“客诉部分”赋值逻辑:开要金需待开票申请后才赋值。(目前该逻辑还未实现,暂时赋值 0)
            String[] row60 = {"", "", "运输开票金额", "",
                    clientinfo.getTaxRate() == null ? "0" : clientinfo.getTaxRate() + "%",
                    ysYsAmount,
                    ysYsExceptionFee,
                    ysSsAmount,
                    bmsYsbillmainExportAll.getBillState() >= 4 ? (bmsYsbillmainExportAll.getFreightFee() != null ? bmsYsbillmainExportAll.getFreightFee().toString() : "0") : "0"};
            if (bmsYsbillmainExportAll.getBillTypeStr() != null && bmsYsbillmainExportAll.getBillTypeStr().equals(FIVE)) {
                row60 = new String[]{"", "", "运输开票金额", "",
                        clientinfo.getTaxRate() == null ? "0" : clientinfo.getTaxRate() + "%",
                        decimalY.toString(),
                        decimalY.toString(),
                        decimalY.toString(), ""};
            }

            for (int i = 0; i < row60.length; i++) {
                Cell tempCell = r60.createCell(i);
                if (i >= 5) {
                    if (StrUtil.isEmpty(row60[i]) || new BigDecimal(row60[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle2);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row60[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle2.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle2);
                    }
                } else {
                    tempCell.setCellValue(row60[i]);
                    tempCell.setCellStyle(commonStyle2);
                }

            }
            if (bmsYsbillmainExportAll.getBillTypeStr() != null && bmsYsbillmainExportAll.getBillTypeStr().equals(FIVE)) {
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 7, 8));
            }

            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 2, 3));
            //应收金额=不含客诉(异常赔付费)，按照开票比例计算得来:
            String ccYsAmount = "0";
            //实收金额=包含客诉(异常赔付费)费用:
            String ccSsAmount = "0";
            //客诉金=异常赔付金额
            String ccYsExceptionFee = "0";

            String ccResponsibleMoney = "0";

            //账单类型为仓储单
            ccYsAmount = bmsYsbillmainExportAll.getCcFeeSumAll() == null ? "0" : ccadsumAmt.add(bmsYsbillmainExportAll.getCcFeeSumAll()).toString();
            ccSsAmount = bmsYsbillmainExportAll.getCcFeeSum() == null ? "0" : ccadsumAmt.add(bmsYsbillmainExportAll.getCcFeeSum()).toString();
            ccYsExceptionFee = bmsYsbillmainExportAll.getCcExceptionFee() == null ? "0" : bmsYsbillmainExportAll.getCcExceptionFee().toString();
            invoicingAmt = "0".equals(bmsYsbillmainExportAll.getIscontainsExceFee()) ? new BigDecimal(ccYsAmount).add(new BigDecimal(ccYsExceptionFee)).toString() : ccYsAmount;

            Row r61 = sheet1.createRow(rowNum[0]++);
            r60.setHeight((short) 500);
            String[] row61 = {"", "", "仓储开票金额", "",
                    clientinfo.getStorageTaxRate() == null ? "0" : clientinfo.getStorageTaxRate() + "%",
                    ccYsAmount,
                    ccYsExceptionFee,
                    ccSsAmount,
                    bmsYsbillmainExportAll.getBillState() >= 4 ? (bmsYsbillmainExportAll.getStoragefee() != null ? bmsYsbillmainExportAll.getStoragefee().toString() : "0") : "0"};
            if (bmsYsbillmainExportAll.getBillTypeStr() != null && bmsYsbillmainExportAll.getBillTypeStr().equals(FIVE)) {
                row61 = new String[]{"", "", "仓储开票金额", "",
                        clientinfo.getStorageTaxRate() == null ? "0" : clientinfo.getStorageTaxRate() + "%",
                        decimalC.toString(),
                        decimalC.toString(),
                        decimalC.toString(), ""};
            }
            for (int i = 0; i < row61.length; i++) {
                Cell tempCell = r61.createCell(i);
                if (i >= 5) {
                    if (StrUtil.isEmpty(row61[i]) || new BigDecimal(row61[i]).compareTo(BigDecimal.ZERO) == 0) {
                        tempCell.setCellValue("");
                        tempCell.setCellStyle(commonStyle2);
                    } else {
                        tempCell.setCellValue(new BigDecimal(row61[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        commonStyle2.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                        tempCell.setCellStyle(commonStyle2);
                    }
                } else {
                    tempCell.setCellValue(row61[i]);
                    tempCell.setCellStyle(commonStyle2);
                }
            }
            if (bmsYsbillmainExportAll.getBillTypeStr() != null && bmsYsbillmainExportAll.getBillTypeStr().equals(FIVE)) {
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 7, 8));
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 2, 3));
            sheet1.addMergedRegion(new CellRangeAddress(ticketStratrowNum, rowNum[0] - 1, 0, 1));
        }


        // 第N行
        Row r63 = sheet1.createRow(rowNum[0]++);
        r63.setHeight((short) 500);
        String[] row63 = {"审批部门", "", "签批意见", "", "", "", "", "", ""};
        for (int i = 0; i < row63.length; i++) {
            Cell tempCell = r63.createCell(i);
            tempCell.setCellValue(row63[i]);
            tempCell.setCellStyle(commonStyle2);
        }
        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 0, 1));
        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 2, 8));

        // 客户确认
        int auditRowNum = rowNum[0];
        Row r64 = sheet1.createRow(rowNum[0]++);
        r64.setHeight((short) 500);
        String[] row64 = {"客户确认", "", "", "", "", "", "", "", ""};
        for (int i = 0; i < row64.length; i++) {
            Cell tempCell = r64.createCell(i);
            tempCell.setCellValue(row64[i]);
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        Row r65 = sheet1.createRow(rowNum[0]++);
        r65.setHeight((short) 500);
        String[] row65 = {"", "", "", "", "", "", "", "", ""};
        for (int i = 0; i < row65.length; i++) {
            Cell tempCell = r65.createCell(i);
            tempCell.setCellValue(row65[i]);
            tempCell.setCellStyle(commonStyle);
        }
        Row r66 = sheet1.createRow(rowNum[0]++);
        r66.setHeight((short) 500);
        String[] row66 = {"", "", "", "", "", "", "", "", ""};
        for (int i = 0; i < row66.length; i++) {
            Cell tempCell = r66.createCell(i);
            tempCell.setCellValue(row66[i]);
            tempCell.setCellStyle(commonStyle);
        }
        //合并单元格·
        sheet1.addMergedRegion(new CellRangeAddress(auditRowNum, rowNum[0] - 1, 0, 1));
        sheet1.addMergedRegion(new CellRangeAddress(auditRowNum, rowNum[0] - 1, 2, 8));
        return hssfWorkbook;
    }

    /**
     * 第二个sheet 存储费主信息
     */
    public SXSSFWorkbook storageSummary(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid = RequestContext.getTenantId();
        // 第2个sheet
        Sheet sheet2 = hssfWorkbook.createSheet("存储费主信息");
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        // 背景色的设定
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum2 = {0};
        // 创建数据行，获取入库明细
        List<BmsYsBillExportInfo> stkList = ysState.getStorageByBillId(tenantid, ids);
        BigDecimal sumAmt = new BigDecimal("0");
        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd");
        BigDecimal[] amtf = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        Boolean[] fare = new Boolean[23];
        // 判断是否要隐藏列
        for (BmsYsBillExportInfo bmsYsBillExportInfo : stkList) {
            amtf[0] = amtf[0].add(bmsYsBillExportInfo.getFreight());
            amtf[1] = amtf[1].add(bmsYsBillExportInfo.getUltrafarFee());
            amtf[2] = amtf[2].add(bmsYsBillExportInfo.getSuperframesFee());
            amtf[3] = amtf[3].add(bmsYsBillExportInfo.getExcessFee());
            amtf[4] = amtf[4].add(bmsYsBillExportInfo.getDeliveryFee());
            amtf[5] = amtf[5].add(bmsYsBillExportInfo.getReturnFee());
            amtf[6] = amtf[6].add(bmsYsBillExportInfo.getShortbargeFee());
            amtf[7] = amtf[7].add(bmsYsBillExportInfo.getExceptionFee());
            amtf[8] = amtf[8].add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()));
            amtf[9] = amtf[9].add(BigDecimal.valueOf(StrUtil.isEmpty(bmsYsBillExportInfo.getAdjustRemark()) ? 0 : 1));
            amtf[10] = amtf[10].add(bmsYsBillExportInfo.getOtherCost1());
            amtf[11] = amtf[11].add(bmsYsBillExportInfo.getOtherCost2());
            amtf[12] = amtf[12].add(bmsYsBillExportInfo.getOtherCost3());
            amtf[13] = amtf[13].add(bmsYsBillExportInfo.getOtherCost4());
            amtf[14] = amtf[14].add(bmsYsBillExportInfo.getOtherCost5());
            amtf[15] = amtf[15].add(bmsYsBillExportInfo.getOtherCost6());
            amtf[16] = amtf[16].add(bmsYsBillExportInfo.getOtherCost7());
            amtf[17] = amtf[17].add(bmsYsBillExportInfo.getOtherCost8());
            amtf[18] = amtf[18].add(bmsYsBillExportInfo.getOtherCost9());
            amtf[19] = amtf[19].add(bmsYsBillExportInfo.getOtherCost10());
            amtf[20] = amtf[20].add(bmsYsBillExportInfo.getOtherCost11());
            amtf[21] = amtf[21].add(bmsYsBillExportInfo.getOtherCost12());
            amtf[22] = amtf[22].add(BigDecimal.valueOf(bmsYsBillExportInfo.getSumAmt()));
        }
        int fi = 0;
        for (BigDecimal bigDecimal : amtf) {
            fare[fi] = bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0;
            fi++;
        }
        //设置列宽
        for (int i = 0; i < fare.length; i++) {
            if (fare[i] != null && fare[i]) {
                sheet2.setColumnWidth(i + 19, 0);
            }
        }
        //第1行
        Row s2r2 = sheet2.createRow(rowNum2[0]++);
        s2r2.setHeight((short) 500);
        String[] s2row2 = {"日期", "库存单号", "计费类型", "费用单号", "费用维度", "温区", "单据所属", "仓库编码", "仓库名称",
                "日结存库存数", "日结存托数", "托规", "总箱数", "总件数", "总体积(m³)",
                "总重量（kg）", "计费人", "计费备注", "备注",
                "存储费", "管理处置费", "整箱分拣费", "拆零分拣费",
                "装卸费", "制单费", "操作费", "客诉理赔费", "调账费", "调账备注",
                "otherCost1", "otherCost2", "otherCost3",
                "otherCost4", "otherCost5", "otherCost6", "otherCost7", "otherCost8",
                "otherCost9", "otherCost10", "otherCost11", "otherCost12",
                "总费用"
        };
        Map<String, SysDept> companyMap = new HashMap<>();
        if (CollUtil.isNotEmpty(stkList)) {
            companyMap = textConversionUtil.getCompanyMap("1", stkList.stream().filter(e -> e.getCompanyId() != null).map(e -> e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        }
        //所有费用列的下标(处理保留2位小数的问题)
        Set<Integer> feeColumns = new HashSet<>(Arrays.asList(19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41));
        for (int i = 0; i < s2row2.length; i++) {
            String name = s2row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            if (StrUtil.isNotEmpty(dicMap.get("ys_storage_othercost" + name))) {
                otherFeeInfo.setOtherName(dicMap.get("ys_storage_othercost" + name));
            }
            switch (name) {
                case "客诉理赔费":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcExceptionFee());
                    break;
                case "otherCost1":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost1());
                    break;
                case "otherCost2":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost2());
                    break;
                case "otherCost3":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost3());
                    break;
                case "otherCost4":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost4());
                    break;
                case "otherCost5":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost5());
                    break;
                case "otherCost6":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost6());
                    break;
                case "otherCost7":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost7());
                    break;
                case "otherCost8":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost8());
                    break;
                case "otherCost9":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost9());
                    break;
                case "otherCost10":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost10());
                    break;
                case "otherCost11":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost11());
                    break;
                case "otherCost12":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost12());
                    break;
                default:
                    otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
            }
            s2row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s2row2.length; i++) {
            Cell tempCell = s2r2.createCell(i);
            tempCell.setCellValue(s2row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        BigDecimal[] amt = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        String lastId = "";
        int row = 1;
        for (int s = 0; s < stkList.size(); s++) {
            BmsYsBillExportInfo bmsYsBillExportInfo = stkList.get(s);
            Row s2r3 = sheet2.createRow(rowNum2[0]++);
            boolean f = s <= 0 || s >= stkList.size() || !stkList.get(s).getId().equals(stkList.get(s - 1).getId());
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s2row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(bmsYsBillExportInfo.getInstorageTime() != null ? sml.format(bmsYsBillExportInfo.getInstorageTime()) : ""); //日期
                        break;
                    case 1:
                        tempCell.setCellValue(bmsYsBillExportInfo.getStockCode()); //库存单号
                        break;
                    case 2:
                        tempCell.setCellValue(bmsYsBillExportInfo.getChargeTypeName()); //计费类型
                        break;
                    case 3:
                        tempCell.setCellValue(bmsYsBillExportInfo.getExpensesCode()); //费用单号
                        break;
                    case 4:
                        tempCell.setCellValue(bmsYsBillExportInfo.getCostDimensionName()); //费用维度
                        break;
                    case 5:
                        tempCell.setCellValue(bmsYsBillExportInfo.getTemperatureName()); //温区
                        break;
                    case 6:
                        if (bmsYsBillExportInfo.getCompanyId() != null && companyMap.containsKey(bmsYsBillExportInfo.getCompanyId().toString())) {
                            bmsYsBillExportInfo.setCompanyName(companyMap.get(bmsYsBillExportInfo.getCompanyId().toString()).getDeptName());
                        }
                        tempCell.setCellValue(bmsYsBillExportInfo.getCompanyName()); //单据所属
                        break;
                    case 7:
                        tempCell.setCellValue(bmsYsBillExportInfo.getWarehouseCode()); //仓库编码
                        break;
                    case 8:
                        tempCell.setCellValue(bmsYsBillExportInfo.getWarehouseName()); //仓库名称
                        break;
                    case 9:
                        tempCell.setCellValue(bmsYsBillExportInfo.getAqty() == null ? 0 : bmsYsBillExportInfo.getAqty().doubleValue());  //日结存库存数
                        break;
                    case 10:
                        tempCell.setCellValue(bmsYsBillExportInfo.getPalletNumber() == null ? 0 : bmsYsBillExportInfo.getPalletNumber().doubleValue()); //日结存托数
                        break;
                    case 11:
                        tempCell.setCellValue(bmsYsBillExportInfo.getPalletRuler() == null ? 0 : bmsYsBillExportInfo.getPalletRuler().doubleValue()); //托规
                        break;
                    case 12:
                        tempCell.setCellValue(bmsYsBillExportInfo.getTotalBoxes() == null ? 0 : bmsYsBillExportInfo.getTotalBoxes().doubleValue()); //总箱数
                        break;
                    case 13:
                        tempCell.setCellValue(bmsYsBillExportInfo.getTotalNumber() == null ? 0 : bmsYsBillExportInfo.getTotalNumber().doubleValue()); //总件数
                        break;
                    case 14:
                        tempCell.setCellValue(bmsYsBillExportInfo.getTotalVolume() == null ? 0 : bmsYsBillExportInfo.getTotalVolume().doubleValue()); //总体积
                        break;
                    case 15:
                        tempCell.setCellValue(bmsYsBillExportInfo.getTotalWeight() == null ? 0 : bmsYsBillExportInfo.getTotalWeight().doubleValue()); //总重量
                        break;
                    case 16:
                        tempCell.setCellValue(bmsYsBillExportInfo.getOperBy()); //计费人
                        break;
                    case 17:
                        tempCell.setCellValue(bmsYsBillExportInfo.getAutomaticBillingRemark()); //计费备注
                        break;
                    case 18:
                        tempCell.setCellValue(bmsYsBillExportInfo.getRemarks()); //计费备注
                        break;
                    case 19:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getFreight() == null ? 0 : bmsYsBillExportInfo.getFreight().setScale(2, RoundingMode.HALF_UP).doubleValue()); //存储费
                        break;
                    case 20:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getUltrafarFee() == null ? 0 : bmsYsBillExportInfo.getUltrafarFee().setScale(2, RoundingMode.HALF_UP).doubleValue()); //管理处置费
                        break;
                    case 21:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getSuperframesFee() == null ? 0 : bmsYsBillExportInfo.getSuperframesFee().setScale(2, RoundingMode.HALF_UP).doubleValue()); //整箱分拣费
                        break;
                    case 22:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getExcessFee() == null ? 0 : bmsYsBillExportInfo.getExcessFee().setScale(2, RoundingMode.HALF_UP).doubleValue()); //拆零分拣费
                        break;
                    case 23:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getDeliveryFee() == null ? 0 : bmsYsBillExportInfo.getDeliveryFee().setScale(2, RoundingMode.HALF_UP).doubleValue()); //装卸费
                        break;
                    case 24:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getReturnFee() == null ? 0 : bmsYsBillExportInfo.getReturnFee().setScale(2, RoundingMode.HALF_UP).doubleValue()); //制单费
                        break;
                    case 25:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getShortbargeFee() == null ? 0 : bmsYsBillExportInfo.getShortbargeFee().setScale(2, RoundingMode.HALF_UP).doubleValue()); //操作费
                        break;
                    case 26:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getExceptionFee() == null ? 0 : bmsYsBillExportInfo.getExceptionFee().setScale(2, RoundingMode.HALF_UP).doubleValue()); // 异常赔付费
                        break;
                    case 27:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getAdjustFee() == null ? 0 : BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()).setScale(2, RoundingMode.HALF_UP).doubleValue()); //调账费
                        break;
                    case 28:
                        tempCell.setCellValue(bmsYsBillExportInfo.getAdjustRemark()); //调账备注
                        break;
                    case 29:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost1() == null ? 0 : bmsYsBillExportInfo.getOtherCost1().setScale(2, RoundingMode.HALF_UP).doubleValue()); //缠膜费
                        break;
                    case 30:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost2() == null ? 0 : bmsYsBillExportInfo.getOtherCost2().setScale(2, RoundingMode.HALF_UP).doubleValue()); //泡沫箱
                        break;
                    case 31:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost3() == null ? 0 : bmsYsBillExportInfo.getOtherCost3().setScale(2, RoundingMode.HALF_UP).doubleValue()); //复冻费
                        break;
                    case 32:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost4() == null ? 0 : bmsYsBillExportInfo.getOtherCost4().setScale(2, RoundingMode.HALF_UP).doubleValue()); //贴标费
                        break;
                    case 33:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost5() == null ? 0 : bmsYsBillExportInfo.getOtherCost5().setScale(2, RoundingMode.HALF_UP).doubleValue()); //盘盈亏
                        break;
                    case 34:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost6() == null ? 0 : bmsYsBillExportInfo.getOtherCost6().setScale(2, RoundingMode.HALF_UP).doubleValue()); //装车费
                        break;
                    case 35:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost7() == null ? 0 : bmsYsBillExportInfo.getOtherCost7().setScale(2, RoundingMode.HALF_UP).doubleValue()); //快递费
                        break;
                    case 36:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost8() == null ? 0 : bmsYsBillExportInfo.getOtherCost8().setScale(2, RoundingMode.HALF_UP).doubleValue()); //餐费
                        break;
                    case 37:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost9() == null ? 0 : bmsYsBillExportInfo.getOtherCost9().setScale(2, RoundingMode.HALF_UP).doubleValue()); //上楼费
                        break;
                    case 38:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost10() == null ? 0 : bmsYsBillExportInfo.getOtherCost10().setScale(2, RoundingMode.HALF_UP).doubleValue()); //退货入库费
                        break;
                    case 39:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost11() == null ? 0 : bmsYsBillExportInfo.getOtherCost11().setScale(2, RoundingMode.HALF_UP).doubleValue()); //处理费
                        break;
                    case 40:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost12() == null ? 0 : bmsYsBillExportInfo.getOtherCost12().setScale(2, RoundingMode.HALF_UP).doubleValue()); //其他费
                        break;
                    case 41:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getSumAmt() == null ? 0 : BigDecimal.valueOf(bmsYsBillExportInfo.getSumAmt()).setScale(2, RoundingMode.HALF_UP).doubleValue());  //总费用
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                if (feeColumns.contains(i)) {
                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }
            if (s < stkList.size() - 1) {
                if (!stkList.get(s).getId().equals(stkList.get(s + 1).getId())) {
                    for (int j = 18; j < s2row2.length; j++) {
                        if (rowNum2[0] - row > 1) {
                            sheet2.addMergedRegion(new CellRangeAddress(row, rowNum2[0] - 1, j, j));
                        }
                    }
                    row = rowNum2[0];
                }
            } else {
                for (int j = 18; j < s2row2.length; j++) {
                    if (row > 0 && rowNum2[0] - row > 1) {
                        sheet2.addMergedRegion(new CellRangeAddress(row, rowNum2[0] - 1, j, j));
                    }
                }
            }
            if (f) {
                amt[0] = amt[0].add(bmsYsBillExportInfo.getTotalBoxes()); //总箱数
                amt[1] = amt[1].add(bmsYsBillExportInfo.getTotalNumber()); //总件数
                amt[2] = amt[2].add(bmsYsBillExportInfo.getTotalWeight()); //总重量
                amt[3] = amt[3].add(bmsYsBillExportInfo.getExcessFee()); //拆零分拣费
                amt[4] = amt[4].add(bmsYsBillExportInfo.getExceptionFee()); //异常赔付费
                amt[5] = amt[5].add(bmsYsBillExportInfo.getTotalVolume()); //总体积
                amt[6] = amt[6].add(bmsYsBillExportInfo.getFreight()); //存储费
                amt[7] = amt[7].add(bmsYsBillExportInfo.getUltrafarFee()); //管理处置费
                amt[25] = amt[25].add(bmsYsBillExportInfo.getSuperframesFee()); //整箱分拣费
                amt[23] = amt[23].add(bmsYsBillExportInfo.getReturnFee()); //制单费
                amt[24] = amt[24].add(bmsYsBillExportInfo.getShortbargeFee());  //操作费
                amt[26] = amt[26].add(bmsYsBillExportInfo.getExceptionFee()); //异常赔付费
                amt[27] = amt[27].add(bmsYsBillExportInfo.getReduceFee()); //减点费（订单）
                amt[28] = amt[28].add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee())); //调账费
                amt[29] = amt[29].add(bmsYsBillExportInfo.getDeliveryFee()); //装卸费
                amt[8] = amt[8].add(bmsYsBillExportInfo.getOtherCost1()); //缠膜费
                amt[9] = amt[9].add(bmsYsBillExportInfo.getOtherCost2()); //泡沫箱
                amt[10] = amt[10].add(bmsYsBillExportInfo.getOtherCost3()); //复冻费
                amt[11] = amt[11].add(bmsYsBillExportInfo.getOtherCost4()); //贴标费
                amt[12] = amt[12].add(bmsYsBillExportInfo.getOtherCost5()); //盘盈亏
                amt[13] = amt[13].add(bmsYsBillExportInfo.getOtherCost6()); //装车费
                amt[14] = amt[14].add(bmsYsBillExportInfo.getOtherCost7()); //快递费
                amt[15] = amt[15].add(bmsYsBillExportInfo.getOtherCost8()); //餐费
                amt[16] = amt[16].add(bmsYsBillExportInfo.getOtherCost9()); //上楼费
                amt[17] = amt[17].add(bmsYsBillExportInfo.getOtherCost10()); //退货入库费
                amt[18] = amt[18].add(bmsYsBillExportInfo.getOtherCost11()); //处理费
                amt[19] = amt[19].add(bmsYsBillExportInfo.getOtherCost12()); //其他费
                amt[20] = amt[20].add(BigDecimal.valueOf(bmsYsBillExportInfo.getBasicsSum())); //基础费
                amt[21] = amt[21].add(BigDecimal.valueOf(bmsYsBillExportInfo.getOtherSum())); //其他费
                amt[22] = amt[22].add(BigDecimal.valueOf(bmsYsBillExportInfo.getSumAmt())); //总费用
                sumAmt = sumAmt.add( bmsYsBillExportInfo.getFreight())
                        .add(bmsYsBillExportInfo.getExcessFee())
                        .add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()))
                        .add(bmsYsBillExportInfo.getReduceFee())
                        .add(bmsYsBillExportInfo.getSuperframesFee())
                        .add(bmsYsBillExportInfo.getExceptionFee())
                        .add(bmsYsBillExportInfo.getUltrafarFee())
                        .add(bmsYsBillExportInfo.getReturnFee())
                        .add(bmsYsBillExportInfo.getShortbargeFee())
                        .add(bmsYsBillExportInfo.getOtherCost1())
                        .add(bmsYsBillExportInfo.getOtherCost2())
                        .add(bmsYsBillExportInfo.getOtherCost3())
                        .add(bmsYsBillExportInfo.getOtherCost4())
                        .add(bmsYsBillExportInfo.getOtherCost5())
                        .add(bmsYsBillExportInfo.getOtherCost6())
                        .add(bmsYsBillExportInfo.getOtherCost7())
                        .add(bmsYsBillExportInfo.getOtherCost8())
                        .add(bmsYsBillExportInfo.getOtherCost9())
                        .add(bmsYsBillExportInfo.getOtherCost10())
                        .add(bmsYsBillExportInfo.getOtherCost11())
                        .add(bmsYsBillExportInfo.getOtherCost12())
                ;
            }
        }
        //汇总每行的金额
        String[] s2row3 = {"合计：", "", "", "", "",
                "", "", "", "", "", "", "", amt[0].toString(), amt[1].toString(),
                amt[5].toString(), amt[2].toString(), "", "", "",
                amt[6].toString(), amt[7].toString(), amt[25].toString(), amt[3].toString(),
                amt[29].toString(), amt[23].toString(), amt[24].toString(), amt[26].toString(),
                amt[28].toString(), "",
                amt[8].toString(), amt[9].toString(), amt[10].toString(), amt[11].toString(),
                amt[12].toString(), amt[13].toString(), amt[14].toString(), amt[15].toString(),
                amt[16].toString(), amt[17].toString(), amt[18].toString(), amt[19].toString(),
                amt[22].toString()
        };

        //所有费用列的下标(处理保留2位小数的问题)
        Set<Integer> feeColumns2r3 = new HashSet<>(Arrays.asList(12, 13, 14, 15, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41));


        Row s2r3 = sheet2.createRow(rowNum2[0]++);
        for (int i = 0; i < s2row3.length; i++) {
            Cell tempCell = s2r3.createCell(i);
            if (i > 11) {
                tempCell.setCellValue(StrUtil.isNotEmpty(s2row3[i]) ? new BigDecimal(s2row3[i]).setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
            } else {
                tempCell.setCellValue(s2row3[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else if (feeColumns2r3.contains(i)) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }


        }
        sheet2.addMergedRegion(new CellRangeAddress(rowNum2[0] - 1, rowNum2[0] - 1, 0, 11));
        //最后一行 合计金额
        Row s2r4 = sheet2.createRow(rowNum2[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"总费用合计（元）:", "", "", "", "",
                "", "", "", "", "", "",
                "", sumAmt.toString(), "", "", "", "",
                "", "", "", "", "", "", "", "", "", "",
                "", "", "", "", "", "", "", "", ""
                , "", "", "", "", "", ""
        };
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if (i == 12) {
                tempCell.setCellValue(new BigDecimal(s2row4[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
            } else {
                tempCell.setCellValue(s2row4[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else if (i == 12) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        sheet2.addMergedRegion(new CellRangeAddress(rowNum2[0] - 1, rowNum2[0] - 1, 0, 11));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum2[0] - 1, rowNum2[0] - 1, 12, 41));
        return hssfWorkbook;
    }

    /**
     * 第三个sheet 存储服务费明细信息
     */
    public SXSSFWorkbook storageDetailSummary(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid = RequestContext.getTenantId();
        // 第3个sheet
        Sheet sheet2 = hssfWorkbook.createSheet("存储服务费明细信息");
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        // 内容样式
        XSSFCellStyle sheet2ContentStyle = ExcelExportUtil.getSheet2ContentStyle(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        // 背景色的设定
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum2 = {0};

        //设置列宽
        for (int i = 0; i < 17; i++) {
            sheet2.setColumnWidth(i, 2700);
        }

        //第1行
        Row s2r2 = sheet2.createRow(rowNum2[0]++);
        s2r2.setHeight((short) 500);
        String[] s2row2 = {"商家基础资料", "", "", "", "", "", "", "", "", "", "", "", "托盘数核算", "", "", "", ""};
        for (int i = 0; i < s2row2.length; i++) {
            Cell tempCell = s2r2.createCell(i);
            tempCell.setCellValue(s2row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        //合并单元格
        sheet2.addMergedRegion(new CellRangeAddress(rowNum2[0] - 1, rowNum2[0] - 1, 0, 11));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum2[0] - 1, rowNum2[0] - 1, 12, 16));

        //第2行
        Row s2r3 = sheet2.createRow(rowNum2[0]++);
        s2r3.setHeight((short) 500);
        String[] s2row3 = {"库存日期", "温层", "商家", "商品ID/SKU", "商品名称",
                "生产日期", "库存件数", "托规", "三级箱规(件/箱)",
                "箱长(cm)", "箱宽(cm)", "箱高(cm)",
                "计算托盘数", "整数部分", "尾数≥0.5取整", "尾数<0.5合并", "合计托盘数"};
        for (int i = 0; i < s2row3.length; i++) {
            Cell tempCell = s2r3.createCell(i);
            tempCell.setCellValue(s2row3[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        // 数据行
        // 求和
        BigDecimal zs = new BigDecimal("0"); //整数
        BigDecimal wsq = new BigDecimal("0"); //尾数取整
        BigDecimal wsh = new BigDecimal("0"); //尾数合并
        BigDecimal qh = new BigDecimal("0");// 合计托盘
        // 获取存储服务费明细信息
        List<BmsYsBillExportInfo> list = ysState.getStorageGoodsDetailByBillId(tenantid, ids);
        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd");
        for (BmsYsBillExportInfo info : list) {
            Row s2r4 = sheet2.createRow(rowNum2[0]++);
            s2r4.setHeight((short) 500);
            for (int i = 0; i < s2row3.length; i++) {
                Cell tempCell = s2r4.createCell(i);
                tempCell.setCellStyle(sheet2HeaderStyle);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getInstorageTime() != null ? sml.format(info.getInstorageTime()) : "");
                        break;
                    case 1:
                        tempCell.setCellValue(info.getTemperatureName());
                        break;
                    case 2:
                        tempCell.setCellValue(info.getClientName());
                        break;
                    case 3:
                        tempCell.setCellValue(info.getSkuCode());
                        break;
                    case 4:
                        tempCell.setCellValue(info.getSkuName());
                        break;
                    case 5:
                        tempCell.setCellValue(info.getProductDate() != null ? sml.format(info.getProductDate()) : "");
                        break;
                    case 6:
                        tempCell.setCellValue(info.getStockQuantity() == null ? 0 : info.getStockQuantity().doubleValue());
                        break;
                    case 7:
                        tempCell.setCellValue(info.getPalletType() == null ? 0 : info.getPalletType().doubleValue());
                        break;
                    case 8:
                        tempCell.setCellValue(info.getBoxType());
                        break;
                    case 9:
                        tempCell.setCellValue(info.getBoxLength() == null ? 0 : info.getBoxLength().doubleValue());
                        break;
                    case 10:
                        tempCell.setCellValue(info.getWeightWidth() == null ? 0 : info.getWeightWidth().doubleValue());
                        break;
                    case 11:
                        tempCell.setCellValue(info.getWeightHeight() == null ? 0 : info.getWeightHeight().doubleValue());
                        break;
                    case 12:
                        tempCell.setCellValue(info.getCalculatePallet() == null ? 0 : info.getCalculatePallet().doubleValue());
                        break;
                    case 13:
                        tempCell.setCellValue(info.getIntegerNumber() == null ? 0 : info.getIntegerNumber().doubleValue());
                        break;
                    case 14:
                        tempCell.setCellValue(info.getGreaterThan5() == null ? 0 : info.getGreaterThan5().doubleValue());
                        break;
                    case 15:
                        tempCell.setCellValue(info.getLessThan5() == null ? 0 : info.getLessThan5().doubleValue());
                        break;
                    case 16:
                        tempCell.setCellValue(info.getTotalPallet() == null ? 0 : info.getTotalPallet().doubleValue());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }
            zs = zs.add(info.getIntegerNumber());
            wsq = wsq.add(info.getGreaterThan5());
            wsh = wsh.add(info.getLessThan5());
            qh = qh.add(info.getTotalPallet());
        }

        //最后一行 合计数量
        Row s2r4 = sheet2.createRow(rowNum2[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"合计：", "", "", "", "",
                "", "", "", "",
                "", "", "",
                "", "", "", "", ""};
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            switch (i) {
                case 13:
                    tempCell.setCellValue(Double.parseDouble(zs.toString()));
                    break;
                case 14:
                    tempCell.setCellValue(Double.parseDouble(wsq.toString()));
                    break;
                case 15:
                    tempCell.setCellValue(Double.parseDouble(wsh.toString()));
                    break;
                case 16:
                    tempCell.setCellValue(Double.parseDouble(qh.toString()));
                    break;
                default:
            }
            if (i < 13) {
                tempCell.setCellValue(s2row4[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        sheet2.addMergedRegion(new CellRangeAddress(rowNum2[0] - 1, rowNum2[0] - 1, 0, 5));

        return hssfWorkbook;
    }

    /**
     * 第四个sheet 入库明细
     */
    public SXSSFWorkbook inWarehousDetailSummary(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, Long id) {
        String tenantid = RequestContext.getTenantId();
        // 第4个sheet
        Sheet sheet3 = hssfWorkbook.createSheet("入库明细");
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum3 = {0};
        List<BmsYsBillExportInfo> inWarList = ysState.getStorageinWarehouseGoodsDetailByBillId(tenantid, id);
        BigDecimal[] amtf = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        Boolean[] fare = new Boolean[22];
        // 判断是否要隐藏列
        for (BmsYsBillExportInfo bmsYsBillExportInfo : inWarList) {
            amtf[0] = amtf[0].add(bmsYsBillExportInfo.getFreight());
            amtf[1] = amtf[1].add(bmsYsBillExportInfo.getUltrafarFee());
            amtf[2] = amtf[2].add(bmsYsBillExportInfo.getSuperframesFee());
            amtf[3] = amtf[3].add(bmsYsBillExportInfo.getExcessFee());
            amtf[4] = amtf[4].add(bmsYsBillExportInfo.getDeliveryFee());
            amtf[5] = amtf[5].add(bmsYsBillExportInfo.getExceptionFee());
            amtf[6] = amtf[6].add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()));
            amtf[7] = amtf[7].add(bmsYsBillExportInfo.getReturnFee());
            amtf[8] = amtf[8].add(bmsYsBillExportInfo.getShortbargeFee());
            amtf[9] = amtf[9].add(bmsYsBillExportInfo.getOtherCost1());
            amtf[10] = amtf[10].add(bmsYsBillExportInfo.getOtherCost2());
            amtf[11] = amtf[11].add(bmsYsBillExportInfo.getOtherCost3());
            amtf[12] = amtf[12].add(bmsYsBillExportInfo.getOtherCost4());
            amtf[13] = amtf[13].add(bmsYsBillExportInfo.getOtherCost5());
            amtf[14] = amtf[14].add(bmsYsBillExportInfo.getOtherCost6());
            amtf[15] = amtf[15].add(bmsYsBillExportInfo.getOtherCost7());
            amtf[16] = amtf[16].add(bmsYsBillExportInfo.getOtherCost8());
            amtf[17] = amtf[17].add(bmsYsBillExportInfo.getOtherCost9());
            amtf[18] = amtf[18].add(bmsYsBillExportInfo.getOtherCost10());
            amtf[19] = amtf[19].add(bmsYsBillExportInfo.getOtherCost11());
            amtf[20] = amtf[20].add(bmsYsBillExportInfo.getOtherCost12());
            amtf[21] = amtf[21].add(BigDecimal.valueOf(bmsYsBillExportInfo.getSumAmt()));
        }
        int fi = 0;
        for (BigDecimal bigDecimal : amtf) {
            fare[fi] = bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0;
            fi++;
        }
        //设置列宽
        for (int i = 0; i < fare.length; i++) {
            if (fare[i] != null && fare[i]) {
                sheet3.setColumnWidth(i + 21, 0);
            }
        }
        //第1行
        Row s2r2 = sheet3.createRow(rowNum3[0]++);
        s2r2.setHeight((short) 500);
        String[] s3row2 = {"入库时间", "入库单号"
                , "外部订单号", "订货类型"
                , "费用单号", "物料编码", "商品名称",
                "温层", "规格型号", "单位", "入库件数", "总箱数"
                , "常温托数", "冷藏托数", "冷冻托数"
                , "总托数", "整箱数", "拆零件数", "总重量（kg）", "总重量(T)", "总体积(m³)",
                "存储费", "管理处置费", "整箱分拣费", "拆零分拣费",
                "装卸费", "制单费", "操作费", "客诉理赔费", "调账费", "调账备注",
                "otherCost1", "otherCost2", "otherCost3",
                "otherCost4", "otherCost5", "otherCost6", "otherCost7", "otherCost8",
                "otherCost9", "otherCost10", "otherCost11", "otherCost12",
                "总费用", "计费备注"};

        for (int i = 0; i < s3row2.length; i++) {
            String name = s3row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            if (StrUtil.isNotEmpty(dicMap.get("ys_storage_othercost" + name))) {
                otherFeeInfo.setOtherName(dicMap.get("ys_storage_othercost" + name));
            }
            switch (name) {
                case "客诉理赔费":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcExceptionFee());
                    break;
                case "otherCost1":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost1());
                    break;
                case "otherCost2":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost2());
                    break;
                case "otherCost3":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost3());
                    break;
                case "otherCost4":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost4());
                    break;
                case "otherCost5":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost5());
                    break;
                case "otherCost6":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost6());
                    break;
                case "otherCost7":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost7());
                    break;
                case "otherCost8":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost8());
                    break;
                case "otherCost9":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost9());
                    break;
                case "otherCost10":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost10());
                    break;
                case "otherCost11":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost11());
                    break;
                case "otherCost12":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost12());
                    break;
                default:
                    otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
            }
            s3row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s3row2.length; i++) {
            Cell tempCell = s2r2.createCell(i);
            tempCell.setCellValue(s3row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        // 创建数据行 入库明细
        String lastId = "";
        int row = 1;

        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd");
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        if (inWarList.size() <= 20000) {
            for (int p = 0; p < inWarList.size(); p++) {
                BmsYsBillExportInfo info = inWarList.get(p);
                Row s2r3 = sheet3.createRow(rowNum3[0]++);
                s2r3.setHeight((short) 500);
                boolean f = true;
                // 赋值
                f = p <= 0 || p >= inWarList.size() || !inWarList.get(p).getId().equals(inWarList.get(p - 1).getId()); // 赋0

                if (StrUtil.isNotEmpty(info.getOrderType()) && dicMap.containsKey("order_type" + info.getOrderType())) {
                    info.setOrderTypeName(dicMap.get("order_type" + info.getOrderType()));
                }

                for (int i = 0; i < s3row2.length; i++) {
                    Cell tempCell = s2r3.createCell(i);
                    switch (i) {
                        case 0:
                            tempCell.setCellValue(info.getSigningDate() != null ? sml.format(info.getSigningDate()) : "");
                            break;
                        case 1:
                            tempCell.setCellValue(info.getRelateCode());
                            break;
                        case 2:
                            tempCell.setCellValue(info.getOrderNo());
                            break;
                        case 3:
                            tempCell.setCellValue(info.getOrderTypeName());
                            break;
                        case 4:
                            tempCell.setCellValue(info.getExpensesCode());
                            break;
                        case 5:
                            tempCell.setCellValue(info.getSkuCode());
                            break;
                        case 6:
                            tempCell.setCellValue(info.getSkuName());
                            break;
                        case 7:
                            tempCell.setCellValue(info.getTemperatureName());
                            break;
                        case 8:
                            tempCell.setCellValue(info.getBoxType());
                            break;
                        case 9:
                            tempCell.setCellValue("");
                            break;
                        case 10:
                            tempCell.setCellValue(info.getContentsNumber() == null ? 0 : Double.parseDouble(info.getContentsNumber().toString()));
                            break;
                        case 11:
                            tempCell.setCellValue(Double.parseDouble(info.getTotalBoxes().add(info.getOddBoxes()).toString()));
                            break;
                        case 12:
                            tempCell.setCellValue(null == info.getCwPalletNumber() ? 0 : info.getCwPalletNumber());
                            break;
                        case 13:
                            tempCell.setCellValue(null == info.getLcPalletNumber() ? 0 : info.getLcPalletNumber());
                            break;
                        case 14:
                            tempCell.setCellValue(null == info.getLdPalletNumber() ? 0 : info.getLdPalletNumber());
                            break;
                        case 15:
                            tempCell.setCellValue(!f ? 0 : info.getPalletNumber() == null ? 0 : Double.parseDouble(info.getPalletNumber().toString()));
                            break;
                        case 16:
                            tempCell.setCellValue(info.getTotalBoxes() != null ? 0 : Double.parseDouble(info.getTotalBoxes().toString()));
                            break;
                        case 17:
                            tempCell.setCellValue(info.getOddBoxes() != null ? 0 : Double.parseDouble(info.getOddBoxes().toString()));
                            break;
                        case 18:
                            tempCell.setCellValue(info.getWeight() == null ? 0 : Double.parseDouble(info.getWeight().toString()));
                            break;
                        case 19:
                            tempCell.setCellValue(info.getTotalWeight() == null ? 0 : Double.parseDouble(info.getTotalWeight().toString()));
                            break;
                        case 20:
                            tempCell.setCellValue(info.getTotalVolume() == null ? 0 : Double.parseDouble(info.getTotalVolume().toString()));
                            break;
                        case 21:
                            tempCell.setCellValue(!f ? 0 : info.getFreight() == null ? 0 : Double.parseDouble(info.getFreight().toString()));
                            break;
                        case 22:
                            tempCell.setCellValue(!f ? 0 : info.getUltrafarFee() == null ? 0 : Double.parseDouble(info.getUltrafarFee().toString()));
                            break;
                        case 23:
                            tempCell.setCellValue(!f ? 0 : info.getSuperframesFee() == null ? 0 : Double.parseDouble(info.getSuperframesFee().toString()));
                            break;
                        case 24:
                            tempCell.setCellValue(!f ? 0 : info.getExcessFee() == null ? 0 : Double.parseDouble(info.getExcessFee().toString()));
                            break;
                        case 25:
                            tempCell.setCellValue(!f ? 0 : info.getDeliveryFee() == null ? 0 : Double.parseDouble(info.getDeliveryFee().toString()));
                            break;
                        case 26:
                            tempCell.setCellValue(!f ? 0 : info.getExceptionFee() == null ? 0 : Double.parseDouble(info.getExceptionFee().toString()));
                            break;
                        case 27:
                            tempCell.setCellValue(!f ? 0 : info.getAdjustFee() == null ? 0 : Double.parseDouble(info.getAdjustFee().toString()));
                            break;
                        case 28:
                            tempCell.setCellValue(!f ? 0 : info.getReturnFee() == null ? 0 : Double.parseDouble(info.getReturnFee().toString()));
                            break;
                        case 29:
                            tempCell.setCellValue(!f ? 0 : info.getShortbargeFee() == null ? 0 : Double.parseDouble(info.getShortbargeFee().toString()));
                            break;
                        case 30:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost1() == null ? 0 : Double.parseDouble(info.getOtherCost1().toString()));
                            break;
                        case 31:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost2() == null ? 0 : Double.parseDouble(info.getOtherCost2().toString()));
                            break;
                        case 32:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost3() == null ? 0 : Double.parseDouble(info.getOtherCost3().toString()));
                            break;
                        case 33:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost4() == null ? 0 : Double.parseDouble(info.getOtherCost4().toString()));
                            break;
                        case 34:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost5() == null ? 0 : Double.parseDouble(info.getOtherCost5().toString()));
                            break;
                        case 35:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost6() == null ? 0 : Double.parseDouble(info.getOtherCost6().toString()));
                            break;
                        case 36:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost7() == null ? 0 : Double.parseDouble(info.getOtherCost7().toString()));
                            break;
                        case 37:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost8() == null ? 0 : Double.parseDouble(info.getOtherCost8().toString()));
                            break;
                        case 38:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost9() == null ? 0 : Double.parseDouble(info.getOtherCost9().toString()));
                            break;
                        case 39:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost10() == null ? 0 : Double.parseDouble(info.getOtherCost10().toString()));
                            break;
                        case 40:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost11() == null ? 0 : Double.parseDouble(info.getOtherCost11().toString()));
                            break;
                        case 41:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost12() == null ? 0 : Double.parseDouble(info.getOtherCost12().toString()));
                            break;
                        case 42:
                            tempCell.setCellValue(!f ? 0 : info.getSumAmt() == null ? 0 : Double.parseDouble(info.getSumAmt().toString()));
                            break;
                        case 43:
                            tempCell.setCellValue(info.getAutomaticBillingRemark());
                            break;
                        default:
                            tempCell.setCellValue("");
                    }
                    tempCell.setCellStyle(commonStyle);
                    if ((p == inWarList.size() - 1 && row < rowNum3[0] - 1) || (row < rowNum3[0] - 1 && p + 1 < inWarList.size() && !lastId.equals(inWarList.get(p + 1).getId()))) {
                        for (int j = 21; j < s3row2.length; j++) {
                            sheet3.addMergedRegion(new CellRangeAddress(row, rowNum3[0] - 1, j, j));
                        }
                        lastId = info.getId();
                        row = rowNum3[0];
                    } else {
                        lastId = info.getId();
                    }
                    if ((p == inWarList.size() - 1 && row == rowNum3[0] - 1) || (row == rowNum3[0] - 1 && p + 1 < inWarList.size() && !lastId.equals(inWarList.get(p + 1).getId()))) {
                        row = rowNum3[0];
                    }
                }
                sumAmt[0] = sumAmt[0].add(info.getContentsNumber() == null ? BigDecimal.ZERO : info.getContentsNumber());
                sumAmt[1] = sumAmt[1].add(info.getTotalWeight() == null ? new BigDecimal("0") : info.getTotalWeight());
                sumAmt[29] = sumAmt[29].add(info.getWeight() == null ? new BigDecimal("0") : info.getWeight());
                sumAmt[21] = sumAmt[21].add(info.getTotalBoxes() == null ? new BigDecimal("0") : info.getTotalBoxes());
                sumAmt[30] = sumAmt[30].add(info.getOddBoxes() == null ? new BigDecimal("0") : info.getOddBoxes());
                sumAmt[23] = sumAmt[23].add(info.getTotalVolume());
                if (f) {
                    sumAmt[22] = sumAmt[22].add(info.getPalletNumber());
                    sumAmt[2] = sumAmt[2].add(info.getSuperframesFee());
                    sumAmt[3] = sumAmt[3].add(info.getExcessFee());
                    sumAmt[4] = sumAmt[4].add(info.getDeliveryFee());
                    sumAmt[5] = sumAmt[5].add(info.getExceptionFee());
                    sumAmt[6] = sumAmt[6].add(info.getOtherCost1());
                    sumAmt[7] = sumAmt[7].add(info.getOtherCost2());
                    sumAmt[8] = sumAmt[8].add(info.getOtherCost3());
                    sumAmt[9] = sumAmt[9].add(info.getOtherCost4());
                    sumAmt[10] = sumAmt[10].add(info.getOtherCost5());
                    sumAmt[11] = sumAmt[11].add(info.getOtherCost6());
                    sumAmt[12] = sumAmt[12].add(info.getOtherCost7());
                    sumAmt[13] = sumAmt[13].add(info.getOtherCost8());
                    sumAmt[14] = sumAmt[14].add(info.getOtherCost9());
                    sumAmt[15] = sumAmt[15].add(info.getOtherCost10());
                    sumAmt[16] = sumAmt[16].add(info.getOtherCost11());
                    sumAmt[17] = sumAmt[17].add(info.getOtherCost12());
                    sumAmt[18] = sumAmt[18].add(BigDecimal.valueOf(info.getBasicsSum()));
                    sumAmt[19] = sumAmt[19].add(BigDecimal.valueOf(info.getOtherSum()));
                    sumAmt[20] = sumAmt[20].add(BigDecimal.valueOf(info.getSumAmt()));
                    sumAmt[24] = sumAmt[24].add(info.getReturnFee());
                    sumAmt[25] = sumAmt[25].add(info.getShortbargeFee());
                    sumAmt[26] = sumAmt[26].add(info.getFreight());
                    sumAmt[27] = sumAmt[27].add(BigDecimal.valueOf(info.getAdjustFee()));
                    sumAmt[28] = sumAmt[28].add(info.getUltrafarFee());
                }
            }
        } else {
            for (int p = 0; p < inWarList.size(); p++) {
                BmsYsBillExportInfo info = inWarList.get(p);
                Row s2r3 = sheet3.createRow(rowNum3[0]++);
                s2r3.setHeight((short) 500);
                boolean f = true;
                // 赋值
                f = p <= 0 || p >= inWarList.size() || !inWarList.get(p).getId().equals(inWarList.get(p - 1).getId()); // 赋0
                if (StrUtil.isNotEmpty(info.getOrderType()) && dicMap.containsKey("order_type" + info.getOrderType())) {
                    info.setOrderTypeName(dicMap.get("order_type" + info.getOrderType()));
                }
                for (int i = 0; i < s3row2.length; i++) {
                    Cell tempCell = s2r3.createCell(i);
                    switch (i) {
                        case 0:
                            tempCell.setCellValue(info.getSigningDate() != null ? sml.format(info.getSigningDate()) : "");
                            break;
                        case 1:
                            tempCell.setCellValue(info.getRelateCode());
                            break;
                        case 2:
                            tempCell.setCellValue(info.getOrderNo());
                            break;
                        case 3:
                            tempCell.setCellValue(info.getOrderTypeName());
                            break;
                        case 4:
                            tempCell.setCellValue(info.getExpensesCode());
                            break;
                        case 5:
                            tempCell.setCellValue(info.getSkuCode());
                            break;
                        case 6:
                            tempCell.setCellValue(info.getSkuName());
                            break;
                        case 7:
                            tempCell.setCellValue(info.getTemperatureName());
                            break;
                        case 8:
                            tempCell.setCellValue(info.getBoxType());
                            break;
                        case 9:
                            tempCell.setCellValue("");
                            break;
                        case 10:
                            tempCell.setCellValue(info.getContentsNumber() == null ? 0 : Double.parseDouble(info.getContentsNumber().toString()));
                            break;
                        case 11:
                            tempCell.setCellValue(info.getTotalBoxes().add(info.getOddBoxes()).toString());
                            break;
                        case 12:
                            tempCell.setCellValue(null == info.getCwPalletNumber() ? 0 : info.getCwPalletNumber());
                            break;
                        case 13:
                            tempCell.setCellValue(null == info.getLcPalletNumber() ? 0 : info.getLcPalletNumber());
                            break;
                        case 14:
                            tempCell.setCellValue(null == info.getLdPalletNumber() ? 0 : info.getLdPalletNumber());
                            break;
                        case 15:
                            tempCell.setCellValue(!f ? 0 : info.getPalletNumber() == null ? 0 : Double.parseDouble(info.getPalletNumber().toString()));
                            break;
                        case 16:
                            tempCell.setCellValue(info.getTotalBoxes() == null ? 0 : Double.parseDouble(info.getTotalBoxes().toString()));
                            break;
                        case 17:
                            tempCell.setCellValue(info.getOddBoxes() == null ? 0 : Double.parseDouble(info.getOddBoxes().toString()));
                            break;
                        case 18:
                            tempCell.setCellValue(info.getWeight() == null ? 0 : Double.parseDouble(info.getWeight().toString()));
                            break;
                        case 19:
                            tempCell.setCellValue(info.getTotalWeight() == null ? 0 : Double.parseDouble(info.getTotalWeight().toString()));
                            break;
                        case 20:
                            tempCell.setCellValue(info.getTotalVolume() == null ? 0 : Double.parseDouble(info.getTotalVolume().toString()));
                            break;
                        case 21:
                            tempCell.setCellValue(!f ? 0 : info.getFreight() == null ? 0 : Double.parseDouble(info.getFreight().toString()));
                            break;
                        case 22:
                            tempCell.setCellValue(!f ? 0 : info.getUltrafarFee() == null ? 0 : Double.parseDouble(info.getUltrafarFee().toString()));
                            break;
                        case 23:
                            tempCell.setCellValue(!f ? 0 : info.getSuperframesFee() == null ? 0 : Double.parseDouble(info.getSuperframesFee().toString()));
                            break;
                        case 24:
                            tempCell.setCellValue(!f ? 0 : info.getExcessFee() == null ? 0 : Double.parseDouble(info.getExcessFee().toString()));
                            break;
                        case 25:
                            tempCell.setCellValue(!f ? 0 : info.getDeliveryFee() == null ? 0 : Double.parseDouble(info.getDeliveryFee().toString()));
                            break;
                        case 26:
                            tempCell.setCellValue(!f ? 0 : info.getExceptionFee() == null ? 0 : Double.parseDouble(info.getExceptionFee().toString()));
                            break;
                        case 27:
                            tempCell.setCellValue(!f ? 0 : info.getAdjustFee() == null ? 0 : Double.parseDouble(info.getAdjustFee().toString()));
                            break;
                        case 28:
                            tempCell.setCellValue(!f ? 0 : info.getReturnFee() == null ? 0 : Double.parseDouble(info.getReturnFee().toString()));
                            break;
                        case 29:
                            tempCell.setCellValue(!f ? 0 : info.getShortbargeFee() == null ? 0 : Double.parseDouble(info.getShortbargeFee().toString()));
                            break;
                        case 30:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost1() == null ? 0 : Double.parseDouble(info.getOtherCost1().toString()));
                            break;
                        case 31:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost2() == null ? 0 : Double.parseDouble(info.getOtherCost2().toString()));
                            break;
                        case 32:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost3() == null ? 0 : Double.parseDouble(info.getOtherCost3().toString()));
                            break;
                        case 33:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost4() == null ? 0 : Double.parseDouble(info.getOtherCost4().toString()));
                            break;
                        case 34:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost5() == null ? 0 : Double.parseDouble(info.getOtherCost5().toString()));
                            break;
                        case 35:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost6() == null ? 0 : Double.parseDouble(info.getOtherCost6().toString()));
                            break;
                        case 36:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost7() == null ? 0 : Double.parseDouble(info.getOtherCost7().toString()));
                            break;
                        case 37:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost8() == null ? 0 : Double.parseDouble(info.getOtherCost8().toString()));
                            break;
                        case 38:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost9() == null ? 0 : Double.parseDouble(info.getOtherCost9().toString()));
                            break;
                        case 39:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost10() == null ? 0 : Double.parseDouble(info.getOtherCost10().toString()));
                            break;
                        case 40:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost11() == null ? 0 : Double.parseDouble(info.getOtherCost11().toString()));
                            break;
                        case 41:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost12() == null ? 0 : Double.parseDouble(info.getOtherCost12().toString()));
                            break;
                        case 42:
                            tempCell.setCellValue(!f ? 0 : info.getSumAmt() == null ? 0 : Double.parseDouble(info.getSumAmt().toString()));
                            break;
                        case 43:
                            tempCell.setCellValue(info.getAutomaticBillingRemark());
                            break;
                        default:
                            tempCell.setCellValue("");
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                sumAmt[0] = sumAmt[0].add(info.getContentsNumber());
                sumAmt[1] = sumAmt[1].add(info.getTotalWeight() == null ? new BigDecimal("0") : info.getTotalWeight());
                sumAmt[29] = sumAmt[29].add(info.getWeight() == null ? new BigDecimal("0") : info.getWeight());
                sumAmt[21] = sumAmt[21].add(info.getTotalBoxes() == null ? new BigDecimal("0") : info.getTotalBoxes());
                sumAmt[30] = sumAmt[30].add(info.getOddBoxes() == null ? new BigDecimal("0") : info.getOddBoxes());
                sumAmt[23] = sumAmt[23].add(info.getTotalVolume());
                if (f) {
                    sumAmt[22] = sumAmt[22].add(info.getPalletNumber());
                    sumAmt[2] = sumAmt[2].add(info.getSuperframesFee());
                    sumAmt[3] = sumAmt[3].add(info.getExcessFee());
                    sumAmt[4] = sumAmt[4].add(info.getDeliveryFee());
                    sumAmt[5] = sumAmt[5].add(info.getExceptionFee());
                    sumAmt[6] = sumAmt[6].add(info.getOtherCost1());
                    sumAmt[7] = sumAmt[7].add(info.getOtherCost2());
                    sumAmt[8] = sumAmt[8].add(info.getOtherCost3());
                    sumAmt[9] = sumAmt[9].add(info.getOtherCost4());
                    sumAmt[10] = sumAmt[10].add(info.getOtherCost5());
                    sumAmt[11] = sumAmt[11].add(info.getOtherCost6());
                    sumAmt[12] = sumAmt[12].add(info.getOtherCost7());
                    sumAmt[13] = sumAmt[13].add(info.getOtherCost8());
                    sumAmt[14] = sumAmt[14].add(info.getOtherCost9());
                    sumAmt[15] = sumAmt[15].add(info.getOtherCost10());
                    sumAmt[16] = sumAmt[16].add(info.getOtherCost11());
                    sumAmt[17] = sumAmt[17].add(info.getOtherCost12());
                    sumAmt[18] = sumAmt[18].add(BigDecimal.valueOf(info.getBasicsSum()));
                    sumAmt[19] = sumAmt[19].add(BigDecimal.valueOf(info.getOtherSum()));
                    sumAmt[20] = sumAmt[20].add(BigDecimal.valueOf(info.getSumAmt()));
                    sumAmt[24] = sumAmt[24].add(info.getReturnFee());
                    sumAmt[25] = sumAmt[25].add(info.getShortbargeFee());
                    sumAmt[26] = sumAmt[26].add(info.getFreight());
                    sumAmt[27] = sumAmt[27].add(BigDecimal.valueOf(info.getAdjustFee()));
                    sumAmt[28] = sumAmt[28].add(info.getUltrafarFee());
                }

            }
        }


        //最后一行 合计金额
        Row s2r4 = sheet3.createRow(rowNum3[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"合计：", "", "", "", "", "", "", "", "", "", sumAmt[0].toString(), sumAmt[21].add(sumAmt[30]).toString(), "", "", "", sumAmt[22].toString(),
                sumAmt[21].toString(), sumAmt[30].toString(),
                sumAmt[29].toString(), sumAmt[1].toString(), sumAmt[23].toString()
                , sumAmt[26].toString(), sumAmt[28].toString(), sumAmt[2].toString(), sumAmt[3].toString(),
                sumAmt[4].toString(), sumAmt[5].toString(), sumAmt[27].toString(), sumAmt[24].toString(),
                sumAmt[25].toString(),
                sumAmt[6].toString(), sumAmt[7].toString(), sumAmt[8].toString(), sumAmt[9].toString(),
                sumAmt[10].toString(), sumAmt[11].toString(), sumAmt[12].toString(), sumAmt[13].toString(),
                sumAmt[14].toString(), sumAmt[15].toString(), sumAmt[16].toString(), sumAmt[17].toString(),
                /*sumAmt[18].toString(),sumAmt[19].toString(),*/sumAmt[20].toString(), ""
        };
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if (i > 9 && i != 12 && i != 13 && i != 14 && i != s2row4.length - 1) {
                tempCell.setCellValue(Double.parseDouble(s2row4[i]));
            } else {
                tempCell.setCellValue(s2row4[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet3.addMergedRegion(new CellRangeAddress(rowNum3[0] - 1, rowNum3[0] - 1, 0, 6));

        return hssfWorkbook;
    }


    /**
     * sheet 入库费主信息 sheet
     */
    public SXSSFWorkbook inWarehouseSummaryMain(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid = RequestContext.getTenantId();
        Sheet sheet = hssfWorkbook.createSheet("入库费主信息");
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        //1、数据准备
        List<BmsYsBillExportInfo> excelDatas = new ArrayList<>();
        List<BmsYsBillExportInfo> bmsYsBillExportInfos = ysState.inWarehouseSummaryMain(tenantid, ids);
        if (CollUtil.isNotEmpty(bmsYsBillExportInfos)) {
            excelDatas = bmsYsBillExportInfos;
        }
        Map<String, SysDept> companyMap = textConversionUtil.getCompanyMap("1", excelDatas.stream().filter(e -> e.getCompanyId() != null).map(e -> e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        excelDatas.forEach(e -> {
            if (e.getCompanyId() != null && companyMap.containsKey(e.getCompanyId().toString())) {
                e.setCompanyName(companyMap.get(e.getCompanyId().toString()).getDeptName());
            }
            if (StrUtil.isNotEmpty(e.getOrderType()) && dicMap.containsKey("order_type" + e.getOrderType())) {
                e.setOrderTypeName(dicMap.get("order_type" + e.getOrderType()));
            }
        });

        //隐藏列
        BigDecimal[] amtf = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        Boolean[] fare = new Boolean[37];
        // 判断是否要隐藏列
        assert bmsYsBillExportInfos != null;
        for (BmsYsBillExportInfo bmsYsBillExportInfo : bmsYsBillExportInfos) {
            amtf[0] = amtf[0].add(bmsYsBillExportInfo.getFreight());
            amtf[1] = amtf[1].add(bmsYsBillExportInfo.getUltrafarFee());
            amtf[2] = amtf[2].add(bmsYsBillExportInfo.getSuperframesFee());
            amtf[3] = amtf[3].add(bmsYsBillExportInfo.getExcessFee());
            amtf[4] = amtf[4].add(bmsYsBillExportInfo.getDeliveryFee());
            amtf[5] = amtf[5].add(bmsYsBillExportInfo.getReturnFee());
            amtf[6] = amtf[6].add(bmsYsBillExportInfo.getShortbargeFee());
            amtf[7] = amtf[7].add(bmsYsBillExportInfo.getExceptionFee());
            amtf[8] = amtf[8].add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()));
            amtf[9] = amtf[9].add(new BigDecimal(StrUtil.isEmpty(bmsYsBillExportInfo.getAdjustRemark()) ? 0 : 1));
            amtf[10] = amtf[10].add(bmsYsBillExportInfo.getOtherCost1());
            amtf[11] = amtf[11].add(bmsYsBillExportInfo.getOtherCost2());
            amtf[12] = amtf[12].add(bmsYsBillExportInfo.getOtherCost3());
            amtf[13] = amtf[13].add(bmsYsBillExportInfo.getOtherCost4());
            amtf[14] = amtf[14].add(bmsYsBillExportInfo.getOtherCost5());
            amtf[15] = amtf[15].add(bmsYsBillExportInfo.getOtherCost6());
            amtf[16] = amtf[16].add(bmsYsBillExportInfo.getOtherCost7());
            amtf[17] = amtf[17].add(bmsYsBillExportInfo.getOtherCost8());
            amtf[18] = amtf[18].add(bmsYsBillExportInfo.getOtherCost9());
            amtf[19] = amtf[19].add(bmsYsBillExportInfo.getOtherCost10());
            amtf[20] = amtf[20].add(bmsYsBillExportInfo.getOtherCost11());
            amtf[21] = amtf[21].add(bmsYsBillExportInfo.getOtherCost12());
            amtf[22] = amtf[22].add(BigDecimal.valueOf(bmsYsBillExportInfo.getSumAmt()));
        }
        int fi = 0;
        for (BigDecimal bigDecimal : amtf) {
            fare[fi] = bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0;
            fi++;
        }
        //设置列宽
        for (int i = 0; i < fare.length; i++) {
            if (fare[i] != null && fare[i]) {
                sheet.setColumnWidth(i + 27, 0);
            }
        }

        //2、组装数值数据
        final int[] rowNum = {0};
        //创建首行
        String[] excelHeaderFields = {
                "订单时间", "入库时间", "入库单号", "外部订单号",
                "计费类型", "费用单号", "费用维度", "订单类型", "配送方式", "单据所属", "发货仓库",
                "门店编码", "门店名称", "拆零总件数", "总件数", "总重量（kg）", "总重量(T)", "总体积(m³)", "总箱数", "常温托数", "冷藏托数", "冷冻托数",
                "总托数", "计费人", "计费备注", "备注", "是否超时", "存储费", "管理处置费", "整箱分拣费", "拆零分拣费",
                "装卸费", "制单费", "操作费", "客诉理赔费", "调账费", "调账备注", "otherCost1", "otherCost2",
                "otherCost3", "otherCost4", "otherCost5", "otherCost6", "otherCost7", "otherCost8",
                "otherCost9", "otherCost10", "otherCost11", "otherCost12", "总费用"
        };

        //所有费用列的下标(处理保留2位小数的问题)
        Set<Integer> feeColumns = new HashSet<>(Arrays.asList(27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49));


        Row headerRow = sheet.createRow(rowNum[0]++);
        headerRow.setHeight((short) 500);

        for (int i = 0; i < excelHeaderFields.length; i++) {
            //其他费字典
            String name = excelHeaderFields[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            if (StrUtil.isNotEmpty(dicMap.get("ys_storage_othercost" + name))) {
                otherFeeInfo.setOtherName(dicMap.get("ys_storage_othercost" + name));
            }
            switch (name) {
                case "客诉理赔费":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcExceptionFee());
                    break;
                case "otherCost1":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost1());
                    break;
                case "otherCost2":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost2());
                    break;
                case "otherCost3":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost3());
                    break;
                case "otherCost4":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost4());
                    break;
                case "otherCost5":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost5());
                    break;
                case "otherCost6":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost6());
                    break;
                case "otherCost7":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost7());
                    break;
                case "otherCost8":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost8());
                    break;
                case "otherCost9":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost9());
                    break;
                case "otherCost10":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost10());
                    break;
                case "otherCost11":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost11());
                    break;
                case "otherCost12":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost12());
                    break;
                default:
                    otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
            }
            excelHeaderFields[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < excelHeaderFields.length; i++) {
            Cell tempCell = headerRow.createCell(i);
            tempCell.setCellValue(excelHeaderFields[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        //合计费用列的合计金额
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),    //总件数
                new BigDecimal("0"),    //总重量(KG)
                new BigDecimal("0"),    //总体积(m³)
                new BigDecimal("0"),    //常温整箱数
                new BigDecimal("0"),    //常温散箱数
                new BigDecimal("0"),    //非常温整箱数
                new BigDecimal("0"),    //非常温散箱数
                new BigDecimal("0"),    //总箱数
                new BigDecimal("0"),    //常温托数
                new BigDecimal("0"),    //冷藏托数
                new BigDecimal("0"),    //冷冻托数
                new BigDecimal("0"),    //总托数
                new BigDecimal("0"),    //总货值
                new BigDecimal("0"),    //计费人
                new BigDecimal("0"),    //计费备注
                new BigDecimal("0"),    //备注
                new BigDecimal("0"),    //存储费
                new BigDecimal("0"),    //管理处置费
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0") // 拆零总件数
        };

        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd");
        //加载行数
        int row = 1;
        for (int s = 0; s < excelDatas.size(); s++) {
            BmsYsBillExportInfo info = excelDatas.get(s);
            Row dataRow = sheet.createRow(rowNum[0]++);
            dataRow.setHeight((short) 500);
            boolean f = true;
            // 赋值
            f = s <= 0 || s >= excelDatas.size() || !excelDatas.get(s).getExpensesCode().equals(excelDatas.get(s - 1).getExpensesCode()); // 赋0
            for (int i = 0; i < excelHeaderFields.length; i++) {
                Cell tempCell = dataRow.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getOrderDate() != null ? sml.format(info.getOrderDate()) : "");
                        break;
                    case 1:
                        tempCell.setCellValue(info.getSigningDate() != null ? sml.format(info.getSigningDate()) : "");
                        break;
                    case 2:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getRelateCode()) ? info.getRelateCode() : "");
                        break;
                    case 3:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getOrderNo()) ? info.getOrderNo() : "");
                        break;
                    case 4:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getChargeTypeName()) ? info.getChargeTypeName() : "");
                        break;
                    case 5:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getExpensesCode()) ? info.getExpensesCode() : "");
                        break;
                    case 6:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getCostDimensionName()) ? info.getCostDimensionName() : "");
                        break;
                    case 7:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getOrderTypeName()) ? info.getOrderTypeName() : "");
                        break;
                    case 8:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getTransportTypeName()) ? info.getTransportTypeName() : "");
                        break;
                    case 9:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getCompanyName()) ? info.getCompanyName() : "");
                        break;
                    case 10:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getWarehouseName()) ? info.getWarehouseName() : "");
                        break;
                    case 11:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getStoreCode()) ? info.getStoreCode() : "");
                        break;
                    case 12:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getReceivingStore()) ? info.getReceivingStore() : "");
                        break;
                    case 13:
                        tempCell.setCellValue(null != info.getSplitTotalNumber() ? info.getSplitTotalNumber().doubleValue() : 0);
                        break;
                    case 14:
                        tempCell.setCellValue(null != info.getSkuNumber() ? info.getSkuNumber().doubleValue() : 0);
                        break;
                    case 15:
                        tempCell.setCellValue(null != info.getWeight() ? info.getWeight().doubleValue() : 0);
                        break;
                    case 16:
                        tempCell.setCellValue(null != info.getTotalWeight() ? info.getTotalWeight().doubleValue() : 0);
                        break;
                    case 17:
                        tempCell.setCellValue(null != info.getTotalVolume() ? info.getTotalVolume().doubleValue() : 0);
                        break;
                    case 18:
                        tempCell.setCellValue(null != info.getTotalBoxes() ? info.getTotalBoxes().doubleValue() : 0);
                        break;
                    case 19:
                        tempCell.setCellValue(null != info.getCwPalletNumber() ? info.getCwPalletNumber() : 0);
                        break;
                    case 20:
                        tempCell.setCellValue(null != info.getLcPalletNumber() ? info.getLcPalletNumber() : 0);
                        break;
                    case 21:
                        tempCell.setCellValue(null != info.getLdPalletNumber() ? info.getLdPalletNumber() : 0);
                        break;
                    case 22:
                        tempCell.setCellValue(null != info.getPalletNumber() ? info.getPalletNumber().doubleValue() : 0);
                        break;
                    case 23:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getOperBy()) ? info.getOperBy() : "");
                        break;
                    case 24:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getAutomaticBillingRemark()) ? info.getAutomaticBillingRemark() : "");
                        break;
                    case 25:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getRemarks()) ? info.getRemarks() : "");
                        break;
                    case 26:
                        String isTimeOut = "";
                        if (info.getIsTimeOut() != null) {
                            isTimeOut = info.getIsTimeOut().equals(0) ? "是" : "否";
                        }
                        tempCell.setCellValue(isTimeOut);
                        break;
                    case 27:
                        tempCell.setCellValue(!f ? 0 : null != info.getFreight() ? info.getFreight().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //存储费
                        break;
                    case 28:
                        tempCell.setCellValue(!f ? 0 : null != info.getUltrafarFee() ? info.getUltrafarFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //管理处置费
                        break;
                    case 29:
                        tempCell.setCellValue(!f ? 0 : null != info.getSuperframesFee() ? info.getSuperframesFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //整箱分拣费
                        break;
                    case 30:
                        tempCell.setCellValue(!f ? 0 : null != info.getExcessFee() ? info.getExcessFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //拆零分拣费
                        break;
                    case 31:
                        tempCell.setCellValue(!f ? 0 : null != info.getDeliveryFee() ? info.getDeliveryFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //装卸费
                        break;
                    case 32:
                        tempCell.setCellValue(!f ? 0 : null != info.getReturnFee() ? info.getReturnFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); // 制单费
                        break;
                    case 33:
                        tempCell.setCellValue(!f ? 0 : null != info.getShortbargeFee() ? info.getShortbargeFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //操作费
                        break;
                    case 34:
                        tempCell.setCellValue(!f ? 0 : null != info.getExceptionFee() ? info.getExceptionFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //异常赔付费
                        break;
                    case 35:
                        tempCell.setCellValue(!f ? 0 : null != info.getAdjustFee() ? BigDecimal.valueOf(info.getAdjustFee()).setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //调账费
                        break;
                    case 36:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getAdjustRemark()) ? info.getAdjustRemark() : ""); //调账备注
                        break;
                    case 37:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost1() ? info.getOtherCost1().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 38:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost2() ? info.getOtherCost2().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 39:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost3() ? info.getOtherCost3().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 40:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost4() ? info.getOtherCost4().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 41:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost5() ? info.getOtherCost5().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 42:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost6() ? info.getOtherCost6().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 43:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost7() ? info.getOtherCost7().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 44:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost8() ? info.getOtherCost8().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 45:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost9() ? info.getOtherCost9().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 46:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost10() ? info.getOtherCost10().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 47:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost11() ? info.getOtherCost11().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 48:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost12() ? info.getOtherCost12().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 49:
                        tempCell.setCellValue(!f ? 0 : null != info.getSumAmt() ? info.getSumAmt() : 0);
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                if (feeColumns.contains(i)) {
                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }
            if (s < excelDatas.size() - 1) {
                if (!excelDatas.get(s).getExpensesCode().equals(excelDatas.get(s + 1).getExpensesCode())) {
                    for (int j = 29; j < excelHeaderFields.length; j++) {
                        if (rowNum[0] - row > 1) {
                            sheet.addMergedRegion(new CellRangeAddress(row, rowNum[0] - 1, j, j));
                        }
                    }
                    row = rowNum[0];
                }
            } else {
                for (int j = 6; j < excelHeaderFields.length; j++) {
                    if (j >= 27 && row > 0 && rowNum[0] - row > 1) {
                        sheet.addMergedRegion(new CellRangeAddress(row, rowNum[0] - 1, j, j));
                    }
                }
            }
            // 汇总单据数据
            sumAmt[0] = sumAmt[0].add(info.getSkuNumber() == null ? new BigDecimal("0") : info.getSkuNumber());
            sumAmt[1] = sumAmt[1].add(info.getWeight() == null ? new BigDecimal("0") : info.getWeight());
            sumAmt[2] = sumAmt[2].add(info.getTotalWeight() == null ? new BigDecimal("0") : info.getTotalWeight());
            sumAmt[3] = sumAmt[3].add(info.getTotalVolume() == null ? new BigDecimal("0") : info.getTotalVolume());
            sumAmt[4] = sumAmt[4].add(info.getCwFullCases() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getCwFullCases())); //常温整箱数
            sumAmt[5] = sumAmt[5].add(info.getCwSplitCases() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getCwSplitCases())); //常温散箱数
            sumAmt[6] = sumAmt[6].add(info.getLdFullCases() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getLdFullCases())); //非常温整箱数
            sumAmt[7] = sumAmt[7].add(info.getLdSplitCases() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getLdSplitCases())); //非常温散箱数
            sumAmt[8] = sumAmt[8].add(info.getTotalBoxes() == null ? new BigDecimal("0") : info.getTotalBoxes()); //总箱数
            sumAmt[9] = sumAmt[9].add(info.getCwPalletNumber() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getCwPalletNumber())); //常温托数
            sumAmt[10] = sumAmt[10].add(info.getLcPalletNumber() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getLcPalletNumber())); //冷藏托数
            sumAmt[11] = sumAmt[11].add(info.getLdPalletNumber() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getLdPalletNumber())); //冷冻托数
            sumAmt[12] = sumAmt[12].add(info.getPalletNumber() == null ? new BigDecimal("0") : info.getPalletNumber()); //总托数
            // 因为存在合并列费用的情况，所以要过滤重复的数据
            if (f) {
                sumAmt[13] = sumAmt[13].add(info.getFreight() == null ? new BigDecimal("0") : info.getFreight()); //存储费
                sumAmt[14] = sumAmt[14].add(info.getUltrafarFee() == null ? new BigDecimal("0") : info.getUltrafarFee()); //管理处置费
                sumAmt[15] = sumAmt[15].add(info.getSuperframesFee() == null ? new BigDecimal("0") : info.getSuperframesFee()); //整箱分拣费
                sumAmt[16] = sumAmt[16].add(info.getExcessFee() == null ? new BigDecimal("0") : info.getExcessFee()); //拆零分拣费
                sumAmt[17] = sumAmt[17].add(info.getDeliveryFee() == null ? new BigDecimal("0") : info.getDeliveryFee()); //装卸费
                sumAmt[18] = sumAmt[18].add(info.getReturnFee() == null ? new BigDecimal("0") : info.getReturnFee()); //制单费
                sumAmt[19] = sumAmt[19].add(info.getShortbargeFee() == null ? new BigDecimal("0") : info.getShortbargeFee()); //操作费
                sumAmt[20] = sumAmt[20].add(info.getExceptionFee() == null ? new BigDecimal("0") : info.getExceptionFee()); //异常赔付费
                sumAmt[21] = sumAmt[21].add(info.getAdjustFee() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getAdjustFee())); //调账费
                sumAmt[22] = sumAmt[22].add(info.getOtherCost1() == null ? new BigDecimal("0") : info.getOtherCost1()); //其他费1
                sumAmt[23] = sumAmt[23].add(info.getOtherCost2() == null ? new BigDecimal("0") : info.getOtherCost2()); //其他费2
                sumAmt[24] = sumAmt[24].add(info.getOtherCost3() == null ? new BigDecimal("0") : info.getOtherCost3()); //其他费3
                sumAmt[25] = sumAmt[25].add(info.getOtherCost4() == null ? new BigDecimal("0") : info.getOtherCost4()); //其他费4
                sumAmt[26] = sumAmt[26].add(info.getOtherCost5() == null ? new BigDecimal("0") : info.getOtherCost5()); //其他费5
                sumAmt[27] = sumAmt[27].add(info.getOtherCost6() == null ? new BigDecimal("0") : info.getOtherCost6()); //其他费6
                sumAmt[28] = sumAmt[28].add(info.getOtherCost7() == null ? new BigDecimal("0") : info.getOtherCost7()); //其他费7
                sumAmt[29] = sumAmt[29].add(info.getOtherCost8() == null ? new BigDecimal("0") : info.getOtherCost8()); //其他费8
                sumAmt[30] = sumAmt[30].add(info.getOtherCost9() == null ? new BigDecimal("0") : info.getOtherCost9()); //其他费9
                sumAmt[31] = sumAmt[31].add(info.getOtherCost10() == null ? new BigDecimal("0") : info.getOtherCost10()); //其他费10
                sumAmt[32] = sumAmt[32].add(info.getOtherCost11() == null ? new BigDecimal("0") : info.getOtherCost11()); //其他费11
                sumAmt[33] = sumAmt[33].add(info.getOtherCost12() == null ? new BigDecimal("0") : info.getOtherCost12()); //其他费12
                sumAmt[34] = sumAmt[34].add(info.getSumAmt() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getSumAmt())); //总费用
                sumAmt[35] = sumAmt[35].add(info.getSplitTotalNumber() == null ? new BigDecimal("0") : info.getSplitTotalNumber()); //拆零总件数
            }

        }

        //最后一行 合计金额
        Row lastRow = sheet.createRow(rowNum[0]++);
        String[] lastRowFields = {"合计", "", "", "", "", "", "", "", "", "", "", "", "", sumAmt[35].toString()
                , sumAmt[0].toString(), sumAmt[1].toString(), sumAmt[2].toString(), sumAmt[3].toString(), sumAmt[8].toString(), sumAmt[9].toString(), sumAmt[10].toString(), sumAmt[11].toString()
                , sumAmt[12].toString(), "", "", "", ""
                , sumAmt[13].toString(), sumAmt[14].toString(), sumAmt[15].toString(), sumAmt[16].toString(), sumAmt[17].toString(), sumAmt[18].toString()
                , sumAmt[19].toString(), sumAmt[20].toString(), sumAmt[21].toString(), ""
                , sumAmt[22].toString(), sumAmt[23].toString(), sumAmt[24].toString(), sumAmt[25].toString(), sumAmt[26].toString(), sumAmt[27].toString()
                , sumAmt[28].toString(), sumAmt[29].toString(), sumAmt[30].toString(), sumAmt[31].toString(), sumAmt[32].toString(), sumAmt[33].toString()
                , sumAmt[34].toString()
        };


        for (int i = 0; i < lastRowFields.length; i++) {
            Cell tempCell = lastRow.createCell(i);
            if (i >= 13 && i < 23 || i >= 27 && i <= 35 || i >= 37) {
                tempCell.setCellValue(StrUtil.isNotEmpty(lastRowFields[i]) ? new BigDecimal(lastRowFields[i]).setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
            } else {
                tempCell.setCellValue(lastRowFields[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else if (i >= 27 && i <= 35 || i >= 37) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }

        }
        // 合计行合并
        sheet.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 0, 7));
        return hssfWorkbook;
    }


    /**
     * sheet 入库费明细信息 sheet
     */
    public SXSSFWorkbook inWarehouseDetailsSummary(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll,
                                                   Map<String, String> dicMap, List<Long> ids) {
        String tenantid = RequestContext.getTenantId();
        Sheet sheet = hssfWorkbook.createSheet("入库费明细信息");
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        Map<String, String> dicMaps = textConversionUtil.getDictByType("warm_zone_type", "1");

        // 1、数据准备
        List<BmsYsBillExportInfo> excelDatas = new ArrayList<>();

        List<BmsYsBillExportInfo> bmsYsBillExportInfos = ysState.inWarehouseSummaryMain(tenantid, ids);
        //取业务单号
        if (CollUtil.isNotEmpty(bmsYsBillExportInfos)) {
            List<String> relateCodes = bmsYsBillExportInfos.stream().map(BmsYsBillExportInfo::getRelateCode).distinct().collect(Collectors.toList());
            excelDatas = ysState.inWarehouseDetailsSummary(tenantid, relateCodes);
        }
        if (CollUtil.isNotEmpty(excelDatas)) {
            excelDatas.forEach(e -> {
                // 温区名称
                if (e.getTemperatureType() != null && dicMaps.containsKey("warm_zone_type" + e.getTemperatureType())) {
                    e.setTemperatureName(dicMaps.get("warm_zone_type" + e.getTemperatureType()));
                }
            });
        }

        // 2、组装数值数据
        //所有行的要汇总的数值
        //行标
        final int[] rowNum = {0};

        //创建首行
        String[] excelHeaderFields = {
                "入库单号", "订单时间", "门店名称", "物料编码",
                "商品名称", "品类", "温层", "商品规格", "单位", "箱规", "整箱数", "拆零件数", "件数",
                "单箱重量(kg)", "单箱体积(cm³)", "总重量(kg)", "总体积(m³)"
        };

        Row headerRow = sheet.createRow(rowNum[0]++);
        headerRow.setHeight((short) 500);

        for (int i = 0; i < excelHeaderFields.length; i++) {
            Cell tempCell = headerRow.createCell(i);
            tempCell.setCellValue(excelHeaderFields[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        //合计费用列的合计金额
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };

        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd");

        for (BmsYsBillExportInfo info : excelDatas) {
            Row dataRow = sheet.createRow(rowNum[0]++);
            dataRow.setHeight((short) 500);
            for (int i = 0; i < excelHeaderFields.length; i++) {
                Cell tempCell = dataRow.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getRelateCode()) ? info.getRelateCode() : "");
                        break;
                    case 1:
                        tempCell.setCellValue(info.getOrderDate() != null ? sml.format(info.getOrderDate()) : "");
                        break;
                    case 2:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getReceivingStore()) ? info.getReceivingStore() : "");
                        break;
                    case 3:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getSkuCode()) ? info.getSkuCode() : "");
                        break;
                    case 4:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getSkuName()) ? info.getSkuName() : "");
                        break;
                    case 5:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getSkuClass()) ? info.getSkuClass() : "");
                        break;
                    case 6:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getTemperatureName()) ? info.getTemperatureName() : "");
                        break;
                    case 7:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getSpecification()) ? info.getSpecification() : "");
                        break;
                    case 8:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getUnit()) ? info.getUnit() : "");
                        break;
                    case 9:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getBoxType()) ? info.getBoxType() : "");
                        break;
                    case 10:
                        tempCell.setCellValue(null != info.getTotalBoxes() ? Double.parseDouble(info.getTotalBoxes().toString()) : 0);
                        break;
                    case 11:
                        tempCell.setCellValue(null != info.getOddBoxes() ? Double.parseDouble(info.getOddBoxes().toString()) : 0);
                        break;
                    case 12:
                        tempCell.setCellValue(null != info.getContentsNumber() ? Double.parseDouble(info.getContentsNumber().toString()) : 0);
                        break;
                    case 13:
                        tempCell.setCellValue(null != info.getWeight() ? Double.parseDouble(info.getWeight().toString()) : 0);
                        break;
                    case 14:
                        tempCell.setCellValue(null != info.getVolume() ? Double.parseDouble(info.getVolume().toString()) : 0);
                        break;
                    case 15:
                        tempCell.setCellValue(null != info.getTotalWeight() ? Double.parseDouble(info.getTotalWeight().toString()) : 0);
                        break;
                    case 16:
                        tempCell.setCellValue(null != info.getTotalVolume() ? Double.parseDouble(info.getTotalVolume().toString()) : 0);
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }

            sumAmt[0] = sumAmt[0].add(info.getTotalBoxes() == null ? new BigDecimal("0") : info.getTotalBoxes());
            sumAmt[1] = sumAmt[1].add(info.getOddBoxes() == null ? new BigDecimal("0") : info.getOddBoxes());
            sumAmt[2] = sumAmt[2].add(info.getContentsNumber() == null ? new BigDecimal("0") : info.getContentsNumber());
            sumAmt[3] = sumAmt[3].add(info.getWeight() == null ? new BigDecimal("0") : info.getWeight());
            sumAmt[4] = sumAmt[4].add(info.getVolume() == null ? new BigDecimal("0") : info.getVolume());
            sumAmt[5] = sumAmt[5].add(info.getTotalWeight() == null ? new BigDecimal("0") : info.getTotalWeight());
            sumAmt[6] = sumAmt[6].add(info.getTotalVolume() == null ? new BigDecimal("0") : info.getTotalVolume());
        }

        //最后一行 合计金额
        Row lastRow = sheet.createRow(rowNum[0]++);
        String[] lastRowFields = {"合计", "", "", "", "", "", "", "", "", ""
                , sumAmt[0].toString(), sumAmt[1].toString(), sumAmt[2].toString(), sumAmt[3].toString(), sumAmt[4].toString(), sumAmt[5].toString()
                , sumAmt[6].toString()
        };
        for (int i = 0; i < lastRowFields.length; i++) {
            Cell tempCell = lastRow.createCell(i);

            if (i >= 10) {
                tempCell.setCellValue(StrUtil.isNotEmpty(lastRowFields[i]) ? Double.parseDouble(lastRowFields[i]) : 0);
            } else {
                tempCell.setCellValue(lastRowFields[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 0, 9));
        return hssfWorkbook;
    }


    /**
     * 第五个sheet 出库明细
     */
    public SXSSFWorkbook outWarehousDetailSummary(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, Long id) {
        String tenantid = RequestContext.getTenantId();
        // 第5个sheet
        Sheet sheet4 = hssfWorkbook.createSheet("出库明细");
        // 背景色的设定
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum4 = {0};
        List<BmsYsBillExportInfo> stkList = ysState.getStorageOutWarehouseGoodsDetailByBillId(tenantid, id);
        BigDecimal[] amtf = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        Boolean[] fare = new Boolean[22];
        long time1 = System.currentTimeMillis();
        // 判断是否要隐藏列
        for (BmsYsBillExportInfo bmsYsBillExportInfo : stkList) {
            amtf[0] = amtf[0].add(bmsYsBillExportInfo.getFreight());
            amtf[1] = amtf[1].add(bmsYsBillExportInfo.getUltrafarFee());
            amtf[2] = amtf[2].add(bmsYsBillExportInfo.getSuperframesFee());
            amtf[3] = amtf[3].add(bmsYsBillExportInfo.getExcessFee());
            amtf[4] = amtf[4].add(bmsYsBillExportInfo.getDeliveryFee());
            amtf[5] = amtf[5].add(bmsYsBillExportInfo.getExceptionFee());
            amtf[6] = amtf[6].add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()));
            amtf[7] = amtf[7].add(bmsYsBillExportInfo.getReturnFee());
            amtf[8] = amtf[8].add(bmsYsBillExportInfo.getShortbargeFee());
            amtf[9] = amtf[9].add(bmsYsBillExportInfo.getOtherCost1());
            amtf[10] = amtf[10].add(bmsYsBillExportInfo.getOtherCost2());
            amtf[11] = amtf[11].add(bmsYsBillExportInfo.getOtherCost3());
            amtf[12] = amtf[12].add(bmsYsBillExportInfo.getOtherCost4());
            amtf[13] = amtf[13].add(bmsYsBillExportInfo.getOtherCost5());
            amtf[14] = amtf[14].add(bmsYsBillExportInfo.getOtherCost6());
            amtf[15] = amtf[15].add(bmsYsBillExportInfo.getOtherCost7());
            amtf[16] = amtf[16].add(bmsYsBillExportInfo.getOtherCost8());
            amtf[17] = amtf[17].add(bmsYsBillExportInfo.getOtherCost9());
            amtf[18] = amtf[18].add(bmsYsBillExportInfo.getOtherCost10());
            amtf[19] = amtf[19].add(bmsYsBillExportInfo.getOtherCost11());
            amtf[20] = amtf[20].add(bmsYsBillExportInfo.getOtherCost12());
            amtf[21] = amtf[21].add(BigDecimal.valueOf(bmsYsBillExportInfo.getSumAmt()));
        }

        System.out.println("计算费用-花费：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
        time1 = System.currentTimeMillis();
        int fi = 0;
        for (BigDecimal bigDecimal : amtf) {
            fare[fi] = bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0;
            fi++;
        }

        System.out.println("判断是否隐藏-花费：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
        time1 = System.currentTimeMillis();
        //设置列宽
        for (int i = 0; i < fare.length; i++) {
            if (fare[i] != null && fare[i]) {
                sheet4.setColumnWidth(i + 18, 0);
            }
        }

        System.out.println("设置列宽-花费：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
        time1 = System.currentTimeMillis();
        //第1行
        Row s2r2 = sheet4.createRow(rowNum4[0]++);
        s2r2.setHeight((short) 500);
        String[] s4row2 = {"出库时间", "出库单号"
                , "外部订单号", "订单类型"
                , "费用单号", "物料编码", "商品名称", "温层", "规格型号", "单位",
                "出库件数", "总箱数", "整箱数", "拆零件数", "总重量（kg）", "总重量（T）", "总货值", "出库门店",
                "存储费", "管理处置费", "整箱分拣费", "拆零分拣费",
                "装卸费", "制单费", "操作费", "客诉理赔费", "调账费", "调账备注"
                , "otherCost1", "otherCost2", "otherCost3",
                "otherCost4", "otherCost5", "otherCost6", "otherCost7", "otherCost8",
                "otherCost9", "otherCost10", "otherCost11", "otherCost12",
                "总费用", "计费备注"};
        for (int i = 0; i < s4row2.length; i++) {
            String name = s4row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            if (StrUtil.isNotEmpty(dicMap.get("ys_storage_othercost" + name))) {
                otherFeeInfo.setOtherName(dicMap.get("ys_storage_othercost" + name));
            }
            switch (name) {
                case "客诉理赔费":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcExceptionFee());
                    break;
                case "otherCost1":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost1());
                    break;
                case "otherCost2":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost2());
                    break;
                case "otherCost3":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost3());
                    break;
                case "otherCost4":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost4());
                    break;
                case "otherCost5":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost5());
                    break;
                case "otherCost6":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost6());
                    break;
                case "otherCost7":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost7());
                    break;
                case "otherCost8":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost8());
                    break;
                case "otherCost9":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost9());
                    break;
                case "otherCost10":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost10());
                    break;
                case "otherCost11":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost11());
                    break;
                case "otherCost12":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost12());
                    break;
                default:
                    otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
            }
            s4row2[i] = otherFeeInfo.getOtherName();
        }
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        String lastId = "";
        int row = 1;
        System.out.println("动态费用字段-花费：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
        time1 = System.currentTimeMillis();
        for (int i = 0; i < s4row2.length; i++) {
            Cell tempCell = s2r2.createCell(i);
            tempCell.setCellValue(s4row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        System.out.println("动态费用字段列格式-花费：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
        time1 = System.currentTimeMillis();
        System.out.println("开始创建数据行----------------------");
        // 创建数据行，获取出库明细
        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd");

        // TODO: 因为合并列非常影响性能，所以判断如果超过两万条数据，则不合并列，否则会导致卡死、内存泄漏等问题
        if (stkList.size() <= 2000) {
            for (int p = 0; p < stkList.size(); p++) {
                BmsYsBillExportInfo info = stkList.get(p);
                Row s2r3 = sheet4.createRow(rowNum4[0]++);

                s2r3.setHeight((short) 500);
                /* 处理 合并费用表格后，合并格金额与表格金额不一致的问题*/
                boolean f = true;
                // 赋值
                f = p <= 0 || p >= stkList.size() || !stkList.get(p).getId().equals(stkList.get(p - 1).getId()); // 赋0

                if (StrUtil.isNotEmpty(info.getOrderType()) && dicMap.containsKey("order_type" + info.getOrderType())) {
                    info.setOrderTypeName(dicMap.get("order_type" + info.getOrderType()));
                }
                for (int i = 0; i < s4row2.length; i++) {
                    Cell tempCell = s2r3.createCell(i);
                    switch (i) {
                        case 0:
                            tempCell.setCellValue(info.getSigningDate() != null ? sml.format(info.getSigningDate()) : "");
                            break;
                        case 1:
                            tempCell.setCellValue(info.getRelateCode());
                            break;
                        case 2:
                            tempCell.setCellValue(info.getOrderNo());
                            break;
                        case 3:
                            tempCell.setCellValue(info.getOrderTypeName());
                            break;
                        case 4:
                            tempCell.setCellValue(info.getExpensesCode());
                            break;
                        case 5:
                            tempCell.setCellValue(info.getSkuCode());
                            break;
                        case 6:
                            tempCell.setCellValue(info.getSkuName());
                            break;
                        case 7:
                            tempCell.setCellValue(info.getTemperatureName());
                            break;
                        case 8:
                            tempCell.setCellValue(info.getBoxType());
                            break;
                        case 9:
                            //tempCell.setCellValue(info.getUnit());
                            tempCell.setCellValue("");
                            break;
                        case 10:
                            tempCell.setCellValue(info.getContentsNumber() == null ? 0 : Double.parseDouble(info.getContentsNumber().toString()));
                            break;
                        case 11:
                            tempCell.setCellValue(Double.parseDouble(info.getTotalBoxes().add(info.getOddBoxes()).toString()));
                            break;
                        case 12:
                            tempCell.setCellValue(info.getTotalBoxes() == null ? 0 : Double.parseDouble(info.getTotalBoxes().toString()));
                            break;
                        case 13:
                            tempCell.setCellValue(info.getOddBoxes() == null ? 0 : Double.parseDouble(info.getOddBoxes().toString()));
                            break;
                        case 14:
                            tempCell.setCellValue(info.getWeight() == null ? 0 : Double.parseDouble(info.getWeight().toString()));
                            break;
                        case 15:
                            tempCell.setCellValue(info.getTotalWeight() == null ? 0 : Double.parseDouble(info.getTotalWeight().toString()));
                            break;
                        case 16:
                            tempCell.setCellValue(!f ? 0 : info.getCargoValue() == null ? 0 : Double.parseDouble(info.getCargoValue().toString()));
                            break;
                        case 17:
                            tempCell.setCellValue(info.getReceivingStore());
                            break;
                        case 18:
                            tempCell.setCellValue(!f ? 0 : info.getFreight() == null ? 0 : Double.parseDouble(info.getFreight().toString()));
                            break;
                        case 19:
                            tempCell.setCellValue(!f ? 0 : info.getUltrafarFee() == null ? 0 : Double.parseDouble(info.getUltrafarFee().toString()));
                            break;
                        case 20:
                            tempCell.setCellValue(!f ? 0 : info.getSuperframesFee() == null ? 0 : Double.parseDouble(info.getSuperframesFee().toString()));
                            break;
                        case 21:
                            tempCell.setCellValue(!f ? 0 : info.getExcessFee() == null ? 0 : Double.parseDouble(info.getExcessFee().toString()));
                            break;
                        case 22:
                            tempCell.setCellValue(!f ? 0 : info.getDeliveryFee() == null ? 0 : Double.parseDouble(info.getDeliveryFee().toString()));
                            break;
                        case 23:
                            tempCell.setCellValue(!f ? 0 : info.getExceptionFee() == null ? 0 : Double.parseDouble(info.getExceptionFee().toString()));
                            break;
                        case 24:
                            tempCell.setCellValue(!f ? 0 : info.getAdjustFee() == null ? 0 : Double.parseDouble(info.getAdjustFee().toString()));
                            break;
                        case 25:
                            tempCell.setCellValue(!f ? 0 : info.getReturnFee() == null ? 0 : Double.parseDouble(info.getReturnFee().toString()));
                            break;
                        case 26:
                            tempCell.setCellValue(!f ? 0 : info.getShortbargeFee() == null ? 0 : Double.parseDouble(info.getShortbargeFee().toString()));
                            break;
                        case 27:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost1() == null ? 0 : Double.parseDouble(info.getOtherCost1().toString()));
                            break;
                        case 28:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost2() == null ? 0 : Double.parseDouble(info.getOtherCost2().toString()));
                            break;
                        case 29:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost3() == null ? 0 : Double.parseDouble(info.getOtherCost3().toString()));
                            break;
                        case 30:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost4() == null ? 0 : Double.parseDouble(info.getOtherCost4().toString()));
                            break;
                        case 31:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost5() == null ? 0 : Double.parseDouble(info.getOtherCost5().toString()));
                            break;
                        case 32:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost6() == null ? 0 : Double.parseDouble(info.getOtherCost6().toString()));
                            break;
                        case 33:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost7() == null ? 0 : Double.parseDouble(info.getOtherCost7().toString()));
                            break;
                        case 34:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost8() == null ? 0 : Double.parseDouble(info.getOtherCost8().toString()));
                            break;
                        case 35:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost9() == null ? 0 : Double.parseDouble(info.getOtherCost9().toString()));
                            break;
                        case 36:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost10() == null ? 0 : Double.parseDouble(info.getOtherCost10().toString()));
                            break;
                        case 37:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost11() == null ? 0 : Double.parseDouble(info.getOtherCost11().toString()));
                            break;
                        case 38:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost12() == null ? 0 : Double.parseDouble(info.getOtherCost12().toString()));
                            break;
                        case 39:
                            tempCell.setCellValue(!f ? 0 : info.getSumAmt() == null ? 0 : Double.parseDouble(info.getSumAmt().toString()));
                            break;
                        case 40:
                            tempCell.setCellValue(info.getAutomaticBillingRemark());
                            break;
                        default:
                            tempCell.setCellValue("");
                    }
                    tempCell.setCellStyle(commonStyle);

                    /* 处理 合并费用表格*/
                    if ((p == stkList.size() - 1 && row < rowNum4[0] - 1) || (row < rowNum4[0] - 1 && p + 1 < stkList.size() && !lastId.equals(stkList.get(p + 1).getId()))) {
                        for (int j = 12; j <= s4row2.length; j++) {
                            if (j >= 17) {
                                sheet4.addMergedRegion(new CellRangeAddress(row, rowNum4[0] - 1, j, j));
                            }
                        }
                        lastId = info.getId();
                        row = rowNum4[0];


                    } else {
                        lastId = info.getId();
                    }

                    if ((p == stkList.size() - 1 && row == rowNum4[0] - 1) || (row == rowNum4[0] - 1 && p + 1 < stkList.size() && !lastId.equals(stkList.get(p + 1).getId()))) {
                        row = rowNum4[0];
                    }

                }
                if (p % 1000 == 0) {
                    System.out.println("创建的第：" + p + "列数据,本次花费：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
                    time1 = System.currentTimeMillis();
                }


                sumAmt[29] = sumAmt[29].add(info.getTotalBoxes());
                sumAmt[30] = sumAmt[30].add(info.getOddBoxes());
                if (f) {
                    sumAmt[2] = sumAmt[2].add(info.getSuperframesFee());
                    sumAmt[3] = sumAmt[3].add(info.getExcessFee());
                    sumAmt[4] = sumAmt[4].add(info.getDeliveryFee());
                    sumAmt[5] = sumAmt[5].add(info.getExceptionFee());
                    sumAmt[6] = sumAmt[6].add(info.getOtherCost1());
                    sumAmt[7] = sumAmt[7].add(info.getOtherCost2());
                    sumAmt[8] = sumAmt[8].add(info.getOtherCost3());
                    sumAmt[9] = sumAmt[9].add(info.getOtherCost4());
                    sumAmt[10] = sumAmt[10].add(info.getOtherCost5());
                    sumAmt[11] = sumAmt[11].add(info.getOtherCost6());
                    sumAmt[12] = sumAmt[12].add(info.getOtherCost7());
                    sumAmt[13] = sumAmt[13].add(info.getOtherCost8());
                    sumAmt[14] = sumAmt[14].add(info.getOtherCost9());
                    sumAmt[15] = sumAmt[15].add(info.getOtherCost10());
                    sumAmt[16] = sumAmt[16].add(info.getOtherCost11());
                    sumAmt[17] = sumAmt[17].add(info.getOtherCost12());
                    sumAmt[18] = sumAmt[18].add(BigDecimal.valueOf(info.getBasicsSum()));
                    sumAmt[19] = sumAmt[19].add(BigDecimal.valueOf(info.getOtherSum()));
                    sumAmt[20] = sumAmt[20].add(BigDecimal.valueOf(info.getSumAmt()));
                    sumAmt[21] = sumAmt[21].add(info.getReturnFee());
                    sumAmt[22] = sumAmt[22].add(info.getShortbargeFee());
                    sumAmt[26] = sumAmt[26].add(info.getFreight());
                    sumAmt[27] = sumAmt[27].add(BigDecimal.valueOf(info.getAdjustFee()));
                    sumAmt[28] = sumAmt[28].add(info.getUltrafarFee());
                    sumAmt[31] = sumAmt[31].add(info.getCargoValue());
                }
            }
        } else {
            for (int p = 0; p < stkList.size(); p++) {
                BmsYsBillExportInfo info = stkList.get(p);
                Row s2r3 = sheet4.createRow(rowNum4[0]++);
                s2r3.setHeight((short) 500);
                /* 处理 合并费用表格后，合并格金额与表格金额不一致的问题*/
                boolean f = true;
                // 赋值
                f = p <= 0 || p >= stkList.size() || !stkList.get(p).getId().equals(stkList.get(p - 1).getId()); // 赋0
                if (StrUtil.isNotEmpty(info.getOrderType()) && dicMap.containsKey("order_type" + info.getOrderType())) {
                    info.setOrderTypeName(dicMap.get("order_type" + info.getOrderType()));
                }
                for (int i = 0; i < s4row2.length; i++) {
                    Cell tempCell = s2r3.createCell(i);
                    switch (i) {
                        case 0:
                            tempCell.setCellValue(info.getSigningDate() != null ? sml.format(info.getSigningDate()) : "");
                            break;
                        case 1:
                            tempCell.setCellValue(info.getRelateCode());
                            break;
                        case 2:
                            tempCell.setCellValue(info.getOrderNo());
                            break;
                        case 3:
                            tempCell.setCellValue(info.getOrderTypeName());
                            break;
                        case 4:
                            tempCell.setCellValue(info.getExpensesCode());
                            break;
                        case 5:
                            tempCell.setCellValue(info.getSkuCode());
                            break;
                        case 6:
                            tempCell.setCellValue(info.getSkuName());
                            break;
                        case 7:
                            tempCell.setCellValue(info.getTemperatureName());
                            break;
                        case 8:
                            tempCell.setCellValue(info.getBoxType());
                            break;
                        case 9:
                            tempCell.setCellValue("");
                            break;
                        case 10:
                            tempCell.setCellValue(info.getContentsNumber() == null ? 0 : Double.parseDouble(info.getContentsNumber().toString()));
                            break;
                        case 11:
                            tempCell.setCellValue(Double.parseDouble(info.getTotalBoxes().add(info.getOddBoxes()).toString()));
                            break;
                        case 12:
                            tempCell.setCellValue(info.getTotalBoxes() == null ? 0 : Double.parseDouble(info.getTotalBoxes().toString()));
                            break;
                        case 13:
                            tempCell.setCellValue(info.getOddBoxes() == null ? 0 : Double.parseDouble(info.getOddBoxes().toString()));
                            break;
                        case 14:
                            tempCell.setCellValue(info.getWeight() == null ? 0 : Double.parseDouble(info.getWeight().toString()));
                            break;
                        case 15:
                            tempCell.setCellValue(info.getTotalWeight() == null ? 0 : Double.parseDouble(info.getTotalWeight().toString()));
                            break;
                        case 16:
                            tempCell.setCellValue(!f ? 0 : info.getCargoValue() == null ? 0 : Double.parseDouble(info.getCargoValue().toString()));
                            break;
                        case 17:
                            tempCell.setCellValue(info.getReceivingStore());
                            break;
                        case 18:
                            tempCell.setCellValue(!f ? 0 : info.getFreight() == null ? 0 : Double.parseDouble(info.getFreight().toString()));
                            break;
                        case 19:
                            tempCell.setCellValue(!f ? 0 : info.getUltrafarFee() == null ? 0 : Double.parseDouble(info.getUltrafarFee().toString()));
                            break;
                        case 20:
                            tempCell.setCellValue(!f ? 0 : info.getSuperframesFee() == null ? 0 : Double.parseDouble(info.getSuperframesFee().toString()));
                            break;
                        case 21:
                            tempCell.setCellValue(!f ? 0 : info.getExcessFee() == null ? 0 : Double.parseDouble(info.getExcessFee().toString()));
                            break;
                        case 22:
                            tempCell.setCellValue(!f ? 0 : info.getDeliveryFee() == null ? 0 : Double.parseDouble(info.getDeliveryFee().toString()));
                            break;
                        case 23:
                            tempCell.setCellValue(!f ? 0 : info.getExceptionFee() == null ? 0 : Double.parseDouble(info.getExceptionFee().toString()));
                            break;
                        case 24:
                            tempCell.setCellValue(!f ? 0 : info.getAdjustFee() == null ? 0 : Double.parseDouble(info.getAdjustFee().toString()));
                            break;
                        case 25:
                            tempCell.setCellValue(!f ? 0 : info.getReturnFee() == null ? 0 : Double.parseDouble(info.getReturnFee().toString()));
                            break;
                        case 26:
                            tempCell.setCellValue(!f ? 0 : info.getShortbargeFee() == null ? 0 : Double.parseDouble(info.getShortbargeFee().toString()));
                            break;
                        case 27:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost1() == null ? 0 : Double.parseDouble(info.getOtherCost1().toString()));
                            break;
                        case 28:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost2() == null ? 0 : Double.parseDouble(info.getOtherCost2().toString()));
                            break;
                        case 29:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost3() == null ? 0 : Double.parseDouble(info.getOtherCost3().toString()));
                            break;
                        case 30:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost4() == null ? 0 : Double.parseDouble(info.getOtherCost4().toString()));
                            break;
                        case 31:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost5() == null ? 0 : Double.parseDouble(info.getOtherCost5().toString()));
                            break;
                        case 32:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost6() == null ? 0 : Double.parseDouble(info.getOtherCost6().toString()));
                            break;
                        case 33:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost7() == null ? 0 : Double.parseDouble(info.getOtherCost7().toString()));
                            break;
                        case 34:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost8() == null ? 0 : Double.parseDouble(info.getOtherCost8().toString()));
                            break;
                        case 35:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost9() == null ? 0 : Double.parseDouble(info.getOtherCost9().toString()));
                            break;
                        case 36:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost10() == null ? 0 : Double.parseDouble(info.getOtherCost10().toString()));
                            break;
                        case 37:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost11() == null ? 0 : Double.parseDouble(info.getOtherCost11().toString()));
                            break;
                        case 38:
                            tempCell.setCellValue(!f ? 0 : info.getOtherCost12() == null ? 0 : Double.parseDouble(info.getOtherCost12().toString()));
                            break;
                        case 39:
                            tempCell.setCellValue(!f ? 0 : info.getSumAmt() == null ? 0 : Double.parseDouble(info.getSumAmt().toString()));
                            break;
                        case 40:
                            tempCell.setCellValue(info.getAutomaticBillingRemark());
                            break;
                        default:
                            tempCell.setCellValue("");
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                if (p % 10000 == 0) {
                    System.out.println("创建的第：" + p + "列数据,本次花费：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
                    time1 = System.currentTimeMillis();
                }
                sumAmt[29] = sumAmt[29].add(info.getTotalBoxes());
                sumAmt[30] = sumAmt[30].add(info.getOddBoxes());
                if (f) {
                    sumAmt[2] = sumAmt[2].add(info.getSuperframesFee());
                    sumAmt[3] = sumAmt[3].add(info.getExcessFee());
                    sumAmt[4] = sumAmt[4].add(info.getDeliveryFee());
                    sumAmt[5] = sumAmt[5].add(info.getExceptionFee());
                    sumAmt[6] = sumAmt[6].add(info.getOtherCost1());
                    sumAmt[7] = sumAmt[7].add(info.getOtherCost2());
                    sumAmt[8] = sumAmt[8].add(info.getOtherCost3());
                    sumAmt[9] = sumAmt[9].add(info.getOtherCost4());
                    sumAmt[10] = sumAmt[10].add(info.getOtherCost5());
                    sumAmt[11] = sumAmt[11].add(info.getOtherCost6());
                    sumAmt[12] = sumAmt[12].add(info.getOtherCost7());
                    sumAmt[13] = sumAmt[13].add(info.getOtherCost8());
                    sumAmt[14] = sumAmt[14].add(info.getOtherCost9());
                    sumAmt[15] = sumAmt[15].add(info.getOtherCost10());
                    sumAmt[16] = sumAmt[16].add(info.getOtherCost11());
                    sumAmt[17] = sumAmt[17].add(info.getOtherCost12());
                    sumAmt[18] = sumAmt[18].add(BigDecimal.valueOf(info.getBasicsSum()));
                    sumAmt[19] = sumAmt[19].add(BigDecimal.valueOf(info.getOtherSum()));
                    sumAmt[20] = sumAmt[20].add(BigDecimal.valueOf(info.getSumAmt()));
                    sumAmt[21] = sumAmt[21].add(info.getReturnFee());
                    sumAmt[22] = sumAmt[22].add(info.getShortbargeFee());
                    sumAmt[26] = sumAmt[26].add(info.getFreight());
                    sumAmt[27] = sumAmt[27].add(BigDecimal.valueOf(info.getAdjustFee()));
                    sumAmt[28] = sumAmt[28].add(info.getUltrafarFee());
                    sumAmt[31] = sumAmt[31].add(info.getCargoValue());
                }
            }
        }


        //最后一行 合计金额
        Row s2r4 = sheet4.createRow(rowNum4[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"合计：", "", "", "", "", "", "", "", "", "", "", sumAmt[29].add(sumAmt[30]).toString(), sumAmt[29].toString(), sumAmt[30].toString(), "", "", sumAmt[31].toString(), "",
                sumAmt[26].toString(), sumAmt[28].toString(), sumAmt[2].toString(), sumAmt[3].toString(),
                sumAmt[4].toString(), sumAmt[5].toString(), sumAmt[27].toString(), sumAmt[21].toString(), sumAmt[22].toString(),
                sumAmt[6].toString(), sumAmt[7].toString(), sumAmt[8].toString(),
                sumAmt[9].toString(), sumAmt[10].toString(), sumAmt[11].toString(),
                sumAmt[12].toString(), sumAmt[13].toString(), sumAmt[14].toString(),
                sumAmt[15].toString(), sumAmt[16].toString(), sumAmt[17].toString(),
                sumAmt[20].toString(), ""
        };
        System.out.println("创建数据行-花费：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
        time1 = System.currentTimeMillis();
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if (i > 15 || i == 10 || i == 11 || i == 14) {
                if (StrUtil.isNotEmpty(s2row4[i])) {
                    tempCell.setCellValue(Double.parseDouble(s2row4[i]));
                } else {
                    tempCell.setCellValue(s2row4[i]);
                }
            } else {
                tempCell.setCellValue(s2row4[i]);
            }

            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }

        System.out.println("设置列类型-花费：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
        time1 = System.currentTimeMillis();
        // 合计行合并
        sheet4.addMergedRegion(new CellRangeAddress(rowNum4[0] - 1, rowNum4[0] - 1, 0, 7));
        return hssfWorkbook;
    }


    /**
     * 出库费主信息
     */
    public SXSSFWorkbook outWarehouseSummaryMain(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid = RequestContext.getTenantId();
        Sheet sheet = hssfWorkbook.createSheet("出库费主信息");
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        //TODO 1、数据准备
        List<BmsYsBillExportInfo> excelDatas = new ArrayList<>();
        List<BmsYsBillExportInfo> bmsYsBillExportInfos = ysState.outWarehouseSummaryMain(tenantid, ids);
        if (CollUtil.isNotEmpty(bmsYsBillExportInfos)) {
            excelDatas = bmsYsBillExportInfos;
        }
        Map<String, SysDept> companyMap = textConversionUtil.getCompanyMap("1", excelDatas.stream().filter(e -> e.getCompanyId() != null).map(e -> e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        excelDatas.forEach(e -> {
            if (e.getCompanyId() != null && companyMap.containsKey(e.getCompanyId().toString())) {
                e.setCompanyName(companyMap.get(e.getCompanyId().toString()).getDeptName());
            }
            if (StrUtil.isNotEmpty(e.getOrderType()) && dicMap.containsKey("order_type" + e.getOrderType())) {
                e.setOrderTypeName(dicMap.get("order_type" + e.getOrderType()));
            }
        });

        //隐藏列
        BigDecimal[] amtf = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        Boolean[] fare = new Boolean[37];
        // 判断是否要隐藏列
        assert bmsYsBillExportInfos != null;
        for (BmsYsBillExportInfo bmsYsBillExportInfo : bmsYsBillExportInfos) {
            amtf[0] = amtf[0].add(bmsYsBillExportInfo.getFreight());
            amtf[1] = amtf[1].add(bmsYsBillExportInfo.getUltrafarFee());
            amtf[2] = amtf[2].add(bmsYsBillExportInfo.getSuperframesFee());
            amtf[3] = amtf[3].add(bmsYsBillExportInfo.getExcessFee());
            amtf[4] = amtf[4].add(bmsYsBillExportInfo.getDeliveryFee());
            amtf[5] = amtf[5].add(bmsYsBillExportInfo.getReturnFee());
            amtf[6] = amtf[6].add(bmsYsBillExportInfo.getShortbargeFee());
            amtf[7] = amtf[7].add(bmsYsBillExportInfo.getExceptionFee());
            amtf[8] = amtf[8].add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()));
            amtf[9] = amtf[9].add(new BigDecimal(StrUtil.isEmpty(bmsYsBillExportInfo.getAdjustRemark()) ? 0 : 1));
            amtf[10] = amtf[10].add(bmsYsBillExportInfo.getOtherCost1());
            amtf[11] = amtf[11].add(bmsYsBillExportInfo.getOtherCost2());
            amtf[12] = amtf[12].add(bmsYsBillExportInfo.getOtherCost3());
            amtf[13] = amtf[13].add(bmsYsBillExportInfo.getOtherCost4());
            amtf[14] = amtf[14].add(bmsYsBillExportInfo.getOtherCost5());
            amtf[15] = amtf[15].add(bmsYsBillExportInfo.getOtherCost6());
            amtf[16] = amtf[16].add(bmsYsBillExportInfo.getOtherCost7());
            amtf[17] = amtf[17].add(bmsYsBillExportInfo.getOtherCost8());
            amtf[18] = amtf[18].add(bmsYsBillExportInfo.getOtherCost9());
            amtf[19] = amtf[19].add(bmsYsBillExportInfo.getOtherCost10());
            amtf[20] = amtf[20].add(bmsYsBillExportInfo.getOtherCost11());
            amtf[21] = amtf[21].add(bmsYsBillExportInfo.getOtherCost12());
            amtf[22] = amtf[22].add(BigDecimal.valueOf(bmsYsBillExportInfo.getSumAmt()));
        }
        int fi = 0;
        for (BigDecimal bigDecimal : amtf) {
            fare[fi] = bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0;
            fi++;
        }
        //设置列宽
        for (int i = 0; i < fare.length; i++) {
            if (fare[i] != null && fare[i]) {
                sheet.setColumnWidth(i + 36, 0);
            }
        }

        // 2、组装数值数据
        //行号
        final int[] rowNum = {0};
        String[] excelHeaderFields = {
                "订单时间", "出库时间", "出库单号", "外部订单号", "交易单号", "计费类型", "费用单号", "费用维度", "订单类型", "配送方式",
                "单据所属", "发货仓库", "门店编码", "门店名称", "目的省份", "目的城市", "目的区域", "拆零总件数", "总件数", "总重量（kg）", "总重量（T）", "总体积(m³)", "常温整箱数", "常温散箱数",
                "非常温整箱数", "非常温散箱数", "总箱数", "常温托数", "冷藏托数", "冷冻托数", "总托数", "总货值", "计费人", "计费备注", "备注",
                "是否超时",
                "存储费", "管理处置费", "整箱分拣费", "拆零分拣费", "装卸费", "制单费", "操作费", "客诉理赔费", "调账费", "调账备注", "缠膜费",
                "泡沫箱", "复冻费", "贴标费", "盘盈亏", "装车费", "快递费", "其他费8", "上楼费", "退货入库费", "处理费", "其他费", "总费用"
        };

        //所有费用列的下标(处理保留2位小数的问题)
        Set<Integer> feeColumns = new HashSet<>(Arrays.asList(36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58));
        Row headerRow = sheet.createRow(rowNum[0]++);
        headerRow.setHeight((short) 500);

        for (int i = 0; i < excelHeaderFields.length; i++) {
            //其他费字典
            String name = excelHeaderFields[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            if (StrUtil.isNotEmpty(dicMap.get("ys_storage_othercost" + name))) {
                otherFeeInfo.setOtherName(dicMap.get("ys_storage_othercost" + name));
            }
            switch (name) {
                case "客诉理赔费":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcExceptionFee());
                    break;
                case "otherCost1":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost1());
                    break;
                case "otherCost2":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost2());
                    break;
                case "otherCost3":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost3());
                    break;
                case "otherCost4":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost4());
                    break;
                case "otherCost5":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost5());
                    break;
                case "otherCost6":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost6());
                    break;
                case "otherCost7":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost7());
                    break;
                case "otherCost8":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost8());
                    break;
                case "otherCost9":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost9());
                    break;
                case "otherCost10":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost10());
                    break;
                case "otherCost11":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost11());
                    break;
                case "otherCost12":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost12());
                    break;
                default:
                    otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
            }
            excelHeaderFields[i] = otherFeeInfo.getOtherName();
        }

        for (int i = 0; i < excelHeaderFields.length; i++) {
            Cell tempCell = headerRow.createCell(i);
            tempCell.setCellValue(excelHeaderFields[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        //合计费用列的合计金额
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),    //总件数
                new BigDecimal("0"),    //总重量(KG)
                new BigDecimal("0"),    //总重量(T)
                new BigDecimal("0"),    //总体积(m³)
                new BigDecimal("0"),    //常温整箱数
                new BigDecimal("0"),    //常温散箱数
                new BigDecimal("0"),    //非常温整箱数
                new BigDecimal("0"),    //非常温散箱数
                new BigDecimal("0"),    //总箱数
                new BigDecimal("0"),    //常温托数
                new BigDecimal("0"),    //冷藏托数
                new BigDecimal("0"),    //冷冻托数
                new BigDecimal("0"),    //总托数
                new BigDecimal("0"),    //总货值
                new BigDecimal("0"),    //存储费
                new BigDecimal("0"),    //管理处置费
                new BigDecimal("0"),    //整箱分拣费
                new BigDecimal("0"),    //拆零分拣费
                new BigDecimal("0"),    //装卸费
                new BigDecimal("0"),    //装卸费
                new BigDecimal("0"),    //操作费
                new BigDecimal("0"),    //异常赔付费
                new BigDecimal("0"),    //调账费
                new BigDecimal("0"),    //缠膜费
                new BigDecimal("0"),    //泡沫箱
                new BigDecimal("0"),    //复冻费
                new BigDecimal("0"),    //贴标费
                new BigDecimal("0"),    //盘盈亏
                new BigDecimal("0"),    //装车费
                new BigDecimal("0"),    //快递费
                new BigDecimal("0"),    //餐费
                new BigDecimal("0"),    //上楼费
                new BigDecimal("0"),    //退货入库费
                new BigDecimal("0"),    //处理费
                new BigDecimal("0"),    //其他费
                new BigDecimal("0"),     //总费用
                new BigDecimal("0")     //拆零总件数
        };

        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd");
        int row = 1;
        Map<String, BigDecimal> result = excelDatas.stream()
                .collect(Collectors.groupingBy(BmsYsBillExportInfo::getExpensesCode,
                        Collectors.reducing(BigDecimal.ZERO, BmsYsBillExportInfo::getCargoValue, BigDecimal::add)));
        //加载行数
        for (int s = 0; s < excelDatas.size(); s++) {
            BmsYsBillExportInfo info = excelDatas.get(s);
            Row dataRow = sheet.createRow(rowNum[0]++);
            dataRow.setHeight((short) 500);
            boolean f = true;
            // 赋值
            f = s <= 0 || s >= excelDatas.size() || !excelDatas.get(s).getExpensesCode().equals(excelDatas.get(s - 1).getExpensesCode()); // 赋0
            for (int i = 0; i < excelHeaderFields.length; i++) {
                Cell tempCell = dataRow.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getOrderDate() != null ? sml.format(info.getOrderDate()) : "");
                        break;
                    case 1:
                        tempCell.setCellValue(info.getSigningDate() != null ? sml.format(info.getSigningDate()) : "");
                        break;
                    case 2:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getRelateCode()) ? info.getRelateCode() : "");
                        break;
                    case 3:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getOrderNo()) ? info.getOrderNo() : "");
                        break;
                    case 4:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getPlatformCode()) ? info.getPlatformCode() : "");
                        break;
                    case 5:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getChargeTypeName()) ? info.getChargeTypeName() : "");
                        break;
                    case 6:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getExpensesCode()) ? info.getExpensesCode() : "");
                        break;
                    case 7:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getCostDimensionName()) ? info.getCostDimensionName() : "");
                        break;
                    case 8:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getOrderTypeName()) ? info.getOrderTypeName() : "");
                        break;
                    case 9:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getTransportTypeName()) ? info.getTransportTypeName() : "");
                        break;
                    case 10:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getCompanyName()) ? info.getCompanyName() : "");
                        break;
                    case 11:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getWarehouseName()) ? info.getWarehouseName() : "");
                        break;
                    case 12:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getStoreCode()) ? info.getStoreCode() : "");
                        break;
                    case 13:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getReceivingStore()) ? info.getReceivingStore() : "");
                        break;
                    case 14:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getDestinationProvince()) ? info.getDestinationProvince() : "");
                        break;
                    case 15:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getDestinationCity()) ? info.getDestinationCity() : "");
                        break;
                    case 16:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getDestinationArea()) ? info.getDestinationArea() : "");
                        break;
                    case 17:
                        tempCell.setCellValue(null != info.getSplitTotalNumber() ? Double.parseDouble(info.getSplitTotalNumber().toString()) : 0); //拆零总件数
                        break;
                    case 18:
                        tempCell.setCellValue(null != info.getSkuNumber() ? Double.parseDouble(info.getSkuNumber().toString()) : 0);
                        break;
                    case 19:
                        tempCell.setCellValue(null != info.getWeight() ? Double.parseDouble(info.getWeight().toString()) : 0);
                        break;
                    case 20:
                        tempCell.setCellValue(null != info.getTotalWeight() ? Double.parseDouble(info.getTotalWeight().toString()) : 0);
                        break;
                    case 21:
                        tempCell.setCellValue(null != info.getTotalVolume() ? Double.parseDouble(info.getTotalVolume().toString()) : 0);
                        break;
                    case 22:
                        tempCell.setCellValue(null != info.getCwFullCases() ? info.getCwFullCases() : 0);
                        break;
                    case 23:
                        tempCell.setCellValue(null != info.getCwSplitCases() ? info.getCwSplitCases() : 0);
                        break;
                    case 24:
                        tempCell.setCellValue(null != info.getLdFullCases() ? info.getLdFullCases() : 0);
                        break;
                    case 25:
                        tempCell.setCellValue(null != info.getLdSplitCases() ? info.getLdSplitCases() : 0);
                        break;
                    case 26:
                        tempCell.setCellValue(null != info.getTotalBoxes() ? Double.parseDouble(info.getTotalBoxes().toString()) : 0);
                        break;
                    case 27:
                        tempCell.setCellValue(null != info.getCwPalletNumber() ? Double.parseDouble(info.getCwPalletNumber().toString()) : 0);
                        break;
                    case 28:
                        tempCell.setCellValue(null != info.getLcPalletNumber() ? Double.parseDouble(info.getLcPalletNumber().toString()) : 0);
                        break;
                    case 29:
                        tempCell.setCellValue(null != info.getLdPalletNumber() ? Double.parseDouble(info.getLdPalletNumber().toString()) : 0);
                        break;
                    case 30:
                        tempCell.setCellValue(null != info.getPalletNumber() ? Double.parseDouble(info.getPalletNumber().toString()) : 0);
                        break;
                    case 31:
                        String expensesCode = info.getExpensesCode();
                        BigDecimal bigDecimal = result.get(expensesCode);
                        tempCell.setCellValue(!f ? 0 : (bigDecimal != null ? bigDecimal.doubleValue() : 0));
                        break;
                    case 32:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getOperBy()) ? info.getOperBy() : "");
                        break;
                    case 33:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getAutomaticBillingRemark()) ? info.getAutomaticBillingRemark() : "");
                        break;
                    case 34:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getRemarks()) ? info.getRemarks() : "");
                        break;
                    case 35:
                        String isTimeOut = "";
                        if (info.getIsTimeOut() != null) {
                            isTimeOut = info.getIsTimeOut().equals(0) ? "是" : "否";
                        }
                        tempCell.setCellValue(isTimeOut);
                        break;
                    case 36:
                        tempCell.setCellValue(!f ? 0 : null != info.getFreight() ? info.getFreight().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //存储费
                        break;
                    case 37:
                        tempCell.setCellValue(!f ? 0 : null != info.getUltrafarFee() ? info.getUltrafarFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //管理处置费
                        break;
                    case 38:
                        tempCell.setCellValue(!f ? 0 : null != info.getSuperframesFee() ? info.getSuperframesFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //整箱分拣费
                        break;
                    case 39:
                        tempCell.setCellValue(!f ? 0 : null != info.getExcessFee() ? info.getExcessFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //拆零分拣费
                        break;
                    case 40:
                        tempCell.setCellValue(!f ? 0 : null != info.getDeliveryFee() ? info.getDeliveryFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //装卸费
                        break;
                    case 41:
                        tempCell.setCellValue(!f ? 0 : null != info.getReturnFee() ? info.getReturnFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); // 制单费
                        break;
                    case 42:
                        tempCell.setCellValue(!f ? 0 : null != info.getShortbargeFee() ? info.getShortbargeFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //操作费
                        break;
                    case 43:
                        tempCell.setCellValue(!f ? 0 : null != info.getExceptionFee() ? info.getExceptionFee().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0); //异常赔付费
                        break;
                    case 44:
                        tempCell.setCellValue(!f ? 0 : null != info.getAdjustFee() ? info.getAdjustFee() : 0); //调账费
                        break;
                    case 45:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getAdjustRemark()) ? info.getAdjustRemark() : ""); //调账备注
                        break;
                    case 46:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost1() ? info.getOtherCost1().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 47:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost2() ? info.getOtherCost2().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 48:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost3() ? info.getOtherCost3().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 49:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost4() ? info.getOtherCost4().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 50:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost5() ? info.getOtherCost5().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 51:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost6() ? info.getOtherCost6().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 52:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost7() ? info.getOtherCost7().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 53:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost8() ? info.getOtherCost8().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 54:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost9() ? info.getOtherCost9().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 55:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost10() ? info.getOtherCost10().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 56:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost11() ? info.getOtherCost11().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 57:
                        tempCell.setCellValue(!f ? 0 : null != info.getOtherCost12() ? info.getOtherCost12().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    case 58:
                        tempCell.setCellValue(!f ? 0 : null != info.getSumAmt() ? BigDecimal.valueOf(info.getSumAmt()).setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                if (feeColumns.contains(i)) {
                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }
            if (s <= excelDatas.size() - 1) {
                boolean tag = (s == excelDatas.size() - 1);
                if (tag || !excelDatas.get(s).getExpensesCode().equals(excelDatas.get(s + 1).getExpensesCode())) {
                    for (int j = 31; j < excelHeaderFields.length; j++) {
                        if (rowNum[0] - row > 1) {
                            sheet.addMergedRegion(new CellRangeAddress(row, rowNum[0] - 1, j, j));
                        }
                    }
                    row = rowNum[0];
                }
            } else {
                for (int j = 35; j < excelHeaderFields.length; j++) {
                    if (row > 0 && rowNum[0] - row > 1) {
                        sheet.addMergedRegion(new CellRangeAddress(row, rowNum[0] - 1, j, j));
                    }
                }
            }
            // 汇总单据数据
            sumAmt[0] = sumAmt[0].add(info.getSkuNumber() == null ? new BigDecimal("0") : info.getSkuNumber());
            sumAmt[1] = sumAmt[1].add(info.getWeight() == null ? new BigDecimal("0") : info.getWeight());
            sumAmt[2] = sumAmt[2].add(info.getTotalWeight() == null ? new BigDecimal("0") : info.getTotalWeight());
            sumAmt[3] = sumAmt[3].add(info.getTotalVolume() == null ? new BigDecimal("0") : info.getTotalVolume());
            sumAmt[4] = sumAmt[4].add(info.getCwFullCases() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getCwFullCases())); //常温整箱数
            sumAmt[5] = sumAmt[5].add(info.getCwSplitCases() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getCwSplitCases())); //常温散箱数
            sumAmt[6] = sumAmt[6].add(info.getLdFullCases() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getLdFullCases())); //非常温整箱数
            sumAmt[7] = sumAmt[7].add(info.getLdSplitCases() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getLdSplitCases())); //非常温散箱数
            sumAmt[8] = sumAmt[8].add(info.getTotalBoxes() == null ? new BigDecimal("0") : info.getTotalBoxes()); //总箱数
            sumAmt[9] = sumAmt[9].add(info.getCwPalletNumber() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getCwPalletNumber())); //常温托数
            sumAmt[10] = sumAmt[10].add(info.getLcPalletNumber() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getLcPalletNumber())); //冷藏托数
            sumAmt[11] = sumAmt[11].add(info.getLdPalletNumber() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getLdPalletNumber())); //冷冻托数
            sumAmt[12] = sumAmt[12].add(info.getPalletNumber() == null ? new BigDecimal("0") : info.getPalletNumber()); //总托数
            sumAmt[13] = sumAmt[13].add(info.getCargoValue() == null ? new BigDecimal("0") : info.getCargoValue()); //总货值
            // 汇总费用数据，因为存在合并列的情况，所以过滤重复数据
            if (f) {
                sumAmt[14] = sumAmt[14].add(info.getFreight() == null ? new BigDecimal("0") : info.getFreight()); //存储费
                sumAmt[15] = sumAmt[15].add(info.getUltrafarFee() == null ? new BigDecimal("0") : info.getUltrafarFee()); //管理处置费
                sumAmt[16] = sumAmt[16].add(info.getSuperframesFee() == null ? new BigDecimal("0") : info.getSuperframesFee()); //整箱分拣费
                sumAmt[17] = sumAmt[17].add(info.getExcessFee() == null ? new BigDecimal("0") : info.getExcessFee()); //拆零分拣费
                sumAmt[18] = sumAmt[18].add(info.getDeliveryFee() == null ? new BigDecimal("0") : info.getDeliveryFee()); //装卸费
                sumAmt[19] = sumAmt[19].add(info.getReturnFee() == null ? new BigDecimal("0") : info.getReturnFee()); //制单费
                sumAmt[20] = sumAmt[20].add(info.getShortbargeFee() == null ? new BigDecimal("0") : info.getShortbargeFee()); //操作费
                sumAmt[21] = sumAmt[21].add(info.getExceptionFee() == null ? new BigDecimal("0") : info.getExceptionFee()); //异常赔付费
                sumAmt[22] = sumAmt[22].add(info.getAdjustFee() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getAdjustFee())); //调账费
                sumAmt[23] = sumAmt[23].add(info.getOtherCost1() == null ? new BigDecimal("0") : info.getOtherCost1()); //其他费1
                sumAmt[24] = sumAmt[24].add(info.getOtherCost2() == null ? new BigDecimal("0") : info.getOtherCost2()); //其他费2
                sumAmt[25] = sumAmt[25].add(info.getOtherCost3() == null ? new BigDecimal("0") : info.getOtherCost3()); //其他费3
                sumAmt[26] = sumAmt[26].add(info.getOtherCost4() == null ? new BigDecimal("0") : info.getOtherCost4()); //其他费4
                sumAmt[27] = sumAmt[27].add(info.getOtherCost5() == null ? new BigDecimal("0") : info.getOtherCost5()); //其他费5
                sumAmt[28] = sumAmt[28].add(info.getOtherCost6() == null ? new BigDecimal("0") : info.getOtherCost6()); //其他费6
                sumAmt[29] = sumAmt[29].add(info.getOtherCost7() == null ? new BigDecimal("0") : info.getOtherCost7()); //其他费7
                sumAmt[30] = sumAmt[30].add(info.getOtherCost8() == null ? new BigDecimal("0") : info.getOtherCost8()); //其他费8
                sumAmt[31] = sumAmt[31].add(info.getOtherCost9() == null ? new BigDecimal("0") : info.getOtherCost9()); //其他费9
                sumAmt[32] = sumAmt[32].add(info.getOtherCost10() == null ? new BigDecimal("0") : info.getOtherCost10()); //其他费10
                sumAmt[33] = sumAmt[33].add(info.getOtherCost11() == null ? new BigDecimal("0") : info.getOtherCost11()); //其他费11
                sumAmt[34] = sumAmt[34].add(info.getOtherCost12() == null ? new BigDecimal("0") : info.getOtherCost12()); //其他费12
                sumAmt[35] = sumAmt[35].add(info.getSumAmt() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getSumAmt())); //总费用
                sumAmt[36] = sumAmt[36].add(info.getSplitTotalNumber() == null ? new BigDecimal("0") : info.getSplitTotalNumber()); //拆零总件数
            }
        }

        //最后一行 合计金额
        Row lastRow = sheet.createRow(rowNum[0]++);
        String[] lastRowFields = {"合计", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", sumAmt[36].toString(), sumAmt[0].toString()
                , sumAmt[1].toString(), sumAmt[2].toString(), sumAmt[3].toString(), sumAmt[4].toString(), sumAmt[5].toString()
                , sumAmt[6].toString(), sumAmt[7].toString(), sumAmt[8].toString(), sumAmt[9].toString(), sumAmt[10].toString(), sumAmt[11].toString()
                , sumAmt[12].toString(), sumAmt[13].toString(), "", "", "", ""
                , sumAmt[14].toString(), sumAmt[15].toString(), sumAmt[16].toString(), sumAmt[17].toString(), sumAmt[18].toString(), sumAmt[19].toString()
                , sumAmt[20].toString(), sumAmt[21].toString(), sumAmt[22].toString(), ""
                , sumAmt[23].toString(), sumAmt[24].toString(), sumAmt[25].toString(), sumAmt[26].toString(), sumAmt[27].toString()
                , sumAmt[28].toString(), sumAmt[29].toString(), sumAmt[30].toString(), sumAmt[31].toString(), sumAmt[32].toString(), sumAmt[33].toString()
                , sumAmt[34].toString(), sumAmt[35].toString()
        };

        //所有费用列的下标(处理保留2位小数的问题)
        Set<Integer> feeColumnsLastRow = new HashSet<>(Arrays.asList(36, 37, 38, 39, 40, 41, 42, 43, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58));

        for (int i = 0; i < lastRowFields.length; i++) {
            Cell tempCell = lastRow.createCell(i);
            if (i >= 17 && i <= 31 || i >= 36 && i <= 44 || i >= 46) {
                tempCell.setCellValue(StrUtil.isNotEmpty(lastRowFields[i]) ? new BigDecimal(lastRowFields[i]).setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
            } else {
                tempCell.setCellValue(lastRowFields[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else if (feeColumnsLastRow.contains(i)) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 0, 16));
        return hssfWorkbook;
    }


    /**
     * 出库订单明细信息 sheet
     */
    public SXSSFWorkbook outWarehousDetailsSummary(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid = RequestContext.getTenantId();
        Sheet sheet = hssfWorkbook.createSheet("出库费明细信息");

        Map<String, String> dicMaps = textConversionUtil.getDictByType("warm_zone_type", "1");
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 1、数据准备
        List<BmsYsBillExportInfo> excelDatas = new ArrayList<>();
        List<BmsYsBillExportInfo> bmsYsBillExportInfos = ysState.outWarehouseSummaryMain(tenantid, ids);
        //取业务单号
        if (CollUtil.isNotEmpty(bmsYsBillExportInfos)) {
            List<String> relateCodes = bmsYsBillExportInfos.stream().map(BmsYsBillExportInfo::getRelateCode).distinct().collect(Collectors.toList());
            excelDatas = ysState.outWarehousDetailSummary(tenantid, relateCodes);
        }
        if (CollUtil.isNotEmpty(excelDatas)) {
            excelDatas.forEach(e -> {
                // 温区名称
                if (e.getTemperatureType() != null && dicMaps.containsKey("warm_zone_type" + e.getTemperatureType())) {
                    e.setTemperatureName(dicMaps.get("warm_zone_type" + e.getTemperatureType()));
                }
            });
        }
        // 2、组装数值数据
        //所有行的要汇总的数值
        final int[] rowNum = {0};
        //创建首行
        String[] excelHeaderFields = {
                "出库单号", "订单时间", "门店名称", "物料编码",
                "商品名称", "品类", "温层", "商品规格", "单位", "箱规", "整箱数", "拆零件数", "件数",
                "单箱重量(kg)", "单箱体积(cm³)", "总重量(kg)", "总体积(m³)", "总金额"
        };
        Row headerRow = sheet.createRow(rowNum[0]++);
        headerRow.setHeight((short) 500);

        for (int i = 0; i < excelHeaderFields.length; i++) {
            Cell tempCell = headerRow.createCell(i);
            tempCell.setCellValue(excelHeaderFields[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        //合计费用列的合计金额
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };

        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd");

        for (BmsYsBillExportInfo info : excelDatas) {
            Row dataRow = sheet.createRow(rowNum[0]++);
            dataRow.setHeight((short) 500);
            for (int i = 0; i < excelHeaderFields.length; i++) {
                Cell tempCell = dataRow.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getRelateCode()) ? info.getRelateCode() : "");
                        break;
                    case 1:
                        tempCell.setCellValue(info.getOrderDate() != null ? sml.format(info.getOrderDate()) : "");
                        break;
                    case 2:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getReceivingStore()) ? info.getReceivingStore() : "");
                        break;
                    case 3:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getSkuCode()) ? info.getSkuCode() : "");
                        break;
                    case 4:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getSkuName()) ? info.getSkuName() : "");
                        break;
                    case 5:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getSkuClass()) ? info.getSkuClass() : "");
                        break;
                    case 6:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getTemperatureName()) ? info.getTemperatureName() : "");
                        break;
                    case 7:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getSpecification()) ? info.getSpecification() : "");
                        break;
                    case 8:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getUnit()) ? info.getUnit() : "");
                        break;
                    case 9:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getBoxType()) ? info.getBoxType() : "");
                        break;
                    case 10:
                        tempCell.setCellValue(null != info.getTotalBoxes() ? Double.parseDouble(info.getTotalBoxes().toString()) : 0);
                        break;
                    case 11:
                        tempCell.setCellValue(null != info.getOddBoxes() ? Double.parseDouble(info.getOddBoxes().toString()) : 0);
                        break;
                    case 12:
                        tempCell.setCellValue(null != info.getContentsNumber() ? Double.parseDouble(info.getContentsNumber().toString()) : 0);
                        break;
                    case 13:
                        tempCell.setCellValue(null != info.getWeight() ? Double.parseDouble(info.getWeight().toString()) : 0);
                        break;
                    case 14:
                        tempCell.setCellValue(null != info.getVolume() ? Double.parseDouble(info.getVolume().toString()) : 0);
                        break;
                    case 15:
                        tempCell.setCellValue(null != info.getTotalWeight() ? Double.parseDouble(info.getTotalWeight().toString()) : 0);
                        break;
                    case 16:
                        tempCell.setCellValue(null != info.getTotalVolume() ? Double.parseDouble(info.getTotalVolume().toString()) : 0);
                        break;
                    case 17:
                        tempCell.setCellValue(null != info.getTotalAmount() ? info.getTotalAmount().setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                if (i == 17) {
                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }

            sumAmt[0] = sumAmt[0].add(info.getTotalBoxes() == null ? new BigDecimal("0") : info.getTotalBoxes());
            sumAmt[1] = sumAmt[1].add(info.getOddBoxes() == null ? new BigDecimal("0") : info.getOddBoxes());
            sumAmt[2] = sumAmt[2].add(info.getContentsNumber() == null ? new BigDecimal("0") : info.getContentsNumber());
            sumAmt[3] = sumAmt[3].add(info.getWeight() == null ? new BigDecimal("0") : info.getWeight());
            sumAmt[4] = sumAmt[4].add(info.getVolume() == null ? new BigDecimal("0") : info.getVolume());
            sumAmt[5] = sumAmt[5].add(info.getTotalWeight() == null ? new BigDecimal("0") : info.getTotalWeight());
            sumAmt[6] = sumAmt[6].add(info.getTotalVolume() == null ? new BigDecimal("0") : info.getTotalVolume());
            sumAmt[7] = sumAmt[7].add(info.getTotalAmount() == null ? new BigDecimal("0") : info.getTotalAmount());
        }

        //最后一行 合计金额
        Row lastRow = sheet.createRow(rowNum[0]++);
        String[] lastRowFields = {"合计", "", "", "", "", "", "", "", "", ""
                , sumAmt[0].toString(), sumAmt[1].toString(), sumAmt[2].toString(), sumAmt[3].toString(), sumAmt[4].toString(), sumAmt[5].toString()
                , sumAmt[6].toString(), sumAmt[7].toString()
        };
        for (int i = 0; i < lastRowFields.length; i++) {
            Cell tempCell = lastRow.createCell(i);
            if (i >= 10) {
                tempCell.setCellValue(StrUtil.isNotEmpty(lastRowFields[i]) ? new BigDecimal(lastRowFields[i]).setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
            } else {
                tempCell.setCellValue(lastRowFields[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else if (i == 17) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 0, 9));
        return hssfWorkbook;
    }


    /**
     * 第六个sheet 配送明细1
     */
    public SXSSFWorkbook distributionDetailsSummary1(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid = RequestContext.getTenantId();
        // 第6个sheet
        Sheet sheet5 = hssfWorkbook.createSheet("月配送量");
        printA4(sheet5, true);
        // 背景色的设定
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum5 = {0};

        //设置列宽
        for (int i = 0; i < 36; i++) {
            sheet5.setColumnWidth(i, 2700);
        }

        //第1行
        Row s5r2 = sheet5.createRow(rowNum5[0]++);
        s5r2.setHeight((short) 500);
        String[] s5row2 = {"城市",/*"线路",*/ "店名", "公里数", "\\", "月份", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"
                , "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "配送次数", "超件数"};
        for (int i = 0; i < s5row2.length; i++) {
            Cell tempCell = s5r2.createCell(i);
            tempCell.setCellValue(s5row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        // 数据行 ，获取配送单据的信息
        // 根据账单查询所有的费用的账期分组（可能存在调整单）
        List<BmsYsBillExportInfo> stkList = ysState.getDistribution1GoodsDetailByBillId(tenantid, ids);
        List<BmsYsBillExportInfo> detailListFirt = ysState.getDetailDaysTwo(tenantid, ids);
        Map<String, List<BmsYsBillExportInfo>> detailMap = new HashMap<>();
        for (BmsYsBillExportInfo bmsYsBillExportInfo : detailListFirt) {
            String groupStr = bmsYsBillExportInfo.getDestinationCity() + bmsYsBillExportInfo.getLineCode() + bmsYsBillExportInfo.getStoreCode();
            if (detailMap.containsKey(groupStr)) {
                detailMap.get(groupStr).add(bmsYsBillExportInfo);
            } else {
                List<BmsYsBillExportInfo> list = new ArrayList<>();
                list.add(bmsYsBillExportInfo);
                detailMap.put(groupStr, list);
            }
        }
        if (stkList.isEmpty()) {
            return hssfWorkbook;
        }
        String billDate = stkList.get(0).getBillDate();
        int row = 2;
        SimpleDateFormat sml = new SimpleDateFormat("dd");
        for (BmsYsBillExportInfo info : stkList) {
            Row s2r3 = sheet5.createRow(rowNum5[0]++);
            s2r3.setHeight((short) 500);
            List<BmsYsBillExportInfo> detailList = detailMap.get(info.getDestinationCity() + info.getLineCode() + info.getStoreCode());
            for (BmsYsBillExportInfo detail : detailList) {
                detail.setDay(Integer.valueOf(sml.format(detail.getSigningDate())));
                switch (detail.getDay()) {
                    case 1:
                        info.setNumber1(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 2:
                        info.setNumber2(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 3:
                        info.setNumber3(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 4:
                        info.setNumber4(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 5:
                        info.setNumber5(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 6:
                        info.setNumber6(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 7:
                        info.setNumber7(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 8:
                        info.setNumber8(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 9:
                        info.setNumber9(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 10:
                        info.setNumber10(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 11:
                        info.setNumber11(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 12:
                        info.setNumber12(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 13:
                        info.setNumber13(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 14:
                        info.setNumber14(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 15:
                        info.setNumber15(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 16:
                        info.setNumber16(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 17:
                        info.setNumber17(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 18:
                        info.setNumber18(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 19:
                        info.setNumber19(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 20:
                        info.setNumber20(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 21:
                        info.setNumber21(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 22:
                        info.setNumber22(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 23:
                        info.setNumber23(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 24:
                        info.setNumber24(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 25:
                        info.setNumber25(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 26:
                        info.setNumber26(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 27:
                        info.setNumber27(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 28:
                        info.setNumber28(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 29:
                        info.setNumber29(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 30:
                        info.setNumber30(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 31:
                        info.setNumber31(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    default:

                }
            }
            for (int i = 0; i < s5row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getDestinationCity());
                        break;
                    case 1:
                        tempCell.setCellValue(info.getReceivingStore());
                        break;
                    case 2:
                        tempCell.setCellValue(info.getNearStoreKm() == null ? 0 : Double.parseDouble(info.getNearStoreKm().toString()));
                        break;
                    case 3:
                        tempCell.setCellValue("件数");
                        break;
                    case 4:
                        tempCell.setCellValue(info.getBillDate());
                        break;
                    case 5:
                        if (StrUtil.isEmpty(info.getNumber1()) || "0".equals(info.getNumber1())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber1()));
                        }
                        break;
                    case 6:
                        if (StrUtil.isEmpty(info.getNumber2()) || "0".equals(info.getNumber2())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber2()));
                        }
                        break;
                    case 7:
                        if (StrUtil.isEmpty(info.getNumber3()) || "0".equals(info.getNumber3())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber3()));
                        }
                        break;
                    case 8:
                        if (StrUtil.isEmpty(info.getNumber4()) || "0".equals(info.getNumber4())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber4()));
                        }
                        break;
                    case 9:
                        if (StrUtil.isEmpty(info.getNumber5()) || "0".equals(info.getNumber5())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber5()));
                        }
                        break;
                    case 10:
                        if (StrUtil.isEmpty(info.getNumber6()) || "0".equals(info.getNumber6())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber6()));
                        }
                        break;
                    case 11:
                        if (StrUtil.isEmpty(info.getNumber7()) || "0".equals(info.getNumber7())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber7()));
                        }
                        break;
                    case 12:
                        if (StrUtil.isEmpty(info.getNumber8()) || "0".equals(info.getNumber8())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber8()));
                        }
                        break;
                    case 13:
                        if (StrUtil.isEmpty(info.getNumber9()) || "0".equals(info.getNumber9())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber9()));
                        }
                        break;
                    case 14:
                        if (StrUtil.isEmpty(info.getNumber10()) || "0".equals(info.getNumber10())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber10()));
                        }
                        break;
                    case 15:
                        if (StrUtil.isEmpty(info.getNumber11()) || "0".equals(info.getNumber11())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber11()));
                        }
                        break;
                    case 16:
                        if (StrUtil.isEmpty(info.getNumber12()) || "0".equals(info.getNumber12())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber12()));
                        }
                        break;
                    case 17:
                        if (StrUtil.isEmpty(info.getNumber13()) || "0".equals(info.getNumber13())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber13()));
                        }
                        break;
                    case 18:
                        if (StrUtil.isEmpty(info.getNumber14()) || "0".equals(info.getNumber14())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber14()));
                        }
                        break;
                    case 19:
                        if (StrUtil.isEmpty(info.getNumber15()) || "0".equals(info.getNumber15())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber15()));
                        }
                        break;
                    case 20:
                        if (StrUtil.isEmpty(info.getNumber16()) || "0".equals(info.getNumber16())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber16()));
                        }
                        break;
                    case 21:
                        if (StrUtil.isEmpty(info.getNumber17()) || "0".equals(info.getNumber17())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber17()));
                        }
                        break;
                    case 22:
                        if (StrUtil.isEmpty(info.getNumber18()) || "0".equals(info.getNumber18())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber18()));
                        }
                        break;
                    case 23:
                        if (StrUtil.isEmpty(info.getNumber19()) || "0".equals(info.getNumber19())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber19()));
                        }
                        break;
                    case 24:
                        if (StrUtil.isEmpty(info.getNumber20()) || "0".equals(info.getNumber20())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber20()));
                        }
                        break;
                    case 25:
                        if (StrUtil.isEmpty(info.getNumber21()) || "0".equals(info.getNumber21())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber21()));
                        }
                        break;
                    case 26:
                        if (StrUtil.isEmpty(info.getNumber22()) || "0".equals(info.getNumber22())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber22()));
                        }
                        break;
                    case 27:
                        if (StrUtil.isEmpty(info.getNumber23()) || "0".equals(info.getNumber23())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber23()));
                        }
                        break;
                    case 28:
                        if (StrUtil.isEmpty(info.getNumber24()) || "0".equals(info.getNumber24())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber24()));
                        }
                        break;
                    case 29:
                        if (StrUtil.isEmpty(info.getNumber25()) || "0".equals(info.getNumber25())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber25()));
                        }
                        break;
                    case 30:
                        if (StrUtil.isEmpty(info.getNumber26()) || "0".equals(info.getNumber26())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber26()));
                        }
                        break;
                    case 31:
                        if (StrUtil.isEmpty(info.getNumber27()) || "0".equals(info.getNumber27())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber27()));
                        }
                        break;
                    case 32:
                        if (StrUtil.isEmpty(info.getNumber28()) || "0".equals(info.getNumber28())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber28()));
                        }
                        break;
                    case 33:
                        if (StrUtil.isEmpty(info.getNumber29()) || "0".equals(info.getNumber29())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber29()));
                        }
                        break;
                    case 34:
                        if (StrUtil.isEmpty(info.getNumber30()) || "0".equals(info.getNumber30())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber30()));
                        }
                        break;
                    case 35:
                        if (StrUtil.isEmpty(info.getNumber31()) || "0".equals(info.getNumber31())) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getNumber31()));
                        }
                        break;
                    case 36:
                        if (info.getDcNum() == null || info.getDcNum().equals(0)) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getDcNum().toString()));
                        }
                        break;
                    case 37:
                        if (info.getOverNum() == null || info.getOverNum().equals(BigDecimal.ZERO)) {
                            tempCell.setCellValue("");
                        } else {
                            tempCell.setCellValue(Double.parseDouble(info.getOverNum().toString()));
                        }
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }

            if ((!billDate.equals(info.getBillDate())) && row - 1 != rowNum5[0] - 2) {
                sheet5.addMergedRegion(new CellRangeAddress(row - 1, rowNum5[0] - 2, 4, 4));
                billDate = info.getBillDate();
                row = rowNum5[0];
            }
            if ((!billDate.equals(info.getBillDate()))) {
                billDate = info.getBillDate();
                row = rowNum5[0];
            }
        }
        return hssfWorkbook;
    }

    /**
     * 打印a4(横向设置)
     *
     * @param sheet sheet
     * @param tag   标签
     */
    public void printA4(Sheet sheet, boolean tag) {
        // 设置打印
        PrintSetup printSetup = sheet.getPrintSetup();
        printSetup.setPaperSize(HSSFPrintSetup.A4_PAPERSIZE);
        // 打印方向，true：横向，false：纵向(默认)
        printSetup.setLandscape(tag);
        printSetup.setVResolution((short) 600);
        printSetup.setFitHeight((short) 0);
        //printSetup.setFitWidth((short)50);
        //自定义缩放，此处100为无缩放
        printSetup.setScale((short) 100);
        //printSetup.set
        sheet.setFitToPage(true);
        //设置打印页水平居中
        sheet.setHorizontallyCenter(true);
        //设置打印页面为垂直居中
        sheet.setVerticallyCenter(false);
        //true设置sheet适应于一页,false设置sheet不一定适应于一页
        sheet.setAutobreaks(false);
    }

    /**
     * 打印a4(纵向设置)
     *
     * @param sheet sheet
     * @param tag   标签
     */
    public void printA4Z(Sheet sheet, boolean tag) {
        // 设置打印
        PrintSetup printSetup = sheet.getPrintSetup();
        printSetup.setPaperSize(HSSFPrintSetup.A4_PAPERSIZE);
        //printSetup.setLandscape(tag); // 打印方向，true：横向，false：纵向(默认)
        printSetup.setFitHeight((short) 10);
        printSetup.setVResolution((short) 600);
        //自定义缩放，此处100为无缩放
        printSetup.setScale((short) 100);
        sheet.setFitToPage(true);
        //设置打印页水平居中
        sheet.setHorizontallyCenter(true);
        //设置打印页面为垂直居中
        sheet.setVerticallyCenter(false);
        //true设置 sheet适应于一页,false设置sheet不一定适应于一页
        sheet.setAutobreaks(false);
    }

    /**
     * 第七个sheet 配送明细2(配送费主信息)
     */
    public SXSSFWorkbook distributionDetailsSummary2(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid = RequestContext.getTenantId();
        // 第7个sheet
        Sheet sheet6 = hssfWorkbook.createSheet("配送费主信息");
        printA4(sheet6, true);
        // 背景色的设定
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum6 = {0};
        List<BmsYsBillExportInfo> stkList = ysState.getDistribution2GoodsDetailByBillId(tenantid, ids);
        BigDecimal[] amtf = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        Boolean[] fare = new Boolean[34];
        // 判断是否要隐藏列
        for (BmsYsBillExportInfo bmsYsBillExportInfo : stkList) {
            amtf[0] = amtf[0].add(bmsYsBillExportInfo.getFreight());
            amtf[1] = amtf[1].add(bmsYsBillExportInfo.getDeliveryFee());
            amtf[2] = amtf[2].add(bmsYsBillExportInfo.getOutboundsortingFee());
            amtf[3] = amtf[3].add(bmsYsBillExportInfo.getShortbargeFee());
            amtf[4] = amtf[4].add(bmsYsBillExportInfo.getUltrafarFee());
            amtf[5] = amtf[5].add(bmsYsBillExportInfo.getSuperframesFee());
            amtf[6] = amtf[6].add(bmsYsBillExportInfo.getExcessFee());
            amtf[7] = amtf[7].add(bmsYsBillExportInfo.getReduceFee());
            amtf[8] = amtf[8].add(bmsYsBillExportInfo.getReturnFee());
            amtf[9] = amtf[9].add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()));
            amtf[10] = amtf[10].add(new BigDecimal(StrUtil.isEmpty(bmsYsBillExportInfo.getAdjustRemark()) ? 0 : 1));
            amtf[11] = amtf[11].add(bmsYsBillExportInfo.getExceptionFee());
            amtf[12] = amtf[12].add(bmsYsBillExportInfo.getOtherCost1());
            amtf[13] = amtf[13].add(bmsYsBillExportInfo.getOtherCost2());
            amtf[14] = amtf[14].add(bmsYsBillExportInfo.getOtherCost3());
            amtf[15] = amtf[15].add(bmsYsBillExportInfo.getOtherCost4());
            amtf[16] = amtf[16].add(bmsYsBillExportInfo.getOtherCost5());
            amtf[17] = amtf[17].add(bmsYsBillExportInfo.getOtherCost6());
            amtf[18] = amtf[18].add(bmsYsBillExportInfo.getOtherCost7());
            amtf[19] = amtf[19].add(bmsYsBillExportInfo.getOtherCost8());
            amtf[20] = amtf[20].add(bmsYsBillExportInfo.getOtherCost9());
            amtf[21] = amtf[21].add(bmsYsBillExportInfo.getOtherCost10());
            amtf[22] = amtf[22].add(bmsYsBillExportInfo.getOtherCost11());
            amtf[23] = amtf[23].add(bmsYsBillExportInfo.getOtherCost12());
            amtf[24] = amtf[24].add(BigDecimal.valueOf(bmsYsBillExportInfo.getSumAmt()));
            amtf[25] = amtf[25].add(bmsYsBillExportInfo.getTotalNumber() == null ? BigDecimal.ZERO : bmsYsBillExportInfo.getTotalNumber());
            amtf[26] = amtf[26].add(bmsYsBillExportInfo.getTotalWeight() == null ? new BigDecimal(0) : bmsYsBillExportInfo.getTotalWeight());
            amtf[27] = amtf[27].add(bmsYsBillExportInfo.getTotalVolume() == null ? new BigDecimal(0) : bmsYsBillExportInfo.getTotalVolume());
            amtf[28] = amtf[28].add(bmsYsBillExportInfo.getCargoValue() == null ? new BigDecimal(0) : bmsYsBillExportInfo.getCargoValue());
            amtf[29] = amtf[29].add(bmsYsBillExportInfo.getTotalBoxes());
            amtf[30] = amtf[30].add(BigDecimal.valueOf(bmsYsBillExportInfo.getCwFullCases()));
            amtf[31] = amtf[31].add(BigDecimal.valueOf(bmsYsBillExportInfo.getCwSplitCases()));
            amtf[32] = amtf[32].add(BigDecimal.valueOf(bmsYsBillExportInfo.getLdFullCases()));
            amtf[33] = amtf[33].add(BigDecimal.valueOf(bmsYsBillExportInfo.getLdSplitCases()));
        }
        int fi = 0;
        for (BigDecimal bigDecimal : amtf) {
            fare[fi] = bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0;
            fi++;
        }
        //设置列宽
        for (int i = 0; i < fare.length; i++) {
            if (fare[i] != null && fare[i]) {
                sheet6.setColumnWidth(i + 10, 0);
            }
        }
        //第1行
        Row s2r2 = sheet6.createRow(rowNum6[0]++);
        s2r2.setHeight((short) 500);
        String[] s6row2 = {"订单号"
                , "外部订单号", "交易单号"
                , "计费类型", "计费单号",
                "费用维度", "单据所属",
                "新增时间", "订单时间", "签收时间",
                "运费", "提货费", "送货费", "短驳费", "超远费",
                "超筐费", "加点费", "减点费", "装卸费", "调账费", "调账备注", "客诉理赔费",
                "otherCost1", "otherCost2", "otherCost3", "otherCost4", "otherCost5",
                "otherCost6", "otherCost7", "otherCost8", "otherCost9", "otherCost10", "otherCost11", "otherCost12",
                "总费用", "总件数", "总重量(kg)", "总体积(cm³)", "总货值", "总箱数", "常温整箱数", "常温散箱数" , "非常温整箱数", "非常温散箱"
                , "计费人", "计费备注", "备注",
                "线路编码", "线路名称",
                "门店编码", "门店名称", "配送类型", "车长", "运输方式", "发货仓库", "始发省", "始发市", "始发区", "始发详细地址", "目的省", "目的市"
                , "目的区", "目的详细地址"
        };

        //所有费用列的下标(处理保留2位小数的问题)
        Set<Integer> feeColumns = new HashSet<>(Arrays.asList(10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 38));


        for (int i = 0; i < s6row2.length; i++) {
            String name = s6row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            if (StrUtil.isNotEmpty(dicMap.get("ys_transport_othercost" + name))) {
                otherFeeInfo.setOtherName(dicMap.get("ys_transport_othercost" + name));
            }
            switch (name) {
                case "otherCost1":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost1());
                    break;
                case "otherCost2":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost2());
                    break;
                case "otherCost3":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost3());
                    break;
                case "otherCost4":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost4());
                    break;
                case "otherCost5":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost5());
                    break;
                case "otherCost6":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost6());
                    break;
                case "otherCost7":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost7());
                    break;
                case "otherCost8":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost8());
                    break;
                case "otherCost9":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost9());
                    break;
                case "otherCost10":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost10());
                    break;
                case "otherCost11":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost11());
                    break;
                case "otherCost12":
                    otherFeeInfo.setOtherFee(bmsYsbillmainExportAll.getCcOtherCost12());
                    break;
                default:
                    otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
            }
            s6row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s6row2.length; i++) {
            Cell tempCell = s2r2.createCell(i);
            tempCell.setCellValue(s6row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        // 创建数据行，获取入库明细
        String lastId = "";
        int row = 1;

        Map<String, SysDept> companyMap = textConversionUtil.getCompanyMap("1", stkList.stream().filter(e -> e.getCompanyId() != null).map(e -> e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        stkList.forEach(e -> {
            if (e.getCompanyId() != null && companyMap.containsKey(e.getCompanyId().toString())) {
                e.setCompanyName(companyMap.get(e.getCompanyId().toString()).getDeptName());
            }
        });
        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (int s = 0; s < stkList.size(); s++) {
            BmsYsBillExportInfo info = stkList.get(s);
            Row s2r3 = sheet6.createRow(rowNum6[0]++);
            s2r3.setHeight((short) 500);
            boolean f = true;
            // 赋值
            f = s <= 0 || s >= stkList.size() || !stkList.get(s).getId().equals(stkList.get(s - 1).getId()); // 赋0

            if (StrUtil.isNotEmpty(info.getOrderType()) && dicMap.containsKey("order_type" + info.getOrderType())) {
                info.setOrderTypeName(dicMap.get("order_type" + info.getOrderType()));
            }
            for (int i = 0; i < s6row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getRelateCode()); // 订单号
                        break;
                    case 1:
                        tempCell.setCellValue(info.getOrderNo()); // 订货单号
                        break;
                    case 2:
                        tempCell.setCellValue(info.getPlatformCode()); // 交易单号
                        break;
                    case 3:
                        tempCell.setCellValue(info.getChargeTypeName());// 计费类型
                        break;
                    case 4:
                        tempCell.setCellValue(info.getExpensesCode());// 计费单号
                        break;
                    case 5:
                        tempCell.setCellValue(info.getCostDimensionName()); // 费用维度
                        break;
                    case 6:
                        tempCell.setCellValue(info.getCompanyName()); // 单据所属
                        break;
                    case 7:
                        tempCell.setCellValue(info.getCreateTime()); // 新增时间
                        break;
                    case 8:
                        tempCell.setCellValue(info.getOrderDate() == null ? "" : sbf.format(info.getOrderDate())); // 订单时间
                        break;
                    case 9:
                        tempCell.setCellValue(info.getSigningDate() == null ? "" : sbf.format(info.getSigningDate())); // 签收时间
                        break;
                    case 10:
                        tempCell.setCellValue(!f ? 0 : info.getFreight() == null ? 0 : info.getFreight().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 11:
                        tempCell.setCellValue(!f ? 0 : info.getDeliveryFee() == null ? 0 : info.getDeliveryFee().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 12:
                        tempCell.setCellValue(!f ? 0 : info.getOutboundsortingFee() == null ? 0 : info.getOutboundsortingFee().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 13:
                        tempCell.setCellValue(!f ? 0 : info.getShortbargeFee() == null ? 0 : info.getShortbargeFee().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 14:
                        tempCell.setCellValue(!f ? 0 : info.getUltrafarFee() == null ? 0 : info.getUltrafarFee().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 15:
                        tempCell.setCellValue(!f ? 0 : info.getSuperframesFee() == null ? 0 : info.getSuperframesFee().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 16:
                        tempCell.setCellValue(!f ? 0 : info.getExcessFee() == null ? 0 : info.getExcessFee().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 17:
                        tempCell.setCellValue(!f ? 0 : info.getReduceFee() == null ? 0 : info.getReduceFee().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 18:
                        tempCell.setCellValue(!f ? 0 : info.getReturnFee() == null ? 0 : info.getReturnFee().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 19:
                        tempCell.setCellValue(!f ? 0 : info.getAdjustFee() == null ? 0 : BigDecimal.valueOf(info.getAdjustFee()).setScale(2, RoundingMode.HALF_UP).doubleValue()); // 调账费
                        break;
                    case 20:
                        tempCell.setCellValue(info.getAdjustRemark()); // 调账备注
                        break;
                    case 21:
                        tempCell.setCellValue(!f ? 0 : info.getExceptionFee() == null ? 0 : info.getExceptionFee().setScale(2, RoundingMode.HALF_UP).doubleValue()); //异常赔付费
                        break;
                    case 22:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost1() == null ? 0 : info.getOtherCost1().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 23:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost2() == null ? 0 : info.getOtherCost2().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 24:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost3() == null ? 0 : info.getOtherCost3().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 25:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost4() == null ? 0 : info.getOtherCost4().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 26:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost5() == null ? 0 : info.getOtherCost5().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 27:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost6() == null ? 0 : info.getOtherCost6().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 28:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost7() == null ? 0 : info.getOtherCost7().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 29:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost8() == null ? 0 : info.getOtherCost8().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 30:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost9() == null ? 0 : info.getOtherCost9().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 31:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost10() == null ? 0 : info.getOtherCost10().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 32:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost11() == null ? 0 : info.getOtherCost11().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 33:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost12() == null ? 0 : info.getOtherCost12().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 34:
                        tempCell.setCellValue(!f ? 0 : info.getSumAmt() == null ? 0 : BigDecimal.valueOf(info.getSumAmt()).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 35:
                        tempCell.setCellValue(!f ? 0 : info.getExtraField1() == null ? 0 : info.getExtraField1().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 36:
                        tempCell.setCellValue(info.getTotalNumber() == null ? 0 : Double.parseDouble(info.getTotalNumber().toString())); // 总件数
                        break;
                    case 37:
                        tempCell.setCellValue(info.getTotalWeight() == null ? 0 : Double.parseDouble(info.getTotalWeight().toString())); // 总重量
                        break;
                    case 38:
                        tempCell.setCellValue(info.getTotalVolume() == null ? 0 : Double.parseDouble(info.getTotalVolume().toString()));
                        break;
                    case 39:
                        tempCell.setCellValue(info.getCargoValue() == null ? 0 : info.getCargoValue().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 40:
                        tempCell.setCellValue(info.getTotalBoxes() == null ? 0 : Double.parseDouble(info.getTotalBoxes().toString()));
                        break;
                    case 41:
                        tempCell.setCellValue(info.getCwFullCases() == null ? 0 : Double.parseDouble(info.getCwFullCases().toString()));
                        break;
                    case 42:
                        tempCell.setCellValue(info.getCwSplitCases() == null ? 0 : Double.parseDouble(info.getCwSplitCases().toString()));
                        break;
                    case 43:
                        tempCell.setCellValue(info.getLdFullCases() == null ? 0 : Double.parseDouble(info.getLdFullCases().toString()));
                        break;
                    case 44:
                        tempCell.setCellValue(info.getLdSplitCases() == null ? 0 : Double.parseDouble(info.getLdSplitCases().toString()));
                        break;
                    case 45:
                        tempCell.setCellValue(info.getOperBy()); // 计费人
                        break;
                    case 46:
                        tempCell.setCellValue(info.getAutomaticBillingRemark()); // 计费备注
                        break;
                    case 47:
                        tempCell.setCellValue(info.getRemarks()); // 备注
                        break;
                    case 48:
                        tempCell.setCellValue(info.getLineCode()); // 线路编码
                        break;
                    case 49:
                        tempCell.setCellValue(info.getLineName()); // 线路名称
                        break;
                    case 50:
                        tempCell.setCellValue(info.getStoreCode()); // 收货门店编码
                        break;
                    case 51:
                        tempCell.setCellValue(info.getReceivingStore()); // 收货门店
                        break;
                    case 52:
                        tempCell.setCellValue(info.getDeliveryModeName()); // 配送类型
                        break;
                    case 53:
                        tempCell.setCellValue(info.getCarModel()); // 车长
                        break;
                    case 54:
                        tempCell.setCellValue(info.getTransportTypeName()); //运输方式
                        break;
                    case 55:
                        tempCell.setCellValue(info.getWarehouseName()); // 发货仓库
                        break;
                    case 56:
                        tempCell.setCellValue(info.getProvinceOrigin()); // 始发省
                        break;
                    case 57:
                        tempCell.setCellValue(info.getOriginatingCity()); // 始发市
                        break;
                    case 58:
                        tempCell.setCellValue(info.getOriginatingArea()); // 始发区
                        break;
                    case 59:
                        tempCell.setCellValue(info.getOriginatingAddress()); // 始发详细地址
                        break;
                    case 60:
                        tempCell.setCellValue(info.getDestinationProvince()); // 目的省
                        break;
                    case 61:
                        tempCell.setCellValue(info.getDestinationCity()); // 目的市
                        break;
                    case 62:
                        tempCell.setCellValue(info.getDestinationArea()); // 目的区
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                if (feeColumns.contains(i)) {
                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }
            if (s < stkList.size() - 1) {
                if (stkList.get(s).getId().equals(stkList.get(s + 1).getId())) {
                    lastId = info.getId();
                } else {
                    for (int j = 10; j < s6row2.length; j++) {
                        if (j <= 35 && rowNum6[0] - row > 1) {
                            sheet6.addMergedRegion(new CellRangeAddress(row, rowNum6[0] - 1, j, j));
                        }
                    }
                    row = rowNum6[0];
                }
            } else {
                for (int j = 6; j < s6row2.length; j++) {
                    if (j <= 35 && row > 0 && rowNum6[0] - row > 1) {
                        sheet6.addMergedRegion(new CellRangeAddress(row, rowNum6[0] - 1, j, j));
                    }
                }
            }

            sumAmt[26] = sumAmt[26].add(info.getTotalNumber() == null ? new BigDecimal("0") : info.getTotalNumber());
            sumAmt[27] = sumAmt[27].add(info.getTotalWeight() == null ? new BigDecimal("0") : info.getTotalWeight());
            sumAmt[28] = sumAmt[28].add(info.getTotalVolume() == null ? new BigDecimal("0") : info.getTotalVolume());
            sumAmt[29] = sumAmt[29].add(info.getCargoValue() == null ? new BigDecimal("0") : info.getCargoValue());
            sumAmt[30] = sumAmt[30].add(info.getTotalBoxes() == null ? BigDecimal.ZERO : info.getTotalBoxes());
            sumAmt[31] = sumAmt[31].add(info.getCwFullCases() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getCwFullCases()));
            sumAmt[32] = sumAmt[32].add(info.getCwSplitCases() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getCwSplitCases()));
            sumAmt[33] = sumAmt[33].add(info.getLdFullCases() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getLdFullCases()));
            sumAmt[34] = sumAmt[34].add(info.getLdSplitCases() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getLdSplitCases()));
            if (f) {
                sumAmt[0] = sumAmt[0].add(info.getAdjustFee() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getAdjustFee()));
                sumAmt[1] = sumAmt[1].add(info.getFreight() == null ? new BigDecimal("0") : info.getFreight());
                sumAmt[2] = sumAmt[2].add(info.getDeliveryFee() == null ? new BigDecimal("0") : info.getDeliveryFee());
                sumAmt[3] = sumAmt[3].add(info.getOutboundsortingFee() == null ? new BigDecimal("0") : info.getOutboundsortingFee());
                sumAmt[4] = sumAmt[4].add(info.getShortbargeFee() == null ? new BigDecimal("0") : info.getShortbargeFee());
                sumAmt[5] = sumAmt[5].add(info.getUltrafarFee() == null ? new BigDecimal("0") : info.getUltrafarFee());
                sumAmt[6] = sumAmt[6].add(info.getSuperframesFee() == null ? new BigDecimal("0") : info.getSuperframesFee());
                sumAmt[7] = sumAmt[7].add(info.getExcessFee() == null ? new BigDecimal("0") : info.getExcessFee());
                sumAmt[8] = sumAmt[8].add(info.getReduceFee() == null ? new BigDecimal("0") : info.getReduceFee());
                sumAmt[9] = sumAmt[9].add(info.getReturnFee() == null ? new BigDecimal("0") : info.getReturnFee());
                sumAmt[10] = sumAmt[10].add(info.getExceptionFee() == null ? new BigDecimal("0") : info.getExceptionFee());
                sumAmt[11] = sumAmt[11].add(info.getOtherCost1() == null ? new BigDecimal("0") : info.getOtherCost1());
                sumAmt[12] = sumAmt[12].add(info.getOtherCost2() == null ? new BigDecimal("0") : info.getOtherCost2());
                sumAmt[13] = sumAmt[13].add(info.getOtherCost3() == null ? new BigDecimal("0") : info.getOtherCost3());
                sumAmt[14] = sumAmt[14].add(info.getOtherCost4() == null ? new BigDecimal("0") : info.getOtherCost4());
                sumAmt[15] = sumAmt[15].add(info.getOtherCost5() == null ? new BigDecimal("0") : info.getOtherCost5());
                sumAmt[16] = sumAmt[16].add(info.getOtherCost6() == null ? new BigDecimal("0") : info.getOtherCost6());
                sumAmt[17] = sumAmt[17].add(info.getOtherCost7() == null ? new BigDecimal("0") : info.getOtherCost7());
                sumAmt[18] = sumAmt[18].add(info.getOtherCost8() == null ? new BigDecimal("0") : info.getOtherCost8());
                sumAmt[19] = sumAmt[19].add(info.getOtherCost9() == null ? new BigDecimal("0") : info.getOtherCost9());
                sumAmt[20] = sumAmt[20].add(info.getOtherCost10() == null ? new BigDecimal("0") : info.getOtherCost10());
                sumAmt[21] = sumAmt[21].add(info.getOtherCost11() == null ? new BigDecimal("0") : info.getOtherCost11());
                sumAmt[22] = sumAmt[22].add(info.getOtherCost12() == null ? new BigDecimal("0") : info.getOtherCost12());
                sumAmt[23] = sumAmt[23].add(info.getBasicsSum() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getBasicsSum()));
                sumAmt[24] = sumAmt[24].add(info.getOtherSum() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getOtherSum()));
                sumAmt[25] = sumAmt[25].add(info.getSumAmt() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getSumAmt()));
                sumAmt[35] = sumAmt[35].add(info.getExtraField1() == null ? new BigDecimal("0") : info.getExtraField1());
            }

        }
        //最后一行 合计金额
        Row s2r4 = sheet6.createRow(rowNum6[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"合计：", "", "", "", "", "", "", "", ""
                , "", sumAmt[1].toString(), sumAmt[2].toString(), sumAmt[3].toString(), sumAmt[4].toString(), sumAmt[5].toString(),
                sumAmt[6].toString(), sumAmt[7].toString(), sumAmt[8].toString(), sumAmt[9].toString(), sumAmt[0].toString(), "", sumAmt[10].toString(),
                sumAmt[11].toString(), sumAmt[12].toString(), sumAmt[13].toString(), sumAmt[14].toString(), sumAmt[15].toString(), sumAmt[16].toString(),
                sumAmt[17].toString(), sumAmt[18].toString(), sumAmt[19].toString(), sumAmt[20].toString(), sumAmt[21].toString(), sumAmt[22].toString(),
                sumAmt[25].toString(), sumAmt[35].toString(), sumAmt[26].toString(),
                sumAmt[27].toString(), sumAmt[28].toString(),
                sumAmt[29].toString(), sumAmt[30].toString(), sumAmt[31].toString(), sumAmt[32].toString()
                , sumAmt[33].toString(), sumAmt[34].toString(), "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""};

        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if (i > 9 && i != 20 && i < 44) {
                tempCell.setCellValue(StrUtil.isNotEmpty(s2row4[i]) ? new BigDecimal(s2row4[i]).setScale(2, RoundingMode.HALF_UP).doubleValue() : 0);
            } else {
                tempCell.setCellValue(s2row4[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else if (feeColumns.contains(i)) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet6.addMergedRegion(new CellRangeAddress(rowNum6[0] - 1, rowNum6[0] - 1, 0, 9));
        return hssfWorkbook;
    }

    /**
     * 第八个sheet 商品明细
     */
    public SXSSFWorkbook orderGoodsDetailSummary(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid = RequestContext.getTenantId();
        // 第4个sheet
        Sheet sheet3 = hssfWorkbook.createSheet("配送商品明细");
        printA4(sheet3, true);
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum3 = {0};
        List<BmsYsBillExportInfo> inWarList = ysState.getTransportGoodsDetailByBillId(tenantid, ids);
        //第1行
        Row s2r2 = sheet3.createRow(rowNum3[0]++);
        s2r2.setHeight((short) 500);
        String[] s3row2 = {"订单号", "订单日期", "门店名称", "品类", "SKU编码", "商品名称",
                "温区", "商品规格", "单位", "箱规", "总件数" , "总箱数", "整箱数", "拆零件数",
                "单箱重量(kg)", "单箱体积(m³)", "总重量（kg）", "总体积(m³)", "总金额"};

        for (int i = 0; i < s3row2.length; i++) {
            String name = s3row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            s3row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s3row2.length; i++) {
            Cell tempCell = s2r2.createCell(i);
            tempCell.setCellValue(s3row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        // 创建数据行 入库明细
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (BmsYsBillExportInfo info : inWarList) {
            Row s2r3 = sheet3.createRow(rowNum3[0]++);
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s3row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getRelateCode()) ? info.getRelateCode() : "");
                        break;
                    case 1:
                        tempCell.setCellValue(info.getOrderDate() != null ? sdf.format(info.getOrderDate()) : "");
                        break;
                    case 2:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getStoreName()) ? info.getStoreName() : "");
                        break;
                    case 3:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getSkuClass()) ? info.getSkuClass() : "");
                        break;
                    case 4:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getSkuCode()) ? info.getSkuCode() : "");
                        break;
                    case 5:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getSkuName()) ? info.getSkuName() : "");
                        break;
                    case 6:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getTemperatureName()) ? info.getTemperatureName() : "");
                        break;
                    case 7:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getSpecification()) ? info.getSpecification() : ""); //规格
                        break;
                    case 8:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getUnit()) ? info.getUnit() : ""); //单位
                        break;
                    case 9:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getBoxType()) ? info.getBoxType() : ""); //箱规
                        break;
                    case 10:
                        if (info.getContentsNumber() != null && !info.getContentsNumber().equals(BigDecimal.ZERO)) {
                            tempCell.setCellValue(Double.parseDouble(info.getContentsNumber().toString()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 11:
                        if (info.getSumBoxes() != null && !info.getSumBoxes().equals(BigDecimal.ZERO)) {
                            tempCell.setCellValue(Double.parseDouble(info.getSumBoxes().toString()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 12:
                        if (info.getTotalBoxes() != null && !info.getTotalBoxes().equals(BigDecimal.ZERO)) {
                            tempCell.setCellValue(Double.parseDouble(info.getTotalBoxes().toString()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 13:
                        if (info.getOddBoxes() != null && !info.getOddBoxes().equals(BigDecimal.ZERO)) {
                            tempCell.setCellValue(Double.parseDouble(info.getOddBoxes().toString()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 14:
                        if (info.getWeight() != null && !info.getWeight().equals(BigDecimal.ZERO)) {
                            tempCell.setCellValue(Double.parseDouble(info.getWeight().toString()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 15:
                        if (info.getVolume() != null && !info.getVolume().equals(BigDecimal.ZERO)) {
                            tempCell.setCellValue(Double.parseDouble(info.getVolume().toString()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 16:
                        if (info.getTotalWeight() != null && !info.getTotalWeight().equals(BigDecimal.ZERO)) {
                            tempCell.setCellValue(Double.parseDouble(info.getTotalWeight().toString()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 17:
                        if (info.getTotalVolume() != null && !info.getTotalVolume().equals(BigDecimal.ZERO)) {
                            tempCell.setCellValue(Double.parseDouble(info.getTotalVolume().toString()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 18:
                        if (info.getTotalAmount() != null && !info.getTotalAmount().equals(BigDecimal.ZERO)) {
                            tempCell.setCellValue(info.getTotalAmount().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                if (i == 18) {
                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }
            sumAmt[0] = sumAmt[0].add(info.getContentsNumber() == null ? BigDecimal.valueOf(0) : info.getContentsNumber()); //总件数
            sumAmt[1] = sumAmt[1].add(info.getSumBoxes() == null ? BigDecimal.valueOf(0) : info.getSumBoxes());
            sumAmt[2] = sumAmt[2].add(info.getTotalBoxes() == null ? BigDecimal.valueOf(0) : info.getTotalBoxes());
            sumAmt[3] = sumAmt[3].add(info.getOddBoxes() == null ? BigDecimal.valueOf(0) : info.getOddBoxes());
            sumAmt[4] = sumAmt[4].add(info.getWeight() == null ? BigDecimal.valueOf(0) : info.getWeight());
            sumAmt[5] = sumAmt[5].add(info.getVolume() == null ? BigDecimal.valueOf(0) : info.getVolume());
            sumAmt[6] = sumAmt[6].add(info.getTotalWeight() == null ? BigDecimal.valueOf(0) : info.getTotalWeight());
            sumAmt[7] = sumAmt[7].add(info.getTotalVolume() == null ? BigDecimal.valueOf(0) : info.getTotalVolume());
            sumAmt[8] = sumAmt[8].add(info.getTotalAmount() == null ? BigDecimal.valueOf(0) : info.getTotalAmount());
        }

        //最后一行 合计金额
        Row s2r4 = sheet3.createRow(rowNum3[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"汇总", "", "", "", "", "",
                "", "", "", "", sumAmt[0].toString(), sumAmt[1].toString(), sumAmt[2].toString(), sumAmt[3].toString(), sumAmt[4].toString(), sumAmt[5].toString(), sumAmt[6].toString(), sumAmt[7].toString(), sumAmt[8].toString()};
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if (i > 9) {
                tempCell.setCellValue(new BigDecimal(s2row4[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
            } else {
                tempCell.setCellValue(s2row4[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else if (i == 18) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet3.addMergedRegion(new CellRangeAddress(rowNum3[0] - 1, rowNum3[0] - 1, 0, 9));
        return hssfWorkbook;
    }

    /**
     * 第九个sheet 固定费明细
     */
    public SXSSFWorkbook fixedExpensesSummary(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, List<Long> list) {
        String tenantid = RequestContext.getTenantId();
        List<BmsYsBillExportInfo> inWarList = ysState.getFixedExpensesByBillId(tenantid, list);
        if (CollUtil.isEmpty(inWarList)) {
            return hssfWorkbook;
        }

        // 第4个sheet
        Sheet sheet3 = hssfWorkbook.createSheet("固定费用明细");
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum3 = {0};

        for (BmsYsBillExportInfo bmsYsBillExportInfo : inWarList) {
            if (StrUtil.isNotEmpty(bmsYsBillExportInfo.getFrequency()) && dicMap.containsKey("frequency" + bmsYsBillExportInfo.getFrequency())) {
                bmsYsBillExportInfo.setFrequencyName(dicMap.get("frequency" + bmsYsBillExportInfo.getFrequency()));
            }
        }

        //第1行
        Row s2r2 = sheet3.createRow(rowNum3[0]++);
        s2r2.setHeight((short) 500);
        String[] s3row2 = {"计费单号", "费用项", "金额", "收费频次", "收费开始时间", "收费结束时间", "费用备注"};

        for (int i = 0; i < s3row2.length; i++) {
            String name = s3row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            s3row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s3row2.length; i++) {
            sheet3.setColumnWidth(i, 5000);
            Cell tempCell = s2r2.createCell(i);
            tempCell.setCellValue(s3row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        // 创建数据行 入库明细
        String lastId = "";
        Integer row = 1;
        BigDecimal[] sumAmt = {
                new BigDecimal("0")
        };
        for (BmsYsBillExportInfo info : inWarList) {
            Row s2r3 = sheet3.createRow(rowNum3[0]++);
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s3row2.length; i++) {
                List<BigDecimal> amtList = new ArrayList<>();
                Cell tempCell = s2r3.createCell(i);

                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getExpensesCode());
                        break;
                    case 1:
                        tempCell.setCellValue(info.getItemName());
                        break;
                    case 2:
                        tempCell.setCellValue(info.getAmount() == null ? 0 : info.getAmount().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 3:
                        tempCell.setCellValue(info.getFrequencyName());
                        break;
                    case 4:
                        tempCell.setCellValue(info.getStartDate());
                        break;
                    case 5:
                        tempCell.setCellValue(info.getEndDate());
                        break;
                    case 6:
                        tempCell.setCellValue(info.getRemarks());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                if (PubNumEnum.zero.getIntValue().equals(i)) {
                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }
            sumAmt[0] = sumAmt[0].add(info.getAmount());
        }

        //最后一行 合计金额
        Row s2r4 = sheet3.createRow(rowNum3[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"汇总", "", sumAmt[0].toString(), "", ""
                , "", ""};
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if (PubNumEnum.zero.getIntValue().equals(i)) {
                tempCell.setCellValue(Double.parseDouble(s2row4[i]));
            } else {
                tempCell.setCellValue(s2row4[i]);
            }
            if (PubNumEnum.zero.getIntValue().equals(i)) {
                tempCell.setCellStyle(commonStyle2);
            } else if (PubNumEnum.two.getIntValue().equals(i)) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet3.addMergedRegion(new CellRangeAddress(rowNum3[0] - 1, rowNum3[0] - 1, 0, 1));
        return hssfWorkbook;
    }

    /**
     * 第九个sheet 增值费明细
     */
    public SXSSFWorkbook valueAddedFeeSummary(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, List<Long> list) {
        String tenantid = RequestContext.getTenantId();
        List<BmsYsBillExportInfo> inWarList = ysState.getValueAddedFeeByBillId(tenantid, list);
        if (CollUtil.isEmpty(inWarList)) {
            return hssfWorkbook;
        }

        // 第4个sheet
        Sheet sheet3 = hssfWorkbook.createSheet("增值费用明细");
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        for (BmsYsBillExportInfo bmsYsBillExportInfo : inWarList) {
            if (StrUtil.isNotEmpty(bmsYsBillExportInfo.getOrderSource()) && dicMap.containsKey("order_source" + bmsYsBillExportInfo.getOrderSource())) {
                bmsYsBillExportInfo.setOrderSourceName(dicMap.get("order_source" + bmsYsBillExportInfo.getOrderSource()));
            }
            if (bmsYsBillExportInfo.getFeeSource() != null && dicMap.containsKey("fee_source" + bmsYsBillExportInfo.getFeeSource())) {
                bmsYsBillExportInfo.setFeeSourceName(dicMap.get("fee_source" + bmsYsBillExportInfo.getFeeSource()));
            }
        }

        final int[] rowNum3 = {0};
        //第1行
        Row s2r2 = sheet3.createRow(rowNum3[0]++);
        s2r2.setHeight((short) 500);
        String[] s3row2 = {"计费单号", "关联单号", "计费时间", "增值类型", "费用一级类型",
                "费用二级类型", "金额", "数量", "单位", "费用来源", "单据来源", "发生时间", "仓库编码", "仓库名称", "费用备注"};

        for (int i = 0; i < s3row2.length; i++) {
            String name = s3row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            s3row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s3row2.length; i++) {
            Cell tempCell = s2r2.createCell(i);
            sheet3.setColumnWidth(i, 5000);
            tempCell.setCellValue(s3row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        // 创建数据行 入库明细
        String lastId = "";
        Integer row = 1;
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (BmsYsBillExportInfo info : inWarList) {
            Row s2r3 = sheet3.createRow(rowNum3[0]++);
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s3row2.length; i++) {
                List<BigDecimal> amtList = new ArrayList<>();
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getExpensesCode());
                        break;
                    case 1:
                        tempCell.setCellValue(info.getRelateCode());
                        break;
                    case 2:
                        String date = Objects.isNull(info.getChargingDate()) ? "" : sdf.format(info.getChargingDate());
                        tempCell.setCellValue(date);
                        break;
                    case 3:
                        tempCell.setCellValue(info.getFeeBelongDesc());
                        break;
                    case 4:
                        tempCell.setCellValue(info.getItemOlevelName());
                        break;
                    case 5:
                        tempCell.setCellValue(info.getItemName());
                        break;
                    case 6:
                        tempCell.setCellValue(info.getAmount() == null ? 0 : info.getAmount().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 7:
                        tempCell.setCellValue(info.getSkuNumber() == null ? 0 : Double.parseDouble(info.getSkuNumber().toString()));
                        break;
                    case 8:
                        tempCell.setCellValue(info.getUnit());
                        break;
                    case 9:
                        tempCell.setCellValue(info.getFeeSourceName());
                        break;
                    case 10:
                        tempCell.setCellValue(info.getOrderSourceName());
                        break;
                    case 11:
                        tempCell.setCellValue(info.getOperTime());
                        break;
                    case 12:
                        tempCell.setCellValue(info.getWarehouseCode());
                        break;
                    case 13:
                        tempCell.setCellValue(info.getWarehouseName());
                        break;
                    case 14:
                        tempCell.setCellValue(info.getRemarks());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                if (PubNumEnum.six.getIntValue().equals(i)) {
                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }
            sumAmt[0] = sumAmt[0].add(info.getSkuNumber() == null ? BigDecimal.ZERO : info.getSkuNumber());
            sumAmt[1] = sumAmt[1].add(info.getAmount());
        }

        //最后一行 合计金额
        Row s2r4 = sheet3.createRow(rowNum3[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"汇总", "", "", "", "", "",
                sumAmt[1].toString(), sumAmt[0].toString(), "", "", "", "", "", "", ""};
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if (PubNumEnum.seven.getIntValue().equals(i) || PubNumEnum.six.getIntValue().equals(i)) {
                tempCell.setCellValue(Double.parseDouble(s2row4[i]));
            } else {
                tempCell.setCellValue(s2row4[i]);
            }
            if (PubNumEnum.zero.getIntValue().equals(i)) {
                tempCell.setCellStyle(commonStyle2);
            } else if (PubNumEnum.five.getIntValue().equals(i)) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet3.addMergedRegion(new CellRangeAddress(rowNum3[0] - 1, rowNum3[0] - 1, 0, 5));
        return hssfWorkbook;
    }


    //导出应收账单主表列表
    @Log(title = "应收账单主表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportDetail")
    @MenuAuthority(code = "应收账单管理-账单下载")
    public ResponseResult<String> selectBmsYsBillExport(HttpServletRequest request, @RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        Long id = JSONObject.parseObject(json).getLong("id");
        BmsYsBillExportInfo billInfo = ysState.getBillingInformation(loginUserInfo.getTenantid().toString(), id);
        if(billInfo==null){
            throw new BusinessException("未获取到账单信息");
        }
        String fileName = billInfo.getBillName();
        return exportUtil.getOutClassNewBillSheets2(token, fileName, billInfo.getBillCode(), "应收账单管理", id, BmsYsbillmainExportAll.class, userId -> {
            // 根据仓库名称查询仓库编码
            return new ArrayList<>();
        });
    }

    public ResponseResult<String> exportSheets(Long id) {
        String token = RequestContext.getToken();
        String tenantid = RequestContext.getTenantId();
        try {
            //获取改账单所有的子账单 ，返回的数据中不包括改账单
            List<BmsYsbillmain> descendantsByBillId = bmsYsbillmainService.findDescendantsByBillId(token, id.toString());
            List<Long> list = new ArrayList<>();
            if (!descendantsByBillId.isEmpty()) {
                List<Long> collect = descendantsByBillId.stream().map(BmsYsbillmain::getId).toList();
                list.addAll(collect);
            }
            //获取主张单基础信息以及金额为子账单总和
            BmsYsbillmainExportAll bmsYsbillmainExportAll = bmsYsbillmainService.getMergeYsExportAll(token, id, list);
            list.add(id);
            //计算运输，仓储 客诉费
            List<BmsYsbillmainExportAll> exTionFeeList = ysState.selectBmsYsBillExtionFeeInfo(tenantid, list);

            if (CollUtil.isNotEmpty(exTionFeeList)) {
                //仓储单客诉理赔费
                bmsYsbillmainExportAll.setCcExceptionFee(exTionFeeList.stream().filter(e -> e.getBillType().equals(BillTypeEnum.STOCK_BILL.getIntValue())).map(BmsYsbillmainExportAll::getExceptionFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                bmsYsbillmainExportAll.setCcFeeSum(bmsYsbillmainExportAll.getCcFeeSum().add(bmsYsbillmainExportAll.getCcExceptionFee()));
                bmsYsbillmainExportAll.setCcUnClaimesAmount(exTionFeeList.stream().filter(e -> e.getBillType().equals(BillTypeEnum.STOCK_BILL.getIntValue())).map(BmsYsbillmainExportAll::getUnClaimesAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                //运输单客诉理赔费
                bmsYsbillmainExportAll.setPsExceptionFee(exTionFeeList.stream().filter(e -> e.getBillType().equals(BillTypeEnum.TRANS_BILL.getIntValue())).map(BmsYsbillmainExportAll::getExceptionFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                bmsYsbillmainExportAll.setPsFeeSum(bmsYsbillmainExportAll.getPsFeeSum().add(bmsYsbillmainExportAll.getPsExceptionFee()));
                bmsYsbillmainExportAll.setYsUnClaimesAmount(exTionFeeList.stream().filter(e -> e.getBillType().equals(BillTypeEnum.TRANS_BILL.getIntValue())).map(BmsYsbillmainExportAll::getUnClaimesAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }

            Map<String, String> dictByMap = textConversionUtil.getDictByType("order_type,fee_source,order_source,sys_fee_frequency", "1");
            Map<String, String> dicMap = textConversionUtil.getDictByType("ys_transport_othercost,ys_storage_othercost,fee_source,order_source,sys_fee_frequency", "2");
            dicMap.putAll(dictByMap);
            //Excel
            SXSSFWorkbook hssfWorkbook = new SXSSFWorkbook();
            /*
             * 第一个sheet 账单汇总
             */
            hssfWorkbook = billSummary(hssfWorkbook, bmsYsbillmainExportAll, dicMap, id, list);
            // 如果仓储费为空，就不建立仓储sheet
            if ((bmsYsbillmainExportAll.getBillType() != null && BmsConstants.GENERATE_BILL_TYPE_STROCK_SETS.contains(bmsYsbillmainExportAll.getBillType())) || (bmsYsbillmainExportAll.getCcFeeSum() != null && bmsYsbillmainExportAll.getCcFeeSum().compareTo(BigDecimal.ZERO) != 0)) {
                /*
                 * 第二个sheet 存储费主信息
                 */
                long time1 = System.currentTimeMillis();
                hssfWorkbook = storageSummary(hssfWorkbook, bmsYsbillmainExportAll, dicMap, list);
                System.out.println("第二个sheet 存储费主信息花费：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
                time1 = System.currentTimeMillis();
                /*
                 * 第三个sheet 存储服务费明细信息
                 */
                hssfWorkbook = storageDetailSummary(hssfWorkbook, bmsYsbillmainExportAll, dicMap, list);
                System.out.println("第三个sheet 存储服务费明细信息花费：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
                time1 = System.currentTimeMillis();
                /*
                 * 第四个sheet 入库费主信息
                 */
                hssfWorkbook = inWarehouseSummaryMain(hssfWorkbook, bmsYsbillmainExportAll, dicMap, list);
                System.out.println("第四个sheet 入库费主信息：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
                time1 = System.currentTimeMillis();
                /*
                 * 第五个sheet 入库费明细信息
                 */
                hssfWorkbook = inWarehouseDetailsSummary(hssfWorkbook, bmsYsbillmainExportAll, dicMap, list);
                System.out.println("第五个sheet 入库费明细信息：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
                time1 = System.currentTimeMillis();
                /*
                 * 第六个sheet 出库费主信息
                 */
                hssfWorkbook = outWarehouseSummaryMain(hssfWorkbook, bmsYsbillmainExportAll, dicMap, list);
                System.out.println("第六个sheet 出库费主信息：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
                time1 = System.currentTimeMillis();
                /*
                 * 第七个sheet 出库费明细信息
                 */
                hssfWorkbook = outWarehousDetailsSummary(hssfWorkbook, bmsYsbillmainExportAll, dicMap, list);
                System.out.println("第七个sheet 出库费明细信息：" + (System.currentTimeMillis() - time1) / 1000 + "秒");
                time1 = System.currentTimeMillis();
            }

            // 如果运输费为空，就不建立运输sheet
            if ((bmsYsbillmainExportAll.getBillType() != null && BmsConstants.GENERATE_BILL_TYPE_TRANS_SETS.contains(bmsYsbillmainExportAll.getBillType())) || (bmsYsbillmainExportAll.getPsFeeSum() != null && bmsYsbillmainExportAll.getPsFeeSum().compareTo(BigDecimal.ZERO) != 0)) {
                /*
                 * 第六个sheet 配送明细1(月配送量)
                 */
                hssfWorkbook = distributionDetailsSummary1(hssfWorkbook, bmsYsbillmainExportAll, dicMap, list);

                /*
                 * 第七个sheet 配送明细2(配送费主信息)
                 */
                hssfWorkbook = distributionDetailsSummary2(hssfWorkbook, bmsYsbillmainExportAll, dicMap, list);

                /* 第八个商品明细sheet*/
                hssfWorkbook = orderGoodsDetailSummary(hssfWorkbook, bmsYsbillmainExportAll, dicMap, list);
            }
            hssfWorkbook = fixedExpensesSummary(hssfWorkbook, bmsYsbillmainExportAll, dicMap, list);

            /* 第十个固定费明细sheet*/
            hssfWorkbook = valueAddedFeeSummary(hssfWorkbook, bmsYsbillmainExportAll, dicMap, list);

            //理赔费用明细sheet
            hssfWorkbook = billClaimsSheet(hssfWorkbook, bmsYsbillmainExportAll, dicMap, id);

            try {
                BmsYsBillExportInfo bmsYsBillExportInfo = ysState.getBillingInformation(tenantid, id);
                FileOutputStream out = null;
                // 文件名
                String fileName = bmsYsbillmainExportAll.getBillName() + ".xlsx";
                out = new FileOutputStream(ExcelExportUtil.getAbsoluteFile(fileName));
                try {
                    ResponseResult<String> result = new ResponseResult<>(true, 0, fileName, fileName);
                    hssfWorkbook.write(out);
                    out.close();
                    return result;
                } catch (IOException e) {
                    log.error("系统异常", e);
                    return new ResponseResult<>(40005, "导出异常");
                }

            } catch (FileNotFoundException e) {
                FileOutputStream out = null;
                // 文件名
                String fileName = bmsYsbillmainExportAll.getBillName() + ".xlsx";
                out = new FileOutputStream(ExcelExportUtil.getAbsoluteFileios(fileName));
                try {
                    ResponseResult<String> result = new ResponseResult<>(true, 0, fileName, fileName);
                    hssfWorkbook.write(out);
                    out.close();
                    return result;
                } catch (IOException ie) {
                    return new ResponseResult<>(40005, "导出异常");
                }
            }
        } catch (Exception e) {
            return new ResponseResult<>(40005, "导出异常");
        }

    }


    /**
     * 根据客户账单日，判断费用结算期
     *
     * @param billDate 账期
     * @param day      账单日
     * @param endDate  终结日
     */
    public String getfeeDayStr(String billDate, int day, String endDate) {
        String feeDayStr = "";
        String endDateStr = "";
        if (StrUtil.isNotEmpty(endDate)) {
            endDateStr = endDate.split("-")[0] + "年" + endDate.split("-")[1] + "月" + endDate.split("-")[2] + "日";
        }

        // 获取
        int lastDay = getMaxDayByYearMonth(Integer.parseInt(billDate.split("-")[0]), Integer.parseInt(billDate.split("-")[1]));
        if (day >= lastDay) {
            // billDate 的自然月
            if (StrUtil.isNotEmpty(endDateStr)) {
                feeDayStr = billDate.split("-")[0] + "年" + billDate.split("-")[1] + "月1日至" + endDateStr;
            } else {
                feeDayStr = billDate.split("-")[0] + "年" + billDate.split("-")[1] + "月1日至" + billDate.split("-")[0] + "年" + billDate.split("-")[1] + "月" + lastDay + "日";
            }

        } else {
            // 非自然月
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DATE, 1);
            calendar.set(Calendar.YEAR, Integer.parseInt(billDate.split("-")[0]));
            calendar.set(Calendar.MONTH, Integer.parseInt(billDate.split("-")[1]) - 2);
            String year = new SimpleDateFormat("yyyy").format(calendar.getTime());
            String month = new SimpleDateFormat("MM").format(calendar.getTime());
            if (StrUtil.isNotEmpty(endDateStr)) {
                feeDayStr = year + "年" + month + "月" + day + "日至" + endDateStr;
            } else {
                feeDayStr = year + "年" + month + "月" + day + "日至" + billDate.split("-")[0] + "年" + billDate.split("-")[1] + "月" + day + "日";
            }

        }

        return feeDayStr;
    }

    /**
     * 获得某个月最大天数
     *
     * @param year  年份
     * @param month 月份 (1-12)
     * @return 某个月最大天数
     */
    public int getMaxDayByYearMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, 1);
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        return calendar.getActualMaximum(Calendar.DATE);
    }


    /**
     * 理赔sheet
     */
    public SXSSFWorkbook billClaimsSheet(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, Long id) {
        String tenantid = RequestContext.getTenantId();
        String token = RequestContext.getToken();
        // 查询账单理赔数据
        List<BmsClaimsInfo> inWarList = new ArrayList<>();
        List<BmsYsBillExportInfo> billCodeById = ysState.getBillCodeById(tenantid, id);
        if (CollUtil.isNotEmpty(billCodeById)) {
            BmsClaimsInfo claimsInfo = new BmsClaimsInfo();
            claimsInfo.setSize(Integer.MAX_VALUE / 100);
            claimsInfo.setBillCode(billCodeById.stream().map(BmsYsBillExportInfo::getBillCode).collect(Collectors.joining(",")));
            inWarList = bmsClaimsInfoService.selectBmsClaimsInfoList(token, claimsInfo).getRows();
        }
        if (CollUtil.isEmpty(inWarList)) {
            return hssfWorkbook;
        }

        Sheet sheet3 = hssfWorkbook.createSheet("理赔费用明细");
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum3 = {0};

        //第1行
        Row s2r2 = sheet3.createRow(rowNum3[0]++);
        s2r2.setHeight((short) 500);
        String[] s3row2 = {"工单单号", "业务单号", "仓库编码", "仓库名称",
                "理赔类型", "是否拆单", "理赔金额", "实际理赔金额", "发起人", "投诉时间", "工单创建时间"
                , "工单内容", "工单大类",
                "工单子类", "门店编码", "门店名称", "区域", "所属省份", "市", "区", "受理人", "受理时间", "完成人", "完成时间", "关闭人", "关闭时间", "对账备注", "实际账期"};

        for (int i = 0; i < s3row2.length; i++) {
            String name = s3row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            s3row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s3row2.length; i++) {
            Cell tempCell = s2r2.createCell(i);
            tempCell.setCellValue(s3row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        // 创建数据行 入库明细
        String lastId = "";
        Integer row = 1;
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (BmsClaimsInfo info : inWarList) {
            Row s2r3 = sheet3.createRow(rowNum3[0]++);
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s3row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        //工单号
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getWorkOrderCode()) ? info.getWorkOrderCode() : "");
                        break;
                    case 1:
                        //业务单号
                        tempCell.setCellValue(info.getRelatedOrderCode());
                        break;
                    case 2:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getWarehouseCode()) ? info.getWarehouseCode() : "");
                        break;
                    case 3:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getWarehouseName()) ? info.getWarehouseName() : "");
                        break;
                    case 4:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getClaimsTypeName()) ? info.getClaimsTypeName() : "");
                        break;
                    case 5:
                        tempCell.setCellValue(info.getSplitStatusName());
                        break;
                    case 6:
                        //理赔金额，原金额
                        tempCell.setCellValue(Double.parseDouble(info.getOldResponsibleMoney().toString()));
                        break;
                    case 7:
                        //实际理赔金额
                        tempCell.setCellValue(Double.parseDouble(info.getResponsibleMoney().toString()));
                        break;
                    case 8:
                        //发起人
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getCreateUserName()) ? info.getCreateUserName() : "");
                        break;
                    case 9:
                        if (info.getComplaintTime() != null) {
                            tempCell.setCellValue(sdf.format(info.getComplaintTime()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 10:
                        if (info.getWorkCreateTime() != null) {
                            tempCell.setCellValue(sdf.format(info.getWorkCreateTime()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 11:
                        tempCell.setCellValue(info.getComplaintContent());
                        break;
                    case 12:
                        tempCell.setCellValue(info.getPrimaryClassificationName());
                        break;
                    case 13:
                        tempCell.setCellValue(info.getSecondaryClassificationName());
                        break;
                    case 14:
                        tempCell.setCellValue(info.getShopCode());
                        break;
                    case 15:
                        tempCell.setCellValue(info.getShopName());
                        break;
                    case 16:
                        tempCell.setCellValue(info.getArea());
                        break;
                    case 17:
                        tempCell.setCellValue(info.getProvince());
                        break;
                    case 18:
                        tempCell.setCellValue(info.getCity());
                        break;
                    case 19:
                        tempCell.setCellValue(info.getDistrict());
                        break;
                    case 20:
                        tempCell.setCellValue(info.getHandleUserName());
                        break;
                    case 21:
                        if (info.getHandleTime() != null) {
                            tempCell.setCellValue(sdf.format(info.getHandleTime()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 22:
                        tempCell.setCellValue(info.getFinishUserName());
                        break;
                    case 23:
                        if (info.getFinishTime() != null) {
                            tempCell.setCellValue(sdf.format(info.getFinishTime()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 24:
                        tempCell.setCellValue(info.getCloseUserName());
                        break;
                    case 25:
                        if (info.getWorkCloseTime() != null) {
                            tempCell.setCellValue(sdf.format(info.getWorkCloseTime()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 26:
                        tempCell.setCellValue(info.getAdjustRemark());
                        break;

                    case 27:
                        tempCell.setCellValue(info.getBillDate());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }
            sumAmt[0] = sumAmt[0].add(info.getResponsibleMoney() == null ? BigDecimal.valueOf(0) : info.getResponsibleMoney());
            sumAmt[1] = sumAmt[1].add(info.getOldResponsibleMoney() == null ? BigDecimal.valueOf(0) : info.getOldResponsibleMoney());
        }

        //最后一行 合计金额
        Row s2r4 = sheet3.createRow(rowNum3[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"汇总", "", "", "",
                "", "", sumAmt[1].toString(), sumAmt[0].toString(), "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""};
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if (PubNumEnum.six.getIntValue().equals(i) || PubNumEnum.seven.getIntValue().equals(i)) {
                tempCell.setCellValue(new BigDecimal(s2row4[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
            } else {
                tempCell.setCellValue(s2row4[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else if (i == 27) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet3.addMergedRegion(new CellRangeAddress(rowNum3[0] - 1, rowNum3[0] - 1, 0, 5));
        return hssfWorkbook;
    }


}
