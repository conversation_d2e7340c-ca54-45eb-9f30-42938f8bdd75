package com.bbyb.joy.bms.calculate.domain.result;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * 单据信息计费结果
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateCodeResult implements Serializable {

    /**
     * 单据类型(1:运输单,2:仓储单)
     */
    private Integer codeType;
    /**
     * 计费成功单据ids(单据自增id)
     */
    private Set<Integer> successCodePkIds = new HashSet<>();
    /**
     * 计费失败单据ids(单据自增id)
     */
    private Set<Integer> failCodePkIds = new HashSet<>();
    /**
     * 单据计费记录
     */
    private CalculateRemarkResult costResult = new CalculateRemarkResult();


}
