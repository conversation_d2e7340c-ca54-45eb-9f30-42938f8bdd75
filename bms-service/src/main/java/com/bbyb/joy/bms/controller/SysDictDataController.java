package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.domain.dto.FileBaseDto;
import com.bbyb.joy.bms.domain.dto.entity.SysDictData;
import com.bbyb.joy.bms.domain.enums.SysCodeEnum;
import com.bbyb.joy.bms.service.ISysDictDataService;
import com.bbyb.joy.bms.service.ISysDictTypeService;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.utils.ExcelUtil;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.mdm.bean.dic.DicQueryBean;
import com.bbyb.joy.mdm.dto.DicDto;
import com.bbyb.joy.mdm.feign.DicFeignService;
import com.google.common.collect.Sets;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping({"/system/dict/data"})
public class SysDictDataController {
    @Resource
    private ISysDictDataService dictDataService;
    @Resource
    private ISysDictTypeService dictTypeService;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private DicFeignService dicFeignService;

    public SysDictDataController() {
    }

    /**
     * 分页查询
     */
    @PostMapping({"/list"})
    public ResponseResult<PagerDataBean<SysDictData>> list(@RequestBody(required = false) SysDictData dictData) {
        String token = RequestContext.getToken();
        if (dictData == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(this.dictDataService.selectDictDataList(token, dictData));
    }

    /**
     * 导出
     */
    @PostMapping({"/export"})
    public ResponseResult<String> export(@RequestBody(required = false) SysDictData dictData) {
        String token = RequestContext.getToken();
        if (dictData == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        PagerDataBean<SysDictData> list = this.dictDataService.selectDictDataList(token, dictData);
        ExcelUtil<SysDictData> util = new ExcelUtil<>(SysDictData.class);
        FileBaseDto fileBaseDto = util.exportExcel(list.getRows(), "字典数据");
        return new ResponseResult<>(true, 0, fileBaseDto.getFileName(), fileBaseDto.getFileName());
    }

    /**
     * 查询字典详情
     */
    @PostMapping({"/{dictCode}"})
    public ResponseResult<SysDictData> getInfo(@PathVariable Long dictCode) {
        String token = RequestContext.getToken();
        if (dictCode == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(this.dictDataService.selectDictDataById(token, dictCode));
    }

    /**
     * 字典控件
     */
    public static final Set<String> SPECIAL_DEAL_DICT_SET = Sets.newHashSet("ys_transport_othercost","ys_storage_othercost","yf_transport_othercost","yf_storage_othercost","yf_storage_basecost","yf_transport_basecost","ys_storage_basecost","ys_transport_basecost");
    @PostMapping({"/type/{dictType}"})
    public ResponseResult<List<SysDictData>> dictType(@PathVariable String dictType) {
        if (dictType == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }

        DicQueryBean queryBean = new DicQueryBean();
        queryBean.setTypenames(dictType);
        queryBean.setSystype(SysCodeEnum.BMS.getName());
        queryBean.setTypeNameQueryType(1);
        queryBean.setPage(1);
        queryBean.setSize(Integer.MAX_VALUE);
        ResponseResult<PagerDataBean<DicDto>> query = dicFeignService.query(queryBean);

        List<SysDictData> data = new ArrayList<>(query.getResult().getRows().size());
        if (query.getResult() == null || CollUtil.isEmpty(query.getResult().getRows())) {
            data = new ArrayList<>();
        } else {
            for (DicDto e : query.getResult().getRows()) {
                SysDictData bean = new SysDictData();
                bean.setDictCode(Long.valueOf(e.getId()));
                bean.setDictSort(Long.valueOf(e.getOrderid()));
                bean.setDictLabel2(e.getParameter2());
                if(SPECIAL_DEAL_DICT_SET.contains(e.getTypename())){
                    bean.setDictLabel(e.getParameter2());
                    bean.setDictValue(e.getItemname());
                }else {
                    bean.setDictLabel(e.getItemname());
                    bean.setDictValue(e.getValue().toString());
                }
                bean.setDictType(e.getParameter1());
                bean.setListClass(e.getParameter3());
                bean.setIsDefault(e.getParameter4());
                bean.setCssClass(e.getParameter5());
                bean.setStatus(String.valueOf(e.getInvalid()));
                data.add(bean);
            }
        }
        return new ResponseResult<>(data);
    }


    /**
     * mdm字典控件
     */
    @PostMapping({"/type/mdm"})
    public ResponseResult<List<SysDictData>> dictType(@RequestBody SysDictData dictData) {

        if (dictData == null || StrUtil.isEmpty(dictData.getDictType())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        if(StrUtil.isEmpty(dictData.getSysCode())){
            dictData.setSysCode(SysCodeEnum.BMS.getValue());
        }
        DicQueryBean queryBean = new DicQueryBean();
        queryBean.setTypenames(dictData.getDictType());
        queryBean.setSystype(dictData.getSysCode());
        queryBean.setTypeNameQueryType(1);
        queryBean.setPage(1);
        queryBean.setSize(Integer.MAX_VALUE);
        ResponseResult<PagerDataBean<DicDto>> listResponseResult = dicFeignService.query(queryBean);

        if(listResponseResult.getResult() == null){
            return new ResponseResult<>(new ArrayList<>());
        }
        List<DicDto> rows = listResponseResult.getResult().getRows();

        List<SysDictData> data = new ArrayList<>();
        for (DicDto row : rows) {
            SysDictData initData = new SysDictData();
            initData.setDictType(row.getTypename());
            initData.setDictValue(row.getValue().toString());
            initData.setDictLabel(row.getItemname());
            initData.setDictSort(row.getOrderid()!=null?row.getOrderid().longValue():null);
            initData.setDicDto(row);
            data.add(initData);
        }
        return new ResponseResult<>(data);
    }

    /**
     * 字典新增
     */
    @PostMapping("/add")
    public ResponseResult<Integer> add(@RequestBody(required = false) SysDictData dict) {
        String token = RequestContext.getToken();
        UserBean userVO = RequestContext.getUserInfo();
        dict.setCreateBy(userVO.getEmployeename());
        SysDictData dictData = new SysDictData();
        dictData.setDictType(dict.getDictType());
        dictData.setDictValue(dict.getDictValue());
        PagerDataBean<SysDictData> sysDictDataList = this.dictDataService.selectDictDataList(token, dictData);
        if (CollUtil.isNotEmpty(sysDictDataList.getRows())) {
            return new ResponseResult<>(false, 405, "字典键值:" + dict.getDictValue() + "已存在,不允许重复", null);
        } else {
            return new ResponseResult<>(this.dictDataService.insertDictData(token, dict));
        }
    }

    /**
     * 字典修改
     */
    @PostMapping("/edit")
    public ResponseResult<Integer> edit(@RequestBody(required = false) SysDictData dict) {
        String token = RequestContext.getToken();
        UserBean userVO = RequestContext.getUserInfo();
        dict.setCreateBy(userVO.getEmployeename());
        SysDictData dictData = new SysDictData();
        dictData.setDictType(dict.getDictType());
        dictData.setDictValue(dict.getDictValue());
        PagerDataBean<SysDictData> sysDictDataList = this.dictDataService.selectDictDataList(token, dictData);
        if (CollUtil.isNotEmpty(sysDictDataList.getRows()) && !((SysDictData) sysDictDataList.getRows().get(0)).getDictCode().equals(dict.getDictCode())) {
            return new ResponseResult<>(false, 405, "字典键值:" + dict.getDictValue() + "已存在,不允许重复", null);
        } else {
            return new ResponseResult<>(this.dictDataService.updateDictData(token, dict));
        }
    }

    /**
     * 字典作废
     */
    @DeleteMapping({"/{dictCodes}"})
    public ResponseResult<Boolean> remove(@PathVariable Long[] dictCodes) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(this.dictDataService.deleteDictDataByIds(token, dictCodes));
    }
}
