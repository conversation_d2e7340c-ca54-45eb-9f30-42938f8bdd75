package com.bbyb.joy.bms.support;

import com.bbyb.joy.enums.EnumValueSupport;

/**
 * @classname: ServiceError
 * @description: 服务错误信息
 * @author: xzy
 * @date: 2021/12/02 19:41
 * @version: 1.0
 */
public enum DownLoadServiceError implements EnumValueSupport {
    //参数不可为空
    PARAMS_ERROR(47000001, "参数不可为空"),
    PARAMS_ERROR1(47000002, "请传入模块名称"),
    PARAMS_ERROR2(47000003, "id不可为空"),
    PARAMS_ERROR3(47000004, "系统暂无该数据"),

    ;


    /**
     * code
     */
    private int code;
    /**
     * message
     */
    private String message;

    private DownLoadServiceError(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int value() {
        return this.code;
    }

    @Override
    public String displayName() {
        return this.message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}
