package com.bbyb.joy.bms.calculate.domain.dto.code.order;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateOrderExpandDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 单据类型(1:运输单,2:仓储单)
     */
    private Integer codeType;

    /**
     * 单据策略类型:1:运输单,21:仓储单(应收),22:仓储单(应付),3:调度单
     */
    private Integer handleType;
    /**
     * 单据信息
     */
    private CalculateOrderDto code;
    /**
     * 单据详情信息
     */
    private List<CalculateOrderDetailDto> detail;
    /**
     * 单据详情2
     * 目前针对到业务场景使用单据:作业单
     */
    private List<CalculateOrderDetail2Dto> detail2;


}
