package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.charginglogic.DispatchBillingBean;
import com.bbyb.joy.bms.domain.dto.dto.*;
import com.bbyb.joy.bms.domain.dto.querybean.BmsYfbillcodeinfoBean;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.WarehouseInfo;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsYfbillcodeinfoService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.exception.BusinessException;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 应付单据信息主Controller
 */
@Api(tags = "应付运输计费主接口")
@RestController
@RequestMapping("/system/yfbillcodeinfo")
public class BmsYfbillcodeinfoController {

    @Resource
    private IBmsYfbillcodeinfoService bmsYfbillcodeinfoService;
    @Resource
    private UserRights userRights;
    @Resource
    private ExportUtil exportUtil;

    /**
     * 运输计费分页查询
     */
    @ApiOperation(value = "运输计费分页查询", response = BmsYfbillcodeDto.class)
    @PostMapping("/selectOrderBill")
    @MenuAuthority(code = "应付运输计费-批量查询")
    public ResponseResult<PagerDataBean<BmsYfbillcodeDto>> selectOrderBill(@RequestBody BmsYfbillcodeinfoBean bean) {
        String token = RequestContext.getToken();
        List<BmsYfbillcodeDto> list = new ArrayList<>();
        // 加权限
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "carrier,warehouse");
        List<Carrierinfo> carrierInfos = sysDataPack.getCarrierInfo();
        List<WarehouseInfo> warehouseInfos = sysDataPack.getWarehouseInfo();
        bean.setCarrierCode(carrierInfos.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.joining(",")));
        bean.setWarehouseCompanyId(warehouseInfos.stream().map(e -> e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        if (StrUtil.isEmpty(bean.getCarrierCode())) {
            return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(bean.getPage(), bean.getSize())));
        }
        return new ResponseResult<>(bmsYfbillcodeinfoService.selectOrderBillPage(token, bean));
    }


    @ApiOperation(value = "订单计费分页查询", response = BmsYsbillcodeDto.class)
    @PostMapping("/billed/selectOrderBill")
    @MenuAuthority(code = "应付运输计费-应付计费账单")
    public ResponseResult<PagerDataBean<BmsYfbillcodeDto>> selectOrderBilled(@RequestBody BmsYfbillcodeinfoBean bean) {
        String token = RequestContext.getToken();
        List<BmsYfbillcodeDto> list = new ArrayList<>();
        // 加权限
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "carrier,warehouse");
        List<Carrierinfo> carrierInfos = sysDataPack.getCarrierInfo();
        List<WarehouseInfo> warehouseInfos = sysDataPack.getWarehouseInfo();
        bean.setCarrierCode(carrierInfos.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.joining(",")));
        bean.setWarehouseCompanyId(warehouseInfos.stream().map(e -> e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        if (StrUtil.isEmpty(bean.getCarrierCode()) || StrUtil.isEmpty(bean.getWarehouseCompanyId())) {
            return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(bean.getPage(), bean.getSize())));
        }
        return new ResponseResult<>(bmsYfbillcodeinfoService.selectOrderBilledPage(token, bean));
    }


    /**
     * 调度单计费导入
     */
    @ApiOperation(value = "调度单计费导入订单", response = MultipartFile.class)
    @PostMapping("/importOrder")
    @MenuAuthority(code = "应付运输计费-导入订单")
    public ResponseResult<String> importOrder(@RequestBody MultipartFile file) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillcodeinfoService.importOrder(token, file));
    }


    /**
     * 修改计费
     */
    @ApiOperation(value = "修改计费", response = DispatchBillingBean.class)
    @PostMapping("/updateCost")
    @MenuAuthority(code = "应付运输计费-修改费用")
    public ResponseResult<String> updateCost(@RequestBody DispatchBillingBean bean) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillcodeinfoService.updateCost(token, bean));
    }

    /**
     * 导入计费
     */
    @ApiOperation(value = "导入计费", response = DRYfcostInfoDto.class)
    @PostMapping("/importCost")
    @MenuAuthority(code = "应付运输计费-导入计费")
    public ResponseResult<String> importCost(@RequestBody DRYfcostInfoDto dto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillcodeinfoService.importCost(token, dto));
    }

    /**
     * 取消计费
     */
    @ApiOperation(value = "取消计费", response = DispatchBillingBean.class)
    @PostMapping("/cancelCost")
    @MenuAuthority(code = "应付运输计费-取消计费")
    public ResponseResult<String> cancelCost(@RequestBody List<DispatchBillingBean> list) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillcodeinfoService.cancelCost(token, list));
    }

    /**
     * 运输计费导出
     */
    @ApiOperation(value = "运输计费导出", response = BmsYfbillcodeDto.class)
    @PostMapping("/exportOrderBill")
    @MenuAuthority(code = "应付运输计费-导出")
    public ResponseResult<String> exportOrderBill(@RequestBody BmsYfbillcodeinfoBean bean) {
        String token = RequestContext.getToken();
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        Optional.ofNullable(bean).map(BmsYfbillcodeinfoBean::getCarrierName).filter(StrUtil::isNotBlank).orElseThrow(() -> new BusinessException("请先选择承运商"));
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "carrier,warehouse");
        List<Carrierinfo> carrierInfos = sysDataPack.getCarrierInfo();
        List<WarehouseInfo> warehouseInfos = sysDataPack.getWarehouseInfo();
        bean.setCarrierCode(carrierInfos.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.joining(",")));
        bean.setWarehouseCompanyId(warehouseInfos.stream().map(e -> e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        // 查询
        PagerDataBean<BmsYfbillcodeDto> list = bmsYfbillcodeinfoService.selectOrderBillPage(token, bean);
        String dateTime = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss").format(new Date());
        String fileNameMsg = "应付运输计费_" + dateTime;
        boolean f = true;
        String msg = list.getRows().get(0).getClientCode();
        for (BmsYfbillcodeDto bmsYfbillcodeDto : list.getRows()) {
            if (!StrUtil.equals(bmsYfbillcodeDto.getClientCode(), msg)) {
                f = false;
                break;
            }
        }
        if (f) {
            fileNameMsg = list.getRows().get(0).getClientName() + "_" + fileNameMsg;
        }
        return exportUtil.getOutClassNewSheets2(token, fileNameMsg, "应付运输计费", "yfbillcodeinfo", BmsYfbillcodeDto.class, bean, userid -> {

            if (StrUtil.isEmpty(bean.getCarrierCode()) || StrUtil.isEmpty(bean.getWarehouseCompanyId())) {
                return null;
            }
            return list.getRows();
        });
    }

    /**
     * 生成账单
     */
    @ApiOperation(value = "生成账单", response = BmsYsbillBtnDto.class)
    @Log(title = "生成账单", businessType = BusinessType.INSERT)
    @PostMapping("/generateBill")
    public ResponseResult<String> generateBill(@RequestBody(required = false) BmsYfbillBtnDto bmsYfbillBtnDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillcodeinfoService.generateBill(token, bmsYfbillBtnDto));
    }

    /**
     * 批量生成账单
     */
    @ApiOperation(value = "批量生成账单", response = BmsYsbillBtnDto.class)
    @Log(title = "批量生成账单", businessType = BusinessType.INSERT)
    @PostMapping("/generateBillBatch")
    public ResponseResult<String> generateBillBatch(@RequestBody(required = false) BmsYfbillBtnDto bmsYfbillBtnDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillcodeinfoService.generateBillBatch(token, bmsYfbillBtnDto));
    }

    /**
     * 根据计费id查询单据信息
     */
    @ApiOperation(value = "根据计费id查询单据信息", response = BmsYfbillcodeDto.class)
    @PostMapping("/selectOrderByExpensesId")
    public ResponseResult<List<BmsYfbillcodeDto>> selectOrderByExpensesId(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (json == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        String expensesId = JSONObject.parseObject(json).getString("expensesId");
        return new ResponseResult<>(bmsYfbillcodeinfoService.selectOrderByExpensesId(token, expensesId));
    }

    /**
     * 批量作废导入的单据数据
     */
    @PostMapping("/deleteImportData")
    @MenuAuthority(code = "应付运输计费-作废,应付仓储计费-作废")
    public ResponseResult<String> deleteImportData(@RequestBody(required = false) List<BmsYfbillcodeDto> codeList) {
        String token = RequestContext.getToken();
        if (CollUtil.isEmpty(codeList)) {
            return new ResponseResult<>(40005, "未获取到作废信息");
        }
        return new ResponseResult<>(bmsYfbillcodeinfoService.deleteImportData(token, codeList));
    }
}
