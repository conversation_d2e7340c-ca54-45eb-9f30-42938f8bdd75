package com.bbyb.joy.bms.calculate.domain.result;

import cn.hutool.core.lang.Dict;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyCalculateExtraType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 计费相关结果
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateCostResult implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 计费单号
     */
    private String expenseCode;
    /**
     * 费用类型(1调度单费2出库类费3入库类费4库存类费5仓储类费)
     */
    private Integer expenseType;
    /**
     * 费用维度(1.单2.趟3.日4.月)
     * 注意:只有按单才会有合并费用单逻辑
     */
    private Integer expensesDimension;
    /**
     * 结算方式0:默认 1月结2现结3季节4周结
     */
    private Integer settleType;
    /**
     * 结算主体(取租户BMD配置字段SettlementEntityType(是否存在多个结算主体 1不存在 2存在))
     */
    private Integer settleEntityType;
    /**
     * 结算方式(1:单结算方式,2:多结算方式)
     */
    private Integer settleSetting;
    /**
     * 结算主体(客户Id || 承运商Id)
     */
    private Integer settleMainId;
    /**
     * 费用维度(单，月，日，品)
     * 注意:只有按单才会有合并费用单逻辑
     */
    private String expensesDimensionName;
    /**
     * 费用类型(基础费&&其他费)
     * 计费报价费用类型转换费用表存储 (应收)
     * feeType 报价规则费用类型（字典 1 运费(freight) 2 超远费(ultrafarFee) 3 超筐费() 4 加点费 5 减点费 6 提货费 7 送货费 8 短驳费 9 退货费
     * 10 存储费 11 管理处置费 12 整箱分拣费 13 拆零分拣费 14整箱装卸费 15拆零装卸费 16 制单费 17 操作费）
     */
    private Integer feeType;
    /**
     * 计费结果
     */
    private BigDecimal calculateResult;
    /**
     * 计费备注
     */
    private String calculateRemark;
    /**
     * 计费过程结果(因为计费过程也是调用groovy生成，所以存在成功或者失败，成功或失败都往计费过程存放即可)
     */
    private String calculateProcessResult;
    /**
     * 业务类型(1城市配送2城际干线3城市仓储4仓配一体)
     */
    private Integer businessType;
    /**
     * 业务时间
     */
    private Date businessTime;
    /**
     * 账期(yyyy-MM)
     */
    private String billDate;
    /**
     * 业务开始时间(订单日期||配载日期(yyyy-MM-dd HH:mm:ss))
     */
    private Date beginTime;
    /**
     * 业务结束时间(签收日期||完成日期(yyyy-MM-dd HH:mm:ss))
     */
    private Date endTime;
    /**
     * 报价子合同id:pub_quoterule_detail.pk_id
     */
    private Integer ruleDetailPkId;
    /**
     * 报价子合同
     */
    private String ruleDetailName;
    /**
     * 计费规则主id:pub_quoterule.pk_id
     */
    private Integer ruleMainPkId;
    /**
     * 报价主合同
     */
    private String ruleMainName;
    /**
     * 单据类型(1:运输单,2:仓储单)
     */
    private Integer codeType;
    /**
     * 计费成功的单据id集合
     */
    private Set<Integer> codePkIds;
    /**
     * 涉及单据信息
     * key:单据自增id,Dict 按需取值，降低内存使用
     */
    private Map<Integer, Dict> codeDataMap;
    /**
     * 计费成功的明细单据id集合
     */
    private Set<Integer> codeDetailPkIds;
    private Map<Integer, Dict> codeDetailDataMap;
    /**
     * 计费成功的明细2单据id集合
     */
    private Set<Integer> codeDetail2PkIds;
    private Map<Integer, Dict> codeDetail2DataMap;
    /**
     * 机构id
     */
    private Integer companyId;
    /**
     * 仓库编码
     */
    private String warehouseCode;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 客户id
     */
    private Integer clientId;
    /**
     * 承运商id
     */
    private Integer carrierId;
    /**
     * 涉及单据数量
     */
    private Integer totalCodeNumber;
    /**
     * 涉及SKU数量
     */
    private Integer totalSkuNumber;
    /**
     * 总箱数
     */
    private BigDecimal totalBoxes;
    /**
     * 总件数
     */
    private BigDecimal totalNumber;
    /**
     * 总重量
     */
    private BigDecimal totalWeight;
    /**
     * 总体积
     */
    private BigDecimal totalVolume;
    /**
     * 总货值
     */
    private BigDecimal totalCargoValue;
    /**
     * 总超件数
     */
    private BigDecimal totalOverNum;
    /**
     * 总超店次
     */
    private Integer totalOverStoreNum;
    /**
     * 分摊类型(1:按单,2重量:按体积,3:按体积,4:总件数 取自bms_clientinfo.share_type)
     */
    private Integer shareType;
    /**
     * 额外结果类型(赋值逻辑查看GroovyCalculateExtraType枚举类,该字段和extraInfo字段配合使用)
     * 该值从groovy自动计费结果数据
     */
    private Integer extraType = GroovyCalculateExtraType.DEFAULT.getCode();
    /**
     * 额外结果信息
     * 自动计费根据业务会存在额外一些输出场景，所以做了额外计费的逻辑,做好了该约定后后续不同维度的计费都应遵循该规则进行实现拓展
     * 该值从groovy自动计费结果数据
     */
    private Map<String, Object> extraInfo = new HashMap<>();



}
