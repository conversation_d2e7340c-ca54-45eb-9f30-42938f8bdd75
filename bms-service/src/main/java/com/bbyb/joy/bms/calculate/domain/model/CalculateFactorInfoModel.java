package com.bbyb.joy.bms.calculate.domain.model;

import com.bbyb.joy.bms.calculate.domain.dto.rule.CalculateFactorInfoDto;
import com.bbyb.joy.bms.calculate.domain.dto.rule.CalculateRuleInfoDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 计费规则相关信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateFactorInfoModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联id(客户id||承运商id)
     */
    private Integer relationId;

    /**
     * 计费规则相关信息
     */
    private CalculateRuleInfoDto ruleInfo;

    /**
     * 计算因子相关信息
     * 当前规则下的
     */
    private List<CalculateFactorInfoDto> factors;


}
