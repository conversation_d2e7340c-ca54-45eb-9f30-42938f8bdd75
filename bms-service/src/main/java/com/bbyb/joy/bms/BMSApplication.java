package com.bbyb.joy.bms;

import org.springframework.boot.Banner.Mode;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, SecurityAutoConfiguration.class, ManagementWebSecurityAutoConfiguration.class}, scanBasePackages = {"com.bbyb.joy"})
@EnableTransactionManagement
@EnableAspectJAutoProxy
@EnableAsync
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.bbyb.joy"})
public class BMSApplication {
    public static void main(String[] args) {
        SpringApplication BMSApplication = new SpringApplication(BMSApplication.class);
        BMSApplication.setBannerMode(Mode.OFF);
        BMSApplication.run(args);
    }
}
