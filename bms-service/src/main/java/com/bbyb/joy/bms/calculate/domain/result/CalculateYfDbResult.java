package com.bbyb.joy.bms.calculate.domain.result;

import com.bbyb.bms.model.po.*;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 计费结果DB操作(应付)
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CalculateYfDbResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 涉及单据(pkId)
     */
    private List<Integer> codePkIds;
    /**
     * 计费成功单据id(pkId)
     */
    private List<Integer> successCodePkIds;
    /**
     * 计费失败单据id(pkId)
     */
    private List<Integer> failCodePkIds;
    /**
     * 单据计费记录
     */
    private List<BmsYfCostRecordPO> insertCostRecordPOs;
    /**
     * 费用
     */
    private List<BmsYfcostMainInfoPO> insertCostMainInfoPOs;
    /**
     * 结算费用
     */
    private List<BmsYfcostInfoPO> insertCostInfoPOs;
    /**
     * 费用中间
     */
    private List<BmsYfexpensesMiddlePO> insertMiddlePOs;
    /**
     * 费用分摊
     */
    private List<BmsYfexpensesMiddleSharePO> insertMiddleSharePOs;


}
