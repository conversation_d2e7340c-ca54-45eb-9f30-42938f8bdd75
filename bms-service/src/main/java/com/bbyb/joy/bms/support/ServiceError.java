package com.bbyb.joy.bms.support;

import com.bbyb.joy.enums.EnumValueSupport;
import lombok.Getter;

/**
 * classname: ServiceError
 * description: 服务错误信息
 */
@Getter
public enum ServiceError implements EnumValueSupport {

    /**
     * 成功
     */
    SUCCESS(200, "操作成功"),

    /**
     * 客户端错误
     */
    REQUEST_PARAMETER_ERROR(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "访问被拒绝"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    REQUEST_TIMEOUT(408, "请求超时"),

    /**
     * 服务端错误
     */
    TOKEN_ERROR(1005, "登录失效，请重新登陆"),
    PARAMETER_ERROR(500, "业务异常"),
    PARAMETER_LOSE_ERROR(501, "参数不全"),
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_ERROR(503, "服务错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),


    INTERFACE_SYNC_TRANS_ERROR(500, "运输单据同步异常"),
    INTERFACE_SYNC_STORAGE_ERROR(500, "仓储单据同步异常"),
    INTERFACE_SYNC_DISPATCH_ERROR(500, "调度单据同步异常"),




    /*
     * 计费相关
     */
    CALCULATE_NO_CODE(500,"根据查询条件未查询要计费单据信息"),
    CALCULATE_NO_RULE(500,"客户没有符合条件的报价合同信息,请检查客户合同以及合同下的分级数据"),

    ;


    /**
     * code
     */
    private final int code;
    /**
     * message
     */
    private final String message;

    private ServiceError(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int value() {
        return this.code;
    }

    @Override
    public String displayName() {
        return this.message;
    }

}
