//package com.bbyb.joy.bms.support.configuration.config;
//
//import com.bbyb.joy.bms.support.filter.trace.RequestAndResponseLogFilter;
//import com.bbyb.joy.bms.support.properties.RequestAndResponseLogProperties;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.stereotype.Component;
//
//import java.util.Collections;
//
//@Component
//@EnableConfigurationProperties
//public class WebFilterConfig {
//    @Autowired
//    private RequestAndResponseLogProperties requestAndResponseLogProperties;
//
//    public WebFilterConfig() {
//    }
//
//    @Bean
//    public FilterRegistrationBean logFilterRegistration() {
//        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
//        RequestAndResponseLogFilter logFilter = new RequestAndResponseLogFilter();
//        logFilter.setNeedLogRequest(this.requestAndResponseLogProperties.isNeedLogRequest());
//        logFilter.setNeedLogResponse(this.requestAndResponseLogProperties.isNeedLogResponse());
//        logFilter.setNeedLogHeader(this.requestAndResponseLogProperties.isNeedLogHeader());
//        logFilter.setNeedLogPayload(this.requestAndResponseLogProperties.isNeedLogPayload());
//        logFilter.setMaxPayloadLength(this.requestAndResponseLogProperties.getMaxPayloadLength());
//        registrationBean.setAsyncSupported(true); // 允许该过滤器支持异步请求
//        registrationBean.setFilter(logFilter);
//        registrationBean.setUrlPatterns(Collections.singletonList("/*"));
//        registrationBean.setName("requestAndResponseLogFilter");
//        registrationBean.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
//        return registrationBean;
//    }
//}
