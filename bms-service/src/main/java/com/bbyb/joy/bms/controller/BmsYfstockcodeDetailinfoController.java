package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.BmsYfstockcodeDetailinfo;
import com.bbyb.joy.bms.domain.dto.FileBaseDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYfstockcodeDetailinfoDto;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsYfstockcodeDetailinfoService;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.utils.ExcelUtil;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 应付出入库商品明细Controller
 *
 * <AUTHOR>
 */
@Api(tags = "应付出入库商品明细接口")
@RestController
@RequestMapping("/system/detailinfo")
public class BmsYfstockcodeDetailinfoController {
    @Resource
    private IBmsYfstockcodeDetailinfoService bmsYfstockcodeDetailinfoService;

    /**
     * 查询应付出入库商品明细列表
     */
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<BmsYfstockcodeDetailinfo>> list(@RequestBody(required = false) BmsYfstockcodeDetailinfo bmsYfstockcodeDetailinfo) {
        //获取token
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfstockcodeDetailinfoService.selectBmsYfstockcodeDetailinfoList(token, bmsYfstockcodeDetailinfo));
    }


    /**
     * 导出应付出入库商品明细列表
     */
    @Log(title = "应付出入库商品明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public ResponseResult<String> export(HttpServletRequest request, @RequestBody(required = false) BmsYfstockcodeDetailinfo bmsYfstockcodeDetailinfo) {
        //获取token
        String token = RequestContext.getToken();
        List<BmsYfstockcodeDetailinfo> list = bmsYfstockcodeDetailinfoService.selectBmsYfstockcodeDetailinfoList(token, bmsYfstockcodeDetailinfo).getRows();
        ExcelUtil<BmsYfstockcodeDetailinfo> util = new ExcelUtil<BmsYfstockcodeDetailinfo>(BmsYfstockcodeDetailinfo.class);
        FileBaseDto fileBaseDto = util.exportExcel(list, "应付出入库商品明细数据");
        return new ResponseResult<>(true, 0, fileBaseDto.getFileName(), fileBaseDto.getFileName());
    }

    /**
     * 获取应付出入库商品明细详细信息
     */
    //获取应付出入库商品明细详细信息
    //@PreAuthorize("@ss.hasPermi('system:detailinfo:query')")
    @PostMapping(value = "/{id}")
    public ResponseResult<BmsYfstockcodeDetailinfo> getInfo(HttpServletRequest request, @PathVariable("id") String id) {
        //获取token
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfstockcodeDetailinfoService.selectBmsYfstockcodeDetailinfoById(token, id));
    }

    /**
     * 新增应付出入库商品明细
     */
    @Log(title = "应付出入库商品明细", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public ResponseResult<Integer> add(@RequestBody(required = false) BmsYfstockcodeDetailinfo bmsYfstockcodeDetailinfo) {
        //获取token
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfstockcodeDetailinfoService.insertBmsYfstockcodeDetailinfo(token, bmsYfstockcodeDetailinfo));
    }

    /**
     * 修改应付出入库商品明细
     */
    @Log(title = "应付出入库商品明细", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public ResponseResult<Integer> edit(@RequestBody(required = false) BmsYfstockcodeDetailinfo bmsYfstockcodeDetailinfo) {
        //获取token
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfstockcodeDetailinfoService.updateBmsYfstockcodeDetailinfo(token, bmsYfstockcodeDetailinfo));
    }

    /**
     * 物理作废应付出入库商品明细
     */
    @Log(title = "应付出入库商品明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public ResponseResult<Integer> remove(@PathVariable String[] ids) {
        //获取token
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfstockcodeDetailinfoService.deleteBmsYfstockcodeDetailinfoByIds(token, ids));
    }

    /**
     * 逻辑作废/启用应付出入库商品明细
     */
    @Log(title = "应付出入库商品明细", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateStatus")
    public ResponseResult<Integer> updateStatus(@RequestBody(required = false) Map<String, Object> idsAndStatus) {
        //获取token
        String token = RequestContext.getToken();
        List<String> idsList = (ArrayList<String>) idsAndStatus.get("ids");
        String[] ids = idsList.toArray(new String[0]);
        String status = String.valueOf(idsAndStatus.get("status"));
        return new ResponseResult<>(bmsYfstockcodeDetailinfoService.updateBmsYfstockcodeDetailinfoStatusByIds(token, ids, status));
    }


    /**
     * 订单计费明细查询
     */
    @PostMapping("/selectOrderDetail")
    public ResponseResult<List<BmsYfstockcodeDetailinfoDto>> selectOrderDetail(@RequestBody(required = false) BmsYfstockcodeDetailinfo bmsYfbillcodeDetailinfo) {
        //获取token
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfstockcodeDetailinfoService.selectBmsYfstockcodeDetailinfoList2(token, bmsYfbillcodeDetailinfo));
    }

}
