package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.biz.BmsYfbillmainBiz;
import com.bbyb.joy.bms.domain.dto.BmsClaimsInfo;
import com.bbyb.joy.bms.domain.dto.PubYfFixedfee;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain;
import com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmainBean;
import com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmainImportBean;
import com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYfbillmainExport;
import com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto;
import com.bbyb.joy.bms.domain.dto.querybean.PubYfFixedfeeQueryBean;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.WarehouseInfo;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsYfbillmainService;
import com.bbyb.joy.bms.service.IBmsYsbillmainService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.exception.BusinessException;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应付账单主Controller
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "应付账单主接口")
@ApiModel(value = "应付账单主接口")
@RestController
@RequestMapping("/system/yfbillmain")
public class BmsYfbillmainController {

    @Resource
    private IBmsYfbillmainService bmsYfbillmainService;
    @Resource
    private BmsYfbillmainBiz bmsYfbillmainMapper;
    @Resource
    private IBmsYsbillmainService bmsYsbillmainService;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private UserRights userRights;

    /**
     * 合并/取消合并
     * @param map 多选的账单信息
     */
    @ApiModelProperty(value = "合并/取消合并")
    @Log(title = "修改应收单据主表", businessType = BusinessType.UPDATE)
    @PostMapping("/billConsolidation")
    @MenuAuthority(code = "应付账单管理-账单合并,应付账单管理-取消合并")
    public ResponseResult<String> billConsolidation(@RequestBody(required = false) Map<String, Object> map) {
        String token = RequestContext.getToken();
        ResponseResult<String> ajaxResult = new ResponseResult<>();
        ajaxResult = new ResponseResult<>(bmsYfbillmainService.billConsolidation(token, map));

        return ajaxResult;
    }


    @ApiModelProperty(value = "查询费用扩展信息 (调账)")
    @PostMapping("/getCostListBymainId")
    public ResponseResult<PagerDataBean<BmsYfcostExtendDto>> getCostListBymainId(@RequestBody(required = false) BmsYfcostExtendDto bmsYfcostExtendDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillmainService.getCostListBymainId(token, bmsYfcostExtendDto));
    }

    @ApiModelProperty(value = "查询费用扩展信息 (对账)")
    @PostMapping("/getCostList")
    public ResponseResult<PagerDataBean<BmsYfcostExtendDto>> getCostList(@RequestBody(required = false) BmsYfcostExtendDto bmsYfcostExtendDto) {
        String token = RequestContext.getToken();
        List<BmsYfcostExtendDto> list = new ArrayList<>();
        SysDataPack sysDataPack = bmsYfbillmainService.getsysDataPack(token);
        List<Carrierinfo> clientList = sysDataPack.getCarrierInfo();
        if (clientList.isEmpty()) {
            return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(bmsYfcostExtendDto.getPage(), bmsYfcostExtendDto.getSize())));
        }
        List<WarehouseInfo> warehouseInfos = sysDataPack.getWarehouseInfo();
        bmsYfcostExtendDto.setCarrierList(clientList.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toList()));
        bmsYfcostExtendDto.setWarehouseList(warehouseInfos.stream().map(WarehouseInfo::getWarehouseCode).collect(Collectors.toList()));
        bmsYfcostExtendDto.setCarrierList(clientList.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toList()));
        return new ResponseResult<>(bmsYfbillmainService.getCostList(token, bmsYfcostExtendDto));
    }


    @ApiModelProperty(value = "根据账单id 查询费用明细")
    @PostMapping(value = "/queryDocument/{id}/{billType}/{codeType}")
    public ResponseResult<PagerDataBean<BmsYfbillmainBean>> queryDocument(@PathVariable("id") String id, @PathVariable("billType") Integer billType, @PathVariable("codeType") String codeType) {
        String token = RequestContext.getToken();
        UserBean loginUserInfo = RequestContext.getUserInfo();
        BmsYfbillmain bmsYfbillmain = new BmsYfbillmain();
        bmsYfbillmain.setId(Long.valueOf(id));
        bmsYfbillmain.setBillType(billType);
        // 查询是否有子级
        List<BmsYfbillmain> sonList = bmsYfbillmainMapper.selectBmsYsbillmainByFatherid(loginUserInfo.getTenantid().toString(), bmsYfbillmain.getId().toString());
        List<String> ids = new ArrayList<>();
        List<String> codes = Arrays.asList(codeType.split(","));
        return new ResponseResult<>(bmsYfbillmainService.selectBillmainAndCostInfoList(token, bmsYfbillmain, codeType, sonList, ids, codes));
    }


    @ApiModelProperty(value = "根据账单id 查询费用明细")
    @PostMapping(value = "/queryDocumentGetSumAmt/{id}/{billType}/{codeType}")
    public ResponseResult<BigDecimal> queryDocumentGetSumAmt(@PathVariable("id") String id, @PathVariable("billType") Integer billType, @PathVariable("codeType") String codeType) {
        String token = RequestContext.getToken();
        BmsYfbillmain bmsYfbillmain = new BmsYfbillmain();
        bmsYfbillmain.setId(Long.valueOf(id));
        bmsYfbillmain.setBillType(billType);
        // 查询是否有子级
        List<BmsYfbillmain> sonList = bmsYfbillmainMapper.selectBmsYsbillmainByFatherid(RequestContext.getTenantId(), bmsYfbillmain.getId().toString());
        List<String> ids = new ArrayList<>();
        List<String> codes = Arrays.asList(codeType.split(","));
        return new ResponseResult<>(bmsYfbillmainService.selectBillmainAndCostInfoListGetSumAmt(token, bmsYfbillmain, codeType, sonList, ids, codes));
    }


    @ApiModelProperty(value = "获取应收账单子级账单信息")
    @PostMapping(value = "/getinfoByFatherId/{id}")
    public ResponseResult<List<BmsYfbillmain>> getInfoByFatherId(@PathVariable("id") String id) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillmainService.selectBmsYsbillmainByFatherid(token, id));
    }


    @ApiModelProperty(value = "应付账单 调账提交")
    @Log(title = "应收账单 调账", businessType = BusinessType.UPDATE)
    @PostMapping("/accountAdjustment")
    @MenuAuthority(code = "应付账单管理-调账")
    public ResponseResult<String> accountAdjustment(@RequestBody(required = false) Map<String, Object> map) {
        String token = RequestContext.getToken();
        if (!bmsYsbillmainService.accountLockingYn(token)) {
            return new ResponseResult<>(40005, "锁账后不能调账！");
        }
        return new ResponseResult<>(bmsYfbillmainService.accountAdjustment(token, map));
    }


    @ApiModelProperty(value = "应付账单管理-对账")
    @Log(title = "应付账单 对账", businessType = BusinessType.UPDATE)
    @PostMapping("/reconciliation")
    @MenuAuthority(code = "应付账单管理-对账")
    public ResponseResult<String> reconciliation(@RequestBody(required = false) Map<String, Object> map) {
        String token = RequestContext.getToken();
        ResponseResult<String> ajaxResult = new ResponseResult<>();
        if (!bmsYsbillmainService.accountLockingYn(token)) {
            return new ResponseResult<>(40005, "锁账后不能对账！");
        }
        try {
            String feeType = null != map.get("feeType") ? map.get("feeType").toString() : "3";
            //对账-增值费
            if (StrUtil.equals(feeType, "1")) {
                String updateListStr = JSONObject.toJSONString(map.get("updateList"));
                List<BmsPubAddedfeeDto> optList = JSON.parseArray(updateListStr, BmsPubAddedfeeDto.class);
                BmsPubAddedfeeDto optData = new BmsPubAddedfeeDto();
                optData.setBillId(Integer.valueOf(map.get("billId").toString()));
                optData.setCommitType(map.get("commitType").toString());
                optData.setUpdateList(optList);
                ajaxResult = new ResponseResult<>(bmsYfbillmainService.reconciliationByAddedFee(token, optData));
            } else if (StrUtil.equals(feeType, "2")) {
                // 对账--固定费
                String updateListStr = JSONObject.toJSONString(map.get("updateList"));
                List<PubYfFixedfee> pubYfFixedfees = JSON.parseArray(updateListStr, PubYfFixedfee.class);
                PubYfFixedfeeQueryBean optData = new PubYfFixedfeeQueryBean();
                optData.setBillId(Integer.valueOf(map.get("billId").toString()));
                optData.setCommitType(map.get("commitType").toString());
                optData.setUpdateList(pubYfFixedfees);
                ajaxResult = new ResponseResult<>(bmsYfbillmainService.reconciliationByFixedFee(token, optData));
            } else {
                ajaxResult = new ResponseResult<>(bmsYfbillmainService.reconciliation(token, map));
            }
        } catch (Exception e) {
            if(e instanceof BusinessException){
                throw new BusinessException(e.getMessage());
            }else if(e instanceof ValidateException){
                throw new BusinessException(e.getMessage());
            }else {
                log.info(e.getMessage());
                throw new BusinessException("对账失败!");
            }
        }
        return ajaxResult;
    }


    @ApiModelProperty(value = "应付账单 作废单据")
    @Log(title = "应收账单 作废单据", businessType = BusinessType.UPDATE)
    @PostMapping("/delCostInfo")
    public ResponseResult<String> delCostInfo(@RequestBody(required = false) Map<String, Object> map) {
        String token = RequestContext.getToken();
        ResponseResult<String> ajaxResult = new ResponseResult<>();
        if (!bmsYsbillmainService.accountLockingYn(token)) {
            return new ResponseResult<>(40005, "锁账后不能对账！");
        }
        ajaxResult = new ResponseResult<>(bmsYfbillmainService.delCostInfo(token, map));
        return ajaxResult;
    }


    @ApiModelProperty(value = "生成应付账单/批量生成应付账单")
    @PostMapping(value = "/generateBill")
    @MenuAuthority(code = "应付仓储计费-批量生成账单,应付仓储计费-按条件生成账单")
    public ResponseResult<String> generateBill(@RequestBody(required = false) Map<String, Object> map) {
        String token = RequestContext.getToken();
        if (!bmsYsbillmainService.accountLockingYn(token)) {
            return new ResponseResult<>(40005, "锁账后不能生成账单！");
        }
        return new ResponseResult<>(bmsYfbillmainService.generateBill(token, map));
    }


    @ApiModelProperty(value = "修改应付账单 提交/取消提交/审核/取消审核 的状态")
    @PostMapping(value = "/updateYfmainStatus")
    @MenuAuthority(code = "应付账单管理-提交,应付账单管理-取消提交,应付账单管理-账单审核,应付账单管理-取消审核")
    public ResponseResult<String> updateYfmainStatus(@RequestBody(required = false) Map<String, Object> map) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillmainService.updateYfmainStatus(token, map));
    }


    @ApiModelProperty(value = "查询应付账单主列表")
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<BmsYfbillmain>> list(@RequestBody(required = false) BmsYfbillmain bmsYfbillmain) {
        String token = RequestContext.getToken();

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CARRIER.getCode(), "carrierList", PermissionConfig.FieldType.LIST_INTEGER, "carrierCode"),
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.STRING, "id")
        );
        bmsYfbillmain = userRights.applyPermissions(bmsYfbillmain, permissionConfigs);

        return new ResponseResult<>(bmsYfbillmainService.selectBmsYfbillmainList(token, bmsYfbillmain));
    }


    @ApiModelProperty(value = "导出应付账单主列表")
    @Log(title = "应付账单主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @MenuAuthority(code = "应付账单管理-导出")
    public ResponseResult<String> export(@RequestBody(required = false) BmsYfbillmain bmsYfbillmain) {
        String token = RequestContext.getToken();
        SysDataPack sysDataPack = bmsYfbillmainService.getsysDataPack(token);
        List<Carrierinfo> clientList = sysDataPack.getCarrierInfo();
        List<WarehouseInfo> warehouseInfos = sysDataPack.getWarehouseInfo();
        if (clientList.isEmpty() || warehouseInfos.isEmpty()) {
            return new ResponseResult<>(40005, "你没有任何相关的承运商或仓库权限！");
        }
        if (!StrUtil.isEmpty(bmsYfbillmain.getCompanyIds())) {
            List<String> compIds = Arrays.asList(bmsYfbillmain.getCompanyIds().split(","));
            bmsYfbillmain.setCompanyIdList(compIds);
        } else {
            List<String> compIds = new ArrayList<>(Arrays.asList(textConversionUtil.getCompanyIdsArr()));
            bmsYfbillmain.setCompanyIdList(compIds);
        }
        List<BmsYfbillmainExport> list = bmsYfbillmainService.selectBmsYfbillmainExportList(token, bmsYfbillmain);
        if (CollUtil.isEmpty(list)) {
            return new ResponseResult<>(40005, "未查询到导出数据！");
        }
        Map<String, List<BmsYfbillmainExport>> billMap = list.stream()
                .filter(k -> StrUtil.isNotEmpty(k.getClientCode()))
                .collect(Collectors.groupingBy(BmsYfbillmainExport::getClientCode));
        String billName = "应付账单管理信息导出";
        if (billMap.size() == 1) {
            billName = list.get(0).getCarrierName() + "_" + list.get(0).getClientName() + "_应付账单列表_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        }
        return exportUtil.getOutClassNew(token, billName, "应付账单管理", BmsYfbillmainExport.class, userid -> {
            if (clientList.isEmpty()) {
                return null;
            }
            return bmsYfbillmainService.selectBmsYfbillmainExportList(token, bmsYfbillmain);
        });
    }


    @ApiModelProperty(value = "获取应付账单主详细信息")
    @PostMapping(value = "/{id}")
    public ResponseResult<BmsYfbillmain> getInfo(@PathVariable("id") String id) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillmainService.selectBmsYfbillmainById(token, id));
    }


    @ApiModelProperty(value = "新增应付账单主")
    @Log(title = "应付账单主", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public ResponseResult<Integer> add(@RequestBody(required = false) BmsYfbillmain bmsYfbillmain) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillmainService.insertBmsYfbillmain(token, bmsYfbillmain));
    }


    @ApiModelProperty(value = "修改应付账单主")
    @Log(title = "应付账单主", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public ResponseResult<Integer> edit(@RequestBody(required = false) BmsYfbillmain bmsYfbillmain) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillmainService.updateBmsYfbillmain(token, bmsYfbillmain));
    }


    @ApiModelProperty(value = "物理作废应付账单主")
    @Log(title = "应付账单作废(物理作废)", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public ResponseResult<Integer> remove(@PathVariable String[] ids) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillmainService.deleteBmsYfbillmainByIds(token, ids));
    }

    @ApiModelProperty(value = "批量作废应付账单")
    @Log(title = "应付账单作废", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateStatus")
    @MenuAuthority(code = "应付账单管理-作废账单")
    public ResponseResult<String> updateStatus(@RequestBody Map<String, Object> idsAndStatus) {
        String token = RequestContext.getToken();
        String str = JSONArray.toJSONString(idsAndStatus.get("bmsYfbillmain"));
        List<BmsYfbillmain> list = JSONArray.parseArray(str, BmsYfbillmain.class);
        List<String> idsList = new ArrayList<>();
        list.forEach(e -> {
            idsList.add(e.getId().toString());
        });
        return new ResponseResult<>(bmsYfbillmainService.updateBmsYfbillmainStatusByIds(token, idsList));
    }


    @ApiModelProperty(value = "导入账单")
    @Log(title = "导入账单", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/importBill", produces = "application/json;charset=UTF-8")
    @MenuAuthority(code = "应付账单管理-导入")
    public ResponseResult<String> importBill(@RequestBody(required = false) List<BmsYfbillmainImportBean> list) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillmainService.importBill(token, list));
    }


    @ApiModelProperty(value = "应付账单 对账提交")
    @Log(title = "应付理赔账单 对账", businessType = BusinessType.UPDATE)
    @PostMapping("/yfClaimsReconciliation")
    public ResponseResult<String> yfClaimsReconciliation(@RequestBody(required = false) Map<String, Object> map) {
        String token = RequestContext.getToken();
        ResponseResult<String> ajaxResult = new ResponseResult<>();
        if (!bmsYsbillmainService.accountLockingYn(token)) {
            return new ResponseResult<>(40005, "锁账后不能对账！");
        }
        String bmsYsbillClaims = JSONObject.toJSONString(map.get("bmsYfbillClaimsList"));
        List<BmsClaimsInfo> bmsYsbillClaimsList = JSON.parseArray(bmsYsbillClaims, BmsClaimsInfo.class);
        ajaxResult = new ResponseResult<>(bmsYfbillmainService.reconciliation(token, map));
        //commitType 1 保存  2保存并提交
        // ajaxResult = new ResponseResult<>(bmsYfbillmainService.claimsReconciliation(token, bmsYsbillClaimsList, map.get("billId").toString(), map.get("commitType").toString()));
        return ajaxResult;
    }


    @ApiModelProperty(value = "按账单id查询增值费用")
    @PostMapping("/add/fee/query/by/bill/{billId}")
    public ResponseResult<Map<String, Object>> queryAddFeeByBillId(@PathVariable("billId") Long billId) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillmainService.queryAddFeeByBillId(token, billId));
    }


    @ApiModelProperty(value = "查询增值费费用列表")
    @PostMapping("/queryAddFeeList/page")
    public ResponseResult<PagerDataBean<BmsPubAddedfeeDto>> queryAddFeeList(@RequestBody BmsYfbillmain bmsYfbillmain) {
        String token = RequestContext.getToken();
        int pageNum = Objects.nonNull(bmsYfbillmain.getPageNum()) ? bmsYfbillmain.getPageNum() : 1;
        int pageSize = Objects.nonNull(bmsYfbillmain.getPageSize()) ? bmsYfbillmain.getPageSize() : 20;
        PagerDataBean<BmsPubAddedfeeDto> tableDataInfo = new PagerDataBean<>();
        List<BmsPubAddedfeeDto> list = bmsYfbillmainService.queryAddFeeByBillIdList(token, bmsYfbillmain.getId());
        try {
            tableDataInfo.setPage(pageNum);
            tableDataInfo.setSize(pageSize);
            tableDataInfo.setPager(new PagerBean(pageNum, pageSize));
            tableDataInfo.setTotal(list.size());
            pageNum = pageNum <= 0 ? 1 : pageNum;
            list = list.stream()
                    .skip((long) (pageNum - 1) * pageSize)
                    .limit(pageSize)
                    .collect(Collectors.toList());
            tableDataInfo.setRows(list);
        } catch (Exception e) {
            tableDataInfo.setTotal(0);
            tableDataInfo.setRows(null);
            log.error("系统异常", e);
        }
        return new ResponseResult<>(tableDataInfo);
    }

    @PostMapping("/queryRule/{billId}")
    public ResponseResult<List<BmsPubAddedfeeDto>> selectBmsPubAddedfeefeeRuleByClinet(@PathVariable("billId") Long billId) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillmainService.selectBmsPubAddedfeefeeRuleByClinet(token, billId));
    }
}
