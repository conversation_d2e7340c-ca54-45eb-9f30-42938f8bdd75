package com.bbyb.joy.bms.calculate.domain.param;

import com.bbyb.joy.bms.calculate.domain.result.CalculateResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 计费结果
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CalculateDbParam {

    /**
     * 关联id(客户id||承运商id)
     */
    private Integer relationId;
    /**
     * 单计费结果
     */
    List<CalculateResult> orderResult = new ArrayList<>();
    /**
     * 天计费结果
     */
    List<CalculateResult> dayResult = new ArrayList<>();
    /**
     * 月计费结果
     */
    List<CalculateResult> monthResult = new ArrayList<>();


}
