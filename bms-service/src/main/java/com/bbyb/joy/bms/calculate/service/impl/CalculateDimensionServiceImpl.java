package com.bbyb.joy.bms.calculate.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bbyb.joy.bms.calculate.domain.dto.code.day.CalculateDayDetail2Dto;
import com.bbyb.joy.bms.calculate.domain.dto.code.day.CalculateDayDetailDto;
import com.bbyb.joy.bms.calculate.domain.dto.code.day.CalculateDayDto;
import com.bbyb.joy.bms.calculate.domain.dto.code.month.CalculateMonthDetail2Dto;
import com.bbyb.joy.bms.calculate.domain.dto.code.month.CalculateMonthDetailDto;
import com.bbyb.joy.bms.calculate.domain.dto.code.month.CalculateMonthDto;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDetail2Dto;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDetailDto;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderExpandDto;
import com.bbyb.joy.bms.calculate.domain.dto.rule.CalculateRuleInfoDto;
import com.bbyb.joy.bms.calculate.domain.model.CalculateFactorInfoModel;
import com.bbyb.joy.bms.calculate.domain.result.CalculateResult;
import com.bbyb.joy.bms.calculate.service.CalculateDimensionService;
import com.bbyb.joy.bms.calculate.utils.CalculateUtil;
import com.bbyb.joy.bms.calculate.utils.groovy.GroovyUtil;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyCalculateResult;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyMatchResult;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyProcessResult;
import com.bbyb.joy.bms.domain.enums.ExpensesDimensionEnum;
import com.bbyb.joy.bms.support.utils.cost.StatisticsFieldEnum;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CalculateDimensionServiceImpl extends CalculateDomensionAbstractService implements CalculateDimensionService {

    @Resource
    GroovyUtil groovyUtil;

    @Override
    public List<CalculateResult> calculateOrder(List<CalculateOrderExpandDto> codes, CalculateFactorInfoModel rule) {

        List<CalculateResult> result = new ArrayList<>();

        for (CalculateOrderExpandDto code : codes) {
            //匹配最优计算因子
            GroovyMatchResult matchResult = groovyUtil.matchBestFactor(code.getCode(), code.getDetail(), code.getDetail2(), rule.getFactors(), rule.getRuleInfo().getMatchingCode());
            log.info("自动计费匹配分级匹配结果-code【{}】:{}",code.getCode().getCode(), JSONUtil.toJsonStr(matchResult));
            if(!matchResult.isSuccess()){
                //匹配计算因子失败
                CalculateResult buildResultData = matchFailFillOrderResult(matchResult
                        , code
                        , rule.getRuleInfo());
                result.add(buildResultData);
            } else {
                //最优计算因子匹配成功
                Object factor = matchResult.getResult();

                GroovyCalculateResult calculateResult = groovyUtil.calculateFee(code.getCode(), code.getDetail(), code.getDetail2(), factor, rule.getRuleInfo().getCalculateCode());
                log.info("自动计费计费结果-code【{}】:{}",code.getCode().getCode(), JSONUtil.toJsonStr(calculateResult));
                //计费公式调用失败
                if(!calculateResult.isSuccess()){
                    CalculateResult buildResultData = calculateFailFillOrderResult(calculateResult
                            , code
                            , rule.getRuleInfo()
                    );
                    result.add(buildResultData);
                }else {
                    //计费公式调用成功

                    //计费过程调用
                    GroovyProcessResult calculateProcessResult = groovyUtil.calculateProcess(code.getCode(), code.getDetail(), code.getDetail2(), factor, rule.getRuleInfo().getProcessCode());
                    log.info("自动计费计费过程结果-code【{}】:{}",code.getCode().getCode(), JSONUtil.toJsonStr(calculateProcessResult));
                    CalculateResult buildResultData = calculateSuccessFillOrderResult(calculateResult
                            , calculateProcessResult
                            , code
                            , rule.getRuleInfo());
                    result.add(buildResultData);
                }

            }
        }
        return result;
    }




    @Override
    public List<CalculateResult> calculateDay(List<CalculateOrderExpandDto> codes, CalculateFactorInfoModel rule) {

        List<CalculateResult> result = new ArrayList<>();

        /*
         * process_before:批事前要做3步处理
         * 1、根据报价模版分组条件进行分组处理
         * 2、convert 批模式数据
         * 最终得到符合条件的批数据
         * key:按分组条件分组后的数据
         */
        Map<String,List<CalculateOrderExpandDto>> processBeforeOnePreMap = processBeforePreBatchOneData(codes,rule.getRuleInfo());

        //循环分组后的数据
        for (String key : processBeforeOnePreMap.keySet()) {

            List<CalculateOrderExpandDto> calculateOrderExpands = processBeforeOnePreMap.get(key);

            //符合条件转换为天数据
            CalculateDayDto calculateDayCode = convertCalculateDayData(calculateOrderExpands);

            //匹配最优计算因子
            GroovyMatchResult matchResult = groovyUtil.matchBestFactor(calculateDayCode, null, null, rule.getFactors(), rule.getRuleInfo().getMatchingCode());
            if(!matchResult.isSuccess()){
                //匹配计算因子失败
                CalculateResult buildResultData = matchFailFillDayResult(matchResult
                        , calculateDayCode
                        , rule.getRuleInfo());
                result.add(buildResultData);
            }else {
                //最优计算因子匹配成功
                Object factor = matchResult.getResult();

                GroovyCalculateResult calculateResult = groovyUtil.calculateFee(calculateDayCode, null, null, factor, rule.getRuleInfo().getCalculateCode());

                //计费公式调用失败
                if(!calculateResult.isSuccess()){
                    CalculateResult buildResultData = calculateFailFillDayResult(calculateResult
                            , calculateDayCode
                            , rule.getRuleInfo());
                    result.add(buildResultData);
                }else {
                    //计费公式调用成功

                    //计费过程调用
                    GroovyProcessResult calculateProcessResult = groovyUtil.calculateProcess(calculateDayCode, null, null, factor, rule.getRuleInfo().getProcessCode());
                    CalculateResult buildResultData = calculateSuccessFillDayResult(calculateResult
                            , calculateProcessResult
                            , calculateDayCode
                            , rule.getRuleInfo());
                    result.add(buildResultData);

                }
            }

        }
        return result;
    }


    /**
     * 批数据分组
     * @return 按分组后的数据处理
     */
    public static Map<String,List<CalculateOrderExpandDto>> processBeforePreBatchOneData(List<CalculateOrderExpandDto> codes,  CalculateRuleInfoDto ruleInfo) {

        Map<String,List<CalculateOrderExpandDto>> result = new HashMap<>();

        String[] groupTypeFields = CalculateUtil.generateFields(ruleInfo.getGroupRule(), ExpensesDimensionEnum.DAY_DIMENSION);
        result = codes.stream()
                .collect(Collectors.groupingBy(e->CalculateUtil.generateKey(e.getCode(),groupTypeFields)));
        return result;
    }


    /**
     * 批-转天参数
     * @return 天数据
     */
    public static CalculateDayDto convertCalculateDayData(List<CalculateOrderExpandDto> codes){

        CalculateDayDto result = new CalculateDayDto();

        //单据pkId集合
        Set<Integer> codePkIds = new HashSet<>();
        Map<Integer, Dict> codeDataMap = new HashMap<>();
        //单据明细pkId集合
        Set<Integer> codeDetailPkIds = new HashSet<>();
        Map<Integer, Dict> codeDetailDataMap = new HashMap<>();
        //单据明细2pkId集合
        Set<Integer> codeDetail2PkIds = new HashSet<>();
        Map<Integer, Dict> codeDetail2DataMap = new HashMap<>();
        //单据总数
        int codeNumber = 0;
        //明细一总数
        int detailNumber = 0;
        //明细一Sku总数
        int detailSkuNumber = 0;
        //明细二总数
        int detail2Number = 0;
        //总箱数
        BigDecimal totalBoxes = BigDecimal.ZERO;
        //总件数
        BigDecimal totalNumber = BigDecimal.ZERO;
        //总重量
        BigDecimal totalWeight = BigDecimal.ZERO;
        //总体积
        BigDecimal totalVolume = BigDecimal.ZERO;
        //总货值
        BigDecimal totalCargoValue = BigDecimal.ZERO;
        //总托数
        BigDecimal totalPalletNumber = BigDecimal.ZERO;
        //总超件数
        BigDecimal totalOverNum = BigDecimal.ZERO;
        //总票数
        BigDecimal totalVotes = BigDecimal.ZERO;

        //明细1-品温区维度汇总(key:温区(CW,LD,LC),value:该维度汇总信息)
        Map<String, CalculateDayDetailDto> temperatureDimensionDetailMap = new HashMap<>();
        Map<String, Set<String>> temperatureDimensionDetailLpnCodeMap = new HashMap<>();
        //明细1-品维度汇总(key:skuCode,value:该维度汇总信息)
        Map<String, CalculateDayDetailDto> skuDimensionDetailMap = new HashMap<>();
        //明细2-门店维度汇总(key:storeCode,value:该维度汇总信息)
        Map<String, CalculateDayDetail2Dto> storeDimensionDetailMap = new HashMap<>();
        //明细2-客户维度汇总(key:clientId,value:该维度汇总信息)
        Map<String,CalculateDayDetail2Dto> clientDimensionDetailMap = new HashMap<>();


        for (CalculateOrderExpandDto code : codes) {
            codeNumber++;

            // 注意:该模块内容已批单据的第一条赋值
            if(codeNumber == 1){
                result.setCodeType(code.getCodeType());
                result.setExpensesType(code.getCode().getCodeType()); // 这里是业务单据的单据类型
                result.setLineCode(code.getCode().getLineCode());
                result.setLineName(code.getCode().getLineName());
                result.setTmsLineCode(code.getCode().getTmsLineCode());
                result.setTmsLineName(code.getCode().getTmsLineName());
                result.setNetworkCode(code.getCode().getNetworkCode());
                result.setOriginatingProvince(code.getCode().getOriginatingProvince());
                result.setOriginatingCity(code.getCode().getOriginatingCity());
                result.setOriginatingArea(code.getCode().getOriginatingArea());
                result.setDestinationProvince(code.getCode().getDestinationProvince());
                result.setDestinationCity(code.getCode().getDestinationCity());
                result.setDestinationArea(code.getCode().getDestinationArea());
                result.setReceivingStoreCode(code.getCode().getReceivingStoreCode());
                result.setReceivingStoreName(code.getCode().getReceivingStoreName());
                result.setCompanyId(code.getCode().getCompanyId());
                result.setCompanyName(code.getCode().getCompanyName());
                result.setClientId(code.getCode().getClientId());
                result.setClientCode(code.getCode().getClientCode());
                result.setClientName(code.getCode().getClientName());
                result.setCarrierId(code.getCode().getCarrierId());
                result.setCarrierCode(code.getCode().getCarrierCode());
                result.setCarrierName(code.getCode().getCarrierName());
                result.setCompanyId(code.getCode().getCompanyId());
                result.setCompanyName(code.getCode().getCompanyName());
                result.setBusinessTime(code.getCode().getBusinessTime());
                result.setBusinessTimeMonth(code.getCode().getBusinessTimeMonth());
                result.setBusinessBeginTime(code.getCode().getBusinessBeginTime());
                result.setBusinessBeginDay(code.getCode().getBusinessBeginDay());
                result.setBusinessEndDay(code.getCode().getBusinessEndDay());
                result.setBusinessEndTime(code.getCode().getBusinessEndTime());
                result.setWarehouseCode(code.getCode().getWarehouseCode());
                result.setWarehouseName(code.getCode().getWarehouseName());
                result.setExpensesType(code.getCode().getExpenseType());
            }

            codePkIds.add(code.getCode().getPkId());
            codeDataMap.put(code.getCode().getPkId(),Dict.create()
                    .set(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName(), code.getCode().getTotalWeight())
                    .set(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName(), code.getCode().getTotalVolume())
                    .set(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName(), code.getCode().getTotalNumber()));
            if(CollUtil.isNotEmpty(code.getDetail())){
                Set<Integer> detailPkIds = code.getDetail().stream()
                        .map(CalculateOrderDetailDto::getPkId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                codeDetailPkIds.addAll(detailPkIds);

                Set<String> detailSkuNumberSet = code.getDetail().stream()
                        .map(CalculateOrderDetailDto::getSkuCode)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                detailSkuNumber = detailSkuNumberSet.size();

                for (CalculateOrderDetailDto detail : code.getDetail()) {
                    codeDetailDataMap.put(detail.getPkId(),Dict.create()
                            .set(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName(), detail.getTotalWeight())
                            .set(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName(), detail.getTotalVolume())
                            .set(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName(), detail.getTotalNumber()));
                }
                
            }
            if(CollUtil.isNotEmpty(code.getDetail2())){
                Set<Integer> detail2PkIds = code.getDetail2().stream()
                        .map(CalculateOrderDetail2Dto::getPkId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                codeDetail2PkIds.addAll(detail2PkIds);

                for (CalculateOrderDetail2Dto detail2 : code.getDetail2()) {
                    codeDetail2DataMap.put(detail2.getPkId(),Dict.create()
                            .set(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName(), detail2.getTotalWeight())
                            .set(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName(), detail2.getTotalVolume())
                            .set(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName(), detail2.getTotalNumber()));
                }

            }
            totalBoxes = totalBoxes.add(code.getCode().getTotalBoxes());
            totalNumber = totalNumber.add(code.getCode().getTotalNumber());
            totalWeight = totalWeight.add(code.getCode().getTotalWeight());
            totalVolume = totalVolume.add(code.getCode().getTotalVolume());
            totalPalletNumber =  totalPalletNumber.add(code.getCode().getTotalPalletNumber());
            totalCargoValue = totalCargoValue.add(code.getCode().getTotalCargoValue());
            totalOverNum = totalOverNum.add(code.getCode().getTotalOverNumber());
            totalVotes = totalVotes.add(code.getCode().getTotalVotes());

            if(CollUtil.isNotEmpty(code.getDetail())){
                detailNumber += code.getDetail().size();

                //明细
                for (CalculateOrderDetailDto detailDto : code.getDetail()) {

                    //品温区
                    if(StrUtil.isNotEmpty(detailDto.getTemperatureType())){

                        if(temperatureDimensionDetailMap.containsKey(detailDto.getTemperatureType())){
                            CalculateDayDetailDto calculateDayDetailDto = temperatureDimensionDetailMap.get(detailDto.getTemperatureType());
                            calculateDayDetailDto.setTotalBoxes(calculateDayDetailDto.getTotalBoxes().add(detailDto.getTotalBoxes()));
                            calculateDayDetailDto.setTotalNumber(calculateDayDetailDto.getTotalNumber().add(detailDto.getTotalNumber()));
                            calculateDayDetailDto.setTotalWeight(calculateDayDetailDto.getTotalWeight().add(detailDto.getTotalWeight()));
                            calculateDayDetailDto.setTotalVolume(calculateDayDetailDto.getTotalVolume().add(detailDto.getTotalVolume()));
                            temperatureDimensionDetailMap.put(detailDto.getTemperatureType(), calculateDayDetailDto);
                        }else {
                            CalculateDayDetailDto calculateDayDetailDto = new CalculateDayDetailDto();
                            calculateDayDetailDto.setTotalBoxes(calculateDayDetailDto.getTotalBoxes().add(detailDto.getTotalBoxes()));
                            calculateDayDetailDto.setTotalNumber(calculateDayDetailDto.getTotalNumber().add(detailDto.getTotalNumber()));
                            calculateDayDetailDto.setTotalWeight(calculateDayDetailDto.getTotalWeight().add(detailDto.getTotalWeight()));
                            calculateDayDetailDto.setTotalVolume(calculateDayDetailDto.getTotalVolume().add(detailDto.getTotalVolume()));
                            temperatureDimensionDetailMap.put(detailDto.getTemperatureType(), calculateDayDetailDto);
                        }

                        if(temperatureDimensionDetailLpnCodeMap.containsKey(detailDto.getTemperatureType())){
                            if(StrUtil.isNotEmpty(detailDto.getLpnCode())){
                                Set<String> lpnCodeSet = temperatureDimensionDetailLpnCodeMap.get(detailDto.getTemperatureType());
                                lpnCodeSet.add(detailDto.getLpnCode());
                                temperatureDimensionDetailLpnCodeMap.put(detailDto.getTemperatureType(), lpnCodeSet);
                            }
                        }else {
                            if(StrUtil.isNotEmpty(detailDto.getLpnCode())){
                                Set<String> lpnCodeSet = Sets.newHashSet(detailDto.getLpnCode());
                                temperatureDimensionDetailLpnCodeMap.put(detailDto.getTemperatureType(), lpnCodeSet);
                            }
                        }

                    }

                    //品sku
                    if(StrUtil.isNotEmpty(detailDto.getSkuCode())){
                        if(skuDimensionDetailMap.containsKey(detailDto.getSkuCode())){
                            CalculateDayDetailDto calculateDayDetailDto = skuDimensionDetailMap.get(detailDto.getTemperatureType());
                            calculateDayDetailDto.setTotalBoxes(calculateDayDetailDto.getTotalBoxes().add(detailDto.getTotalBoxes()));
                            calculateDayDetailDto.setTotalNumber(calculateDayDetailDto.getTotalNumber().add(detailDto.getTotalNumber()));
                            calculateDayDetailDto.setTotalWeight(calculateDayDetailDto.getTotalWeight().add(detailDto.getTotalWeight()));
                            calculateDayDetailDto.setTotalVolume(calculateDayDetailDto.getTotalVolume().add(detailDto.getTotalVolume()));
                            skuDimensionDetailMap.put(detailDto.getTemperatureType(), calculateDayDetailDto);
                        }else {
                            CalculateDayDetailDto calculateDayDetailDto = new CalculateDayDetailDto();
                            calculateDayDetailDto.setSkuCode(detailDto.getSkuCode());
                            calculateDayDetailDto.setSkuName(detailDto.getSkuName());
                            calculateDayDetailDto.setTotalBoxes(calculateDayDetailDto.getTotalBoxes().add(detailDto.getTotalBoxes()));
                            calculateDayDetailDto.setTotalNumber(calculateDayDetailDto.getTotalNumber().add(detailDto.getTotalNumber()));
                            calculateDayDetailDto.setTotalWeight(calculateDayDetailDto.getTotalWeight().add(detailDto.getTotalWeight()));
                            calculateDayDetailDto.setTotalVolume(calculateDayDetailDto.getTotalVolume().add(detailDto.getTotalVolume()));
                            skuDimensionDetailMap.put(detailDto.getTemperatureType(), calculateDayDetailDto);
                        }
                    }

                }

            }

            //明细2
            if(CollUtil.isNotEmpty(code.getDetail2())){
                detail2Number += code.getDetail2().size();

                for (CalculateOrderDetail2Dto detail2Dto : code.getDetail2()) {

                    //门店
                    if(StrUtil.isNotEmpty(detail2Dto.getReceivingStoreCode())){
                        if(storeDimensionDetailMap.containsKey(detail2Dto.getReceivingStoreCode())){
                            CalculateDayDetail2Dto calculateDayDetail2Dto = storeDimensionDetailMap.get(detail2Dto.getReceivingStoreCode());
                            calculateDayDetail2Dto.setTotalBoxes(calculateDayDetail2Dto.getTotalBoxes().add(detail2Dto.getTotalBoxes()));
                            calculateDayDetail2Dto.setTotalNumber(calculateDayDetail2Dto.getTotalNumber().add(detail2Dto.getTotalNumber()));
                            calculateDayDetail2Dto.setTotalWeight(calculateDayDetail2Dto.getTotalWeight().add(detail2Dto.getTotalWeight()));
                            calculateDayDetail2Dto.setTotalVolume(calculateDayDetail2Dto.getTotalVolume().add(detail2Dto.getTotalVolume()));
                            storeDimensionDetailMap.put(detail2Dto.getReceivingStoreCode(), calculateDayDetail2Dto);
                        }else {
                            CalculateDayDetail2Dto calculateDayDetail2Dto = new CalculateDayDetail2Dto();
                            calculateDayDetail2Dto.setReceivingStoreCode(detail2Dto.getReceivingStoreCode());
                            calculateDayDetail2Dto.setReceivingStoreName(detail2Dto.getReceivingStoreName());
                            calculateDayDetail2Dto.setTotalBoxes(calculateDayDetail2Dto.getTotalBoxes().add(detail2Dto.getTotalBoxes()));
                            calculateDayDetail2Dto.setTotalNumber(calculateDayDetail2Dto.getTotalNumber().add(detail2Dto.getTotalNumber()));
                            calculateDayDetail2Dto.setTotalWeight(calculateDayDetail2Dto.getTotalWeight().add(detail2Dto.getTotalWeight()));
                            calculateDayDetail2Dto.setTotalVolume(calculateDayDetail2Dto.getTotalVolume().add(detail2Dto.getTotalVolume()));
                            storeDimensionDetailMap.put(detail2Dto.getReceivingStoreCode(), calculateDayDetail2Dto);
                        }
                    }

                    //客户
                    if(StrUtil.isNotEmpty(detail2Dto.getClientCode())){
                        if(clientDimensionDetailMap.containsKey(detail2Dto.getClientCode())){
                            CalculateDayDetail2Dto calculateDayDetail2Dto = clientDimensionDetailMap.get(detail2Dto.getClientCode());
                            calculateDayDetail2Dto.setTotalBoxes(calculateDayDetail2Dto.getTotalBoxes().add(detail2Dto.getTotalBoxes()));
                            calculateDayDetail2Dto.setTotalNumber(calculateDayDetail2Dto.getTotalNumber().add(detail2Dto.getTotalNumber()));
                            calculateDayDetail2Dto.setTotalWeight(calculateDayDetail2Dto.getTotalWeight().add(detail2Dto.getTotalWeight()));
                            calculateDayDetail2Dto.setTotalVolume(calculateDayDetail2Dto.getTotalVolume().add(detail2Dto.getTotalVolume()));
                            clientDimensionDetailMap.put(detail2Dto.getClientCode(), calculateDayDetail2Dto);
                        }else {
                            CalculateDayDetail2Dto calculateDayDetail2Dto = new CalculateDayDetail2Dto();
                            calculateDayDetail2Dto.setClientCode(detail2Dto.getClientCode());
                            calculateDayDetail2Dto.setClientName(detail2Dto.getClientName());
                            calculateDayDetail2Dto.setTotalBoxes(calculateDayDetail2Dto.getTotalBoxes().add(detail2Dto.getTotalBoxes()));
                            calculateDayDetail2Dto.setTotalNumber(calculateDayDetail2Dto.getTotalNumber().add(detail2Dto.getTotalNumber()));
                            calculateDayDetail2Dto.setTotalWeight(calculateDayDetail2Dto.getTotalWeight().add(detail2Dto.getTotalWeight()));
                            calculateDayDetail2Dto.setTotalVolume(calculateDayDetail2Dto.getTotalVolume().add(detail2Dto.getTotalVolume()));
                            clientDimensionDetailMap.put(detail2Dto.getClientCode(), calculateDayDetail2Dto);
                        }
                    }

                }
            }

        }

        //温区维度rebuild
        if(!temperatureDimensionDetailMap.isEmpty()){
            Map<String, CalculateDayDetailDto> rebuildTemperatureDimensionDetailMap = new HashMap<>();
            temperatureDimensionDetailMap.forEach((key, value) -> {
                if(temperatureDimensionDetailLpnCodeMap.containsKey(key)){
                    Set<String> lpnCodeSet = temperatureDimensionDetailLpnCodeMap.get(key);
                    value.setDistinctTotalPalletNumber(lpnCodeSet.size());
                    rebuildTemperatureDimensionDetailMap.put(key,value);
                } else {
                    rebuildTemperatureDimensionDetailMap.put(key,value);
                }
            });
            temperatureDimensionDetailMap = rebuildTemperatureDimensionDetailMap;
        }

        result.setCodePkIds(codePkIds);
        result.setCodeDataMap(codeDataMap);
        result.setCodeDetailPkIds(codeDetailPkIds);
        result.setCodeDetailDataMap(codeDetailDataMap);
        result.setCodeDetail2PkIds(codeDetail2PkIds);
        result.setCodeDetail2DataMap(codeDetail2DataMap);
        result.setCodeNumber(codeNumber);
        result.setDetailNumber(detailNumber);
        result.setDetailSkuNumber(detailSkuNumber);
        result.setDetail2Number(detail2Number);
        result.setTotalBoxes(totalBoxes);
        result.setTotalNumber(totalNumber);
        result.setTotalWeight(totalWeight);
        result.setTotalVolume(totalVolume);
        result.setTotalPalletNumber(totalPalletNumber);
        result.setTotalCargoValue(totalCargoValue);
        result.setTotalOverNum(totalOverNum);
        result.setTotalVotes(totalVotes);
        result.setTemperatureDimensionDetailMap(temperatureDimensionDetailMap);
        result.setSkuDimensionDetailMap(skuDimensionDetailMap);
        result.setStoreDimensionDetailMap(storeDimensionDetailMap);
        result.setClientDimensionDetailMap(clientDimensionDetailMap);
        return result;
    }





    @Override
    public List<CalculateResult> calculateMonth(List<CalculateOrderExpandDto> codes, CalculateFactorInfoModel rule) {

        List<CalculateResult> result = new ArrayList<>();

        /*
         * process_before:批事前要做3步处理
         * 1、根据报价模版分组条件进行分组处理
         * 2、convert 批模式数据
         * 最终得到符合条件的批数据
         */
        Map<String,List<CalculateOrderExpandDto>> processBeforeOnePreMap = processBeforePreBatchOneData(codes,rule.getRuleInfo());

        //循环分组后的数据
        for (String key : processBeforeOnePreMap.keySet()) {

            List<CalculateOrderExpandDto> calculateOrderExpands = processBeforeOnePreMap.get(key);

            //符合条件转换为月数据
            CalculateMonthDto calculateCode = convertCalculateMonthData(calculateOrderExpands);

            //匹配最优计算因子
            GroovyMatchResult matchResult = groovyUtil.matchBestFactor(calculateCode, null, null, rule.getFactors(), rule.getRuleInfo().getMatchingCode());
            if(!matchResult.isSuccess()){
                //匹配计算因子失败
                CalculateResult buildResultData = matchFailFillMonthResult(matchResult
                        , calculateCode
                        , rule.getRuleInfo());
                result.add(buildResultData);
            }else {
                //最优计算因子匹配成功
                Object factor = matchResult.getResult();

                GroovyCalculateResult calculateResult = groovyUtil.calculateFee(calculateCode, null, null, factor, rule.getRuleInfo().getCalculateCode());

                //计费公式调用失败
                if(!calculateResult.isSuccess()){
                    CalculateResult buildResultData = matchFailFillMonthResult(matchResult
                            , calculateCode
                            , rule.getRuleInfo());
                    result.add(buildResultData);
                }else {
                    //计费公式调用成功

                    //计费过程调用
                    GroovyProcessResult calculateProcessResult = groovyUtil.calculateProcess(calculateCode, null, null, factor, rule.getRuleInfo().getProcessCode());
                    CalculateResult buildResultData = calculateSuccessFillMonthResult(calculateResult
                            , calculateProcessResult
                            , calculateCode
                            , rule.getRuleInfo());
                    result.add(buildResultData);
                }
            }
        }
        return result;
    }


    /**
     * 批-转月参数
     * @return 月数据
     */
    public static CalculateMonthDto convertCalculateMonthData(List<CalculateOrderExpandDto> codes){

        CalculateMonthDto result = new CalculateMonthDto();

        //单据pkId集合
        Set<Integer> codePkIds = new HashSet<>();
        Map<Integer, Dict> codeDataMap = new HashMap<>();
        //单据明细pkId集合
        Set<Integer> codeDetailPkIds = new HashSet<>();
        Map<Integer, Dict> codeDetailDataMap = new HashMap<>();
        //单据明细2pkId集合
        Set<Integer> codeDetail2PkIds = new HashSet<>();
        Map<Integer, Dict> codeDetail2DataMap = new HashMap<>();
        //单据总数
        int codeNumber = 0;
        //明细一总数
        int detailNumber = 0;
        //明细一SKU总数
        int detailSkuNumber = 0;
        //明细二总数
        int detail2Number = 0;
        //总箱数
        BigDecimal totalBoxes = BigDecimal.ZERO;
        //总件数
        BigDecimal totalNumber = BigDecimal.ZERO;
        //总重量
        BigDecimal totalWeight = BigDecimal.ZERO;
        //总体积
        BigDecimal totalVolume = BigDecimal.ZERO;
        //总货值
        BigDecimal totalCargoValue = BigDecimal.ZERO;
        //总超件数
        BigDecimal totalOverNum = BigDecimal.ZERO;
        //总票数
        BigDecimal totalVotes = BigDecimal.ZERO;

        //明细1-品温区维度汇总(key:温区(CW,LD,LC),value:该维度汇总信息)
        Map<String, CalculateMonthDetailDto> temperatureDimensionDetailMap = new HashMap<>();
        Map<String, Set<String>> temperatureDimensionDetailLpnCodeMap = new HashMap<>();
        //去重总托数
        Set<String> distinctLpnCodeSet = new HashSet<>();
        //明细1-品维度汇总(key:skuCode,value:该维度汇总信息)
        Map<String, CalculateMonthDetailDto> skuDimensionDetailMap = new HashMap<>();
        //明细2-门店维度汇总(key:storeCode,value:该维度汇总信息)
        Map<String, CalculateMonthDetail2Dto> storeDimensionDetailMap = new HashMap<>();
        //明细2-客户维度汇总(key:clientId,value:该维度汇总信息)
        Map<String,CalculateMonthDetail2Dto> clientDimensionDetailMap = new HashMap<>();


        for (CalculateOrderExpandDto code : codes) {
            codeNumber++;

            // 注意:该模块内容已批单据的第一条赋值
            if(codeNumber == 1){
                result.setCodeType(code.getCodeType());
                result.setExpensesType(code.getCode().getCodeType()); //这里是业务单据的单据类型维度
                result.setLineCode(code.getCode().getLineCode());
                result.setLineName(code.getCode().getLineName());
                result.setTmsLineCode(code.getCode().getTmsLineCode());
                result.setTmsLineName(code.getCode().getTmsLineName());
                result.setNetworkCode(code.getCode().getNetworkCode());
                result.setOriginatingProvince(code.getCode().getOriginatingProvince());
                result.setOriginatingCity(code.getCode().getOriginatingCity());
                result.setOriginatingArea(code.getCode().getOriginatingArea());
                result.setDestinationProvince(code.getCode().getDestinationProvince());
                result.setDestinationCity(code.getCode().getDestinationCity());
                result.setDestinationArea(code.getCode().getDestinationArea());
                result.setReceivingStoreCode(code.getCode().getReceivingStoreCode());
                result.setReceivingStoreName(code.getCode().getReceivingStoreName());
                result.setCompanyId(code.getCode().getCompanyId());
                result.setCompanyName(code.getCode().getCompanyName());
                result.setClientId(code.getCode().getClientId());
                result.setClientCode(code.getCode().getClientCode());
                result.setClientName(code.getCode().getClientName());
                result.setCarrierId(code.getCode().getCarrierId());
                result.setCarrierCode(code.getCode().getCarrierCode());
                result.setCarrierName(code.getCode().getCarrierName());
                result.setCompanyId(code.getCode().getCompanyId());
                result.setCompanyName(code.getCode().getCompanyName());
                result.setWarehouseCode(code.getCode().getWarehouseCode());
                result.setWarehouseName(code.getCode().getWarehouseName());
                result.setBusinessTime(code.getCode().getBusinessTime());
                result.setBusinessTimeMonth(code.getCode().getBusinessTimeMonth());
                result.setBusinessBeginTime(code.getCode().getBusinessBeginTime());
                result.setBusinessEndTime(code.getCode().getBusinessEndTime());
                result.setBusinessBeginMonth(code.getCode().getBusinessBeginMonth());
                result.setBusinessEndMonth(code.getCode().getBusinessEndMonth());
                result.setExpensesType(code.getCode().getExpenseType());
            }

            codePkIds.add(code.getCode().getPkId());
            codeDataMap.put(code.getCode().getPkId(),Dict.create()
                    .set(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName(), code.getCode().getTotalWeight())
                    .set(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName(), code.getCode().getTotalVolume())
                    .set(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName(), code.getCode().getTotalNumber()));
            if(CollUtil.isNotEmpty(code.getDetail())){
                Set<Integer> detailPkIds = code.getDetail().stream()
                        .map(CalculateOrderDetailDto::getPkId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                codeDetailPkIds.addAll(detailPkIds);

                Set<String> detailSkuNumberSet = code.getDetail().stream()
                        .map(CalculateOrderDetailDto::getSkuCode)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                detailSkuNumber = detailSkuNumberSet.size();

                for (CalculateOrderDetailDto detail : code.getDetail()) {
                    codeDetailDataMap.put(detail.getPkId(), Dict.create()
                            .set(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName(), detail.getTotalWeight())
                            .set(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName(), detail.getTotalVolume())
                            .set(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName(), detail.getTotalNumber()));
                }

            }
            if(CollUtil.isNotEmpty(code.getDetail2())){
                Set<Integer> detail2PkIds = code.getDetail2().stream()
                        .map(CalculateOrderDetail2Dto::getPkId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                codeDetail2PkIds.addAll(detail2PkIds);

                for (CalculateOrderDetail2Dto detail2 : code.getDetail2()) {
                    codeDetail2DataMap.put(detail2.getPkId(), Dict.create()
                            .set(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName(), detail2.getTotalWeight())
                            .set(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName(), detail2.getTotalVolume())
                            .set(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName(), detail2.getTotalNumber()));
                }
            }
            totalBoxes = totalBoxes.add(code.getCode().getTotalBoxes());
            totalNumber = totalNumber.add(code.getCode().getTotalNumber());
            totalWeight = totalWeight.add(code.getCode().getTotalWeight());
            totalVolume = totalVolume.add(code.getCode().getTotalVolume());
            totalCargoValue = totalCargoValue.add(code.getCode().getTotalCargoValue());
            totalOverNum = totalOverNum.add(code.getCode().getTotalOverNumber());
            totalVotes = totalVotes.add(code.getCode().getTotalVotes());

            if(CollUtil.isNotEmpty(code.getDetail())){
                detailNumber += code.getDetail().size();

                //明细
                for (CalculateOrderDetailDto detailDto : code.getDetail()) {

                    //品温区
                    if(StrUtil.isNotEmpty(detailDto.getTemperatureType())){
                        if(temperatureDimensionDetailMap.containsKey(detailDto.getTemperatureType())){
                            CalculateMonthDetailDto calculateDetailDto = temperatureDimensionDetailMap.get(detailDto.getTemperatureType());
                            calculateDetailDto.setTotalBoxes(calculateDetailDto.getTotalBoxes().add(detailDto.getTotalBoxes()));
                            calculateDetailDto.setTotalNumber(calculateDetailDto.getTotalNumber().add(detailDto.getTotalNumber()));
                            calculateDetailDto.setTotalWeight(calculateDetailDto.getTotalWeight().add(detailDto.getTotalWeight()));
                            calculateDetailDto.setTotalVolume(calculateDetailDto.getTotalVolume().add(detailDto.getTotalVolume()));
                            temperatureDimensionDetailMap.put(detailDto.getTemperatureType(), calculateDetailDto);
                        }else {
                            CalculateMonthDetailDto calculateDetailDto = new CalculateMonthDetailDto();
                            calculateDetailDto.setTotalBoxes(calculateDetailDto.getTotalBoxes().add(detailDto.getTotalBoxes()));
                            calculateDetailDto.setTotalNumber(calculateDetailDto.getTotalNumber().add(detailDto.getTotalNumber()));
                            calculateDetailDto.setTotalWeight(calculateDetailDto.getTotalWeight().add(detailDto.getTotalWeight()));
                            calculateDetailDto.setTotalVolume(calculateDetailDto.getTotalVolume().add(detailDto.getTotalVolume()));
                            temperatureDimensionDetailMap.put(detailDto.getTemperatureType(), calculateDetailDto);
                        }
                    }
                    // 托盘数累计
                    if(temperatureDimensionDetailLpnCodeMap.containsKey(detailDto.getTemperatureType())){
                        if(StrUtil.isNotEmpty(detailDto.getLpnCode())){
                            Set<String> lpnCodeSet = temperatureDimensionDetailLpnCodeMap.get(detailDto.getTemperatureType());
                            lpnCodeSet.add(detailDto.getLpnCode());
                            temperatureDimensionDetailLpnCodeMap.put(detailDto.getTemperatureType(), lpnCodeSet);
                        }
                    }else {
                        if(StrUtil.isNotEmpty(detailDto.getLpnCode())){
                            Set<String> lpnCodeSet = Sets.newHashSet(detailDto.getLpnCode());
                            temperatureDimensionDetailLpnCodeMap.put(detailDto.getTemperatureType(), lpnCodeSet);
                        }
                    }

                    //品sku
                    if(StrUtil.isNotEmpty(detailDto.getSkuCode())){
                        if(skuDimensionDetailMap.containsKey(detailDto.getSkuCode())){
                            CalculateMonthDetailDto calculateDetailDto = skuDimensionDetailMap.get(detailDto.getTemperatureType());
                            calculateDetailDto.setTotalBoxes(calculateDetailDto.getTotalBoxes().add(detailDto.getTotalBoxes()));
                            calculateDetailDto.setTotalNumber(calculateDetailDto.getTotalNumber().add(detailDto.getTotalNumber()));
                            calculateDetailDto.setTotalWeight(calculateDetailDto.getTotalWeight().add(detailDto.getTotalWeight()));
                            calculateDetailDto.setTotalVolume(calculateDetailDto.getTotalVolume().add(detailDto.getTotalVolume()));
                            skuDimensionDetailMap.put(detailDto.getTemperatureType(), calculateDetailDto);
                        }else {
                            CalculateMonthDetailDto calculateDetailDto = new CalculateMonthDetailDto();
                            calculateDetailDto.setSkuCode(detailDto.getSkuCode());
                            calculateDetailDto.setSkuName(detailDto.getSkuName());
                            calculateDetailDto.setTotalBoxes(calculateDetailDto.getTotalBoxes().add(detailDto.getTotalBoxes()));
                            calculateDetailDto.setTotalNumber(calculateDetailDto.getTotalNumber().add(detailDto.getTotalNumber()));
                            calculateDetailDto.setTotalWeight(calculateDetailDto.getTotalWeight().add(detailDto.getTotalWeight()));
                            calculateDetailDto.setTotalVolume(calculateDetailDto.getTotalVolume().add(detailDto.getTotalVolume()));
                            skuDimensionDetailMap.put(detailDto.getTemperatureType(), calculateDetailDto);
                        }
                    }

                }

            }

            //明细2
            if(CollUtil.isNotEmpty(code.getDetail2())){
                detail2Number += code.getDetail2().size();

                for (CalculateOrderDetail2Dto detail2Dto : code.getDetail2()) {

                    //门店
                    if(StrUtil.isNotEmpty(detail2Dto.getReceivingStoreCode())){
                        if(storeDimensionDetailMap.containsKey(detail2Dto.getReceivingStoreCode())){
                            CalculateMonthDetail2Dto calculateDetail2Dto = storeDimensionDetailMap.get(detail2Dto.getReceivingStoreCode());
                            calculateDetail2Dto.setTotalBoxes(calculateDetail2Dto.getTotalBoxes().add(detail2Dto.getTotalBoxes()));
                            calculateDetail2Dto.setTotalNumber(calculateDetail2Dto.getTotalNumber().add(detail2Dto.getTotalNumber()));
                            calculateDetail2Dto.setTotalWeight(calculateDetail2Dto.getTotalWeight().add(detail2Dto.getTotalWeight()));
                            calculateDetail2Dto.setTotalVolume(calculateDetail2Dto.getTotalVolume().add(detail2Dto.getTotalVolume()));
                            storeDimensionDetailMap.put(detail2Dto.getReceivingStoreCode(), calculateDetail2Dto);
                        }else {
                            CalculateMonthDetail2Dto calculateDetail2Dto = new CalculateMonthDetail2Dto();
                            calculateDetail2Dto.setClientCode(detail2Dto.getClientCode());
                            calculateDetail2Dto.setClientName(detail2Dto.getClientName());
                            calculateDetail2Dto.setReceivingStoreCode(detail2Dto.getReceivingStoreCode());
                            calculateDetail2Dto.setReceivingStoreName(detail2Dto.getReceivingStoreName());
                            calculateDetail2Dto.setTotalBoxes(calculateDetail2Dto.getTotalBoxes().add(detail2Dto.getTotalBoxes()));
                            calculateDetail2Dto.setTotalNumber(calculateDetail2Dto.getTotalNumber().add(detail2Dto.getTotalNumber()));
                            calculateDetail2Dto.setTotalWeight(calculateDetail2Dto.getTotalWeight().add(detail2Dto.getTotalWeight()));
                            calculateDetail2Dto.setTotalVolume(calculateDetail2Dto.getTotalVolume().add(detail2Dto.getTotalVolume()));
                            storeDimensionDetailMap.put(detail2Dto.getReceivingStoreCode(), calculateDetail2Dto);
                        }
                    }

                    //客户
                    if(StrUtil.isNotEmpty(detail2Dto.getClientCode())){
                        if(clientDimensionDetailMap.containsKey(detail2Dto.getClientCode())){
                            CalculateMonthDetail2Dto calculateDetail2Dto = clientDimensionDetailMap.get(detail2Dto.getClientCode());
                            calculateDetail2Dto.setTotalBoxes(calculateDetail2Dto.getTotalBoxes().add(detail2Dto.getTotalBoxes()));
                            calculateDetail2Dto.setTotalNumber(calculateDetail2Dto.getTotalNumber().add(detail2Dto.getTotalNumber()));
                            calculateDetail2Dto.setTotalWeight(calculateDetail2Dto.getTotalWeight().add(detail2Dto.getTotalWeight()));
                            calculateDetail2Dto.setTotalVolume(calculateDetail2Dto.getTotalVolume().add(detail2Dto.getTotalVolume()));
                            clientDimensionDetailMap.put(detail2Dto.getClientCode(), calculateDetail2Dto);
                        }else {
                            CalculateMonthDetail2Dto calculateDetail2Dto = new CalculateMonthDetail2Dto();
                            calculateDetail2Dto.setClientCode(detail2Dto.getClientCode());
                            calculateDetail2Dto.setClientName(detail2Dto.getClientName());
                            calculateDetail2Dto.setReceivingStoreCode(detail2Dto.getReceivingStoreCode());
                            calculateDetail2Dto.setReceivingStoreName(detail2Dto.getReceivingStoreName());
                            calculateDetail2Dto.setTotalBoxes(calculateDetail2Dto.getTotalBoxes().add(detail2Dto.getTotalBoxes()));
                            calculateDetail2Dto.setTotalNumber(calculateDetail2Dto.getTotalNumber().add(detail2Dto.getTotalNumber()));
                            calculateDetail2Dto.setTotalWeight(calculateDetail2Dto.getTotalWeight().add(detail2Dto.getTotalWeight()));
                            calculateDetail2Dto.setTotalVolume(calculateDetail2Dto.getTotalVolume().add(detail2Dto.getTotalVolume()));
                            clientDimensionDetailMap.put(detail2Dto.getClientCode(), calculateDetail2Dto);
                        }
                    }

                }
            }

        }

        //温区维度rebuild
        if(!temperatureDimensionDetailMap.isEmpty()){
            Map<String, CalculateMonthDetailDto> rebuildTemperatureDimensionDetailMap = new HashMap<>();
            temperatureDimensionDetailMap.forEach((key, value) -> {
                if(temperatureDimensionDetailLpnCodeMap.containsKey(key)){
                    Set<String> lpnCodeSet = temperatureDimensionDetailLpnCodeMap.get(key);
                    value.setDistinctTotalPalletNumber(lpnCodeSet.size());
                    rebuildTemperatureDimensionDetailMap.put(key,value);
                }else {
                    rebuildTemperatureDimensionDetailMap.put(key,value);
                }
            });
            temperatureDimensionDetailMap = rebuildTemperatureDimensionDetailMap;
        }

        result.setCodePkIds(codePkIds);
        result.setCodeDataMap(codeDataMap);
        result.setCodeDetailPkIds(codeDetailPkIds);
        result.setCodeDetailDataMap(codeDetailDataMap);
        result.setCodeDetail2PkIds(codeDetail2PkIds);
        result.setCodeDetail2DataMap(codeDetail2DataMap);
        result.setCodeNumber(codeNumber);
        result.setDetailNumber(detailNumber);
        result.setDetailSkuNumber(detailSkuNumber);
        result.setDetail2Number(detail2Number);
        result.setTotalBoxes(totalBoxes);
        result.setTotalNumber(totalNumber);
        result.setTotalWeight(totalWeight);
        result.setTotalVolume(totalVolume);
        result.setTotalCargoValue(totalCargoValue);
        result.setTotalOverNum(totalOverNum);
        result.setTotalVotes(totalVotes);
        result.setTemperatureDimensionDetailMap(temperatureDimensionDetailMap);
        result.setSkuDimensionDetailMap(skuDimensionDetailMap);
        result.setStoreDimensionDetailMap(storeDimensionDetailMap);
        result.setClientDimensionDetailMap(clientDimensionDetailMap);
        return result;
    }








}
