package com.bbyb.joy.bms.calculate.domain.result;

import com.bbyb.bms.model.po.BmsYsCostRecordPO;
import com.bbyb.bms.model.po.BmsYscostInfoPO;
import com.bbyb.bms.model.po.BmsYscostMainInfoPO;
import com.bbyb.bms.model.po.BmsYsexpensesMiddlePO;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 计费结果DB操作(应收)
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CalculateYsDbResult implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 涉及单据(pkId)
     */
    private List<Integer> codePkIds;
    /**
     * 计费成功单据id(pkId)
     */
    private List<Integer> successCodePkIds;
    /**
     * 计费失败单据id(pkId)
     */
    private List<Integer> failCodePkIds;
    /**
     * 单据计费记录
     */
    private List<BmsYsCostRecordPO> insertCostRecordPOs;
    /**
     * 费用
     */
    private List<BmsYscostMainInfoPO> insertCostMainInfoPOs;
    /**
     * 结算费用
     */
    private List<BmsYscostInfoPO> insertCostInfoPOs;
    /**
     * 费用中间
     */
    private List<BmsYsexpensesMiddlePO> insertMiddlePOs;



}
