package com.bbyb.joy.bms.calculate.domain.dto.code.month;

import com.bbyb.joy.bms.calculate.domain.dto.code.batch.CalculateBatchDetail2Dto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 月维度详情2实体
 * 该类的是单据进入计费的单据详情信息
 * 该类整合了BMS所有业务单据类型的通用字段
 * 后端拓展单据业务如果有新的单据业务属性计费需要就需要在该类进行拓展补充
 * ## 字段含义补充:
 * 1、(重要)所有业务属性字段不再使用type的code,全部要转译为对应的label,便于维护报价
 * 2、(重要)那基于1带来额外的补充,需要补充对应的业务属性转译的字典的维护源头(要确保一定准确)
 * 3、(重要)日期业务字段全部转移为字符串,groovy运行引用就不再需要处理时间
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateMonthDetail2Dto extends CalculateBatchDetail2Dto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户维度-客户编码
     */
    public String clientCode;
    /**
     * 客户维度-客户名称
     */
    public String clientName;
    /**
     * 门店维度-门店编码
     */
    public String receivingStoreCode;
    /**
     * 门店维度-门店名称
     */
    public String receivingStoreName;


}
