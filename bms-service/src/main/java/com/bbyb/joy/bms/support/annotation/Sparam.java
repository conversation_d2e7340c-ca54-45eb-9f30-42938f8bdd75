package com.bbyb.joy.bms.support.annotation;


/**
 * @classname: <PERSON><PERSON>
 * @description: 参数
 * @author: xzy
 * @date: 2021/12/02 19:41
 * @version: 1.0
 */

import java.lang.annotation.*;


@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.PARAMETER, ElementType.FIELD})
public @interface Sparam {
    //required
    boolean required() default false;

    //length
    int length() default 0;

    //code
    String code() default "100000";

    //msg
    String msg() default "参数异常";

    //min
    int min() default 0;

    //max
    int max() default 0;

    //regexp
    String regexp() default "";
}