package com.bbyb.joy.bms.calculate.service;

import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderExpandDto;
import com.bbyb.joy.bms.calculate.domain.model.CalculateFactorInfoModel;
import com.bbyb.joy.bms.calculate.domain.result.CalculateResult;

import java.util.List;

/**
 * 计费服务
 * 给到的单据信息维度 客户||承运商
 * 费用维度:按单+费用项+合单汇总
 * (只关注计费)
 */
public interface CalculateDimensionService {


    /**
     * 按单计费
     * @param codes 单据信息
     * @param rule 计费模版相关信息
     * 入参是到客户||承运商 维度的单据信息
     * 按单计费最终会牵扯到合并费用单的逻辑,按批计费的没有
     * @return 计费结果 key:codePkId,value:costInfo
     */
    List<CalculateResult> calculateOrder(List<CalculateOrderExpandDto> codes, CalculateFactorInfoModel rule);
    /**
     * 按日计费
     * @param codes 单据信息
     * @param rule 计费模版相关信息
     * 入参是到客户||承运商 维度的单据信息
     * @return 计费结果
     */
    List<CalculateResult> calculateDay(List<CalculateOrderExpandDto> codes, CalculateFactorInfoModel rule);
    /**
     * 按月计费
     * @param codes 单据信息
     * @param rule 计费模版相关信息
     * 入参是到客户||承运商 维度的单据信息
     * @return 计费结果
     */
    List<CalculateResult> calculateMonth(List<CalculateOrderExpandDto> codes, CalculateFactorInfoModel rule);

}
