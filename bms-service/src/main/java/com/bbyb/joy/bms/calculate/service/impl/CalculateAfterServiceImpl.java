package com.bbyb.joy.bms.calculate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import com.bbyb.bms.model.po.*;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.calculate.domain.param.CalculateDbParam;
import com.bbyb.joy.bms.calculate.domain.result.*;
import com.bbyb.joy.bms.calculate.service.CalculateAfterService;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyCalculateExtraType;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import com.bbyb.joy.bms.support.utils.DateUtils;
import com.bbyb.joy.bms.support.utils.cost.CostUtil;
import com.bbyb.joy.bms.support.utils.cost.StatisticsFieldEnum;
import com.bbyb.joy.bms.support.utils.getCode.CodeCategory;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.utils.utils.SnowFlowUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CalculateAfterServiceImpl extends CalculateAbstractAfterService implements CalculateAfterService {


    @Override
    public CalculateYsDbResult ysDbCalculate(CalculateDbParam param) {

        List<CalculateResult> orderResult = param.getOrderResult();
        List<CalculateResult> dayResult = param.getDayResult();
        List<CalculateResult> monthResult = param.getMonthResult();

        UserBean userInfo = RequestContext.getUserInfo();

        CompletableFuture<CalculateYsDbResult> orderFuture = ysDbOrderFuture(orderResult,userInfo);
        CompletableFuture<CalculateYsDbResult> dayFuture = ysDbDayFuture(dayResult,userInfo);
        CompletableFuture<CalculateYsDbResult> monthFuture = ysDbMonthFuture(monthResult,userInfo);

        List<CompletableFuture<CalculateYsDbResult>> futures = Arrays.asList(orderFuture, dayFuture, monthFuture);

        CompletableFuture<List<CalculateYsDbResult>> allResults = CompletableFuture.allOf(
                        futures.toArray(new CompletableFuture[0]))
                        .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList()));

        List<CalculateYsDbResult> results = allResults.join();

        //单日月数据整合
        Set<Integer> codePkIds = new HashSet<>();
        Set<Integer> successCodePkIds = new HashSet<>();
        Set<Integer> failCodePkIds = new HashSet<>();
        List<BmsYscostMainInfoPO> insertCostMainInfoPOs = new ArrayList<>();
        List<BmsYscostInfoPO> insertCostInfoPOs = new ArrayList<>();
        List<BmsYsexpensesMiddlePO> insertMiddlePOs = new ArrayList<>();
        List<BmsYsCostRecordPO> insertCostRecordPOs = new ArrayList<>();

        for (CalculateYsDbResult dbResult : results) {
            codePkIds.addAll(dbResult.getCodePkIds());
            successCodePkIds.addAll(dbResult.getSuccessCodePkIds());
            failCodePkIds.addAll(dbResult.getFailCodePkIds());
            insertCostMainInfoPOs.addAll(dbResult.getInsertCostMainInfoPOs());
            insertCostInfoPOs.addAll(dbResult.getInsertCostInfoPOs());
            insertMiddlePOs.addAll(dbResult.getInsertMiddlePOs());
            insertCostRecordPOs.addAll(dbResult.getInsertCostRecordPOs());
        }
        //单据计费成功的要从计费失败中去掉
        failCodePkIds = failCodePkIds.stream()
                .filter(k->!successCodePkIds.contains(k))
                .collect(Collectors.toSet());

        return CalculateYsDbResult.builder()
                .codePkIds(Lists.newArrayList(codePkIds))
                .successCodePkIds(Lists.newArrayList(successCodePkIds))
                .failCodePkIds(Lists.newArrayList(failCodePkIds))
                .insertCostMainInfoPOs(insertCostMainInfoPOs)
                .insertCostInfoPOs(insertCostInfoPOs)
                .insertMiddlePOs(insertMiddlePOs)
                .insertCostRecordPOs(insertCostRecordPOs)
                .build();
    }


    /**
     * 应收单维度DB数据整合
     * @param param 单维度计费结果
     * @return 单维度所有的DB结果
     */
    @Async("asyncTaskExecutor")
    public CompletableFuture<CalculateYsDbResult> ysDbOrderFuture(List<CalculateResult> param, UserBean userInfo){

        return CompletableFuture.supplyAsync(()->{

            String tenantId = userInfo.getTenantid().toString();
            Date now = new Date();

            Set<Integer> codePkIds = new HashSet<>();
            Set<Integer> successCodePkIds = new HashSet<>();
            Set<Integer> failCodePkIds = new HashSet<>();
            //key:codePkId,value:records
            Map<Integer, List<BmsYsCostRecordPO>> recordMap = new HashMap<>();
            Map<Integer, List<BmsYscostMainInfoPO>> insertMainMap = new HashMap<>();
            Map<Integer, List<BmsYscostInfoPO>> insertCostMap = new HashMap<>();
            Map<Integer, List<BmsYsexpensesMiddlePO>> insertMiddleMap = new HashMap<>();


            //单维度converDB
            for (CalculateResult calculateResult : param) {

                CalculateCodeResult codeResult = calculateResult.getCodeResult();
                CalculateCostResult costResult = calculateResult.getCostResult();
                String mainExpenseCode = codeUtils.getCode(CodeCategory.YSMainExpenseCode, tenantId);
                String expenseCode = codeUtils.getCode(CodeCategory.YSExpensesCode, tenantId);

                //单据相关
                Integer optCodePkId = null;
                Boolean optCostFlag = null;
                if(!codeResult.getSuccessCodePkIds().isEmpty()){
                    Integer codePkId = codeResult.getSuccessCodePkIds().iterator().next();
                    optCodePkId = codePkId;
                    successCodePkIds.add(codePkId);
                    codePkIds.add(codePkId);
                    optCostFlag = true;
                }
                if(!codeResult.getFailCodePkIds().isEmpty()){
                    Integer codePkId = codeResult.getFailCodePkIds().iterator().next();
                    optCodePkId = codePkId;
                    codePkIds.add(codePkId);
                    failCodePkIds.add(codePkId);
                    optCostFlag = false;
                }

                //费用相关
                String costMainId = SnowFlowUtils.nextId();
                if(costResult!=null){
                    BmsYscostMainInfoPO buildMainCostInfo = new BmsYscostMainInfoPO();
                    buildMainCostInfo.setId(costMainId);
                    buildMainCostInfo.setExpensesCode(mainExpenseCode);
                    buildMainCostInfo.setExpensesType(costResult.getExpenseType());
                    buildMainCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
                    buildMainCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
                    buildMainCostInfo.setBusinessType(costResult.getBusinessType());
                    buildMainCostInfo.setBusinessTime(costResult.getBusinessTime());
                    buildMainCostInfo.setOrderDate(costResult.getBeginTime());
                    buildMainCostInfo.setSigningDate(costResult.getEndTime());
                    buildMainCostInfo.setClientId(costResult.getClientId());
                    buildMainCostInfo.setCompanyId(costResult.getCompanyId());
                    buildMainCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
                    buildMainCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
                    buildMainCostInfo.setTotalBoxes(costResult.getTotalBoxes());
                    buildMainCostInfo.setTotalNumber(costResult.getTotalNumber());
                    buildMainCostInfo.setTotalWeight(costResult.getTotalWeight());
                    buildMainCostInfo.setTotalVolume(costResult.getTotalVolume());
                    buildMainCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
                    buildMainCostInfo.setTotalOverNum(costResult.getTotalOverNum());
                    buildMainCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
                    buildMainCostInfo.setSettleType(costResult.getSettleType());
                    buildMainCostInfo.setSettleEntityType(costResult.getSettleEntityType());
                    buildMainCostInfo.setSettleSetting(costResult.getSettleSetting());
                    buildMainCostInfo.setSettleMainId(costResult.getSettleMainId());
                    buildMainCostInfo.setSettleAmount(costResult.getCalculateResult());
                    buildMainCostInfo = CostUtil.setCostField(buildMainCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
                    buildMainCostInfo.setCreateBy(userInfo.getEmployeename());
                    buildMainCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
                    buildMainCostInfo.setCreateTime(now);
                    buildMainCostInfo.setOperBy(userInfo.getEmployeename());
                    buildMainCostInfo.setOperCode(userInfo.getEmployeecode().toString());
                    buildMainCostInfo.setOperTime(now);
                    buildMainCostInfo.setOptMonth(DateUtils.optMonth());
                    buildMainCostInfo.setOptDay(DateUtils.optDay());
                    if(insertMainMap.containsKey(optCodePkId)){
                        List<BmsYscostMainInfoPO> mainInfoPOS = insertMainMap.get(optCodePkId);
                        mainInfoPOS.add(buildMainCostInfo);
                        insertMainMap.put(optCodePkId,mainInfoPOS);
                    }else {
                        insertMainMap.put(optCodePkId, Lists.newArrayList(buildMainCostInfo));
                    }

                    String costId = SnowFlowUtils.nextId();
                    BmsYscostInfoPO buildCostInfo = new BmsYscostInfoPO();
                    buildCostInfo.setId(costId);
                    buildCostInfo.setExpensesCode(expenseCode);
                    buildCostInfo.setMainCodeId(costMainId);
                    buildCostInfo.setExpensesType(costResult.getExpenseType());
                    buildCostInfo.setExpensesDimension(costResult.getExpensesDimension());
                    buildCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
                    buildCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
                    buildCostInfo.setBusinessTime(costResult.getBusinessTime());
                    buildCostInfo.setBillDate(costResult.getBillDate());
                    buildCostInfo.setOrderDate(costResult.getBeginTime());
                    buildCostInfo.setSigningDate(costResult.getEndTime());
                    buildCostInfo.setClientId(costResult.getClientId());
                    buildCostInfo.setCompanyId(costResult.getCompanyId());
                    buildCostInfo.setWarehouseCode(costResult.getWarehouseCode());
                    buildCostInfo.setWarehouseName(costResult.getWarehouseName());
                    buildCostInfo.setQuoterRuleId(costResult.getRuleMainPkId().toString());
                    buildCostInfo.setQuoterRuleName(costResult.getRuleMainName());
                    buildCostInfo.setQuoterRuleDetailId(costResult.getRuleDetailPkId().toString());
                    buildCostInfo.setQuoterRuleDetailName(costResult.getRuleDetailName());
                    buildCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
                    buildCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
                    buildCostInfo.setTotalBoxes(costResult.getTotalBoxes());
                    buildCostInfo.setTotalNumber(costResult.getTotalNumber());
                    buildCostInfo.setTotalWeight(costResult.getTotalWeight());
                    buildCostInfo.setTotalVolume(costResult.getTotalVolume());
                    buildCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
                    buildCostInfo.setTotalOverNum(costResult.getTotalOverNum());
                    buildCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
                    buildCostInfo.setSettleType(costResult.getSettleType());
                    buildCostInfo.setSettleEntityType(costResult.getSettleEntityType());
                    buildCostInfo.setSettleSetting(costResult.getSettleSetting());
                    buildCostInfo.setSettleMainId(costResult.getSettleMainId());
                    buildCostInfo.setSettleAmount(costResult.getCalculateResult());
                    buildCostInfo = CostUtil.setCostField(buildCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
                    buildCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
                    buildCostInfo.setCreateBy(userInfo.getEmployeecode().toString());
                    buildCostInfo.setCreateTime(now);
                    buildCostInfo.setOperBy(userInfo.getEmployeecode().toString());
                    buildCostInfo.setOperCode(userInfo.getEmployeecode().toString());
                    buildCostInfo.setOperTime(now);
                    buildCostInfo.setOptMonth(DateUtils.optMonth());
                    buildCostInfo.setOptDay(DateUtils.optDay());
                    if(insertCostMap.containsKey(optCodePkId)){
                        List<BmsYscostInfoPO> bmsYscostInfoPOS = insertCostMap.get(optCodePkId);
                        bmsYscostInfoPOS.add(buildCostInfo);
                        insertCostMap.put(optCodePkId,bmsYscostInfoPOS);
                    }else {
                        insertCostMap.put(optCodePkId,Lists.newArrayList(buildCostInfo));
                    }

                    Map<Integer, Dict> codeDataMap = costResult.getCodeDataMap();

                    BmsYsexpensesMiddlePO buildCostMiddle = new BmsYsexpensesMiddlePO();
                    buildCostMiddle.setCodePkId(optCodePkId);
                    buildCostMiddle.setCodeType(costResult.getCodeType());
                    buildCostMiddle.setMainCodeId(costMainId);
                    buildCostMiddle.setShareType(costResult.getShareType());
                    buildCostMiddle = CostUtil.setCostField(buildCostMiddle, costResult.getFeeType(), costResult.getCalculateResult());
                    buildCostMiddle.setSettleAmount(costResult.getCalculateResult());
                    buildCostMiddle.setOptMonth(DateUtils.optMonth());
                    buildCostMiddle.setOptDay(DateUtils.optDay());
                    if(codeDataMap.containsKey(optCodePkId)){
                        Dict dict = codeDataMap.get(optCodePkId);
                        BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()):BigDecimal.ZERO;
                        BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
                        BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
                        buildCostMiddle.setTotalWeight(totalWeight);
                        buildCostMiddle.setTotalVolume(totalVolume);
                        buildCostMiddle.setTotalNumber(totalNumber);
                    }
                    if(insertMiddleMap.containsKey(optCodePkId)){
                        List<BmsYsexpensesMiddlePO> middlePOList = insertMiddleMap.get(optCodePkId);
                        middlePOList.add(buildCostMiddle);
                        insertMiddleMap.put(optCodePkId,middlePOList);
                    }else {
                        insertMiddleMap.put(optCodePkId,Lists.newArrayList(buildCostMiddle));
                    }
                }

                if(optCodePkId != null){
                    BmsYsCostRecordPO buildRecord = new BmsYsCostRecordPO();
                    buildRecord.setCodePkId(optCodePkId);
                    buildRecord.setCodeType(codeResult.getCodeType());
                    buildRecord.setMainCodeId(optCostFlag ? costMainId : "");
                    buildRecord.setExpensesCode(optCostFlag ? mainExpenseCode : "");
                    buildRecord.setCalculateFlag(optCostFlag? BmsConstants.CALCULATE_FLAG_0 : BmsConstants.CALCULATE_FLAG_1);
                    buildRecord.setCalculateResult(codeResult.getCostResult().getCalculateResult());
                    buildRecord.setCalculateRemark(codeResult.getCostResult().getCalculateRemark());
                    buildRecord.setCalculateProcessResult(codeResult.getCostResult().getCalculateProcessResult());
                    buildRecord.setExpensesDimension(codeResult.getCostResult().getExpensesDimension());
                    buildRecord.setExpensesDimensionName(codeResult.getCostResult().getExpensesDimensionName());
                    buildRecord.setRuleDetailPkId(codeResult.getCostResult().getRuleDetailPkId());
                    buildRecord.setRuleDetailName(codeResult.getCostResult().getRuleDetailName());
                    buildRecord.setRuleMainPkId(codeResult.getCostResult().getRuleMainPkId());
                    buildRecord.setRuleMainName(codeResult.getCostResult().getRuleMainName());
                    buildRecord.setOperCode(userInfo.getEmployeecode().toString());
                    buildRecord.setOperBy(userInfo.getEmployeename());
                    buildRecord.setOperTime(now);
                    buildRecord.setOperDeptId(userInfo.getDepartmenid().intValue());
                    buildRecord.setDelFlag(BmsConstants.DEL_FLAG_0);
                    buildRecord.setOptMonth(DateUtils.optMonth());
                    buildRecord.setOptDay(DateUtils.optDay());
                    if(recordMap.containsKey(optCodePkId)){
                        List<BmsYsCostRecordPO> bmsYsCostRecordPOS = recordMap.get(optCodePkId);
                        bmsYsCostRecordPOS.add(buildRecord);
                        recordMap.put(optCodePkId,bmsYsCostRecordPOS);
                    }else {
                        recordMap.put(optCodePkId, Lists.newArrayList(buildRecord));
                    }
                }

            }

            //费用合并(按单计费需要合并)
            //单据计费成功的要从计费失败中去掉
            failCodePkIds = failCodePkIds.stream()
                    .filter(k->!successCodePkIds.contains(k))
                    .collect(Collectors.toSet());

            List<BmsYsCostRecordPO> insertCostRecordPOS = Lists.newArrayList();
            List<BmsYscostMainInfoPO> insertCostMainInfoPOS = Lists.newArrayList();
            List<BmsYscostInfoPO> insertCostInfoPOS = Lists.newArrayList();
            List<BmsYsexpensesMiddlePO> insertMiddlePOS = Lists.newArrayList();

            for (Integer key : insertMainMap.keySet()) {

                List<BmsYscostMainInfoPO> insertMainCosts = insertMainMap.get(key);
                List<BmsYscostInfoPO> insertCosts = insertCostMap.get(key);
                List<BmsYsexpensesMiddlePO> insertMiddles = insertMiddleMap.get(key);

                //大于1条就要合并
                if(insertMainCosts.size()>1){

                    //合并除了费用信息其它数据一致
                    String costMainId = SnowFlowUtils.nextId();
                    String mainExpenseCode = codeUtils.getCode(CodeCategory.YSMainExpenseCode, tenantId);
                    String costId = SnowFlowUtils.nextId();
                    String expenseCode = codeUtils.getCode(CodeCategory.YSExpensesCode, tenantId);

                    BmsYscostMainInfoPO buildMainCost = BeanUtil.toBean(insertMainCosts.get(0), BmsYscostMainInfoPO.class);
                    buildMainCost = CostUtil.aggregateCost(buildMainCost, insertCosts, BmsYscostMainInfoPO.class, CostUtil.YS_COST_SETTLE_SUM_FIELDS);
                    buildMainCost.setId(costMainId);
                    buildMainCost.setExpensesCode(mainExpenseCode);
                    insertCostMainInfoPOS.add(buildMainCost);

                    BmsYscostInfoPO buildCost = BeanUtil.toBean(insertCosts.get(0), BmsYscostInfoPO.class);
                    buildCost = CostUtil.aggregateCost(buildCost, insertCosts, BmsYscostInfoPO.class, CostUtil.YS_COST_SETTLE_SUM_FIELDS);
                    buildCost.setId(costId);
                    buildCost.setExpensesCode(expenseCode);
                    buildCost.setMainCodeId(costMainId);
                    insertCostInfoPOS.add(buildCost);

                    BmsYsexpensesMiddlePO buildMiddle = BeanUtil.toBean(insertMiddles.get(0), BmsYsexpensesMiddlePO.class);
                    buildMiddle = CostUtil.aggregateCost(buildMiddle, insertCosts, BmsYsexpensesMiddlePO.class, CostUtil.YS_COST_SETTLE_SUM_FIELDS);
                    buildMiddle.setMainCodeId(costMainId);
                    insertMiddlePOS.add(buildMiddle);


                }else {
                    insertCostMainInfoPOS.addAll(insertMainCosts);
                    insertCostInfoPOS.addAll(insertCosts);
                    insertMiddlePOS.addAll(insertMiddles);
                }

            }

            // 单据计费记录
            if(!recordMap.isEmpty()){
                insertCostRecordPOS = recordMap.values().stream()
                        .flatMap(List::stream).toList();
            }

            return CalculateYsDbResult.builder()
                    .codePkIds(Lists.newArrayList(codePkIds))
                    .successCodePkIds(Lists.newArrayList(successCodePkIds))
                    .failCodePkIds(Lists.newArrayList(failCodePkIds))
                    .insertCostMainInfoPOs(insertCostMainInfoPOS)
                    .insertCostInfoPOs(insertCostInfoPOS)
                    .insertMiddlePOs(insertMiddlePOS)
                    .insertCostRecordPOs(insertCostRecordPOS)
                    .build();
        });
    }
    /**
     * 应收天维度DB数据整合
     * @param param 单维度计费结果
     * @return 天维度所有的DB结果
     */
    @Async("asyncTaskExecutor")
    public CompletableFuture<CalculateYsDbResult> ysDbDayFuture(List<CalculateResult> param, UserBean userInfo){

        return CompletableFuture.supplyAsync(()->{

            String tenantId = userInfo.getTenantid().toString();
            Date now = new Date();

            Set<Integer> codePkIds = new HashSet<>();
            Set<Integer> successCodePkIds = new HashSet<>();
            Set<Integer> failCodePkIds = new HashSet<>();

            List<BmsYsCostRecordPO> insertCostRecordPOS = Lists.newArrayList();
            List<BmsYscostMainInfoPO> insertCostMainInfoPOS = Lists.newArrayList();
            List<BmsYscostInfoPO> insertCostInfoPOS = Lists.newArrayList();
            List<BmsYsexpensesMiddlePO> insertMiddlePOS = Lists.newArrayList();

            for (CalculateResult calculateResult : param) {

                CalculateCodeResult codeResult = calculateResult.getCodeResult();
                CalculateCostResult costResult = calculateResult.getCostResult();

                //单据相关
                Boolean optCostFlag = null;
                if(!codeResult.getSuccessCodePkIds().isEmpty()){
                    successCodePkIds.addAll(codeResult.getSuccessCodePkIds());
                    codePkIds.addAll(codeResult.getSuccessCodePkIds());
                    optCostFlag = true;
                }
                if(!codeResult.getFailCodePkIds().isEmpty()){
                    failCodePkIds.addAll(codeResult.getFailCodePkIds());
                    codePkIds.addAll(codeResult.getFailCodePkIds());
                    optCostFlag = false;
                    if(codeResult.getCostResult()!=null){
                        for (Integer codePkId : failCodePkIds) {
                            BmsYsCostRecordPO buildRecord = new BmsYsCostRecordPO();
                            buildRecord.setCodePkId(codePkId);
                            buildRecord.setCodeType(codeResult.getCodeType());
                            buildRecord.setCalculateFlag(BmsConstants.CALCULATE_FLAG_1);
                            buildRecord.setCalculateResult(codeResult.getCostResult().getCalculateResult());
                            buildRecord.setCalculateRemark(codeResult.getCostResult().getCalculateRemark());
                            buildRecord.setCalculateProcessResult(codeResult.getCostResult().getCalculateProcessResult());
                            buildRecord.setExpensesDimension(codeResult.getCostResult().getExpensesDimension());
                            buildRecord.setExpensesDimensionName(codeResult.getCostResult().getExpensesDimensionName());
                            buildRecord.setRuleDetailPkId(codeResult.getCostResult().getRuleDetailPkId());
                            buildRecord.setRuleDetailName(codeResult.getCostResult().getRuleDetailName());
                            buildRecord.setRuleMainPkId(codeResult.getCostResult().getRuleMainPkId());
                            buildRecord.setRuleMainName(codeResult.getCostResult().getRuleMainName());
                            buildRecord.setOperCode(userInfo.getEmployeecode().toString());
                            buildRecord.setOperBy(userInfo.getEmployeename());
                            buildRecord.setOperTime(now);
                            buildRecord.setOperDeptId(userInfo.getDepartmenid().intValue());
                            buildRecord.setDelFlag(BmsConstants.DEL_FLAG_0);
                            buildRecord.setOptMonth(DateUtils.optMonth());
                            buildRecord.setOptDay(DateUtils.optDay());
                            insertCostRecordPOS.add(buildRecord);
                        }
                    }
                }

                //费用相关
                String costMainId = SnowFlowUtils.nextId();
                String mainExpenseCode = codeUtils.getCode(CodeCategory.YSMainExpenseCode, tenantId);
                if(costResult!=null){

                    String costId = SnowFlowUtils.nextId();
                    String expenseCode = codeUtils.getCode(CodeCategory.YSExpensesCode, tenantId);

                    BmsYscostMainInfoPO buildMainCostInfo = new BmsYscostMainInfoPO();
                    buildMainCostInfo.setId(costMainId);
                    buildMainCostInfo.setExpensesCode(mainExpenseCode);
                    buildMainCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
                    buildMainCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
                    buildMainCostInfo.setBusinessType(costResult.getBusinessType());
                    buildMainCostInfo.setBusinessTime(costResult.getBusinessTime());
                    buildMainCostInfo.setOrderDate(costResult.getBeginTime());
                    buildMainCostInfo.setSigningDate(costResult.getEndTime());
                    buildMainCostInfo.setClientId(costResult.getClientId());
                    buildMainCostInfo.setCompanyId(costResult.getCompanyId());
                    buildMainCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
                    buildMainCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
                    buildMainCostInfo.setTotalBoxes(costResult.getTotalBoxes());
                    buildMainCostInfo.setTotalNumber(costResult.getTotalNumber());
                    buildMainCostInfo.setTotalWeight(costResult.getTotalWeight());
                    buildMainCostInfo.setTotalVolume(costResult.getTotalVolume());
                    buildMainCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
                    buildMainCostInfo.setTotalOverNum(costResult.getTotalOverNum());
                    buildMainCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
                    buildMainCostInfo.setSettleType(costResult.getSettleType());
                    buildMainCostInfo.setSettleEntityType(costResult.getSettleEntityType());
                    buildMainCostInfo.setSettleSetting(costResult.getSettleSetting());
                    buildMainCostInfo.setSettleMainId(costResult.getSettleMainId());
                    buildMainCostInfo.setSettleAmount(costResult.getCalculateResult());
                    buildMainCostInfo = CostUtil.setCostField(buildMainCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
                    buildMainCostInfo.setCreateBy(userInfo.getEmployeename());
                    buildMainCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
                    buildMainCostInfo.setCreateTime(now);
                    buildMainCostInfo.setOperBy(userInfo.getEmployeename());
                    buildMainCostInfo.setOperCode(userInfo.getEmployeecode().toString());
                    buildMainCostInfo.setOperTime(now);
                    buildMainCostInfo.setOptMonth(DateUtils.optMonth());
                    buildMainCostInfo.setOptDay(DateUtils.optDay());
                    insertCostMainInfoPOS.add(buildMainCostInfo);

                    BmsYscostInfoPO buildCostInfo = new BmsYscostInfoPO();
                    buildCostInfo.setId(costId);
                    buildCostInfo.setExpensesCode(expenseCode);
                    buildCostInfo.setMainCodeId(costMainId);
                    buildCostInfo.setExpensesType(costResult.getExpenseType());
                    buildCostInfo.setExpensesDimension(costResult.getExpensesDimension());
                    buildCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
                    buildCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
                    buildCostInfo.setBusinessTime(costResult.getBusinessTime());
                    buildCostInfo.setBillDate(costResult.getBillDate());
                    buildCostInfo.setOrderDate(costResult.getBeginTime());
                    buildCostInfo.setSigningDate(costResult.getEndTime());
                    buildCostInfo.setClientId(costResult.getClientId());
                    buildCostInfo.setCompanyId(costResult.getCompanyId());
                    buildCostInfo.setWarehouseCode(costResult.getWarehouseCode());
                    buildCostInfo.setWarehouseName(costResult.getWarehouseName());
                    buildCostInfo.setQuoterRuleId(costResult.getRuleMainPkId().toString());
                    buildCostInfo.setQuoterRuleName(costResult.getRuleMainName());
                    buildCostInfo.setQuoterRuleDetailId(costResult.getRuleDetailPkId().toString());
                    buildCostInfo.setQuoterRuleDetailName(costResult.getRuleDetailName());
                    buildCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
                    buildCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
                    buildCostInfo.setTotalBoxes(costResult.getTotalBoxes());
                    buildCostInfo.setTotalNumber(costResult.getTotalNumber());
                    buildCostInfo.setTotalWeight(costResult.getTotalWeight());
                    buildCostInfo.setTotalVolume(costResult.getTotalVolume());
                    buildCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
                    buildCostInfo.setTotalOverNum(costResult.getTotalOverNum());
                    buildCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
                    buildCostInfo.setSettleType(costResult.getSettleType());
                    buildCostInfo.setSettleEntityType(costResult.getSettleEntityType());
                    buildCostInfo.setSettleSetting(costResult.getSettleSetting());
                    buildCostInfo.setSettleMainId(costResult.getSettleMainId());
                    buildCostInfo.setSettleAmount(costResult.getCalculateResult());
                    buildCostInfo = CostUtil.setCostField(buildCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
                    buildCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
                    buildCostInfo.setCreateBy(userInfo.getEmployeecode().toString());
                    buildCostInfo.setCreateTime(now);
                    buildCostInfo.setOperBy(userInfo.getEmployeecode().toString());
                    buildCostInfo.setOperCode(userInfo.getEmployeecode().toString());
                    buildCostInfo.setOperTime(now);
                    buildCostInfo.setOptMonth(DateUtils.optMonth());
                    buildCostInfo.setOptDay(DateUtils.optDay());
                    insertCostInfoPOS.add(buildCostInfo);


                    //二次处理middle表，批涉及费用分摊
                    List<BmsYsexpensesMiddlePO> secondOptMiddles = new ArrayList<>();
                    Map<Integer, Dict> codeDataMap = costResult.getCodeDataMap();
                    for (Integer optCodePkId : costResult.getCodePkIds()) {

                        BmsYsexpensesMiddlePO buildCostMiddle = new BmsYsexpensesMiddlePO();
                        buildCostMiddle.setCodePkId(optCodePkId);
                        buildCostMiddle.setCodeType(costResult.getCodeType());
                        buildCostMiddle.setMainCodeId(costMainId);
                        buildCostMiddle.setShareType(costResult.getShareType());
                        buildCostMiddle.setOptMonth(DateUtils.optMonth());
                        buildCostMiddle.setOptDay(DateUtils.optDay());
                        if(codeDataMap.containsKey(optCodePkId)){
                            Dict dict = codeDataMap.get(optCodePkId);
                            BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName())?dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()):BigDecimal.ZERO;
                            BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
                            BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
                            buildCostMiddle.setTotalWeight(totalWeight);
                            buildCostMiddle.setTotalVolume(totalVolume);
                            buildCostMiddle.setTotalNumber(totalNumber);
                        }
                        secondOptMiddles.add(buildCostMiddle);
                    }
                    secondOptMiddles = CostUtil.allocateCost(buildMainCostInfo,secondOptMiddles, StatisticsFieldEnum.getByValue(costResult.getShareType()),CostUtil.YS_COST_SETTLE_SUM_FIELDS,BmsYsexpensesMiddlePO.class);
                    insertMiddlePOS.addAll(secondOptMiddles);
                }

                // 计费过程
                if(costResult==null){
                    if(CollUtil.isNotEmpty(codePkIds)){

                        for (Integer optCodePkId : codePkIds) {
                            BmsYsCostRecordPO buildRecord = new BmsYsCostRecordPO();
                            buildRecord.setCodePkId(optCodePkId);
                            buildRecord.setCodeType(codeResult.getCodeType());
                            buildRecord.setMainCodeId(costMainId);
                            buildRecord.setExpensesCode(mainExpenseCode);
                            buildRecord.setCalculateFlag(Boolean.TRUE.equals(optCostFlag) ? BmsConstants.CALCULATE_FLAG_0 : BmsConstants.CALCULATE_FLAG_1);
                            buildRecord.setCalculateResult(codeResult.getCostResult().getCalculateResult());
                            buildRecord.setCalculateRemark(codeResult.getCostResult().getCalculateRemark());
                            buildRecord.setCalculateProcessResult(codeResult.getCostResult().getCalculateProcessResult());
                            buildRecord.setExpensesDimension(codeResult.getCostResult().getExpensesDimension());
                            buildRecord.setExpensesDimensionName(codeResult.getCostResult().getExpensesDimensionName());
                            buildRecord.setRuleDetailPkId(codeResult.getCostResult().getRuleDetailPkId());
                            buildRecord.setRuleDetailName(codeResult.getCostResult().getRuleDetailName());
                            buildRecord.setRuleMainPkId(codeResult.getCostResult().getRuleMainPkId());
                            buildRecord.setRuleMainName(codeResult.getCostResult().getRuleMainName());
                            buildRecord.setOperCode(userInfo.getEmployeecode().toString());
                            buildRecord.setOperBy(userInfo.getEmployeename());
                            buildRecord.setOperTime(now);
                            buildRecord.setOperDeptId(userInfo.getDepartmenid().intValue());
                            buildRecord.setDelFlag(BmsConstants.DEL_FLAG_0);
                            buildRecord.setOptMonth(DateUtils.optMonth());
                            buildRecord.setOptDay(DateUtils.optDay());
                            insertCostRecordPOS.add(buildRecord);
                        }
                    }
                }else {
                    BmsYsCostRecordPO buildRecord = new BmsYsCostRecordPO();
                    buildRecord.setCodeType(codeResult.getCodeType());
                    buildRecord.setMainCodeId(costMainId);
                    buildRecord.setExpensesCode(mainExpenseCode);
                    buildRecord.setCalculateFlag(Boolean.TRUE.equals(optCostFlag) ? BmsConstants.CALCULATE_FLAG_0 : BmsConstants.CALCULATE_FLAG_1);
                    buildRecord.setCalculateResult(codeResult.getCostResult().getCalculateResult());
                    buildRecord.setCalculateRemark(codeResult.getCostResult().getCalculateRemark());
                    buildRecord.setCalculateProcessResult(codeResult.getCostResult().getCalculateProcessResult());
                    buildRecord.setExpensesDimension(codeResult.getCostResult().getExpensesDimension());
                    buildRecord.setExpensesDimensionName(codeResult.getCostResult().getExpensesDimensionName());
                    buildRecord.setRuleDetailPkId(codeResult.getCostResult().getRuleDetailPkId());
                    buildRecord.setRuleDetailName(codeResult.getCostResult().getRuleDetailName());
                    buildRecord.setRuleMainPkId(codeResult.getCostResult().getRuleMainPkId());
                    buildRecord.setRuleMainName(codeResult.getCostResult().getRuleMainName());
                    buildRecord.setOperCode(userInfo.getEmployeecode().toString());
                    buildRecord.setOperBy(userInfo.getEmployeename());
                    buildRecord.setOperTime(now);
                    buildRecord.setOperDeptId(userInfo.getDepartmenid().intValue());
                    buildRecord.setDelFlag(BmsConstants.DEL_FLAG_0);
                    buildRecord.setOptMonth(DateUtils.optMonth());
                    buildRecord.setOptDay(DateUtils.optDay());
                    insertCostRecordPOS.add(buildRecord);
                }

            }

            //单据计费成功的要从计费失败中去掉
            failCodePkIds = failCodePkIds.stream()
                    .filter(k->!successCodePkIds.contains(k))
                    .collect(Collectors.toSet());
            return CalculateYsDbResult.builder()
                    .codePkIds(Lists.newArrayList(codePkIds))
                    .successCodePkIds(Lists.newArrayList(successCodePkIds))
                    .failCodePkIds(Lists.newArrayList(failCodePkIds))
                    .insertCostMainInfoPOs(insertCostMainInfoPOS)
                    .insertCostInfoPOs(insertCostInfoPOS)
                    .insertMiddlePOs(insertMiddlePOS)
                    .insertCostRecordPOs(insertCostRecordPOS)
                    .build();
        });
    }
    /**
     * 应收月维度DB数据整合
     * @param param 单维度计费结果
     * @return 月维度所有的DB结果
     */
    @Async("asyncTaskExecutor")
    public CompletableFuture<CalculateYsDbResult> ysDbMonthFuture(List<CalculateResult> param, UserBean userInfo){

        return CompletableFuture.supplyAsync(()->{

            String tenantId = userInfo.getTenantid().toString();
            Date now = new Date();

            Set<Integer> codePkIds = new HashSet<>();
            Set<Integer> successCodePkIds = new HashSet<>();
            Set<Integer> failCodePkIds = new HashSet<>();

            List<BmsYsCostRecordPO> insertCostRecordPOS = Lists.newArrayList();
            List<BmsYscostMainInfoPO> insertCostMainInfoPOS = Lists.newArrayList();
            List<BmsYscostInfoPO> insertCostInfoPOS = Lists.newArrayList();
            List<BmsYsexpensesMiddlePO> insertMiddlePOS = Lists.newArrayList();

            for (CalculateResult calculateResult : param) {

                CalculateCodeResult codeResult = calculateResult.getCodeResult();
                CalculateCostResult costResult = calculateResult.getCostResult();

                //单据相关
                Boolean optCostFlag = null;
                if(!codeResult.getSuccessCodePkIds().isEmpty()){
                    successCodePkIds.addAll(codeResult.getSuccessCodePkIds());
                    codePkIds.addAll(codeResult.getSuccessCodePkIds());
                    optCostFlag = true;
                }
                if(!codeResult.getFailCodePkIds().isEmpty()){
                    failCodePkIds.addAll(codeResult.getFailCodePkIds());
                    codePkIds.addAll(codeResult.getFailCodePkIds());
                    optCostFlag = false;
                }

                //费用相关
                String costMainId = SnowFlowUtils.nextId();
                String mainExpenseCode = codeUtils.getCode(CodeCategory.YSMainExpenseCode, tenantId);
                if(costResult!=null){

                    String costId = SnowFlowUtils.nextId();
                    String expenseCode = codeUtils.getCode(CodeCategory.YSExpensesCode, tenantId);

                    BmsYscostMainInfoPO buildMainCostInfo = new BmsYscostMainInfoPO();
                    buildMainCostInfo.setId(costMainId);
                    buildMainCostInfo.setExpensesCode(mainExpenseCode);
                    buildMainCostInfo.setExpensesType(costResult.getExpenseType());
                    buildMainCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
                    buildMainCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
                    buildMainCostInfo.setBusinessType(costResult.getBusinessType());
                    buildMainCostInfo.setBusinessTime(costResult.getBusinessTime());
                    buildMainCostInfo.setOrderDate(costResult.getBeginTime());
                    buildMainCostInfo.setSigningDate(costResult.getEndTime());
                    buildMainCostInfo.setClientId(costResult.getClientId());
                    buildMainCostInfo.setCompanyId(costResult.getCompanyId());
                    buildMainCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
                    buildMainCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
                    buildMainCostInfo.setTotalBoxes(costResult.getTotalBoxes());
                    buildMainCostInfo.setTotalNumber(costResult.getTotalNumber());
                    buildMainCostInfo.setTotalWeight(costResult.getTotalWeight());
                    buildMainCostInfo.setTotalVolume(costResult.getTotalVolume());
                    buildMainCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
                    buildMainCostInfo.setTotalOverNum(costResult.getTotalOverNum());
                    buildMainCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
                    buildMainCostInfo.setSettleType(costResult.getSettleType());
                    buildMainCostInfo.setSettleEntityType(costResult.getSettleEntityType());
                    buildMainCostInfo.setSettleSetting(costResult.getSettleSetting());
                    buildMainCostInfo.setSettleMainId(costResult.getSettleMainId());
                    buildMainCostInfo.setSettleAmount(costResult.getCalculateResult());
                    buildMainCostInfo = CostUtil.setCostField(buildMainCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
                    buildMainCostInfo.setCreateBy(userInfo.getEmployeename());
                    buildMainCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
                    buildMainCostInfo.setCreateTime(now);
                    buildMainCostInfo.setOperBy(userInfo.getEmployeename());
                    buildMainCostInfo.setOperCode(userInfo.getEmployeecode().toString());
                    buildMainCostInfo.setOperTime(now);
                    buildMainCostInfo.setOptMonth(DateUtils.optMonth());
                    buildMainCostInfo.setOptDay(DateUtils.optDay());
                    insertCostMainInfoPOS.add(buildMainCostInfo);

                    BmsYscostInfoPO buildCostInfo = new BmsYscostInfoPO();
                    buildCostInfo.setId(costId);
                    buildCostInfo.setExpensesCode(expenseCode);
                    buildCostInfo.setMainCodeId(costMainId);
                    buildCostInfo.setExpensesType(costResult.getExpenseType());
                    buildCostInfo.setExpensesDimension(costResult.getExpensesDimension());
                    buildCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
                    buildCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
                    buildCostInfo.setBusinessTime(costResult.getBusinessTime());
                    buildCostInfo.setBillDate(costResult.getBillDate());
                    buildCostInfo.setOrderDate(costResult.getBeginTime());
                    buildCostInfo.setSigningDate(costResult.getEndTime());
                    buildCostInfo.setClientId(costResult.getClientId());
                    buildCostInfo.setCompanyId(costResult.getCompanyId());
                    buildCostInfo.setWarehouseCode(costResult.getWarehouseCode());
                    buildCostInfo.setWarehouseName(costResult.getWarehouseName());
                    buildCostInfo.setQuoterRuleId(costResult.getRuleMainPkId().toString());
                    buildCostInfo.setQuoterRuleName(costResult.getRuleMainName());
                    buildCostInfo.setQuoterRuleDetailId(costResult.getRuleDetailPkId().toString());
                    buildCostInfo.setQuoterRuleDetailName(costResult.getRuleDetailName());
                    buildCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
                    buildCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
                    buildCostInfo.setTotalBoxes(costResult.getTotalBoxes());
                    buildCostInfo.setTotalNumber(costResult.getTotalNumber());
                    buildCostInfo.setTotalWeight(costResult.getTotalWeight());
                    buildCostInfo.setTotalVolume(costResult.getTotalVolume());
                    buildCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
                    buildCostInfo.setTotalOverNum(costResult.getTotalOverNum());
                    buildCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
                    buildCostInfo.setSettleType(costResult.getSettleType());
                    buildCostInfo.setSettleEntityType(costResult.getSettleEntityType());
                    buildCostInfo.setSettleSetting(costResult.getSettleSetting());
                    buildCostInfo.setSettleMainId(costResult.getSettleMainId());
                    buildCostInfo.setSettleAmount(costResult.getCalculateResult());
                    buildCostInfo = CostUtil.setCostField(buildCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
                    buildCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
                    buildCostInfo.setCreateBy(userInfo.getEmployeecode().toString());
                    buildCostInfo.setCreateTime(now);
                    buildCostInfo.setOperBy(userInfo.getEmployeecode().toString());
                    buildCostInfo.setOperCode(userInfo.getEmployeecode().toString());
                    buildCostInfo.setOperTime(now);
                    buildCostInfo.setOptMonth(DateUtils.optMonth());
                    buildCostInfo.setOptDay(DateUtils.optDay());
                    insertCostInfoPOS.add(buildCostInfo);


                    //需要二次处理middle表，批涉及费用分摊
                    List<BmsYsexpensesMiddlePO> secondOptMiddles = new ArrayList<>();
                    Map<Integer, Dict> codeDataMap = costResult.getCodeDataMap();
                    for (Integer optCodePkId : costResult.getCodePkIds()) {

                        BmsYsexpensesMiddlePO buildCostMiddle = new BmsYsexpensesMiddlePO();
                        buildCostMiddle.setCodePkId(optCodePkId);
                        buildCostMiddle.setCodeType(costResult.getCodeType());
                        buildCostMiddle.setMainCodeId(costMainId);
                        buildCostMiddle.setShareType(costResult.getShareType());
                        buildCostMiddle.setOptMonth(DateUtils.optMonth());
                        buildCostMiddle.setOptDay(DateUtils.optDay());
                        if(codeDataMap.containsKey(optCodePkId)){
                            Dict dict = codeDataMap.get(optCodePkId);
                            BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName())?dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()):BigDecimal.ZERO;
                            BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
                            BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
                            buildCostMiddle.setTotalWeight(totalWeight);
                            buildCostMiddle.setTotalVolume(totalVolume);
                            buildCostMiddle.setTotalNumber(totalNumber);
                        }
                        secondOptMiddles.add(buildCostMiddle);
                    }
                    secondOptMiddles = CostUtil.allocateCost(buildMainCostInfo,secondOptMiddles, StatisticsFieldEnum.getByValue(costResult.getShareType()),CostUtil.YS_COST_SETTLE_SUM_FIELDS,BmsYsexpensesMiddlePO.class);
                    insertMiddlePOS.addAll(secondOptMiddles);
                }

                // 计费过程
                if(costResult == null){
                    if(CollUtil.isNotEmpty(codePkIds)){

                        for (Integer optCodePkId : codePkIds) {
                            BmsYsCostRecordPO buildRecord = new BmsYsCostRecordPO();
                            buildRecord.setCodePkId(optCodePkId);
                            buildRecord.setCodeType(codeResult.getCodeType());
                            buildRecord.setMainCodeId(costMainId);
                            buildRecord.setExpensesCode(mainExpenseCode);
                            buildRecord.setCalculateFlag(Boolean.TRUE.equals(optCostFlag) ? BmsConstants.CALCULATE_FLAG_0 : BmsConstants.CALCULATE_FLAG_1);
                            buildRecord.setCalculateResult(codeResult.getCostResult().getCalculateResult());
                            buildRecord.setCalculateRemark(codeResult.getCostResult().getCalculateRemark());
                            buildRecord.setCalculateProcessResult(codeResult.getCostResult().getCalculateProcessResult());
                            buildRecord.setExpensesDimension(codeResult.getCostResult().getExpensesDimension());
                            buildRecord.setExpensesDimensionName(codeResult.getCostResult().getExpensesDimensionName());
                            buildRecord.setRuleDetailPkId(codeResult.getCostResult().getRuleDetailPkId());
                            buildRecord.setRuleDetailName(codeResult.getCostResult().getRuleDetailName());
                            buildRecord.setRuleMainPkId(codeResult.getCostResult().getRuleMainPkId());
                            buildRecord.setRuleMainName(codeResult.getCostResult().getRuleMainName());
                            buildRecord.setOperCode(userInfo.getEmployeecode().toString());
                            buildRecord.setOperBy(userInfo.getEmployeename());
                            buildRecord.setOperTime(now);
                            buildRecord.setOperDeptId(userInfo.getDepartmenid().intValue());
                            buildRecord.setDelFlag(BmsConstants.DEL_FLAG_0);
                            buildRecord.setOptMonth(DateUtils.optMonth());
                            buildRecord.setOptDay(DateUtils.optDay());
                            insertCostRecordPOS.add(buildRecord);
                        }
                    }
                } else {
                    BmsYsCostRecordPO buildRecord = new BmsYsCostRecordPO();
                    buildRecord.setCodeType(codeResult.getCodeType());
                    buildRecord.setMainCodeId(costMainId);
                    buildRecord.setExpensesCode(mainExpenseCode);
                    buildRecord.setCalculateFlag(Boolean.TRUE.equals(optCostFlag) ? BmsConstants.CALCULATE_FLAG_0 : BmsConstants.CALCULATE_FLAG_1);
                    buildRecord.setCalculateResult(codeResult.getCostResult().getCalculateResult());
                    buildRecord.setCalculateRemark(codeResult.getCostResult().getCalculateRemark());
                    buildRecord.setCalculateProcessResult(codeResult.getCostResult().getCalculateProcessResult());
                    buildRecord.setExpensesDimension(codeResult.getCostResult().getExpensesDimension());
                    buildRecord.setExpensesDimensionName(codeResult.getCostResult().getExpensesDimensionName());
                    buildRecord.setRuleDetailPkId(codeResult.getCostResult().getRuleDetailPkId());
                    buildRecord.setRuleDetailName(codeResult.getCostResult().getRuleDetailName());
                    buildRecord.setRuleMainPkId(codeResult.getCostResult().getRuleMainPkId());
                    buildRecord.setRuleMainName(codeResult.getCostResult().getRuleMainName());
                    buildRecord.setOperCode(userInfo.getEmployeecode().toString());
                    buildRecord.setOperBy(userInfo.getEmployeename());
                    buildRecord.setOperTime(now);
                    buildRecord.setOperDeptId(userInfo.getDepartmenid().intValue());
                    buildRecord.setDelFlag(BmsConstants.DEL_FLAG_0);
                    buildRecord.setOptMonth(DateUtils.optMonth());
                    buildRecord.setOptDay(DateUtils.optDay());
                    insertCostRecordPOS.add(buildRecord);
                }
            }

            //单据计费成功的要从计费失败中去掉
            failCodePkIds = failCodePkIds.stream()
                    .filter(k->!successCodePkIds.contains(k))
                    .collect(Collectors.toSet());
            return CalculateYsDbResult.builder()
                    .codePkIds(Lists.newArrayList(codePkIds))
                    .successCodePkIds(Lists.newArrayList(successCodePkIds))
                    .failCodePkIds(Lists.newArrayList(failCodePkIds))
                    .insertCostMainInfoPOs(insertCostMainInfoPOS)
                    .insertCostInfoPOs(insertCostInfoPOS)
                    .insertMiddlePOs(insertMiddlePOS)
                    .insertCostRecordPOs(insertCostRecordPOS)
                    .build();
        });
    }




    @Override
    public CalculateYfDbResult yfDbCalculate(CalculateDbParam param) {

        List<CalculateResult> orderResult = param.getOrderResult();
        List<CalculateResult> dayResult = param.getDayResult();
        List<CalculateResult> monthResult = param.getMonthResult();

        UserBean userInfo = RequestContext.getUserInfo();

        CompletableFuture<CalculateYfDbResult> orderFuture = yfDbOrderFuture(orderResult ,userInfo);
        CompletableFuture<CalculateYfDbResult> dayFuture = yfDbDayFuture(dayResult ,userInfo);
        CompletableFuture<CalculateYfDbResult> monthFuture = yfDbMonthFuture(monthResult,userInfo);

        List<CompletableFuture<CalculateYfDbResult>> futures = Arrays.asList(orderFuture, dayFuture, monthFuture);

        CompletableFuture<List<CalculateYfDbResult>> allResults = CompletableFuture.allOf(
                        futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList()));

        List<CalculateYfDbResult> results = allResults.join();

        //单日月数据整合
        Set<Integer> codePkIds = new HashSet<>();
        Set<Integer> successCodePkIds = new HashSet<>();
        Set<Integer> failCodePkIds = new HashSet<>();

        List<BmsYfcostMainInfoPO> insertCostMainInfoPOs = new ArrayList<>();
        List<BmsYfcostInfoPO> insertCostInfoPOs = new ArrayList<>();
        List<BmsYfexpensesMiddlePO> insertMiddlePOs = new ArrayList<>();
        List<BmsYfexpensesMiddleSharePO> insertMiddleSharePOs = new ArrayList<>();
        List<BmsYfCostRecordPO> insertCostRecordPOs = new ArrayList<>();

        for (CalculateYfDbResult dbResult : results) {
            codePkIds.addAll(dbResult.getCodePkIds());
            successCodePkIds.addAll(dbResult.getSuccessCodePkIds());
            failCodePkIds.addAll(dbResult.getFailCodePkIds());
            insertCostMainInfoPOs.addAll(dbResult.getInsertCostMainInfoPOs());
            insertCostInfoPOs.addAll(dbResult.getInsertCostInfoPOs());
            insertMiddlePOs.addAll(dbResult.getInsertMiddlePOs());
            insertMiddleSharePOs.addAll(dbResult.getInsertMiddleSharePOs());
            insertCostRecordPOs.addAll(dbResult.getInsertCostRecordPOs());
        }

        //单据计费成功的要从计费失败中去掉
        failCodePkIds = failCodePkIds.stream()
                .filter(k -> !successCodePkIds.contains(k))
                .collect(Collectors.toSet());

        return CalculateYfDbResult.builder()
                .codePkIds(Lists.newArrayList(codePkIds))
                .successCodePkIds(Lists.newArrayList(successCodePkIds))
                .failCodePkIds(Lists.newArrayList(failCodePkIds))
                .insertCostMainInfoPOs(insertCostMainInfoPOs)
                .insertCostInfoPOs(insertCostInfoPOs)
                .insertMiddlePOs(insertMiddlePOs)
                .insertMiddleSharePOs(insertMiddleSharePOs)
                .insertCostRecordPOs(insertCostRecordPOs)
                .build();
    }



    /**
     * 应付单维度DB数据整合
     * 只有成功的才会有明细id
     * @param param 单维度计费结果
     * @return 单维度所有的DB结果
     */
    @Async("asyncTaskExecutor")
    public CompletableFuture<CalculateYfDbResult> yfDbOrderFuture(List<CalculateResult> param ,UserBean userInfo){

        return CompletableFuture.supplyAsync(()->{

            String tenantId = userInfo.getTenantid().toString();
            Date now = new Date();

            Set<Integer> codePkIds = new HashSet<>();
            Set<Integer> successCodePkIds = new HashSet<>();
            Set<Integer> failCodePkIds = new HashSet<>();
            //key:codePkId,value:records
            Map<Integer, Set<Integer>> codeDetail2PkIdMap = new HashMap<>();
            Map<Integer, List<BmsYfCostRecordPO>> recordMap = new HashMap<>();
            Map<Integer, List<BmsYfcostMainInfoPO>> insertMainMap = new HashMap<>();
            Map<Integer, List<BmsYfcostInfoPO>> insertCostMap = new HashMap<>();
            Map<Integer, List<BmsYfexpensesMiddlePO>> insertMiddleMap = new HashMap<>();
            Map<Integer, List<BmsYfexpensesMiddleSharePO>> insertMiddleShareMap = new HashMap<>();

            // 所有的作业单信息重量信息(因为要考虑合单)
            Map<Integer, Dict> allCodeDetail2DataMap = new HashMap<>();

            //单纬度convertDB
            for (CalculateResult calculateResult : param) {

                CalculateCodeResult codeResult = calculateResult.getCodeResult();
                CalculateCostResult costResult = calculateResult.getCostResult();

                //费用相关
                String costMainId = SnowFlowUtils.nextId();
                String mainExpenseCode = codeUtils.getCode(CodeCategory.YFMainExpenseCode, tenantId);
                String costId = SnowFlowUtils.nextId();
                String expenseCode = codeUtils.getCode(CodeCategory.YFExpensesCode, tenantId);

                //单据相关
                Integer optCodePkId = null;
                Boolean optCostFlag = null;

                //计费成功会需要明细二的数据(明细2维度分摊)
                Set<Integer> optCodeDetail2PkIds = new HashSet<>();
                //计费记录需要使用
                if(!codeResult.getSuccessCodePkIds().isEmpty()){
                    optCodePkId = codeResult.getSuccessCodePkIds().iterator().next();
                    optCodeDetail2PkIds = costResult.getCodeDetail2PkIds();
                    successCodePkIds.add(optCodePkId);
                    codePkIds.add(optCodePkId);
                    optCostFlag = true;
                }
                if(!codeResult.getFailCodePkIds().isEmpty()){
                    optCodePkId = codeResult.getFailCodePkIds().iterator().next();
                    failCodePkIds.add(optCodePkId);
                    codePkIds.add(optCodePkId);
                    optCostFlag = false;
                }

                if(costResult!=null){

                    // 根据extraType决定费用构建方式
                    GroovyCalculateExtraType extraType = GroovyCalculateExtraType.getByCode(costResult.getExtraType());
                    if (extraType == null) {
                        extraType = GroovyCalculateExtraType.DEFAULT;
                    }

                    // 构建费用信息
                    buildYfCostInfo(costResult, costMainId, mainExpenseCode, costId, expenseCode,
                                  optCodePkId, optCodeDetail2PkIds, extraType, userInfo, now,
                                  insertMainMap, insertCostMap, insertMiddleMap, insertMiddleShareMap,
                                  allCodeDetail2DataMap);
                }
                if(optCodePkId != null){

                    BmsYfCostRecordPO buildRecord = new BmsYfCostRecordPO();
                    buildRecord.setCodePkId(optCodePkId);
                    buildRecord.setCodeType(codeResult.getCodeType());
                    buildRecord.setMainCodeId(costMainId);
                    buildRecord.setExpensesCode(mainExpenseCode);
                    buildRecord.setCalculateFlag(costResult != null?BmsConstants.CALCULATE_FLAG_1:BmsConstants.CALCULATE_FLAG_0);
                    buildRecord.setCalculateResult(costResult != null ? costResult.getCalculateResult() : null);
                    buildRecord.setCalculateRemark(costResult != null ? costResult.getCalculateRemark() : null);
                    buildRecord.setCalculateProcessResult(costResult != null ? costResult.getCalculateProcessResult() : null);
                    buildRecord.setExpensesDimension(costResult != null ? costResult.getExpensesDimension() : null);
                    buildRecord.setExpensesDimensionName(costResult != null ? costResult.getExpensesDimensionName() : null);
                    buildRecord.setRuleDetailPkId(codeResult.getCostResult().getRuleDetailPkId());
                    buildRecord.setRuleDetailName(codeResult.getCostResult().getRuleDetailName());
                    buildRecord.setRuleMainPkId(codeResult.getCostResult().getRuleMainPkId());
                    buildRecord.setRuleMainName(codeResult.getCostResult().getRuleMainName());
                    buildRecord.setOperCode(userInfo.getEmployeecode().toString());
                    buildRecord.setOperBy(userInfo.getEmployeename());
                    buildRecord.setOperTime(now);
                    buildRecord.setOperDeptId(userInfo.getDepartmenid().intValue());
                    buildRecord.setDelFlag(BmsConstants.DEL_FLAG_0);
                    buildRecord.setOptMonth(DateUtils.optMonth());
                    buildRecord.setOptDay(DateUtils.optDay());
                    if(recordMap.containsKey(optCodePkId)){
                        List<BmsYfCostRecordPO> costRecordPOS = recordMap.get(optCodePkId);
                        costRecordPOS.add(buildRecord);
                        recordMap.put(optCodePkId,costRecordPOS);
                    }else {
                        recordMap.put(optCodePkId, Lists.newArrayList(buildRecord));
                    }
                }

                if(codeDetail2PkIdMap.containsKey(optCodePkId)){
                    Set<Integer> codeDetail2PkIdSet = codeDetail2PkIdMap.get(optCodePkId);
                    codeDetail2PkIdSet.addAll(optCodeDetail2PkIds);
                    codeDetail2PkIdMap.put(optCodePkId, codeDetail2PkIdSet);
                }else {
                    if(CollUtil.isNotEmpty(optCodeDetail2PkIds)){
                        codeDetail2PkIdMap.put(optCodePkId, optCodeDetail2PkIds);
                    }
                }

            }

            //费用合并(按单计费需要合并)
            //单据计费成功的要从计费失败中去掉
            failCodePkIds = failCodePkIds.stream()
                    .filter(k->!successCodePkIds.contains(k))
                    .collect(Collectors.toSet());

            List<BmsYfCostRecordPO> insertCostRecordPOS = Lists.newArrayList();
            List<BmsYfcostMainInfoPO> insertMainCostPOs = Lists.newArrayList();
            List<BmsYfcostInfoPO> insertCostInfoPOS = Lists.newArrayList();
            List<BmsYfexpensesMiddlePO> insertMiddlePOS = Lists.newArrayList();
            List<BmsYfexpensesMiddleSharePO> insertMiddleSharePOs = Lists.newArrayList();

            for (Integer key : insertMainMap.keySet()) {

                List<BmsYfcostMainInfoPO> insertMainCosts = insertMainMap.get(key);
                List<BmsYfcostInfoPO> insertCosts = insertCostMap.get(key);
                List<BmsYfexpensesMiddlePO> insertMiddles = insertMiddleMap.get(key);
                List<BmsYfexpensesMiddleSharePO> insertMiddleShares = insertMiddleShareMap.containsKey(key)?insertMiddleShareMap.get(key):List.of();
                List<BmsYfCostRecordPO> insertRecords = recordMap.get(key);

                //大于1条就要合并
                if(insertMainCosts.size()>1) {

                    //合并除了费用信息其他数据一致
                    String costMainId = SnowFlowUtils.nextId();
                    String mainExpenseCode = codeUtils.getCode(CodeCategory.YSMainExpenseCode, tenantId);
                    String costId = SnowFlowUtils.nextId();
                    String expenseCode = codeUtils.getCode(CodeCategory.YSExpensesCode, tenantId);

                    BmsYfcostMainInfoPO buildMainCost = BeanUtil.toBean(insertMainCosts.get(0), BmsYfcostMainInfoPO.class);
                    buildMainCost = CostUtil.aggregateCost(buildMainCost,insertCosts,BmsYfcostMainInfoPO.class,CostUtil.YF_COST_SETTLE_SUM_FIELDS);
                    buildMainCost.setId(costMainId);
                    buildMainCost.setExpensesCode(mainExpenseCode);
                    insertMainCostPOs.add(buildMainCost);

                    BmsYfcostInfoPO buildCost = BeanUtil.toBean(insertCosts.get(0), BmsYfcostInfoPO.class);
                    buildCost = CostUtil.aggregateCost(buildCost, insertCosts, BmsYfcostInfoPO.class, CostUtil.YF_COST_SETTLE_SUM_FIELDS);
                    buildCost.setId(costId);
                    buildCost.setExpensesCode(expenseCode);
                    buildCost.setMainCodeId(costMainId);
                    insertCostInfoPOS.add(buildCost);

                    BmsYfexpensesMiddlePO buildMiddle = BeanUtil.toBean(insertMiddles.get(0), BmsYfexpensesMiddlePO.class);
                    buildMiddle = CostUtil.aggregateCost(buildMiddle, insertCosts, BmsYfexpensesMiddlePO.class, CostUtil.YF_COST_SETTLE_SUM_FIELDS);
                    buildMiddle.setMainCodeId(costMainId);
                    insertMiddlePOS.add(buildMiddle);

                    //分摊明细2单据pk_id
                    BmsYfexpensesMiddleSharePO middleShare = BeanUtil.toBean(insertMiddleShares.get(0), BmsYfexpensesMiddleSharePO.class);
                    List<BmsYfexpensesMiddleSharePO> buildMiddleSharePOs = new ArrayList<>();
                    Set<Integer> detail2PkIdSet = codeDetail2PkIdMap.get(key);
                    for (Integer detail2PkId : detail2PkIdSet) {
                        BmsYfexpensesMiddleSharePO buildShareData = new BmsYfexpensesMiddleSharePO();
                        buildShareData.setCodePkId(detail2PkId);
                        buildShareData.setMainCodeId(costMainId);
                        buildShareData.setShareType(middleShare.getShareType());
                        buildShareData.setOptMonth(middleShare.getOptMonth());
                        buildShareData.setOptDay(middleShare.getOptDay());
                        if(allCodeDetail2DataMap.containsKey(detail2PkId)){
                            Dict dict = allCodeDetail2DataMap.get(detail2PkId);
                            BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName())?dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()):BigDecimal.ZERO;
                            BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
                            BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
                            buildShareData.setTotalWeight(totalWeight);
                            buildShareData.setTotalVolume(totalVolume);
                            buildShareData.setTotalNumber(totalNumber);
                        }
                        buildMiddleSharePOs.add(buildShareData);
                    }
                    buildMiddleSharePOs = CostUtil.allocateCost(buildMainCost,buildMiddleSharePOs,StatisticsFieldEnum.getByValue(middleShare.getShareType()),CostUtil.YF_COST_SETTLE_SUM_FIELDS,BmsYfexpensesMiddleSharePO.class);
                    insertMiddleSharePOs.add(buildMiddleSharePOs.get(0));
                    insertCostRecordPOS.addAll(insertRecords);
                } else {

                    insertMainCostPOs.addAll(insertMainCosts);
                    insertCostInfoPOS.addAll(insertCosts);
                    insertMiddlePOS.addAll(insertMiddles);
                    insertMiddleSharePOs.addAll(insertMiddleShares);
                    insertCostRecordPOS.addAll(insertRecords);
                }

            }

            // 单据计费记录
            if(!recordMap.isEmpty()){
                insertCostRecordPOS = recordMap.values().stream()
                        .flatMap(List::stream).toList();
            }

            return CalculateYfDbResult.builder()
                    .codePkIds(Lists.newArrayList(codePkIds))
                    .successCodePkIds(Lists.newArrayList(successCodePkIds))
                    .failCodePkIds(Lists.newArrayList(failCodePkIds))
                    .insertCostMainInfoPOs(insertMainCostPOs)
                    .insertCostInfoPOs(insertCostInfoPOS)
                    .insertMiddlePOs(insertMiddlePOS)
                    .insertMiddleSharePOs(insertMiddleSharePOs)
                    .insertCostRecordPOs(insertCostRecordPOS)
                    .build();
        });
    }
    /**
     * 应付天维度DB数据整合
     * @param param 单维度计费结果
     * @return 天维度所有的DB结果
     */
    @Async("asyncTaskExecutor")
    public CompletableFuture<CalculateYfDbResult> yfDbDayFuture(List<CalculateResult> param, UserBean userInfo){

        return CompletableFuture.supplyAsync(()->{

            String tenantId = userInfo.getTenantid().toString();
            Date now = new Date();

            Set<Integer> codePkIds = new HashSet<>();
            Set<Integer> successCodePkIds = new HashSet<>();
            Set<Integer> failCodePkIds = new HashSet<>();

            List<BmsYfCostRecordPO> insertCostRecordPOS = new ArrayList<>();
            List<BmsYfcostMainInfoPO> insertCostMainInfoPOS = new ArrayList<>();
            List<BmsYfcostInfoPO> insertCostInfoPOS = new ArrayList<>();
            List<BmsYfexpensesMiddlePO> insertMiddlePOS = new ArrayList<>();
            List<BmsYfexpensesMiddleSharePO> insertMiddleSharePOs = new ArrayList<>();

            for (CalculateResult calculateResult : param) {

                CalculateCodeResult codeResult = calculateResult.getCodeResult();
                CalculateCostResult costResult = calculateResult.getCostResult();

                //单据相关
                boolean optCostFlag = false;
                if (!codeResult.getSuccessCodePkIds().isEmpty()) {

                    optCostFlag = true;
                    successCodePkIds.addAll(codeResult.getSuccessCodePkIds());
                    codePkIds.addAll(codeResult.getSuccessCodePkIds());
                }
                if(!codeResult.getFailCodePkIds().isEmpty()) {

                    optCostFlag = false;
                    failCodePkIds.addAll(codeResult.getFailCodePkIds());
                    codePkIds.addAll(codeResult.getFailCodePkIds());
                }

                //费用相关
                String costMainId = SnowFlowUtils.nextId();
                String mainExpenseCode = codeUtils.getCode(CodeCategory.YFMainExpenseCode, tenantId);
                String expenseCode = codeUtils.getCode(CodeCategory.YFExpensesCode, tenantId);
                if(costResult != null){

                    String costId = SnowFlowUtils.nextId();

                    BmsYfcostMainInfoPO buildMainCostInfo = new BmsYfcostMainInfoPO();
                    buildMainCostInfo.setId(costMainId);
                    buildMainCostInfo.setExpensesCode(mainExpenseCode);
                    buildMainCostInfo.setExpensesType(costResult.getExpenseType());
                    buildMainCostInfo.setExpensesDimension(costResult.getExpensesDimension());
                    buildMainCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
                    buildMainCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
                    buildMainCostInfo.setBusinessTime(costResult.getBusinessTime());
                    buildMainCostInfo.setDispatchDate(costResult.getBeginTime());
                    buildMainCostInfo.setFinishDate(costResult.getEndTime());
                    buildMainCostInfo.setCarrierId(costResult.getCarrierId());
                    buildMainCostInfo.setClientId(costResult.getClientId());
                    buildMainCostInfo.setCompanyId(costResult.getCompanyId());
                    buildMainCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
                    buildMainCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
                    buildMainCostInfo.setTotalBoxes(costResult.getTotalBoxes());
                    buildMainCostInfo.setTotalNumber(costResult.getTotalNumber());
                    buildMainCostInfo.setTotalWeight(costResult.getTotalWeight());
                    buildMainCostInfo.setTotalVolume(costResult.getTotalVolume());
                    buildMainCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
                    buildMainCostInfo.setTotalOverNum(costResult.getTotalOverNum());
                    buildMainCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
                    buildMainCostInfo.setSettleType(costResult.getSettleType());
                    buildMainCostInfo.setSettleEntityType(costResult.getSettleEntityType());
                    buildMainCostInfo.setSettleSetting(costResult.getSettleSetting());
                    buildMainCostInfo.setSettleMainId(costResult.getSettleMainId());
                    buildMainCostInfo.setSettleAmount(costResult.getCalculateResult());
                    buildMainCostInfo = CostUtil.setCostField(buildMainCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
                    buildMainCostInfo.setCreateBy(userInfo.getEmployeename());
                    buildMainCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
                    buildMainCostInfo.setCreateTime(now);
                    buildMainCostInfo.setOperBy(userInfo.getEmployeename());
                    buildMainCostInfo.setOperCode(userInfo.getEmployeecode().toString());
                    buildMainCostInfo.setOperTime(now);
                    buildMainCostInfo.setOptMonth(DateUtils.optMonth());
                    buildMainCostInfo.setOptDay(DateUtils.optDay());
                    insertCostMainInfoPOS.add(buildMainCostInfo);

                    BmsYfcostInfoPO buildCostInfo = new BmsYfcostInfoPO();
                    buildCostInfo.setId(costId);
                    buildCostInfo.setExpensesCode(expenseCode);
                    buildCostInfo.setMainCodeId(costMainId);
                    buildCostInfo.setExpensesType(costResult.getExpenseType());
                    buildCostInfo.setExpensesDimension(costResult.getExpensesDimension());
                    buildCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
                    buildCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
                    buildCostInfo.setBusinessTime(costResult.getBusinessTime());
                    buildCostInfo.setBillDate(costResult.getBillDate());
                    buildCostInfo.setDispatchDate(costResult.getBeginTime());
                    buildCostInfo.setFinishDate(costResult.getEndTime());
                    buildCostInfo.setCarrierId(costResult.getCarrierId());
                    buildCostInfo.setClientId(costResult.getClientId());
                    buildCostInfo.setCompanyId(costResult.getCompanyId());
                    buildCostInfo.setQuoterRuleId(costResult.getRuleMainPkId().toString());
                    buildCostInfo.setQuoterRuleName(costResult.getRuleMainName());
                    buildCostInfo.setQuoterRuleDetailId(costResult.getRuleDetailPkId().toString());
                    buildCostInfo.setQuoterRuleDetailName(costResult.getRuleDetailName());
                    buildCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
                    buildCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
                    buildCostInfo.setTotalBoxes(costResult.getTotalBoxes());
                    buildCostInfo.setTotalNumber(costResult.getTotalNumber());
                    buildCostInfo.setTotalWeight(costResult.getTotalWeight());
                    buildCostInfo.setTotalVolume(costResult.getTotalVolume());
                    buildCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
                    buildCostInfo.setTotalOverNum(costResult.getTotalOverNum());
                    buildCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
                    buildCostInfo.setSettleType(costResult.getSettleType());
                    buildCostInfo.setSettleEntityType(costResult.getSettleEntityType());
                    buildCostInfo.setSettleSetting(costResult.getSettleSetting());
                    buildCostInfo.setSettleMainId(costResult.getSettleMainId());
                    buildCostInfo.setSettleAmount(costResult.getCalculateResult());
                    buildCostInfo = CostUtil.setCostField(buildCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
                    buildCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
                    buildCostInfo.setCreateBy(userInfo.getEmployeecode().toString());
                    buildCostInfo.setCreateTime(now);
                    buildCostInfo.setOperBy(userInfo.getEmployeecode().toString());
                    buildCostInfo.setOperCode(userInfo.getEmployeecode().toString());
                    buildCostInfo.setOperTime(now);
                    buildCostInfo.setOptMonth(DateUtils.optMonth());
                    buildCostInfo.setOptDay(DateUtils.optDay());
                    insertCostInfoPOS.add(buildCostInfo);

                    //二次处理middle表，批涉及费用分摊
                    List<BmsYfexpensesMiddlePO> secondOptMiddles = new ArrayList<>();
                    Map<Integer, Dict> codeDataMap = costResult.getCodeDataMap();
                    for (Integer optCodePkId : costResult.getCodePkIds()) {

                        BmsYfexpensesMiddlePO buildCostMiddle = new BmsYfexpensesMiddlePO();
                        buildCostMiddle.setCodePkId(optCodePkId);
                        buildCostMiddle.setCodeType(costResult.getCodeType());
                        buildCostMiddle.setMainCodeId(costMainId);
                        buildCostMiddle.setShareType(costResult.getShareType());
                        buildCostMiddle.setOptMonth(DateUtils.optMonth());
                        buildCostMiddle.setOptDay(DateUtils.optDay());
                        if(codeDataMap.containsKey(optCodePkId)){
                            Dict dict = codeDataMap.get(optCodePkId);
                            BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) : BigDecimal.ZERO;
                            BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
                            BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
                            buildCostMiddle.setTotalWeight(totalWeight);
                            buildCostMiddle.setTotalVolume(totalVolume);
                            buildCostMiddle.setTotalNumber(totalNumber);
                        }
                        secondOptMiddles.add(buildCostMiddle);

                    }
                    secondOptMiddles = CostUtil.allocateCost(buildMainCostInfo,secondOptMiddles,StatisticsFieldEnum.getByValue(costResult.getShareType()),CostUtil.YF_COST_SETTLE_SUM_FIELDS,BmsYfexpensesMiddlePO.class);
                    insertMiddlePOS.addAll(secondOptMiddles);

                    //明细2纬度分摊
                    List<BmsYfexpensesMiddleSharePO> secondOptMiddlesSharePOS = new ArrayList<>();
                    Map<Integer, Dict> codeDetail2DataMap = costResult.getCodeDetail2DataMap();
                    if(CollUtil.isNotEmpty(costResult.getCodeDetail2PkIds())){
                        for (Integer optCodeDetail2PkId : costResult.getCodeDetail2PkIds()) {

                            BmsYfexpensesMiddleSharePO initMiddleSharePO = new BmsYfexpensesMiddleSharePO();
                            initMiddleSharePO.setCodePkId(optCodeDetail2PkId);
                            initMiddleSharePO.setMainCodeId(costMainId);
                            initMiddleSharePO.setShareType(costResult.getShareType());
                            initMiddleSharePO.setDelFlag(BmsConstants.DEL_FLAG_0);
                            initMiddleSharePO.setOptMonth(DateUtils.optMonth());
                            initMiddleSharePO.setOptDay(DateUtils.optDay());
                            if(codeDetail2DataMap.containsKey(optCodeDetail2PkId)){
                                Dict dict = codeDetail2DataMap.get(optCodeDetail2PkId);
                                BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) : BigDecimal.ZERO;
                                BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
                                BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
                                initMiddleSharePO.setTotalWeight(totalWeight);
                                initMiddleSharePO.setTotalVolume(totalVolume);
                                initMiddleSharePO.setTotalNumber(totalNumber);
                            }
                            secondOptMiddlesSharePOS.add(initMiddleSharePO);
                        }
                        List<BmsYfexpensesMiddleSharePO> middleSharePOS = CostUtil.allocateCost(buildMainCostInfo, secondOptMiddlesSharePOS, StatisticsFieldEnum.getByValue(costResult.getShareType()), CostUtil.YF_COST_SETTLE_SUM_FIELDS, BmsYfexpensesMiddleSharePO.class);
                        insertMiddleSharePOs.addAll(middleSharePOS);
                    }
                }

                // 计费过程
                if(costResult==null){
                    if(!codePkIds.isEmpty()){

                        for (Integer optCodePkId : codePkIds) {
                            BmsYfCostRecordPO buildRecord = new BmsYfCostRecordPO();
                            buildRecord.setCodePkId(optCodePkId);
                            buildRecord.setCodeType(codeResult.getCostResult().getCodeType());
                            buildRecord.setMainCodeId(optCostFlag ? costMainId : "");
                            buildRecord.setExpensesCode(optCostFlag ? mainExpenseCode : "" );
                            buildRecord.setCalculateFlag(optCostFlag ? BmsConstants.CALCULATE_FLAG_0 : BmsConstants.CALCULATE_FLAG_1);
                            buildRecord.setCalculateResult(codeResult.getCostResult().getCalculateResult());
                            buildRecord.setCalculateRemark(codeResult.getCostResult().getCalculateRemark());
                            buildRecord.setCalculateProcessResult(codeResult.getCostResult().getCalculateProcessResult());
                            buildRecord.setExpensesDimension(codeResult.getCostResult().getExpensesDimension());
                            buildRecord.setExpensesDimensionName(codeResult.getCostResult().getExpensesDimensionName());
                            buildRecord.setRuleDetailPkId(codeResult.getCostResult().getRuleDetailPkId());
                            buildRecord.setRuleDetailName(codeResult.getCostResult().getRuleDetailName());
                            buildRecord.setRuleMainPkId(codeResult.getCostResult().getRuleMainPkId());
                            buildRecord.setRuleMainName(codeResult.getCostResult().getRuleMainName());
                            buildRecord.setOperCode(userInfo.getEmployeecode().toString());
                            buildRecord.setOperBy(userInfo.getEmployeename());
                            buildRecord.setOperTime(now);
                            buildRecord.setOperDeptId(userInfo.getDepartmenid().intValue());
                            buildRecord.setDelFlag(BmsConstants.DEL_FLAG_0);
                            buildRecord.setOptMonth(DateUtils.optMonth());
                            buildRecord.setOptDay(DateUtils.optDay());
                            insertCostRecordPOS.add(buildRecord);
                        }
                    }
                }else {
                    BmsYfCostRecordPO buildRecord = new BmsYfCostRecordPO();
                    buildRecord.setCodeType(codeResult.getCostResult().getCodeType());
                    buildRecord.setMainCodeId(optCostFlag ? costMainId : "");
                    buildRecord.setExpensesCode(optCostFlag ? mainExpenseCode : "" );
                    buildRecord.setCalculateFlag(optCostFlag ? BmsConstants.CALCULATE_FLAG_0 : BmsConstants.CALCULATE_FLAG_1);
                    buildRecord.setCalculateResult(codeResult.getCostResult().getCalculateResult());
                    buildRecord.setCalculateRemark(codeResult.getCostResult().getCalculateRemark());
                    buildRecord.setCalculateProcessResult(codeResult.getCostResult().getCalculateProcessResult());
                    buildRecord.setExpensesDimension(codeResult.getCostResult().getExpensesDimension());
                    buildRecord.setExpensesDimensionName(codeResult.getCostResult().getExpensesDimensionName());
                    buildRecord.setRuleDetailPkId(codeResult.getCostResult().getRuleDetailPkId());
                    buildRecord.setRuleDetailName(codeResult.getCostResult().getRuleDetailName());
                    buildRecord.setRuleMainPkId(codeResult.getCostResult().getRuleMainPkId());
                    buildRecord.setRuleMainName(codeResult.getCostResult().getRuleMainName());
                    buildRecord.setOperCode(userInfo.getEmployeecode().toString());
                    buildRecord.setOperBy(userInfo.getEmployeename());
                    buildRecord.setOperTime(now);
                    buildRecord.setOperDeptId(userInfo.getDepartmenid().intValue());
                    buildRecord.setDelFlag(BmsConstants.DEL_FLAG_0);
                    buildRecord.setOptMonth(DateUtils.optMonth());
                    buildRecord.setOptDay(DateUtils.optDay());
                    insertCostRecordPOS.add(buildRecord);
                }

            }

            //单据计费成功的要从计费失败中去掉
            failCodePkIds = failCodePkIds.stream()
                    .filter(k->!successCodePkIds.contains(k))
                    .collect(Collectors.toSet());
            return CalculateYfDbResult.builder()
                    .codePkIds(Lists.newArrayList(codePkIds))
                    .successCodePkIds(Lists.newArrayList(successCodePkIds))
                    .failCodePkIds(Lists.newArrayList(failCodePkIds))
                    .insertCostMainInfoPOs(insertCostMainInfoPOS)
                    .insertCostInfoPOs(insertCostInfoPOS)
                    .insertMiddlePOs(insertMiddlePOS)
                    .insertMiddleSharePOs(insertMiddleSharePOs)
                    .insertCostRecordPOs(insertCostRecordPOS)
                    .build();
        });
    }
    /**
     * 应付月维度DB数据整合
     * @param param 单维度计费结果
     * @return 月维度所有的DB结果
     */
    @Async("asyncTaskExecutor")
    public CompletableFuture<CalculateYfDbResult> yfDbMonthFuture(List<CalculateResult> param, UserBean userInfo){

        return CompletableFuture.supplyAsync(()->{

            String tenantId = userInfo.getTenantid().toString();
            Date now = new Date();

            Set<Integer> codePkIds = new HashSet<>();
            Set<Integer> successCodePkIds = new HashSet<>();
            Set<Integer> failCodePkIds = new HashSet<>();

            List<BmsYfCostRecordPO> insertCostRecordPOS = new ArrayList<>();
            List<BmsYfcostMainInfoPO> insertCostMainInfoPOS = new ArrayList<>();
            List<BmsYfcostInfoPO> insertCostInfoPOS = new ArrayList<>();
            List<BmsYfexpensesMiddlePO> insertMiddlePOS = new ArrayList<>();
            List<BmsYfexpensesMiddleSharePO> insertMiddleSharePOs = new ArrayList<>();

            for (CalculateResult calculateResult : param) {

                CalculateCodeResult codeResult = calculateResult.getCodeResult();
                CalculateCostResult costResult = calculateResult.getCostResult();

                //单据相关
                boolean optCostFlag = false;
                if (!codeResult.getSuccessCodePkIds().isEmpty()) {

                    optCostFlag = true;
                    successCodePkIds.addAll(codeResult.getSuccessCodePkIds());
                    codePkIds.addAll(codeResult.getSuccessCodePkIds());
                }
                if(!codeResult.getFailCodePkIds().isEmpty()) {

                    optCostFlag = false;
                    failCodePkIds.addAll(codeResult.getFailCodePkIds());
                    codePkIds.addAll(codeResult.getFailCodePkIds());
                }

                //费用相关
                String costMainId = SnowFlowUtils.nextId();
                String mainExpenseCode = codeUtils.getCode(CodeCategory.YFMainExpenseCode, tenantId);
                if(costResult != null){

                    String costId = SnowFlowUtils.nextId();
                    String expenseCode = codeUtils.getCode(CodeCategory.YFExpensesCode, tenantId);

                    BmsYfcostMainInfoPO buildMainCostInfo = new BmsYfcostMainInfoPO();
                    buildMainCostInfo.setId(costMainId);
                    buildMainCostInfo.setExpensesCode(mainExpenseCode);
                    buildMainCostInfo.setExpensesType(costResult.getExpenseType());
                    buildMainCostInfo.setExpensesDimension(costResult.getExpensesDimension());
                    buildMainCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
                    buildMainCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
                    buildMainCostInfo.setBusinessTime(costResult.getBusinessTime());
                    buildMainCostInfo.setDispatchDate(costResult.getBeginTime());
                    buildMainCostInfo.setFinishDate(costResult.getEndTime());
                    buildMainCostInfo.setCarrierId(costResult.getCarrierId());
                    buildMainCostInfo.setClientId(costResult.getClientId());
                    buildMainCostInfo.setCompanyId(costResult.getCompanyId());
                    buildMainCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
                    buildMainCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
                    buildMainCostInfo.setTotalBoxes(costResult.getTotalBoxes());
                    buildMainCostInfo.setTotalNumber(costResult.getTotalNumber());
                    buildMainCostInfo.setTotalWeight(costResult.getTotalWeight());
                    buildMainCostInfo.setTotalVolume(costResult.getTotalVolume());
                    buildMainCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
                    buildMainCostInfo.setTotalOverNum(costResult.getTotalOverNum());
                    buildMainCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
                    buildMainCostInfo.setSettleType(costResult.getSettleType());
                    buildMainCostInfo.setSettleEntityType(costResult.getSettleEntityType());
                    buildMainCostInfo.setSettleSetting(costResult.getSettleSetting());
                    buildMainCostInfo.setSettleMainId(costResult.getSettleMainId());
                    buildMainCostInfo.setSettleAmount(costResult.getCalculateResult());
                    buildMainCostInfo = CostUtil.setCostField(buildMainCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
                    buildMainCostInfo.setCreateBy(userInfo.getEmployeename());
                    buildMainCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
                    buildMainCostInfo.setCreateTime(now);
                    buildMainCostInfo.setOperBy(userInfo.getEmployeename());
                    buildMainCostInfo.setOperCode(userInfo.getEmployeecode().toString());
                    buildMainCostInfo.setOperTime(now);
                    buildMainCostInfo.setOptMonth(DateUtils.optMonth());
                    buildMainCostInfo.setOptDay(DateUtils.optDay());
                    insertCostMainInfoPOS.add(buildMainCostInfo);

                    BmsYfcostInfoPO buildCostInfo = new BmsYfcostInfoPO();
                    buildCostInfo.setId(costId);
                    buildCostInfo.setExpensesCode(expenseCode);
                    buildCostInfo.setMainCodeId(costMainId);
                    buildCostInfo.setExpensesType(costResult.getExpenseType());
                    buildCostInfo.setExpensesDimension(costResult.getExpensesDimension());
                    buildCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
                    buildCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
                    buildCostInfo.setBusinessTime(costResult.getBusinessTime());
                    buildCostInfo.setBillDate(costResult.getBillDate());
                    buildCostInfo.setDispatchDate(costResult.getBeginTime());
                    buildCostInfo.setFinishDate(costResult.getEndTime());
                    buildCostInfo.setClientId(costResult.getClientId());
                    buildCostInfo.setCarrierId(costResult.getCarrierId());
                    buildCostInfo.setCompanyId(costResult.getCompanyId());
                    buildCostInfo.setQuoterRuleId(costResult.getRuleMainPkId().toString());
                    buildCostInfo.setQuoterRuleName(costResult.getRuleMainName());
                    buildCostInfo.setQuoterRuleDetailId(costResult.getRuleDetailPkId().toString());
                    buildCostInfo.setQuoterRuleDetailName(costResult.getRuleDetailName());
                    buildCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
                    buildCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
                    buildCostInfo.setTotalBoxes(costResult.getTotalBoxes());
                    buildCostInfo.setTotalNumber(costResult.getTotalNumber());
                    buildCostInfo.setTotalWeight(costResult.getTotalWeight());
                    buildCostInfo.setTotalVolume(costResult.getTotalVolume());
                    buildCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
                    buildCostInfo.setTotalOverNum(costResult.getTotalOverNum());
                    buildCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
                    buildCostInfo.setSettleType(costResult.getSettleType());
                    buildCostInfo.setSettleEntityType(costResult.getSettleEntityType());
                    buildCostInfo.setSettleSetting(costResult.getSettleSetting());
                    buildCostInfo.setSettleMainId(costResult.getSettleMainId());
                    buildCostInfo.setSettleAmount(costResult.getCalculateResult());
                    buildCostInfo = CostUtil.setCostField(buildCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
                    buildCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
                    buildCostInfo.setCreateBy(userInfo.getEmployeecode().toString());
                    buildCostInfo.setCreateTime(now);
                    buildCostInfo.setOperBy(userInfo.getEmployeecode().toString());
                    buildCostInfo.setOperCode(userInfo.getEmployeecode().toString());
                    buildCostInfo.setOperTime(now);
                    buildCostInfo.setOptMonth(DateUtils.optMonth());
                    buildCostInfo.setOptDay(DateUtils.optDay());
                    insertCostInfoPOS.add(buildCostInfo);

                    //二次处理middle表，批涉及费用分摊
                    List<BmsYfexpensesMiddlePO> secondOptMiddles = new ArrayList<>();
                    Map<Integer, Dict> codeDataMap = costResult.getCodeDataMap();
                    for (Integer optCodePkId : costResult.getCodePkIds()) {

                        BmsYfexpensesMiddlePO buildCostMiddle = new BmsYfexpensesMiddlePO();
                        buildCostMiddle.setCodePkId(optCodePkId);
                        buildCostMiddle.setCodeType(costResult.getCodeType());
                        buildCostMiddle.setMainCodeId(costMainId);
                        buildCostMiddle.setShareType(costResult.getShareType());
                        buildCostMiddle.setOptMonth(DateUtils.optMonth());
                        buildCostMiddle.setOptDay(DateUtils.optDay());
                        if(codeDataMap.containsKey(optCodePkId)){
                            Dict dict = codeDataMap.get(optCodePkId);
                            BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName())?dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()):BigDecimal.ZERO;
                            BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
                            BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
                            buildCostMiddle.setTotalWeight(totalWeight);
                            buildCostMiddle.setTotalVolume(totalVolume);
                            buildCostMiddle.setTotalNumber(totalNumber);
                        }
                        secondOptMiddles.add(buildCostMiddle);
                    }
                    secondOptMiddles = CostUtil.allocateCost(buildMainCostInfo,secondOptMiddles,StatisticsFieldEnum.getByValue(costResult.getShareType()),CostUtil.YF_COST_SETTLE_SUM_FIELDS,BmsYfexpensesMiddlePO.class);
                    insertMiddlePOS.addAll(secondOptMiddles);

                    //明细2纬度分摊
                    if(CollUtil.isNotEmpty(costResult.getCodeDetail2PkIds())){

                        Map<Integer, Dict> codeDetail2DataMap = costResult.getCodeDetail2DataMap();
                        List<BmsYfexpensesMiddleSharePO> secondOptMiddlesSharePOS = new ArrayList<>();
                        for (Integer optCodeDetail2PkId : costResult.getCodeDetail2PkIds()) {
                            BmsYfexpensesMiddleSharePO initMiddleSharePO = new BmsYfexpensesMiddleSharePO();
                            initMiddleSharePO.setCodePkId(optCodeDetail2PkId);
                            initMiddleSharePO.setMainCodeId(costMainId);
                            initMiddleSharePO.setShareType(costResult.getShareType());
                            initMiddleSharePO.setDelFlag(BmsConstants.DEL_FLAG_0);
                            initMiddleSharePO.setOptMonth(DateUtils.optMonth());
                            initMiddleSharePO.setOptDay(DateUtils.optDay());
                            if(codeDetail2DataMap.containsKey(optCodeDetail2PkId)){
                                Dict dict = codeDetail2DataMap.get(optCodeDetail2PkId);
                                BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName())?dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()):BigDecimal.ZERO;
                                BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
                                BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ? dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
                                initMiddleSharePO.setTotalWeight(totalWeight);
                                initMiddleSharePO.setTotalVolume(totalVolume);
                                initMiddleSharePO.setTotalNumber(totalNumber);
                            }
                            secondOptMiddlesSharePOS.add(initMiddleSharePO);
                        }
                        List<BmsYfexpensesMiddleSharePO> middleSharePOS = CostUtil.allocateCost(buildMainCostInfo, secondOptMiddlesSharePOS, StatisticsFieldEnum.getByValue(costResult.getShareType()), CostUtil.YF_COST_SETTLE_SUM_FIELDS, BmsYfexpensesMiddleSharePO.class);
                        insertMiddleSharePOs.addAll(middleSharePOS);
                    }
                }

                // 计费过程
                if(costResult==null){
                    if(!codePkIds.isEmpty()){

                        for (Integer optCodePkId : codePkIds) {
                            BmsYfCostRecordPO buildRecord = new BmsYfCostRecordPO();
                            buildRecord.setCodePkId(optCodePkId);
                            buildRecord.setCodeType(codeResult.getCostResult().getCodeType());
                            buildRecord.setMainCodeId(optCostFlag ? costMainId : "");
                            buildRecord.setExpensesCode(optCostFlag ? mainExpenseCode : "");
                            buildRecord.setCalculateFlag(optCostFlag ? BmsConstants.CALCULATE_FLAG_0 : BmsConstants.CALCULATE_FLAG_1);
                            buildRecord.setCalculateResult(codeResult.getCostResult().getCalculateResult());
                            buildRecord.setCalculateRemark(codeResult.getCostResult().getCalculateRemark());
                            buildRecord.setCalculateProcessResult(codeResult.getCostResult().getCalculateProcessResult());
                            buildRecord.setExpensesDimension(codeResult.getCostResult().getExpensesDimension());
                            buildRecord.setExpensesDimensionName(codeResult.getCostResult().getExpensesDimensionName());
                            buildRecord.setRuleDetailPkId(codeResult.getCostResult().getRuleDetailPkId());
                            buildRecord.setRuleDetailName(codeResult.getCostResult().getRuleDetailName());
                            buildRecord.setRuleMainPkId(codeResult.getCostResult().getRuleMainPkId());
                            buildRecord.setRuleMainName(codeResult.getCostResult().getRuleMainName());
                            buildRecord.setOperCode(userInfo.getEmployeecode().toString());
                            buildRecord.setOperBy(userInfo.getEmployeename());
                            buildRecord.setOperTime(now);
                            buildRecord.setOperDeptId(userInfo.getDepartmenid().intValue());
                            buildRecord.setDelFlag(BmsConstants.DEL_FLAG_0);
                            buildRecord.setOptMonth(DateUtils.optMonth());
                            buildRecord.setOptDay(DateUtils.optDay());
                            insertCostRecordPOS.add(buildRecord);
                        }
                    }
                }else {
                    BmsYfCostRecordPO buildRecord = new BmsYfCostRecordPO();
                    buildRecord.setCodeType(codeResult.getCostResult().getCodeType());
                    buildRecord.setMainCodeId(optCostFlag ? costMainId : "");
                    buildRecord.setExpensesCode(optCostFlag ? mainExpenseCode : "");
                    buildRecord.setCalculateFlag(optCostFlag ? BmsConstants.CALCULATE_FLAG_0 : BmsConstants.CALCULATE_FLAG_1);
                    buildRecord.setCalculateResult(codeResult.getCostResult().getCalculateResult());
                    buildRecord.setCalculateRemark(codeResult.getCostResult().getCalculateRemark());
                    buildRecord.setCalculateProcessResult(codeResult.getCostResult().getCalculateProcessResult());
                    buildRecord.setExpensesDimension(codeResult.getCostResult().getExpensesDimension());
                    buildRecord.setExpensesDimensionName(codeResult.getCostResult().getExpensesDimensionName());
                    buildRecord.setRuleDetailPkId(codeResult.getCostResult().getRuleDetailPkId());
                    buildRecord.setRuleDetailName(codeResult.getCostResult().getRuleDetailName());
                    buildRecord.setRuleMainPkId(codeResult.getCostResult().getRuleMainPkId());
                    buildRecord.setRuleMainName(codeResult.getCostResult().getRuleMainName());
                    buildRecord.setOperCode(userInfo.getEmployeecode().toString());
                    buildRecord.setOperBy(userInfo.getEmployeename());
                    buildRecord.setOperTime(now);
                    buildRecord.setOperDeptId(userInfo.getDepartmenid().intValue());
                    buildRecord.setDelFlag(BmsConstants.DEL_FLAG_0);
                    buildRecord.setOptMonth(DateUtils.optMonth());
                    buildRecord.setOptDay(DateUtils.optDay());
                    insertCostRecordPOS.add(buildRecord);
                }
            }

            //单据计费成功的要从计费失败中去掉
            failCodePkIds = failCodePkIds.stream()
                    .filter(k->!successCodePkIds.contains(k))
                    .collect(Collectors.toSet());
            return CalculateYfDbResult.builder()
                    .codePkIds(Lists.newArrayList(codePkIds))
                    .successCodePkIds(Lists.newArrayList(successCodePkIds))
                    .failCodePkIds(Lists.newArrayList(failCodePkIds))
                    .insertCostMainInfoPOs(insertCostMainInfoPOS)
                    .insertCostInfoPOs(insertCostInfoPOS)
                    .insertMiddlePOs(insertMiddlePOS)
                    .insertMiddleSharePOs(insertMiddleSharePOs)
                    .insertCostRecordPOs(insertCostRecordPOS)
                    .build();
        });


    }


    @Override
    public void calculateDataDalibration(Integer handlerType, List<Integer> codePkIds) {
        calculateBiz.calculateDataDalibration(handlerType,codePkIds);
    }

    /**
     * 根据GroovyCalculateExtraType构建应付费用信息
     *
     * @param costResult 计费结果
     * @param costMainId 主费用ID
     * @param mainExpenseCode 主费用编码
     * @param costId 费用ID
     * @param expenseCode 费用编码
     * @param optCodePkId 单据主键ID
     * @param optCodeDetail2PkIds 作业单主键ID集合
     * @param extraType 额外计费类型
     * @param userInfo 用户信息
     * @param now 当前时间
     * @param insertMainMap 主费用信息Map
     * @param insertCostMap 费用信息Map
     * @param insertMiddleMap 中间表Map
     * @param insertMiddleShareMap 分摊表Map
     * @param allCodeDetail2DataMap 所有作业单数据Map
     */
    private void buildYfCostInfo(CalculateCostResult costResult, String costMainId, String mainExpenseCode,
                                String costId, String expenseCode, Integer optCodePkId, Set<Integer> optCodeDetail2PkIds,
                                GroovyCalculateExtraType extraType, UserBean userInfo, Date now,
                                Map<Integer, List<BmsYfcostMainInfoPO>> insertMainMap,
                                Map<Integer, List<BmsYfcostInfoPO>> insertCostMap,
                                Map<Integer, List<BmsYfexpensesMiddlePO>> insertMiddleMap,
                                Map<Integer, List<BmsYfexpensesMiddleSharePO>> insertMiddleShareMap,
                                Map<Integer, Dict> allCodeDetail2DataMap) {

        log.info("构建应付费用信息，extraType: {}, optCodePkId: {}", extraType.getName(), optCodePkId);

        switch (extraType) {
            case DEFAULT:
                // 默认方式构建费用信息
                buildDefaultYfCostInfo(costResult, costMainId, mainExpenseCode, costId, expenseCode,
                                     optCodePkId, optCodeDetail2PkIds, userInfo, now, insertMainMap,
                                     insertCostMap, insertMiddleMap, insertMiddleShareMap, allCodeDetail2DataMap);
                break;
            case SCHEDULT_JOB:
                // 调度单维度计费，按照作业单维度分摊
                buildScheduleJobYfCostInfo(costResult, costMainId, mainExpenseCode, costId, expenseCode,
                                         optCodePkId, optCodeDetail2PkIds, userInfo, now, insertMainMap,
                                         insertCostMap, insertMiddleMap, insertMiddleShareMap, allCodeDetail2DataMap);
                break;
            default:
                log.warn("未知的extraType: {}, 使用默认方式处理", extraType);
                buildDefaultYfCostInfo(costResult, costMainId, mainExpenseCode, costId, expenseCode,
                                     optCodePkId, optCodeDetail2PkIds, userInfo, now, insertMainMap,
                                     insertCostMap, insertMiddleMap, insertMiddleShareMap, allCodeDetail2DataMap);
                break;
        }
    }

    /**
     * 默认方式构建应付费用信息
     */
    private void buildDefaultYfCostInfo(CalculateCostResult costResult, String costMainId, String mainExpenseCode,
                                       String costId, String expenseCode, Integer optCodePkId, Set<Integer> optCodeDetail2PkIds,
                                       UserBean userInfo, Date now,
                                       Map<Integer, List<BmsYfcostMainInfoPO>> insertMainMap,
                                       Map<Integer, List<BmsYfcostInfoPO>> insertCostMap,
                                       Map<Integer, List<BmsYfexpensesMiddlePO>> insertMiddleMap,
                                       Map<Integer, List<BmsYfexpensesMiddleSharePO>> insertMiddleShareMap,
                                       Map<Integer, Dict> allCodeDetail2DataMap) {

        // 构建主费用信息
        BmsYfcostMainInfoPO buildMainCostInfo = new BmsYfcostMainInfoPO();
        buildMainCostInfo.setId(costMainId);
        buildMainCostInfo.setExpensesCode(mainExpenseCode);
        buildMainCostInfo.setExpensesType(costResult.getExpenseType());
        buildMainCostInfo.setExpensesDimension(costResult.getExpensesDimension());
        buildMainCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
        buildMainCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
        buildMainCostInfo.setBusinessTime(costResult.getBusinessTime());
        buildMainCostInfo.setDispatchDate(costResult.getBeginTime());
        buildMainCostInfo.setFinishDate(costResult.getEndTime());
        buildMainCostInfo.setCarrierId(costResult.getCarrierId());
        buildMainCostInfo.setClientId(costResult.getClientId());
        buildMainCostInfo.setCompanyId(costResult.getCompanyId());
        buildMainCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
        buildMainCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
        buildMainCostInfo.setTotalBoxes(costResult.getTotalBoxes());
        buildMainCostInfo.setTotalNumber(costResult.getTotalNumber());
        buildMainCostInfo.setTotalWeight(costResult.getTotalWeight());
        buildMainCostInfo.setTotalVolume(costResult.getTotalVolume());
        buildMainCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
        buildMainCostInfo.setTotalOverNum(costResult.getTotalOverNum());
        buildMainCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
        buildMainCostInfo.setSettleType(costResult.getSettleType());
        buildMainCostInfo.setSettleEntityType(costResult.getSettleEntityType());
        buildMainCostInfo.setSettleSetting(costResult.getSettleSetting());
        buildMainCostInfo.setSettleMainId(costResult.getSettleMainId());
        buildMainCostInfo.setSettleAmount(costResult.getCalculateResult());
        buildMainCostInfo = CostUtil.setCostField(buildMainCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
        buildMainCostInfo.setCreateBy(userInfo.getEmployeename());
        buildMainCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
        buildMainCostInfo.setCreateTime(now);
        buildMainCostInfo.setOperBy(userInfo.getEmployeename());
        buildMainCostInfo.setOperCode(userInfo.getEmployeecode().toString());
        buildMainCostInfo.setOperTime(now);
        buildMainCostInfo.setOptMonth(DateUtils.optMonth());
        buildMainCostInfo.setOptDay(DateUtils.optDay());

        // 添加到主费用信息Map
        if(insertMainMap.containsKey(optCodePkId)){
            List<BmsYfcostMainInfoPO> mainInfoPOS = insertMainMap.get(optCodePkId);
            mainInfoPOS.add(buildMainCostInfo);
            insertMainMap.put(optCodePkId, mainInfoPOS);
        } else {
            insertMainMap.put(optCodePkId, Lists.newArrayList(buildMainCostInfo));
        }

        // 构建费用信息
        BmsYfcostInfoPO buildCostInfo = new BmsYfcostInfoPO();
        buildCostInfo.setId(costId);
        buildCostInfo.setExpensesCode(expenseCode);
        buildCostInfo.setMainCodeId(costMainId);
        buildCostInfo.setExpensesType(costResult.getExpenseType());
        buildCostInfo.setExpensesDimension(costResult.getExpensesDimension());
        buildCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
        buildCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
        buildCostInfo.setBusinessTime(costResult.getBusinessTime());
        buildCostInfo.setBillDate(costResult.getBillDate());
        buildCostInfo.setDispatchDate(costResult.getBeginTime());
        buildCostInfo.setFinishDate(costResult.getEndTime());
        buildCostInfo.setClientId(costResult.getClientId());
        buildCostInfo.setCarrierId(costResult.getCarrierId());
        buildCostInfo.setCompanyId(costResult.getCompanyId());
        buildCostInfo.setWarehouseCode(costResult.getWarehouseCode());
        buildCostInfo.setWarehouseName(costResult.getWarehouseName());
        buildCostInfo.setQuoterRuleId(costResult.getRuleMainPkId().toString());
        buildCostInfo.setQuoterRuleName(costResult.getRuleMainName());
        buildCostInfo.setQuoterRuleDetailId(costResult.getRuleDetailPkId().toString());
        buildCostInfo.setQuoterRuleDetailName(costResult.getRuleDetailName());
        buildCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
        buildCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
        buildCostInfo.setTotalBoxes(costResult.getTotalBoxes());
        buildCostInfo.setTotalNumber(costResult.getTotalNumber());
        buildCostInfo.setTotalWeight(costResult.getTotalWeight());
        buildCostInfo.setTotalVolume(costResult.getTotalVolume());
        buildCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
        buildCostInfo.setTotalOverNum(costResult.getTotalOverNum());
        buildCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
        buildCostInfo.setSettleType(costResult.getSettleType());
        buildCostInfo.setSettleEntityType(costResult.getSettleEntityType());
        buildCostInfo.setSettleSetting(costResult.getSettleSetting());
        buildCostInfo.setSettleMainId(costResult.getSettleMainId());
        buildCostInfo.setSettleAmount(costResult.getCalculateResult());
        buildCostInfo = CostUtil.setCostField(buildCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
        buildCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
        buildCostInfo.setCreateBy(userInfo.getEmployeecode().toString());
        buildCostInfo.setCreateTime(now);
        buildCostInfo.setOperBy(userInfo.getEmployeecode().toString());
        buildCostInfo.setOperCode(userInfo.getEmployeecode().toString());
        buildCostInfo.setOperTime(now);
        buildCostInfo.setOptMonth(DateUtils.optMonth());
        buildCostInfo.setOptDay(DateUtils.optDay());

        // 添加到费用信息Map
        if(insertCostMap.containsKey(optCodePkId)){
            List<BmsYfcostInfoPO> costInfos = insertCostMap.get(optCodePkId);
            costInfos.add(buildCostInfo);
            insertCostMap.put(optCodePkId, costInfos);
        } else {
            insertCostMap.put(optCodePkId, Lists.newArrayList(buildCostInfo));
        }

        // 构建中间表信息
        Map<Integer, Dict> codeDataMap = costResult.getCodeDataMap();
        BmsYfexpensesMiddlePO buildCostMiddle = new BmsYfexpensesMiddlePO();
        buildCostMiddle.setCodePkId(optCodePkId);
        buildCostMiddle.setCodeType(costResult.getCodeType());
        buildCostMiddle.setMainCodeId(costMainId);
        buildCostMiddle.setShareType(costResult.getShareType());
        buildCostMiddle.setSettleAmount(costResult.getCalculateResult());
        buildCostMiddle.setOptMonth(DateUtils.optMonth());
        buildCostMiddle.setOptDay(DateUtils.optDay());

        if(codeDataMap.containsKey(optCodePkId)){
            Dict dict = codeDataMap.get(optCodePkId);
            BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) ?
                dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) : BigDecimal.ZERO;
            BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ?
                dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
            BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ?
                dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
            buildCostMiddle.setTotalWeight(totalWeight);
            buildCostMiddle.setTotalVolume(totalVolume);
            buildCostMiddle.setTotalNumber(totalNumber);
        }
        buildCostMiddle = CostUtil.setCostField(buildCostMiddle, costResult.getFeeType(), costResult.getCalculateResult());

        // 添加到中间表Map
        if(insertMiddleMap.containsKey(optCodePkId)){
            List<BmsYfexpensesMiddlePO> middlePOList = insertMiddleMap.get(optCodePkId);
            middlePOList.add(buildCostMiddle);
            insertMiddleMap.put(optCodePkId, middlePOList);
        } else {
            insertMiddleMap.put(optCodePkId, Lists.newArrayList(buildCostMiddle));
        }

        // 构建分摊表信息（如果有作业单明细）
        if(CollUtil.isNotEmpty(optCodeDetail2PkIds)){
            Map<Integer, Dict> codeDetail2DataMap = costResult.getCodeDetail2DataMap();
            allCodeDetail2DataMap.putAll(codeDetail2DataMap);
            List<BmsYfexpensesMiddleSharePO> buildSharePOList = Lists.newArrayList();

            for (Integer optCodeDetail2PkId : optCodeDetail2PkIds) {
                BmsYfexpensesMiddleSharePO buildShareData = new BmsYfexpensesMiddleSharePO();
                buildShareData.setCodePkId(optCodeDetail2PkId);
                buildShareData.setMainCodeId(costMainId);
                buildShareData.setShareType(costResult.getShareType());
                buildShareData.setOptMonth(DateUtils.optMonth());
                buildShareData.setOptDay(DateUtils.optDay());

                if(codeDetail2DataMap.containsKey(optCodeDetail2PkId)){
                    Dict dict = codeDetail2DataMap.get(optCodeDetail2PkId);
                    BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) ?
                        dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) : BigDecimal.ZERO;
                    BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ?
                        dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
                    BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ?
                        dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
                    buildShareData.setTotalWeight(totalWeight);
                    buildShareData.setTotalVolume(totalVolume);
                    buildShareData.setTotalNumber(totalNumber);
                }
                buildSharePOList.add(buildShareData);
            }

            // 使用CostUtil进行费用分摊
            buildSharePOList = CostUtil.allocateCost(buildMainCostInfo, buildSharePOList,
                StatisticsFieldEnum.getByValue(costResult.getShareType()),
                CostUtil.YF_COST_SETTLE_SUM_FIELDS, BmsYfexpensesMiddleSharePO.class);

            // 添加到分摊表Map
            if(insertMiddleShareMap.containsKey(optCodePkId)){
                List<BmsYfexpensesMiddleSharePO> middleSharePOS = insertMiddleShareMap.get(optCodePkId);
                middleSharePOS.addAll(buildSharePOList);
                insertMiddleShareMap.put(optCodePkId, middleSharePOS);
            } else {
                insertMiddleShareMap.put(optCodePkId, Lists.newArrayList(buildSharePOList));
            }
        }
    }

    /**
     * 调度单维度计费，按照作业单维度分摊
     */
    private void buildScheduleJobYfCostInfo(CalculateCostResult costResult, String costMainId, String mainExpenseCode,
                                           String costId, String expenseCode, Integer optCodePkId, Set<Integer> optCodeDetail2PkIds,
                                           UserBean userInfo, Date now,
                                           Map<Integer, List<BmsYfcostMainInfoPO>> insertMainMap,
                                           Map<Integer, List<BmsYfcostInfoPO>> insertCostMap,
                                           Map<Integer, List<BmsYfexpensesMiddlePO>> insertMiddleMap,
                                           Map<Integer, List<BmsYfexpensesMiddleSharePO>> insertMiddleShareMap,
                                           Map<Integer, Dict> allCodeDetail2DataMap) {

        log.info("调度单维度计费，按照作业单维度分摊，optCodePkId: {}", optCodePkId);

        // 检查extraInfo是否有值
        Map<String, Object> extraInfo = costResult.getExtraInfo();
        if (extraInfo == null || extraInfo.isEmpty()) {
            log.warn("extraInfo为空，使用默认方式处理");
            buildDefaultYfCostInfo(costResult, costMainId, mainExpenseCode, costId, expenseCode,
                                 optCodePkId, optCodeDetail2PkIds, userInfo, now, insertMainMap,
                                 insertCostMap, insertMiddleMap, insertMiddleShareMap, allCodeDetail2DataMap);
            return;
        }

        // 先构建分摊表信息（根据extraInfo）
        List<BmsYfexpensesMiddleSharePO> buildSharePOList = buildJobShareCostInfo(
            costResult, costMainId, optCodeDetail2PkIds, extraInfo, allCodeDetail2DataMap);

        if (CollUtil.isEmpty(buildSharePOList)) {
            log.warn("构建作业单分摊信息失败，使用默认方式处理");
            buildDefaultYfCostInfo(costResult, costMainId, mainExpenseCode, costId, expenseCode,
                                 optCodePkId, optCodeDetail2PkIds, userInfo, now, insertMainMap,
                                 insertCostMap, insertMiddleMap, insertMiddleShareMap, allCodeDetail2DataMap);
            return;
        }

        // 向上汇总到主费用信息和费用信息
        BmsYfcostMainInfoPO buildMainCostInfo = buildMainCostInfoFromShare(
            costResult, costMainId, mainExpenseCode, buildSharePOList, userInfo, now);
        BmsYfcostInfoPO buildCostInfo = buildCostInfoFromShare(
            costResult, costId, expenseCode, costMainId, buildSharePOList, userInfo, now);
        BmsYfexpensesMiddlePO buildCostMiddle = buildMiddleCostInfoFromShare(
            costResult, costMainId, optCodePkId, buildSharePOList);

        // 添加到相应的Map中
        addToMaps(optCodePkId, buildMainCostInfo, buildCostInfo, buildCostMiddle, buildSharePOList,
                 insertMainMap, insertCostMap, insertMiddleMap, insertMiddleShareMap);
    }

    /**
     * 根据extraInfo构建作业单分摊费用信息
     */
    private List<BmsYfexpensesMiddleSharePO> buildJobShareCostInfo(CalculateCostResult costResult, String costMainId,
                                                                 Set<Integer> optCodeDetail2PkIds, Map<String, Object> extraInfo,
                                                                 Map<Integer, Dict> allCodeDetail2DataMap) {

        List<BmsYfexpensesMiddleSharePO> buildSharePOList = Lists.newArrayList();
        Map<Integer, Dict> codeDetail2DataMap = costResult.getCodeDetail2DataMap();
        allCodeDetail2DataMap.putAll(codeDetail2DataMap);

        for (Integer optCodeDetail2PkId : optCodeDetail2PkIds) {
            BmsYfexpensesMiddleSharePO buildShareData = new BmsYfexpensesMiddleSharePO();
            buildShareData.setCodePkId(optCodeDetail2PkId);
            buildShareData.setMainCodeId(costMainId);
            buildShareData.setShareType(costResult.getShareType());
            buildShareData.setOptMonth(DateUtils.optMonth());
            buildShareData.setOptDay(DateUtils.optDay());

            // 设置统计字段（重量、体积、件数）
            if(codeDetail2DataMap.containsKey(optCodeDetail2PkId)){
                Dict dict = codeDetail2DataMap.get(optCodeDetail2PkId);
                BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) ?
                    dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) : BigDecimal.ZERO;
                BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ?
                    dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
                BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ?
                    dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
                buildShareData.setTotalWeight(totalWeight);
                buildShareData.setTotalVolume(totalVolume);
                buildShareData.setTotalNumber(totalNumber);
            }

            // 根据extraInfo设置费用信息（key为作业单号，value为该作业单的费用）
            String jobCode = getJobCodeByPkId(optCodeDetail2PkId, codeDetail2DataMap);
            if (jobCode != null && extraInfo.containsKey(jobCode)) {
                Object jobCostObj = extraInfo.get(jobCode);
                BigDecimal jobCost = convertToBigDecimal(jobCostObj);
                if (jobCost != null) {
                    // 设置结算金额
                    buildShareData.setSettleAmount(jobCost);
                    // 根据费用类型设置对应的费用字段
                    buildShareData = CostUtil.setCostField(buildShareData, costResult.getFeeType(), jobCost);
                } else {
                    log.warn("作业单 {} 的费用值无法转换为BigDecimal: {}", jobCode, jobCostObj);
                    buildShareData.setSettleAmount(BigDecimal.ZERO);
                }
            } else {
                log.warn("作业单 {} 在extraInfo中未找到对应费用", jobCode);
                buildShareData.setSettleAmount(BigDecimal.ZERO);
            }

            buildSharePOList.add(buildShareData);
        }

        return buildSharePOList;
    }

    /**
     * 根据作业单主键ID获取作业单号
     */
    private String getJobCodeByPkId(Integer codePkId, Map<Integer, Dict> codeDetail2DataMap) {
        if (codeDetail2DataMap.containsKey(codePkId)) {
            Dict dict = codeDetail2DataMap.get(codePkId);
            // 假设作业单号字段名为 "jobCode" 或 "relateCode"
            if (dict.containsKey("jobCode")) {
                return dict.getStr("jobCode");
            } else if (dict.containsKey("relateCode")) {
                return dict.getStr("relateCode");
            }
        }
        // 如果没有找到，返回主键ID的字符串形式作为备用
        return codePkId.toString();
    }

    /**
     * 将Object转换为BigDecimal
     */
    private BigDecimal convertToBigDecimal(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为BigDecimal: {}", value);
                return null;
            }
        }
        return null;
    }

    /**
     * 从分摊信息汇总构建主费用信息
     */
    private BmsYfcostMainInfoPO buildMainCostInfoFromShare(CalculateCostResult costResult, String costMainId,
                                                          String mainExpenseCode, List<BmsYfexpensesMiddleSharePO> shareList,
                                                          UserBean userInfo, Date now) {

        BmsYfcostMainInfoPO buildMainCostInfo = new BmsYfcostMainInfoPO();
        buildMainCostInfo.setId(costMainId);
        buildMainCostInfo.setExpensesCode(mainExpenseCode);
        buildMainCostInfo.setExpensesType(costResult.getExpenseType());
        buildMainCostInfo.setExpensesDimension(costResult.getExpensesDimension());
        buildMainCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
        buildMainCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
        buildMainCostInfo.setBusinessTime(costResult.getBusinessTime());
        buildMainCostInfo.setDispatchDate(costResult.getBeginTime());
        buildMainCostInfo.setFinishDate(costResult.getEndTime());
        buildMainCostInfo.setCarrierId(costResult.getCarrierId());
        buildMainCostInfo.setClientId(costResult.getClientId());
        buildMainCostInfo.setCompanyId(costResult.getCompanyId());
        buildMainCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
        buildMainCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
        buildMainCostInfo.setTotalBoxes(costResult.getTotalBoxes());
        buildMainCostInfo.setTotalNumber(costResult.getTotalNumber());
        buildMainCostInfo.setTotalWeight(costResult.getTotalWeight());
        buildMainCostInfo.setTotalVolume(costResult.getTotalVolume());
        buildMainCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
        buildMainCostInfo.setTotalOverNum(costResult.getTotalOverNum());
        buildMainCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
        buildMainCostInfo.setSettleType(costResult.getSettleType());
        buildMainCostInfo.setSettleEntityType(costResult.getSettleEntityType());
        buildMainCostInfo.setSettleSetting(costResult.getSettleSetting());
        buildMainCostInfo.setSettleMainId(costResult.getSettleMainId());
        buildMainCostInfo.setCreateBy(userInfo.getEmployeename());
        buildMainCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
        buildMainCostInfo.setCreateTime(now);
        buildMainCostInfo.setOperBy(userInfo.getEmployeename());
        buildMainCostInfo.setOperCode(userInfo.getEmployeecode().toString());
        buildMainCostInfo.setOperTime(now);
        buildMainCostInfo.setOptMonth(DateUtils.optMonth());
        buildMainCostInfo.setOptDay(DateUtils.optDay());

        // 使用CostUtil汇总分摊表的费用到主费用信息
        buildMainCostInfo = CostUtil.aggregateCost(buildMainCostInfo, shareList,
            BmsYfcostMainInfoPO.class, CostUtil.YF_COST_SETTLE_SUM_FIELDS);

        return buildMainCostInfo;
    }

    /**
     * 从分摊信息汇总构建费用信息
     */
    private BmsYfcostInfoPO buildCostInfoFromShare(CalculateCostResult costResult, String costId, String expenseCode,
                                                  String costMainId, List<BmsYfexpensesMiddleSharePO> shareList,
                                                  UserBean userInfo, Date now) {

        BmsYfcostInfoPO buildCostInfo = new BmsYfcostInfoPO();
        buildCostInfo.setId(costId);
        buildCostInfo.setExpensesCode(expenseCode);
        buildCostInfo.setMainCodeId(costMainId);
        buildCostInfo.setExpensesType(costResult.getExpenseType());
        buildCostInfo.setExpensesDimension(costResult.getExpensesDimension());
        buildCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
        buildCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
        buildCostInfo.setBusinessTime(costResult.getBusinessTime());
        buildCostInfo.setBillDate(costResult.getBillDate());
        buildCostInfo.setDispatchDate(costResult.getBeginTime());
        buildCostInfo.setFinishDate(costResult.getEndTime());
        buildCostInfo.setClientId(costResult.getClientId());
        buildCostInfo.setCarrierId(costResult.getCarrierId());
        buildCostInfo.setCompanyId(costResult.getCompanyId());
        buildCostInfo.setWarehouseCode(costResult.getWarehouseCode());
        buildCostInfo.setWarehouseName(costResult.getWarehouseName());
        buildCostInfo.setQuoterRuleId(costResult.getRuleMainPkId().toString());
        buildCostInfo.setQuoterRuleName(costResult.getRuleMainName());
        buildCostInfo.setQuoterRuleDetailId(costResult.getRuleDetailPkId().toString());
        buildCostInfo.setQuoterRuleDetailName(costResult.getRuleDetailName());
        buildCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
        buildCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
        buildCostInfo.setTotalBoxes(costResult.getTotalBoxes());
        buildCostInfo.setTotalNumber(costResult.getTotalNumber());
        buildCostInfo.setTotalWeight(costResult.getTotalWeight());
        buildCostInfo.setTotalVolume(costResult.getTotalVolume());
        buildCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
        buildCostInfo.setTotalOverNum(costResult.getTotalOverNum());
        buildCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
        buildCostInfo.setSettleType(costResult.getSettleType());
        buildCostInfo.setSettleEntityType(costResult.getSettleEntityType());
        buildCostInfo.setSettleSetting(costResult.getSettleSetting());
        buildCostInfo.setSettleMainId(costResult.getSettleMainId());
        buildCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
        buildCostInfo.setCreateBy(userInfo.getEmployeecode().toString());
        buildCostInfo.setCreateTime(now);
        buildCostInfo.setOperBy(userInfo.getEmployeecode().toString());
        buildCostInfo.setOperCode(userInfo.getEmployeecode().toString());
        buildCostInfo.setOperTime(now);
        buildCostInfo.setOptMonth(DateUtils.optMonth());
        buildCostInfo.setOptDay(DateUtils.optDay());

        // 使用CostUtil汇总分摊表的费用到费用信息
        buildCostInfo = CostUtil.aggregateCost(buildCostInfo, shareList,
            BmsYfcostInfoPO.class, CostUtil.YF_COST_SETTLE_SUM_FIELDS);

        return buildCostInfo;
    }

    /**
     * 从分摊信息汇总构建中间表信息
     */
    private BmsYfexpensesMiddlePO buildMiddleCostInfoFromShare(CalculateCostResult costResult, String costMainId,
                                                             Integer optCodePkId, List<BmsYfexpensesMiddleSharePO> shareList) {

        BmsYfexpensesMiddlePO buildCostMiddle = new BmsYfexpensesMiddlePO();
        buildCostMiddle.setCodePkId(optCodePkId);
        buildCostMiddle.setCodeType(costResult.getCodeType());
        buildCostMiddle.setMainCodeId(costMainId);
        buildCostMiddle.setShareType(costResult.getShareType());
        buildCostMiddle.setOptMonth(DateUtils.optMonth());
        buildCostMiddle.setOptDay(DateUtils.optDay());

        // 汇总统计字段
        BigDecimal totalWeight = shareList.stream()
            .map(BmsYfexpensesMiddleSharePO::getTotalWeight)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalVolume = shareList.stream()
            .map(BmsYfexpensesMiddleSharePO::getTotalVolume)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalNumber = shareList.stream()
            .map(BmsYfexpensesMiddleSharePO::getTotalNumber)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        buildCostMiddle.setTotalWeight(totalWeight);
        buildCostMiddle.setTotalVolume(totalVolume);
        buildCostMiddle.setTotalNumber(totalNumber);

        // 使用CostUtil汇总分摊表的费用到中间表
        buildCostMiddle = CostUtil.aggregateCost(buildCostMiddle, shareList,
            BmsYfexpensesMiddlePO.class, CostUtil.YF_COST_SETTLE_SUM_FIELDS);

        return buildCostMiddle;
    }

    /**
     * 将构建的费用信息添加到相应的Map中
     */
    private void addToMaps(Integer optCodePkId, BmsYfcostMainInfoPO buildMainCostInfo, BmsYfcostInfoPO buildCostInfo,
                          BmsYfexpensesMiddlePO buildCostMiddle, List<BmsYfexpensesMiddleSharePO> buildSharePOList,
                          Map<Integer, List<BmsYfcostMainInfoPO>> insertMainMap,
                          Map<Integer, List<BmsYfcostInfoPO>> insertCostMap,
                          Map<Integer, List<BmsYfexpensesMiddlePO>> insertMiddleMap,
                          Map<Integer, List<BmsYfexpensesMiddleSharePO>> insertMiddleShareMap) {

        // 添加主费用信息
        if(insertMainMap.containsKey(optCodePkId)){
            List<BmsYfcostMainInfoPO> mainInfoPOS = insertMainMap.get(optCodePkId);
            mainInfoPOS.add(buildMainCostInfo);
            insertMainMap.put(optCodePkId, mainInfoPOS);
        } else {
            insertMainMap.put(optCodePkId, Lists.newArrayList(buildMainCostInfo));
        }

        // 添加费用信息
        if(insertCostMap.containsKey(optCodePkId)){
            List<BmsYfcostInfoPO> costInfos = insertCostMap.get(optCodePkId);
            costInfos.add(buildCostInfo);
            insertCostMap.put(optCodePkId, costInfos);
        } else {
            insertCostMap.put(optCodePkId, Lists.newArrayList(buildCostInfo));
        }

        // 添加中间表信息
        if(insertMiddleMap.containsKey(optCodePkId)){
            List<BmsYfexpensesMiddlePO> middlePOList = insertMiddleMap.get(optCodePkId);
            middlePOList.add(buildCostMiddle);
            insertMiddleMap.put(optCodePkId, middlePOList);
        } else {
            insertMiddleMap.put(optCodePkId, Lists.newArrayList(buildCostMiddle));
        }

        // 添加分摊表信息
        if(insertMiddleShareMap.containsKey(optCodePkId)){
            List<BmsYfexpensesMiddleSharePO> middleSharePOS = insertMiddleShareMap.get(optCodePkId);
            middleSharePOS.addAll(buildSharePOList);
            insertMiddleShareMap.put(optCodePkId, middleSharePOS);
        } else {
            insertMiddleShareMap.put(optCodePkId, Lists.newArrayList(buildSharePOList));
        }
    }
}
