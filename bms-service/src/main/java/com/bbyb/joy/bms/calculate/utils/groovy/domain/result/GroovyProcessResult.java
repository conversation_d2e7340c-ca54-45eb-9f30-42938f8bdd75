package com.bbyb.joy.bms.calculate.utils.groovy.domain.result;

import lombok.Data;

/**
 * groovy调用(计费过程结果信息)
 */
@Data
public class GroovyProcessResult {

    /**
     * 服务执行结果编码
     */
    private int code;

    /**
     * 是否成功
     * true:成功
     * false:失败
     */
    private boolean success;


    /**
     * 服务执行结果描述
     */
    private String msg;


    /**
     * 计算因子参数信息
     */
    private String result;

}
