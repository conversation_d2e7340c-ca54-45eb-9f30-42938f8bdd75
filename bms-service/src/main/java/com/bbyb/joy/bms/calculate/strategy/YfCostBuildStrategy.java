package com.bbyb.joy.bms.calculate.strategy;

import cn.hutool.core.lang.Dict;
import com.bbyb.bms.model.po.BmsYfcostInfoPO;
import com.bbyb.bms.model.po.BmsYfcostMainInfoPO;
import com.bbyb.bms.model.po.BmsYfexpensesMiddlePO;
import com.bbyb.bms.model.po.BmsYfexpensesMiddleSharePO;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.calculate.domain.result.CalculateCostResult;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyCalculateExtraType;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 应付费用构建策略接口
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public interface YfCostBuildStrategy {

    /**
     * 获取策略支持的计费类型
     * 
     * @return 支持的GroovyCalculateExtraType
     */
    GroovyCalculateExtraType getSupportedType();

    /**
     * 构建应付费用信息
     * 
     * @param costResult 计费结果
     * @param costMainId 主费用ID
     * @param mainExpenseCode 主费用编码
     * @param costId 费用ID
     * @param expenseCode 费用编码
     * @param optCodePkId 单据主键ID
     * @param optCodeDetail2PkIds 作业单主键ID集合
     * @param userInfo 用户信息
     * @param now 当前时间
     * @param allCodeDetail2DataMap 所有作业单数据Map
     * @return 费用构建结果
     */
    YfCostBuildResult buildYfCostInfo(CalculateCostResult costResult, String costMainId, String mainExpenseCode,
                                     String costId, String expenseCode, Integer optCodePkId, Set<Integer> optCodeDetail2PkIds,
                                     UserBean userInfo, Date now, Map<Integer, Dict> allCodeDetail2DataMap);

    /**
     * 费用构建结果封装类
     */
    class YfCostBuildResult {
        
        /**
         * 主费用信息
         */
        private BmsYfcostMainInfoPO mainCostInfo;
        
        /**
         * 费用信息
         */
        private BmsYfcostInfoPO costInfo;
        
        /**
         * 中间表信息
         */
        private BmsYfexpensesMiddlePO middleInfo;
        
        /**
         * 分摊表信息列表
         */
        private List<BmsYfexpensesMiddleSharePO> shareInfoList;

        public YfCostBuildResult() {}

        public YfCostBuildResult(BmsYfcostMainInfoPO mainCostInfo, BmsYfcostInfoPO costInfo, 
                               BmsYfexpensesMiddlePO middleInfo, List<BmsYfexpensesMiddleSharePO> shareInfoList) {
            this.mainCostInfo = mainCostInfo;
            this.costInfo = costInfo;
            this.middleInfo = middleInfo;
            this.shareInfoList = shareInfoList;
        }

        // Getters and Setters
        public BmsYfcostMainInfoPO getMainCostInfo() {
            return mainCostInfo;
        }

        public void setMainCostInfo(BmsYfcostMainInfoPO mainCostInfo) {
            this.mainCostInfo = mainCostInfo;
        }

        public BmsYfcostInfoPO getCostInfo() {
            return costInfo;
        }

        public void setCostInfo(BmsYfcostInfoPO costInfo) {
            this.costInfo = costInfo;
        }

        public BmsYfexpensesMiddlePO getMiddleInfo() {
            return middleInfo;
        }

        public void setMiddleInfo(BmsYfexpensesMiddlePO middleInfo) {
            this.middleInfo = middleInfo;
        }

        public List<BmsYfexpensesMiddleSharePO> getShareInfoList() {
            return shareInfoList;
        }

        public void setShareInfoList(List<BmsYfexpensesMiddleSharePO> shareInfoList) {
            this.shareInfoList = shareInfoList;
        }
    }
}
