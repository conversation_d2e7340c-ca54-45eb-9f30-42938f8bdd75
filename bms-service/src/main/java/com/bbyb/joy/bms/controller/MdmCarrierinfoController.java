package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.domain.dto.FileBaseDto;
import com.bbyb.joy.bms.domain.dto.MdmCarrierinfo;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.service.IMdmCarrierinfoService;
import com.bbyb.joy.bms.support.AdvancePermissionUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.utils.ExcelUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 承运商Controller
 */
@RestController
@RequestMapping("/system/carrierinfo")
public class MdmCarrierinfoController {

    @Resource
    AdvancePermissionUtil advancePermissionUtil;
    @Resource
    private IMdmCarrierinfoService mdmCarrierinfoService;
    @Resource
    private UserRights userRights;

    /**
     * 查询承运商列表
     */
    @PostMapping("/list")
    @MenuAuthority(code = "承运商信息")
    public ResponseResult<PagerDataBean<MdmCarrierinfo>> list(@RequestBody(required = false) MdmCarrierinfo mdmCarrierinfo) {
        String token = RequestContext.getToken();
        if (mdmCarrierinfo == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CARRIER.getCode(), "carrierList", PermissionConfig.FieldType.LIST_STRING, "carrierCode"),
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.STRING, "id")
        );
        mdmCarrierinfo = userRights.applyPermissions(mdmCarrierinfo, permissionConfigs);

        return new ResponseResult<>(mdmCarrierinfoService.selectMdmCarrierinfoList(token, mdmCarrierinfo));
    }

    /**
     * 查询承运商列表
     */
    @PostMapping("/list2")
    public ResponseResult<PagerDataBean<MdmCarrierinfo>> list2(@RequestBody(required = false) MdmCarrierinfo mdmCarrierinfo) {
        String token = RequestContext.getToken();
        if (mdmCarrierinfo == null) {
            mdmCarrierinfo = new MdmCarrierinfo();
        }
        return new ResponseResult<>(mdmCarrierinfoService.selectMdmCarrierinfoList(token, mdmCarrierinfo));
    }


    /**
     * 导出承运商列表
     */
    @PostMapping("/export")
    public ResponseResult<String> export(@RequestBody(required = false) MdmCarrierinfo mdmCarrierinfo) {
        String token = RequestContext.getToken();
        PagerDataBean<MdmCarrierinfo> list = mdmCarrierinfoService.selectMdmCarrierinfoList(token, mdmCarrierinfo);
        ExcelUtil<MdmCarrierinfo> util = new ExcelUtil<MdmCarrierinfo>(MdmCarrierinfo.class);
        FileBaseDto fileBaseDto = util.exportExcel(list.getRows(), "承运商数据");
        return new ResponseResult<>(true, 0, fileBaseDto.getFileName(), fileBaseDto.getFileName());
    }

    /**
     * 获取承运商详细信息
     */
    @PostMapping(value = "/{id}")
    public ResponseResult<MdmCarrierinfo> getInfo(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(json)) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        String id = JSONObject.parseObject(json).getString("id");
        return new ResponseResult<>(mdmCarrierinfoService.selectMdmCarrierinfoById(token, id));
    }


    /**
     * 修改承运商
     */
    @PostMapping("/edit")
    @MenuAuthority(code = "承运商信息-修改")
    public ResponseResult<Integer> edit(@RequestBody(required = false) MdmCarrierinfo mdmCarrierinfo) throws Exception {
        String token = RequestContext.getToken();
        if (mdmCarrierinfo == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(mdmCarrierinfoService.updateMdmCarrierinfo(token, mdmCarrierinfo));
    }

}
