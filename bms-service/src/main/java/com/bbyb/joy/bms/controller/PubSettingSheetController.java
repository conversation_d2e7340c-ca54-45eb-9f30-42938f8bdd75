package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.sheet.PubSettingSheetRequest;
import com.bbyb.joy.bms.domain.dto.sheet.PubSheetModel;
import com.bbyb.joy.bms.service.PubSettingSheetService;
import com.bbyb.joy.core.context.RequestContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 配置Sheet控制器
 */
@RestController
@RequestMapping("/system/sheet")
public class PubSettingSheetController {

    private final PubSettingSheetService settingSheetService;

    public PubSettingSheetController(PubSettingSheetService settingSheetService) {
        this.settingSheetService = settingSheetService;
    }

    /**
     * 查询详情
     *
     * @param settingSheetRequest 配置Sheet要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/detail/query")
    public ResponseResult<PubSheetModel> queryDetail(@RequestBody PubSettingSheetRequest settingSheetRequest) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(settingSheetService.queryDetail(token, settingSheetRequest.getRelationId()));
    }

    /**
     * 修改Sheet配置
     *
     * @param sheetRequest Sheet要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/setting/modify")
    public ResponseResult<Boolean> modifySheetSetting(@RequestBody PubSettingSheetRequest sheetRequest) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(settingSheetService.modifySheetSetting(token, sheetRequest.getSheetModel(), sheetRequest.getRelationId()));
    }
}
