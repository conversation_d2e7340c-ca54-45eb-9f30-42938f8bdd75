package com.bbyb.joy.bms.support.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 请求响应日志记录配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "jp.common.base")
@Data
public class RequestAndResponseLogProperties {

    /**
     * 是否记录请求日志
     */
    private boolean needLogRequest = true;

    /**
     * 是否记录响应日志
     */
    private boolean needLogResponse = true;

    /**
     * 是否记录header
     */
    private boolean needLogHeader = true;

    /**
     * 是否记录参数
     */
    private boolean needLogPayload = true;

    /**
     * 记录的最大body大小
     */
    private int maxPayloadLength = 2 * 1024 * 1024;

    /**
     * 不记录日志的地址
     */
    private List<String> excludeUrlPatterns;


}