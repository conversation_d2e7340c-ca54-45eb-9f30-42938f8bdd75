package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bms.domain.RequestApiDto;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IApiService;
import com.bbyb.joy.bms.service.ValueAddApiService;
import com.bbyb.joy.bms.support.annotation.ApiCheck;
import com.bbyb.joy.bms.support.annotation.Log;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 基础信息接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/Api")
public class BmsPubApiController {
    @Resource
    private IApiService apiService;
    @Resource
    private ValueAddApiService valueAddApiService;

    /**
     * 接口对接
     */
    @Log(title = "接口对接", businessType = BusinessType.OTHER)
    @PostMapping("/entrance")
    @ApiCheck
    public String entrance(@RequestBody RequestApiDto requestApiDto) {
        return apiService.requestApi(requestApiDto);
    }

    /**
     * 增值服务接口对接
     */
    @Log(title = "增值服务接口对接", businessType = BusinessType.OTHER)
    @PostMapping("/valueAddServices")
    @ApiCheck
    public String valueAddServices(@RequestBody RequestApiDto requestApiDto) {
        return valueAddApiService.valueAddApi(requestApiDto);
    }
}
