package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.domain.dto.PubFileExport;
import com.bbyb.joy.bms.service.IPubFileExportService;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.core.context.RequestContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 报导出下载Controller
 */
@RestController
@RequestMapping("/system/export")
public class PubFileExportController {

    @Resource
    private IPubFileExportService pubFileExportService;

    /**
     * 查询报导出下载列表
     */
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<PubFileExport>> list(@RequestBody(required = false) PubFileExport pubFileExport) {
        String token = RequestContext.getToken();
        if (pubFileExport == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        UserBean loginUserInfo = RequestContext.getUserInfo();
        pubFileExport.setOperCode(loginUserInfo.getEmployeecode().toString());
        return new ResponseResult<>(pubFileExportService.selectPubFileExportList(token, pubFileExport));
    }


    /**
     * 逻辑作废/启用报导出下载
     */
    @PostMapping("/updateStatus")
    public ResponseResult<String> updateStatus(@RequestBody(required = false) Map<String, Object> map) {
        String token = RequestContext.getToken();
        if (map == null || map.isEmpty()) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(pubFileExportService.updatePubFileExportStatus(token, map));
    }

}
