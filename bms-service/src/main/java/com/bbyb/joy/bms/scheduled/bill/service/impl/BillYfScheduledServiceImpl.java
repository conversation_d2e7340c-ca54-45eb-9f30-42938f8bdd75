package com.bbyb.joy.bms.scheduled.bill.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.biz.BmsYfbillmainBiz;
import com.bbyb.joy.bms.biz.BmsYfcostInfoBiz;
import com.bbyb.joy.bms.biz.MdmCarrierinfoBiz;
import com.bbyb.joy.bms.domain.dto.MdmCarrierinfo;
import com.bbyb.joy.bms.domain.dto.SysDept;
import com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain;
import com.bbyb.joy.bms.domain.dto.dto.BmsYfcostExtendDto;
import com.bbyb.joy.bms.scheduled.bill.service.BillYfScheduledService;
import com.bbyb.joy.bms.support.utils.DateUtils;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.getCode.CodeCategory;
import com.bbyb.joy.bms.support.utils.getCode.CodeUtils;
import com.bbyb.joy.core.context.RequestContext;
import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BillYfScheduledServiceImpl implements BillYfScheduledService {

    // 客户mapper
    @Resource
    private MdmCarrierinfoBiz mdmCarrierinfoMapper;
    @Resource
    private BmsYfbillmainBiz bmsYfbillmainMapper;
    @Resource
    private BmsYfcostInfoBiz bmsYfcostInfoMapper;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    CodeUtils codeUtils;


    @Override
    public void autoGenerateBill() {

        UserBean userVO = RequestContext.getUserInfo();

        try {
            System.out.println("应付账单开始自动生成-----");
            Map<String,Object> relut = new HashMap<>();
            // 查询出所有登记账期的客户
            List<MdmCarrierinfo> clientList= mdmCarrierinfoMapper.selectBillDateAndRuleList();
            if(CollUtil.isEmpty(clientList)){
                return;
            }
            for (MdmCarrierinfo mdmCarrierinfo : clientList) {

                Integer carrDay= StrUtil.equals(mdmCarrierinfo.getAccountperi(),"自然月")?1:Integer.parseInt(mdmCarrierinfo.getAccountperi());
                String accountDay = getAcctDay(carrDay);
                System.out.println("承运商名称："+mdmCarrierinfo.getCarrierName()+",账单日:"+mdmCarrierinfo.getAccountperi()+",当前账期"+accountDay);
                // 先更新冲销单
                FeeFlagBill(mdmCarrierinfo,accountDay);
                //1）仓+运+集团+网点
                if("1".equals(mdmCarrierinfo.getBillingLogic().toString())){
                    GenerateBill1(mdmCarrierinfo,accountDay);
                }
                if("2".equals(mdmCarrierinfo.getBillingLogic().toString())){
                    GenerateBill2(mdmCarrierinfo,accountDay);
                }
                if("3".equals(mdmCarrierinfo.getBillingLogic().toString())){
                    GenerateBill3(mdmCarrierinfo,accountDay);
                }
                if("4".equals(mdmCarrierinfo.getBillingLogic().toString())){
                    GenerateBill4(mdmCarrierinfo,accountDay);
                }
                if("5".equals(mdmCarrierinfo.getBillingLogic().toString())){
                    GenerateBill5(mdmCarrierinfo,accountDay);
                }
                if("6".equals(mdmCarrierinfo.getBillingLogic().toString())){
                    GenerateBill6(mdmCarrierinfo,accountDay);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }


    public static String getAcctDay(Integer acctDay) {
        if (acctDay == null || acctDay < 1 || acctDay > 31) {
            throw new IllegalArgumentException("账单日必须在1-31之间");
        }

        LocalDate now = LocalDate.now();
        // 固定获取上个月（M-1）
        LocalDate acctDate = now.minusMonths(1);

        // 处理月末日期（如31号在2月不存在的情况）
        try {
            acctDate = acctDate.withDayOfMonth(acctDay);
        } catch (DateTimeException e) {
            // 如果指定日期不存在，使用该月最后一天
            acctDate = acctDate.withDayOfMonth(acctDate.lengthOfMonth());
        }

        return acctDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }


    /**
     * 1）仓+运+子公司+网点
     * @param mdmCarrierinfo 符合条件的客户信息
     * @param billDate 账单日
     */
    private void GenerateBill1(MdmCarrierinfo mdmCarrierinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按网点(机构)分组
        List<BmsYfcostExtendDto> costGroupByclient=bmsYfcostInfoMapper.getcodeListGroupcompanyByClientAndCompany(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),billDate);
        String carrierName = mdmCarrierinfo.getCarrierName();
        String companyName = "";
        Map<String, SysDept> dept = textConversionUtil.getCompanyMap("1",costGroupByclient.stream().filter(e->e.getCompanyId()!=null).map(e->e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        for (BmsYfcostExtendDto bmsYfcostExtendDto : costGroupByclient) {
            if(bmsYfcostExtendDto.getVotes()==0){
                continue;
            }
            if(bmsYfcostExtendDto.getCompanyId()!=null && dept.containsKey(bmsYfcostExtendDto.getCompanyId().toString())){
                companyName = dept.get(bmsYfcostExtendDto.getCompanyId().toString()).getDeptName();
            }
            carrierName = bmsYfcostExtendDto.getClientName();
            if(StringUtil.isNotEmpty(carrierName)){
                String billName ="【"+carrierName+"】"+companyName;
                BmsYfbillmain bmsYfbillmain =assemblyBillInformation(bmsYfcostExtendDto,billDate,mdmCarrierinfo.getId().longValue(),billName);
                // 新增一个账单
                Integer billId= bmsYfbillmainMapper.insertBmsYfbillmain(userVO.getTenantid().toString(),bmsYfbillmain);
                // 更新这个账单对应的费用关联
                bmsYfcostInfoMapper.updateBillIdById(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),null,billDate,bmsYfcostExtendDto.getCompanyId().intValue(),bmsYfbillmain.getId().intValue(),bmsYfbillmain.getBillCode());
            }
        }
    }


    /**
     * 2）仓+运+子公司+全公司
     * @param mdmCarrierinfo 符合条件的客户信息
     * @param billDate 账单日
     */
    private void GenerateBill2(MdmCarrierinfo mdmCarrierinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按全公司(所有的机构)分组
        List<BmsYfcostExtendDto> costGroupByclient=bmsYfcostInfoMapper.getcodeListGroupcompanyByClient(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),billDate);
        String carrierName = mdmCarrierinfo.getCarrierName();
        String companyName = "绝配总公司";
        for (BmsYfcostExtendDto bmsYfcostExtendDto : costGroupByclient) {
            if(bmsYfcostExtendDto.getVotes()==0){
                continue;
            }
            carrierName = bmsYfcostExtendDto.getClientName();
            String billName ="【"+carrierName+"】"+companyName;
            if(StringUtil.isNotEmpty(carrierName)){
                BmsYfbillmain bmsYfbillmain =assemblyBillInformation(bmsYfcostExtendDto,billDate,mdmCarrierinfo.getId().longValue(),billName);
                // 新增一个账单
                Integer billId= bmsYfbillmainMapper.insertBmsYfbillmain(userVO.getTenantid().toString(),bmsYfbillmain);
                // 更新这个账单对应的费用关联
                bmsYfcostInfoMapper.updateBillIdById(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),null,billDate,null,bmsYfbillmain.getId().intValue(),bmsYfbillmain.getBillCode());
            }
        }
    }



    /**
     * 3）仓/运+子公司+网点
     * @param mdmCarrierinfo 符合条件的客户信息
     * @param billDate 账单日
     */
    private void GenerateBill3(MdmCarrierinfo mdmCarrierinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按网点(机构)分组
        List<BmsYfcostExtendDto> costGroupByclient=bmsYfcostInfoMapper.getcodeListGroupcompanyByClientAndCompanyAndStock(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),billDate);
        String carrierName = mdmCarrierinfo.getCarrierName();
        String companyName = "";
        Map<String, SysDept> dept = textConversionUtil.getCompanyMap("1",costGroupByclient.stream().filter(e->e.getCompanyId()!=null).map(e->e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        for (BmsYfcostExtendDto bmsYfcostExtendDto : costGroupByclient) {
            if(bmsYfcostExtendDto.getVotes()==0){
                continue;
            }
            if(bmsYfcostExtendDto.getCompanyId()!=null && dept.containsKey(bmsYfcostExtendDto.getCompanyId().toString())){
                companyName = dept.get(bmsYfcostExtendDto.getCompanyId().toString()).getDeptName();
            }
            carrierName = bmsYfcostExtendDto.getClientName();
            String billName ="【"+carrierName+"】"+companyName;
            if(StringUtil.isNotEmpty(carrierName)){
                BmsYfbillmain bmsYfbillmain =assemblyBillInformation(bmsYfcostExtendDto,bmsYfcostExtendDto.getBillDate(),mdmCarrierinfo.getId().longValue(),billName);
                // 新增一个账单
                Integer billId= bmsYfbillmainMapper.insertBmsYfbillmain(userVO.getTenantid().toString(),bmsYfbillmain);
                // 更新这个账单对应的费用关联
                if("1".equals(bmsYfcostExtendDto.getBillType())){
                    bmsYfcostInfoMapper.updateBillIdYsById(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),null,billDate,bmsYfcostExtendDto.getCompanyId().intValue(),bmsYfbillmain.getId().intValue());
                }
                if("2".equals(bmsYfcostExtendDto.getBillType())){
                    bmsYfcostInfoMapper.updateBillIdCcById(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),null,billDate,bmsYfcostExtendDto.getCompanyId().intValue(),bmsYfbillmain.getId().intValue(),null);
                }
            }
        }
    }

    /**
     * 4）仓/运+子公司+全公司
     * @param mdmCarrierinfo 符合条件的客户信息
     * @param billDate 账单日
     */
    private void GenerateBill4(MdmCarrierinfo mdmCarrierinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按全公司(所有的机构)分组
        List<BmsYfcostExtendDto> costGroupByclient=bmsYfcostInfoMapper.getcodeListGroupcompanyAndStockByClient(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),billDate);
        String carrierName = mdmCarrierinfo.getCarrierName();
        String companyName = "绝配总公司";
        for (BmsYfcostExtendDto bmsYfcostExtendDto : costGroupByclient) {
            if(bmsYfcostExtendDto.getVotes()==0){
                continue;
            }
            carrierName = bmsYfcostExtendDto.getClientName();
            String billName ="【"+carrierName+"】"+companyName;
            if(StringUtil.isNotEmpty(carrierName)){
                BmsYfbillmain bmsYfbillmain =assemblyBillInformation(bmsYfcostExtendDto,bmsYfcostExtendDto.getBillDate(),mdmCarrierinfo.getId().longValue(),billName);
                // 新增一个账单
                Integer billId= bmsYfbillmainMapper.insertBmsYfbillmain(userVO.getTenantid().toString(),bmsYfbillmain);
                // 更新这个账单对应的费用关联
                if("1".equals(bmsYfcostExtendDto.getBillType())){
                    bmsYfcostInfoMapper.updateBillIdYsById(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),null,bmsYfcostExtendDto.getBillDate(),null,bmsYfbillmain.getId().intValue());
                }
                if("2".equals(bmsYfcostExtendDto.getBillType())){
                    bmsYfcostInfoMapper.updateBillIdCcById(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),null,bmsYfcostExtendDto.getBillDate(),null,bmsYfbillmain.getId().intValue(),null);
                }
            }
        }
    }

    /**
     * 5）仓/运+子公司+网点+仓库
     * @param mdmCarrierinfo 符合条件的客户信息
     * @param billDate 账单日
     */
    private void GenerateBill5(MdmCarrierinfo mdmCarrierinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按网点(机构)分组
        List<BmsYfcostExtendDto> costGroupByclient=bmsYfcostInfoMapper.getcodeListGroupcompanyAndWareByClientAndCompanyAndStock(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),billDate);
        String carrierName = mdmCarrierinfo.getCarrierName();
        String companyName = "";
        String ware = "";
        Map<String, SysDept> dept = textConversionUtil.getCompanyMap("1",costGroupByclient.stream().filter(e->e.getCompanyId()!=null).map(e->e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        for (BmsYfcostExtendDto bmsYfcostExtendDto : costGroupByclient) {
            if(bmsYfcostExtendDto.getVotes()==0){
                continue;
            }
            if(bmsYfcostExtendDto.getCompanyId()!=null && dept.containsKey(bmsYfcostExtendDto.getCompanyId().toString())){
                companyName = dept.get(bmsYfcostExtendDto.getCompanyId().toString()).getDeptName();
            }
            if("2".equals(bmsYfcostExtendDto.getBillType())){
                ware=bmsYfcostExtendDto.getWarehouseName()+"-";
            }
            carrierName =bmsYfcostExtendDto.getClientName();
            String billName =ware+"【"+carrierName+"】"+companyName;
            if(StringUtil.isNotEmpty(carrierName)){
                BmsYfbillmain bmsYfbillmain =assemblyBillInformation(bmsYfcostExtendDto,bmsYfcostExtendDto.getBillDate(),mdmCarrierinfo.getId().longValue(),billName);
                // 新增一个账单
                Integer billId= bmsYfbillmainMapper.insertBmsYfbillmain(userVO.getTenantid().toString(),bmsYfbillmain);
                // 更新这个账单对应的费用关联
                if("1".equals(bmsYfcostExtendDto.getBillType())){
                    bmsYfcostInfoMapper.updateBillIdYsById(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),null,bmsYfcostExtendDto.getBillDate(),bmsYfcostExtendDto.getCompanyId().intValue(),bmsYfbillmain.getId().intValue());
                }
                if("2".equals(bmsYfcostExtendDto.getBillType())){
                    bmsYfcostInfoMapper.updateBillIdCcById(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),null,bmsYfcostExtendDto.getBillDate(),bmsYfcostExtendDto.getCompanyId().intValue(),bmsYfbillmain.getId().intValue(),bmsYfcostExtendDto.getWarehouseCode());
                }
            }


        }
    }

    /**
     * 6）仓/运+子公司+全公司+仓库
     * @param mdmCarrierinfo 符合条件的客户信息
     * @param billDate 账单日
     */
    private void GenerateBill6(MdmCarrierinfo mdmCarrierinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按全公司(所有的机构)分组
        List<BmsYfcostExtendDto> costGroupByclient=bmsYfcostInfoMapper.getcodeListGroupcompanyAndWareByClient(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),billDate);
        String carrierName = mdmCarrierinfo.getCarrierName();
        String companyName = "绝配总公司";
        String ware = "";
        for (BmsYfcostExtendDto bmsYfcostExtendDto : costGroupByclient) {
            if(bmsYfcostExtendDto.getVotes()==0){
                continue;
            }
            if("2".equals(bmsYfcostExtendDto.getBillType())){
                ware=bmsYfcostExtendDto.getWarehouseName()+"-";
            }
            // 获取客户名称
            carrierName = bmsYfcostExtendDto.getClientName();
            String billName =ware+"【"+carrierName+"】"+companyName;
            if(StringUtil.isNotEmpty(carrierName)){
                BmsYfbillmain bmsYfbillmain =assemblyBillInformation(bmsYfcostExtendDto,bmsYfcostExtendDto.getBillDate(),mdmCarrierinfo.getId().longValue(),billName);
                // 新增一个账单
                Integer billId= bmsYfbillmainMapper.insertBmsYfbillmain(userVO.getTenantid().toString(),bmsYfbillmain);
                // 更新这个账单对应的费用关联
                if("1".equals(bmsYfcostExtendDto.getBillType())){
                    bmsYfcostInfoMapper.updateBillIdYsById(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),null,bmsYfcostExtendDto.getBillDate(),null,bmsYfbillmain.getId().intValue());
                }
                if("2".equals(bmsYfcostExtendDto.getBillType())){
                    bmsYfcostInfoMapper.updateBillIdCcById(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),null,bmsYfcostExtendDto.getBillDate(),null,bmsYfbillmain.getId().intValue(),bmsYfcostExtendDto.getWarehouseCode());
                }
            }

        }
    }

    /**
     * 冲消单 单独生成账单
     * @param mdmCarrierinfo
     * @param billDate
     */
    private void FeeFlagBill(MdmCarrierinfo mdmCarrierinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按全公司(所有的机构)分组
        List<BmsYfcostExtendDto> costGroupByclient=bmsYfcostInfoMapper.getcodeFeeFlagList(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),billDate);
        String collect = costGroupByclient.stream().filter(e -> e.getCompanyId() != null).map(e -> e.getCompanyId().toString()).distinct().collect(Collectors.joining(","));
        Map<String, SysDept> dept = textConversionUtil.getCompanyMap("1",collect);
        String companyName="";
        for (BmsYfcostExtendDto bmsYfcostExtendDto : costGroupByclient) {
            if(bmsYfcostExtendDto.getVotes()==0){
                continue;
            }
            if(bmsYfcostExtendDto.getCompanyId()!=null && dept.containsKey(bmsYfcostExtendDto.getCompanyId().toString())){
                companyName = dept.get(bmsYfcostExtendDto.getCompanyId().toString()).getDeptName();
            }
            String billName ="【"+bmsYfcostExtendDto.getClientName()+"】"+companyName;
            if(StringUtil.isNotEmpty(bmsYfcostExtendDto.getClientName())){
                BmsYfbillmain bmsYfbillmain =assemblyBillInformation(bmsYfcostExtendDto,bmsYfcostExtendDto.getBillDate(),mdmCarrierinfo.getId().longValue(),billName);
                // 新增一个冲销账单
                bmsYfbillmain.setBillMarking(2);
                bmsYfbillmain.setBillName(bmsYfbillmain.getBillName()+"-冲销单");
                Integer billId= bmsYfbillmainMapper.insertBmsYfbillmain(userVO.getTenantid().toString(),bmsYfbillmain);
                // 更新这个账单对应的费用关联
                bmsYfcostInfoMapper.updateBillIdFeeFlagById(userVO.getTenantid().toString(),mdmCarrierinfo.getCarrierCode(),bmsYfcostExtendDto.getBillDate(),bmsYfcostExtendDto.getCompanyId().intValue(),bmsYfbillmain.getId().intValue());

            }
        }
    }

    /**
     * 组装应收账单信息
     * @param cost 费用信息
     * @param accountDay 账单日期
     * @param carrierId 承运商id
     * @return
     */
    public BmsYfbillmain assemblyBillInformation(BmsYfcostExtendDto cost, String accountDay, Long carrierId, String billName){

        UserBean userVO = RequestContext.getUserInfo();

        String billDate = accountDay;
        if(accountDay!=null){
            SimpleDateFormat simp = new SimpleDateFormat("yyyy-MM");
            SimpleDateFormat simp2 = new SimpleDateFormat("yyyy年MM月");
            try {
                // 日期格式（用于账单名称）
                billDate = simp2.format(simp.parse(accountDay));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        String name = billName+billDate;
        if(cost.getBillType()!=null && "1".equals(cost.getBillType())){
            name=name+"运输账单";
        }
        if(cost.getBillType()!=null && "2".equals(cost.getBillType())){
            name=name+"仓储账单";
        }
        if(cost.getBillType()==null){
            name=name+"仓配账单";
        }

        BmsYfbillmain bmsYfbillmain = new BmsYfbillmain();
        bmsYfbillmain.setBillType(0);
        bmsYfbillmain.setBillCode(codeUtils.getCode(CodeCategory.YFCode,userVO.getTenantid().toString()));
        bmsYfbillmain.setBillName(name);
        bmsYfbillmain.setBillDate(accountDay);
        bmsYfbillmain.setVotes(cost.getVotes());
        bmsYfbillmain.setBillAmount(cost.getSumAmt());
        bmsYfbillmain.setAdjustedAmount(cost.getSumAmt());
        bmsYfbillmain.setYfAmount(cost.getSumAmt());
        bmsYfbillmain.setCompanyId(cost.getCompanyId().intValue());
        bmsYfbillmain.setCarrierId(carrierId);
        bmsYfbillmain.setClientId(cost.getClientId().intValue());
        bmsYfbillmain.setBillMarking(1);
        bmsYfbillmain.setBillState(1);
        bmsYfbillmain.setAuditState(1);
        bmsYfbillmain.setTicketState(1);
        bmsYfbillmain.setHxState(1);
        bmsYfbillmain.setSubmitStatus(1);
        bmsYfbillmain.setRemark(new SimpleDateFormat("yyyy-MM-dd HH:ss:mm").format(new Date())+"自动生成");
        bmsYfbillmain.setCreateTime(DateUtils.getNowDate());
        bmsYfbillmain.setOperTime(DateUtils.getNowDate());
        return bmsYfbillmain;
    }



}
