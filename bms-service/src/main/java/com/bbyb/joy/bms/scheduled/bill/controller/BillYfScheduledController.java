package com.bbyb.joy.bms.scheduled.bill.controller;

import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.scheduled.bill.service.BillYfScheduledService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 应付定时任务-自动生成账单接口
 */
@Api(tags = "应付定时任务-自动生成账单接口")
@RestController
@RequestMapping("/system/billYfSchedule")
public class BillYfScheduledController {

    @Resource
    BillYfScheduledService scheduledService;


    /**
     * 自动生成账单(定时任务触发)
     * 调用方式使用按批量进行，因为定时任务会有账单逻辑,会将费用数据传过来
     */
    @GetMapping("/autoGenerateBill")
    public ResponseResult<String> autoGenerateBill(){
        scheduledService.autoGenerateBill();
        return new ResponseResult<>("操作成功");
    }

}
