package com.bbyb.joy.bms.scheduled.bill.controller;

import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.scheduled.bill.service.CalculateScheduleService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 自动计费定时任务-自动生成账单接口
 */
@Api(tags = "自动计费定时任务-自动生成账单接口")
@RestController
@RequestMapping("/system/calculateSchedule")
public class CalculateScheduleController {

    @Resource
    CalculateScheduleService calculateScheduleService;


    @GetMapping("/ysAutoCalculate")
    public ResponseResult<String> ysAutoCalculate(){
        calculateScheduleService.ysAutoCalculate();
        return new ResponseResult<>("操作成功");
    }

    @GetMapping("/yfAutoCalculate")
    public ResponseResult<String> yfAutoCalculate(){
        calculateScheduleService.yfAutoCalculate();
        return new ResponseResult<>("操作成功");
    }

}
