package com.bbyb.joy.bms.calculate.utils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 对象操作工具
 */
public class ObjectUtil {


//    // 初始化对象
//    BmsJobCodeDetailInfoDto dto = groovyUtil.initDefaultValues(BmsJobCodeDetailInfoDto.class);
//    // 结果示例：
//    // pkId = 0
//    // id = ""
//    // totalBoxes = BigDecimal.ZERO
//    // delFlag = 0
//    // 其他字段也会根据类型被赋予相应的默认值

    /**
     * 初始化对象属性默认值
     * @param clazz 要初始化的对象类型
     * @return 初始化后的对象实例
     * @throws Exception 反射异常
     */
    public <T> T initDefaultValues(Class<T> clazz) throws Exception {
        T instance = clazz.getDeclaredConstructor().newInstance();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            Class<?> type = field.getType();

            if (field.get(instance) == null) {
                if (type == Integer.class || type == int.class) {
                    field.set(instance, 0);
                } else if (type == String.class) {
                    field.set(instance, "");
                } else if (type == Double.class || type == double.class) {
                    field.set(instance, 0.0);
                } else if (type == BigDecimal.class) {
                    field.set(instance, BigDecimal.ZERO);
                } else if (type == Boolean.class || type == boolean.class) {
                    field.set(instance, false);
                } else if (type == Long.class || type == long.class) {
                    field.set(instance, 0L);
                } else if (type == Float.class || type == float.class) {
                    field.set(instance, 0.0f);
                } else if (type == Short.class || type == short.class) {
                    field.set(instance, (short)0);
                } else if (type == Date.class) {
                    field.set(instance, new Date(0)); // 1970-01-01
                } else if (type == LocalDate.class) {
                    field.set(instance, LocalDate.of(1970, 1, 1));
                } else if (type == LocalDateTime.class) {
                    field.set(instance, LocalDateTime.of(1970, 1, 1, 0, 0));
                }
            }
        }

        return instance;
    }




    //decimal默认值处理
    public static BigDecimal defaultIfNull(BigDecimal val) {
        return val != null ? val : BigDecimal.ZERO;
    }

}
