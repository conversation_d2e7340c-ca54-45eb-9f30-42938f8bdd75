package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.SysDept;
import com.bbyb.joy.bms.domain.dto.datareport.StateYfGroup;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.service.IStateYfService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 应收数据状态台账
 */
@RestController
@RequestMapping("/DataReport/StateYf")
public class StateYfController {

    @Resource
    private IStateYfService stateYs;
    @Resource
    private TextConversionUtil util;
    @Resource
    private UserRights userRights;
    @Resource
    private ExportUtil exportUtil;

    /**
     * 应收状态台账分组列表
     */
    @PostMapping("/groupList")
    public ResponseResult<PagerDataBean<StateYfGroup>> groupList(@RequestBody(required = false) StateYfGroup param) {
        String token = RequestContext.getToken();
        if (param == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CLIENT.getCode(), "carrierList", PermissionConfig.FieldType.LIST_STRING, "carrierCode"),
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.ARRAY_STRING, "id")
        );
        param = userRights.applyPermissions(param, permissionConfigs);

        return new ResponseResult<>(stateYs.groupList(token, param));
    }

    /**
     * 应收状态台账分组列表
     */
    @PostMapping("/List")
    public ResponseResult<PagerDataBean<StateYfGroup>> list(@RequestBody(required = false) StateYfGroup bean) {
        String token = RequestContext.getToken();
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        if (bean.getCompanyIds() == null || bean.getCompanyIds().length == 0) {
            bean.setCompanyIds(util.getCompanyIdsArr());
        }
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "carrier");
        List<Carrierinfo> carrierList = sysDataPack.getCarrierInfo();
        if (CollUtil.isEmpty(carrierList)) {
            return new ResponseResult<>(new PagerDataBean<>(new ArrayList<>(), 0, new PagerBean(bean.getPage(), bean.getSize())));
        }
        bean.setCarrierList(carrierList.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toList()));

        PagerDataBean<StateYfGroup> stateYfGroupPagerDataBean = stateYs.detailsList(token, bean);
        if(stateYfGroupPagerDataBean==null || CollUtil.isEmpty(stateYfGroupPagerDataBean.getRows())){
            return new ResponseResult<>(new PagerDataBean<>(new ArrayList<>(), 0, new PagerBean(bean.getPage(), bean.getSize())));
        }


        Map<String, SysDept> companyMap = util.getCompanyMapRoot("1", null);
        for (StateYfGroup row : stateYfGroupPagerDataBean.getRows()) {
            if(row.getCompanyId()!=null && companyMap.containsKey(row.getCompanyId().toString())){
                row.setCompanyName(companyMap.get(row.getCompanyId().toString()).getDeptName());
            }
        }

        return new ResponseResult<>(stateYfGroupPagerDataBean);
    }

    /**
     * 应付账单状态台账导出
     */
    @PostMapping("/export")
    @MenuAuthority(code = "应付账单状态台账-导出")
    public ResponseResult<String> export(@RequestBody(required = false) StateYfGroup bean) {
        String token = RequestContext.getToken();
        if (bean.getCompanyIds() == null || bean.getCompanyIds().length == 0) {
            bean.setCompanyIds(util.getCompanyIdsArr());
        }
        Long userInfoId = RequestContext.getUserInfo().getId().longValue();
        SysDataPack sysDataPack = userRights.getRightsByUserId(userInfoId, "carrier");
        List<Carrierinfo> carrierList = sysDataPack.getCarrierInfo();
        if (CollUtil.isEmpty(carrierList)) {
            return new ResponseResult<>(400401, "没有承运商权限");
        }
        bean.setCarrierList(carrierList.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toList()));
        return exportUtil.getOutClassNew(token, "应付账单状态台账明细导出", "应付账单状态台账", StateYfGroup.class, userId -> {
            return stateYs.detailsList(token, bean).getRows();
        });
    }
}
