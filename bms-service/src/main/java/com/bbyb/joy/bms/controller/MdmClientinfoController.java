package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.*;
import com.bbyb.joy.bms.domain.enums.CooperateTypeEnum;
import com.bbyb.joy.bms.domain.enums.InvoiceModeEnum;
import com.bbyb.joy.bms.service.IMdmClientinfoService;
import com.bbyb.joy.bms.support.AdvancePermissionUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.utils.ExcelUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.enums.PubNumEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 客户信息Controller
 */
@Slf4j
@RestController
@RequestMapping("/system/clientinfo")
public class MdmClientinfoController {

    @Resource
    AdvancePermissionUtil advancePermissionUtil;
    @Resource
    private IMdmClientinfoService mdmClientinfoService;
    @Resource
    private UserRights userRights;

    /**
     * 查询客户信息列表
     */
    @PostMapping("/list")
    @MenuAuthority(code = "客户信息")
    public ResponseResult<PagerDataBean<MdmClientinfo>> list(@RequestBody(required = false) MdmClientinfo mdmClientinfo) {
        String token = RequestContext.getToken();
        if (mdmClientinfo == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
//        UserBean userVO = RequestContext.getUserInfo();
//        Long userId = Long.valueOf(userVO.getId());
//        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
//        List<ClientInfo> clientList = sysDataPack.getClientinfo();
//        //高级权限:机构含下级
//        boolean advancedPermissions = advancePermissionUtil.checkAdvancePermission(userVO, "单据权限-查询机构下所有客户的单");
//        if (CollUtil.isEmpty(clientList) && !advancedPermissions) {
//            return new ResponseResult<>(new PagerDataBean<>(new ArrayList<>(), 0, new PagerBean(mdmClientinfo.getPage(), mdmClientinfo.getSize())));
//        }
//        mdmClientinfo.setClientList(advancedPermissions ? new ArrayList<>() : clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));


        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CLIENT.getCode(), "clientList", PermissionConfig.FieldType.LIST_STRING, "clientCode"),
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.ARRAY_STRING, "id")
        );
        mdmClientinfo = userRights.applyPermissions(mdmClientinfo, permissionConfigs);


        return new ResponseResult<>(mdmClientinfoService.selectMdmClientinfoList(token, mdmClientinfo));
    }

    /**
     * 查询客户信息列表
     */
    @PostMapping("/list2")
    public ResponseResult<PagerDataBean<MdmClientinfo>> list2(@RequestBody(required = false) MdmClientinfo mdmClientinfo) {
        String token = RequestContext.getToken();
        if (mdmClientinfo == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(mdmClientinfoService.selectMdmClientinfoList(token, mdmClientinfo));
    }


    /**
     * 导出客户信息列表
     */
    @PostMapping("/export")
    public ResponseResult<String> export(@RequestBody(required = false) MdmClientinfo mdmClientinfo) {
        String token = RequestContext.getToken();
        PagerDataBean<MdmClientinfo> list = mdmClientinfoService.selectMdmClientinfoList(token, mdmClientinfo);
        ExcelUtil<MdmClientinfo> util = new ExcelUtil<MdmClientinfo>(MdmClientinfo.class);
        FileBaseDto fileBaseDto = util.exportExcel(list.getRows(), "客户信息数据");
        return new ResponseResult<>(true, 0, fileBaseDto.getFileName(), fileBaseDto.getFileName());
    }

    /**
     * 获取客户信息详细信息
     */
    @PostMapping(value = "/{id}")
    public ResponseResult<MdmClientinfo> getInfo(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(json)) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        String id = JSONObject.parseObject(json).getString("id");
        return new ResponseResult<>(mdmClientinfoService.selectMdmClientinfoById(token, id));
    }

    /**
     * 新增客户信息
     */
    @PostMapping("/add")
    public ResponseResult<Integer> add(@RequestBody(required = false) MdmClientinfo mdmClientinfo) {
        String token = RequestContext.getToken();
        if (mdmClientinfo == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(mdmClientinfoService.insertMdmClientinfo(token, mdmClientinfo));
    }

    /**
     * 修改客户信息
     */
    @PostMapping("/edit")
    @MenuAuthority(code = "客户信息-修改")
    public ResponseResult<Integer> edit(@RequestBody(required = false) MdmClientinfo mdmClientinfo) {
        String token = RequestContext.getToken();
        if (mdmClientinfo == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        ResponseResult<Integer> i = new ResponseResult<>();
        try {
            if (CollUtil.isEmpty(mdmClientinfo.getMdmClientInvoiceinfoList())) {
                return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "客户开票信息不可为空");
            }
            if (mdmClientinfo.getInvoiceMode().equals(InvoiceModeEnum.RADIO.getIntValue())) {
                if (mdmClientinfo.getBrandRatio() != null
                        && mdmClientinfo.getDeliveryRatio() != null
                        && mdmClientinfo.getFreightRatio() != null
                        && mdmClientinfo.getMessageRatio() != null
                        && mdmClientinfo.getPlatformRatio() != null
                        && mdmClientinfo.getStorageRatio() != null
                        && mdmClientinfo.getScmRadio() != null
                        && mdmClientinfo.getGpsRadio() != null
                        && mdmClientinfo.getUnloadRadio() != null
                        && mdmClientinfo.getConsumablesRadio() != null
                        && mdmClientinfo.getAirtransportRadio() != null) {
                    BigDecimal decimal = BigDecimal.valueOf(mdmClientinfo.getBrandRatio())
                            .add(BigDecimal.valueOf(mdmClientinfo.getDeliveryRatio()))
                            .add(BigDecimal.valueOf(mdmClientinfo.getFreightRatio()))
                            .add(BigDecimal.valueOf(mdmClientinfo.getMessageRatio()))
                            .add(BigDecimal.valueOf(mdmClientinfo.getPlatformRatio()))
                            .add(BigDecimal.valueOf(mdmClientinfo.getStorageRatio()))
                            .add(BigDecimal.valueOf(mdmClientinfo.getScmRadio()))
                            .add(BigDecimal.valueOf(mdmClientinfo.getGpsRadio()))
                            .add(BigDecimal.valueOf(mdmClientinfo.getUnloadRadio()))
                            .add(BigDecimal.valueOf(mdmClientinfo.getConsumablesRadio()))
                            .add(BigDecimal.valueOf(mdmClientinfo.getAirtransportRadio()));
                    if (decimal.compareTo(BigDecimal.ONE) != 0) {
                        return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "当开票方式为按比例开票时，费用比例合计必须为1");
                    }
                } else {
                    return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "当开票方式为按比例开票时，费用比例合计必须为1");
                }
            }
            if (mdmClientinfo.getCooperateType().equals(CooperateTypeEnum.COMPANY.getIntValue())) {
                List<String> openingNameList = new ArrayList<>();
                for (MdmClientInvoiceinfo item : mdmClientinfo.getMdmClientInvoiceinfoList()) {
                    if (mdmClientinfo.getCooperateType().equals(CooperateTypeEnum.COMPANY.getIntValue()) || mdmClientinfo.getCooperateType().equals(CooperateTypeEnum.PERSON.getIntValue()) || mdmClientinfo.getCooperateType().equals(PubNumEnum.four.getIntValue())) {
                        if (StrUtil.isEmpty(item.getOpeningName())) {
                            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "发票抬头不可为空");
                        }
                        if (StrUtil.isEmpty(item.getTaxpayerNum())) {
                            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "税号不可为空");
                        }
                    }
                    if (mdmClientinfo.getCooperateType().equals(CooperateTypeEnum.PERSON.getIntValue()) || mdmClientinfo.getCooperateType().equals(PubNumEnum.four.getIntValue())) {
                        if (StrUtil.isEmpty(item.getOpeningBank())) {
                            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "开户行不可为空");
                        }
                        if (StrUtil.isEmpty(item.getCardNumber())) {
                            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "银行账号不可为空");
                        }
                        if (StrUtil.isEmpty(item.getLinkPhone())) {
                            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "电话不可为空");
                        }
                        if (StrUtil.isEmpty(item.getTacAddress())) {
                            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "地址不可为空");
                        }
                    }
                    if (StrUtil.isNotEmpty(item.getOpeningName())) {
                        openingNameList.add(item.getOpeningName());
                    }
                }
                if (mdmClientinfo.getCooperateType().equals(CooperateTypeEnum.PERSON.getIntValue()) || mdmClientinfo.getCooperateType().equals(PubNumEnum.four.getIntValue())) {
                    if (openingNameList.isEmpty()) {
                        return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "开票类型为专票、专票+普票时,发票抬头信息至少有一行");
                    }
                }
            }
            i = new ResponseResult<>(mdmClientinfoService.updateMdmClientinfo(token, mdmClientinfo));
        } catch (Exception e) {
            log.error("系统异常", e);
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), ServiceError.SERVICE_ERROR.getMessage());
        }
        return i;
    }

    /**
     * 物理作废客户信息
     */
    @DeleteMapping("/{ids}")
    public ResponseResult<Integer> remove(@PathVariable String[] ids) {
        String token = RequestContext.getToken();
        if (ids == null || ids.length == 0) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(mdmClientinfoService.deleteMdmClientinfoByIds(token, ids));
    }

    /**
     * 逻辑作废/启用客户信息
     */
    @PostMapping(value = "/updateStatus")
    public ResponseResult<Integer> updateStatus(@RequestBody(required = false) Map<String, Object> idsAndStatus) {
        String token = RequestContext.getToken();
        List<String> idsList = (ArrayList<String>) idsAndStatus.get("ids");
        String[] ids = idsList.toArray(new String[0]);
        String status = String.valueOf(idsAndStatus.get("status"));
        return new ResponseResult<>(mdmClientinfoService.updateMdmClientinfoStatusByIds(token, ids, status));
    }

    /**
     * 获取客户信息详细信息
     */
    @PostMapping(value = "/invoiceinfo")
    public ResponseResult<List<MdmClientInvoiceinfo>> getclientInvoiceinfo(@RequestBody(required = false) MdmClientInvoiceinfo mdmClientInvoiceinfo) {
        String token = RequestContext.getToken();
        if (mdmClientInvoiceinfo == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(mdmClientinfoService.getclientInvoiceinfo(token, mdmClientInvoiceinfo));
    }

    /**
     * 获取客户信息详细信息
     */
    @PostMapping(value = "/getClientApplyRemark")
    public ResponseResult<Map<String, List<BmsApplyRemarkDict>>> getClientApplyRemark(@RequestBody(required = false) BmsApplyRemarkDict applyRemarkDict) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(mdmClientinfoService.getClientAppleInvoiceRemark(token, applyRemarkDict));
    }


    /**
     * 获取系统所有地区信息
     */
    //获取系统所有地区信息
    @PostMapping(value = "/getAllBelongArea")
    public ResponseResult<List<MdmCityarea>> getAllBelongArea() {
        String token = RequestContext.getToken();
        return new ResponseResult<>(mdmClientinfoService.getAllBelongArea(token));
    }

}
