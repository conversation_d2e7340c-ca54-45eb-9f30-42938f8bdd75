package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.BmsExpenseItemInfo;
import com.bbyb.joy.bms.service.BmsExpenseItemService;
import com.bbyb.joy.bms.support.ServiceError;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "费用项接口")
@RestController
@RequestMapping("/system/expenseItem")
public class BmsExpenseItemController {

    @Resource
    private BmsExpenseItemService itemService;

    @PostMapping("/list")
    public ResponseResult<List<BmsExpenseItemInfo>> getExpenseItem(@RequestBody(required = false) BmsExpenseItemInfo info) {
        if (info == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(itemService.getItems(info));
    }

    @PostMapping("/listByPubYf")
    public ResponseResult<List<BmsExpenseItemInfo>> getExpenseItemByPubYf(@RequestBody(required = false) BmsExpenseItemInfo info) {
        if (info == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(itemService.getExpenseItemByPubYf(info));
    }

}
