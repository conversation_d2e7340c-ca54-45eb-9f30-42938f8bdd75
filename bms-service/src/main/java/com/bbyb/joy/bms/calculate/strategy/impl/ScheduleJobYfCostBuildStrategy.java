package com.bbyb.joy.bms.calculate.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import com.bbyb.bms.model.po.BmsYfcostInfoPO;
import com.bbyb.bms.model.po.BmsYfcostMainInfoPO;
import com.bbyb.bms.model.po.BmsYfexpensesMiddlePO;
import com.bbyb.bms.model.po.BmsYfexpensesMiddleSharePO;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.calculate.domain.result.CalculateCostResult;
import com.bbyb.joy.bms.calculate.strategy.YfCostBuildStrategy;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyCalculateExtraType;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import com.bbyb.joy.bms.support.utils.DateUtils;
import com.bbyb.joy.bms.support.utils.cost.CostUtil;
import com.bbyb.joy.bms.support.utils.cost.StatisticsFieldEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 调度单维度计费，按照作业单维度分摊策略
 * 
 */
@Slf4j
@Component
public class ScheduleJobYfCostBuildStrategy implements YfCostBuildStrategy {

    @Resource
    private DefaultYfCostBuildStrategy defaultStrategy;

    @Override
    public GroovyCalculateExtraType getSupportedType() {
        return GroovyCalculateExtraType.SCHEDULT_JOB;
    }

    @Override
    public YfCostBuildResult buildYfCostInfo(CalculateCostResult costResult, String costMainId, String mainExpenseCode,
                                           String costId, String expenseCode, Integer optCodePkId, Set<Integer> optCodeDetail2PkIds,
                                           UserBean userInfo, Date now, Map<Integer, Dict> allCodeDetail2DataMap) {
        
        log.info("使用调度单作业单分摊策略构建应付费用信息，optCodePkId: {}", optCodePkId);
        
        // 检查extraInfo是否有值
        Map<String, Object> extraInfo = costResult.getExtraInfo();
        if (extraInfo == null || extraInfo.isEmpty()) {
            log.warn("extraInfo为空，回退到默认策略处理");
            return defaultStrategy.buildYfCostInfo(costResult, costMainId, mainExpenseCode, costId, expenseCode,
                                                 optCodePkId, optCodeDetail2PkIds, userInfo, now, allCodeDetail2DataMap);
        }
        
        if (CollUtil.isEmpty(optCodeDetail2PkIds)) {
            log.warn("作业单明细为空，回退到默认策略处理");
            return defaultStrategy.buildYfCostInfo(costResult, costMainId, mainExpenseCode, costId, expenseCode,
                                                 optCodePkId, optCodeDetail2PkIds, userInfo, now, allCodeDetail2DataMap);
        }
        
        // 先构建分摊表信息（根据extraInfo）
        List<BmsYfexpensesMiddleSharePO> buildSharePOList = buildJobShareCostInfo(
            costResult, costMainId, optCodeDetail2PkIds, extraInfo, allCodeDetail2DataMap);
        
        if (CollUtil.isEmpty(buildSharePOList)) {
            log.warn("构建作业单分摊信息失败，回退到默认策略处理");
            return defaultStrategy.buildYfCostInfo(costResult, costMainId, mainExpenseCode, costId, expenseCode,
                                                 optCodePkId, optCodeDetail2PkIds, userInfo, now, allCodeDetail2DataMap);
        }
        
        // 向上汇总到主费用信息和费用信息
        BmsYfcostMainInfoPO buildMainCostInfo = buildMainCostInfoFromShare(
            costResult, costMainId, mainExpenseCode, buildSharePOList, userInfo, now);
        List<BmsYfcostInfoPO> buildCostInfos = Lists.newArrayList();
        BmsYfcostInfoPO buildCostInfo = buildCostInfoFromShare(
            costResult, costId, expenseCode, costMainId, buildSharePOList, userInfo, now);
        buildCostInfos.add(buildCostInfo);
        BmsYfexpensesMiddlePO buildCostMiddle = buildMiddleCostInfoFromShare(
            costResult, costMainId, optCodePkId, buildSharePOList);

        return new YfCostBuildResult(buildMainCostInfo, buildCostInfos, buildCostMiddle, buildSharePOList);
    }

    /**
     * 根据extraInfo构建作业单分摊费用信息
     */
    private List<BmsYfexpensesMiddleSharePO> buildJobShareCostInfo(CalculateCostResult costResult, String costMainId,
                                                                 Set<Integer> optCodeDetail2PkIds, Map<String, Object> extraInfo,
                                                                 Map<Integer, Dict> allCodeDetail2DataMap) {
        
        List<BmsYfexpensesMiddleSharePO> buildSharePOList = Lists.newArrayList();
        Map<Integer, Dict> codeDetail2DataMap = costResult.getCodeDetail2DataMap();
        allCodeDetail2DataMap.putAll(codeDetail2DataMap);
        
        for (Integer optCodeDetail2PkId : optCodeDetail2PkIds) {
            BmsYfexpensesMiddleSharePO buildShareData = new BmsYfexpensesMiddleSharePO();
            buildShareData.setCodePkId(optCodeDetail2PkId);
            buildShareData.setMainCodeId(costMainId);
            buildShareData.setShareType(costResult.getShareType());
            buildShareData.setOptMonth(DateUtils.optMonth());
            buildShareData.setOptDay(DateUtils.optDay());
            
            // 设置统计字段（重量、体积、件数）
            if(codeDetail2DataMap.containsKey(optCodeDetail2PkId)){
                Dict dict = codeDetail2DataMap.get(optCodeDetail2PkId);
                BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) ? 
                    dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) : BigDecimal.ZERO;
                BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ? 
                    dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
                BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ? 
                    dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
                buildShareData.setTotalWeight(totalWeight);
                buildShareData.setTotalVolume(totalVolume);
                buildShareData.setTotalNumber(totalNumber);
            }
            
            // 根据extraInfo设置费用信息（key为作业单号，value为该作业单的费用）
            String jobCode = getJobCodeByPkId(optCodeDetail2PkId, codeDetail2DataMap);
            if (jobCode != null && extraInfo.containsKey(jobCode)) {
                Object jobCostObj = extraInfo.get(jobCode);
                BigDecimal jobCost = convertToBigDecimal(jobCostObj);
                if (jobCost != null) {
                    // 设置结算金额
                    buildShareData.setSettleAmount(jobCost);
                    // 根据费用类型设置对应的费用字段
                    buildShareData = CostUtil.setCostField(buildShareData, costResult.getFeeType(), jobCost);
                } else {
                    log.warn("作业单 {} 的费用值无法转换为BigDecimal: {}", jobCode, jobCostObj);
                    buildShareData.setSettleAmount(BigDecimal.ZERO);
                }
            } else {
                log.warn("作业单 {} 在extraInfo中未找到对应费用", jobCode);
                buildShareData.setSettleAmount(BigDecimal.ZERO);
            }
            
            buildSharePOList.add(buildShareData);
        }
        
        return buildSharePOList;
    }

    /**
     * 根据作业单主键ID获取作业单号
     */
    private String getJobCodeByPkId(Integer codePkId, Map<Integer, Dict> codeDetail2DataMap) {
        if (codeDetail2DataMap.containsKey(codePkId)) {
            Dict dict = codeDetail2DataMap.get(codePkId);
            // 假设作业单号字段名为 "jobCode" 或 "relateCode"
            if (dict.containsKey("jobCode")) {
                return dict.getStr("jobCode");
            } else if (dict.containsKey("relateCode")) {
                return dict.getStr("relateCode");
            }
        }
        // 如果没有找到，返回主键ID的字符串形式作为备用
        return codePkId.toString();
    }

    /**
     * 将Object转换为BigDecimal
     */
    private BigDecimal convertToBigDecimal(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为BigDecimal: {}", value);
                return null;
            }
        }
        return null;
    }

    /**
     * 从分摊信息汇总构建主费用信息
     */
    private BmsYfcostMainInfoPO buildMainCostInfoFromShare(CalculateCostResult costResult, String costMainId,
                                                          String mainExpenseCode, List<BmsYfexpensesMiddleSharePO> shareList,
                                                          UserBean userInfo, Date now) {

        BmsYfcostMainInfoPO buildMainCostInfo = new BmsYfcostMainInfoPO();
        buildMainCostInfo.setId(costMainId);
        buildMainCostInfo.setExpensesCode(mainExpenseCode);
        buildMainCostInfo.setExpensesType(costResult.getExpenseType());
        buildMainCostInfo.setExpensesDimension(costResult.getExpensesDimension());
        buildMainCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
        buildMainCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
        buildMainCostInfo.setBusinessTime(costResult.getBusinessTime());
        buildMainCostInfo.setDispatchDate(costResult.getBeginTime());
        buildMainCostInfo.setFinishDate(costResult.getEndTime());
        buildMainCostInfo.setCarrierId(costResult.getCarrierId());
        buildMainCostInfo.setClientId(costResult.getClientId());
        buildMainCostInfo.setCompanyId(costResult.getCompanyId());
        buildMainCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
        buildMainCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
        buildMainCostInfo.setTotalBoxes(costResult.getTotalBoxes());
        buildMainCostInfo.setTotalNumber(costResult.getTotalNumber());
        buildMainCostInfo.setTotalWeight(costResult.getTotalWeight());
        buildMainCostInfo.setTotalVolume(costResult.getTotalVolume());
        buildMainCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
        buildMainCostInfo.setTotalOverNum(costResult.getTotalOverNum());
        buildMainCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
        buildMainCostInfo.setSettleType(costResult.getSettleType());
        buildMainCostInfo.setSettleEntityType(costResult.getSettleEntityType());
        buildMainCostInfo.setSettleSetting(costResult.getSettleSetting());
        buildMainCostInfo.setSettleMainId(costResult.getSettleMainId());
        buildMainCostInfo.setCreateBy(userInfo.getEmployeename());
        buildMainCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
        buildMainCostInfo.setCreateTime(now);
        buildMainCostInfo.setOperBy(userInfo.getEmployeename());
        buildMainCostInfo.setOperCode(userInfo.getEmployeecode().toString());
        buildMainCostInfo.setOperTime(now);
        buildMainCostInfo.setOptMonth(DateUtils.optMonth());
        buildMainCostInfo.setOptDay(DateUtils.optDay());

        // 使用CostUtil汇总分摊表的费用到主费用信息
        buildMainCostInfo = CostUtil.aggregateCost(buildMainCostInfo, shareList,
            BmsYfcostMainInfoPO.class, CostUtil.YF_COST_SETTLE_SUM_FIELDS);

        return buildMainCostInfo;
    }

    /**
     * 从分摊信息汇总构建费用信息
     */
    private BmsYfcostInfoPO buildCostInfoFromShare(CalculateCostResult costResult, String costId, String expenseCode,
                                                  String costMainId, List<BmsYfexpensesMiddleSharePO> shareList,
                                                  UserBean userInfo, Date now) {

        BmsYfcostInfoPO buildCostInfo = new BmsYfcostInfoPO();
        buildCostInfo.setId(costId);
        buildCostInfo.setExpensesCode(expenseCode);
        buildCostInfo.setMainCodeId(costMainId);
        buildCostInfo.setExpensesType(costResult.getExpenseType());
        buildCostInfo.setExpensesDimension(costResult.getExpensesDimension());
        buildCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
        buildCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
        buildCostInfo.setBusinessTime(costResult.getBusinessTime());
        buildCostInfo.setBillDate(costResult.getBillDate());
        buildCostInfo.setDispatchDate(costResult.getBeginTime());
        buildCostInfo.setFinishDate(costResult.getEndTime());
        buildCostInfo.setClientId(costResult.getClientId());
        buildCostInfo.setCarrierId(costResult.getCarrierId());
        buildCostInfo.setCompanyId(costResult.getCompanyId());
        buildCostInfo.setWarehouseCode(costResult.getWarehouseCode());
        buildCostInfo.setWarehouseName(costResult.getWarehouseName());
        buildCostInfo.setQuoterRuleId(costResult.getRuleMainPkId().toString());
        buildCostInfo.setQuoterRuleName(costResult.getRuleMainName());
        buildCostInfo.setQuoterRuleDetailId(costResult.getRuleDetailPkId().toString());
        buildCostInfo.setQuoterRuleDetailName(costResult.getRuleDetailName());
        buildCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
        buildCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
        buildCostInfo.setTotalBoxes(costResult.getTotalBoxes());
        buildCostInfo.setTotalNumber(costResult.getTotalNumber());
        buildCostInfo.setTotalWeight(costResult.getTotalWeight());
        buildCostInfo.setTotalVolume(costResult.getTotalVolume());
        buildCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
        buildCostInfo.setTotalOverNum(costResult.getTotalOverNum());
        buildCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
        buildCostInfo.setSettleType(costResult.getSettleType());
        buildCostInfo.setSettleEntityType(costResult.getSettleEntityType());
        buildCostInfo.setSettleSetting(costResult.getSettleSetting());
        buildCostInfo.setSettleMainId(costResult.getSettleMainId());
        buildCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
        buildCostInfo.setCreateBy(userInfo.getEmployeecode().toString());
        buildCostInfo.setCreateTime(now);
        buildCostInfo.setOperBy(userInfo.getEmployeecode().toString());
        buildCostInfo.setOperCode(userInfo.getEmployeecode().toString());
        buildCostInfo.setOperTime(now);
        buildCostInfo.setOptMonth(DateUtils.optMonth());
        buildCostInfo.setOptDay(DateUtils.optDay());

        // 使用CostUtil汇总分摊表的费用到费用信息
        buildCostInfo = CostUtil.aggregateCost(buildCostInfo, shareList,
            BmsYfcostInfoPO.class, CostUtil.YF_COST_SETTLE_SUM_FIELDS);

        return buildCostInfo;
    }

    /**
     * 从分摊信息汇总构建中间表信息
     */
    private BmsYfexpensesMiddlePO buildMiddleCostInfoFromShare(CalculateCostResult costResult, String costMainId,
                                                             Integer optCodePkId, List<BmsYfexpensesMiddleSharePO> shareList) {

        BmsYfexpensesMiddlePO buildCostMiddle = new BmsYfexpensesMiddlePO();
        buildCostMiddle.setCodePkId(optCodePkId);
        buildCostMiddle.setCodeType(costResult.getCodeType());
        buildCostMiddle.setMainCodeId(costMainId);
        buildCostMiddle.setShareType(costResult.getShareType());
        buildCostMiddle.setOptMonth(DateUtils.optMonth());
        buildCostMiddle.setOptDay(DateUtils.optDay());

        // 汇总统计字段
        BigDecimal totalWeight = shareList.stream()
            .map(BmsYfexpensesMiddleSharePO::getTotalWeight)
            .filter(java.util.Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalVolume = shareList.stream()
            .map(BmsYfexpensesMiddleSharePO::getTotalVolume)
            .filter(java.util.Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalNumber = shareList.stream()
            .map(BmsYfexpensesMiddleSharePO::getTotalNumber)
            .filter(java.util.Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        buildCostMiddle.setTotalWeight(totalWeight);
        buildCostMiddle.setTotalVolume(totalVolume);
        buildCostMiddle.setTotalNumber(totalNumber);

        // 使用CostUtil汇总分摊表的费用到中间表
        buildCostMiddle = CostUtil.aggregateCost(buildCostMiddle, shareList,
            BmsYfexpensesMiddlePO.class, CostUtil.YF_COST_SETTLE_SUM_FIELDS);

        return buildCostMiddle;
    }
}
