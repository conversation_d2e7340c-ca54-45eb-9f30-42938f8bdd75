package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.biz.BmsClaimsInfoBiz;
import com.bbyb.joy.bms.biz.BmsYsbillmainBiz;
import com.bbyb.joy.bms.domain.dto.BmsClaimsInfo;
import com.bbyb.joy.bms.domain.dto.BmsFixedExpenseInfo;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain;
import com.bbyb.joy.bms.domain.dto.dto.BmsFixedExpenseDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYsFixedExpenseDto;
import com.bbyb.joy.bms.domain.dto.querybean.BmsFixedExpenseQueryBean;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.service.BmsFixedExpenseService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Api(tags = "费用项接口")
@RestController
@RequestMapping("/system/fixedExpense")
public class BmsFixedExpenseController {

    @Resource
    private ExportUtil exportUtil;
    @Resource
    private BmsFixedExpenseService expenseService;
    @Resource
    private BmsYsbillmainBiz bmsYsbillmainMapper;
    @Resource
    private BmsClaimsInfoBiz bmsClaimsInfoMapper;
    @Resource
    private UserRights userRights;

    /**
     * 固定费用分页查询
     */
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<BmsFixedExpenseDto>> list(HttpServletRequest request, @RequestBody BmsFixedExpenseQueryBean queryBean) {
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client,carrier");
        if ("1".equals(queryBean.getSettleType())) {
            // 应收是客户
            // 客户不会为空
            queryBean.setClientIds(new ArrayList<>(sysDataPack.getClientinfo().stream().map(ClientInfo::getId).collect(Collectors.toSet())));
        } else if ("2".equals(queryBean.getSettleType())) {
            // 应付是承运商
            // 承运商可能为空，为空就查全部了
            if (CollUtil.isEmpty(sysDataPack.getCarrierInfo())) {
                List<String> codes = new ArrayList<>();
                codes.add("");
                queryBean.setCarrierCodes(codes);
            } else {
                queryBean.setCarrierCodes(new ArrayList<>(sysDataPack.getCarrierInfo().stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toSet())));
            }
        } else {
            return new ResponseResult<>(400401, "错误的结算类别");
        }
        return new ResponseResult<>(expenseService.list(RequestContext.getToken(), queryBean));
    }

    /**
     * 固定费用作废
     *
     * @param info 费用信息
     */
    @PostMapping("/del")
    @MenuAuthority(code = "应收固定费管理-作废")
    public ResponseResult<String> del(@RequestBody BmsFixedExpenseInfo info) {
        return new ResponseResult<>(expenseService.del(RequestContext.getToken(), info));
    }

    /**
     * 固定费用导出
     */
    @PostMapping("/export")
    @MenuAuthority(code = "应收固定费管理-导出全部")
    public ResponseResult<String> export(HttpServletRequest request, @RequestBody BmsFixedExpenseQueryBean queryBean) {
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client,carrier");
        if ("1".equals(queryBean.getSettleType())) {
            // 应收是客户
            queryBean.setClientIds(new ArrayList<>(sysDataPack.getClientinfo().stream().map(ClientInfo::getId).collect(Collectors.toSet())));
        } else if ("2".equals(queryBean.getSettleType())) {
            // 应付是承运商
            // 承运商可能为空，为空就查全部了
            if (CollUtil.isEmpty(sysDataPack.getCarrierInfo())) {
                List<String> codes = new ArrayList<>();
                codes.add("");
                queryBean.setCarrierCodes(codes);
            } else {
                queryBean.setCarrierCodes(new ArrayList<>(sysDataPack.getCarrierInfo().stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toSet())));
            }
        } else {
            return new ResponseResult<>(400401, "错误的结算类别");
        }
        queryBean.setClientIds(new ArrayList<>(sysDataPack.getClientinfo().stream().map(ClientInfo::getId).collect(Collectors.toSet())));
        String finalToken = RequestContext.getToken();
        return exportUtil.getOutClassNew(finalToken, "固定费用管理导出", "固定费用管理", BmsFixedExpenseDto.class, id -> expenseService.list(finalToken, queryBean).getRows());
    }


    /**
     * 应收-固定费用查询
     */
    @PostMapping("/getFixedFeeByYs/{billId}/{settleType}")
    public ResponseResult<BmsYsFixedExpenseDto> getFixedFeeByYs(HttpServletRequest request, @PathVariable("billId") Integer billId, @PathVariable("settleType") String settleType) {
        UserBean userVO = RequestContext.getUserInfo();
        BmsFixedExpenseQueryBean queryBean = new BmsFixedExpenseQueryBean();
        queryBean.setBillId(billId);
        queryBean.setSettleType(settleType);
        //固定费信息
        PagerDataBean<BmsFixedExpenseDto> list = expenseService.listByYsReconciliation(RequestContext.getToken(), queryBean);
        //FIXME 因为查询固定费明细的时候有赋值billIds所以需要重置下账单id数据
        queryBean.setBillId(billId);
        queryBean.setBillIds(null);
        BmsYsFixedExpenseDto ysFixedExpenseDto = expenseService.getFixedFeeByYs(RequestContext.getToken(), queryBean);
        if (list != null && CollUtil.isNotEmpty(list.getRows()) && ysFixedExpenseDto != null) {
            ysFixedExpenseDto.setTableDataInfo(list);
            ysFixedExpenseDto.setIsFeeShow(true);
        } else {
            Objects.requireNonNull(ysFixedExpenseDto).setIsFeeShow(false);
        }
        List<BmsYsbillmain> billCodeById = bmsYsbillmainMapper.getBillCodeById(RequestContext.getTenantId(), billId.longValue());
        if (CollUtil.isEmpty(billCodeById)) {
            ysFixedExpenseDto.setIsClaims(0);
        } else {
            BmsClaimsInfo bmsClaimsInfo = new BmsClaimsInfo();
            bmsClaimsInfo.setBillCode(billCodeById.stream().map(BmsYsbillmain::getBillCode).collect(Collectors.joining(",")));
            List<BmsClaimsInfo> bmsClaimsInfos = bmsClaimsInfoMapper.selectBmsClaimsInfoList(RequestContext.getTenantId(), bmsClaimsInfo).getRows();
            if (CollUtil.isNotEmpty(bmsClaimsInfos)) {
                ysFixedExpenseDto.setIsClaims(1);
            } else {
                ysFixedExpenseDto.setIsClaims(0);
            }
        }
        return new ResponseResult<>(ysFixedExpenseDto);
    }


    /**
     * 应收-批量更新固定费用编辑保存
     * <p> Date 2023-03-31 13:35
     */
    @PostMapping("/updateBatchByYs")
    public ResponseResult<String> updateBatchByYs(@RequestBody BmsFixedExpenseQueryBean queryBean) {
        return new ResponseResult<>(expenseService.updateBatchByYs(RequestContext.getToken(), queryBean));
    }


}
