package com.bbyb.joy.bms.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.CompanyNetwork;
import com.bbyb.joy.bms.domain.dto.FileBaseDto;
import com.bbyb.joy.bms.domain.dto.MdmWarehouseinfo;
import com.bbyb.joy.bms.service.IMdmClientinfoService;
import com.bbyb.joy.bms.service.IMdmWarehouseinfoService;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.utils.ExcelUtil;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 仓库信息Controller
 */
@RestController
@RequestMapping("/system/warehouseinfo")
public class MdmWarehouseinfoController {

    @Resource
    private IMdmWarehouseinfoService mdmWarehouseinfoService;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private IMdmClientinfoService mdmClientinfoService;

    /**
     * 查询仓库信息列表
     */
    @PostMapping("/list")
    @MenuAuthority(code = "仓库信息")
    public ResponseResult<PagerDataBean<MdmWarehouseinfo>> list(@RequestBody(required = false) MdmWarehouseinfo mdmWarehouseinfo) {
        String token = RequestContext.getToken();
        if (mdmWarehouseinfo == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(mdmWarehouseinfoService.selectMdmWarehouseinfoList(token, mdmWarehouseinfo));
    }

    /**
     * 查询仓库信息列表
     */
    @PostMapping("/list2")
    public ResponseResult<PagerDataBean<MdmWarehouseinfo>> list2(@RequestBody(required = false) MdmWarehouseinfo mdmWarehouseinfo) {
        String token = RequestContext.getToken();
        if (mdmWarehouseinfo == null) {
            mdmWarehouseinfo = new MdmWarehouseinfo();
        }
        return new ResponseResult<>(mdmWarehouseinfoService.selectMdmWarehouseinfoList(token, mdmWarehouseinfo));
    }

    /**
     * 查询所有网点
     */
    @PostMapping(value = "/getNetWorkList")
    public ResponseResult<List<CompanyNetwork>> getNetWorkList(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(mdmClientinfoService.getNetWorkList(token));
    }

    /**
     * 保存仓库网点对应关系（查询 companyNetwork是否存在对应的省区关系）
     */
    @PostMapping(value = "/saveNetWork")
    public ResponseResult<Integer> saveNetWork(@RequestBody(required = false) CompanyNetwork companyNetwork) {
        String token = RequestContext.getToken();
        if (companyNetwork == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        if (StrUtil.isEmpty(companyNetwork.getWarehouseCode())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "仓库编码不可为空");
        }
        if (StrUtil.isEmpty(companyNetwork.getBillName())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "所属地区不可为空");
        }
        return new ResponseResult<>(mdmClientinfoService.saveCompanyWork(token, companyNetwork));
    }


    /**
     * 导出仓库信息列表
     */
    @PostMapping("/export")
    public ResponseResult<String> export(@RequestBody(required = false) MdmWarehouseinfo mdmWarehouseinfo) {
        String token = RequestContext.getToken();
        PagerDataBean<MdmWarehouseinfo> list = mdmWarehouseinfoService.selectMdmWarehouseinfoList(token, mdmWarehouseinfo);
        ExcelUtil<MdmWarehouseinfo> util = new ExcelUtil<>(MdmWarehouseinfo.class);
        FileBaseDto fileBaseDto = util.exportExcel(list.getRows(), "仓库信息数据");
        return new ResponseResult<>(true, 0, fileBaseDto.getFileName(), fileBaseDto.getFileName());
    }

    /**
     * 获取仓库信息详细信息
     */
    @PostMapping(value = "/{id}")
    public ResponseResult<MdmWarehouseinfo> getInfo(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        String id = JSONObject.parseObject(json).getString("id");
        if (StrUtil.isEmpty(id)) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(mdmWarehouseinfoService.selectMdmWarehouseinfoById(token, id));
    }

    /**
     * 新增仓库信息
     */
    @PostMapping("/add")
    public ResponseResult<Integer> add(@RequestBody(required = false) MdmWarehouseinfo mdmWarehouseinfo) {
        String token = RequestContext.getToken();
        if (mdmWarehouseinfo == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(mdmWarehouseinfoService.insertMdmWarehouseinfo(token, mdmWarehouseinfo));
    }

    /**
     * 修改仓库信息
     */
    @PostMapping("/edit")
    @MenuAuthority(code = "仓库信息-修改")
    public ResponseResult<Integer> edit(@RequestBody(required = false) MdmWarehouseinfo mdmWarehouseinfo) {
        String token = RequestContext.getToken();
        if (mdmWarehouseinfo == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(mdmWarehouseinfoService.updateMdmWarehouseinfo(token, mdmWarehouseinfo));
    }

    /**
     * 物理作废仓库信息
     */
    @DeleteMapping("/{ids}")
    public ResponseResult<Integer> remove(@PathVariable String[] ids) {
        String token = RequestContext.getToken();
        if (ids == null || ids.length == 0) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(mdmWarehouseinfoService.deleteMdmWarehouseinfoByIds(token, ids));
    }

    /**
     * 逻辑作废/启用仓库信息
     */
    @PostMapping(value = "/updateStatus")
    public ResponseResult<Integer> updateStatus(@RequestBody(required = false) Map<String, Object> idsAndStatus) {
        String token = RequestContext.getToken();
        List<String> idsList = (ArrayList<String>) idsAndStatus.get("ids");
        String[] ids = idsList.toArray(new String[0]);
        String status = String.valueOf(idsAndStatus.get("status"));
        return new ResponseResult<>(mdmWarehouseinfoService.updateMdmWarehouseinfoStatusByIds(token, ids, status));
    }
}
