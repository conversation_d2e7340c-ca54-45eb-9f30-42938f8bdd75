package com.bbyb.joy.bms.calculate.domain.dto.code.batch;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 批详情公共属性
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateBatchDetailDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * skuCode
     * 计费过程使用
     */
    public String skuCode;

    /**
     * skuName
     * 计费过程使用
     */
    public String skuName;

    /**
     * 涉及SKU数量(sku种类数汇总)
     */
    public Integer totalSkuNumber = 0;

    /**
     * 总箱数
     */
    public BigDecimal totalBoxes = BigDecimal.ZERO;

    /**
     * 总件数
     */
    public BigDecimal totalNumber = BigDecimal.ZERO;

    /**
     * 总重量
     */
    public BigDecimal totalWeight = BigDecimal.ZERO;

    /**
     * 总体积
     */
    public BigDecimal totalVolume = BigDecimal.ZERO;

    /**
     * 总托数
     */
    public BigDecimal totalPalletNumber = BigDecimal.ZERO;


    /* 去重维度 ****************************/
    /**
     * 总托数(按托盘编码去重)
     */
    public Integer distinctTotalPalletNumber = 0;


}
