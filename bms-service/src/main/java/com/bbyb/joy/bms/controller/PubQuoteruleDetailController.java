package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.constant.UserConstants;
import com.bbyb.joy.bms.domain.dto.PubQuotationClassificationdetail;
import com.bbyb.joy.bms.domain.dto.PubQuoteruleDetail;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.domain.enums.RuleTypeEnum;
import com.bbyb.joy.bms.service.IPubQuoteruleDetailService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.exception.BusinessException;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报价管理主Controller
 */
@Slf4j
@RestController
@RequestMapping("/system/detail")
public class PubQuoteruleDetailController {
    @Resource
    private IPubQuoteruleDetailService pubQuoteruleDetailService;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private UserRights userRights;
    @Resource
    private ExportUtil exportUtil;


    /**
     * 查询报价明细管理主列表
     */
    @PostMapping("/list")
    @MenuAuthority(code = "承运商报价明细设置")
    public ResponseResult<PagerDataBean<PubQuoteruleDetail>> list(@RequestBody(required = false) PubQuoteruleDetail pubQuoteruleDetail) {
        if (pubQuoteruleDetail == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        List<PubQuoteruleDetail> list = new ArrayList<>();

        // permission
        List<PermissionConfig> permissionConfigs = new ArrayList<>();
        permissionConfigs.add(new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.STRING, "id"));
        if (pubQuoteruleDetail.getRuleType() != null && pubQuoteruleDetail.getRuleType().equals(RuleTypeEnum.TRANS.getIntValue())) {
            permissionConfigs.add(new PermissionConfig(PermissionType.CARRIER.getCode(), "clientList", PermissionConfig.FieldType.LIST_STRING, "clientCode"));
        }
        if (pubQuoteruleDetail.getRuleType() != null && pubQuoteruleDetail.getRuleType().equals(RuleTypeEnum.STORAGE.getIntValue())) {
            permissionConfigs.add(new PermissionConfig(PermissionType.CARRIER.getCode(), "carrierList", PermissionConfig.FieldType.LIST_STRING, "carrierCode"));
        }
        pubQuoteruleDetail = userRights.applyPermissions(pubQuoteruleDetail, permissionConfigs);

        List<String> idList = new ArrayList<>();
        if (StrUtil.isNotEmpty(pubQuoteruleDetail.getClientCode()) || StrUtil.isNotEmpty(pubQuoteruleDetail.getDeliverArea())
                || StrUtil.isNotEmpty(pubQuoteruleDetail.getReceiveArea()) || StrUtil.isNotEmpty(pubQuoteruleDetail.getRouteCode())
                || StrUtil.isNotEmpty(pubQuoteruleDetail.getWarehouseName())) {
            List<PubQuotationClassificationdetail> pubQuotationClassificationdetailList = textConversionUtil.getPubClass(pubQuoteruleDetail);
            if (CollUtil.isNotEmpty(pubQuotationClassificationdetailList)) {
                idList = pubQuotationClassificationdetailList.stream().map(PubQuotationClassificationdetail::getQuoteruledetailId).distinct().collect(Collectors.toList());
            } else {
                return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(pubQuoteruleDetail.getPage(), pubQuoteruleDetail.getSize())));
            }
        }
        if (CollUtil.isNotEmpty(idList)) {
            pubQuoteruleDetail.setIdList(idList);
        }
        return new ResponseResult<>(pubQuoteruleDetailService.selectPubQuoteruleDetailList(RequestContext.getToken(), pubQuoteruleDetail));
    }


    /**
     * 导出报价管理主列表
     */
    @PostMapping("/export")
    public ResponseResult<String> export(@RequestBody(required = false) PubQuoteruleDetail pubQuoteruleDetail) {
        String token = RequestContext.getToken();
        if (cn.hutool.core.util.StrUtil.isEmpty(pubQuoteruleDetail.getCompanyIds())) {
            pubQuoteruleDetail.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        return exportUtil.getOutClassNew(token, "报价明细设置导出", "报价明细管理", PubQuoteruleDetail.class, userId -> {
            SysDataPack sysDataPack = userRights.getRightsByToken(token, "client,carrier");
            List<ClientInfo> clientList = sysDataPack.getClientinfo();
            List<Carrierinfo> carrierList = sysDataPack.getCarrierInfo();
            if (pubQuoteruleDetail.getRuleType() != null && pubQuoteruleDetail.getRuleType().equals(RuleTypeEnum.TRANS.getIntValue())) {
                if (CollUtil.isEmpty(clientList)) {
                    return null;
                }
                pubQuoteruleDetail.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
            }
            if (pubQuoteruleDetail.getRuleType() != null && pubQuoteruleDetail.getRuleType().equals(RuleTypeEnum.STORAGE.getIntValue())) {
                if (CollUtil.isEmpty(carrierList)) {
                    return null;
                }
                pubQuoteruleDetail.setCarrierList(carrierList.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toList()));
            }
            List<String> idList = new ArrayList<>();
            if (StrUtil.isNotEmpty(pubQuoteruleDetail.getClientCode()) || StrUtil.isNotEmpty(pubQuoteruleDetail.getDeliverArea())
                    || StrUtil.isNotEmpty(pubQuoteruleDetail.getReceiveArea()) || StrUtil.isNotEmpty(pubQuoteruleDetail.getRouteCode())
                    || StrUtil.isNotEmpty(pubQuoteruleDetail.getWarehouseName())) {
                List<PubQuotationClassificationdetail> pubQuotationClassificationdetailList = textConversionUtil.getPubClass(pubQuoteruleDetail);
                if (CollUtil.isNotEmpty(pubQuotationClassificationdetailList)) {
                    idList = pubQuotationClassificationdetailList.stream().map(PubQuotationClassificationdetail::getQuoteruledetailId).distinct().collect(Collectors.toList());
                } else {
                    return null;
                }
            }
            if (CollUtil.isNotEmpty(idList)) {
                pubQuoteruleDetail.setIdList(idList);
            }
            return pubQuoteruleDetailService.selectPubQuoteruleDetailList(token, pubQuoteruleDetail).getRows();
        });
    }

    /**
     * 获取报价明细管理主详细信息
     */
    @PostMapping(value = "/{id}")
    public ResponseResult<PubQuoteruleDetail> getInfo(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(json)) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        String id = "";
        if (StrUtil.isNotEmpty(json)) {
            id = JSONObject.parseObject(json).getString("id");
        }
        return new ResponseResult<>(pubQuoteruleDetailService.selectPubQuoteruleDetailById(token, id));
    }

    /**
     * 审核报价管理明细主
     */
    @PostMapping(value = "/audit")
    @MenuAuthority(code = "承运商报价明细设置-审核")
    public ResponseResult<Integer> audit(@RequestBody(required = false) PubQuoteruleDetail pubQuoteruleDetail) {
        String token = RequestContext.getToken();
        if (pubQuoteruleDetail == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }

        return new ResponseResult<>(pubQuoteruleDetailService.auditPubQuoteruleDetail(token, pubQuoteruleDetail));
    }

    /**
     * 批量作废报价管理明细主
     */
    @DeleteMapping("/{ids}")
    @MenuAuthority(code = "承运商报价明细设置-作废")
    public ResponseResult<Integer> delete(@PathVariable String[] ids) {
        String token = RequestContext.getToken();
        if (ids == null || ids.length == 0) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(pubQuoteruleDetailService.deletePubQuoteruleDetailByIds(token, ids));
    }

    /**
     * 新增报价管理明细
     */
    @PostMapping("/add")
    @MenuAuthority(code = "承运商报价明细设置-新增")
    public ResponseResult<Integer> add(@RequestBody(required = false) PubQuoteruleDetail pubQuoteruleDetail) {
        String token = RequestContext.getToken();
        if (pubQuoteruleDetail == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        // 去掉空格
        removeSpace(pubQuoteruleDetail);
        if (StrUtil.isEmpty(pubQuoteruleDetail.getRulecode())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "明细编码不能为空");
        }
        if (pubQuoteruleDetail.getQuoteruleId() == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "报价编码不能为空");
        }

        if (StrUtil.isEmpty(pubQuoteruleDetail.getFiles())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "报价附件不能为空");
        }
        if (pubQuoteruleDetail.getQuoteruleTemplateId() == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "规则模板不能为空");
        }
        if (CollUtil.isEmpty(pubQuoteruleDetail.getQuotationClassificationdetailList())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "报价分级明细不可为空");
        } else if (UserConstants.NOT_UNIQUE.equals(pubQuoteruleDetailService.checkRuleDetailCodeUnique(token, pubQuoteruleDetail))) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "新增报价明细信息'" + pubQuoteruleDetail.getRulecode() + "'失败，报价明细编码已存在");
        } else {
            return new ResponseResult<>(pubQuoteruleDetailService.insertPubQuoteruleDetail(token, pubQuoteruleDetail));
        }
    }

    /**
     * 修改报价管理明细
     */
    @PostMapping("/update")
    @MenuAuthority(code = "承运商报价明细设置-修改")
    public ResponseResult<Integer> edit(@RequestBody(required = false) PubQuoteruleDetail pubQuoteruleDetail) {
        String token = RequestContext.getToken();
        if (pubQuoteruleDetail == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        // 去掉空格
        removeSpace(pubQuoteruleDetail);
        if (StrUtil.isEmpty(pubQuoteruleDetail.getRulecode())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "明细编码不能为空");
        }
        if (pubQuoteruleDetail.getQuoteruleId() == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "报价编码不能为空");
        }
        if (StrUtil.isEmpty(pubQuoteruleDetail.getFiles())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "报价附件不能为空");
        }
        if (pubQuoteruleDetail.getQuoteruleTemplateId() == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "规则模板不能为空");
        }
        if (CollUtil.isEmpty(pubQuoteruleDetail.getQuotationClassificationdetailList())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "报价分级明细不可为空");
        }
        if (UserConstants.NOT_UNIQUE.equals(pubQuoteruleDetailService.checkRuleDetailCodeUnique(token, pubQuoteruleDetail))) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "新增报价明细信息'" + pubQuoteruleDetail.getRulecode() + "'失败，报价明细编码已存在");
        } else {
            try {
                return new ResponseResult<>(pubQuoteruleDetailService.updatePubQuoteruleDetail(token, pubQuoteruleDetail));
            } catch (Exception e) {
                if(e instanceof BusinessException){
                    throw new BusinessException(e.getMessage());
                }else if (e instanceof ValidateException){
                    throw new BusinessException(e.getMessage());
                }else {
                    throw new BusinessException("修改费用失败!");
                }
            }
        }
    }

    /**
     * 空格过滤
     */
    public void removeSpace(PubQuoteruleDetail pubQuoteruleDetail) {
        for (PubQuotationClassificationdetail quotation : pubQuoteruleDetail.getQuotationClassificationdetailList()) {
            if (StrUtil.isNotEmpty(quotation.getStoreCode())) {
                quotation.setStoreCode(quotation.getStoreCode().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getStoreName())) {
                quotation.setStoreName(quotation.getStoreName().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getDeliverProvince())) {
                quotation.setDeliverProvince(quotation.getDeliverProvince().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getDeliverArea())) {
                quotation.setDeliverArea(quotation.getDeliverArea().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getDeliverCity())) {
                quotation.setDeliverCity(quotation.getDeliverCity().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getReceiveProvince())) {
                quotation.setReceiveProvince(quotation.getReceiveProvince().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getReceiveArea())) {
                quotation.setReceiveArea(quotation.getReceiveArea().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getReceiveCity())) {
                quotation.setReceiveCity(quotation.getReceiveCity().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getRouteCode())) {
                quotation.setRouteCode(quotation.getRouteCode().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getCarmodel())) {
                quotation.setCarmodel(quotation.getCarmodel().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getCarlong())) {
                quotation.setCarlong(quotation.getCarlong().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getWarehouseName())) {
                quotation.setWarehouseName(quotation.getWarehouseName().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getTemperatureZone())) {
                quotation.setTemperatureZone(quotation.getTemperatureZone().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getSpecifications())) {
                quotation.setSpecifications(quotation.getSpecifications().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getCategory())) {
                quotation.setCategory(quotation.getCategory().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getNumberLadder1())) {
                quotation.setNumberLadder1(quotation.getNumberLadder1().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getNumberLadder2())) {
                quotation.setNumberLadder2(quotation.getNumberLadder2().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getNumberLadder3())) {
                quotation.setNumberLadder3(quotation.getNumberLadder3().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getNumberLadder4())) {
                quotation.setNumberLadder4(quotation.getNumberLadder4().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getNumberLadder5())) {
                quotation.setNumberLadder5(quotation.getNumberLadder5().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getNumberLadder6())) {
                quotation.setNumberLadder6(quotation.getNumberLadder6().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getCategory1())) {
                quotation.setCategory1(quotation.getCategory1().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getCategory2())) {
                quotation.setCategory2(quotation.getCategory2().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getCategory3())) {
                quotation.setCategory3(quotation.getCategory3().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getCategory4())) {
                quotation.setCategory4(quotation.getCategory4().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getCategory5())) {
                quotation.setCategory5(quotation.getCategory5().replace(" ", "").replace("\t", ""));
            }
            if (StrUtil.isNotEmpty(quotation.getCategory6())) {
                quotation.setCategory6(quotation.getCategory6().replace(" ", "").replace("\t", ""));
            }
        }
    }

    /**
     * 报价明细启用禁用
     */
    @PostMapping(value = "/updateStatus")
    @MenuAuthority(code = "承运商报价明细设置-启用,承运商报价明细设置-禁用")
    public ResponseResult<Integer> updateStatus(@RequestBody(required = false) PubQuoteruleDetail pubQuoteruleDetail) {
        String token = RequestContext.getToken();
        if (pubQuoteruleDetail == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(pubQuoteruleDetailService.updatePubQuoteruleByIds(token, pubQuoteruleDetail));
    }

    /**
     * 批量修改报价审核状态
     */
    @DeleteMapping("/updateAuditState/{ids}")
    @MenuAuthority(code = "承运商报价明细设置-审核,承运商报价明细设置-取消审核")
    public ResponseResult<Integer> updateAuditState(@PathVariable String[] ids) {
        String token = RequestContext.getToken();
        if (ids == null || ids.length == 0) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(pubQuoteruleDetailService.updateAuditState(token, ids));
    }

}