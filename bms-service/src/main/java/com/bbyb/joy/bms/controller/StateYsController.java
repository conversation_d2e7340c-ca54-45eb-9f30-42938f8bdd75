package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.datareport.StateYsGroup;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.service.IStateYsService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应收数据状态台账
 */
@RestController
@RequestMapping("/DataReport/StateYs")
public class StateYsController {

    @Resource
    private IStateYsService stateYs;
    @Resource
    private TextConversionUtil util;
    @Resource
    private UserRights userRights;
    @Resource
    private ExportUtil exportUtil;


    /**
     * 分组列表
     */
    @PostMapping("/groupList")
    public ResponseResult<PagerDataBean<StateYsGroup>> groupList(@RequestBody(required = false) StateYsGroup param) {
        String token = RequestContext.getToken();
        if (param == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CLIENT.getCode(), "clientList", PermissionConfig.FieldType.LIST_STRING, "clientCode"),
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.LIST_STRING, "id")
        );
        param = userRights.applyPermissions(param, permissionConfigs);

        return new ResponseResult<>(stateYs.groupList(token, param));
    }

    /**
     * 详情  应收状态台账分组列表
     */
    @PostMapping("/List")
    public ResponseResult<PagerDataBean<StateYsGroup>> list(@RequestBody(required = false) StateYsGroup bean) {
        String token = RequestContext.getToken();
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        if (CollUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(util.getCompanyIdsLis());
        }
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
        List<ClientInfo> clientList = sysDataPack.getClientinfo();
        if (CollUtil.isEmpty(clientList)) {
            return new ResponseResult<>(new PagerDataBean<>(new ArrayList<>(), 0, new PagerBean(bean.getPage(), bean.getSize())));
        }
        bean.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
        return new ResponseResult<>(stateYs.detailsList(token, bean));
    }

    /**
     * 导出应付账单主列表
     */
    @PostMapping("/export")
    @MenuAuthority(code = "应收账单状态台账-导出")
    public ResponseResult<String> export(@RequestBody(required = false) StateYsGroup bean) {
        String token = RequestContext.getToken();
        if (CollUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(util.getCompanyIdsLis());
        }
        Long userInfoId = RequestContext.getUserInfo().getId().longValue();
        SysDataPack sysDataPack = userRights.getRightsByUserId(userInfoId, "client");
        List<ClientInfo> clientList = sysDataPack.getClientinfo();
        if (CollUtil.isEmpty(clientList)) {
            return new ResponseResult<>(400401, "没有客户权限");
        }
        bean.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
        return exportUtil.getOutClassNew(token, "应收账单状态台账明细导出", "应收账单状态台账", StateYsGroup.class, userId -> {
            return stateYs.detailsList(token, bean).getRows();
        });

    }
}
