package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.domain.constant.UserConstants;
import com.bbyb.joy.bms.domain.dto.PubQuoteruleTemplate;
import com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleTemplateDto;
import com.bbyb.joy.bms.service.IPubQuoteruleTemplateService;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 报价规则模板主Controller
 */
@RestController
@RequestMapping("/system/template")
public class PubQuoteruleTemplateController {

    @Resource
    private IPubQuoteruleTemplateService pubQuoteruleTemplateService;

    /**
     * 查询报价规则模板主列表
     */
    @PostMapping("/list")
    @MenuAuthority(code = "报价规则模板")
    public ResponseResult<PagerDataBean<PubQuoteruleTemplate>> list(@RequestBody(required = false) PubQuoteruleTemplate pubQuoteruleTemplate) {
        String token = RequestContext.getToken();
        if (pubQuoteruleTemplate == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(pubQuoteruleTemplateService.selectPubQuoteruleTemplateList(token, pubQuoteruleTemplate));
    }

    /**
     * 获取报价规则模板主详细信息
     */
    @PostMapping(value = "/{id}")
    public ResponseResult<PubQuoteruleTemplate> getInfo(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(json)) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        String id = JSONObject.parseObject(json).getString("id");
        return new ResponseResult<>(pubQuoteruleTemplateService.selectPubQuoteruleTemplateById(token, id));
    }

    /**
     * 获取模板信息
     */
    @PostMapping(value = "/getTempInfo")
    public ResponseResult<List<PubQuoteruleTemplateDto>> getTempInfo(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(json)) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        Integer ruleType = JSONObject.parseObject(json).getInteger("ruleType");
        Integer orderType = JSONObject.parseObject(json).getInteger("orderType");
        return new ResponseResult<>(pubQuoteruleTemplateService.selectPubQuoteruleTemplateList(token, ruleType, orderType));
    }

    /**
     * 获取单个报价明细模板字段信息
     */
    @PostMapping(value = "/getSingleTempInfo")
    public ResponseResult<PubQuoteruleTemplateDto> getSingleTempInfo(@RequestParam("templateId") String templateId) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(pubQuoteruleTemplateService.selectSinglePubQuoteruleTemplate(token, templateId));
    }

    /**
     * 新增报价规则模板主
     */
    @PostMapping("/add")
    @MenuAuthority(code = "报价规则模板-新增,报价规则模板-复制")
    public ResponseResult<String> add(@RequestBody(required = false) PubQuoteruleTemplate pubQuoteruleTemplate) {
        String token = RequestContext.getToken();
        if (pubQuoteruleTemplate == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        UserBean userVO = RequestContext.getUserInfo();
        if (StrUtil.isEmpty(pubQuoteruleTemplate.getRuleCode())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "规则编码不能为空");
        }
        if (StrUtil.isEmpty(pubQuoteruleTemplate.getRuleName())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "规则名称不能为空");
        }
        if (pubQuoteruleTemplate.getRuleType() == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "规则类型不能为空");
        }
        if (pubQuoteruleTemplate.getConsolidationRule() == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "合并规则不能为空");
        }
        if (CollUtil.isEmpty(pubQuoteruleTemplate.getTemplateDetails())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "计费字段不能为空");
        }
        if (pubQuoteruleTemplate.getBjType() == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "报价类型不能为空");
        }
        pubQuoteruleTemplate.setCreateBy(userVO.getEmployeename());
        if (UserConstants.NOT_UNIQUE.equals(this.pubQuoteruleTemplateService.checkRuleCodeUnique(token, pubQuoteruleTemplate))) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "新增规则模板'" + pubQuoteruleTemplate.getRuleCode() + "'失败，规则编码已存在");
        } else {
            return new ResponseResult<>(pubQuoteruleTemplateService.insertPubQuoteruleTemplate(token, pubQuoteruleTemplate));
        }
    }

    /**
     * 修改报价规则模板主
     */
    @PostMapping("/edit")
    @MenuAuthority(code = "报价规则模板-修改")
    public ResponseResult<String> edit(@RequestBody(required = false) PubQuoteruleTemplate pubQuoteruleTemplate) {
        String token = RequestContext.getToken();
        if (pubQuoteruleTemplate == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        UserBean userVO = RequestContext.getUserInfo();
        if (StrUtil.isEmpty(pubQuoteruleTemplate.getRuleCode())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "规则编码不能为空");
        }
        if (StrUtil.isEmpty(pubQuoteruleTemplate.getRuleName())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "规则名称不能为空");
        }
        if (pubQuoteruleTemplate.getRuleType() == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "规则类型不能为空");
        }
        if (pubQuoteruleTemplate.getConsolidationRule() == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "合并规则不能为空");
        }
        if (CollUtil.isEmpty(pubQuoteruleTemplate.getTemplateDetails())) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "计费字段不能为空");
        }
        if (pubQuoteruleTemplate.getBjType() == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "报价类型不能为空");
        }
        pubQuoteruleTemplate.setCreateBy(userVO.getEmployeename());
        if (UserConstants.NOT_UNIQUE.equals(this.pubQuoteruleTemplateService.checkRuleCodeUnique(token, pubQuoteruleTemplate))) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.value(), "修改规则模板'" + pubQuoteruleTemplate.getRuleCode() + "'失败，规则编码已存在");
        } else {
            return new ResponseResult<>(pubQuoteruleTemplateService.updatePubQuoteruleTemplate(token, pubQuoteruleTemplate));
        }
    }

    /**
     * 物理作废报价规则模板主
     */
    @DeleteMapping("/{ids}")
    @MenuAuthority(code = "报价规则模板-作废")
    public ResponseResult<Integer> remove(@PathVariable String[] ids) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(pubQuoteruleTemplateService.deletePubQuoteruleTemplateByIds(token, ids));
    }

    /**
     * 报价模板启用禁用
     */
    @PostMapping("/updateStatus")
    @MenuAuthority(code = "报价规则模板-启用,报价规则模板-禁用")
    public ResponseResult<Integer> updateStatus(@RequestBody(required = false) PubQuoteruleTemplate pubQuoteruleTemplate) {
        String token = RequestContext.getToken();
        if (pubQuoteruleTemplate == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(pubQuoteruleTemplateService.updatePubQuoteruleByIds(token, pubQuoteruleTemplate));
    }
}
