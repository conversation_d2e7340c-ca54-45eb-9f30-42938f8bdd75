package com.bbyb.joy.bms.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.domain.dto.AutomaticBilling;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.dto.BMSAutomaticLogImplementDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsPubAddFeeRequest;
import com.bbyb.joy.bms.domain.dto.dto.BmsPubAddedfeeDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYsAddedfeeDto;
import com.bbyb.joy.bms.domain.dto.export.BmsYfPubAddedfeeExport;
import com.bbyb.joy.bms.domain.dto.export.BmsYsPubAddedfeeExport;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.domain.enums.AutoStateEnum;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsAutoMaticService;
import com.bbyb.joy.bms.service.IBmsPubAddedfeeService;
import com.bbyb.joy.bms.service.IYFAutomaticBilling;
import com.bbyb.joy.bms.service.IYSAutomaticBilling;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 增值费用表;(bms_addedfee)表控制层
 */
@ApiModel(value = "增值费用表")
@RestController
@RequestMapping("/bmsPubAddedfee")
public class BmsPubAddedfeeController {


    @Resource
    private IYSAutomaticBilling ysAutomaticBilling;
    @Resource
    private IYFAutomaticBilling yfAutomaticBilling;
    @Resource
    private IBmsAutoMaticService bmsAutoMaticService;
    @Resource
    private UserRights userRights;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    private IBmsPubAddedfeeService baseService;

    /**
     * 增值费用分页查询
     *
     * @param bmsPubAddedfee 筛选条件
     * @return 查询结果
     */
    @ApiOperation("增值费用分页查询")
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<BmsPubAddedfeeDto>> paginQuery(@RequestBody BmsPubAddedfeeDto bmsPubAddedfee) {

        UserBean userInfo = RequestContext.getUserInfo();
        String settleType = bmsPubAddedfee.getSettleType();

        // permission
        List<PermissionConfig> permissionConfigs = new ArrayList<>();
        permissionConfigs.add(new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.STRING, "id"));
        if ("1".equals(settleType)) {
            // 应收(1) 客户权限
            permissionConfigs.add(new PermissionConfig(PermissionType.CLIENT.getCode(), "clientIds", PermissionConfig.FieldType.LIST_STRING, "clientCode"));
        } else if ("2".equals(settleType)) {
            // 应付(2) 承运商权限
            permissionConfigs.add(new PermissionConfig(PermissionType.CARRIER.getCode(), "carrierCodes", PermissionConfig.FieldType.LIST_STRING, "carrierCode"));
        }
        bmsPubAddedfee = userRights.applyPermissions(bmsPubAddedfee, permissionConfigs);

        return new ResponseResult<>(baseService.queryAllByDtoLimit(userInfo.getToken(), bmsPubAddedfee));
    }

    /**
     * 生成，批量生成账单
     */
    @ApiOperation("增值费用分页查询")
    @PostMapping("/generate/bill")
    @MenuAuthority(code = "应付增值费管理-按条件生成账单,应付增值费管理-批量生成账单")
    public ResponseResult<String> generateAdditionalBills(@RequestBody(required = false) BmsPubAddedfeeDto dto) {
        UserBean userInfo = RequestContext.getUserInfo();
        return new ResponseResult<>(baseService.generateAdditionalBills(userInfo.getToken(), dto));
    }

    /**
     * 增值费用导出
     *
     * @param bmsPubAddedfee 筛选条件
     * @return 查询结果
     */
    @ApiOperation("增值费用导出")
    @PostMapping("/export")
    @MenuAuthority(code = "应付增值费管理-导出全部,应收增值费管理-导出全部")
    public ResponseResult<String> export(@RequestBody BmsPubAddedfeeDto bmsPubAddedfee) {
        UserBean userInfo = RequestContext.getUserInfo();
        String excelTitle = "增值费用导出";
        if (ObjUtil.isEmpty(bmsPubAddedfee)) {
            return new ResponseResult<>(400401, "未获取到导出条件");
        }
        if (StrUtil.isEmpty(bmsPubAddedfee.getSettleType())) {
            return new ResponseResult<>(400401, "未获取到导出条件");
        }
        Long userId = userInfo.getId().longValue();
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client,carrier");
        String settleType = bmsPubAddedfee.getSettleType();
        if ("1".equals(settleType)) {
            // 应收(1) 客户权限
            List<ClientInfo> clientInfos = sysDataPack.getClientinfo();
            List<String> collect = clientInfos.stream().map(ClientInfo::getClientCode).collect(Collectors.toList());
            bmsPubAddedfee.setClientIds(collect);
        } else if ("2".equals(settleType)) {
            // 应付(2) 承运商权限
            List<Carrierinfo> carrierInfos = sysDataPack.getCarrierInfo();
            List<String> collect = carrierInfos.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toList());
            if (CollUtil.isEmpty(carrierInfos)) {
                bmsPubAddedfee.setCarrierCodes(null);
            } else {
                bmsPubAddedfee.setCarrierCodes(collect);
            }
        }
        PagerDataBean<BmsPubAddedfeeDto> bmsPubAddedfeeDtos = baseService.queryAllByDtoLimit(userInfo.getToken(), bmsPubAddedfee);

        if (StrUtil.equals(bmsPubAddedfee.getSettleType(), "1")) {
            excelTitle = "应收增值费导出";
            List<BmsYsPubAddedfeeExport> bmsYsPubAddedfeeExports = copyYsExportProperties(bmsPubAddedfeeDtos.getRows());
            return exportUtil.getOutClassNew(userInfo.getToken(), excelTitle, "增值费用", BmsYsPubAddedfeeExport.class, returnData -> bmsYsPubAddedfeeExports);
        }
        if (StrUtil.equals(bmsPubAddedfee.getSettleType(), "2")) {
            excelTitle = "应付增值费导出";
            List<BmsYfPubAddedfeeExport> bmsYfPubAddedfeeExports = copyYfExportProperties(bmsPubAddedfeeDtos.getRows());
            return exportUtil.getOutClassNew(userInfo.getToken(), excelTitle, "增值费用", BmsYfPubAddedfeeExport.class, returnData -> bmsYfPubAddedfeeExports);
        }
        return null;
    }


    //应收属性复制
    public List<BmsYsPubAddedfeeExport> copyYsExportProperties(List<BmsPubAddedfeeDto> list) {
        List<BmsYsPubAddedfeeExport> dealList = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            for (BmsPubAddedfeeDto bmsPubAddedfeeDto : list) {
                BmsYsPubAddedfeeExport targetObj = new BmsYsPubAddedfeeExport();
                BeanUtil.copyProperties(bmsPubAddedfeeDto, targetObj);
                dealList.add(targetObj);
            }
        }
        return dealList;
    }


    //应付属性赋值
    public List<BmsYfPubAddedfeeExport> copyYfExportProperties(List<BmsPubAddedfeeDto> list) {
        List<BmsYfPubAddedfeeExport> dealList = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            for (BmsPubAddedfeeDto bmsPubAddedfeeDto : list) {
                BmsYfPubAddedfeeExport targetObj = new BmsYfPubAddedfeeExport();
                BeanUtil.copyProperties(bmsPubAddedfeeDto, targetObj);
                dealList.add(targetObj);
            }
        }
        return dealList;
    }


    /**
     * 取消计费
     *
     * @return 更新结果
     */
    @ApiOperation("取消计费")
    @PostMapping("cancelCost")
    @MenuAuthority(code = "应付增值费管理-取消计费,应收增值费管理-取消计费")
    public ResponseResult<String> cancelCost(@RequestBody BmsPubAddedfeeDto bmsPubAddedfee) {
        UserBean loginUserInfo = RequestContext.getUserInfo();
        return new ResponseResult<>(baseService.cancelCost(loginUserInfo.getToken(), bmsPubAddedfee));
    }


    @Log(title = "应收增值费自动计费", businessType = BusinessType.UPDATE)
    @PostMapping("/YSvalueAddedFee")
    @MenuAuthority(code = "应收增值费管理-自动计费")
    public ResponseResult<String> ysValueAddedFee(@RequestBody(required = false) AutomaticBilling automaticBilling) {
        UserBean loginUserInfo = RequestContext.getUserInfo();
        //新增
        String userName = loginUserInfo.getEmployeename();
        BMSAutomaticLogImplementDto dto = bmsAutoMaticService.insertAutomaticLogImplement(loginUserInfo.getToken(), userName, "应收仓储自动计费");
        if (dto == null) {
            return new ResponseResult<>(40005, "新增计划失败！");
        }
        //执行
        String finalToken = loginUserInfo.getToken();
//        // 目前只开发了库存的已计费，再次计费
//        CompletableFuture<String> completableFuture = ysAutomaticBilling.ysAddedServiceOrder(finalToken, null, automaticBilling.getStartDate(),
//                automaticBilling.getEndDate());
//        //修改
//        completableFuture.thenApplyAsync(result -> modifyAutoMatic(dto, result, finalToken));
//        ysAutomaticBilling.ysAddedServiceOrder(dto.getGid(),finalToken, null, automaticBilling.getStartDate(),
//                automaticBilling.getEndDate());
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                String res = ysAutomaticBilling.ysAddedServiceOrder(finalToken, null, automaticBilling.getStartDate(),automaticBilling.getEndDate());
                modifyAutoMatic(dto, res, finalToken);
            }
        });
        thread.start();
        return new ResponseResult<>("调用成功,请等待服务执行完成,再查询计费结果！");
    }

    @Log(title = "应付增值费自动计费", businessType = BusinessType.UPDATE)
    @PostMapping("/YFvalueAddedFee")
    @MenuAuthority(code = "应付增值费管理-自动计费")
    public ResponseResult<String> yfValueAddedFee(@RequestBody(required = false) AutomaticBilling automaticBilling) {
        UserBean loginUserInfo = RequestContext.getUserInfo();
        //验证
        if (!verification()) {
            return new ResponseResult<>(40005, "上次服务未执行完成,请等待！");
        }
        //新增
        String userName = loginUserInfo.getEmployeename();
        BMSAutomaticLogImplementDto dto = bmsAutoMaticService.insertAutomaticLogImplement(loginUserInfo.getToken(), userName, "应收仓储自动计费");
        if (dto == null) {
            return new ResponseResult<>(40005, "新增计划失败！");
        }
        //执行
        String finalToken = loginUserInfo.getToken();
        // 目前只开发了库存的已计费，再次计费
//        CompletableFuture<String> completableFuture = yfAutomaticBilling.yfAddedServiceOrder(finalToken, null, automaticBilling.getStartDate(),
//                automaticBilling.getEndDate());
//        //修改
//        completableFuture.thenApplyAsync(result -> modifyAutoMatic(dto, result, finalToken));
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                String res = yfAutomaticBilling.yfAddedServiceOrder(finalToken, null, automaticBilling.getStartDate(),automaticBilling.getEndDate());
                modifyAutoMatic(dto, res, finalToken);
            }
        });
        thread.start();
        return new ResponseResult<>("调用成功,请等待服务执行完成,再查询计费结果！");
    }

    private String modifyAutoMatic(BMSAutomaticLogImplementDto dto, String res, String finalToken) {
        //修改
        BMSAutomaticLogImplementDto upd = new BMSAutomaticLogImplementDto();
        upd.setGid(dto.getGid());
        upd.setAutossate(2);
        upd.setResultrek(res);
        upd.setEndtime(new Date());
        bmsAutoMaticService.updateAutomaticLogImplement(finalToken, upd);
        return res;
    }

    //验证是否可以操作
    private boolean verification() {
        BMSAutomaticLogImplementDto dto = bmsAutoMaticService.selectAutomaticLogImplement(RequestContext.getToken(), "应付增值费自动计费");
        return dto != null && dto.getAutossate() != null && dto.getAutossate().equals(AutoStateEnum.EXECETING.getIntValue());
    }


    @ApiOperation("应收账款判断是否有增值费信息及查询")
    @PostMapping("/getAddedFeeByYs/{billId}/{settleType}")
    public ResponseResult<BmsYsAddedfeeDto> getAddedFeeByYs(@PathVariable("billId") Integer billId, @PathVariable("settleType") String settleType) {
        BmsPubAddedfeeDto params = new BmsPubAddedfeeDto();
        params.setBillId(billId);
        params.setSettleType(settleType);
        PagerDataBean<BmsPubAddedfeeDto> list = baseService.listByYsReconciliation(RequestContext.getToken(), params);
        // FIXME 因为查询明细的时候有赋值billids，需要重置
        params.setBillId(billId);
        params.setBillIds(null);
        BmsYsAddedfeeDto addedFeeByYs = baseService.getAddedFeeByYs(RequestContext.getToken(), params);
        if (CollUtil.isNotEmpty(list.getRows()) && addedFeeByYs != null) {
            addedFeeByYs.setTableDataInfo(list);
        }
        return new ResponseResult<>(addedFeeByYs);
    }


    @ApiOperation("应收账款对账增值费保存")
    @PostMapping("updateBatchByYs")
    public ResponseResult<String> updateBatchByYs(@RequestBody BmsPubAddedfeeDto bmsPubAddedfee) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(baseService.updateBatchByYs(token, bmsPubAddedfee));
    }


    @ApiOperation("增值费导入功能")
    @PostMapping("/importData")
    @MenuAuthority(code = "应付增值费管理-导入,应收增值费管理-导入")
    public ResponseResult<String> importData(@RequestParam("file") MultipartFile file,
                                             @RequestParam("settleType") String settleType) {
        if (file == null || file.isEmpty()) {
            return new ResponseResult<>(400401, "未获取到上传文件");
        }
        if (StrUtil.isEmpty(settleType)) {
            return new ResponseResult<>(400401, "未获取到对应的增值费计费类别");
        }
        return new ResponseResult<>(baseService.importData(RequestContext.getToken(), file, settleType));
    }


    /**
     * 批量保存(针对前端导入解析的数据)
     *
     * @param data 导入的数据
     */
    @ApiOperation("增值费导入批量保存功能")
    @PostMapping("/insertBatchForImport")
    public ResponseResult<String> insertBatchForImport(@RequestBody BmsPubAddedfeeDto data) {
        if (null == data) {
            return new ResponseResult<>(400401, "未获取到要保存的数据");
        }
        return new ResponseResult<>(baseService.insertBatchForImport(RequestContext.getToken(), data.getDatas(), data.getSettleType()));
    }


    /**
     * 修改增值费用
     */
    @ApiOperation("增值费修改功能")
    @PostMapping("/edit")
    @MenuAuthority(code = "应付增值费管理-修改,应收增值费管理-修改")
    public ResponseResult<String> edit(@RequestBody BmsPubAddedfeeDto bmsAddedfee) {
        return new ResponseResult<>(baseService.edit(RequestContext.getToken(), bmsAddedfee));
    }


    /**
     * 作废增值费用
     */
    @ApiOperation("增值费作废功能")
    @PostMapping("/del")
    @MenuAuthority(code = "应付增值费管理-作废,应收增值费管理-作废")
    public ResponseResult<String> del(@RequestBody BmsPubAddedfeeDto bmsAddedfee) {
        return new ResponseResult<>(baseService.del(RequestContext.getToken(), bmsAddedfee));
    }

    /**
     * 对账时查询未绑定的增值费用单
     *
     * @param request2 要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/reconciliation/addFee/query")
    public ResponseResult<PagerDataBean<BmsPubAddedfeeDto>> queryAddFeeOrderWhenReconciliation(@Validated(BmsPubAddFeeRequest.QueryAddFeeOrderWhenReconciliation.class) @RequestBody BmsPubAddFeeRequest request2) {
        return new ResponseResult<>(baseService.queryAddFeeOrderWhenReconciliation(RequestContext.getToken(), request2));
    }

}
