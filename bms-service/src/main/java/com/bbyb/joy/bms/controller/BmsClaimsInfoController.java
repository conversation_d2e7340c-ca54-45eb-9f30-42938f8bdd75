package com.bbyb.joy.bms.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.BmsClaimsInfo;
import com.bbyb.joy.bms.domain.dto.BmsYfClaimsInfo;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain;
import com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsClaimsInfoService;
import com.bbyb.joy.bms.service.IBmsYfbillmainService;
import com.bbyb.joy.bms.service.IBmsYsbillmainService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.enums.PubNumEnum;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 理赔信息Controller
 */
@Api(tags = "理赔信息接口")
@RestController
@RequestMapping("system/claimsInfo")
public class BmsClaimsInfoController {

    @Resource
    private IBmsClaimsInfoService bmsClaimsInfoService;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    private UserRights userRights;
    @Resource
    private IBmsYsbillmainService iBmsYsbillmainService;
    @Resource
    private IBmsYfbillmainService iBmsYfbillmainService;

    /**
     * 查询理赔信息列表
     */
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<BmsClaimsInfo>> list(HttpServletRequest request, @RequestBody BmsClaimsInfo bmsClaimsInfo) {
        if (bmsClaimsInfo.getYsIdOrFatherid() != null) {
            //查询自己本身的账单单号，防止不是合并账单
            BmsYsbillmain bmsYsbillmain = iBmsYsbillmainService.selectBmsYsbillmainById(RequestContext.getToken(), bmsClaimsInfo.getYsIdOrFatherid().toString()).getResult();
            List<BmsYsbillmain> billCodeById = iBmsYsbillmainService.findDescendantsByBillId(RequestContext.getToken(), bmsClaimsInfo.getYsIdOrFatherid().toString());
            billCodeById.add(bmsYsbillmain);
            bmsClaimsInfo.setBillCode(billCodeById.stream().map(BmsYsbillmain::getBillCode).collect(Collectors.joining(",")));
        }
        if (bmsClaimsInfo.getYfIdOrFatherid() != null) {
            //查询自己本身的账单单号，防止不是合并账单
            BmsYfbillmain bmsYfbillmain = iBmsYfbillmainService.selectBmsYfbillmainById(RequestContext.getToken(), bmsClaimsInfo.getYfIdOrFatherid().toString());
            List<BmsYfbillmain> billCodeById = iBmsYfbillmainService.findDescendantsByBillId(RequestContext.getToken(), bmsClaimsInfo.getYfIdOrFatherid().toString());
            billCodeById.add(bmsYfbillmain);
            bmsClaimsInfo.setBillCode(billCodeById.stream().map(BmsYfbillmain::getBillCode).collect(Collectors.joining(",")));
        }
        return new ResponseResult<>(bmsClaimsInfoService.selectBmsClaimsInfoList(RequestContext.getToken(), bmsClaimsInfo));
    }


    /**
     * 导出理赔信息列表
     */
    @Log(title = "理赔信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public ResponseResult<String> export(@RequestBody BmsClaimsInfo bmsClaimsInfo) {
        PagerDataBean<BmsClaimsInfo> list = bmsClaimsInfoService.selectBmsClaimsInfoList(RequestContext.getToken(), bmsClaimsInfo);
        Map<String, List<BmsClaimsInfo>> clienList = list.getRows().stream().collect(Collectors.groupingBy(BmsClaimsInfo::getClientName));
        int i = 0;
        String cileName = "应收理赔导出" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        for (String clien : clienList.keySet()) {
            i++;
        }
        if (PubNumEnum.one.getIntValue().equals(i)) {
            cileName = list.getRows().get(0).getClientName() + "_应收理赔_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        }
        String finalToken = RequestContext.getToken();
        return exportUtil.getOutClassNew(finalToken, cileName, "应收理赔", BmsClaimsInfo.class, userId -> {
            SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
            List<ClientInfo> clientList = sysDataPack.getClientinfo();
            if (CollUtil.isEmpty(clientList)) {
                return null;
            }
            return bmsClaimsInfoService.selectBmsClaimsInfoList(finalToken, bmsClaimsInfo).getRows();
        });
    }

    /**
     * 导出理赔信息列表
     */
    @Log(title = "应付理赔信息", businessType = BusinessType.EXPORT)
    @PostMapping("/yfExport")
    public ResponseResult<String> yfExport(@RequestBody BmsClaimsInfo bmsClaimsInfo) {
        PagerDataBean<BmsClaimsInfo> list = bmsClaimsInfoService.selectBmsClaimsInfoList(RequestContext.getToken(), bmsClaimsInfo);
        Map<String, List<BmsClaimsInfo>> clienList = list.getRows().stream().collect(Collectors.groupingBy(BmsClaimsInfo::getClientName));
        int i = 0;
        String cileName = "应付理赔导出" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        for (String clien : clienList.keySet()) {
            i++;
        }
        if (PubNumEnum.one.getIntValue().equals(i)) {
            cileName = list.getRows().get(0).getClientName() + "_应付理赔_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        }
        String finalToken = RequestContext.getToken();
        return exportUtil.getOutClassNew(finalToken, cileName, "应付理赔", BmsYfClaimsInfo.class, userId -> {
            SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
            List<ClientInfo> clientList = sysDataPack.getClientinfo();
            if (CollUtil.isEmpty(clientList)) {
                return null;
            }
            return BeanUtil.copyToList(bmsClaimsInfoService.selectBmsClaimsInfoList(finalToken, bmsClaimsInfo).getRows(), BmsYfClaimsInfo.class);
        });
    }

    /**
     * 获取理赔信息详细信息
     */
    @PostMapping(value = "/{id}")
    public ResponseResult<BmsClaimsInfo> getInfo(@PathVariable("id") String id) {
        return new ResponseResult<>(bmsClaimsInfoService.selectBmsClaimsInfoById(RequestContext.getToken(), id));
    }

    /**
     * 新增理赔信息
     */
    @Log(title = "理赔信息", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public ResponseResult<Integer> add(HttpServletRequest request, @RequestBody BmsClaimsInfo bmsClaimsInfo) {
        return new ResponseResult<>(bmsClaimsInfoService.insertBmsClaimsInfo(RequestContext.getToken(), bmsClaimsInfo));
    }

    /**
     * 修改理赔信息
     */
    @Log(title = "理赔信息", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public ResponseResult<Integer> edit(HttpServletRequest request, @RequestBody BmsClaimsInfo bmsClaimsInfo) {
        return new ResponseResult<>(bmsClaimsInfoService.updateBmsClaimsInfo(RequestContext.getToken(), bmsClaimsInfo));
    }

    /**
     * 物理作废理赔信息
     */
    //作废理赔信息
    //@PreAuthorize("@ss.hasPermi('system:claimsInfo:remove')")
    @Log(title = "理赔信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public ResponseResult<Integer> remove(HttpServletRequest request, @PathVariable String[] ids) {
        /*
         * 取出对应入参，如下获取方式，是因为此接口可能会做为跨服务调用接口
         * 1、先获取请求头和body信息
         * 2、第一步获取不到，再从request中获取参数
         */
        String token = request.getHeader("Authorization");
        if (cn.hutool.core.util.StrUtil.isEmpty(token)) {
            token = request.getParameter("token");
        }
        return new ResponseResult<>(bmsClaimsInfoService.deleteBmsClaimsInfoByIds(token, ids));
    }

    /**
     * 逻辑作废/启用理赔信息
     */
    //修改理赔信息
    //@PreAuthorize("@ss.hasPermi('system:claimsInfo:edit')")
    @Log(title = "理赔信息", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateStatus")
    public ResponseResult<Integer> updateStatus(HttpServletRequest request, @RequestBody Map<String, Object> idsAndStatus) {
        /*
         * 取出对应入参，如下获取方式，是因为此接口可能会做为跨服务调用接口
         * 1、先获取请求头和body信息
         * 2、第一步获取不到，再从request中获取参数
         */
        String token = request.getHeader("Authorization");
        if (cn.hutool.core.util.StrUtil.isEmpty(token)) {
            token = request.getParameter("token");
        }
        List<String> ids = (ArrayList<String>) idsAndStatus.get("ids");
        String status = String.valueOf(idsAndStatus.get("status"));
        return new ResponseResult<>(bmsClaimsInfoService.updateBmsClaimsInfoStatusByIds(token, ids, status));
    }
}
