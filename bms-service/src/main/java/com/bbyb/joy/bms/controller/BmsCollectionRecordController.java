package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.biz.BmsCollectionRecordBiz;
import com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain;
import com.bbyb.joy.bms.domain.dto.bill.RecordHxInfoBean;
import com.bbyb.joy.bms.domain.dto.bill.RecordHxInfoDto;
import com.bbyb.joy.bms.domain.dto.settlement.BmsCollectionRecord;
import com.bbyb.joy.bms.domain.dto.settlement.BmsCollectionRecordDetails;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.domain.enums.HxStateEnum;
import com.bbyb.joy.bms.service.IBmsCollectionRecordService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 收款记录Controller
 */
@Api(tags = "收款记录接口")
@RestController
@RequestMapping("/system/record")
public class BmsCollectionRecordController {

    @Resource
    private IBmsCollectionRecordService bmsCollectionRecordService;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    private BmsCollectionRecordBiz bmsCollectionRecordMapper;
    @Resource
    UserRights userRights;

    /**
     * 查询收款记录列表
     */
    //查询收款记录列表
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<BmsCollectionRecord>> list(@RequestBody(required = false) BmsCollectionRecord bmsCollectionRecord) {

        // permission
        List<PermissionConfig> permissionConfigs = List.of(
                new PermissionConfig(PermissionType.COMPANY.getCode(), "createDeptIds", PermissionConfig.FieldType.ARRAY_STRING, "id")
        );
        bmsCollectionRecord = userRights.applyPermissions(bmsCollectionRecord, permissionConfigs);

        return new ResponseResult<>(bmsCollectionRecordService.selectBmsCollectionRecordList(RequestContext.getToken(), bmsCollectionRecord));
    }

    /**
     * 查询详细信息
     *
     * @param bmsCollectionRecord bmsCollectionRecord
     * @return {@link List}<{@link BmsCollectionRecord}>
     */
    @PostMapping("query/ids")
    public ResponseResult<List<BmsCollectionRecord>> queryDetail(@RequestBody BmsCollectionRecord bmsCollectionRecord) {
        return new ResponseResult<>(bmsCollectionRecordService.queryDetail(RequestContext.getToken(), bmsCollectionRecord.getIds()));
    }

    /**
     * 根据首款信息查询账单核销信息
     */
    //根据首款信息查询账单核销信息
    @PostMapping("/billList")
    public ResponseResult<RecordHxInfoDto> billList(@RequestBody(required = false) BmsYsbillmain bmsYsbillmain) {
        UserBean userVO = RequestContext.getUserInfo();
        bmsYsbillmain.setHxType(bmsYsbillmain.getHxType() == null ? 1 : bmsYsbillmain.getHxType());
        RecordHxInfoDto recordHxInfoDto = bmsCollectionRecordService.selectBmsYsbillmainList(RequestContext.getToken(), bmsYsbillmain);
        if (bmsYsbillmain.getHxType().equals(HxStateEnum.NO_VERIFICATION.getIntValue())) {
            recordHxInfoDto.setList(bmsCollectionRecordMapper.selectBmsYsbillmainList(RequestContext.getTenantId(), bmsYsbillmain));
        } else {
            // 查询收款下所有账单编码
            List<BmsYsbillmain> ysbillmainList = bmsCollectionRecordMapper.selectBmsYsbillByRecordId(RequestContext.getTenantId(), bmsYsbillmain);
            if (CollUtil.isNotEmpty(ysbillmainList)) {
                List<String> billCodeList = Arrays.stream(ysbillmainList.stream().map(BmsYsbillmain::getBillCode).collect(Collectors.joining(",")).split(",")).distinct().collect(Collectors.toList());
                BmsYsbillmain bmsYsbillmain1 = new BmsYsbillmain();
                bmsYsbillmain1.setBillCodeList(billCodeList);
                bmsYsbillmain1.setPageDomain(bmsYsbillmain.getPageDomain());
                PagerDataBean<BmsYsbillmain> ysbillmainList1 = bmsCollectionRecordMapper.selectBmsYsbillByBillCode(RequestContext.getTenantId(), bmsYsbillmain1);
                recordHxInfoDto.setList(ysbillmainList1);
            }
        }
        return new ResponseResult<>(recordHxInfoDto);
    }

    /**
     * 核销保存
     */
    @PostMapping("/saveList")
    @MenuAuthority(code = "应收收款管理-批量核销保存")
    public ResponseResult<String> saveList(@RequestBody(required = false) RecordHxInfoBean recordHxInfoDto) {
        return new ResponseResult<>(bmsCollectionRecordService.saveList(RequestContext.getToken(), recordHxInfoDto));
    }


    /**
     * 导出收款记录列表
     */
    @PostMapping("/export")
    @MenuAuthority(code = "应收收款管理-导出")
    public ResponseResult<String> export(HttpServletRequest request, @RequestBody(required = false) BmsCollectionRecord bmsCollectionRecord) {
        PagerDataBean<BmsCollectionRecord> bmsCollectionRecords = bmsCollectionRecordService.selectBmsCollectionRecordList(RequestContext.getToken(), bmsCollectionRecord);
        System.out.println("参数");
        return exportUtil.getOutClassNew(RequestContext.getToken(), "收款记录数据", "收款记录", BmsCollectionRecord.class, userId -> {
            return bmsCollectionRecords.getRows();
        });
    }

    /**
     * 获取收款记录详细信息
     */
    @PostMapping(value = "/{id}")
    public ResponseResult<BmsCollectionRecord> getInfo(@PathVariable("id") String id) {
        return new ResponseResult<>(bmsCollectionRecordService.selectBmsCollectionRecordById(RequestContext.getToken(), id));
    }

    /**
     * 新增收款记录
     */
    @PostMapping("/add")
    @MenuAuthority(code = "应收收款管理-导入")
    public ResponseResult<String> add(@RequestBody List<BmsCollectionRecord> bmsCollectionRecord) {
        return new ResponseResult<>(bmsCollectionRecordService.insertBmsCollectionRecord(RequestContext.getToken(), bmsCollectionRecord));
    }

    /**
     * 修改收款记录
     */
    @PostMapping("/edit")
    @MenuAuthority(code = "应收收款管理-修改")
    public ResponseResult<String> edit(HttpServletRequest request, @RequestBody BmsCollectionRecord bmsCollectionRecord) {
        String rs = bmsCollectionRecordService.updateBmsCollectionRecord(RequestContext.getToken(), bmsCollectionRecord);
        if ("success".equals(rs)) {
            return new ResponseResult<>("调用成功！");
        }
        return new ResponseResult<>(rs);
    }

    /**
     * 物理作废收款记录
     */
    //作废收款记录单条
    @DeleteMapping("/{id}")
    @MenuAuthority(code = "应收收款管理-作废")
    public ResponseResult<String> remove(@PathVariable String id) {
        return new ResponseResult<>(bmsCollectionRecordService.deleteBmsCollectionRecordById(RequestContext.getToken(), id));
    }

    /**
     * 批量作废收款记录
     */
    @Log(title = "收款记录", businessType = BusinessType.DELETE)
    @PostMapping("/removeBatch")
    public ResponseResult<String> removeBatch(@RequestBody(required = false) BmsCollectionRecord param) {
        return new ResponseResult<>(bmsCollectionRecordService.deleteBantch(RequestContext.getToken(), param));
    }

    /**
     * 逻辑作废/启用收款记录
     */
    @PostMapping("/updateStatus")
    public ResponseResult<Integer> updateStatus(HttpServletRequest request, @RequestBody Map<String, Object> idsAndStatus) {
        List<String> idsList = (ArrayList<String>) idsAndStatus.get("ids");
        String[] ids = idsList.toArray(new String[0]);
        String status = String.valueOf(idsAndStatus.get("status"));
        return new ResponseResult<>(bmsCollectionRecordService.updateBmsCollectionRecordStatusByIds(RequestContext.getToken(), ids, status));
    }


    //收款记录详情
    @PostMapping("/detail")
    @MenuAuthority(code = "应收收款管理-修改")
    public ResponseResult<PagerDataBean<BmsCollectionRecordDetails>> selectDetail(@RequestBody BmsCollectionRecord bmsCollectionRecord) {
        return new ResponseResult<>(bmsCollectionRecordService.selectDetail(RequestContext.getToken(), bmsCollectionRecord));
    }

    /**
     * 修改收入类型批量修改
     *
     * @param records 记录
     */
    @PostMapping("/incomeType/batch/modify")
    @MenuAuthority(code = "应收收款管理-收款确认")
    public ResponseResult<Boolean> modifyIncomeTypeBatch(HttpServletRequest request, @RequestBody List<BmsCollectionRecord> records) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsCollectionRecordService.modifyIncomeTypeBatch(token, records));
    }
}
