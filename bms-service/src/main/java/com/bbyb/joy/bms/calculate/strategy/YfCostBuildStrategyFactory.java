package com.bbyb.joy.bms.calculate.strategy;

import com.bbyb.joy.bms.calculate.strategy.impl.DefaultYfCostBuildStrategy;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyCalculateExtraType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 应付费用构建策略工厂
 * 
 */
@Slf4j
@Component
public class YfCostBuildStrategyFactory {

    @Resource
    private List<YfCostBuildStrategy> strategies;
    @Resource
    private DefaultYfCostBuildStrategy defaultStrategy;

    private final Map<GroovyCalculateExtraType, YfCostBuildStrategy> strategyMap = new HashMap<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        for (YfCostBuildStrategy strategy : strategies) {
            GroovyCalculateExtraType supportedType = strategy.getSupportedType();
            strategyMap.put(supportedType, strategy);
            log.info("注册应付费用构建策略: {} -> {}", supportedType.getName(), strategy.getClass().getSimpleName());
        }
    }

    /**
     * 根据extraType获取对应的策略
     * 
     * @param extraType 额外计费类型
     * @return 对应的策略实现
     */
    public YfCostBuildStrategy getStrategy(GroovyCalculateExtraType extraType) {
        if (extraType == null) {
            log.warn("extraType为null，使用默认策略");
            return defaultStrategy;
        }

        YfCostBuildStrategy strategy = strategyMap.get(extraType);
        if (strategy == null) {
            log.warn("未找到extraType: {} 对应的策略，使用默认策略", extraType.getName());
            return defaultStrategy;
        }

        return strategy;
    }

    /**
     * 根据extraType编码获取对应的策略
     * 
     * @param extraTypeCode 额外计费类型编码
     * @return 对应的策略实现
     */
    public YfCostBuildStrategy getStrategy(Integer extraTypeCode) {
        GroovyCalculateExtraType extraType = GroovyCalculateExtraType.getByCode(extraTypeCode);
        return getStrategy(extraType);
    }

    /**
     * 获取所有已注册的策略
     * 
     * @return 策略映射
     */
    public Map<GroovyCalculateExtraType, YfCostBuildStrategy> getAllStrategies() {
        return new HashMap<>(strategyMap);
    }

    /**
     * 检查是否支持指定的extraType
     * 
     * @param extraType 额外计费类型
     * @return 是否支持
     */
    public boolean isSupported(GroovyCalculateExtraType extraType) {
        return extraType != null && strategyMap.containsKey(extraType);
    }

    /**
     * 检查是否支持指定的extraType编码
     * 
     * @param extraTypeCode 额外计费类型编码
     * @return 是否支持
     */
    public boolean isSupported(Integer extraTypeCode) {
        GroovyCalculateExtraType extraType = GroovyCalculateExtraType.getByCode(extraTypeCode);
        return isSupported(extraType);
    }
}
