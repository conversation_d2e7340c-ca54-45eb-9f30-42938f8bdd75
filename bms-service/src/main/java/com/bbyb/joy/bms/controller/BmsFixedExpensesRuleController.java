package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.domain.dto.BmsFixedExpenseRuleInfo;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.dto.BmsFixedExpenseRuleDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsFixedExpenseRuleExtendDto;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.service.BmsFixedExpenseRuleService;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "固定费用规则")
@RestController
@RequestMapping("/system/fixedExpensesRule")
public class BmsFixedExpensesRuleController {

    @Resource
    private BmsFixedExpenseRuleService ruleService;
    @Resource
    private UserRights userRights;

    /**
     * 规则新增接口
     */
    @PostMapping("/add")
    @MenuAuthority(code = "应收固定费规则-新增")
    public ResponseResult<String> addRule(HttpServletRequest request, @RequestBody(required = false) BmsFixedExpenseRuleDto ruleInfo) {
        if (ruleInfo == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(ruleService.addRule(RequestContext.getToken(), ruleInfo));
    }

    /**
     * 规则更新接口
     */
    @PostMapping("/update")
    @MenuAuthority(code = "应收固定费规则-修改")
    public ResponseResult<String> update(@RequestBody(required = false) BmsFixedExpenseRuleDto ruleDto) {
        if (ruleDto == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(ruleService.update(RequestContext.getToken(), ruleDto));
    }

    /**
     * 规则作废接口
     */
    @PostMapping("/del")
    @MenuAuthority(code = "应收固定费规则-作废")
    public ResponseResult<String> del(@Validated(BmsFixedExpenseRuleDto.VerificationBmsFixedExpenseRuleDto.class)
                                      @RequestBody(required = false) BmsFixedExpenseRuleDto ruleDto) {
        if (ruleDto == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(ruleService.del(RequestContext.getToken(), ruleDto));
    }

    /**
     * 固定费用查询接口
     */
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<BmsFixedExpenseRuleExtendDto>> list(@RequestBody(required = false) BmsFixedExpenseRuleDto ruleDto) {
        if (ruleDto == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        UserBean loginUserInfo = RequestContext.getUserInfo();
        Long userId = Long.valueOf(loginUserInfo.getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
        List<ClientInfo> clientInfos = sysDataPack.getClientinfo();
        List<Long> clientIds = clientInfos.stream().map(ClientInfo::getId).collect(Collectors.toList());
        ruleDto.setClientIds(clientIds);
        return new ResponseResult<>(ruleService.list(loginUserInfo.getToken(), ruleDto));
    }

    /**
     * 查询详细信息
     *
     * @param ruleDto ruleDto
     */
    @PostMapping("/detail/query")
    public ResponseResult<BmsFixedExpenseRuleInfo> queryDetail(@RequestBody(required = false) BmsFixedExpenseRuleDto ruleDto) {
        if (ruleDto == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(ruleService.queryDetail(RequestContext.getToken(), ruleDto.getId()));
    }

    /**
     * 已启用  禁用 修改
     *
     * @param ruleDto 规则dto
     */
    @PostMapping("/enabled/modify")
    public ResponseResult<String> modifyEnabled(@RequestBody(required = false) BmsFixedExpenseRuleInfo ruleDto) {
        if (ruleDto == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(ruleService.modifyEnabled(RequestContext.getToken(), ruleDto));
    }

    /**
     * 生成固定费
     */
    @PostMapping("/expenseCreate")
    public ResponseResult<String> expenseCreate() {
        ruleService.expenseCreate(RequestContext.getToken());
        return new ResponseResult<>("调用成功");
    }
}
