package com.bbyb.joy.bms.calculate.convert.impl;

import com.bbyb.joy.bms.calculate.convert.OrderConverter;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 计费单据转换注册表
 */
@Component
public class OrderConverterRegistry {

    @Resource
    private List<OrderConverter<?, ?>> converters;

    private final Map<ConverterKey, OrderConverter<?, ?>> converterMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (OrderConverter<?, ?> converter : converters) {
            ConverterKey key = new ConverterKey(converter.getSourceType(), converter.getTargetType());
            converterMap.put(key, converter);
        }
    }

    /**
     * 获取转换器
     * @param sourceType 源类型
     * @param targetType 目标类型
     * @return 转换器
     */
    @SuppressWarnings("unchecked")
    public <S, T> OrderConverter<S, T> getConverter(Class<S> sourceType, Class<T> targetType) {
        ConverterKey key = new ConverterKey(sourceType, targetType);
        return (OrderConverter<S, T>) converterMap.get(key);
    }

    /**
     * 转换器键
     */
    private static class ConverterKey {
        private final Class<?> sourceType;
        private final Class<?> targetType;

        public ConverterKey(Class<?> sourceType, Class<?> targetType) {
            this.sourceType = sourceType;
            this.targetType = targetType;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ConverterKey that = (ConverterKey) o;
            return sourceType.equals(that.sourceType) && targetType.equals(that.targetType);
        }

        @Override
        public int hashCode() {
            return 31 * sourceType.hashCode() + targetType.hashCode();
        }
    }


}
