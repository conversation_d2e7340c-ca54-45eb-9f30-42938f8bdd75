package com.bbyb.joy.bms.support.configuration;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class BmsConstants {

    /**
     * 计费业务类型 1.运输订单 2 出库单 3 入库单 4 库存单
     */
    public static Integer OPT_TYPE_01 = 1;
    public static Integer OPT_TYPE_02 = 2;
    public static Integer OPT_TYPE_03 = 3;
    public static Integer OPT_TYPE_04 = 4;
    //仓储业务
    public static List<Integer> STORAGE_TYPE_SETS = Lists.newArrayList(OPT_TYPE_02, OPT_TYPE_03, OPT_TYPE_04);

    /**
     * 系统简码
     */
    public static String SYS_CODE_OMS = "OMS";
    public static Integer COMMON_NUMBER_ZERO = 0;
    /**
     * 固定费和增值费按照季度计费使用
     */
    public static Set<Integer> FEE_BELONG_REGION_ONE_SET = Sets.newHashSet(1, 2, 3);
    public static Set<Integer> FEE_BELONG_REGION_TWO_SET = Sets.newHashSet(4, 5, 6);
    public static Set<Integer> FEE_BELONG_REGION_THREE_SET = Sets.newHashSet(7, 8, 9);
    public static Set<Integer> FEE_BELONG_REGION_FOUR_SET = Sets.newHashSet(10, 11, 12);
    /**
     * 按季度对应帐期月份
     */
    public static String FEE_BELONG_REGION_ONE_BILL_DATE = "03";
    public static String FEE_BELONG_REGION_TWO_BILL_DATE = "06";
    public static String FEE_BELONG_REGION_THREE_BILL_DATE = "09";
    public static String FEE_BELONG_REGION_FOUR_BILL_DATE = "12";
    /**
     * 固定费按季度计费
     */
    public static Set<Integer> FEE_BELONG_REGION_BILL_DATE = Sets.newHashSet(3, 6, 9, 12);
    /**
     * 计费频次:1按月
     */
    public static Integer FREQUENCY_1 = 1;
    /**
     * 计费频次:2按季度
     */
    public static Integer FREQUENCY_2 = 2;
    /**
     * 计费业务类型 1.运输订单 2 出库单 3 入库单 4 库存单
     */
    public static Integer YS_OPT_TYPE_01 = 1;
    public static Integer YS_OPT_TYPE_02 = 2;
    public static Integer YS_OPT_TYPE_03 = 3;
    public static Integer YS_OPT_TYPE_04 = 4;
    //应付-运输单
    public static String YF_OPT_TYPE_01_STR = "1";
    //应付-出库单
    public static String YF_OPT_TYPE_02_STR = "2";
    //应付-入库单
    public static String YF_OPT_TYPE_03_STR = "3";
    //应付-库存单
    public static String YF_OPT_TYPE_04_STR = "4";
    //应付-增值单
    public static String YF_OPT_TYPE_05_STR = "5";
    public static Set<Integer> YS_OPT_TYPE_CODE_SETS = Sets.newHashSet(1, 2, 3);
    public static Set<Integer> YS_OPT_TYPE_STOCK_CODE_SETS = Sets.newHashSet(4);


    /**
     * 运输单
     */
    public static Set<Integer> YF_OPT_TYPE_CODE_SETS = Sets.newHashSet(1);
    /**
     * 出入库
     */
    public static Set<Integer> YF_OPT_TYPE_InOutCODE_SETS = Sets.newHashSet(2, 3);
    /**
     * 库存单
     */
    public static Set<Integer> YF_OPT_TYPE_STOCK_CODE_SETS = Sets.newHashSet(4);
    /**
     * 计费业务类型 1.运输订单 2 出库单 3 入库单 4 库存单
     */
    public static Integer YF_OPT_TYPE_01 = 1;
    public static Integer YF_OPT_TYPE_02 = 2;
    public static Integer YF_OPT_TYPE_03 = 3;
    public static Integer YF_OPT_TYPE_04 = 4;
    /**
     * 报价类型(1客户,2承运商)
     */
    public static Integer RULE_TYPE_1 = 1;
    /**
     * 报价类型(1客户,2承运商)
     */
    public static Integer RULE_TYPE_2 = 2;
    /**
     * 有效
     */
    public static Integer DEL_FLAG_0 = 0;
    /**
     * 无效
     */
    public static Integer DEL_FLAG_1 = 1;
    /**
     * 启用
     */
    public static Integer IS_ENABLE_0 = 0;
    /**
     * 不启用
     */
    public static Integer IS_ENABLE_1 = 1;
    /**
     * 未审核
     */
    public static Integer AUDIT_STATE_1 = 1;
    /**
     * 已审核
     */
    public static Integer AUDIT_STATE_2 = 2;
    /**
     * 已拒绝
     */
    public static Integer AUDIT_STATE_3 = 3;
    /**
     * 1运输单
     */
    public static Integer BILL_TYPE_1 = 1;
    /**
     * 2出库单
     */
    public static Integer BILL_TYPE_2 = 2;
    /**
     * 3入库单
     */
    public static Integer BILL_TYPE_3 = 3;


    /* 单据类型(1运输单 2出库单 3入库单 4库存单 5增值单) */
    /**
     * 4库存单
     */
    public static Integer BILL_TYPE_4 = 4;
    /**
     * 5增值单（仓）
     */
    public static Integer BILL_TYPE_5 = 5;
    /**
     * 6增值单(运)
     */
    public static Integer BILL_TYPE_6 = 6;
    /**
     * 运输
     */
    public static Set<Integer> BILL_TYPE_TRANS_SETS = Sets.newHashSet(1, 6);
    /**
     * 仓储
     */
    public static Set<Integer> BILL_TYPE_STORAGE_SETS = Sets.newHashSet(2, 3, 4, 5);
    /**
     * 下载
     */
    public static Integer IS_DOWN_DETAIL_0 = 0;
    /**
     * 不下载
     */
    public static Integer IS_DOWN_DETAIL_1 = 1;
    /**
     * 参与计费
     */
    public static Integer IS_CALCULATED_1 = 1;
    /**
     * 未参与计费
     */
    public static Integer IS_CALCULATED_0 = 0;
    /**
     * 待计费
     */
    public static String ESTIMATE_BILLING_STATE_0 = "0";
    /**
     * 计费中
     */
    public static String ESTIMATE_BILLING_STATE_1 = "1";
    /**
     * 应收运输单预估计费
     */
    public static String ESTIMATE_BILLING_MOUDLE_YS_FREIGHT = "ESTIMATE_YS_FREIGHT";
    /**
     * 应收出库单预估计费
     */
    public static String ESTIMATE_BILLING_MOUDLE_YS_OUTBOUND = "ESTIMATE_YS_OUTBOUND";
    /**
     * 应收入库单预估计费
     */
    public static String ESTIMATE_BILLING_MOUDLE_YS_INBOUND = "ESTIMATE_YS_INBOUND";
    /**
     * 应收库存单预估计费
     */
    public static String ESTIMATE_BILLING_MOUDLE_YS_STOCK = "ESTIMATE_YS_STOCK";
    /**
     * 应收增值单预估计费
     */
    public static String ESTIMATE_BILLING_MOUDLE_YS_ADDFEE = "ESTIMATE_YS_ADDFEE";
    public static String ESTIMATE_BILLING_MOUDLE_YS_MONTH = "ESTIMATE_YS_MONTH";
    /**
     * 运输业务
     */
    public static Integer BUSINESS_TYPE_1 = 1;
    /**
     * 仓储业务
     */
    public static Integer BUSINESS_TYPE_2 = 2;
    /**
     * 仓配业务
     */
    public static Integer BUSINESS_TYPE_3 = 3;
    /**
     * 0:按月
     */
    public static Integer ESTIMATE_TYPE_0 = 0;
    /**
     * 1:按日
     */
    public static Integer ESTIMATE_TYPE_1 = 1;
    /**
     * 增
     */
    public static String OPT_TYPE_INSERT = "I";


    /*
     * 查询预估计费类型 0:按月 1:按日
     */
    /**
     * 删
     */
    public static String OPT_TYPE_DELETE = "D";
    /**
     * 改
     */
    public static String OPT_TYPE_UPDATE = "U";
    /**
     * 新报价增值费拜编码
     */
    public static Integer FEE_TYPE_ADD_FEE = 32767;
    public static String FEE_TYPE_ADD_FEE_STR = "增值费（仓）";
    public static String FEE_TYPE_ADD_FEE_STR_6 = "增值费（运）";
    /**
     * 附件类型:1报价附件
     */
    public static Integer ATTACHMENT_TYPE_1 = 1;
    /**
     * 附件类型:2收款附件
     */
    public static Integer ATTACHMENT_TYPE_2 = 2;
    /**
     * 附件类型:3付款附件
     */
    public static Integer ATTACHMENT_TYPE_3 = 3;
    /**
     * 附件类型:4核销附件
     */
    public static Integer ATTACHMENT_TYPE_4 = 4;
    /**
     * 附件类型:5应付核销附件
     */
    //FIXME 注意:不知道为什么明细报价的附件类型都是5，因为要兼容明细的老数据，所以只能继续延用
    public static Integer ATTACHMENT_TYPE_5 = 5;
    /**
     * 跨月
     */
    public static Integer JUDGE_MONTH_0 = 0;
    /**
     * 非跨月
     */
    public static Integer JUDGE_MONTH_1 = 1;
    /**
     * 生成账单
     */
    public static Integer GENERATE_BILL_1 = 1;
    /**
     * 未生成账单
     */
    public static Integer GENERATE_BILL_0 = 0;
    /**
     * 含运输单 账单下载使用
     */
    public static Set<Integer> GENERATE_BILL_TYPE_TRANS_SETS = Sets.newHashSet(2, 4);
    /**
     * 含仓储单 账单下载使用
     */
    public static Set<Integer> GENERATE_BILL_TYPE_STROCK_SETS = Sets.newHashSet(3, 4);
    public static String PUBLIC_QUOTERULE_SETUP_MOUDLE = "客户报价设置";
    public static String PUBLIC_MAIN_QUOTERULE_NODE = "主合同";
    public static String PUBLIC_SUB_QUOTERULE_NODE = "子合同";
    /**
     * 应收账单 客户
     */
    public static Integer BMS_APPLY_RULE_TYPE_1 = 1;
    /**
     * 应付账单 承运商
     */
    public static Integer BMS_APPLY_RULE_TYPE_2 = 2;
    /**
     * 自治区地区处理
     */
    public static Map<Object, Object> AUTONOMOUS_SPECIAL_AREA_MAPS = MapUtil.of(new String[][]{
            {"内蒙古自治区", "内蒙古地区"},
            {"广西壮族自治区", "广西地区"},
            {"西藏自治区", "西藏地区"},
            {"宁夏回族自治区", "宁夏地区"},
            {"新疆维吾尔自治区", "新疆地区"}
    });
    /**
     * 特殊地区处理
     */
    public static Map<Object, Object> SPECIAL_AREA_MAPS = MapUtil.of(new String[][]{
            {"香港", "香港地区"},
            {"澳门", "澳门地区"},
            {"全国", "全国地区"}
    });
    /**
     * 直辖市地区处理
     */
    public static Map<Object, Object> MUNICIPALITY_SPECIAL_AREA_MAPS = MapUtil.of(new String[][]{
            {"北京市", "北京地区"},
            {"天津市", "天津地区"},
            {"上海市", "上海地区"},
            {"重庆市", "重庆地区"}
    });
    /**
     * 订单来源 0 对接产生 1 导入产生
     */
    public static Integer ORDER_SOURCE_0 = 0;
    /**
     * 订单来源 0 对接产生 1 导入产生
     */
    public static Integer ORDER_SOURCE_1 = 1;
    /**
     * 判断报价是否有效 0:有效
     */
    public static Integer ORDER_SORT_0 = 0;
    /**
     * 判断报价是否有效 1:无效
     */
    public static Integer ORDER_SORT_1 = 1;
    /**
     * 判断是否合并操作 Y合并账单，N取消合并
     */
    public static String CONSOLID_YN_Y = "Y";
    /**
     * 判断是否合并操作 Y合并账单，N取消合并
     */
    public static String CONSOLID_YN_N = "N";
    /**
     * 是否为合并账单：0:正常账单 1:合并账单
     */
    public static String MERGE_STATUS_0 = "0";
    /**
     * 是否为合并账单：0:正常账单 1:合并账单
     */
    public static String MERGE_STATUS_1 = "1";
    /**
     * 费用归属1运输 2仓储 3 配送.
     */
    public static Integer FEE_BELONG_1 = 1;
    /**
     * 费用归属1运输 2仓储 3 配送.
     */
    public static Integer FEE_BELONG_2 = 2;
    /**
     * 费用归属1运输 2仓储 3 配送.
     */
    public static Integer FEE_BELONG_3 = 3;
    /**
     * xlsx
     */
    public static String XLSX = ".xlsx";
    /**
     * 逗号
     */
    public static String COMMA = ",";
    /**
     * 仓配
     */
    public static Integer STORAGE_TRANSPORT_4 = 4;
    /**
     * 仓储3
     */
    public static Integer STORAGE_3 = 4;
    /**
     * 运输2
     */
    public static Integer TRANSPORT_2 = 4;
    /**
     * 是否开票备注自动更新月标识 0:否,1:是
     */
    public static Integer AUTOMATIC_REMARK_0 = 0;
    /**
     * 是否开票备注自动更新月标识 0:否,1:是
     */
    public static Integer AUTOMATIC_REMARK_1 = 1;
    public static Set<Integer> OTHER_INVOICE_PROJECT_SETS = Sets.newHashSet(3, 4, 5, 6);
    /**
     * 开票方式 1按比例开票  2按金额开票
     */
    public static Integer INVOICE_MODE_1 = 1;
    /**
     * 开票方式 1按比例开票  2按金额开票
     */
    public static Integer INVOICE_MODE_2 = 2;
    /**
     * 应收出账sheet-账单汇总
     */
    public static String OUTGOING_CODE_YS_BILL_SUMMARY = "YS_BILL_SUMMARY";
    /**
     * 应收出账sheet-存储费主信息
     */
    public static String OUTGOING_CODE_YS_STORAGE_FEE_MASTER_INFORMATION = "YS_STORAGE_FEE_MASTER_INFORMATION";
    /**
     * 应收出账sheet-存储费明细信息
     */
    public static String OUTGOING_CODE_YS_STORAGE_FEE_DETAIL_INFORMATION = "YS_STORAGE_FEE_DETAIL_INFORMATION";
    /**
     * 应收出账sheet-入库费主信息
     */
    public static String OUTGOING_CODE_YS_INVENTORY_FEE_MASTER_INFORMATION = "YS_INVENTORY_FEE_MASTER_INFORMATION";
    /**
     * 应收出账sheet-入库费明细信息
     */
    public static String OUTGOING_CODE_YS_INVENTORY_FEE_DETAIL_INFORMATION = "YS_INVENTORY_FEE_DETAIL_INFORMATION";
    /**
     * 应收出账sheet-出库费主信息
     */
    public static String OUTGOING_CODE_YS_OUTBOUND_FEE_MASTER_INFORMATION = "YS_OUTBOUND_FEE_MASTER_INFORMATION";
    /**
     * 应收出账sheet-出库费明细信息
     */
    public static String OUTGOING_CODE_YS_OUTBOUND_FEE_DETAIL_INFORMATION = "YS_OUTBOUND_FEE_DETAIL_INFORMATION";
    /**
     * 应收出账sheet-月配送量
     */
    public static String OUTGOING_CODE_YS_MONTHLY_DELIVERY_VOLUME = "YS_MONTHLY_DELIVERY_VOLUME";
    /**
     * 应收出账sheet-配送费主信息
     */
    public static String OUTGOING_CODE_YS_DELIVERY_FEE_MASTER_INFORMATION = "YS_DELIVERY_FEE_MASTER_INFORMATION";
    /**
     * 应收出账sheet-配送费商品信息
     */
    public static String OUTGOING_CODE_YS_DELIVERY_FEE_GOODS_INFORMATION = "YS_DELIVERY_FEE_GOODS_INFORMATION";
    /**
     * 应收出账sheet-固定费用明细
     */
    public static String OUTGOING_CODE_YS_FIXED_COST_DETAILS = "YS_FIXED_COST_DETAILS";
    /**
     * 应收出账sheet-理赔费用信息
     */
    public static String OUTGOING_CODE_YS_CLAIM_EXPENSE_INFORMATION = "YS_CLAIM_EXPENSE_INFORMATION";
    /**
     * 应收出账sheet-增值费用信息
     */
    public static String OUTGOING_CODE_YS_VALUE_ADDED_EXPENSE_INFORMATION = "YS_VALUE_ADDED_EXPENSE_INFORMATION";
    public static Set<String> OTHERFEE_SETS = Sets.newHashSet(
            "otherCost1"
            , "otherCost2"
            , "otherCost3"
            , "otherCost4"
            , "otherCost5"
            , "otherCost6"
            , "otherCost7"
            , "otherCost8"
            , "otherCost9"
            , "otherCost10"
            , "otherCost11"
            , "otherCost12"
    );
    /**
     * 字段类型 0文本 1 金额 2数字 3 时间 4 数字(保留2位小数) 5日期
     */
    public static Integer FIELD_TYPE_0 = 0;
    /**
     * 字段类型 0文本 1 金额 2数字 3 时间 4 数字(保留2位小数) 5日期
     */
    public static Integer FIELD_TYPE_1 = 1;
    /**
     * 字段类型 0文本 1 金额 2数字 3 时间 4 数字(保留2位小数) 5日期
     */
    public static Integer FIELD_TYPE_2 = 2;
    /**
     * 字段类型 0文本 1 金额 2数字 3 时间 4 数字(保留2位小数) 5日期
     */
    public static Integer FIELD_TYPE_3 = 3;
    /**
     * 字段类型 0文本 1 金额 2数字 3 时间 4 数字(保留2位小数) 5日期
     */
    public static Integer FIELD_TYPE_4 = 4;
    /**
     * 字段类型 0文本 1 金额 2数字 3 时间 4 数字(保留2位小数) 5日期
     */
    public static Integer FIELD_TYPE_5 = 5;
    /**
     * 是否需要合并 0 不合并 1 合并
     */
    public static Integer IS_MERAGE_0 = 0;
    public static Integer IS_MERAGE_1 = 1;
    /**
     * 是否需要汇总 0 否 1 是 2 是(费用单维度汇总)
     */
    public static Integer IS_SUM_0 = 0;
    public static Integer IS_SUM_1 = 1;
    public static Integer IS_SUM_2 = 2;
    /**
     * 是否启用 false:不启用 true 启用
     */
    public static Boolean SELF_FLAG_FALSE = Boolean.FALSE;
    public static Boolean SELF_FLAG_TRUE = Boolean.TRUE;
    /**
     * 是否需要隐藏 0不隐藏 1 隐藏(基础费和其他费使用)仅数字列可使用'
     */
    public static Integer IS_HIDE_0 = 0;
    public static Integer IS_HIDE_1 = 1;
    /**
     * 未计费
     */
    public static Integer COST_STATUS_0 = 0;
    /**
     * 已计费
     */
    public static Integer COST_STATUS_1 = 1;
    /**
     * 不收费
     */
    public static Integer COST_STATUS_2 = 2;
    /**
     * 计费失败
     */
    public static Integer COST_STATUS_3 = 3;
    /**
     * 0:未计费,3:计费失败
     */
    public static Set<Integer> STATUS_NOCOST_SETS = Sets.newHashSet(0, 3);
    /**
     * 未生成
     */
    public static Integer BILLING_STATUS_0 = 0;
    /**
     * 已生成
     */
    public static Integer BILLING_STATUS_1 = 1;
    /**
     * 结算方式1月结2现结3季节4周结
     */
    public static Integer CARRIERPAY_TYPE_1 = 1;
    /**
     * 结算方式1月结2现结3季节4周结
     */
    public static Integer CARRIERPAY_TYPE_2 = 2;
    /**
     * 结算方式1月结2现结3季节4周结
     */
    public static Integer CARRIERPAY_TYPE_3 = 3;
    /**
     * 结算方式1月结2现结3季节4周结
     */
    public static Integer CARRIERPAY_TYPE_4 = 4;
    /**
     * 结算方式1月结2现结3季节4周结5到付(货到打卡)
     */
    public static Integer CARRIERPAY_TYPE_5 = 5;
    /**
     * 结算方式1月结2现结3季节4周结5到付(货到打卡)6到付（目的站收）
     */
    public static Integer CARRIERPAY_TYPE_6 = 6;
    /**
     * 结算方式1月结2现结3季节4周结5到付(货到打卡)6到付（目的站收）7回单付
     */
    public static Integer CARRIERPAY_TYPE_7 = 7;
    /**
     * 结算方式1月结2现结3季节4周结5到付(货到打卡)6到付（目的站收）7回单付 8现返（付）
     */
    public static Integer CARRIERPAY_TYPE_8 = 8;
    /**
     * 结算方式1月结2现结3季节4周结5到付(货到打卡)6到付（目的站收）7回单付 8现返（付）9欠返（付）
     */
    public static Integer CARRIERPAY_TYPE_9 = 9;
    /**
     * 结算方式1月结2现结3季节4周结5到付(货到打卡)6到付（目的站收）7回单付 8现返（付）9欠返（付）10异常费
     */
    public static Integer CARRIERPAY_TYPE_10 = 10;
    /**
     * 结算方式1月结2现结3季节4周结5到付(货到打卡)6到付（目的站收）7回单付 8现返（付）9欠返（付）10异常费 11附加费
     */
    public static Integer CARRIERPAY_TYPE_11 = 11;
    /**
     * 费用类型:1运输费2出库类费3入库类费4库存类费5仓储类费
     */
    public static Integer EXPENSES_TYPE_1 = 1;
    /**
     * 费用类型:1运输费2出库类费3入库类费4库存类费5仓储类费
     */
    public static Integer EXPENSES_TYPE_2 = 2;
    /**
     * 费用类型:1运输费2出库类费3入库类费4库存类费5仓储类费
     */
    public static Integer EXPENSES_TYPE_3 = 3;
    /**
     * 费用类型:1运输费2出库类费3入库类费4库存类费5仓储类费
     */
    public static Integer EXPENSES_TYPE_4 = 4;
    /**
     * 费用类型:1运输费2出库类费3入库类费4库存类费5仓储类费
     */
    public static Integer EXPENSES_TYPE_5 = 5;
    /**
     * 费用维度：1.单2.趟3.日4.月5品规
     */
    public static Integer COST_DIMENSION_1 = 1;
    /**
     * 费用维度：1.单2.趟3.日4.月5品规
     */
    public static Integer COST_DIMENSION_2 = 2;
    /**
     * 费用维度：1.单2.趟3.日4.月5品规
     */
    public static Integer COST_DIMENSION_3 = 3;
    /**
     * 费用维度：1.单2.趟3.日4.月5品规
     */
    public static Integer COST_DIMENSION_4 = 4;
    /**
     * 费用维度：1.单2.趟3.日4.月5品规
     */
    public static Integer COST_DIMENSION_5 = 5;
    /**
     * 计费方式1自动计费2手动计费
     */
    public static Integer CHARGE_TYPE_1 = 1;
    /**
     * 计费方式1自动计费2手动计费
     */
    public static Integer CHARGE_TYPE_2 = 2;
    /**
     * 费用标识1正常费用2冲销费
     */
    public static Integer FEE_FLAG_1 = 1;
    public static Integer EXPENSES_MARK_1 = 1;
    /**
     * 费用标识1正常费用2冲销费
     */
    public static Integer FEE_FLAG_2 = 2;
    public static Integer EXPENSES_MARK_2 = 2;
    /**
     * 单号类型(1.运输订单 2 出库单 3 入库单 4:库存单)
     */
    public static Integer CODE_TYPE_1 = 1;
    /**
     * 单号类型(1.运输订单 2 出库单 3 入库单 4:库存单)
     */
    public static Integer CODE_TYPE_2 = 2;
    /**
     * 单号类型(1.运输订单 2 出库单 3 入库单 4:库存单)
     */
    public static Integer CODE_TYPE_3 = 3;
    /**
     * 单号类型(1.运输订单 2 出库单 3 入库单 4:库存单)
     */
    public static Integer CODE_TYPE_4 = 4;
    /**
     * 单据类型(2.出库单, 入库单, 4:库存单)
     */
    public static Set<Integer> CODE_TYPE_STORAGE_SETS = Sets.newHashSet(2,3,4);
    /**
     * 原始单据类型(1,调度单，2,作业单)
     */
    public static Integer ORIGINAL_ORDER_TYPE_1 = 1;
    /**
     * /**
     * 原始单据类型(1,调度单，2,作业单)
     */
    public static Integer ORIGINAL_ORDER_TYPE_2 = 2;

    /**
     * 结算方式 默认月结
     */
    public static Integer SETTLE_TYPE_DEFAULT = 1;
    /**
     * 结算方式0:默认 1月结2现结3季节4周结
     */
    public static Integer SETTLE_TYPE_0 = 0;
    /**
     * 结算方式0:默认 1月结2现结3季节4周结
     */
    public static Integer SETTLE_TYPE_1 = 1;
    /**
     * 结算方式0:默认 1月结2现结3季节4周结
     */
    public static Integer SETTLE_TYPE_2 = 2;
    /**
     * 结算方式0:默认 1月结2现结3季节4周结
     */
    public static Integer SETTLE_TYPE_3 = 3;
    /**
     * 结算方式0:默认 1月结2现结3季节4周结
     */
    public static Integer SETTLE_TYPE_4 = 4;
    /**
     * 是否为增值费0否1是
     */
    public static Integer IS_INCREMENT_0 = 0;
    /**
     * 是否为增值费0否1是
     */
    public static Integer IS_INCREMENT_1 = 1;

    /**
     * 单结算方式SETS
     */
    public static Set<Integer> COST_SETTLE_SINGLE_SETS = Sets.newHashSet(1, 2);
    /**
     * 多结算方式SETS
     */
    public static Set<Integer> COST_SETTLE_MULTITION_SETS = Sets.newHashSet(3, 4);
    /**
     * 单结算方式
     */
    public static Integer SETTLE_SETTING_1 = 1;
    /**
     * 多结算方式
     */
    public static Integer SETTLE_SETTING_2 = 2;


    /*
     * 公共费用类型：1固定费2增值费3浮动计费
     */

    /**
     * 1固定费
     */
    public static Integer SHOW_TYPE_1 = 1;

    /**
     * 2增值费
     */
    public static Integer SHOW_TYPE_2 = 2;

    /**
     * 3浮动计费
     */
    public static Integer SHOW_TYPE_3 = 3;


    /**
     * 单据类型类型 1.运输订单 2 出库单 3 入库单 4 库存单
     */
    public static Integer CODE_TYPE_01 = 1;
    public static Integer CODE_TYPE_02 = 2;
    public static Integer CODE_TYPE_03 = 3;
    public static Integer CODE_TYPE_04 = 4;


    /**
     * 费用中间表(1:运单，2:仓储)
     */
    public static Integer MIDDLE_CODE_TYPE_01 = 1;
    public static Integer MIDDLE_CODE_TYPE_02 = 2;


    /**
     * 分摊类型(1:按单,2:按重量,3:按体积,4:按件数 取自bms_clientinfo.share_type)
     */
    public static Integer SHARE_TYPE_01 = 1;
    public static Integer SHARE_TYPE_02 = 2;
    public static Integer SHARE_TYPE_03 = 3;
    public static Integer SHARE_TYPE_04 = 4;

    /**
     * 结算模式(1:应收，2:应付,仓储计费需要判断是否产生应收应付对应单据)
     */
    public static Integer COST_MODE_01 = 1;
    public static Integer COST_MODE_02 = 2;


    /**
     * 是否自营(0否1是);根据单据对接字段(0是  1否)判断是否为自营，如果为否要新增应付单据
     */
    public static Integer IS_AUTARKY_0 = 0;
    public static Integer IS_AUTARKY_1 = 1;



    /**
     * 计费是否成功,0:成功，1:失败(只关注计费公式，和计费过程没有关系)
     */
    public static Integer CALCULATE_FLAG_0 = 0;
    public static Integer CALCULATE_FLAG_1 = 1;


    /**
     * 账单标识 1正常账单 2冲销账单
     */
    public static Integer BILL_MARKING_1 = 1;
    public static Integer BILL_MARKING_2 = 2;


    /**
     * 账单开票状态
     * 1未开票 2已开票 3部分开票 4无需开票
     */
    public static Integer TICKET_STATE_1 = 1;
    public static Integer TICKET_STATE_2 = 2;
    public static Integer TICKET_STATE_3 = 3;
    public static Integer TICKET_STATE_4 = 4;


    /**
     * 核销状态 1未核销 2已核销 3部分核销
     */
    public static Integer HX_STATE_1 = 1;
    public static Integer HX_STATE_2 = 2;
    public static Integer HX_STATE_3 = 3;


    /**
     * 计费时间维度(1:订单日期,2:签收日期)
     * 计费时间维度(1:配载日期,2:完成日期)
     */
    public static Integer COST_DATE_DIMENSION_DEFAULT = 1; //默认
    public static Integer COST_DATE_DIMENSION_1 = 1; //订单日期||配载日期
    public static Integer COST_DATE_DIMENSION_2 = 2; //签收日期||完成日期


    /**
     * 附件下载
     */
    public static String FILE_DOWNLOAD_PATH = "http://joy.uat.enjoy-link.com/file/downLoadFile?url=";


    /**
     * 基础费常量
     */
    public static final String FREIGHT = "freight";
    public static final String DELIVERY_FEE = "deliveryFee";
    public static final String ULTRAFAR_FEE = "ultrafarFee";
    public static final String SUPERFRAMES_FEE = "superframesFee";
    public static final String EXCESS_FEE = "excessFee";
    public static final String REDUCE_FEE = "reduceFee";
    public static final String OUTBOUNDSORTING_FEE = "outboundsortingFee";
    public static final String SHORTBARGE_FEE = "shortbargeFee";
    public static final String RETURN_FEE = "returnFee";
    public static final String EXCEPTION_FEE = "exceptionFee";
    public static final String ADJUST_FEE = "adjustFee";
    /**
     * 其它费常量
     */
    public static final String OTHER_COST1 = "otherCost1";
    public static final String OTHER_COST2 = "otherCost2";
    public static final String OTHER_COST3 = "otherCost3";
    public static final String OTHER_COST4 = "otherCost4";
    public static final String OTHER_COST5 = "otherCost5";
    public static final String OTHER_COST6 = "otherCost6";
    public static final String OTHER_COST7 = "otherCost7";
    public static final String OTHER_COST8 = "otherCost8";
    public static final String OTHER_COST9 = "otherCost9";
    public static final String OTHER_COST10 = "otherCost10";
    public static final String OTHER_COST11 = "otherCost11";
    public static final String OTHER_COST12 = "otherCost12";



}
