package com.bbyb.joy.bms.calculate.domain.dto.rule;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 计算规则信息
 * pub_quoterule+pub_quoterule_detail+pub_quoterule_template
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateRuleInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 关联ID(报价类型(1客户,2承运商))
     */
    private Integer relationId;
    /**
     * 报价子合同id:pub_quoterule_detail.id
     */
    private String id;
    /**
     * 报价子合同id:pub_quoterule_detail.pk_id
     */
    private Integer pkId;
    /**
     * 报价子合同编码
     */
    private String ruleCode;
    /**
     * 报价子合同
     */
    private String ruleName;
    /**
     * 使用机构串  #1#,#2#,#3#,#4#
     */
    private String userCompanyId;
    /**
     * 是否为项目报价1否2是
     */
    private Integer projectQuotation;
    /**
     * 报价主合同id:pub_quoterule_detail.id
     */
    private String mainId;
    /**
     * 报价主合同id:pub_quoterule_detail.pk_id
     */
    private Integer mainPkId;
    /**
     * 报价主合同id:pub_quoterule_detail.rule_code
     */
    private String mainRuleCode;
    /**
     * 报价主合同:pub_quoterule_detail.rule_name
     */
    private String mainRuleName;
    /**
     * 费用类型(基础费&&其他费)
     * 计费报价费用类型转换费用表存储 (应收)
     * feeType 报价规则费用类型（字典 1 运费(freight) 2 超远费(ultrafarFee) 3 超筐费() 4 加点费 5 减点费 6 提货费 7 送货费 8 短驳费 9 退货费
     * 10 存储费 11 管理处置费 12 整箱分拣费 13 拆零分拣费 14整箱装卸费 15拆零装卸费 16 制单费 17 操作费）
     */
    private Integer feeType;
    /**
     * 匹配条件规则
     */
    private String matchingCode;
    /**
     * 计费公式规则(规则模版:pub_quoterule_template.calculation_formula_code)
     */
    private String calculateCode;
    /**
     * 计费过程代码
     */
    private String processCode;
    /**
     * 费用类型名称
     */
    private String feeTypeName;
    /**
     * 分摊方式(1:按单,2:按体积,3:按重量)
     */
    private Integer shareType = 1;
    /**
     * 取高取低(0:取低,1:取高)
     * 暂时还不用
     */
    private Integer comparisonStrategy = 1;
    /**
     * 规则模版id(规则模版:pub_quoterule_template.id)
     */
    private String templateId;
    /**
     * 规则模版编码(规则模版:pub_quoterule_template.rule_code)
     */
    private String templateRuleCode;
    /**
     * 规则模版名称(规则模版:pub_quoterule_template.rule_name)
     */
    private String templateRuleName;
    /**
     * 规则模版:(规则类型(1客户,2承运商):pub_quoterule_detail.rule_type)
     */
    private Integer ruleType;
    /**
     * 合并规则(1:"单",2:"趟",3:"日",4:"月",5:"品规"):pub_quoterule_template.consolidation_rule
     */
    private Integer consolidationRule;
    private String consolidationRuleName;
    /**
     * 报价类型 1通用 2应收 3应付:pub_quoterule_template.bj_type
     */
    private Integer ruleBjType;
    /**
     * 单据类型 1运输单 2入库单 3出库单 4库存单 5增值单: pub_quoterule_detail.bill_type
     * 注意：这里给的是报价明细中的单据类型,不是报价规则模版中的
     */
    private Integer orderType;
    /**
     * 单据类型多选 1运输单 2入库单 3出库单 4库存单 5增值单
     */
    private Integer orderTypeList;
    /**
     * 报价分组条件 pub_quoterule_template.group_rule
     * 1|线路
     * 2|网点
     * 3|始发省份
     * 4|始发城市
     * 5|始发区县
     * 6|目的省份
     * 7|目的城市
     * 8|目的区县
     * 9|门店
     * 10|原调度单号
     * 11|TMS线路编码
     * 12|客户
     * 13|配载时间(yyyy-MM-dd)
     * 14|订单时间(yyyy-MM-dd)
     * 15|签收时间(yyyy-MM-dd)
     * 16|完成时间(yyyy-MM-dd)
     * 17|店次
     * 入参数据格式(例子):#12#,#11#,#13#,#10#
     */
    private String groupRule;
    /**
     * 结算类型1月结2现结3季节4周结 取缓存
     */
    private Integer carrierpayType;
    /**
     * 业务类型
     */
    private Integer businessType;
    /**
     * 结算方式0:默认 1月结2现结3季节4周结
     */
    private Integer settleType;
    /**
     * 结算主体(取租户BMD配置字段SettlementEntityType(是否存在多个结算主体 1不存在 2存在))
     */
    private Integer settleEntityType;
    /**
     * 结算方式(1:单结算方式,2:多结算方式)
     */
    private Integer settleSetting;


}
