package com.bbyb.joy.bms.support.exception;


import com.bbyb.joy.bms.domain.enums.InterfaceRecvRecerdEnum;
import com.bbyb.joy.bms.support.CoreServerException;
import com.bbyb.joy.bms.support.ServiceError;
import lombok.Getter;

/**
 * 通用业务异常
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Getter
public class BusinessException extends CoreServerException {

    private final Integer code;
    private final String message;

    public BusinessException(String message) {
        super(message);
        this.code = ServiceError.PARAMETER_ERROR.getCode();
        this.message = message;
    }

    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BusinessException(ServiceError serviceError) {
        super(serviceError.getMessage());
        this.code = serviceError.getCode();
        this.message = serviceError.getMessage();
    }

    public BusinessException(String message, InterfaceRecvRecerdEnum recordEnum, String requestJson, String responseJson, String serviceCode) {
        super(message);
        this.code = ServiceError.PARAMETER_ERROR.getCode();
        this.message = message;
    }

    public BusinessException(Integer code, String message, InterfaceRecvRecerdEnum recordEnum, String requestJson, String responseJson, String serviceCode) {
        super(message);
        this.code = code;
        this.message = message;
    }


    @Override
    public String getMessage() {
        return message;
    }
}
