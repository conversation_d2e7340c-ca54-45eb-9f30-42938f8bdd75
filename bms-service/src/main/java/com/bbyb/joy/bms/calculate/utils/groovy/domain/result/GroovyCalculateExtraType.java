package com.bbyb.joy.bms.calculate.utils.groovy.domain.result;

import io.swagger.annotations.ApiModel;
import lombok.Getter;

@Getter
@ApiModel("自动计费额外计费场景类型")
public enum GroovyCalculateExtraType {


    /**
     * 维度计费
     */
    DEFAULT(0, "默认","默认"),
    SCHEDULT_JOB(1, "调度单-单维度计费-作业单维度费用输出", "调度单维度进行计费，作业单维度输出计费结果，要在作业单维度的费用进行计费输出");

    /**
     * 额外类型编码
     */
    private final Integer code;

    /**
     * 额外计费类型名称
     */
    private final String name;

    /**
     * 额外计费描述
     */
    private final String desc;

    GroovyCalculateExtraType(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    /**
     * 根据编码获取枚举
     */
    public static GroovyCalculateExtraType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (GroovyCalculateExtraType type : GroovyCalculateExtraType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的额外计费类型编码: " + code);
    }

    /**
     * 根据名称获取枚举
     */
    public static GroovyCalculateExtraType getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        for (GroovyCalculateExtraType type : GroovyCalculateExtraType.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的额外计费类型名称: " + name);
    }

}
