package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.pay.BmsInvoiceRecord;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsInvoiceRecordService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 收票记录Controller
 *
 * <AUTHOR>
 */
@Api(tags = "收票记录接口")
@RestController
@RequestMapping("/system/nvoRecordService")
public class BmsInvoiceRecordController {

    @Resource
    private IBmsInvoiceRecordService bmsInvoiceRecordService;
    @Resource
    private UserRights userRights;
    @Resource
    private ExportUtil exportUtil;

    /**
     * 查询收票记录列表
     */
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<BmsInvoiceRecord>> list(@RequestBody(required = false) BmsInvoiceRecord bmsInvoiceRecord) {
        String token = RequestContext.getToken();
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "carrier");
        List<Carrierinfo> carrierList = sysDataPack.getCarrierInfo();
        if (CollUtil.isEmpty(carrierList)) {
            return new ResponseResult<>(new PagerDataBean<>(new ArrayList<>(), 0, new PagerBean(bmsInvoiceRecord.getPage(), bmsInvoiceRecord.getSize())));
        }
        bmsInvoiceRecord.setCarrierList(carrierList.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toList()));
        return new ResponseResult<>(bmsInvoiceRecordService.selectBmsInvoiceRecordList(token, bmsInvoiceRecord));
    }


    /**
     * 导出收票记录列表
     */
    @Log(title = "收票记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public ResponseResult<String> export(@RequestBody(required = false) BmsInvoiceRecord bmsInvoiceRecord) {
        //获取token
        String token = RequestContext.getToken();
        return exportUtil.getOutClassNew(token, "收票记录", "收票管理", BmsInvoiceRecord.class, userId -> {
            return bmsInvoiceRecordService.selectBmsInvoiceRecordList(token, bmsInvoiceRecord).getRows();
        });
    }


    /**
     * 新增收票记录
     */
    @Log(title = "收票记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @MenuAuthority(code = "应付账单管理-收票登记,应付付款管理-新增")
    public ResponseResult<String> add(@RequestBody(required = false) List<BmsInvoiceRecord> bmsInvoiceRecord) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsInvoiceRecordService.insertBmsInvoiceRecord(token, bmsInvoiceRecord));
    }

    /**
     * 修改收票记录
     */
    @Log(title = "收票记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @MenuAuthority(code = "应付账单管理-发票作废,应付付款管理-修改")
    public ResponseResult<String> edit(@RequestBody(required = false) List<BmsInvoiceRecord> bmsInvoiceRecord) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsInvoiceRecordService.updateBmsInvoiceRecord(token, bmsInvoiceRecord));
    }


}
