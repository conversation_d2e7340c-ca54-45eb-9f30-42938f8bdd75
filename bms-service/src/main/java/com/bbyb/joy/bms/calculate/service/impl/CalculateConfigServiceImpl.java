package com.bbyb.joy.bms.calculate.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.bbyb.bms.model.po.SysConfigPO;
import com.bbyb.bms.model.po.SysConfigPOExample;
import com.bbyb.joy.bms.calculate.domain.model.CalculateConfigModel;
import com.bbyb.joy.bms.calculate.service.CalculateConfigService;
import com.bbyb.joy.bms.support.datesource.BmsDs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class CalculateConfigServiceImpl implements CalculateConfigService {

    /**
     * 自动计费配置参数
     * sys_config.config_name = '自动计费参数'
     */
    public static final String CALCULATE_CONFIG_KEY = "自动计费参数";
    public static final String CALCULATE_RELATION_TASK_MIN = "calculate.relation.task.min";
    public static final String CALCULATE_RELATION_TASK_MAX = "calculate.relation.task.max";
    public static final String CALCULATE_RULE_TASK_MIN = "calculate.rule.task.min";
    public static final String CALCULATE_RULE_TASK_MAX = "calculate.rule.task.max";



    @Override
    public CalculateConfigModel config() {

        SysConfigPOExample queryConfigExample = new SysConfigPOExample();
        queryConfigExample.createCriteria()
                .andConfigKeyEqualTo(CALCULATE_CONFIG_KEY);
        List<SysConfigPO> queryConfigs = BmsDs.instance().WMSMybatis().mapper().selectByExample(queryConfigExample);

        if(CollUtil.isEmpty(queryConfigs)){
            return defaultBuild();
        }

        CalculateConfigModel buildData = defaultBuild();

        for (SysConfigPO queryConfig : queryConfigs) {
            if(queryConfig.getConfigKey().equals(CALCULATE_RELATION_TASK_MIN)){
                buildData.setRelationTaskMin(Integer.parseInt(queryConfig.getConfigValue()));
            }
            if(queryConfig.getConfigKey().equals(CALCULATE_RELATION_TASK_MAX)){
                buildData.setRelationTaskMax(Integer.parseInt(queryConfig.getConfigValue()));
            }
            if(queryConfig.getConfigKey().equals(CALCULATE_RULE_TASK_MIN)){
                buildData.setRuleTaskMin(Integer.parseInt(queryConfig.getConfigValue()));
            }
            if(queryConfig.getConfigKey().equals(CALCULATE_RULE_TASK_MAX)){
                buildData.setRuleTaskMax(Integer.parseInt(queryConfig.getConfigValue()));
            }
        }
        return buildData;
    }


    /**
     * 默认
     * @return 自动计费参数
     */
    protected static CalculateConfigModel defaultBuild() {
        return CalculateConfigModel.builder()
                .relationTaskMin(1)
                .relationTaskMax(3)
                .relationTaskMin(1)
                .relationTaskMax(1)
                .build();
    }

}
