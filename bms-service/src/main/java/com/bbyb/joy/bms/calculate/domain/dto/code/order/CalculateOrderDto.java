package com.bbyb.joy.bms.calculate.domain.dto.code.order;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 单维度实体
 * 该类的是单维度进入计费的单据信息
 * 该类整合了BMS所有业务单据类型的通用字段
 * 后端拓展单据业务如果有新的单据业务属性计费需要就需要在该类进行拓展补充
 * ## 字段含义补充:
 * 1、(重要)所有业务属性字段不再使用type的code,全部要转译为对应的label,便于维护报价
 * 2、(重要)那基于1带来额外的补充,需要补充对应的业务属性转译的字典的维护源头(要确保一定准确)
 * 3、(重要)日期业务字段全部转移为字符串,groovy运行引用就不再需要处理时间
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateOrderDto implements Serializable {

    public static final long serialVersionUID = 1L;

    public String id;
    public Integer pkId;

    // 关联信息
    public String mainCodeId;             // 单据主表id(uuid)
    public Integer mainCodePkId;          // 单据主表id(自增id)

    // 单据基本信息
    public String code;                   // 业务单号
    public Integer codeType;              // 单据类型
    public String codeTypeName;
    //    public Integer businessType;          // 业务类型
    public String businessTypeName;
    //    public Integer orderSource;            // 订单来源
    public String orderSourceName;
    //    public Integer splitStatus;            // 拆单状态
    public String splitStatusName;
    //    public Integer clientsDimension;       // 是否客户维度
    public String clientsDimensionName;

    //费用相关
    public Integer expenseType;           // 费用类型(1调度单费2出库类费3入库类费4库存类费5仓储类费)
    public String expenseTypeName;

    // 运输配送信息
    //    public String transportType;          // 运输方式
    public String transportTypeName;
    //    public String transportMode;          // 运输类型
    public String transportModeName;
    //    public String deliveryMode;           // 配送类型(1专配2共配)
    public String deliveryModeName;
    public String lineCode;               // 线路编码
    public String lineName;               // 线路名称
    public String tmsLineCode;            // TMS线路编码
    public String tmsLineName;            // TMS线路名称

    // 客户承运商信息
    public Integer clientId;              // 客户id
    public String clientCode;             // 客户编码
    public String clientName;             // 客户名称
    public Integer carrierId;             // 承运商id
    public String carrierCode;            // 承运商编码
    public String carrierName;            // 承运商名称
    public Integer companyId;             // 机构id
    public String companyName;            // 机构

    // 业务状态
    //    public Integer isTimeout;              // 是否超时
    public String isTimeoutName;
    //    public Integer isAutarky;              // 是否自营
    public String isAutarkyName;
    //    public Integer isRejected;             // 是否拒收
    private String isRejectedName;

    public String rejectedResponsibleParty; // 拒收责任方

    // 仓储信息
    public String storageServiceProvider; // 仓储服务商
    public String warehouseCode;          // 仓库编码
    public String warehouseName;          // 仓库名称
    public String networkCode;            // 网点编码
    public String lpnCode;                // 托盘编码

    // 数量统计
    public BigDecimal totalBoxes = BigDecimal.ZERO;         // 总箱数
    public BigDecimal totalSplitBoxes = BigDecimal.ZERO;    // 拆零总箱数
    public BigDecimal totalNormalFullBoxes = BigDecimal.ZERO; // 常温整箱数
    public BigDecimal totalNormalSplitBoxes = BigDecimal.ZERO; // 常温散箱数
    public BigDecimal totalUnnormalFullBoxes = BigDecimal.ZERO; // 非常温整箱数
    public BigDecimal totalUnnormalSplitBoxes = BigDecimal.ZERO; // 非常温散箱数
    public BigDecimal totalNumber = BigDecimal.ZERO;        // 总件数
    public BigDecimal totalSplitNumber = BigDecimal.ZERO;   // 拆零总件数
    public BigDecimal totalOverNumber = BigDecimal.ZERO;    // 超件总数
    public BigDecimal totalDayNumber = BigDecimal.ZERO;     // 日结存总件数
    public BigDecimal totalWeight = BigDecimal.ZERO;        // 总重量(kg)
    public BigDecimal totalVolume = BigDecimal.ZERO;        // 总体积(m³)
    public BigDecimal totalSkuNumber = BigDecimal.ZERO;     // SKU总数
    public BigDecimal totalPalletNumber = BigDecimal.ZERO;  // 总托数
    public BigDecimal totalDayPalletNumber = BigDecimal.ZERO; // 日结存总托数
    public BigDecimal totalCwPalletNumber = BigDecimal.ZERO; // 常温总托数
    public BigDecimal totalLdPalletNumber = BigDecimal.ZERO; // 冷冻总托数
    public BigDecimal totalLcPalletNumber = BigDecimal.ZERO; // 冷藏总托数
    public BigDecimal totalCargoValue = BigDecimal.ZERO;    // 总货值
    public BigDecimal totalVotes = BigDecimal.ZERO;         // 总票数
    public BigDecimal palletRule;         // 托规

    // 商品信息
    public String boxRule;                // 箱规
    public String temperatureType;        // 温区类型
    public String skuCode;                // 商品编码
    public String skuName;                // 商品名称
    public String unit;                   // 单位

    // 时间信息
    public Date businessTime;             // 根据客户||承运商设置获取对应的业务时间(yyyy-MM-dd HH:mm:ss)
    public String businessTimeStr;        // 根据客户||承运商设置获取对应的业务时间(yyyy-MM-dd HH:mm:ss)
    public String businessTimeDay;        // 根据客户||承运商设置获取对应的业务时间(yyyy-MM-dd)
    public String businessTimeMonth;      // 根据客户||承运商设置获取对应的业务时间(yyyy-MM)
    public Date businessBeginTime;        // 订单日期||配载日期(yyyy-MM-dd HH:mm:ss)
    public String businessBeginStr;       // 订单日期||配载日期(yyyy-MM-dd HH:mm:ss)
    public String businessBeginMonth;     // 订单日期||配载日期(yyyy-MM)
    public String businessBeginDay;       // 订单日期||配载日期(yyyy-MM-dd)
    public Date businessEndTime;          // 签收日期||完成日期(yyyy-MM-dd HH:mm:ss)
    public String businessEndStr;         // 签收日期||完成日期(yyyy-MM-dd HH:mm:ss)
    public String businessEndMonth;         // 签收日期||完成日期(yyyy-MM)
    public String businessEndDay;           // 签收日期||完成日期(yyyy-MM-dd)
    public String orderDate;                // 订单日期(yyyy-MM-dd HH:mm:ss)
    public String signingDate;              // 签收日期(yyyy-MM-dd HH:mm:ss)
    public String dispatchDate;             // 配载日期(yyyy-MM-dd HH:mm:ss)
    public String startDate;                // 发车日期(yyyy-MM-dd HH:mm:ss)
    public String finishDate;               // 完成日期(yyyy-MM-dd HH:mm:ss)

    // 地址信息
    public String deliveryCode = "";           // 发货地编码
    public String deliveryName = "";           // 发货地名称
    public String receivingStoreCode = "";     // 收货门店编码
    public String receivingStoreName = "";     // 收货门店名称
    public String originatingProvince = "";    // 始发省
    public String originatingCity = "";        // 始发市
    public String originatingArea = "";        // 始发区
    public String originatingAddress = "";     // 始发详细地址
    public String destinationProvince = "";    // 目的省
    public String destinationCity = "";        // 目的市
    public String destinationArea = "";        // 目的区
    public String destinationAddress = "";     // 目的详细地址

    // 配送信息
    public Integer baseStores;            // 门店基数
    public Integer totalStoreTimes;       // 总店次数
    public Integer totalInOfficeTimes;    // 基数内总店次数
    public Integer totalOutOfficeTimes;   // 基数外总店次数
    public BigDecimal baseKilometer;      // 基础公里数
    public BigDecimal totalKilometer;     // 总里程数
    public BigDecimal overMileage;        // 超公里数
    public Integer baseStoreStatus;       // 是否基数外门店
    public Integer baseMileageStatus;     // 是否超基数公里
    public BigDecimal distanceWarehouseMileage; // 距离仓库公里数
    public Integer loadingPointsNumber;   // 装货点个数
    public Integer unloadingPointsNumber; // 卸货点个数

    // 车辆信息
    public String driver;                 // 司机
    public String carNumber;              // 车牌号
    public String carType;                // 车型
    public String carModel;               // 车长

    // 基础字段
    public String remark;                 // 备注
    public Integer optMonth;              // 月分区
    public Integer optDay;                // 日分区

}
