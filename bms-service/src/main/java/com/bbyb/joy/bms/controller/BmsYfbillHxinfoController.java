package com.bbyb.joy.bms.controller;

import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.dto.BmsPaymentRecordDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYfBatchhxDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYfbillHxDetailDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYfbillHxDto;
import com.bbyb.joy.bms.domain.dto.querybean.BmsYfbillHxQueryBean;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.service.IBmsYfbillHxinfoService;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应收账单核销Controller
 */
@Api(tags = "应付账单核销接口")
@RestController
@RequestMapping("/system/yfhxinfo")
public class BmsYfbillHxinfoController {

    @Resource
    private IBmsYfbillHxinfoService bmsYfbillHxinfoService;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private UserRights userRights;

    /**
     * 应收账单核销分页查询
     */
    @ApiOperation(value = "应收账单核销分页查询", response = BmsYfbillHxDto.class)
    @PostMapping("/selectYfbillHx")
    public ResponseResult<PagerDataBean<BmsYfbillHxDto>> selectYfbillHx(@RequestBody(required = false) BmsYfbillHxQueryBean bean) {
        String token = RequestContext.getToken();

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CLIENT.getCode(), "carrierIds", PermissionConfig.FieldType.STRING, "id"),
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.STRING, "id")
        );
        bean = userRights.applyPermissions(bean, permissionConfigs);

        return new ResponseResult<>(bmsYfbillHxinfoService.selectYsbillHx(token, bean));
    }

    /**
     * 收款单信息查询
     */
    @ApiOperation(value = "收款单信息查询", response = BmsPaymentRecordDto.class)
    @PostMapping("/selectPaymentRecord")
    public ResponseResult<PagerDataBean<BmsPaymentRecordDto>> selectPaymentRecord(@RequestBody(required = false) BmsPaymentRecordDto dto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillHxinfoService.selectPaymentRecord(token, dto.getCarrierName(), dto.getCompanyIds()));
    }

    /**
     * 根据账单编号查询核销明细
     */
    @ApiOperation(value = "根据账单编号查询核销明细", response = BmsYfbillHxDetailDto.class)
    @PostMapping("/hxDetailList")
    public ResponseResult<PagerDataBean<BmsYfbillHxDetailDto>> hxDetailList(@RequestBody(required = false) BmsYfbillHxDetailDto dto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillHxinfoService.hxDetailList(token, dto.getBillCode()));
    }

    /**
     * 批量核销
     */
    @ApiOperation(value = "批量核销", response = BmsYfBatchhxDto.class)
    @PostMapping("/batchhx")
    @MenuAuthority(code = "应付账单核销-批量核销保存")
    public ResponseResult<String> batchhx(@RequestBody(required = false) BmsYfBatchhxDto bmsYfBatchhxDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillHxinfoService.batchhx(token, bmsYfBatchhxDto));
    }

    /**
     * 取消核销
     */
    @ApiOperation(value = "取消核销", response = BmsYfBatchhxDto.class)
    @PostMapping("/batchCancel")
    @MenuAuthority(code = "应付账单核销-取消核销")
    public ResponseResult<String> batchCancel(@RequestBody(required = false) BmsYfBatchhxDto bmsYfBatchhxDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfbillHxinfoService.batchCancel(token, bmsYfBatchhxDto));
    }
}
