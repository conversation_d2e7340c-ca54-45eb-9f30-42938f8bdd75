package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.biz.YfBillExportBiz;
import com.bbyb.joy.bms.domain.dto.BmsClaimsInfo;
import com.bbyb.joy.bms.domain.dto.BmsYsbillcodeinfo;
import com.bbyb.joy.bms.domain.dto.FileBaseDto;
import com.bbyb.joy.bms.domain.dto.SysDept;
import com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain;
import com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYfbillmainExportAll;
import com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsBillExportInfo;
import com.bbyb.joy.bms.domain.dto.bmsysbillexport.BmsYsbillmainExportAll;
import com.bbyb.joy.bms.domain.dto.bmsysbillexport.OtherFeeInfo;
import com.bbyb.joy.bms.domain.enums.*;
import com.bbyb.joy.bms.service.IBmsClaimsInfoService;
import com.bbyb.joy.bms.service.IBmsYfbillmainService;
import com.bbyb.joy.bms.service.IPubFileExportService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import com.bbyb.joy.bms.support.exception.BusinessException;
import com.bbyb.joy.bms.support.utils.ExcelExportUtil;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.enums.PubNumEnum;
import com.bbyb.joy.mdm.dto.DicDto;
import com.bbyb.mdm.model.po.PubIdreplacePO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "应付账单导出")
@RestController
@RequestMapping("/system/yfbillExport")
public class YfbillMainExportController {

    @Resource
    private YfBillExportBiz yfBillExportMapper;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private IPubFileExportService pubFileExportService;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    private IBmsClaimsInfoService bmsClaimsInfoService;
    @Resource
    private IBmsYfbillmainService bmsYfbillmainService;

    /**
     * 第一个sheet 账单汇总
     *
     * @param hssfWorkbook           hssf工作簿
     * @param bmsYfbillmainExportAll bms yfbillmain导出全部
     * @param dicMap                 dicMap
     * @param id                     id
     * @param sons                   子账单集合主键
     * @return {@link SXSSFWorkbook}
     */
    public SXSSFWorkbook billSummary(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, Long id, List<Long> sons) {
        String tenantid= RequestContext.getTenantId();
        // 第一个sheet
        Sheet sheet1 = hssfWorkbook.createSheet("账单汇总");

        //表头样式
        XSSFCellStyle headerStyle = ExcelExportUtil.getDefaultTitleStyle(hssfWorkbook);
        // 背景色的设定
        headerStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.DARK_TEAL.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);

        final int[] rowNum = {0};

        //设置列宽
        for (int i = 0; i < 9; i++) {
            sheet1.setColumnWidth(i, 3200);
        }

        //第一行
        Row r0 = sheet1.createRow(rowNum[0]++);
        r0.setHeight((short) 800);
        Cell c00 = r0.createCell(0);
        String billName = bmsYfbillmainExportAll.getBillName() == null ? "" : bmsYfbillmainExportAll.getBillName();
        if (bmsYfbillmainExportAll.getBillTypeStr() != null && bmsYfbillmainExportAll.getBillTypeStr().equals(BmsConstants.YF_OPT_TYPE_05_STR)) {
            if (billName != null) {
                billName = billName + "应付增值对账单";
            }
        }
        c00.setCellValue(billName);
        c00.setCellStyle(headerStyle);
        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));
        BigDecimal sumAmount = new BigDecimal(0);
        //第二行
        Row r1 = sheet1.createRow(rowNum[0]++);
        r1.setHeight((short) 500);
        // 指定年月
        String dateStr = bmsYfbillmainExportAll.getBillDate().replace("年", "");
        LocalDate date = LocalDate.parse(dateStr + "01", DateTimeFormatter.BASIC_ISO_DATE);
        // 指定年月的第一天
        LocalDate dateFirst = date.with(TemporalAdjusters.firstDayOfMonth());
        // 指定年月的最后一天
        LocalDate dateEnd = date.with(TemporalAdjusters.lastDayOfMonth());
        String billDateStr = dateFirst.toString().replaceFirst("-", "年").replaceFirst("-", "月") + "日至" + dateEnd.toString().replaceFirst("-", "年").replaceFirst("-", "月") + "日";

        String[] rowFirst = {"承运商名称", "", bmsYfbillmainExportAll.getCarrierName(), "费用结算期", billDateStr, "", "", "", ""};
        for (int i = 0; i < rowFirst.length; i++) {
            Cell tempCell = r1.createCell(i);
            tempCell.setCellValue(rowFirst[i]);
            if (PubNumEnum.zero.getIntValue().equals(i) || PubNumEnum.three.getIntValue().equals(i)) {
                tempCell.setCellStyle(commonStyle2);
            } else {
                tempCell.setCellStyle(commonStyle);
            }

        }
        String merginBill = "0";
        if (bmsYfbillmainExportAll.getIdStr().split(",").length > 1) {
            //id有多个则是合并账单
            merginBill = "1";
        }
        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(1, 1, 0, 1));
        sheet1.addMergedRegion(new CellRangeAddress(1, 1, 4, 8));
        String[] otherFeeNames = {"客诉理赔费", "otherCost1", "otherCost2", "otherCost3", "otherCost4", "otherCost5", "otherCost6", "otherCost7", "otherCost8", "otherCost9", "otherCost10", "otherCost11", "otherCost12"};
        //仓储服务部分 如果仓储合计费为0 ，那么就隐藏仓储费
        if ((bmsYfbillmainExportAll.getExpensesType() != null && bmsYfbillmainExportAll.getExpensesType().equals(OperTypeEnum.OUT_FEE.getIntValue())) || (bmsYfbillmainExportAll.getCcFeeSum() != null && bmsYfbillmainExportAll.getCcFeeSum().compareTo(BigDecimal.ZERO) > 0)) {
            //增值单只有增值费
            if ((!bmsYfbillmainExportAll.getBillType().equals(5))) {
                Row r2 = sheet1.createRow(rowNum[0]++);
                r2.setHeight((short) 500);
                String[] rowSecond = {"仓储服务部分", "", "服务项目", "类型", "金额", "", "", "", ""};
                for (int i = 0; i < rowSecond.length; i++) {
                    Cell tempCell = r2.createCell(i);
                    tempCell.setCellValue(rowSecond[i]);
                    if (i == 0) {
                        tempCell.setCellStyle(commonStyle2);
                    } else {
                        tempCell.setCellStyle(commonStyle);
                    }

                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(2, 2, 4, 8));

                //第四行
                Row r3 = sheet1.createRow(rowNum[0]++);
                r3.setHeight((short) 500);
                String[] row3 = {"", "", "管理处置费", "常温", bmsYfbillmainExportAll.getGlFeeCwAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getGlFeeCwAmount()), "", "", "", ""};
                for (int i = 0; i < row3.length; i++) {
                    Cell tempCell = r3.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row3[i]));
                    } else {
                        tempCell.setCellValue(row3[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(3, 3, 4, 8));

                //第5行
                Row r5 = sheet1.createRow(rowNum[0]++);
                r5.setHeight((short) 500);
                String[] row5 = {"", "", "", "冷藏", bmsYfbillmainExportAll.getGlFeeLcAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getGlFeeLcAmount()), "", "", "", ""};
                for (int i = 0; i < row5.length; i++) {
                    Cell tempCell = r5.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row5[i]));
                    } else {
                        tempCell.setCellValue(row5[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(4, 4, 4, 8));

                //第6行
                Row r6 = sheet1.createRow(rowNum[0]++);
                r6.setHeight((short) 500);
                String[] row6 = {"", "", "", "冷冻", bmsYfbillmainExportAll.getGlFeeLdAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getGlFeeLdAmount()), "", "", "", ""};
                for (int i = 0; i < row6.length; i++) {
                    Cell tempCell = r6.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row6[i]));
                    } else {
                        tempCell.setCellValue(row6[i]);
                    }

                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(5, 5, 4, 8));
                sheet1.addMergedRegion(new CellRangeAddress(3, 5, 2, 2));

                //第7行
                Row r7 = sheet1.createRow(rowNum[0]++);
                r7.setHeight((short) 500);
                String[] row7 = {"", "", "存储服务费", "常温", bmsYfbillmainExportAll.getCcFeeCwAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getCcFeeCwAmount()), "", "", "", ""};
                for (int i = 0; i < row7.length; i++) {
                    Cell tempCell = r7.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row7[i]));
                    } else {
                        tempCell.setCellValue(row7[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }

                sheet1.addMergedRegion(new CellRangeAddress(6, 6, 4, 8));

                //第8行
                Row r8 = sheet1.createRow(rowNum[0]++);
                r8.setHeight((short) 500);
                String[] row8 = {"", "", "", "冷藏", bmsYfbillmainExportAll.getCcFeeLcAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getCcFeeLcAmount()), "", "", "", ""};
                for (int i = 0; i < row8.length; i++) {
                    Cell tempCell = r8.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row8[i]));
                    } else {
                        tempCell.setCellValue(row8[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(7, 7, 4, 8));

                //第9行
                Row r9 = sheet1.createRow(rowNum[0]++);
                r9.setHeight((short) 500);
                String[] row9 = {"", "", "", "冷冻", bmsYfbillmainExportAll.getCcFeeLdAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getCcFeeLdAmount()), "", "", "", ""};
                for (int i = 0; i < row9.length; i++) {
                    Cell tempCell = r9.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row9[i]));
                    } else {
                        tempCell.setCellValue(row9[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格

                sheet1.addMergedRegion(new CellRangeAddress(8, 8, 4, 8));
                sheet1.addMergedRegion(new CellRangeAddress(6, 8, 2, 2));
                //sheet1.addMergedRegion(new CellRangeAddress(6, 8, 3, 3));

                //第9行
                Row r10 = sheet1.createRow(rowNum[0]++);
                r10.setHeight((short) 500);
                String[] row10 = {"", "", "分拣操作费", "入库操作", "金额", "", "", "", ""};
                for (int i = 0; i < row10.length; i++) {
                    Cell tempCell = r10.createCell(i);
                    tempCell.setCellValue(row10[i]);
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(9, 9, 4, 8));

                //第10行
                Row r11 = sheet1.createRow(rowNum[0]++);
                r11.setHeight((short) 500);
                String[] row11 = {"", "", "", "常温", bmsYfbillmainExportAll.getFjFeeCwRkAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getFjFeeCwRkAmount()), "", "", "", ""};
                for (int i = 0; i < row11.length; i++) {
                    Cell tempCell = r11.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row11[i]));
                    } else {
                        tempCell.setCellValue(row11[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(10, 10, 4, 8));

                //第11行
                Row r12 = sheet1.createRow(rowNum[0]++);
                r12.setHeight((short) 500);
                String[] row12 = {"", "", "", "冷藏", bmsYfbillmainExportAll.getFjFeeLcRkAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getFjFeeLcRkAmount()), "", "", "", ""};
                for (int i = 0; i < row12.length; i++) {
                    Cell tempCell = r12.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row12[i]));
                    } else {
                        tempCell.setCellValue(row12[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(11, 11, 4, 8));

                //第12行
                Row r13 = sheet1.createRow(rowNum[0]++);
                r13.setHeight((short) 500);
                String[] row13 = {"", "", "", "冷冻", bmsYfbillmainExportAll.getFjFeeLdRkAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getFjFeeLdRkAmount()), "", "", "", ""};
                for (int i = 0; i < row13.length; i++) {
                    Cell tempCell = r13.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row13[i]));
                    } else {
                        tempCell.setCellValue(row13[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(12, 12, 4, 8));

                //第13行
                Row r14 = sheet1.createRow(rowNum[0]++);
                r14.setHeight((short) 500);
                String[] row14 = {"", "", "", "出库操作", "金额", "", "", "", ""};
                for (int i = 0; i < row14.length; i++) {
                    Cell tempCell = r14.createCell(i);
                    tempCell.setCellValue(row14[i]);
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(13, 13, 4, 8));

                //第14行
                Row r15 = sheet1.createRow(rowNum[0]++);
                r15.setHeight((short) 500);
                String[] row15 = {"", "", "", "常温", bmsYfbillmainExportAll.getFjFeeCwCkAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getFjFeeCwCkAmount()), "", "", "", ""};
                for (int i = 0; i < row15.length; i++) {
                    Cell tempCell = r15.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row15[i]));
                    } else {
                        tempCell.setCellValue(row15[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(14, 14, 4, 8));

                //第15行
                Row r16 = sheet1.createRow(rowNum[0]++);
                r16.setHeight((short) 500);
                String[] row16 = {"", "", "", "冷藏", bmsYfbillmainExportAll.getFjFeeLcCkAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getFjFeeLcCkAmount()), "", "", "", ""};
                for (int i = 0; i < row16.length; i++) {
                    Cell tempCell = r16.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row16[i]));
                    } else {
                        tempCell.setCellValue(row16[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(15, 15, 4, 8));

                //第16行
                Row r17 = sheet1.createRow(rowNum[0]++);
                r17.setHeight((short) 500);
                String[] row17 = {"", "", "", "冷冻", bmsYfbillmainExportAll.getFjFeeLdCkAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getFjFeeLdCkAmount()), "", "", "", ""};
                for (int i = 0; i < row17.length; i++) {
                    Cell tempCell = r17.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row17[i]));
                    } else {
                        tempCell.setCellValue(row17[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(16, 16, 4, 8));
                sheet1.addMergedRegion(new CellRangeAddress(9, 16, 2, 2));

                //第17行
                Row r18 = sheet1.createRow(rowNum[0]++);
                r18.setHeight((short) 500);
                String[] row18 = {"", "", "入库装卸服务费", "重货", bmsYfbillmainExportAll.getFjFeeRkZhAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getFjFeeRkZhAmount().add(bmsYfbillmainExportAll.getCcShortbargeFee())), "", "", "", ""};
                for (int i = 0; i < row18.length; i++) {
                    Cell tempCell = r18.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row18[i]));
                    } else {
                        tempCell.setCellValue(row18[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(17, 17, 4, 8));

                //第18行
                Row r19 = sheet1.createRow(rowNum[0]++);
                r19.setHeight((short) 500);
                String[] row19 = {"", "", "", "泡货", bmsYfbillmainExportAll.getFjFeeRkPhAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getFjFeeRkPhAmount()), "", "", "", ""};
                for (int i = 0; i < row19.length; i++) {
                    Cell tempCell = r19.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row19[i]));
                    } else {
                        tempCell.setCellValue(row19[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(18, 18, 4, 8));
                sheet1.addMergedRegion(new CellRangeAddress(17, 18, 2, 2));

                //第19行
                Row r20 = sheet1.createRow(rowNum[0]++);
                r20.setHeight((short) 500);
                String[] row20 = {"", "", "出库装卸服务费", "重货", bmsYfbillmainExportAll.getFjFeeCkZhAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getFjFeeCkZhAmount()), "", "", "", ""};
                for (int i = 0; i < row20.length; i++) {
                    Cell tempCell = r20.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row20[i]));
                    } else {
                        tempCell.setCellValue(row20[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(19, 19, 4, 8));
                //第20行
                Row r21 = sheet1.createRow(rowNum[0]++);
                r21.setHeight((short) 500);
                String[] row21 = {"", "", "", "泡货", bmsYfbillmainExportAll.getFjFeeCkPhAmount() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getFjFeeCkPhAmount()), "", "", "", ""};
                for (int i = 0; i < row21.length; i++) {
                    Cell tempCell = r21.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row21[i]));
                    } else {
                        tempCell.setCellValue(row21[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(20, 20, 4, 8));
                sheet1.addMergedRegion(new CellRangeAddress(19, 20, 2, 2));
                sheet1.addMergedRegion(new CellRangeAddress(2, 21, 0, 1));
                //第19行
                Row r20z = sheet1.createRow(rowNum[0]++);
                r20z.setHeight((short) 500);
                String[] row20z = {"", "", "制单费", "", bmsYfbillmainExportAll.getCcReturnFee() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getCcReturnFee()), "", "", "", ""};
                for (int i = 0; i < row20z.length; i++) {
                    Cell tempCell = r20z.createCell(i);
                    if (PubNumEnum.four.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row20z[i]));
                    } else {
                        tempCell.setCellValue(row20z[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(21, 21, 4, 8));
                //其他费用
                int orderOtherFeeStart = rowNum[0];
                Row r44 = sheet1.createRow(rowNum[0]++);
                r44.setHeight((short) 500);
                String[] row44 = {"其他款项明细", "", "新增/扣款事件明细", "金额", "", "", "", "", ""};
                for (int i = 0; i < row44.length; i++) {
                    Cell tempCell = r44.createCell(i);
                    tempCell.setCellValue(row44[i]);
                    if (i == 0) {
                        tempCell.setCellStyle(commonStyle2);
                    } else {
                        tempCell.setCellStyle(commonStyle);
                    }
                }
                //合并单元格:其他款项明细所在的单元格
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 3, 8));
                List<OtherFeeInfo> orderOtherFeeInfoList = new ArrayList<>();
                for (String name : otherFeeNames) {
                    OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
                    otherFeeInfo.setOtherName(name);
                    if (StrUtil.isNotEmpty(dicMap.get("yf_storage_othercost" + name))) {
                        otherFeeInfo.setOtherName(dicMap.get("yf_storage_othercost" + name));
                    }
                    switch (name) {
                        case "客诉理赔费":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcExceptionFee());
                            break;
                        case "otherCost1":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost1());
                            break;
                        case "otherCost2":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost2());
                            break;
                        case "otherCost3":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost3());
                            break;
                        case "otherCost4":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost4());
                            break;
                        case "otherCost5":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost5());
                            break;
                        case "otherCost6":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost6());
                            break;
                        case "otherCost7":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost7());
                            break;
                        case "otherCost8":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost8());
                            break;
                        case "otherCost9":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost9());
                            break;
                        case "otherCost10":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost10());
                            break;
                        case "otherCost11":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost11());
                            break;
                        case "otherCost12":
                            otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost12());
                            break;
                        default:
                            otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
                    }
                    orderOtherFeeInfoList.add(otherFeeInfo);
                }
                for (OtherFeeInfo otherFeeInfo : orderOtherFeeInfoList) {
                    Row rn = sheet1.createRow(rowNum[0]++);
                    rn.setHeight((short) 500);
                    String[] rowRns = {"", "", otherFeeInfo.getOtherName(), String.valueOf(otherFeeInfo.getOtherFee()), "", "", "", "", ""};
                    for (int i = 0; i < rowRns.length; i++) {
                        Cell tempCell = rn.createCell(i);
                        if (PubNumEnum.three.getIntValue().equals(i)) {
                            tempCell.setCellValue(Double.parseDouble(rowRns[i]));
                        } else {
                            tempCell.setCellValue(rowRns[i]);
                        }
                        tempCell.setCellStyle(commonStyle);
                    }
                    //合并单元格
                    sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 3, 8));
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(orderOtherFeeStart, rowNum[0] - 1, 0, 1));
            }
            //增值单只有增值费
            if ((!bmsYfbillmainExportAll.getBillTypeStr().equals(BmsConstants.YF_OPT_TYPE_05_STR))) {
                // 第35行
                Row r36 = sheet1.createRow(rowNum[0]++);
                r36.setHeight((short) 500);
                String[] row36 = {"仓储费用合计:", "", "", bmsYfbillmainExportAll.getCcFeeSum() == null ? "0" : String.valueOf(
                        bmsYfbillmainExportAll.getCcFeeSum()), "", "", "", "", ""};
                for (int i = 0; i < row36.length; i++) {
                    Cell tempCell = r36.createCell(i);
                    if (PubNumEnum.three.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row36[i]));
                    } else {
                        tempCell.setCellValue(row36[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 0, 2));
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 3, 8));
            }
        }
        //运输服务部分 当运输费合计为0时，隐藏运输费用明细
        if ((bmsYfbillmainExportAll.getExpensesType() != null && bmsYfbillmainExportAll.getExpensesType().equals(OperTypeEnum.ORDER_FEE.getIntValue())) || (bmsYfbillmainExportAll.getCcFeeSum() != null && bmsYfbillmainExportAll.getPsFeeSum().compareTo(BigDecimal.ZERO) > 0)) {
            // 第36行
            int psRow = rowNum[0]++;
            Row r37 = sheet1.createRow(psRow);
            r37.setHeight((short) 800);
            String[] row37 = {"配送服务部分", "", "当前账单覆盖门店数", "配送店次", "总件数", "总箱数", "总重量", "总体积", "总货值"};
            for (int i = 0; i < row37.length; i++) {
                Cell tempCell = r37.createCell(i);
                tempCell.setCellValue(row37[i]);
                tempCell.setCellStyle(commonStyle2);
            }
            // 配送店次
            List<BmsYsbillcodeinfo> routeinfoList = yfBillExportMapper.getGroupDistributionNumByBillId(tenantid, id);
            //第N行
            for (BmsYsbillcodeinfo bmsYsbillcodeinfo : routeinfoList) {
                // 当前账单覆盖门店数
                String storeNum = bmsYsbillcodeinfo.getStoreNum();
                // 配送店次
                String distributionStoreNum = bmsYsbillcodeinfo.getDistributionStoreNum();
                // 总件数
                String sumGoods = bmsYsbillcodeinfo.getTotalNumber().toString();
                //总箱数
                String sumBox = bmsYsbillcodeinfo.getTotalBoxes().toString();
                // 总重量
                String sumWeight = bmsYsbillcodeinfo.getTotalWeight().toString();
                // 总体积
                String sumVolume = bmsYsbillcodeinfo.getTotalVolume().toString();
                // 总货值
                String totalValue = bmsYsbillcodeinfo.getCargoValue().toString();

                //数据
                Row rn = sheet1.createRow(rowNum[0]++);
                rn.setHeight((short) 700);
                String[] rowRns = {"", "", storeNum, distributionStoreNum, sumGoods, sumBox, sumWeight, sumVolume, totalValue};
                for (int j = 0; j < rowRns.length; j++) {
                    Cell tempCell = rn.createCell(j);
                    if (PubNumEnum.two.getIntValue().equals(j)) {
                        tempCell.setCellValue(rowRns[j]);
                    } else {
                        tempCell.setCellValue(Double.parseDouble(StrUtil.isEmpty(rowRns[j]) ? "0" : rowRns[j]));
                    }
                    tempCell.setCellStyle(commonStyle);
                }
            }

            Row r37n1 = sheet1.createRow(rowNum[0]++);
            r37n1.setHeight((short) 800);
            String[] row37n1 = {"", "", "费用项", "金额", "", "", "", "", ""};
            for (int i = 0; i < row37n1.length; i++) {
                Cell tempCell = r37n1.createCell(i);
                tempCell.setCellValue(row37n1[i]);
                tempCell.setCellStyle(commonStyle2);
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 3, 8));

            String[] ccFeeNames = {"运费", "提货费", "送货费", "超筐费", "加点费", "减点费", "短驳费", "退货费", "超远费"};

            List<OtherFeeInfo> ccFeeInfoList = new ArrayList<>();
            for (String name : ccFeeNames) {
                OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
                otherFeeInfo.setOtherName(name);
                switch (name) {
                    case "运费":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsFreight());
                        break;
                    case "提货费":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsDeliveryFee());
                        break;
                    case "送货费":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOutboundsortingFee());
                        break;
                    case "超筐费":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsSuperframesFee());
                        break;
                    case "加点费":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsExcessFee());
                        break;
                    case "减点费":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsReduceFee());
                        break;
                    case "短驳费":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsShortbargeFee());
                        break;
                    case "退货费":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsReturnFee());
                        break;
                    case "超远费":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsUltrafarFee());
                        break;
                    default:
                        otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
                }
                ccFeeInfoList.add(otherFeeInfo);
            }

            for (OtherFeeInfo ccFeeInfo : ccFeeInfoList) {
                Row r37n2 = sheet1.createRow(rowNum[0]++);
                r37n2.setHeight((short) 500);
                String[] row37n2 = {"", "", ccFeeInfo.getOtherName(), String.valueOf(ccFeeInfo.getOtherFee()), "", "", "", "", ""};
                for (int i = 0; i < row37n2.length; i++) {
                    Cell tempCell = r37n2.createCell(i);
                    if (PubNumEnum.three.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(row37n2[i]));
                    } else {
                        tempCell.setCellValue(row37n2[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 3, 8));
            }

            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(psRow, rowNum[0] - 1, 0, 1));


            //第N行
            int orderOtherFeeStart = rowNum[0];
            Row r44 = sheet1.createRow(rowNum[0]++);
            r44.setHeight((short) 500);
            String[] row44 = {"其他款项明细", "", "新增/扣款事件明细", "新增或扣款金额", "", "", "", "", ""};
            for (int i = 0; i < row44.length; i++) {
                Cell tempCell = r44.createCell(i);
                tempCell.setCellValue(row44[i]);
                if (i == 0) {
                    tempCell.setCellStyle(commonStyle2);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }

            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 3, 8));

            List<OtherFeeInfo> orderOtherFeeInfoList = new ArrayList<>();
            for (String name : otherFeeNames) {
                OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
                otherFeeInfo.setOtherName(name);
                if (StrUtil.isNotEmpty(dicMap.get("yf_transport_othercost" + name))) {
                    otherFeeInfo.setOtherName(dicMap.get("yf_transport_othercost" + name));
                }
                switch (name) {
                    case "客诉理赔费":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsExceptionFee());
                        break;
                    case "otherCost1":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOtherCost1());
                        break;
                    case "otherCost2":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOtherCost2());
                        break;
                    case "otherCost3":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOtherCost3());
                        break;
                    case "otherCost4":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOtherCost4());
                        break;
                    case "otherCost5":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOtherCost5());
                        break;
                    case "otherCost6":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOtherCost6());
                        break;
                    case "otherCost7":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOtherCost7());
                        break;
                    case "otherCost8":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOtherCost8());
                        break;
                    case "otherCost9":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOtherCost9());
                        break;
                    case "otherCost10":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOtherCost10());
                        break;
                    case "otherCost11":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOtherCost11());
                        break;
                    case "otherCost12":
                        otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getPsOtherCost12());
                        break;
                    default:
                        otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
                }
                orderOtherFeeInfoList.add(otherFeeInfo);
            }

            for (OtherFeeInfo otherFeeInfo : orderOtherFeeInfoList) {
                Row rn = sheet1.createRow(rowNum[0]++);
                rn.setHeight((short) 500);
                String[] rowRns = {"", "", otherFeeInfo.getOtherName(), String.valueOf(otherFeeInfo.getOtherFee()), "", "", "", "", ""};
                for (int i = 0; i < rowRns.length; i++) {
                    Cell tempCell = rn.createCell(i);
                    if (PubNumEnum.three.getIntValue().equals(i)) {
                        tempCell.setCellValue(Double.parseDouble(rowRns[i]));
                    } else {
                        tempCell.setCellValue(rowRns[i]);
                    }
                    tempCell.setCellStyle(commonStyle);
                }
                //合并单元格
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 3, 8));
            }

            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(orderOtherFeeStart, rowNum[0] - 1, 0, 1));

            // 第N行
            Row r58 = sheet1.createRow(rowNum[0]++);
            r58.setHeight((short) 500);
            String[] row58 = {"配送费用合计:", "", "", String.valueOf(bmsYfbillmainExportAll.getPsFeeSum()), "", "", "", "", ""};
            for (int i = 0; i < row58.length; i++) {
                Cell tempCell = r58.createCell(i);
                tempCell.setCellValue(row58[i]);
                tempCell.setCellStyle(commonStyle);
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 0, 2));
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 3, 8));

        }
        //客诉理赔(仓储理赔+运输理赔) 合并账单显示
        //if((!bmsYfbillmainExportAll.getBillTypeStr().equals(BmsConstants.YF_OPT_TYPE_05_STR))&& BmsConstants.MERGE_STATUS_1.equals(merginBill)){
        //    //如果是合并账单，则显示汇总异常理赔费用
        //    //理赔金额
        //    Row r29 = sheet1.createRow(rowNum[0]++);
        //    r29.setHeight((short) 500);
        //    String[] row29 = {"客诉理赔", "", "客诉理赔费", String.valueOf(bmsYfbillmainExportAll.getCcExceptionFee().add(bmsYfbillmainExportAll.getPsExceptionFee())), "", "", "", "", ""};
        //    for (int i = 0; i < row29.length; i++) {
        //        Cell tempCell = r29.createCell(i);
        //        tempCell.setCellValue(row29[i]);
        //        if(i==0)
        //        {
        //            tempCell.setCellStyle(commonStyle2);
        //        }else
        //        {
        //            tempCell.setCellStyle(commonStyle);
        //        }
        //    }
        //    //合并单元格
        //    sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 0, 1));
        //    sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 3, 8));
        //
        //}
        //增值费（仓储增值费部分）
        List<BmsYsBillExportInfo> adAmont = yfBillExportMapper.getValueAddedFeeByBillIdGroupBy(tenantid, sons);
        int yfZzrow = 0;
        if (CollUtil.isNotEmpty(adAmont)) {
            int biegin = rowNum[0];
            yfZzrow = rowNum[0];
            int zzrow = rowNum[0];
            //合并单元格
            Map<Integer, List<BmsYsBillExportInfo>> collect = adAmont.stream().collect(Collectors.groupingBy(BmsYsBillExportInfo::getFeeBelong));
            if (CollUtil.isNotEmpty(collect.get(1))) {
                Row r59 = sheet1.createRow(rowNum[0]++);
                zzrow = rowNum[0];
                yfZzrow = yfZzrow == 0 ? rowNum[0] : yfZzrow;
                r59.setHeight((short) 500);
                String[] row59 = {"其他款项明细", "", "增值服务费部分_运输", "费用项", "金额", "", "", "", ""};
                for (int i = 0; i < row59.length; i++) {
                    Cell tempCell = r59.createCell(i);
                    tempCell.setCellValue(row59[i]);
                    tempCell.setCellStyle(i == 0 ? commonStyle2 : commonStyle);
                }
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 4, 8));
                for (BmsYsBillExportInfo bmsYsBillExportInfo : collect.get(1)) {
                    Row r59zz = sheet1.createRow(rowNum[0]++);
                    sumAmount = sumAmount.add(bmsYsBillExportInfo.getAmount());
                    r59zz.setHeight((short) 500);
                    String[] row59zz = {"", "", "", bmsYsBillExportInfo.getItemName(), bmsYsBillExportInfo.getAmount().toString(), "", "", "", ""};
                    for (int i = 0; i < row59zz.length; i++) {
                        Cell tempCell = r59zz.createCell(i);
                        if (PubNumEnum.five.getIntValue().equals(i)) {
                            if (StrUtil.isEmpty(row59zz[i]) || new BigDecimal(row59zz[i]).compareTo(BigDecimal.ZERO) == 0) {
                                tempCell.setCellValue("");
                                tempCell.setCellStyle(commonStyle);
                            } else {
                                tempCell.setCellValue(new BigDecimal(row59zz[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                                tempCell.setCellStyle(commonStyle);
                            }
                        } else {
                            tempCell.setCellValue(row59zz[i]);
                            tempCell.setCellStyle(commonStyle);
                        }
                    }
                    //合并单元格
                    sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 4, 8));
                }
                sheet1.addMergedRegion(new CellRangeAddress(zzrow - 1, rowNum[0] - 1, 2, 2));
            }

            if (CollUtil.isNotEmpty(collect.get(2))) {
                Row r58z = sheet1.createRow(rowNum[0]++);
                zzrow = rowNum[0];
                yfZzrow = yfZzrow == 0 ? rowNum[0] : yfZzrow;
                String[] row59z = {"其他款项明细", "", "增值服务费部分_仓储", "费用项", "金额", "", "", "", ""};
                for (int i = 0; i < row59z.length; i++) {
                    Cell tempCell = r58z.createCell(i);
                    tempCell.setCellValue(row59z[i]);
                    tempCell.setCellStyle(i == 0 ? commonStyle2 : commonStyle);
                }
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 4, 8));
                for (BmsYsBillExportInfo bmsYsBillExportInfo : collect.get(2)) {
                    Row r59zz = sheet1.createRow(rowNum[0]++);
                    sumAmount = sumAmount.add(bmsYsBillExportInfo.getAmount());
                    r59zz.setHeight((short) 500);
                    String[] row59zz = {"", "", "", bmsYsBillExportInfo.getItemName(), bmsYsBillExportInfo.getAmount().toString(), "", "", "", ""};
                    for (int i = 0; i < row59zz.length; i++) {
                        Cell tempCell = r59zz.createCell(i);
                        if (PubNumEnum.five.getIntValue().equals(i)) {
                            if (StrUtil.isEmpty(row59zz[i]) || new BigDecimal(row59zz[i]).compareTo(BigDecimal.ZERO) == 0) {
                                tempCell.setCellValue("");
                                tempCell.setCellStyle(commonStyle);
                            } else {
                                tempCell.setCellValue(new BigDecimal(row59zz[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                                tempCell.setCellStyle(commonStyle);
                            }
                        } else {
                            tempCell.setCellValue(row59zz[i]);
                            tempCell.setCellStyle(commonStyle);
                        }
                    }
                    //合并单元格
                    //sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 2, 3));
                    sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 4, 8));
                }
                sheet1.addMergedRegion(new CellRangeAddress(zzrow - 1, rowNum[0] - 1, 2, 2));
            }
            sheet1.addMergedRegion(new CellRangeAddress(biegin, rowNum[0] - 1, 0, 1));
        }
        // 查询固定费用
        List<BmsYsBillExportInfo> gdAmont = yfBillExportMapper.getFixedExpensesByBillIdGroupBy(tenantid, sons);
        int gdzzrow = 0;
        // 固定费用明细
        if (CollUtil.isNotEmpty(gdAmont)) {
            Row r59 = sheet1.createRow(rowNum[0]++);
            gdzzrow = rowNum[0];
            r59.setHeight((short) 500);
            String[] row59 = {"固定费用明细", "", "费用项", "金额", "", "", "", "", "",};
            for (int i = 0; i < row59.length; i++) {
                Cell tempCell = r59.createCell(i);
                tempCell.setCellValue(row59[i]);
                tempCell.setCellStyle(i == 0 ? commonStyle2 : commonStyle);
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 3, 8));
            for (BmsYsBillExportInfo bmsYsBillExportInfo : gdAmont) {
                sumAmount = sumAmount.add(bmsYsBillExportInfo.getAmount());
                Row r59gd = sheet1.createRow(rowNum[0]++);
                r59gd.setHeight((short) 500);
                String[] row59gd = {"", "", bmsYsBillExportInfo.getItemName(), bmsYsBillExportInfo.getAmount().toString(), "", "", "", "", ""};
                for (int i = 0; i < row59gd.length; i++) {
                    Cell tempCell = r59gd.createCell(i);
                    if (PubNumEnum.five.getIntValue().equals(i)) {
                        if (StrUtil.isEmpty(row59gd[i]) || new BigDecimal(row59gd[i]).compareTo(BigDecimal.ZERO) == 0) {
                            tempCell.setCellValue("");
                            tempCell.setCellStyle(commonStyle);
                        } else {
                            tempCell.setCellValue(new BigDecimal(row59gd[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                            commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                            tempCell.setCellStyle(commonStyle);
                        }
                    } else {
                        tempCell.setCellValue(row59gd[i]);
                        tempCell.setCellStyle(commonStyle);
                    }
                }
                //合并单元格
                //sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 2, 3));
                sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 3, 8));
            }
            sheet1.addMergedRegion(new CellRangeAddress(gdzzrow - 1, rowNum[0] - 1, 0, 1));

        }
        // 费用合计
        Row r58 = sheet1.createRow(rowNum[0]++);
        r58.setHeight((short) 500);
        String[] row58 = {"费用合计:", "", "", String.valueOf(sumAmount.add(bmsYfbillmainExportAll.getPsFeeSum()).add(bmsYfbillmainExportAll.getCcFeeSum())), "", "", "", "", ""};
        for (int i = 0; i < row58.length; i++) {
            Cell tempCell = r58.createCell(i);
            if (PubNumEnum.four.getIntValue().equals(i)) {
                if (StrUtil.isEmpty(row58[i]) || new BigDecimal(row58[i]).compareTo(BigDecimal.ZERO) == 0) {
                    tempCell.setCellValue("");
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellValue(new BigDecimal(row58[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle);
                }
            } else {
                tempCell.setCellValue(row58[i]);
                tempCell.setCellStyle(commonStyle);
            }
        }
        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 0, 2));
        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0] - 1, rowNum[0] - 1, 3, 8));
/*
        // 第N行
        int ticketStratrowNum = rowNum[0];
        Row r59 = sheet1.createRow(rowNum[0]++);
        r59.setHeight((short) 500);
        String[] row59 = {"开票", "", "开票项目", "开票比例", "", "", "开票金额", "", ""};
        for (int i = 0; i < row59.length; i++) {
            Cell tempCell = r59.createCell(i);
            tempCell.setCellValue(row59[i]);
            tempCell.setCellStyle(commonStyle2);
        }
        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 3, 5));
        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 6, 8));

        String[] ticketFeeNames = {"仓储费","运输费","信息费","平台费","品牌费","派送服务费" };

        BmsYsBillExportInfo tick=yfBillExportMapper.getBillingInformation(id);
        List<OtherFeeInfo>  ticketFeeInfoList = new ArrayList<>();
        BigDecimal sumAmt= new BigDecimal("0");
        for(String name : ticketFeeNames)
        {
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            if("运输费".equals(name))
            {
                otherFeeInfo.setOther1("9%");
                otherFeeInfo.setOtherFee(tick.getTicketAmount().multiply(new BigDecimal(0.09)).setScale(2,BigDecimal.ROUND_HALF_UP));
                sumAmt=sumAmt.add(tick.getTicketAmount().multiply(new BigDecimal(0.09)).setScale(2,BigDecimal.ROUND_HALF_UP));
            }else{
                otherFeeInfo.setOther1("6%");
                otherFeeInfo.setOtherFee(tick.getTicketAmount().multiply(new BigDecimal(0.06)).setScale(2,BigDecimal.ROUND_HALF_UP));
                sumAmt=sumAmt.add(tick.getTicketAmount().multiply(new BigDecimal(0.06)).setScale(2,BigDecimal.ROUND_HALF_UP));
            }
            ticketFeeInfoList.add(otherFeeInfo);
        }

        for(OtherFeeInfo ticketFeeInfo : ticketFeeInfoList){
            Row r37n2 = sheet1.createRow(rowNum[0]++);
            r37n2.setHeight((short) 500);
            String[] row37n2 = {"", "", ticketFeeInfo.getOtherName(),ticketFeeInfo.getOther1(), "", "", String.valueOf(ticketFeeInfo.getOtherFee()), "", ""};
            for (int i = 0; i < row37n2.length; i++) {
                Cell tempCell = r37n2.createCell(i);
                if(PubNumEnum.six.getIntValue().equals(i)){
                    tempCell.setCellValue(Double.parseDouble(row37n2[i]));
                }else{
                    tempCell.setCellValue(row37n2[i]);
                }
                tempCell.setCellStyle(commonStyle);
            }
            //合并单元格
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 3, 5));
            sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 6, 8));
        }

        sheet1.addMergedRegion(new CellRangeAddress(ticketStratrowNum, rowNum[0]-1, 0, 1));

        // 第N行
        Row r62 = sheet1.createRow(rowNum[0]++);
        r62.setHeight((short) 500);
        String[] row62 = {"总费用合计（元）:", "", "", "开票金额:"+tick.getTicketAmount()+"，比例计算汇总："+sumAmt, "", "", "", "", ""};
        for (int i = 0; i < row62.length; i++) {
            Cell tempCell = r62.createCell(i);
            tempCell.setCellValue(row62[i]);
            if(i==0)
            {
                tempCell.setCellStyle(commonStyle2);
            }else
            {
                tempCell.setCellStyle(commonStyle);
            }
        }
        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 0, 2));
        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 3, 8));

        // 第N行
        Row r63 = sheet1.createRow(rowNum[0]++);
        r63.setHeight((short) 500);
        String[] row63 = {"审批部门", "", "签批意见", "", "", "", "", "", ""};
        for (int i = 0; i < row63.length; i++) {
            Cell tempCell = r63.createCell(i);
            tempCell.setCellValue(row63[i]);
            tempCell.setCellStyle(commonStyle2);
        }
        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 0, 1));
        sheet1.addMergedRegion(new CellRangeAddress(rowNum[0]-1, rowNum[0]-1, 2, 8));

        // 客户确认
        int auditRowNum = rowNum[0];
        Row r64 = sheet1.createRow(rowNum[0]++);
        r64.setHeight((short) 500);
        String[] row64 = {"客户确认", "", "", "", "", "", "", "", ""};
        for (int i = 0; i < row64.length; i++) {
            Cell tempCell = r64.createCell(i);
            tempCell.setCellValue(row64[i]);
            if(i==0)
            {
                tempCell.setCellStyle(commonStyle2);
            }else
            {
                tempCell.setCellStyle(commonStyle);
            }
        }
        Row r65 = sheet1.createRow(rowNum[0]++);
        r65.setHeight((short) 500);
        String[] row65 = {"", "", "", "", "", "", "", "", ""};
        for (int i = 0; i < row65.length; i++) {
            Cell tempCell = r65.createCell(i);
            tempCell.setCellValue(row65[i]);
            tempCell.setCellStyle(commonStyle);
        }
        Row r66 = sheet1.createRow(rowNum[0]++);
        r66.setHeight((short) 500);
        String[] row66 = {"", "", "", "", "", "", "", "", ""};
        for (int i = 0; i < row66.length; i++) {
            Cell tempCell = r66.createCell(i);
            tempCell.setCellValue(row66[i]);
            tempCell.setCellStyle(commonStyle);
        }
        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(auditRowNum, rowNum[0]-1, 0, 1));
        sheet1.addMergedRegion(new CellRangeAddress(auditRowNum, rowNum[0]-1, 2, 8));*/
        return hssfWorkbook;
    }

    /**
     * 第二个sheet 存储费主信息
     *
     * @param hssfWorkbook           列
     * @param bmsYfbillmainExportAll 数据
     * @param dicMap                 字典
     */
    public SXSSFWorkbook storageSummary(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid= RequestContext.getTenantId();
        // 第2个sheet
        Sheet sheet2 = hssfWorkbook.createSheet("存储费主信息");
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        // 背景色的设定
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 创建数据行，获取入库明细
        List<BmsYsBillExportInfo> stkList = yfBillExportMapper.getStorageByBillId(tenantid, ids);
        final int[] rowNum2 = {0};
        BigDecimal[] amtf = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        Boolean[] fare = new Boolean[22];
        // 判断是否要隐藏列
        for (BmsYsBillExportInfo bmsYsBillExportInfo : stkList) {
            amtf[0] = amtf[0].add(bmsYsBillExportInfo.getFreight());
            amtf[1] = amtf[1].add(bmsYsBillExportInfo.getUltrafarFee());
            amtf[2] = amtf[2].add(bmsYsBillExportInfo.getSuperframesFee());
            amtf[3] = amtf[3].add(bmsYsBillExportInfo.getExcessFee());
            amtf[4] = amtf[4].add(bmsYsBillExportInfo.getDeliveryFee());
            amtf[5] = amtf[5].add(bmsYsBillExportInfo.getReturnFee());
            amtf[6] = amtf[6].add(bmsYsBillExportInfo.getShortbargeFee());
            amtf[7] = amtf[7].add(bmsYsBillExportInfo.getExceptionFee());
            amtf[8] = amtf[8].add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()));
            amtf[9] = amtf[9].add(bmsYsBillExportInfo.getOtherCost1());
            amtf[10] = amtf[10].add(bmsYsBillExportInfo.getOtherCost2());
            amtf[11] = amtf[11].add(bmsYsBillExportInfo.getOtherCost3());
            amtf[12] = amtf[12].add(bmsYsBillExportInfo.getOtherCost4());
            amtf[13] = amtf[13].add(bmsYsBillExportInfo.getOtherCost5());
            amtf[14] = amtf[14].add(bmsYsBillExportInfo.getOtherCost6());
            amtf[15] = amtf[15].add(bmsYsBillExportInfo.getOtherCost7());
            amtf[16] = amtf[16].add(bmsYsBillExportInfo.getOtherCost8());
            amtf[17] = amtf[17].add(bmsYsBillExportInfo.getOtherCost9());
            amtf[18] = amtf[18].add(bmsYsBillExportInfo.getOtherCost10());
            amtf[19] = amtf[19].add(bmsYsBillExportInfo.getOtherCost11());
            amtf[20] = amtf[20].add(bmsYsBillExportInfo.getOtherCost12());
            amtf[21] = amtf[21].add(BigDecimal.valueOf(bmsYsBillExportInfo.getSumAmt()));
        }
        int fi = 0;
        for (BigDecimal bigDecimal : amtf) {
            fare[fi] = bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0;
            fi++;
        }
        //设置列宽
        for (int i = 0; i < fare.length; i++) {
            if (fare[i] != null && fare[i]) {
                sheet2.setColumnWidth(i + 12, 0);
            }
        }
        //第1行
        Row s2r2 = sheet2.createRow(rowNum2[0]++);
        s2r2.setHeight((short) 500);
        String[] s2row2 = {"日期", "库存单号", "温区", "仓库编码", "仓库名称",
                "日结存库存数", "日结存托数", "托规", "总箱数", "总件数", "总体积",
                "总重量",
                "存储费", "管理处置费", "整箱分拣费", "拆零分拣费", "装卸费", "制单费", "操作费", "客诉理赔费", "调账费", "otherCost1", "otherCost2", "otherCost3",
                "otherCost4", "otherCost5", "otherCost6", "otherCost7", "otherCost8",
                "otherCost9", "otherCost10", "otherCost11", "otherCost12", "总费用"};
        for (int i = 0; i < s2row2.length; i++) {
            String name = s2row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            if (StrUtil.isNotEmpty(dicMap.get("yf_storage_othercost" + name))) {
                otherFeeInfo.setOtherName(dicMap.get("yf_storage_othercost" + name));
            }
            switch (name) {
                case "客诉理赔费":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcExceptionFee());
                    break;
                case "otherCost1":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost1());
                    break;
                case "otherCost2":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost2());
                    break;
                case "otherCost3":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost3());
                    break;
                case "otherCost4":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost4());
                    break;
                case "otherCost5":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost5());
                    break;
                case "otherCost6":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost6());
                    break;
                case "otherCost7":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost7());
                    break;
                case "otherCost8":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost8());
                    break;
                case "otherCost9":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost9());
                    break;
                case "otherCost10":
                    otherFeeInfo.setOtherFee(new BigDecimal(67835));
                    break;
                case "otherCost11":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost11());
                    break;
                case "otherCost12":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost12());
                    break;
                default:
                    otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
            }
            s2row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s2row2.length; i++) {
            Cell tempCell = s2r2.createCell(i);
            tempCell.setCellValue(s2row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        BigDecimal sumAmt = new BigDecimal("0");
        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd");
        BigDecimal[] amt = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        for (int s = 0; s < stkList.size(); s++) {
            BmsYsBillExportInfo bmsYsBillExportInfo = stkList.get(s);
            Row s2r3 = sheet2.createRow(rowNum2[0]++);
            boolean f = s <= 0 || s >= stkList.size() || !stkList.get(s).getId().equals(stkList.get(s - 1).getId());
            // 赋0
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s2row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(bmsYsBillExportInfo.getInstorageTime() != null ? sml.format(bmsYsBillExportInfo.getInstorageTime()) : "");
                        break;
                    case 1:
                        tempCell.setCellValue(bmsYsBillExportInfo.getStockCode());
                        break;
                    case 2:
                        tempCell.setCellValue(bmsYsBillExportInfo.getTemperatureName());
                        break;
                    case 3:
                        tempCell.setCellValue(bmsYsBillExportInfo.getWarehouseCode());
                        break;
                    case 4:
                        tempCell.setCellValue(bmsYsBillExportInfo.getWarehouseName());
                        break;
                    case 5:
                        tempCell.setCellValue(bmsYsBillExportInfo.getAqty() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getAqty().toString()));
                        break;
                    case 6:
                        tempCell.setCellValue(bmsYsBillExportInfo.getPalletNumber() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getPalletNumber().toString()));
                        break;
                    case 7:
                        tempCell.setCellValue(bmsYsBillExportInfo.getPalletRuler() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getPalletRuler().toString()));
                        break;
                    case 8:
                        tempCell.setCellValue(bmsYsBillExportInfo.getTotalBoxes() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getTotalBoxes().toString()));
                        break;
                    case 9:
                        tempCell.setCellValue(bmsYsBillExportInfo.getTotalNumber() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getTotalNumber().toString()));
                        break;
                    case 10:
                        tempCell.setCellValue(bmsYsBillExportInfo.getTotalVolume() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getTotalVolume().toString()));
                        break;
                    case 11:
                        tempCell.setCellValue(bmsYsBillExportInfo.getTotalWeight() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getTotalWeight().toString()));
                        break;
                    case 12:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getFreight() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getFreight().toString()));
                        break;
                    case 13:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getUltrafarFee() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getUltrafarFee().toString()));
                        break;
                    case 14:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getSuperframesFee() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getSuperframesFee().toString()));
                        break;
                    case 15:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getExcessFee() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getExcessFee().toString()));
                        break;
                    case 16:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getDeliveryFee() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getDeliveryFee().toString()));
                        break;
                    case 17:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getReturnFee() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getReturnFee().toString()));
                        break;
                    case 18:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getShortbargeFee() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getShortbargeFee().toString()));
                        break;
                    case 19:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getExceptionFee() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getExceptionFee().toString()));
                        break;
                    case 20:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getAdjustFee() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getAdjustFee().toString()));
                        break;
                    case 21:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost1() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getOtherCost1().toString()));
                        break;
                    case 22:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost2() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getOtherCost2().toString()));
                        break;
                    case 23:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost3() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getOtherCost3().toString()));
                        break;
                    case 24:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost4() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getOtherCost4().toString()));
                        break;
                    case 25:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost5() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getOtherCost5().toString()));
                        break;
                    case 26:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost6() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getOtherCost6().toString()));
                        break;
                    case 27:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost7() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getOtherCost7().toString()));
                        break;
                    case 28:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost8() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getOtherCost8().toString()));
                        break;
                    case 29:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost9() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getOtherCost9().toString()));
                        break;
                    case 30:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost10() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getOtherCost10().toString()));
                        break;
                    case 31:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost11() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getOtherCost11().toString()));
                        break;
                    case 32:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getOtherCost12() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getOtherCost12().toString()));
                        break;
                    case 33:
                        tempCell.setCellValue(!f ? 0 : bmsYsBillExportInfo.getSumAmt() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getSumAmt().toString()));
                        break;
//                    case 34:
//                        tempCell.setCellValue(bmsYsBillExportInfo.getFeeTypeFirstName());
//                        break;
//                    case 35:
//                        tempCell.setCellValue(bmsYsBillExportInfo.getIsIncrement() == 0 ? "否" : "是");
//                        break;
//                    case 36:
//                        tempCell.setCellValue(bmsYsBillExportInfo.getTotalQuantity() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getTotalQuantity().toString()));
//                        break;
//                    case 37:
//                        tempCell.setCellValue(bmsYsBillExportInfo.getPrice() == null ? 0 : Double.parseDouble(bmsYsBillExportInfo.getPrice().toString()));
//                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }
            if (f) {
                amt[0] = amt[0].add(bmsYsBillExportInfo.getTotalBoxes());
                amt[1] = amt[1].add(bmsYsBillExportInfo.getTotalNumber());
                amt[2] = amt[2].add(bmsYsBillExportInfo.getTotalWeight());
                amt[5] = amt[5].add(bmsYsBillExportInfo.getTotalVolume());
                amt[3] = amt[3].add(bmsYsBillExportInfo.getExcessFee());
                amt[4] = amt[4].add(bmsYsBillExportInfo.getExceptionFee());
                amt[6] = amt[6].add(bmsYsBillExportInfo.getFreight());
                amt[7] = amt[7].add(bmsYsBillExportInfo.getUltrafarFee());
                amt[25] = amt[25].add(bmsYsBillExportInfo.getSuperframesFee());
                amt[26] = amt[26].add(bmsYsBillExportInfo.getDeliveryFee());
                amt[27] = amt[27].add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()));
                amt[23] = amt[23].add(bmsYsBillExportInfo.getShortbargeFee());
                amt[24] = amt[24].add(bmsYsBillExportInfo.getReturnFee());
                amt[8] = amt[8].add(bmsYsBillExportInfo.getOtherCost1());
                amt[9] = amt[9].add(bmsYsBillExportInfo.getOtherCost2());
                amt[10] = amt[10].add(bmsYsBillExportInfo.getOtherCost3());
                amt[11] = amt[11].add(bmsYsBillExportInfo.getOtherCost4());
                amt[12] = amt[12].add(bmsYsBillExportInfo.getOtherCost5());
                amt[13] = amt[13].add(bmsYsBillExportInfo.getOtherCost6());
                amt[14] = amt[14].add(bmsYsBillExportInfo.getOtherCost7());
                amt[15] = amt[15].add(bmsYsBillExportInfo.getOtherCost8());
                amt[16] = amt[16].add(bmsYsBillExportInfo.getOtherCost9());
                amt[17] = amt[17].add(bmsYsBillExportInfo.getOtherCost10());
                amt[18] = amt[18].add(bmsYsBillExportInfo.getOtherCost11());
                amt[19] = amt[19].add(bmsYsBillExportInfo.getOtherCost12());
                amt[20] = amt[20].add(BigDecimal.valueOf(bmsYsBillExportInfo.getBasicsSum()));
                amt[21] = amt[21].add(BigDecimal.valueOf(bmsYsBillExportInfo.getOtherSum()));
                amt[22] = amt[22].add(BigDecimal.valueOf(bmsYsBillExportInfo.getSumAmt()));
                sumAmt = sumAmt.add(bmsYsBillExportInfo.getFreight())
                        .add(bmsYsBillExportInfo.getExcessFee())
                        .add(bmsYsBillExportInfo.getExceptionFee())
                        .add(bmsYsBillExportInfo.getSuperframesFee())
                        .add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()))
                        .add(bmsYsBillExportInfo.getUltrafarFee())
                        .add(bmsYsBillExportInfo.getDeliveryFee())
                        .add(bmsYsBillExportInfo.getOtherCost1())
                        .add(bmsYsBillExportInfo.getOtherCost2())
                        .add(bmsYsBillExportInfo.getOtherCost3())
                        .add(bmsYsBillExportInfo.getOtherCost4())
                        .add(bmsYsBillExportInfo.getOtherCost5())
                        .add(bmsYsBillExportInfo.getOtherCost6())
                        .add(bmsYsBillExportInfo.getOtherCost7())
                        .add(bmsYsBillExportInfo.getOtherCost8())
                        .add(bmsYsBillExportInfo.getOtherCost9())
                        .add(bmsYsBillExportInfo.getOtherCost10())
                        .add(bmsYsBillExportInfo.getOtherCost11())
                        .add(bmsYsBillExportInfo.getOtherCost12())
                        .add(bmsYsBillExportInfo.getShortbargeFee())
                        .add(bmsYsBillExportInfo.getReturnFee())
                ;
            }


        }
        //汇总每行的金额
        String[] s2row3 = {"合计：", "", "", "", "",
                "", "", "", amt[0].toString(), amt[1].toString(), amt[5].toString(), amt[2].toString(),
                amt[6].toString(), amt[7].toString(), amt[25].toString(), amt[3].toString(),
                amt[26].toString(), amt[24].toString(), amt[23].toString(), amt[4].toString(),
                amt[27].toString(),
                amt[8].toString(), amt[9].toString(), amt[10].toString(), amt[11].toString(),
                amt[12].toString(), amt[13].toString(), amt[14].toString(), amt[15].toString(),
                amt[16].toString(), amt[17].toString(), amt[18].toString(), amt[19].toString(), sumAmt.toString()
        };
        Row s2r3 = sheet2.createRow(rowNum2[0]++);
        for (int i = 8; i < s2row3.length; i++) {
            Cell tempCell = s2r3.createCell(i);
            if (i < s2row3.length - 4) {
                tempCell.setCellValue(StrUtil.isNotEmpty(s2row3[i]) ? Double.parseDouble(s2row3[i]) : 0);
            } else {
                tempCell.setCellValue(s2row3[i]);
            }
            tempCell.setCellStyle(commonStyle);
        }
        sheet2.addMergedRegion(new CellRangeAddress(rowNum2[0] - 1, rowNum2[0] - 1, 0, 7));


        //最后一行 合计金额
        Row s2r4 = sheet2.createRow(rowNum2[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"总费用合计（元）:", "", "", "", "",
                "", "", "", "", "", "",
                "", sumAmt.toString(), "", "", "", "",
                "", "", "", "", "",
                "", "", "", "", "", "", "", "", "", "", "", ""};
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if (i == 12) {
                tempCell.setCellValue(Double.parseDouble(s2row4[i]));
            } else {
                tempCell.setCellValue(s2row4[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        sheet2.addMergedRegion(new CellRangeAddress(rowNum2[0] - 1, rowNum2[0] - 1, 0, 11));

        sheet2.addMergedRegion(new CellRangeAddress(rowNum2[0] - 1, rowNum2[0] - 1, 12, 33));


        return hssfWorkbook;
    }

    /**
     * 第三个sheet 存储服务费明细信息
     *
     * @param hssfWorkbook           excel
     * @param bmsYfbillmainExportAll 导出数据
     * @param dicMap                 字典
     */

    public SXSSFWorkbook storageDetailSummary(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, Long id) {
        String tenantid= RequestContext.getTenantId();
        // 第3个sheet
        Sheet sheet2 = hssfWorkbook.createSheet("存储服务费明细信息");
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        // 内容样式
        XSSFCellStyle sheet2ContentStyle = ExcelExportUtil.getSheet2ContentStyle(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        // 背景色的设定
        sheet2HeaderStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.SEA_GREEN.getIndex());
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum2 = {0};

        //设置列宽
        for (int i = 0; i < 17; i++) {
            sheet2.setColumnWidth(i, 2700);
        }

        //第1行
        Row s2r2 = sheet2.createRow(rowNum2[0]++);
        s2r2.setHeight((short) 500);
        String[] s2row2 = {"商家基础资料", "", "", "", "", "", "", "", "", "", "", "", "托盘数核算", "", "", "", ""};
        for (int i = 0; i < s2row2.length; i++) {
            Cell tempCell = s2r2.createCell(i);
            tempCell.setCellValue(s2row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        //合并单元格
        sheet2.addMergedRegion(new CellRangeAddress(rowNum2[0] - 1, rowNum2[0] - 1, 0, 11));
        sheet2.addMergedRegion(new CellRangeAddress(rowNum2[0] - 1, rowNum2[0] - 1, 12, 16));

        //第2行
        Row s2r3 = sheet2.createRow(rowNum2[0]++);
        s2r3.setHeight((short) 500);
        String[] s2row3 = {"库存日期", "温层", "商家", "商品ID/SKU", "商品名称",
                "生产日期", "库存件数", "托规", "三级箱规(件/箱)",
                "箱长(cm)", "箱宽(cm)", "箱高(cm)",
                "计算托盘数", "整数部分", "尾数≥0.5取整", "尾数<0.5合并", "合计托盘数", "基础费合计", "其他费合计", "总费用"};
        for (int i = 0; i < s2row3.length; i++) {
            Cell tempCell = s2r3.createCell(i);
            tempCell.setCellValue(s2row3[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        // 数据行
        // 求和
        //整数
        BigDecimal zs = new BigDecimal("0");
        //尾数取整
        BigDecimal wsq = new BigDecimal("0");
        //尾数合并
        BigDecimal wsh = new BigDecimal("0");
        // 合计托盘
        BigDecimal qh = new BigDecimal("0");
        // 获取存储服务费明细信息
        List<BmsYsBillExportInfo> list = yfBillExportMapper.getStorageGoodsDetailByBillId(tenantid, id);
        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd");
        for (BmsYsBillExportInfo info : list) {
            Row s2r4 = sheet2.createRow(rowNum2[0]++);
            s2r4.setHeight((short) 500);
            for (int i = 0; i < s2row3.length; i++) {
                Cell tempCell = s2r4.createCell(i);
                tempCell.setCellStyle(sheet2HeaderStyle);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getInstorageTime() != null ? sml.format(info.getInstorageTime()) : "");
                        break;
                    case 1:
                        tempCell.setCellValue(info.getTemperatureName());
                        break;
                    case 2:
                        tempCell.setCellValue(info.getClientName());
                        break;
                    case 3:
                        tempCell.setCellValue(info.getSkuCode());
                        break;
                    case 4:
                        tempCell.setCellValue(info.getSkuName());
                        break;
                    case 5:
                        tempCell.setCellValue(info.getProductDate() != null ? sml.format(info.getProductDate()) : "");
                        break;
                    case 6:
                        tempCell.setCellValue(info.getStockQuantity() == null ? 0 : Double.parseDouble(info.getStockQuantity().toString()));
                        break;
                    case 7:
                        tempCell.setCellValue(info.getPalletType() == null ? 0 : Double.parseDouble(info.getPalletType().toString()));
                        break;
                    case 8:
                        tempCell.setCellValue(info.getBoxType());
                        break;
                    case 9:
                        tempCell.setCellValue(info.getBoxLength() == null ? 0 : Double.parseDouble(info.getBoxLength().toString()));
                        break;
                    case 10:
                        tempCell.setCellValue(info.getWeightWidth() == null ? 0 : Double.parseDouble(info.getWeightWidth().toString()));
                        break;
                    case 11:
                        tempCell.setCellValue(info.getWeightHeight() == null ? 0 : Double.parseDouble(info.getWeightHeight().toString()));
                        break;
                    case 12:
                        tempCell.setCellValue(info.getCalculatePallet() == null ? 0 : Double.parseDouble(info.getCalculatePallet().toString()));
                        break;
                    case 13:
                        tempCell.setCellValue(info.getIntegerNumber() == null ? 0 : Double.parseDouble(info.getIntegerNumber().toString()));
                        break;
                    case 14:
                        tempCell.setCellValue(info.getGreaterThan5() == null ? 0 : Double.parseDouble(info.getGreaterThan5().toString()));
                        break;
                    case 15:
                        tempCell.setCellValue(info.getLessThan5() == null ? 0 : Double.parseDouble(info.getLessThan5().toString()));
                        break;
                    case 16:
                        tempCell.setCellValue(info.getTotalPallet() == null ? 0 : Double.parseDouble(info.getTotalPallet().toString()));
                        break;
                    case 17:
                        tempCell.setCellValue(info.getBasicsSum() == null ? 0 : Double.parseDouble(info.getBasicsSum().toString()));
                        break;
                    case 18:
                        tempCell.setCellValue(info.getOtherSum() == null ? 0 : Double.parseDouble(info.getOtherSum().toString()));
                        break;
                    case 19:
                        tempCell.setCellValue(info.getSumAmt() == null ? 0 : Double.parseDouble(info.getSumAmt().toString()));
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }
            zs = zs.add(info.getIntegerNumber());
            wsq = wsq.add(info.getGreaterThan5());
            wsh = wsh.add(info.getLessThan5());
            qh = qh.add(info.getTotalPallet());
        }

        //最后一行 合计数量
        Row s2r4 = sheet2.createRow(rowNum2[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"合计：", "", "", "", "",
                "", "", "", "",
                "", "", "",
                "", "", "", "", "", "", "", ""};
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            switch (i) {
                case 13:
                    tempCell.setCellValue(Double.parseDouble(zs.toString()));
                    break;
                case 14:
                    tempCell.setCellValue(Double.parseDouble(wsq.toString()));
                    break;
                case 15:
                    tempCell.setCellValue(Double.parseDouble(wsh.toString()));
                    break;
                case 16:
                    tempCell.setCellValue(Double.parseDouble(qh.toString()));
                    break;
                default:
            }
            if (i < 13) {
                tempCell.setCellValue(s2row4[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        sheet2.addMergedRegion(new CellRangeAddress(rowNum2[0] - 1, rowNum2[0] - 1, 0, 5));

        return hssfWorkbook;
    }

    /**
     * 第四个sheet 入库明细
     *
     * @param hssfWorkbook           excel
     * @param bmsYfbillmainExportAll 导出数据
     * @param dicMap                 字典
     */

    public SXSSFWorkbook inWarehousDetailSummary(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid= RequestContext.getTenantId();
        // 第4个sheet
        Sheet sheet3 = hssfWorkbook.createSheet("入库费用明细");
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum3 = {0};

        List<BmsYsBillExportInfo> inWarList = yfBillExportMapper.getStorageinWarehouseGoodsDetailByBillId(tenantid, ids);

        BigDecimal[] amtf = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        Boolean[] fare = new Boolean[22];
        // 判断是否要隐藏列
        for (BmsYsBillExportInfo bmsYsBillExportInfo : inWarList) {
            amtf[0] = amtf[0].add(bmsYsBillExportInfo.getFreight());
            amtf[1] = amtf[1].add(bmsYsBillExportInfo.getUltrafarFee());
            amtf[2] = amtf[2].add(bmsYsBillExportInfo.getSuperframesFee());
            amtf[3] = amtf[3].add(bmsYsBillExportInfo.getExcessFee());
            amtf[4] = amtf[4].add(bmsYsBillExportInfo.getDeliveryFee());
            amtf[5] = amtf[5].add(bmsYsBillExportInfo.getReturnFee());
            amtf[6] = amtf[6].add(bmsYsBillExportInfo.getShortbargeFee());
            amtf[7] = amtf[7].add(bmsYsBillExportInfo.getExceptionFee());
            amtf[8] = amtf[8].add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()));
            amtf[9] = amtf[9].add(bmsYsBillExportInfo.getOtherCost1());
            amtf[10] = amtf[10].add(bmsYsBillExportInfo.getOtherCost2());
            amtf[11] = amtf[11].add(bmsYsBillExportInfo.getOtherCost3());
            amtf[12] = amtf[12].add(bmsYsBillExportInfo.getOtherCost4());
            amtf[13] = amtf[13].add(bmsYsBillExportInfo.getOtherCost5());
            amtf[14] = amtf[14].add(bmsYsBillExportInfo.getOtherCost6());
            amtf[15] = amtf[15].add(bmsYsBillExportInfo.getOtherCost7());
            amtf[16] = amtf[16].add(bmsYsBillExportInfo.getOtherCost8());
            amtf[17] = amtf[17].add(bmsYsBillExportInfo.getOtherCost9());
            amtf[18] = amtf[18].add(bmsYsBillExportInfo.getOtherCost10());
            amtf[19] = amtf[19].add(bmsYsBillExportInfo.getOtherCost11());
            amtf[20] = amtf[20].add(bmsYsBillExportInfo.getOtherCost12());
            amtf[21] = amtf[21].add(BigDecimal.valueOf(bmsYsBillExportInfo.getSumAmt()));
        }
        int fi = 0;
        for (BigDecimal bigDecimal : amtf) {
            fare[fi] = bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0;
            fi++;
        }
        //设置列宽
        for (int i = 0; i < fare.length; i++) {
            if (fare[i] != null && fare[i]) {
                sheet3.setColumnWidth(i + 17, 0);
            }
        }
        //第1行
        Row s3r2 = sheet3.createRow(rowNum3[0]++);
        s3r2.setHeight((short) 500);
        String[] s3row2 = {"入库时间", "入库单号", "物料编码", "商品名称",
                "温层", "商品规格", "单位", "规格型号", "入库件数", "总箱数", "总托数",
                "整箱数", "拆零件数", "单箱重量（kg）", "单箱体积（cm³）", "总重量(T)", "总体积(m³)"
                , "存储费", "管理处置费", "整箱分拣费", "拆零分拣费", "装卸费", "制单费", "操作费", "客诉理赔费", "调账费",
                "otherCost1", "otherCost2", "otherCost3", "otherCost4",
                "otherCost5", "otherCost6", "otherCost7", "otherCost8",
                "otherCost9", "otherCost10", "otherCost11", "otherCost12", "总费用", "计费人姓名", "计费备注"};
        // 动态费用字段
        for (int i = 0; i < s3row2.length; i++) {
            Cell tempCell = s3r2.createCell(i);
            String name = s3row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            if (StrUtil.isNotEmpty(dicMap.get("yf_storage_othercost" + name))) {
                otherFeeInfo.setOtherName(dicMap.get("yf_storage_othercost" + name));
            }
            name = otherFeeInfo.getOtherName();
            tempCell.setCellValue(name);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        // 创建数据行 入库明细
        String lastId = "";
        int row = 1;
        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd");
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        int o = 0;
        for (int p = 0; p < inWarList.size(); p++) {
            BmsYsBillExportInfo info = inWarList.get(p);
            Row s2r3 = sheet3.createRow(rowNum3[0]++);
            s2r3.setHeight((short) 500);
            boolean f = p <= 0 || p >= inWarList.size() || !inWarList.get(p).getId().equals(inWarList.get(p - 1).getId());
            // 赋0
            for (int i = 0; i < s3row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getSigningDate() != null ? sml.format(info.getSigningDate()) : "");
                        break;
                    case 1:
                        tempCell.setCellValue(info.getRelateCode());
                        break;
                    case 2:
                        tempCell.setCellValue(info.getSkuCode());
                        break;
                    case 3:
                        tempCell.setCellValue(info.getSkuName());
                        break;
                    case 4:
                        tempCell.setCellValue(info.getTemperatureName());
                        break;
                    case 5:
                        tempCell.setCellValue(info.getSpecification());
                        break;
                    case 6:
                        tempCell.setCellValue(info.getUnit());
                        break;
                    case 7:
                        tempCell.setCellValue(info.getBoxType());
                        break;
                    case 8:
                        tempCell.setCellValue(info.getContentsNumber() == null ? 0 : Double.parseDouble(info.getContentsNumber().toString()));
                        break;
                    case 9:
                        tempCell.setCellValue(info.getTotalBoxes() == null ? 0 : Double.parseDouble(info.getTotalBoxes().toString()));
                        break;
                    case 10:
                        tempCell.setCellValue(!f ? 0 : info.getPalletNumber() == null ? 0 : Double.parseDouble(info.getPalletNumber().toString()));
                        break;
                    case 11:
                        tempCell.setCellValue(info.getTotalBoxes() == null ? 0 : Double.parseDouble(info.getTotalBoxes().toString()));
                        break;
                    case 12:
                        tempCell.setCellValue(info.getOddBoxes() == null ? 0 : Double.parseDouble(info.getOddBoxes().toString()));
                        break;
                    case 13:
                        tempCell.setCellValue(info.getWeight() == null ? 0 : Double.parseDouble(info.getWeight().toString()));
                        break;
                    case 14:
                        tempCell.setCellValue(info.getVolume() == null ? 0 : Double.parseDouble(info.getVolume().toString()));
                        break;
                    case 15:
                        tempCell.setCellValue(info.getTotalWeight() == null ? 0 : Double.parseDouble(info.getTotalWeight().toString()));
                        break;
                    case 16:
                        tempCell.setCellValue(info.getTotalVolume() == null ? 0 : Double.parseDouble(info.getTotalVolume().toString()));
                        break;
                    case 17:
                        tempCell.setCellValue(!f ? 0 : info.getFreight() == null ? 0 : Double.parseDouble(info.getFreight().toString()));
                        break;
                    case 18:
                        tempCell.setCellValue(!f ? 0 : info.getUltrafarFee() == null ? 0 : Double.parseDouble(info.getUltrafarFee().toString()));
                        break;
                    case 19:
                        tempCell.setCellValue(!f ? 0 : info.getSuperframesFee() == null ? 0 : Double.parseDouble(info.getSuperframesFee().toString()));
                        break;
                    case 20:
                        tempCell.setCellValue(!f ? 0 : info.getExcessFee() == null ? 0 : Double.parseDouble(info.getExcessFee().toString()));
                        break;
                    case 21:
                        tempCell.setCellValue(!f ? 0 : info.getDeliveryFee() == null ? 0 : Double.parseDouble(info.getDeliveryFee().toString()));
                        break;
                    case 22:
                        tempCell.setCellValue(!f ? 0 : info.getReturnFee() == null ? 0 : Double.parseDouble(info.getReturnFee().toString()));
                        break;
                    case 23:
                        tempCell.setCellValue(!f ? 0 : info.getShortbargeFee() == null ? 0 : Double.parseDouble(info.getShortbargeFee().toString()));
                        break;
                    case 24:
                        tempCell.setCellValue(!f ? 0 : info.getExceptionFee() == null ? 0 : Double.parseDouble(info.getExceptionFee().toString()));
                        break;
                    case 25:
                        tempCell.setCellValue(!f ? 0 : info.getAdjustFee() == null ? 0 : Double.parseDouble(info.getAdjustFee().toString()));
                        break;
                    case 26:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost1() == null ? 0 : Double.parseDouble(info.getOtherCost1().toString()));
                        break;
                    case 27:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost2() == null ? 0 : Double.parseDouble(info.getOtherCost2().toString()));
                        break;
                    case 28:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost3() == null ? 0 : Double.parseDouble(info.getOtherCost3().toString()));
                        break;
                    case 29:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost4() == null ? 0 : Double.parseDouble(info.getOtherCost4().toString()));
                        break;
                    case 30:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost5() == null ? 0 : Double.parseDouble(info.getOtherCost5().toString()));
                        break;
                    case 31:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost6() == null ? 0 : Double.parseDouble(info.getOtherCost6().toString()));
                        break;
                    case 32:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost7() == null ? 0 : Double.parseDouble(info.getOtherCost7().toString()));
                        break;
                    case 33:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost8() == null ? 0 : Double.parseDouble(info.getOtherCost8().toString()));
                        break;
                    case 34:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost9() == null ? 0 : Double.parseDouble(info.getOtherCost9().toString()));
                        break;
                    case 35:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost10() == null ? 0 : Double.parseDouble(info.getOtherCost10().toString()));
                        break;
                    case 36:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost11() == null ? 0 : Double.parseDouble(info.getOtherCost11().toString()));
                        break;
                    case 37:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost12() == null ? 0 : Double.parseDouble(info.getOtherCost12().toString()));
                        break;
                    case 38:
                        tempCell.setCellValue(!f ? 0 : info.getSumAmt() == null ? 0 : Double.parseDouble(info.getSumAmt().toString()));
                        break;
                    case 39:
                        tempCell.setCellValue(info.getOperBy());
                        break;
                    case 40:
                        tempCell.setCellValue(info.getRemarksJf());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }
            if ((p == inWarList.size() - 1 && row < rowNum3[0] - 1) || (row < rowNum3[0] - 1 && p + 1 < inWarList.size() && !lastId.equals(inWarList.get(p + 1).getId()))) {
                for (int j = 9; j <= 37; j++) {
                    if (j == 9 || j > 14) {
                        sheet3.addMergedRegion(new CellRangeAddress(row, rowNum3[0] - 1, j, j));
                    }
                }
                lastId = info.getId();
                row = rowNum3[0];
            } else {
                lastId = info.getId();

            }
            if ((p == inWarList.size() - 1 && row == rowNum3[0] - 1) || (row == rowNum3[0] - 1 && p + 1 < inWarList.size() && !lastId.equals(inWarList.get(p + 1).getId()))) {
                row = rowNum3[0];
            }
            sumAmt[0] = sumAmt[0].add(info.getContentsNumber() == null ? BigDecimal.ZERO : info.getContentsNumber());
            sumAmt[1] = sumAmt[1].add(info.getTotalWeight() == null ? new BigDecimal("0") : info.getTotalWeight());
            sumAmt[24] = sumAmt[24].add(info.getTotalBoxes());
            sumAmt[27] = sumAmt[27].add(info.getOddBoxes());
            sumAmt[28] = sumAmt[28].add(info.getWeight());
            sumAmt[29] = sumAmt[29].add(info.getVolume());
            sumAmt[26] = sumAmt[26].add(info.getTotalVolume());
            if (f) {
                sumAmt[25] = sumAmt[25].add(info.getPalletNumber());
                sumAmt[2] = sumAmt[2].add(info.getFreight());
                sumAmt[3] = sumAmt[3].add(info.getUltrafarFee());
                sumAmt[4] = sumAmt[4].add(info.getSuperframesFee());
                sumAmt[5] = sumAmt[5].add(info.getExcessFee());
                sumAmt[6] = sumAmt[6].add(info.getDeliveryFee());
                sumAmt[7] = sumAmt[7].add(info.getReturnFee());
                sumAmt[8] = sumAmt[8].add(info.getShortbargeFee());
                sumAmt[9] = sumAmt[9].add(info.getExceptionFee());
                sumAmt[10] = sumAmt[10].add(BigDecimal.valueOf(info.getAdjustFee()));
                sumAmt[11] = sumAmt[11].add(info.getOtherCost1());
                sumAmt[12] = sumAmt[12].add(info.getOtherCost2());
                sumAmt[13] = sumAmt[13].add(info.getOtherCost3());
                sumAmt[14] = sumAmt[14].add(info.getOtherCost4());
                sumAmt[15] = sumAmt[15].add(info.getOtherCost5());
                sumAmt[16] = sumAmt[16].add(info.getOtherCost6());
                sumAmt[17] = sumAmt[17].add(info.getOtherCost7());
                sumAmt[18] = sumAmt[18].add(info.getOtherCost8());
                sumAmt[19] = sumAmt[19].add(info.getOtherCost9());
                sumAmt[20] = sumAmt[20].add(info.getOtherCost10());
                sumAmt[21] = sumAmt[21].add(info.getOtherCost11());
                sumAmt[22] = sumAmt[22].add(info.getOtherCost12());
                sumAmt[23] = sumAmt[23].add(BigDecimal.valueOf(info.getSumAmt()));
            }

        }

        //最后一行 合计金额
        Row s2r4 = sheet3.createRow(rowNum3[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"合计：", "", "", "", "", "", "", "", sumAmt[0].toString(), sumAmt[24].toString(), sumAmt[25].toString(),
                sumAmt[24].toString(), sumAmt[27].toString(), sumAmt[28].toString(), sumAmt[29].toString(), sumAmt[1].toString(), sumAmt[26].toString(),
                sumAmt[2].toString(), sumAmt[3].toString(), sumAmt[4].toString(), sumAmt[5].toString(),
                sumAmt[6].toString(), sumAmt[7].toString(), sumAmt[8].toString(), sumAmt[9].toString(),
                sumAmt[10].toString(),
                sumAmt[11].toString(), sumAmt[12].toString(), sumAmt[13].toString(), sumAmt[14].toString(),
                sumAmt[15].toString(), sumAmt[16].toString(), sumAmt[17].toString(), sumAmt[18].toString(),
                sumAmt[19].toString(), sumAmt[20].toString(), sumAmt[21].toString(), sumAmt[22].toString(), sumAmt[23].toString(), "", ""
        };
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if (i > 7 && i < 38) {
                tempCell.setCellValue(Double.parseDouble(s2row4[i]));
            } else {
                tempCell.setCellValue(s2row4[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet3.addMergedRegion(new CellRangeAddress(rowNum3[0] - 1, rowNum3[0] - 1, 0, 7));

        return hssfWorkbook;
    }

    /**
     * 第五个sheet 出库明细
     */
    public SXSSFWorkbook outWarehousDetailSummary(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid= RequestContext.getTenantId();
        // 第5个sheet
        Sheet sheet4 = hssfWorkbook.createSheet("出库费用明细");
        // 背景色的设定
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum4 = {0};
        List<BmsYsBillExportInfo> stkList = yfBillExportMapper.getStorageOutWarehouseGoodsDetailByBillId(tenantid, ids);

        BigDecimal[] amtf = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        Boolean[] fare = new Boolean[22];
        // 判断是否要隐藏列
        for (BmsYsBillExportInfo bmsYsBillExportInfo : stkList) {
            amtf[0] = amtf[0].add(bmsYsBillExportInfo.getFreight());
            amtf[1] = amtf[1].add(bmsYsBillExportInfo.getUltrafarFee());
            amtf[2] = amtf[2].add(bmsYsBillExportInfo.getSuperframesFee());
            amtf[3] = amtf[3].add(bmsYsBillExportInfo.getExcessFee());
            amtf[4] = amtf[4].add(bmsYsBillExportInfo.getDeliveryFee());
            amtf[5] = amtf[5].add(bmsYsBillExportInfo.getReturnFee());
            amtf[6] = amtf[6].add(bmsYsBillExportInfo.getShortbargeFee());
            amtf[7] = amtf[7].add(bmsYsBillExportInfo.getExceptionFee());
            amtf[8] = amtf[8].add(BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()));
            amtf[9] = amtf[9].add(bmsYsBillExportInfo.getOtherCost1());
            amtf[10] = amtf[10].add(bmsYsBillExportInfo.getOtherCost2());
            amtf[11] = amtf[11].add(bmsYsBillExportInfo.getOtherCost3());
            amtf[12] = amtf[12].add(bmsYsBillExportInfo.getOtherCost4());
            amtf[13] = amtf[13].add(bmsYsBillExportInfo.getOtherCost5());
            amtf[14] = amtf[14].add(bmsYsBillExportInfo.getOtherCost6());
            amtf[15] = amtf[15].add(bmsYsBillExportInfo.getOtherCost7());
            amtf[16] = amtf[16].add(bmsYsBillExportInfo.getOtherCost8());
            amtf[17] = amtf[17].add(bmsYsBillExportInfo.getOtherCost9());
            amtf[18] = amtf[18].add(bmsYsBillExportInfo.getOtherCost10());
            amtf[19] = amtf[19].add(bmsYsBillExportInfo.getOtherCost11());
            amtf[20] = amtf[20].add(bmsYsBillExportInfo.getOtherCost12());
            amtf[21] = amtf[21].add(BigDecimal.valueOf(bmsYsBillExportInfo.getSumAmt()));
        }
        int fi = 0;
        for (BigDecimal bigDecimal : amtf) {
            fare[fi] = bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0;
            fi++;
        }
        //设置列宽
        for (int i = 0; i < fare.length; i++) {
            if (fare[i] != null && fare[i]) {
                sheet4.setColumnWidth(i + 17, 0);
            }
        }
        //第1行
        Row s4r2 = sheet4.createRow(rowNum4[0]++);
        s4r2.setHeight((short) 500);
        String[] s4row2 = {"出库时间", "出库单号", "物料编码", "商品名称", "温层", "商品规格", "单位", "规格型号", "出库件数",
                "整箱数", "拆零件数", "单箱重量（kg）", "单箱体积（m³）", "总重量（T）", "总体积", "整箱数",
                "出库门店", "存储费", "管理处置费", "整箱分拣费", "拆零分拣费", "装卸费", "制单费", "操作费", "客诉理赔费", "调账费"
                , "otherCost1", "otherCost2", "otherCost3", "otherCost4", "otherCost5", "otherCost6",
                "otherCost7", "otherCost8", "otherCost9", "otherCost10", "otherCost11", "otherCost12", "总费用", "计费人姓名", "计费备注"};
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };

        BigDecimal sumContentsNumber = BigDecimal.ZERO;
        BigDecimal sumFreight = BigDecimal.ZERO;
        BigDecimal sumUltrafarFee = BigDecimal.ZERO;
        BigDecimal sumSuperframesFee = BigDecimal.ZERO;
        BigDecimal sumExcessFee = BigDecimal.ZERO;
        BigDecimal sumDeliveryFee = BigDecimal.ZERO;
        BigDecimal sumReturnFee = BigDecimal.ZERO;
        BigDecimal sumShortbargeFee = BigDecimal.ZERO;
        BigDecimal sumExceptionFee = BigDecimal.ZERO;
        BigDecimal sumAdjustFee = BigDecimal.ZERO;
        BigDecimal sumOtherCost1 = BigDecimal.ZERO;
        BigDecimal sumOtherCost2 = BigDecimal.ZERO;
        BigDecimal sumOtherCost3 = BigDecimal.ZERO;
        BigDecimal sumOtherCost4 = BigDecimal.ZERO;
        BigDecimal sumOtherCost5 = BigDecimal.ZERO;
        BigDecimal sumOtherCost6 = BigDecimal.ZERO;
        BigDecimal sumOtherCost7 = BigDecimal.ZERO;
        BigDecimal sumOtherCost8 = BigDecimal.ZERO;
        BigDecimal sumOtherCost9 = BigDecimal.ZERO;
        BigDecimal sumOtherCost10 = BigDecimal.ZERO;
        BigDecimal sumOtherCost11 = BigDecimal.ZERO;
        BigDecimal sumOtherCost12 = BigDecimal.ZERO;
        BigDecimal sumSumAmt = BigDecimal.ZERO;
        BigDecimal sumTotalBoxes = BigDecimal.ZERO;
        BigDecimal sumOddBoxes = BigDecimal.ZERO;
        BigDecimal sumWeight = BigDecimal.ZERO;
        BigDecimal sumVolume = BigDecimal.ZERO;
        BigDecimal sumTotalVolume = BigDecimal.ZERO;

        // 动态费用字段
        for (int i = 0; i < s4row2.length; i++) {
            String name = s4row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            if (StrUtil.isNotEmpty(dicMap.get("yf_storage_othercost" + name))) {
                otherFeeInfo.setOtherName(dicMap.get("yf_storage_othercost" + name));
            }
            switch (name) {
                case "客诉理赔费":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcExceptionFee());
                    break;
                case "otherCost1":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost1());
                    break;
                case "otherCost2":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost2());
                    break;
                case "otherCost3":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost3());
                    break;
                case "otherCost4":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost4());
                    break;
                case "otherCost5":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost5());
                    break;
                case "otherCost6":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost6());
                    break;
                case "otherCost7":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost7());
                    break;
                case "otherCost8":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost8());
                    break;
                case "otherCost9":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost9());
                    break;
                case "otherCost10":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost10());
                    break;
                case "otherCost11":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost11());
                    break;
                case "otherCost12":
                    otherFeeInfo.setOtherFee(bmsYfbillmainExportAll.getCcOtherCost12());
                    break;
                default:
                    otherFeeInfo.setOtherFee(BigDecimal.valueOf(0));
            }
            s4row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s4row2.length; i++) {
            Cell tempCell = s4r2.createCell(i);
            tempCell.setCellValue(s4row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        Integer row = 1;
        // 创建数据行，获取出库明细
        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd");
        for (int p = 0; p < stkList.size(); p++) {
            BmsYsBillExportInfo info = stkList.get(p);
            Row s2r3 = sheet4.createRow(rowNum4[0]++);
            s2r3.setHeight((short) 500);
            boolean f = p <= 0 || p >= stkList.size() || !stkList.get(p).getId().equals(stkList.get(p - 1).getId());
            // 赋0
            for (int i = 0; i < s4row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getSigningDate() != null ? sml.format(info.getSigningDate()) : "");
                        break;
                    case 1:
                        tempCell.setCellValue(info.getRelateCode());
                        break;
                    case 2:
                        tempCell.setCellValue(info.getSkuCode());
                        break;
                    case 3:
                        tempCell.setCellValue(info.getSkuName());
                        break;
                    case 4:
                        tempCell.setCellValue(info.getTemperatureName());
                        break;
                    case 5:
                        tempCell.setCellValue(info.getSpecification());
                        break;
                    case 6:
                        tempCell.setCellValue(info.getUnit());
                        break;
                    case 7:
                        tempCell.setCellValue(info.getBoxType());
                        break;
                    case 8:
                        tempCell.setCellValue(info.getContentsNumber() == null ? 0 : Double.parseDouble(info.getContentsNumber().toString()));
                        break;
                    case 9:
                        tempCell.setCellValue(info.getTotalBoxes() == null ? 0 : Double.parseDouble(info.getTotalBoxes().toString()));
                        break;
                    case 10:
                        tempCell.setCellValue(info.getOddBoxes() == null ? 0 : Double.parseDouble(info.getOddBoxes().toString()));
                        break;
                    case 11:
                        tempCell.setCellValue(info.getWeight() == null ? 0 : Double.parseDouble(info.getWeight().toString()));
                        break;
                    case 12:
                        tempCell.setCellValue(info.getVolume() == null ? 0 : Double.parseDouble(info.getVolume().toString()));
                        break;
                    case 13:
                        tempCell.setCellValue(info.getTotalWeight() == null ? 0 : Double.parseDouble(info.getTotalWeight().toString()));
                        break;
                    case 14:
                        tempCell.setCellValue(info.getTotalVolume() == null ? 0 : Double.parseDouble(info.getTotalVolume().toString()));
                        break;
                    case 15:
                        tempCell.setCellValue(info.getTotalBoxes() == null ? 0 : Double.parseDouble(info.getTotalBoxes().toString()));
                        break;
                    case 16:
                        tempCell.setCellValue(info.getReceivingStore());
                        break;
                    case 17:
                        tempCell.setCellValue(!f ? 0 : info.getFreight() == null ? 0 : Double.parseDouble(info.getFreight().toString()));
                        break;
                    case 18:
                        tempCell.setCellValue(!f ? 0 : info.getUltrafarFee() == null ? 0 : Double.parseDouble(info.getUltrafarFee().toString()));
                        break;
                    case 19:
                        tempCell.setCellValue(!f ? 0 : info.getSuperframesFee() == null ? 0 : Double.parseDouble(info.getSuperframesFee().toString()));
                        break;
                    case 20:
                        tempCell.setCellValue(!f ? 0 : info.getExcessFee() == null ? 0 : Double.parseDouble(info.getExcessFee().toString()));
                        break;
                    case 21:
                        tempCell.setCellValue(!f ? 0 : info.getDeliveryFee() == null ? 0 : Double.parseDouble(info.getDeliveryFee().toString()));
                        break;
                    case 22:
                        tempCell.setCellValue(!f ? 0 : info.getReturnFee() == null ? 0 : Double.parseDouble(info.getReturnFee().toString()));
                        break;
                    case 23:
                        tempCell.setCellValue(!f ? 0 : info.getShortbargeFee() == null ? 0 : Double.parseDouble(info.getShortbargeFee().toString()));
                        break;
                    case 24:
                        tempCell.setCellValue(!f ? 0 : info.getExceptionFee() == null ? 0 : Double.parseDouble(info.getExceptionFee().toString()));
                        break;
                    case 25:
                        tempCell.setCellValue(!f ? 0 : info.getAdjustFee() == null ? 0 : Double.parseDouble(info.getAdjustFee().toString()));
                        break;
                    case 26:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost1() == null ? 0 : Double.parseDouble(info.getOtherCost1().toString()));
                        break;
                    case 27:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost2() == null ? 0 : Double.parseDouble(info.getOtherCost2().toString()));
                        break;
                    case 28:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost3() == null ? 0 : Double.parseDouble(info.getOtherCost3().toString()));
                        break;
                    case 29:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost4() == null ? 0 : Double.parseDouble(info.getOtherCost4().toString()));
                        break;
                    case 30:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost5() == null ? 0 : Double.parseDouble(info.getOtherCost5().toString()));
                        break;
                    case 31:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost6() == null ? 0 : Double.parseDouble(info.getOtherCost6().toString()));
                        break;
                    case 32:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost7() == null ? 0 : Double.parseDouble(info.getOtherCost7().toString()));
                        break;
                    case 33:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost8() == null ? 0 : Double.parseDouble(info.getOtherCost8().toString()));
                        break;
                    case 34:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost9() == null ? 0 : Double.parseDouble(info.getOtherCost9().toString()));
                        break;
                    case 35:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost10() == null ? 0 : Double.parseDouble(info.getOtherCost10().toString()));
                        break;
                    case 36:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost11() == null ? 0 : Double.parseDouble(info.getOtherCost11().toString()));
                        break;
                    case 37:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost12() == null ? 0 : Double.parseDouble(info.getOtherCost12().toString()));
                        break;
                    case 38:
                        tempCell.setCellValue(!f ? 0 : info.getSumAmt() == null ? 0 : Double.parseDouble(info.getSumAmt().toString()));
                        break;
                    case 39:
                        tempCell.setCellValue(info.getOperBy());
                        break;
                    case 40:
                        tempCell.setCellValue(info.getRemarksJf());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
                /*if((p==stkList.size()-1 && row<rowNum4[0]-1) || (row<rowNum4[0]-1 && p+1<stkList.size()  && !lastId.equals(stkList.get(p+1).getId()))){
                    for(int j =14;j<stkList.size();j++){
                        sheet4.addMergedRegion(new CellRangeAddress(row, rowNum4[0]-1, j, j));
                    }
                    lastId=info.getId();
                    row=rowNum4[0];
                }else{
                    lastId=info.getId();
                }

                if((p==stkList.size()-1 && row==rowNum4[0]-1) || (row==rowNum4[0]-1 && p+1<stkList.size()  && !lastId.equals(stkList.get(p+1).getId()))){
                    row=rowNum4[0];
                }*/
            }
            if (f) {
                sumContentsNumber = sumContentsNumber.add(info.getContentsNumber());
                sumAmt[2] = sumAmt[2].add(info.getFreight());
                sumFreight = sumFreight.add(info.getFreight());
                sumAmt[3] = sumAmt[3].add(info.getUltrafarFee());
                sumUltrafarFee = sumUltrafarFee.add(info.getUltrafarFee());
                sumAmt[4] = sumAmt[4].add(info.getSuperframesFee());
                sumSuperframesFee = sumSuperframesFee.add(info.getSuperframesFee());
                sumAmt[5] = sumAmt[5].add(info.getExcessFee());
                sumExcessFee = sumExcessFee.add(info.getExcessFee());
                sumAmt[6] = sumAmt[6].add(info.getDeliveryFee());
                sumDeliveryFee = sumDeliveryFee.add(info.getDeliveryFee());
                sumAmt[7] = sumAmt[7].add(info.getReturnFee());
                sumReturnFee = sumReturnFee.add(info.getReturnFee());
                sumAmt[8] = sumAmt[8].add(info.getShortbargeFee());
                sumShortbargeFee = sumShortbargeFee.add(info.getShortbargeFee());
                sumAmt[9] = sumAmt[9].add(info.getExceptionFee());
                sumExceptionFee = sumExceptionFee.add(info.getExceptionFee());
                sumAmt[10] = sumAmt[10].add(BigDecimal.valueOf(info.getAdjustFee()));
                sumAdjustFee = sumAdjustFee.add(BigDecimal.valueOf(info.getAdjustFee()));
                sumAmt[11] = sumAmt[11].add(info.getOtherCost1());
                sumOtherCost1 = sumOtherCost1.add(info.getOtherCost1());
                sumAmt[12] = sumAmt[12].add(info.getOtherCost2());
                sumOtherCost2 = sumOtherCost2.add(info.getOtherCost2());
                sumAmt[13] = sumAmt[13].add(info.getOtherCost3());
                sumOtherCost3 = sumOtherCost3.add(info.getOtherCost3());
                sumAmt[14] = sumAmt[14].add(info.getOtherCost4());
                sumOtherCost4 = sumOtherCost4.add(info.getOtherCost4());
                sumAmt[15] = sumAmt[15].add(info.getOtherCost5());
                sumOtherCost5 = sumOtherCost5.add(info.getOtherCost5());
                sumAmt[16] = sumAmt[16].add(info.getOtherCost6());
                sumOtherCost6 = sumOtherCost6.add(info.getOtherCost6());
                sumAmt[17] = sumAmt[17].add(info.getOtherCost7());
                sumOtherCost7 = sumOtherCost7.add(info.getOtherCost7());
                sumAmt[18] = sumAmt[18].add(info.getOtherCost8());
                sumOtherCost8 = sumOtherCost8.add(info.getOtherCost8());
                sumAmt[19] = sumAmt[19].add(info.getOtherCost9());
                sumOtherCost9 = sumOtherCost9.add(info.getOtherCost9());
                sumAmt[20] = sumAmt[20].add(info.getOtherCost10());
                sumOtherCost10 = sumOtherCost10.add(info.getOtherCost10());
                sumAmt[21] = sumAmt[21].add(info.getOtherCost11());
                sumOtherCost11 = sumOtherCost11.add(info.getOtherCost11());
                sumAmt[22] = sumAmt[22].add(info.getOtherCost12());
                sumOtherCost12 = sumOtherCost12.add(info.getOtherCost12());
                sumAmt[23] = sumAmt[23].add(BigDecimal.valueOf(info.getSumAmt()));
                sumSumAmt = sumSumAmt.add(BigDecimal.valueOf(info.getSumAmt()));
                sumAmt[24] = sumAmt[24].add(info.getTotalBoxes());
                sumTotalBoxes = sumTotalBoxes.add(info.getTotalBoxes());
                sumAmt[25] = sumAmt[25].add(info.getOddBoxes());
                sumOddBoxes = sumOddBoxes.add(info.getOddBoxes());
                sumAmt[26] = sumAmt[26].add(info.getWeight());
                sumWeight = sumWeight.add(info.getWeight());
                sumVolume = sumVolume.add(info.getVolume());
                sumTotalVolume = sumTotalVolume.add(info.getTotalVolume());
            }
        }

        //最后一行 合计金额
        Row s2r4 = sheet4.createRow(rowNum4[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"合计：", "", "", "", "", "", "", "", sumContentsNumber.toString(), sumAmt[24].toString(), "", "", "", "", "", "", "",
                sumAmt[2].toString(), sumAmt[3].toString(), sumAmt[4].toString(), sumAmt[5].toString(),
                sumAmt[6].toString(), sumAmt[7].toString(), sumAmt[8].toString(), sumAmt[9].toString(),
                sumAmt[10].toString(),
                sumAmt[11].toString(), sumAmt[12].toString(), sumAmt[13].toString(), sumAmt[14].toString(),
                sumAmt[15].toString(), sumAmt[16].toString(), sumAmt[17].toString(), sumAmt[18].toString(),
                sumAmt[19].toString(), sumAmt[20].toString(), sumAmt[21].toString(), sumAmt[22].toString(),
                sumAmt[23].toString(), "", ""
        };
//        String[] s2row4 = { "合计：","", "","", "", "", "", "",sumAmt[25].toString(),sumAmt[24].toString(),"","","","","","","",
//                sumAmt[2].toString(),sumAmt[3].toString(),sumAmt[4].toString(),sumAmt[5].toString(),
//                sumAmt[6].toString(),sumAmt[7].toString(),sumAmt[8].toString(),sumAmt[9].toString(),
//                sumAmt[10].toString(),
//                sumAmt[11].toString(),sumAmt[12].toString(),sumAmt[13].toString(),sumAmt[14].toString(),
//                sumAmt[15].toString(),sumAmt[16].toString(),sumAmt[17].toString(),sumAmt[18].toString(),
//                sumAmt[19].toString(),sumAmt[20].toString(),sumAmt[21].toString(),sumAmt[22].toString(),
//                sumAmt[23].toString(),"",""
//        };
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if ((i > 16 && i < 39) || i == 8 || i == 9) {
                tempCell.setCellValue(Double.parseDouble(s2row4[i]));
            } else {
                tempCell.setCellValue(s2row4[i]);
            }

            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet4.addMergedRegion(new CellRangeAddress(rowNum4[0] - 1, rowNum4[0] - 1, 0, 7));
        return hssfWorkbook;
    }


    /**
     * 第七个sheet 配送明细
     *
     * @param hssfWorkbook           excel
     * @param bmsYfbillmainExportAll 数据
     * @param dicMap                 字典
     */
    public SXSSFWorkbook distributionDetailsSummary(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid= RequestContext.getTenantId();
        // 第7个sheet
        Sheet sheet6 = hssfWorkbook.createSheet("配送门店汇总");
        // 背景色的设定
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum6 = {0};

        //设置列宽
        for (int i = 0; i < 19; i++) {
            sheet6.setColumnWidth(i, 2700);
        }

        //第1行
        Row s6r2 = sheet6.createRow(rowNum6[0]++);
        s6r2.setHeight((short) 500);
        String[] s6row2 = {"门店名称", "门店地址", "线路名称", "配送趟数",
                "当月配送总店次", "始发地", "目的地"
        };
        for (int i = 0; i < s6row2.length; i++) {
            Cell tempCell = s6r2.createCell(i);
            tempCell.setCellValue(s6row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        // 创建数据行，获取入库明细
        List<BmsYsBillExportInfo> stkList = yfBillExportMapper.distributionDetailsSummary(tenantid, ids);
        Map<String, String> dicMap2 = textConversionUtil.getDictByType("car_model", "1");
        stkList.forEach(e -> {
            if (e.getCarModel() != null) {
                e.setCarModelName(StrUtil.isEmpty(dicMap2.get("car_model" + e.getCarModel())) ? e.getCarModel() : dicMap2.get("car_model" + e.getCarModel()));
            }
        });
        for (BmsYsBillExportInfo info : stkList) {
            Row s2r3 = sheet6.createRow(rowNum6[0]++);
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s6row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getReceivingStore());
                        break;
                    case 1:
                        tempCell.setCellValue(info.getCity());
                        break;
                    case 2:
                        tempCell.setCellValue(info.getLineName());
                        break;
                    case 3:
                        tempCell.setCellValue(info.getDistributionNum() == null ? 0 : info.getDistributionNum());
                        break;
                    case 4:
                        tempCell.setCellValue(info.getOfficeTimes() == null ? 0 : info.getOfficeTimes());
                        break;
                    case 5:
                        tempCell.setCellValue(info.getOriginatingCity());
                        break;
                    case 6:
                        tempCell.setCellValue(info.getDestinationCity());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }
        }
        return hssfWorkbook;
    }

    /**
     * 第七个sheet 费用单维度
     *
     * @param hssfWorkbook           excel
     * @param bmsYfbillmainExportAll 导出数据
     * @param dicMap                 字典
     */
    public SXSSFWorkbook distributionDetailsSummary2(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid= RequestContext.getTenantId();
        // 第7个sheet
        Sheet sheet6 = hssfWorkbook.createSheet("运输费用明细");
        // 背景色的设定
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum6 = {0};
        List<BmsYsBillExportInfo> stkList = yfBillExportMapper.getDistribution2GoodsDetailByBillId(tenantid, ids);

        BigDecimal[] amtf = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        Boolean[] fare = new Boolean[23];
        // 判断是否要隐藏列
        for (BmsYsBillExportInfo bmsYsBillExportInfo : stkList) {
            amtf[0] = amtf[0].add(bmsYsBillExportInfo.getFreight());
            amtf[1] = amtf[1].add(bmsYsBillExportInfo.getDeliveryFee());
            amtf[2] = amtf[2].add(bmsYsBillExportInfo.getOutboundsortingFee());
            amtf[3] = amtf[3].add(bmsYsBillExportInfo.getShortbargeFee());
            amtf[4] = amtf[4].add(bmsYsBillExportInfo.getUltrafarFee());
            amtf[5] = amtf[5].add(bmsYsBillExportInfo.getSuperframesFee());
            amtf[6] = amtf[6].add(bmsYsBillExportInfo.getExcessFee());
            amtf[7] = amtf[7].add(bmsYsBillExportInfo.getReduceFee());
            amtf[8] = amtf[8].add(bmsYsBillExportInfo.getReturnFee());
            amtf[9] = amtf[9].add(bmsYsBillExportInfo.getExceptionFee());
            amtf[10] = amtf[10].add(bmsYsBillExportInfo.getAdjustFee() == null ? new BigDecimal(0) : BigDecimal.valueOf(bmsYsBillExportInfo.getAdjustFee()));
            amtf[11] = amtf[11].add(bmsYsBillExportInfo.getOtherCost1());
            amtf[12] = amtf[12].add(bmsYsBillExportInfo.getOtherCost2());
            amtf[13] = amtf[13].add(bmsYsBillExportInfo.getOtherCost3());
            amtf[14] = amtf[14].add(bmsYsBillExportInfo.getOtherCost4());
            amtf[15] = amtf[15].add(bmsYsBillExportInfo.getOtherCost5());
            amtf[16] = amtf[16].add(bmsYsBillExportInfo.getOtherCost6());
            amtf[17] = amtf[17].add(bmsYsBillExportInfo.getOtherCost7());
            amtf[18] = amtf[18].add(bmsYsBillExportInfo.getOtherCost8());
            amtf[19] = amtf[19].add(bmsYsBillExportInfo.getOtherCost9());
            amtf[20] = amtf[20].add(bmsYsBillExportInfo.getOtherCost10());
            amtf[21] = amtf[21].add(bmsYsBillExportInfo.getOtherCost11());
            amtf[22] = amtf[22].add(bmsYsBillExportInfo.getOtherCost12());
        }
        int fi = 0;
        for (BigDecimal bigDecimal : amtf) {
            fare[fi] = bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0;
            fi++;
        }
        //设置列宽
        for (int i = 0; i < fare.length; i++) {
            if (fare[i] != null && fare[i]) {
                sheet6.setColumnWidth(i + 21, 0);
            }
        }
        //第1行
        Row s6r2 = sheet6.createRow(rowNum6[0]++);
        s6r2.setHeight((short) 500);
        String[] s6row2 = {"调度单号", "计费类型", "计费单号", "计费时间", "费用维度", "费用标识",
                "调账备注", "虚拟调度单号", "是否拆单", "新增时间", "配载日期", "完成时间", "单据所属",
                "承运商名称", "承运商编码", "客户名称", "线路编码", "线路名称", "干线路编码", "干线路名称", "订单数",
                "运费", "提货费", "送货费", "短驳费", "超远费", "超框费", "加点费", "减点费", "装卸费", "客诉理赔费", "调账费",
                "otherCost1", "otherCost2", "otherCost3", "otherCost4", "otherCost5", "otherCost6",
                "otherCost7", "otherCost8", "otherCost9", "otherCost10", "otherCost11", "otherCost12",
                "总费用", "计费箱数", "计费人姓名", "计费备注", "总箱数", "总件数", "总重量", "总体积", "总货值",
                "配送类型", "运输方式", "车牌号码", "车长", "司机", "始发省", "始发市", "始发区", "目的省", "目的市", "目的区", "账期"
        };
        for (int i = 0; i < s6row2.length; i++) {
            String name = s6row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            if (StrUtil.isNotEmpty(dicMap.get("yf_transport_othercost" + name))) {
                otherFeeInfo.setOtherName(dicMap.get("yf_transport_othercost" + name));
            }
            s6row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s6row2.length; i++) {
            Cell tempCell = s6r2.createCell(i);
            tempCell.setCellValue(s6row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }

        // 创建数据行，获取入库明细
        String lastId = "";
        int row = 2;
        String code = "";
        if (CollUtil.isNotEmpty(stkList)) {
            code = stkList.get(0).getExpensesCode();
        }
        Map<String, SysDept> companyMap = textConversionUtil.getCompanyMap("1", stkList.stream().filter(e -> e.getCompanyId() != null).map(e -> e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        stkList.forEach(e -> {
            if (e.getCompanyId() != null && companyMap.containsKey(e.getCompanyId().toString())) {
                e.setCompanyName(companyMap.get(e.getCompanyId().toString()).getDeptName());
            }
        });
        Map<String, String> dicMap2 = textConversionUtil.getDictByType("transport_type,delivery_mode,car_model,cost_dimension,fee_type_first", "1");
        stkList.forEach(e -> {
            if (e.getCarModel() != null) {
                e.setCarModelName(dicMap2.get("car_model" + e.getCarModel()));
            }
            if (e.getTransportType() != null) {
                e.setTransportTypeName(dicMap2.get("transport_type" + e.getTransportType()));
            }
            if (e.getDeliveryMode() != null) {
                e.setDeliveryModeName(dicMap2.get("delivery_mode" + e.getDeliveryMode()));
            }
            if (e.getCostDimension() != null) {
                e.setCostDimensionName(dicMap2.get("cost_dimension" + e.getCostDimension()));
            }
            if (e.getFeeTypeFirst() != null) {
                e.setFeeTypeFirstName(dicMap2.get("fee_type_first" + e.getFeeTypeFirst()));
            }
        });
        SimpleDateFormat sml = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        for (int s = 0; s < stkList.size(); s++) {
            BmsYsBillExportInfo info = stkList.get(s);
            Row s2r3 = sheet6.createRow(rowNum6[0]++);
            s2r3.setHeight((short) 500);
            boolean f = true;
            // 赋0
            f = s <= 0 || s >= stkList.size() || !stkList.get(s).getId().equals(stkList.get(s - 1).getId());
            for (int i = 0; i < s6row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        // 调度单号
                        tempCell.setCellValue(info.getSchedulingBillCode());
                        break;
                    case 1:
                        // 计费类型
                        tempCell.setCellValue(info.getChargeTypeName());
                        break;
                    case 2:
                        // 计费单号
                        tempCell.setCellValue(info.getExpensesCode());
                        break;
                    case 3:
                        // 计费时间
                        tempCell.setCellValue(info.getOperTime());
                        break;
                    case 4:
                        // 费用维度
                        tempCell.setCellValue(info.getCostDimensionName());
                        break;
                    case 5:
                        // 费用标识
                        tempCell.setCellValue(info.getFeeFlagName());
                        break;
                    case 6:
                        // 调账备注
                        tempCell.setCellValue(info.getAdjustRemark());
                        break;
                    case 7:
                        // 虚拟调度单号
                        tempCell.setCellValue(info.getVirtualOrderNo());
                        break;
                    case 8:
                        // 是否拆单
                        tempCell.setCellValue(info.getProjectQuoteName());
                        break;
                    case 9:
                        // 新增时间
                        tempCell.setCellValue(info.getCreateTime());
                        break;
                    case 10:
                        // 配载日期
                        tempCell.setCellValue(info.getDispatchDate());
                        break;
                    case 11:
                        // 完成时间
                        tempCell.setCellValue(info.getFinishDate());
                        break;
                    case 12:
                        // 单据所属
                        tempCell.setCellValue(info.getCompanyName());
                        break;
                    case 13:
                        // 承运商名称
                        tempCell.setCellValue(info.getCarrierName());
                        break;
                    case 14:
                        // 承运商编码
                        tempCell.setCellValue(info.getCarrierCode());
                        break;
                    case 15:
                        // 客户名称
                        tempCell.setCellValue(info.getClientName());
                        break;
                    case 16:
                        // 线路编码
                        tempCell.setCellValue(info.getLineCode());
                        break;
                    case 17:
                        // 线路名称
                        tempCell.setCellValue(info.getLineName());
                        break;
                    case 18:
                        // 干线路编码
                        tempCell.setCellValue(info.getTlineCode());
                        break;
                    case 19:
                        // 干线路名称
                        tempCell.setCellValue(info.getTlineName());
                        break;
                    case 20:
                        // 订单数
                        tempCell.setCellValue(info.getOrderNumber() == null ? 0 : info.getOrderNumber());
                        break;
                    case 21:
                        tempCell.setCellValue(!f ? 0 : info.getFreight() == null ? 0 : Double.parseDouble(info.getFreight().toString()));
                        break;
                    case 22:
                        tempCell.setCellValue(!f ? 0 : info.getDeliveryFee() == null ? 0 : Double.parseDouble(info.getDeliveryFee().toString()));
                        break;
                    case 23:
                        tempCell.setCellValue(!f ? 0 : info.getOutboundsortingFee() == null ? 0 : Double.parseDouble(info.getOutboundsortingFee().toString()));
                        break;
                    case 24:
                        tempCell.setCellValue(!f ? 0 : info.getShortbargeFee() == null ? 0 : Double.parseDouble(info.getShortbargeFee().toString()));
                        break;
                    case 25:
                        tempCell.setCellValue(!f ? 0 : info.getUltrafarFee() == null ? 0 : Double.parseDouble(info.getUltrafarFee().toString()));
                        break;
                    case 26:
                        tempCell.setCellValue(!f ? 0 : info.getSuperframesFee() == null ? 0 : Double.parseDouble(info.getSuperframesFee().toString()));
                        break;
                    case 27:
                        tempCell.setCellValue(!f ? 0 : info.getExcessFee() == null ? 0 : Double.parseDouble(info.getExcessFee().toString()));
                        break;
                    case 28:
                        tempCell.setCellValue(!f ? 0 : info.getReduceFee() == null ? 0 : Double.parseDouble(info.getReduceFee().toString()));
                        break;
                    case 29:
                        tempCell.setCellValue(!f ? 0 : info.getReturnFee() == null ? 0 : Double.parseDouble(info.getReturnFee().toString()));
                        break;
                    case 30:
                        tempCell.setCellValue(!f ? 0 : info.getExceptionFee() == null ? 0 : Double.parseDouble(info.getExceptionFee().toString()));
                        break;
                    case 31:
                        // 调账费
                        tempCell.setCellValue(info.getAdjustFee() == null ? 0 : info.getAdjustFee());
                        break;
                    case 32:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost1() == null ? 0 : Double.parseDouble(info.getOtherCost1().toString()));
                        break;
                    case 33:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost2() == null ? 0 : Double.parseDouble(info.getOtherCost2().toString()));
                        break;
                    case 34:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost3() == null ? 0 : Double.parseDouble(info.getOtherCost3().toString()));
                        break;
                    case 35:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost4() == null ? 0 : Double.parseDouble(info.getOtherCost4().toString()));
                        break;
                    case 36:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost5() == null ? 0 : Double.parseDouble(info.getOtherCost5().toString()));
                        break;
                    case 37:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost6() == null ? 0 : Double.parseDouble(info.getOtherCost6().toString()));
                        break;
                    case 38:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost7() == null ? 0 : Double.parseDouble(info.getOtherCost7().toString()));
                        break;
                    case 39:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost8() == null ? 0 : Double.parseDouble(info.getOtherCost8().toString()));
                        break;
                    case 40:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost9() == null ? 0 : Double.parseDouble(info.getOtherCost9().toString()));
                        break;
                    case 41:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost10() == null ? 0 : Double.parseDouble(info.getOtherCost10().toString()));
                        break;
                    case 42:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost11() == null ? 0 : Double.parseDouble(info.getOtherCost11().toString()));
                        break;
                    case 43:
                        tempCell.setCellValue(!f ? 0 : info.getOtherCost12() == null ? 0 : Double.parseDouble(info.getOtherCost12().toString()));
                        break;
                    case 44:
                        tempCell.setCellValue(!f ? 0 : info.getSumAmt() == null ? 0 : info.getSumAmt());
                        break;
                    case 45:
                        //折箱数
                        tempCell.setCellValue(!f ? 0 : info.getExtraField1() == null ? 0 : info.getExtraField1().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 46:
                        //计费人姓名
                        tempCell.setCellValue(info.getOperBy());
                        break;
                    case 47:
                        // 计费备注
                        tempCell.setCellValue(info.getAutomaticBillingRemark());
                        break;
                    case 48:
                        tempCell.setCellValue(info.getTotalBoxes() == null ? 0 : Double.parseDouble(info.getTotalBoxes().toString())); // 总箱数
                        break;
                    case 49:
                        tempCell.setCellValue(info.getTotalNumber() == null ? 0 : Double.parseDouble(info.getTotalNumber().toString())); // 总件数
                        break;
                    case 50:
                        tempCell.setCellValue(info.getTotalWeight() == null ? 0 : Double.parseDouble(info.getTotalWeight().toString())); // 总重量
                        break;
                    case 51:
                        tempCell.setCellValue(info.getTotalVolume() == null ? 0 : Double.parseDouble(info.getTotalVolume().toString())); // 总体积
                        break;
                    case 52:
                        tempCell.setCellValue(info.getCargoValue() == null ? 0 : Double.parseDouble(info.getCargoValue().toString())); // 总货值
                        break;
                    case 53:
                        // 配送类型
                        tempCell.setCellValue(info.getDeliveryModeName());
                        break;
                    case 54:
                        // 运输方式
                        tempCell.setCellValue(info.getTransportTypeName());
                        break;
                    case 55:
                        // 车牌号码
                        tempCell.setCellValue(info.getCarCode());
                        break;
                    case 56:
                        tempCell.setCellValue(StrUtil.isEmpty(info.getCarModelName()) ? info.getCarModel() : info.getCarModelName()); // 车长
                        break;
                    case 57:
                        // 司机
                        tempCell.setCellValue(info.getDriver());
                        break;
                    case 58:
                        // 始发省
                        tempCell.setCellValue(info.getProvinceOrigin());
                        break;
                    case 59:
                        // 始发市
                        tempCell.setCellValue(info.getOriginatingCity());
                        break;
                    case 60:
                        // 始发区
                        tempCell.setCellValue(info.getOriginatingArea());
                        break;
                    case 61:
                        // 目的省
                        tempCell.setCellValue(info.getDestinationProvince());
                        break;
                    case 62:
                        // 目的市
                        tempCell.setCellValue(info.getDestinationCity());
                        break;
                    case 63:
                        // 目的区
                        tempCell.setCellValue(info.getDestinationArea());
                        break;
                    case 64:
                        // 账期
                        tempCell.setCellValue(info.getBillDate());
                        break;
//                    case 65:
//                        // 费用大类
//                        tempCell.setCellValue(info.getFeeTypeFirstName());
//                        break;
//                    case 66:
//                        // 是否为增值费
//                        tempCell.setCellValue(info.getIsIncrement() == 0 ? "否" : "是");
//                        break;
//                    case 67:
//                        tempCell.setCellValue(info.getTotalQuantity() == null ? 0 : Double.parseDouble(info.getTotalQuantity().toString())); // 附加费总数量
//                        break;
//                    case 68:
//                        tempCell.setCellValue(info.getPrice() == null ? 0 : Double.parseDouble(info.getPrice().toString())); // 附加费单价
//                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
                if ((s == stkList.size() - 1 && row < rowNum6[0] - 1) || (row < rowNum6[0] - 1 && s + 1 < stkList.size() && !lastId.equals(stkList.get(s + 1).getId()))) {
                    for (int j = 13; j < stkList.size(); j++) {
                        sheet6.addMergedRegion(new CellRangeAddress(row, rowNum6[0] - 1, j, j));
                    }
                    lastId = info.getId();
                    row = rowNum6[0];
                } else {
                    lastId = info.getId();
                }

                if ((s == stkList.size() - 1 && row == rowNum6[0] - 1) || (row == rowNum6[0] - 1 && s + 1 < stkList.size() && !lastId.equals(stkList.get(s + 1).getId()))) {
                    row = rowNum6[0];
                }
            }
            sumAmt[26] = sumAmt[26].add(info.getTotalNumber() == null ? new BigDecimal("0") : info.getTotalNumber());
            sumAmt[27] = sumAmt[27].add(info.getTotalWeight() == null ? new BigDecimal("0") : info.getTotalWeight());
            sumAmt[28] = sumAmt[28].add(info.getTotalVolume() == null ? new BigDecimal("0") : info.getTotalVolume());
            sumAmt[29] = sumAmt[29].add(info.getCargoValue() == null ? new BigDecimal("0") : info.getCargoValue());
            sumAmt[30] = sumAmt[30].add(info.getOrderNumber() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getOrderNumber()));
            sumAmt[31] = sumAmt[31].add(info.getTotalBoxes() == null ? new BigDecimal("0") : info.getTotalBoxes());
            //折箱数
            sumAmt[32] = sumAmt[32].add(info.getExtraField1() == null ? new BigDecimal("0") : info.getExtraField1());

            if (f) {
                sumAmt[0] = sumAmt[0].add(info.getAdjustFee() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getAdjustFee()));
                sumAmt[1] = sumAmt[1].add(info.getFreight() == null ? new BigDecimal("0") : info.getFreight());
                sumAmt[2] = sumAmt[2].add(info.getDeliveryFee() == null ? new BigDecimal("0") : info.getDeliveryFee());
                sumAmt[3] = sumAmt[3].add(info.getOutboundsortingFee() == null ? new BigDecimal("0") : info.getOutboundsortingFee());
                sumAmt[4] = sumAmt[4].add(info.getShortbargeFee() == null ? new BigDecimal("0") : info.getShortbargeFee());
                sumAmt[5] = sumAmt[5].add(info.getUltrafarFee() == null ? new BigDecimal("0") : info.getUltrafarFee());
                sumAmt[6] = sumAmt[6].add(info.getSuperframesFee() == null ? new BigDecimal("0") : info.getSuperframesFee());
                sumAmt[7] = sumAmt[7].add(info.getExcessFee() == null ? new BigDecimal("0") : info.getExcessFee());
                sumAmt[8] = sumAmt[8].add(info.getReduceFee() == null ? new BigDecimal("0") : info.getReduceFee());
                sumAmt[9] = sumAmt[9].add(info.getReturnFee() == null ? new BigDecimal("0") : info.getReturnFee());
                sumAmt[10] = sumAmt[10].add(info.getExceptionFee() == null ? new BigDecimal("0") : info.getExceptionFee());
                sumAmt[11] = sumAmt[11].add(info.getOtherCost1() == null ? new BigDecimal("0") : info.getOtherCost1());
                sumAmt[12] = sumAmt[12].add(info.getOtherCost2() == null ? new BigDecimal("0") : info.getOtherCost2());
                sumAmt[13] = sumAmt[13].add(info.getOtherCost3() == null ? new BigDecimal("0") : info.getOtherCost3());
                sumAmt[14] = sumAmt[14].add(info.getOtherCost4() == null ? new BigDecimal("0") : info.getOtherCost4());
                sumAmt[15] = sumAmt[15].add(info.getOtherCost5() == null ? new BigDecimal("0") : info.getOtherCost5());
                sumAmt[16] = sumAmt[16].add(info.getOtherCost6() == null ? new BigDecimal("0") : info.getOtherCost6());
                sumAmt[17] = sumAmt[17].add(info.getOtherCost7() == null ? new BigDecimal("0") : info.getOtherCost7());
                sumAmt[18] = sumAmt[18].add(info.getOtherCost8() == null ? new BigDecimal("0") : info.getOtherCost8());
                sumAmt[19] = sumAmt[19].add(info.getOtherCost9() == null ? new BigDecimal("0") : info.getOtherCost9());
                sumAmt[20] = sumAmt[20].add(info.getOtherCost10() == null ? new BigDecimal("0") : info.getOtherCost10());
                sumAmt[21] = sumAmt[21].add(info.getOtherCost11() == null ? new BigDecimal("0") : info.getOtherCost11());
                sumAmt[22] = sumAmt[22].add(info.getOtherCost12() == null ? new BigDecimal("0") : info.getOtherCost12());
                sumAmt[23] = sumAmt[23].add(info.getBasicsSum() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getBasicsSum()));
                sumAmt[24] = sumAmt[24].add(info.getOtherSum() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getOtherSum()));
                sumAmt[25] = sumAmt[25].add(info.getBasicsSum() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getBasicsSum()))
                        .add(info.getOtherSum() == null ? new BigDecimal("0") : BigDecimal.valueOf(info.getOtherSum()));
            }
            // 合并单元格

            if (!code.equals(stkList.get(s).getExpensesCode())) {
                code = stkList.get(s).getExpensesCode();
                if (rowNum6[0] - 1 > row) {
                    for (int i = 1; i <= 48; i++) {
                        if (i >= 20 && i <= 45) {
                            sheet6.addMergedRegion(new CellRangeAddress(row - 1, rowNum6[0] - 2, i, i));
                        }
                    }
                    row = rowNum6[0];
                } else {
                    row = rowNum6[0];
                }
            }
        }


        //最后一行 合计金额
        Row s2r4 = sheet6.createRow(rowNum6[0]++);
        s2r4.setHeight((short) 500);
        /*String[] s2row4 = {"合计：", "", "", "", "", "",
                "", sumAmt[0].toString(),sumAmt[1].toString(),sumAmt[2].toString(),sumAmt[3].toString(),
                sumAmt[4].toString(),sumAmt[5].toString(),sumAmt[6].toString(),sumAmt[7].toString(),
                sumAmt[8].toString(),sumAmt[9].toString(),sumAmt[10].toString(),sumAmt[11].toString(),
                sumAmt[12].toString(),sumAmt[13].toString(),sumAmt[14].toString(),sumAmt[15].toString(),
                sumAmt[16].toString(),sumAmt[17].toString(),sumAmt[18].toString(),sumAmt[19].toString(),
                sumAmt[20].toString(),sumAmt[21].toString(),sumAmt[22].toString(),sumAmt[23].toString(),
                sumAmt[24].toString(),sumAmt[25].toString(),
                "","","","","","",
                "","","",""};*/
        String[] s2row4 = {"合计", "", "", "", "", "", "", "", "", "", "", "", "",
                "", "", "", "", "", "", "", sumAmt[30].toString(),
                sumAmt[1].toString(), sumAmt[2].toString(), sumAmt[3].toString(), sumAmt[4].toString(), sumAmt[5].toString(),
                sumAmt[6].toString(), sumAmt[7].toString(), sumAmt[8].toString(), sumAmt[9].toString(), sumAmt[10].toString(), sumAmt[0].toString(),
                sumAmt[11].toString(), sumAmt[12].toString(), sumAmt[13].toString(), sumAmt[14].toString(), sumAmt[15].toString(),
                sumAmt[16].toString(), sumAmt[17].toString(), sumAmt[18].toString(), sumAmt[19].toString(), sumAmt[20].toString(),
                sumAmt[21].toString(), sumAmt[22].toString(),/*sumAmt[23].toString(),sumAmt[24].toString(),
                */sumAmt[25].toString(), sumAmt[32].toString(),
                "", "", sumAmt[31].toString(), sumAmt[26].toString(), sumAmt[27].toString(), sumAmt[28].toString(), sumAmt[29].toString(),
                "", "", "", "", "", "", "", "", "", "", "", ""};

        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            //20-45 字符串
            if ((i >= 20 && i <= 45) || (i >= 48 && i <= 51)) {
                tempCell.setCellValue(Double.parseDouble(s2row4[i]));
            } else {
                tempCell.setCellValue(s2row4[i]);
            }

            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet6.addMergedRegion(new CellRangeAddress(rowNum6[0] - 1, rowNum6[0] - 1, 0, 6));

        return hssfWorkbook;
    }


    /**
     * 第八个sheet 调度单明细
     *
     * @param hssfWorkbook           excel
     * @param bmsYfbillmainExportAll 数据
     * @param dicMap                 字典
     */
    public SXSSFWorkbook distributionDetailsSummary3(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll,
                                                     Map<String, String> dicMap, List<Long> ids) {
        String tenantid= RequestContext.getTenantId();
        // 第7个sheet
        Sheet sheet6 = hssfWorkbook.createSheet("作业单明细");
        // 背景色的设定
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum6 = {0};

        //设置列宽
        for (int i = 0; i < 19; i++) {
            sheet6.setColumnWidth(i, 2700);
        }

        //第1行
        Row s6r2 = sheet6.createRow(rowNum6[0]++);
        s6r2.setHeight((short) 500);
        String[] s6row2 = {"订单号", "虚拟调度单号", "单据所属", "客户编号", "客户名称", "门店编码", "门店名称", "总件数",
                "总重量", "总体积", "总货值", "总箱数", "是否基数外门店", "是否超基数公里", "超公里数", "距离最近门店公里数", "目的省", "目的市", "目的区"
        };
        for (int i = 0; i < s6row2.length; i++) {
            Cell tempCell = s6r2.createCell(i);
            tempCell.setCellValue(s6row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        // 创建数据行，获取入库明细
        List<BmsYsBillExportInfo> stkList = yfBillExportMapper.newDistributionDetailsSummary3(tenantid, ids);
        Map<String, SysDept> companyMap = textConversionUtil.getCompanyMap("1", stkList.stream().filter(e -> e.getCompanyId() != null).map(e -> e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        stkList.forEach(e -> {
            if (e.getCompanyId() != null && companyMap.containsKey(e.getCompanyId().toString())) {
                e.setCompanyName(companyMap.get(e.getCompanyId().toString()).getDeptName());
            }
        });
        for (BmsYsBillExportInfo info : stkList) {
            Row s2r3 = sheet6.createRow(rowNum6[0]++);
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s6row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        //订单号
                        tempCell.setCellValue(info.getRelateCode());
                        break;
                    case 1:
                        //虚拟调度单号
                        tempCell.setCellValue(info.getVirtualOrderNo());
                        break;
                    case 2:
                        //单据所属
                        tempCell.setCellValue(info.getCompanyName());
                        break;
                    case 3:
                        //客户编码
                        tempCell.setCellValue(info.getClientCode());
                        break;
                    case 4:
                        //客户名称
                        tempCell.setCellValue(info.getClientName());
                        break;
                    case 5:
                        //门店编码
                        tempCell.setCellValue(info.getStoreCode());
                        break;
                    case 6:
                        //门店名称
                        tempCell.setCellValue(info.getReceivingStore());
                        break;
                    case 7:
                        //总件数
                        tempCell.setCellValue(Double.parseDouble(info.getTotalNumber().toString()));
                        break;
                    case 8:
                        //总重量
                        tempCell.setCellValue(Double.parseDouble(info.getTotalWeight().toString()));
                        break;
                    case 9:
                        // 总体积
                        tempCell.setCellValue(Double.parseDouble(info.getTotalVolume().toString()));
                        break;
                    case 10:
                        //总货值
                        tempCell.setCellValue(Double.parseDouble(info.getCargoValue().toString()));
                        break;
                    case 11:
                        //总箱数
                        tempCell.setCellValue(Double.parseDouble(info.getTotalBoxes().toString()));
                        break;
                    case 12:
                        //是否基数外门店
                        tempCell.setCellValue(info.getIfBaseStores());
                        break;
                    case 13:
                        // 是否超基数公里
                        tempCell.setCellValue(info.getIfSuperBaseKilometer());
                        break;
                    case 14:
                        // 超公里数
                        tempCell.setCellValue(info.getStoreDistanceKilometer());
                        break;
                    case 15:
                        // 距离最近门店公里数
                        tempCell.setCellValue(Double.parseDouble(info.getNearStoreKm().toString()));
                        break;
                    case 16:
                        // 目的省
                        tempCell.setCellValue(info.getDestinationProvince());
                        break;
                    case 17:
                        // 目的市
                        tempCell.setCellValue(info.getDestinationCity());
                        break;
                    case 18:
                        // 目的区
                        tempCell.setCellValue(info.getDestinationArea());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }
        }
        return hssfWorkbook;
    }


    /**
     * 第九个sheet 报单明细2
     *
     * @param hssfWorkbook           excel
     * @param bmsYfbillmainExportAll 数据
     * @param dicMap                 字典
     */
    public SXSSFWorkbook distributionDetailsSummary4(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, Long id) {
        String tenantid= RequestContext.getTenantId();
        // 第7个sheet
        Sheet sheet6 = hssfWorkbook.createSheet("报单明细2");
        // 背景色的设定
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.SEA_GREEN.getIndex());
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum6 = {0};
        //设置列宽
        for (int i = 0; i < 19; i++) {
            sheet6.setColumnWidth(i, 2700);
        }
        //第1行
        Row s6r2 = sheet6.createRow(rowNum6[0]++);
        s6r2.setHeight((short) 500);
        String[] s6row2 = {"线路编码", "线路名称", "起始日期", "终止日期", "期间报单天数"};

        for (int i = 0; i < s6row2.length; i++) {
            Cell tempCell = s6r2.createCell(i);
            tempCell.setCellValue(s6row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        // 创建数据行，获取入库明细
        String lastId = "";
        String sumId = "";
        Integer row = 0;
        List<BmsYsBillExportInfo> stkList = yfBillExportMapper.distributionDetailsSummary4(tenantid, id);
        for (BmsYsBillExportInfo info : stkList) {
            Row s2r3 = sheet6.createRow(rowNum6[0]++);
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s6row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getLineCode());
                        break;
                    case 1:
                        tempCell.setCellValue(info.getLineName());
                        break;
                    case 2:
                        tempCell.setCellValue(info.getFinishDateBegin());
                        break;
                    case 3:
                        tempCell.setCellValue(info.getFinishDateEnd());
                        break;
                    case 4:
                        tempCell.setCellValue(info.getDistributionNum() == null ? 0 : info.getDistributionNum());
                        break;
                    default:
                        tempCell.setCellValue("");
                }

                tempCell.setCellStyle(commonStyle);
            }
        }
        return hssfWorkbook;
    }


    /**
     * 第十个sheet 门店配送明细 （主营）
     *
     * @param hssfWorkbook           excel
     * @param bmsYfbillmainExportAll 导出数据
     * @param dicMap                 字典
     */
    public SXSSFWorkbook distributionDetailsSummary5(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, Long id) {
        String tenantid= RequestContext.getTenantId();
        // 第7个sheet
        Sheet sheet6 = hssfWorkbook.createSheet("门店配送明细");
        // 背景色的设定
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        sheet2HeaderStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.SEA_GREEN.getIndex());
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum6 = {0};
        //设置列宽
        for (int i = 0; i < 19; i++) {
            sheet6.setColumnWidth(i, 2700);
        }
        //第1行
        Row s6r2 = sheet6.createRow(rowNum6[0]++);
        s6r2.setHeight((short) 500);
        String[] s6row2 = {"门店编码", "门店名称", "线路编码", "所属线路", "开业日期", "本周期配送天数", "是否超公里", "超公里",
                "是否基数外门店", "起始日期", "终止日期"};

        for (int i = 0; i < s6row2.length; i++) {
            Cell tempCell = s6r2.createCell(i);
            tempCell.setCellValue(s6row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        // 创建数据行，获取入库明细
        List<BmsYsBillExportInfo> stkList = yfBillExportMapper.distributionDetailsSummary5(tenantid, id);
        for (BmsYsBillExportInfo info : stkList) {
            Row s2r3 = sheet6.createRow(rowNum6[0]++);
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s6row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getStoreCode());
                        break;
                    case 1:
                        tempCell.setCellValue(info.getReceivingStore());
                        break;
                    case 2:
                        tempCell.setCellValue(info.getLineCode());
                        break;
                    case 3:
                        tempCell.setCellValue(info.getRouteName());
                        break;
                    case 4:
                        tempCell.setCellValue(info.getFoundTime());
                        break;
                    case 5:
                        tempCell.setCellValue(info.getDistributionNum());
                        break;
                    case 6:
                        tempCell.setCellValue(info.getIfSuperBaseKilometer() == null ? "否" : "0".equals(info.getIfSuperBaseKilometer()) ? "否" : "是");
                        break;
                    case 7:
                        tempCell.setCellValue(info.getStoreDistanceKilometer());
                        break;
                    case 8:
                        tempCell.setCellValue(info.getIfBaseStores() == null ? "否" : "0".equals(info.getIfBaseStores()) ? "否" : "是");
                        break;
                    case 9:
                        tempCell.setCellValue(info.getFinishDateBegin());
                        break;
                    case 10:
                        tempCell.setCellValue(info.getFinishDateEnd());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }
        }
        return hssfWorkbook;
    }


    /**
     * 第十一个sheet 配送明细1
     *
     * @param hssfWorkbook           excel
     * @param bmsYfbillmainExportAll 导出数据
     * @param dicMap                 字典
     */

    public SXSSFWorkbook distributionDetailsSummary1(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, List<Long> ids, String typeName) {
        String tenantid= RequestContext.getTenantId();
        // 第6个sheet
        Sheet sheet5 = hssfWorkbook.createSheet("配送明细1");
        // 背景色的设定
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum5 = {0};

        //设置列宽
        for (int i = 0; i < 36; i++) {
            sheet5.setColumnWidth(i, 2000);
        }
        //第1行
        Row s5r2 = sheet5.createRow(rowNum5[0]++);
        String[] s5row2 = {"线路", "店名\\日期", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"
                , "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "合计", "配送店次合计", "配送箱(筐)数合计"};


        // 数据行 ，获取配送单据的信息
        // 根据账单查询所有的费用的账期分组（可能存在调整单）
        List<BmsYsBillExportInfo> stkList = yfBillExportMapper.getDistribution1GoodsDetailByBillId(tenantid, ids);
        SimpleDateFormat simp = new SimpleDateFormat("yyyy-MM");
        String[] s5row1 = {bmsYfbillmainExportAll.getBillDate() + "月" + typeName + "配送账单明细", "店名\\日期", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"
                , "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "合计", "配送店次合计", "配送箱(筐)数合计"};
        for (int i = 0; i < s5row1.length; i++) {
            Cell tempCell = s5r2.createCell(i);
            tempCell.setCellValue(s5row1[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        //第1行
        Row s5r3 = sheet5.createRow(rowNum5[0]++);
        sheet5.addMergedRegion(new CellRangeAddress(0, 0, 0, 35));
        for (int i = 0; i < s5row2.length; i++) {
            Cell tempCell = s5r3.createCell(i);
            tempCell.setCellValue(s5row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        if (stkList.isEmpty()) {
            return hssfWorkbook;
        }
        SimpleDateFormat sml = new SimpleDateFormat("dd");
        for (BmsYsBillExportInfo info : stkList) {
            Row s2r3 = sheet5.createRow(rowNum5[0]++);
            s2r3.setHeight((short) 500);
            List<BmsYsBillExportInfo> detailList = yfBillExportMapper.getDetailDays(tenantid, info);
            BigDecimal sumDay = BigDecimal.ZERO;
            for (BmsYsBillExportInfo detail : detailList) {
                detail.setDay(Integer.valueOf(sml.format(detail.getSigningDate())));
                sumDay = sumDay.add(detail.getTotalNumber());
                switch (detail.getDay()) {
                    case 1:
                        info.setNumber1(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 2:
                        info.setNumber2(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 3:
                        info.setNumber3(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 4:
                        info.setNumber4(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 5:
                        info.setNumber5(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 6:
                        info.setNumber6(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 7:
                        info.setNumber7(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 8:
                        info.setNumber8(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 9:
                        info.setNumber9(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 10:
                        info.setNumber10(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 11:
                        info.setNumber11(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 12:
                        info.setNumber12(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 13:
                        info.setNumber13(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 14:
                        info.setNumber14(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 15:
                        info.setNumber15(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 16:
                        info.setNumber16(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 17:
                        info.setNumber17(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 18:
                        info.setNumber18(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 19:
                        info.setNumber19(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 20:
                        info.setNumber20(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 21:
                        info.setNumber21(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 22:
                        info.setNumber22(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 23:
                        info.setNumber23(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 24:
                        info.setNumber24(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 25:
                        info.setNumber25(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 26:
                        info.setNumber26(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 27:
                        info.setNumber27(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 28:
                        info.setNumber28(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 29:
                        info.setNumber29(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 30:
                        info.setNumber30(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 31:
                        info.setNumber31(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    default:

                }
            }
            info.setSumDay(sumDay.intValue());
            for (int i = 0; i < s5row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getLineName());
                        break;
                    case 1:
                        tempCell.setCellValue(info.getReceivingStore());
                        break;
                    case 2:
                        tempCell.setCellValue(info.getNumber1() == null ? 0 : Double.parseDouble(info.getNumber1()));
                        break;
                    case 3:
                        tempCell.setCellValue(info.getNumber2() == null ? 0 : Double.parseDouble(info.getNumber2()));
                        break;
                    case 4:
                        tempCell.setCellValue(info.getNumber3() == null ? 0 : Double.parseDouble(info.getNumber3()));
                        break;
                    case 5:
                        tempCell.setCellValue(info.getNumber4() == null ? 0 : Double.parseDouble(info.getNumber4()));
                        break;
                    case 6:
                        tempCell.setCellValue(info.getNumber5() == null ? 0 : Double.parseDouble(info.getNumber5()));
                        break;
                    case 7:
                        tempCell.setCellValue(info.getNumber6() == null ? 0 : Double.parseDouble(info.getNumber6()));
                        break;
                    case 8:
                        tempCell.setCellValue(info.getNumber7() == null ? 0 : Double.parseDouble(info.getNumber7()));
                        break;
                    case 9:
                        tempCell.setCellValue(info.getNumber8() == null ? 0 : Double.parseDouble(info.getNumber8()));
                        break;
                    case 10:
                        tempCell.setCellValue(info.getNumber9() == null ? 0 : Double.parseDouble(info.getNumber9()));
                        break;
                    case 11:
                        tempCell.setCellValue(info.getNumber10() == null ? 0 : Double.parseDouble(info.getNumber10()));
                        break;
                    case 12:
                        tempCell.setCellValue(info.getNumber11() == null ? 0 : Double.parseDouble(info.getNumber11()));
                        break;
                    case 13:
                        tempCell.setCellValue(info.getNumber12() == null ? 0 : Double.parseDouble(info.getNumber12()));
                        break;
                    case 14:
                        tempCell.setCellValue(info.getNumber13() == null ? 0 : Double.parseDouble(info.getNumber13()));
                        break;
                    case 15:
                        tempCell.setCellValue(info.getNumber14() == null ? 0 : Double.parseDouble(info.getNumber14()));
                        break;
                    case 16:
                        tempCell.setCellValue(info.getNumber15() == null ? 0 : Double.parseDouble(info.getNumber15()));
                        break;
                    case 17:
                        tempCell.setCellValue(info.getNumber16() == null ? 0 : Double.parseDouble(info.getNumber16()));
                        break;
                    case 18:
                        tempCell.setCellValue(info.getNumber17() == null ? 0 : Double.parseDouble(info.getNumber17()));
                        break;
                    case 19:
                        tempCell.setCellValue(info.getNumber18() == null ? 0 : Double.parseDouble(info.getNumber18()));
                        break;
                    case 20:
                        tempCell.setCellValue(info.getNumber19() == null ? 0 : Double.parseDouble(info.getNumber19()));
                        break;
                    case 21:
                        tempCell.setCellValue(info.getNumber20() == null ? 0 : Double.parseDouble(info.getNumber20()));
                        break;
                    case 22:
                        tempCell.setCellValue(info.getNumber21() == null ? 0 : Double.parseDouble(info.getNumber21()));
                        break;
                    case 23:
                        tempCell.setCellValue(info.getNumber22() == null ? 0 : Double.parseDouble(info.getNumber22()));
                        break;
                    case 24:
                        tempCell.setCellValue(info.getNumber23() == null ? 0 : Double.parseDouble(info.getNumber23()));
                        break;
                    case 25:
                        tempCell.setCellValue(info.getNumber24() == null ? 0 : Double.parseDouble(info.getNumber24()));
                        break;
                    case 26:
                        tempCell.setCellValue(info.getNumber25() == null ? 0 : Double.parseDouble(info.getNumber25()));
                        break;
                    case 27:
                        tempCell.setCellValue(info.getNumber26() == null ? 0 : Double.parseDouble(info.getNumber26()));
                        break;
                    case 28:
                        tempCell.setCellValue(info.getNumber27() == null ? 0 : Double.parseDouble(info.getNumber27()));
                        break;
                    case 29:
                        tempCell.setCellValue(info.getNumber28() == null ? 0 : Double.parseDouble(info.getNumber28()));
                        break;
                    case 30:
                        tempCell.setCellValue(info.getNumber29() == null ? 0 : Double.parseDouble(info.getNumber29()));
                        break;
                    case 31:
                        tempCell.setCellValue(info.getNumber30() == null ? 0 : Double.parseDouble(info.getNumber30()));
                        break;
                    case 32:
                        tempCell.setCellValue(info.getNumber31() == null ? 0 : Double.parseDouble(info.getNumber31()));
                        break;
                    case 33:
                        tempCell.setCellValue(info.getSumDay() == null ? 0 : info.getSumDay());
                        break;
                    case 34:
                        tempCell.setCellValue(info.getOfficeTimes());
                        break;
                    case 35:
                        tempCell.setCellValue(Double.parseDouble(info.getTotalBoxes().toString()));
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }
        }

        return hssfWorkbook;
    }


    /**
     * 第十三个sheet 线路明细
     *
     * @param hssfWorkbook           excel
     * @param bmsYfbillmainExportAll 数据
     * @param dicMap                 字典
     */
    public SXSSFWorkbook distributionDetailsSummary7(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid= RequestContext.getTenantId();
        // 第7个sheet
        Sheet sheet6 = hssfWorkbook.createSheet("线路明细");
        // 背景色的设定
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum6 = {0};
        //设置列宽
        for (int i = 0; i < 19; i++) {
            sheet6.setColumnWidth(i, 3000);
        }
        //第1行
        Row s6r2 = sheet6.createRow(rowNum6[0]++);
        s6r2.setHeight((short) 500);
        String[] s6row1 = {bmsYfbillmainExportAll.getSheet1Name(), "", "", "",
                "", "", ""
        };
        for (int i = 0; i < s6row1.length; i++) {
            Cell tempCell = s6r2.createCell(i);
            tempCell.setCellValue(s6row1[i]);
            tempCell.setCellStyle(commonStyle);
        }
        sheet6.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));

        //第2行
        Row s6r3 = sheet6.createRow(rowNum6[0]++);
        String[] s6row2 = {"线路名称", "覆盖门店数", "配送店次", "配送趟次",
                "基数内配送店次", "基数外配送店次", "非基数内外配送店次"
        };
        for (int i = 0; i < s6row2.length; i++) {
            Cell tempCell = s6r3.createCell(i);
            tempCell.setCellValue(s6row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
//        List<BmsYsBillExportInfo> stkList = yfBillExportMapper.newDistributionDetailsSummary7(tenantid, ids);
        List<BmsYsBillExportInfo> stkList = new ArrayList<>();
        for (BmsYsBillExportInfo info : stkList) {
            Row s2r3 = sheet6.createRow(rowNum6[0]++);
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s6row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        // 线路名称
                        tempCell.setCellValue(info.getLineName());
                        break;
                    case 1:
                        // 覆盖门店数
                        tempCell.setCellValue(info.getStoreNum());
                        break;
                    case 2:
                        // 配送店次
                        tempCell.setCellValue(info.getCjsNum());
                        break;
                    case 3:
                        // 配送趟次
                        tempCell.setCellValue(info.getPstcNum());
                        break;
                    case 4:
                        //基数内配送店次
                        tempCell.setCellValue(info.getHeadOfficeTimes());
                        break;
                    case 5:
                        //基数外配送店次
                        tempCell.setCellValue(info.getBodyOfficeTimes());
                        break;
                    case 6:
                        // 非基数配送店次
                        tempCell.setCellValue(info.getOfficeTimes());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }
        }
        return hssfWorkbook;
    }


    /**
     * 第十四个sheet 配送明细2（温度区间）
     *
     * @param hssfWorkbook           excel
     * @param bmsYfbillmainExportAll 数据
     * @param dicMap                 字典
     */

    public SXSSFWorkbook distributionDetailsSummary9(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, Long id) {
        String tenantid= RequestContext.getTenantId();
        // 第6个sheet
        Sheet sheet5 = hssfWorkbook.createSheet("配送明细2(温区)");
        // 背景色的设定
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.SEA_GREEN.getIndex());
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum5 = {0};

        //设置列宽
        for (int i = 0; i < 36; i++) {
            sheet5.setColumnWidth(i, 2000);
        }

        //第1行
        Row s5r2 = sheet5.createRow(rowNum5[0]++);

        String[] s5row2 = {"线路", "温区\\日期", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"
                , "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "合计"};


        // 数据行 ，获取配送单据的信息
        // 根据账单查询所有的费用的账期分组（可能存在调整单）
        List<BmsYsBillExportInfo> stkList = yfBillExportMapper.distributionDetailsSummary9(tenantid, id);
        if (CollUtil.isEmpty(stkList)) {
            return hssfWorkbook;
        }
        SimpleDateFormat simp = new SimpleDateFormat("yyyy-MM");
        String[] s5row1 = {bmsYfbillmainExportAll.getBillDate() + "月协同配送账单明细", "店名\\日期", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"
                , "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "合计"};
        for (int i = 0; i < s5row1.length; i++) {
            Cell tempCell = s5r2.createCell(i);
            tempCell.setCellValue(s5row1[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        //第1行
        Row s5r3 = sheet5.createRow(rowNum5[0]++);
        sheet5.addMergedRegion(new CellRangeAddress(0, 0, 0, 33));
        for (int i = 0; i < s5row2.length; i++) {
            Cell tempCell = s5r3.createCell(i);
            tempCell.setCellValue(s5row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        if (stkList.isEmpty()) {
            return hssfWorkbook;
        }
        SimpleDateFormat sml = new SimpleDateFormat("dd");
        for (BmsYsBillExportInfo info : stkList) {
            info.setId(id.toString());
            Row s2r3 = sheet5.createRow(rowNum5[0]++);
            s2r3.setHeight((short) 500);
            List<BmsYsBillExportInfo> detailList = yfBillExportMapper.getDetailDays2(tenantid, info);
            BigDecimal sumDay = BigDecimal.ZERO;
            for (BmsYsBillExportInfo detail : detailList) {
                detail.setDay(Integer.valueOf(sml.format(detail.getSigningDate())));
                sumDay = sumDay.add(detail.getTotalNumber());
                switch (detail.getDay()) {
                    case 1:
                        info.setNumber1(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 2:
                        info.setNumber2(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 3:
                        info.setNumber3(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 4:
                        info.setNumber4(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 5:
                        info.setNumber5(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 6:
                        info.setNumber6(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 7:
                        info.setNumber7(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 8:
                        info.setNumber8(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 9:
                        info.setNumber9(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 10:
                        info.setNumber10(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 11:
                        info.setNumber11(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 12:
                        info.setNumber12(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 13:
                        info.setNumber13(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 14:
                        info.setNumber14(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 15:
                        info.setNumber15(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 16:
                        info.setNumber16(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 17:
                        info.setNumber17(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 18:
                        info.setNumber18(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 19:
                        info.setNumber19(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 20:
                        info.setNumber20(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 21:
                        info.setNumber21(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 22:
                        info.setNumber22(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 23:
                        info.setNumber23(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 24:
                        info.setNumber24(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 25:
                        info.setNumber25(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 26:
                        info.setNumber26(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 27:
                        info.setNumber27(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 28:
                        info.setNumber28(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 29:
                        info.setNumber29(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 30:
                        info.setNumber30(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    case 31:
                        info.setNumber31(detail.getTotalNumber() == null ? "" : detail.getTotalNumber().toString());
                        break;
                    default:

                }
            }
            info.setSumDay(sumDay.intValue());
            for (int i = 0; i < s5row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getLineName());
                        break;
                    case 1:
                        tempCell.setCellValue(info.getReceivingStore());
                        break;
                    case 2:
                        tempCell.setCellValue(info.getNumber1() == null ? "" : info.getNumber1());
                        break;
                    case 3:
                        tempCell.setCellValue(info.getNumber2() == null ? "" : info.getNumber2());
                        break;
                    case 4:
                        tempCell.setCellValue(info.getNumber3() == null ? "" : info.getNumber3());
                        break;
                    case 5:
                        tempCell.setCellValue(info.getNumber4() == null ? "" : info.getNumber4());
                        break;
                    case 6:
                        tempCell.setCellValue(info.getNumber5() == null ? "" : info.getNumber5());
                        break;
                    case 7:
                        tempCell.setCellValue(info.getNumber6() == null ? "" : info.getNumber6());
                        break;
                    case 8:
                        tempCell.setCellValue(info.getNumber7() == null ? "" : info.getNumber7());
                        break;
                    case 9:
                        tempCell.setCellValue(info.getNumber8() == null ? "" : info.getNumber8());
                        break;
                    case 10:
                        tempCell.setCellValue(info.getNumber9() == null ? "" : info.getNumber9());
                        break;
                    case 11:
                        tempCell.setCellValue(info.getNumber10() == null ? "" : info.getNumber10());
                        break;
                    case 12:
                        tempCell.setCellValue(info.getNumber11() == null ? "" : info.getNumber11());
                        break;
                    case 13:
                        tempCell.setCellValue(info.getNumber12() == null ? "" : info.getNumber12());
                        break;
                    case 14:
                        tempCell.setCellValue(info.getNumber13() == null ? "" : info.getNumber13());
                        break;
                    case 15:
                        tempCell.setCellValue(info.getNumber14() == null ? "" : info.getNumber14());
                        break;
                    case 16:
                        tempCell.setCellValue(info.getNumber15() == null ? "" : info.getNumber15());
                        break;
                    case 17:
                        tempCell.setCellValue(info.getNumber16() == null ? "" : info.getNumber16());
                        break;
                    case 18:
                        tempCell.setCellValue(info.getNumber17() == null ? "" : info.getNumber17());
                        break;
                    case 19:
                        tempCell.setCellValue(info.getNumber18() == null ? "" : info.getNumber18());
                        break;
                    case 20:
                        tempCell.setCellValue(info.getNumber19() == null ? "" : info.getNumber19());
                        break;
                    case 21:
                        tempCell.setCellValue(info.getNumber20() == null ? "" : info.getNumber20());
                        break;
                    case 22:
                        tempCell.setCellValue(info.getNumber21() == null ? "" : info.getNumber21());
                        break;
                    case 23:
                        tempCell.setCellValue(info.getNumber22() == null ? "" : info.getNumber22());
                        break;
                    case 24:
                        tempCell.setCellValue(info.getNumber23() == null ? "" : info.getNumber23());
                        break;
                    case 25:
                        tempCell.setCellValue(info.getNumber24() == null ? "" : info.getNumber24());
                        break;
                    case 26:
                        tempCell.setCellValue(info.getNumber25() == null ? "" : info.getNumber25());
                        break;
                    case 27:
                        tempCell.setCellValue(info.getNumber26() == null ? "" : info.getNumber26());
                        break;
                    case 28:
                        tempCell.setCellValue(info.getNumber27() == null ? "" : info.getNumber27());
                        break;
                    case 29:
                        tempCell.setCellValue(info.getNumber28() == null ? "" : info.getNumber28());
                        break;
                    case 30:
                        tempCell.setCellValue(info.getNumber29() == null ? "" : info.getNumber29());
                        break;
                    case 31:
                        tempCell.setCellValue(info.getNumber30() == null ? "" : info.getNumber30());
                        break;
                    case 32:
                        tempCell.setCellValue(info.getNumber31() == null ? "" : info.getNumber31());
                        break;
                    case 33:
                        tempCell.setCellValue(info.getSumDay() == null ? 0 : info.getSumDay());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }
        }

        return hssfWorkbook;
    }


    //导出应收账单主表列表
    @Log(title = "应付账单主表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportDetail")
    @MenuAuthority(code = "应付账单管理-账单下载")
    public ResponseResult<String> selectBmsYsBillExport(HttpServletRequest request, @RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        Long id = JSONObject.parseObject(json).getLong("id");
        BmsYsBillExportInfo billInfo = yfBillExportMapper.getBillingInformation(loginUserInfo.getTenantid().toString(), id);
        if(billInfo==null){
            throw new BusinessException("未获取到账单信息");
        }
        String flieName = billInfo.getCarrierName() + "_" + billInfo.getClientName() + "_应付账单费用详情_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        return exportUtil.getOutClassNewYfBillSheets(token, flieName, "应付账单管理", id, BmsYfbillmainExportAll.class, userId -> {
            // 根据仓库名称查询仓库编码
            return new ArrayList<>();
        });
    }

    public FileBaseDto exportSheets(Long id) {
        FileBaseDto fileBaseDto = new FileBaseDto();
        String token = RequestContext.getToken();
        String tenantid= RequestContext.getTenantId();
        try {
            List<BmsYfbillmain> descendantsByBillId = bmsYfbillmainService.findDescendantsByBillId(token, id.toString());
            List<Long> list = new ArrayList<>();
            if (!descendantsByBillId.isEmpty()) {
                List<Long> collect = descendantsByBillId.stream().map(BmsYfbillmain::getId).toList();
                list.addAll(collect);
            }
            // 根据账单id获取账单数据
            BmsYfbillmainExportAll bmsYfbillmainExportAll = bmsYfbillmainService.getMergeYsExportAll(token, id, list);
            list.add(id);
            List<BmsYfbillmainExportAll> extionFeeList = yfBillExportMapper.selectBmsYfBillExtionFee(tenantid, list);
            if (CollUtil.isNotEmpty(extionFeeList)) {
                //仓储单客诉理赔费
                if (extionFeeList.stream().filter(e -> e.getBillType().equals(BillTypeEnum.STOCK_BILL.getIntValue())).map(BmsYfbillmainExportAll::getExceptionFee).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ZERO) != 0) {
                    bmsYfbillmainExportAll.setCcExceptionFee(extionFeeList.stream().filter(e -> e.getBillType().equals(BillTypeEnum.STOCK_BILL.getIntValue())).map(BmsYfbillmainExportAll::getExceptionFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                    bmsYfbillmainExportAll.setCcFeeSum(bmsYfbillmainExportAll.getCcFeeSum().add(bmsYfbillmainExportAll.getCcExceptionFee()));
                }
                //运输单客诉理赔费
                if (extionFeeList.stream().filter(e -> e.getBillType().equals(BillTypeEnum.TRANS_BILL.getIntValue())).map(BmsYfbillmainExportAll::getExceptionFee).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ZERO) != 0) {
                    bmsYfbillmainExportAll.setPsExceptionFee(extionFeeList.stream().filter(e -> e.getBillType().equals(BillTypeEnum.TRANS_BILL.getIntValue())).map(BmsYfbillmainExportAll::getExceptionFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                    bmsYfbillmainExportAll.setPsFeeSum(bmsYfbillmainExportAll.getPsFeeSum().add(bmsYfbillmainExportAll.getPsExceptionFee()));
                }
            }
            if (bmsYfbillmainExportAll.getCcExceptionFee() == null) {
                bmsYfbillmainExportAll.setCcExceptionFee(BigDecimal.ZERO);
            }
            if (bmsYfbillmainExportAll.getPsExceptionFee() == null) {
                bmsYfbillmainExportAll.setPsExceptionFee(BigDecimal.ZERO);
            }
            Map<String, String> dicMap = textConversionUtil.getDictByType("order_source,fee_source", "2");
            Map<String, String> dicMapValue = textConversionUtil.getDictByType("order_source,fee_source", "1");
            Map<String, DicDto> feeDicMap = textConversionUtil.mdmDicMap("yf_transport_othercost,yf_storage_othercost,yf_transport_basecost,yf_storage_basecost", 1, SysCodeEnum.BMS);
            if(!feeDicMap.isEmpty()){
                Map<String, String> collect = feeDicMap.values().stream().collect(Collectors.toMap(k -> StrUtil.format("{}{}", k.getTypename(), k.getParameter2()), PubIdreplacePO::getItemname, (k, v) -> k));
                dicMap.putAll(collect);
            }

            //Excel
            SXSSFWorkbook hssfWorkbook = new SXSSFWorkbook();
            //第一个sheet 账单汇总
            hssfWorkbook = billSummary(hssfWorkbook, bmsYfbillmainExportAll, dicMap, id, list);
            String typeName = "";
            if (!"5".equals(bmsYfbillmainExportAll.getBillTypeStr())) {
                if ((bmsYfbillmainExportAll.getExpensesType() != null && bmsYfbillmainExportAll.getExpensesType().equals(OperTypeEnum.OUT_FEE.getIntValue())) || (bmsYfbillmainExportAll.getCcFeeSum() != null && bmsYfbillmainExportAll.getCcFeeSum().compareTo(BigDecimal.ZERO) > 0)) {
                    //第二个sheet 存储费主信息
                    hssfWorkbook = storageSummary(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list);

                    //第三个sheet 存储服务费明细信息
                    /*hssfWorkbook=storageDetailSummary(hssfWorkbook,bmsYfbillmainExportAll,dicMap,id);*/

                    //第四个sheet 入库明细
                    hssfWorkbook = inWarehousDetailSummary(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list);

                    //第五个sheet 出库明细
                    hssfWorkbook = outWarehousDetailSummary(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list);
                }

                // 运输
                if (bmsYfbillmainExportAll.getExpensesType() != null && bmsYfbillmainExportAll.getExpensesType().equals(OperTypeEnum.ORDER_FEE.getIntValue()) || (bmsYfbillmainExportAll.getPsFeeSum() != null && bmsYfbillmainExportAll.getPsFeeSum().compareTo(BigDecimal.ZERO) > 0)) {
                    typeName = "(协同)";
                    // 线路明细
                    hssfWorkbook = distributionDetailsSummary7(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list);
                    // 协同
                    if (bmsYfbillmainExportAll.getClientType() == null || (bmsYfbillmainExportAll.getClientType() != null && bmsYfbillmainExportAll.getClientType().equals(ClientTypeEnum.COOPERATE.getIntValue()))) {
                        // 配送明细
                        hssfWorkbook = distributionDetailsSummary(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list);
                        // 费用单维度
                        hssfWorkbook = distributionDetailsSummary2(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list);
                        // 调度单明细
                        hssfWorkbook = distributionDetailsSummary3(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list);
                        // 配送明细1
                        hssfWorkbook = distributionDetailsSummary1(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list, "协同");
                    }
                    // 主营
                    if ((bmsYfbillmainExportAll.getClientType() != null && bmsYfbillmainExportAll.getClientType().equals(ClientTypeEnum.MAJOR.getIntValue()))) {
                        typeName = "(主营)";
                        // 配送门店汇总
                        hssfWorkbook = distributionDetailsSummary(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list);
                        // 费用单维度
                        hssfWorkbook = distributionDetailsSummary2(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list);
                        // 调度单明细
                        hssfWorkbook = distributionDetailsSummary3(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list);
                        // 配送明细1
                        hssfWorkbook = distributionDetailsSummary1(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list, "主营");
                    }
                }
                //理赔费用明细sheet
                hssfWorkbook = billClaimsSheet(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list);
                //固定费
                hssfWorkbook = fixedDetailSummary(hssfWorkbook, bmsYfbillmainExportAll, dicMap, list);
            } else {
                typeName = "增值";
            }
            //第十个增值费明细sheet
            hssfWorkbook = valueAddedFeeSummary(hssfWorkbook, null, dicMapValue, list);
            try {
                BmsYsBillExportInfo bmsYsBillExportInfo = yfBillExportMapper.getBillingInformation(tenantid, id);
                FileOutputStream out = null;
                // 文件名
                String fileName = "应付账单-" + bmsYsBillExportInfo.getBillName() + typeName + ".xlsx";
                String absoluteFile = ExcelExportUtil.getAbsoluteFile(fileName);
                out = new FileOutputStream(absoluteFile);
                try {
                    ResponseResult<String> result = new ResponseResult<>(true, 0, fileName, fileName);
                    hssfWorkbook.write(out);
                    out.close();
                    File file = new File(absoluteFile);
                    String readableSize = FileUtil.readableFileSize(file.length());
                    fileBaseDto.setFileName(file.getName());
                    fileBaseDto.setFileSize(readableSize);
                    return fileBaseDto;
                } catch (IOException e) {
                    log.error("系统异常", e);
                    fileBaseDto.setErrMsg("系统异常");
                    return fileBaseDto;
                }

            } catch (FileNotFoundException e) {
                BmsYsBillExportInfo bmsYsBillExportInfo = yfBillExportMapper.getBillingInformation(tenantid, id);
                FileOutputStream out = null;
                // 文件名
                String fileName = "应付账单-" + bmsYsBillExportInfo.getBillName() + typeName + ".xlsx";
                String absoluteFileios = ExcelExportUtil.getAbsoluteFileios(fileName);
                out = new FileOutputStream(absoluteFileios);
                try {
                    ResponseResult<String> result = new ResponseResult<>(true, 0, fileName, fileName);
                    hssfWorkbook.write(out);
                    out.close();
                    File file = new File(absoluteFileios);
                    String readableSize = FileUtil.readableFileSize(file.length());
                    fileBaseDto.setFileName(file.getName());
                    fileBaseDto.setFileSize(readableSize);
                    return fileBaseDto;
                } catch (IOException e1) {
                    log.error("系统异常", e);
                    fileBaseDto.setErrMsg("系统异常");
                    return fileBaseDto;
                }
            }
        } catch (Exception e) {
            log.error("系统异常", e);
            fileBaseDto.setErrMsg("系统异常");
            return fileBaseDto;
        }
    }

    /**
     * 增值费汇总
     *
     * @param hssfWorkbook           hssf工作簿
     * @param bmsYsbillmainExportAll bms ysbillmain导出全部
     * @param dicMap                 dic映射
     * @return {@link SXSSFWorkbook}
     */
    public SXSSFWorkbook valueAddedFeeSummary(SXSSFWorkbook hssfWorkbook, BmsYsbillmainExportAll bmsYsbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid= RequestContext.getTenantId();
        List<BmsYsBillExportInfo> inWarList = yfBillExportMapper.getValueAddedFeeByBillId(tenantid, ids);
        if (CollUtil.isEmpty(inWarList)) {
            return hssfWorkbook;
        }
        // 第4个sheet
        Sheet sheet3 = hssfWorkbook.createSheet("增值费用明细");
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        for (BmsYsBillExportInfo bmsYsBillExportInfo : inWarList) {
            if (StrUtil.isNotEmpty(bmsYsBillExportInfo.getOrderSource()) && dicMap.containsKey("order_source" + bmsYsBillExportInfo.getOrderSource())) {
                bmsYsBillExportInfo.setOrderSourceName(dicMap.get("order_source" + bmsYsBillExportInfo.getOrderSource()));
            }
            if (bmsYsBillExportInfo.getFeeSource() != null && dicMap.containsKey("fee_source" + bmsYsBillExportInfo.getFeeSource())) {
                bmsYsBillExportInfo.setFeeSourceName(dicMap.get("fee_source" + bmsYsBillExportInfo.getFeeSource()));
            }
        }
        final int[] rowNum3 = {0};
        //第1行
        Row s2r2 = sheet3.createRow(rowNum3[0]++);
        s2r2.setHeight((short) 500);
        String[] s3row2 = {"计费单号", "关联单号", "计费时间", "增值类型", "费用一级类型",
                "费用二级类型", "金额", "数量", "单位", "费用来源", "单据来源", "发生时间", "仓库编码", "仓库名称", "费用备注"};

        for (int i = 0; i < s3row2.length; i++) {
            String name = s3row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            s3row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s3row2.length; i++) {
            Cell tempCell = s2r2.createCell(i);
            sheet3.setColumnWidth(i, 5000);
            tempCell.setCellValue(s3row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        // 创建数据行 入库明细
        BigDecimal[] sumAmt = {
                new BigDecimal("0"),
                new BigDecimal("0")
        };
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (BmsYsBillExportInfo info : inWarList) {
            Row s2r3 = sheet3.createRow(rowNum3[0]++);
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s3row2.length; i++) {
                List<BigDecimal> amtList = new ArrayList<>();
                Cell tempCell = s2r3.createCell(i);

                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getExpensesCode());
                        break;
                    case 1:
                        tempCell.setCellValue(info.getRelateCode());
                        break;
                    case 2:
                        String date = Objects.isNull(info.getChargingDate()) ? "" : sdf.format(info.getChargingDate());
                        tempCell.setCellValue(date);
                        break;
                    case 3:
                        tempCell.setCellValue(info.getFeeBelongDesc());
                        break;
                    case 4:
                        tempCell.setCellValue(info.getItemOlevelName());
                        break;
                    case 5:
                        tempCell.setCellValue(info.getItemName());
                        break;
                    case 6:
                        tempCell.setCellValue(info.getAmount() == null ? 0 : info.getAmount().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 7:
                        tempCell.setCellValue(info.getSkuNumber() == null ? 0 : Double.parseDouble(info.getSkuNumber().toString()));
                        break;
                    case 8:
                        tempCell.setCellValue(info.getUnit());
                        break;
                    case 9:
                        tempCell.setCellValue(info.getFeeSourceName());
                        break;
                    case 10:
                        tempCell.setCellValue(info.getOrderSourceName());
                        break;
                    case 11:
                        tempCell.setCellValue(info.getOperTime());
                        break;
                    case 12:
                        tempCell.setCellValue(info.getWarehouseCode());
                        break;
                    case 13:
                        tempCell.setCellValue(info.getWarehouseName());
                        break;
                    case 14:
                        tempCell.setCellValue(info.getRemark());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                if (PubNumEnum.six.getIntValue().equals(i)) {
                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }
            sumAmt[0] = sumAmt[0].add(info.getSkuNumber() == null ? BigDecimal.ZERO : info.getSkuNumber());
            sumAmt[1] = sumAmt[1].add(info.getAmount());
        }

        //最后一行 合计金额
        Row s2r4 = sheet3.createRow(rowNum3[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"汇总", "", "", "", "", "",
                sumAmt[1].toString(), sumAmt[0].toString(), "", "", "", "", "", "", ""};
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if (PubNumEnum.seven.getIntValue().equals(i) || PubNumEnum.six.getIntValue().equals(i)) {
                tempCell.setCellValue(Double.parseDouble(s2row4[i]));
            } else {
                tempCell.setCellValue(s2row4[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else if (PubNumEnum.six.getIntValue().equals(i)) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet3.addMergedRegion(new CellRangeAddress(rowNum3[0] - 1, rowNum3[0] - 1, 0, 5));
        return hssfWorkbook;
    }

    /**
     * 固定费sheet
     */
    public SXSSFWorkbook fixedDetailSummary(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid= RequestContext.getTenantId();
        List<BmsYsBillExportInfo> inWarList = yfBillExportMapper.getFixedExpensesByBillId(tenantid, ids);
        Map<String, String> dicMap2 = textConversionUtil.getDictByType("sys_fee_frequency", "1");
        if (CollUtil.isEmpty(inWarList)) {
            return hssfWorkbook;
        }
        // 第4个sheet
        Sheet sheet3 = hssfWorkbook.createSheet("固定费用明细");
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum3 = {0};


        for (BmsYsBillExportInfo bmsYsBillExportInfo : inWarList) {
            if (StrUtil.isNotEmpty(bmsYsBillExportInfo.getFrequency()) && dicMap.containsKey("frequency" + bmsYsBillExportInfo.getFrequency())) {
                bmsYsBillExportInfo.setFrequencyName(dicMap.get("frequency" + bmsYsBillExportInfo.getFrequency()));
            }
        }

        //第1行
        Row s2r2 = sheet3.createRow(rowNum3[0]++);
        s2r2.setHeight((short) 500);
        String[] s3row2 = {"计费单号", "费用项", "金额", "收费频次", "收费开始时间", "收费结束时间", "费用备注"};

        for (int i = 0; i < s3row2.length; i++) {
            String name = s3row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            s3row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s3row2.length; i++) {
            sheet3.setColumnWidth(i, 5000);
            Cell tempCell = s2r2.createCell(i);
            tempCell.setCellValue(s3row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        // 创建数据行 入库明细
        String lastId = "";
        Integer row = 1;
        BigDecimal[] sumAmt = {
                new BigDecimal("0")
        };
        for (BmsYsBillExportInfo info : inWarList) {
            Row s2r3 = sheet3.createRow(rowNum3[0]++);
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s3row2.length; i++) {
                List<BigDecimal> amtList = new ArrayList<>();
                Cell tempCell = s2r3.createCell(i);

                switch (i) {
                    case 0:
                        tempCell.setCellValue(info.getExpensesCode());
                        break;
                    case 1:
                        tempCell.setCellValue(info.getItemName());
                        break;
                    case 2:
                        tempCell.setCellValue(info.getAmount() == null ? 0 : info.getAmount().setScale(2, RoundingMode.HALF_UP).doubleValue());
                        break;
                    case 3:
                        tempCell.setCellValue(dicMap2.get("sys_fee_frequency" + info.getFrequency()));
                        break;
                    case 4:
                        tempCell.setCellValue(info.getStartDate());
                        break;
                    case 5:
                        tempCell.setCellValue(info.getEndDate());
                        break;
                    case 6:
                        tempCell.setCellValue(info.getRemarks());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                if (PubNumEnum.two.getIntValue().equals(i)) {
                    commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    tempCell.setCellStyle(commonStyle);
                } else {
                    tempCell.setCellStyle(commonStyle);
                }
            }
            sumAmt[0] = sumAmt[0].add(info.getAmount());
        }

        //最后一行 合计金额
        Row s2r4 = sheet3.createRow(rowNum3[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"汇总", "", sumAmt[0].toString(), "", "", "", ""};
        for (int i = 0; i < s2row4.length; i++) {
            Cell tempCell = s2r4.createCell(i);
            if (PubNumEnum.two.getIntValue().equals(i)) {
                tempCell.setCellValue(Double.parseDouble(s2row4[i]));
            } else {
                tempCell.setCellValue(s2row4[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else if (PubNumEnum.one.getIntValue().equals(i)) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet3.addMergedRegion(new CellRangeAddress(rowNum3[0] - 1, rowNum3[0] - 1, 0, 1));
        return hssfWorkbook;
    }

    /**
     * 理赔sheet
     *
     * @param hssfWorkbook           excel
     * @param bmsYfbillmainExportAll 数据
     * @param dicMap                 字典
     */
    public SXSSFWorkbook billClaimsSheet(SXSSFWorkbook hssfWorkbook, BmsYfbillmainExportAll bmsYfbillmainExportAll, Map<String, String> dicMap, List<Long> ids) {
        String tenantid= RequestContext.getTenantId();
        String token = RequestContext.getToken();
        // 查询账单理赔数据
        List<BmsClaimsInfo> inWarList = new ArrayList<>();
        List<BmsYsBillExportInfo> billCodeById = yfBillExportMapper.getBillCodeById(tenantid, ids);
        if (CollUtil.isNotEmpty(billCodeById)) {
            BmsClaimsInfo claimsInfo = new BmsClaimsInfo();
            claimsInfo.setSize(Integer.MAX_VALUE / 100);
            claimsInfo.setBillCode(billCodeById.stream().map(BmsYsBillExportInfo::getBillCode).collect(Collectors.joining(",")));
            inWarList = bmsClaimsInfoService.selectBmsClaimsInfoList(token, claimsInfo).getRows();
        }
        if (CollUtil.isEmpty(inWarList)) {
            return hssfWorkbook;
        }

        Sheet sheet3 = hssfWorkbook.createSheet("理赔费用明细");
        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        XSSFCellStyle commonStyle2 = ExcelExportUtil.getDefaultContentStyle2(hssfWorkbook);
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        final int[] rowNum3 = {0};

        //第1行
        Row s2r2 = sheet3.createRow(rowNum3[0]++);
        s2r2.setHeight((short) 500);
        String[] s3row2 = {"工单单号", "业务单号", "仓库编码", "仓库名称",
                "理赔类型", "理赔金额", "发起人", "投诉时间", "工单创建时间"
                , "工单内容", "工单大类",
                "工单子类", "门店编码", "门店名称", "区域", "所属省份", "市", "区", "受理人", "受理时间", "完成人", "完成时间", "关闭人", "关闭时间", "对账备注"};

        for (int i = 0; i < s3row2.length; i++) {
            String name = s3row2[i];
            OtherFeeInfo otherFeeInfo = new OtherFeeInfo();
            otherFeeInfo.setOtherName(name);
            s3row2[i] = otherFeeInfo.getOtherName();
        }
        for (int i = 0; i < s3row2.length; i++) {
            Cell tempCell = s2r2.createCell(i);
            tempCell.setCellValue(s3row2[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
        }
        // 创建数据行 入库明细
        BigDecimal[] sumAmt = {
                new BigDecimal("0")
        };
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (BmsClaimsInfo info : inWarList) {
            Row s2r3 = sheet3.createRow(rowNum3[0]++);
            s2r3.setHeight((short) 500);
            for (int i = 0; i < s3row2.length; i++) {
                Cell tempCell = s2r3.createCell(i);
                switch (i) {
                    case 0:
                        //工单号
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getWorkOrderCode()) ? info.getWorkOrderCode() : "");
                        break;
                    case 1:
                        //业务单号
                        tempCell.setCellValue(info.getRelatedOrderCode());
                        break;
                    case 2:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getWarehouseCode()) ? info.getWarehouseCode() : "");
                        break;
                    case 3:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getWarehouseName()) ? info.getWarehouseName() : "");
                        break;
                    case 4:
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getClaimsTypeName()) ? info.getClaimsTypeName() : "");
                        break;
                    case 5:
                        //理赔金额
                        tempCell.setCellValue(Double.parseDouble(info.getResponsibleMoney().toString()));
                        break;
                    case 6:
                        //发起人
                        tempCell.setCellValue(StrUtil.isNotEmpty(info.getCreateUserName()) ? info.getCreateUserName() : "");
                        break;
                    case 7:
                        if (info.getComplaintTime() != null) {
                            tempCell.setCellValue(sdf.format(info.getComplaintTime()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 8:
                        if (info.getWorkCreateTime() != null) {
                            tempCell.setCellValue(sdf.format(info.getWorkCreateTime()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 9:
                        tempCell.setCellValue(info.getComplaintContent());
                        break;
                    case 10:
                        tempCell.setCellValue(info.getPrimaryClassificationName());
                        break;
                    case 11:
                        tempCell.setCellValue(info.getSecondaryClassificationName());
                        break;
                    case 12:
                        tempCell.setCellValue(info.getShopCode());
                        break;
                    case 13:
                        tempCell.setCellValue(info.getShopName());
                        break;
                    case 14:
                        tempCell.setCellValue(info.getArea());
                        break;
                    case 15:
                        tempCell.setCellValue(info.getProvince());
                        break;
                    case 16:
                        tempCell.setCellValue(info.getCity());
                        break;
                    case 17:
                        tempCell.setCellValue(info.getDistrict());
                        break;
                    case 18:
                        tempCell.setCellValue(info.getHandleUserName());
                        break;
                    case 19:
                        if (info.getHandleTime() != null) {
                            tempCell.setCellValue(sdf.format(info.getHandleTime()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 20:
                        tempCell.setCellValue(info.getFinishUserName());
                        break;
                    case 21:
                        if (info.getFinishTime() != null) {
                            tempCell.setCellValue(sdf.format(info.getFinishTime()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 22:
                        tempCell.setCellValue(info.getCloseUserName());
                        break;
                    case 23:
                        if (info.getWorkCloseTime() != null) {
                            tempCell.setCellValue(sdf.format(info.getWorkCloseTime()));
                        } else {
                            tempCell.setCellValue("");
                        }
                        break;
                    case 24:
                        tempCell.setCellValue(info.getAdjustRemark());
                        break;
                    default:
                        tempCell.setCellValue("");
                }
                tempCell.setCellStyle(commonStyle);
            }
            sumAmt[0] = sumAmt[0].add(info.getResponsibleMoney() == null ? BigDecimal.valueOf(0) : info.getResponsibleMoney());
        }

        //最后一行 合计金额
        Row s2r4 = sheet3.createRow(rowNum3[0]++);
        s2r4.setHeight((short) 500);
        String[] s2row4 = {"汇总", "", "", "",
                "", sumAmt[0].toString(), "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""};
        for (int i = 0; i < s2row4.length; i++) {

            Cell tempCell = s2r4.createCell(i);
            if (PubNumEnum.five.getIntValue().equals(i)) {
                tempCell.setCellValue(new BigDecimal(s2row4[i]).setScale(2, RoundingMode.HALF_UP).doubleValue());
            } else {
                tempCell.setCellValue(s2row4[i]);
            }
            if (i == 0) {
                tempCell.setCellStyle(commonStyle2);
            } else if (i == 24) {
                commonStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                tempCell.setCellStyle(commonStyle);
            } else {
                tempCell.setCellStyle(commonStyle);
            }
        }
        // 合计行合并
        sheet3.addMergedRegion(new CellRangeAddress(rowNum3[0] - 1, rowNum3[0] - 1, 0, 4));
        return hssfWorkbook;
    }

}
