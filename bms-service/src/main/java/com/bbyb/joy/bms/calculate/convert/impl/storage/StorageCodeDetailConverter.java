package com.bbyb.joy.bms.calculate.convert.impl.storage;

import cn.hutool.core.bean.BeanUtil;
import com.bbyb.joy.bms.calculate.convert.OrderConverter;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDetailDto;
import com.bbyb.joy.bms.code.domain.dto.BmsStorageCodeDetailInfoDto;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
public class StorageCodeDetailConverter implements OrderConverter<BmsStorageCodeDetailInfoDto, CalculateOrderDetailDto> {


    @Override
    public List<CalculateOrderDetailDto> convert(List<BmsStorageCodeDetailInfoDto> sourceList) {
        if(sourceList == null || sourceList.isEmpty()){
            return Collections.emptyList();
        }

        List<CalculateOrderDetailDto> targetList = new ArrayList<>(sourceList.size());
        for (BmsStorageCodeDetailInfoDto source : sourceList) {
            CalculateOrderDetailDto target = BeanUtil.toBean(source, CalculateOrderDetailDto.class);
            // 特殊字段处理
            target.setPkId(source.getId());
            if(target.getTotalBoxes() == null){
                target.setTotalBoxes(BigDecimal.ZERO);
            }
            if(target.getTotalOddBoxes() == null){
                target.setTotalOddBoxes(BigDecimal.ZERO);
            }
            if(target.getTotalNumber() == null){
                target.setTotalNumber(BigDecimal.ZERO);
            }
            if(target.getTotalWeight() == null){
                target.setTotalWeight(BigDecimal.ZERO);
            }
            if(target.getTotalVolume() == null){
                target.setTotalVolume(BigDecimal.ZERO);
            }
            if(target.getTotalCargoValue() == null){
                target.setTotalCargoValue(BigDecimal.ZERO);
            }
            targetList.add(target);
        }

        return targetList;
    }

    @Override
    public Class<BmsStorageCodeDetailInfoDto> getSourceType() {
        return BmsStorageCodeDetailInfoDto.class;
    }

    @Override
    public Class<CalculateOrderDetailDto> getTargetType() {
        return CalculateOrderDetailDto.class;
    }
}
