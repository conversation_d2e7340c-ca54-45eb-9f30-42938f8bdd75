package com.bbyb.joy.bms.calculate.convert;

import com.bbyb.joy.bms.calculate.convert.impl.OrderConverterRegistry;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDetail2Dto;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDetailDto;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDto;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderExpandDto;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 单据扩展对象构建器
 */
@Component
public class OrderExpandBuilder {

    @Resource
    private OrderConverterRegistry converterRegistry;

    /**
     * 创建构建器实例
     * @return 构建器实例
     */
    public Builder builder() {
        return new Builder(converterRegistry);
    }

    /**
     * 单据扩展对象构建器内部类
     */
    public static class Builder {
        private final OrderConverterRegistry registry;
        private Integer handlerType; // 处理单据策略类型:1:运输单,21:仓储单(应收),22:仓储单(应付),3:调度单")
        private Integer codeType; // 运输||仓储
        private List<CalculateOrderDto> codes;
        private List<CalculateOrderDetailDto> details;
        private List<CalculateOrderDetail2Dto> details2;

        public Builder(OrderConverterRegistry registry) {
            this.registry = registry;
        }


        /**
         * 初始化单据信息
         * @param <S> 源类型
         * @return 构建器实例
         */
        public <S> Builder initCodeType(Integer codeType) {
            this.codeType = codeType;
            return this;
        }

        /**
         * 初始化处理单据场景
         * @param <S> 源类型
         * @return 构建器实例
         */
        public <S> Builder initHandlerType(Integer handlerType) {
            this.handlerType = handlerType;
            return this;
        }


        /**
         * 初始化单据信息
         * @param sourceCodes 源单据列表
         * @param sourceType 源单据类型
         * @param <S> 源类型
         * @return 构建器实例
         */
        public <S> Builder initCodes(List<S> sourceCodes, Class<S> sourceType) {
            OrderConverter<S, CalculateOrderDto> converter = registry.getConverter(sourceType, CalculateOrderDto.class);
            if (converter != null && sourceCodes != null) {
                this.codes = converter.convert(sourceCodes);
            }
            return this;
        }

        /**
         * 初始化单据详情信息
         * @param sourceDetails 源单据详情列表
         * @param sourceType 源单据详情类型
         * @param <S> 源类型
         * @return 构建器实例
         */
        public <S> Builder initDetail(List<S> sourceDetails, Class<S> sourceType) {
            OrderConverter<S, CalculateOrderDetailDto> converter = registry.getConverter(sourceType, CalculateOrderDetailDto.class);
            if (converter != null && sourceDetails != null) {
                this.details = converter.convert(sourceDetails);
            }
            return this;
        }

        /**
         * 初始化单据详情2信息
         * @param sourceDetails2 源单据详情2列表
         * @param sourceType 源单据详情2类型
         * @param <S> 源类型
         * @return 构建器实例
         */
        public <S> Builder initDetail2(List<S> sourceDetails2, Class<S> sourceType) {
            OrderConverter<S, CalculateOrderDetail2Dto> converter = registry.getConverter(sourceType, CalculateOrderDetail2Dto.class);
            if (converter != null && sourceDetails2 != null) {
                this.details2 = converter.convert(sourceDetails2);
            }
            return this;
        }

        /**
         * 构建单据扩展对象列表
         * @return 单据扩展对象列表
         */
        public List<CalculateOrderExpandDto> build() {
            if (codes == null || codes.isEmpty()) {
                return new ArrayList<>();
            }

            List<CalculateOrderExpandDto> result = new ArrayList<>(codes.size());

            for (CalculateOrderDto code : codes) {


                CalculateOrderExpandDto expandDto = new CalculateOrderExpandDto();
                expandDto.setCode(code);

                if(codeType!=null){
                    expandDto.setCodeType(codeType);
                }

                if(handlerType!=null){
                    expandDto.setHandleType(handlerType);
                }

                // 设置详情信息（如果有）
                if (details != null && !details.isEmpty()) {
                    List<CalculateOrderDetailDto> codeDetails = filterDetailsByCodeId(details, code.getPkId());
                    expandDto.setDetail(codeDetails);
                }
                // 设置详情2信息（如果有）
                if (details2 != null && !details2.isEmpty()) {
                    List<CalculateOrderDetail2Dto> codeDetails2 = filterDetails2ByCodeId(details2, code.getPkId());
                    expandDto.setDetail2(codeDetails2);
                }
                result.add(expandDto);
            }

            return result;
        }

        /**
         * 根据单据ID过滤详情
         */
        private List<CalculateOrderDetailDto> filterDetailsByCodeId(List<CalculateOrderDetailDto> details, Integer codeId) {
            List<CalculateOrderDetailDto> result = new ArrayList<>();
            for (CalculateOrderDetailDto detail : details) {
                if(handlerType.equals(3)){
                    // 调度单需要特殊处理
                    if (codeId.equals(detail.getSchedulingPkId())) {
                        result.add(detail);
                    }
                }else {
                    if (codeId.equals(detail.getMainPkId())) {
                        result.add(detail);
                    }
                }
            }
            return result;
        }

        /**
         * 根据单据ID过滤详情2
         */
        private List<CalculateOrderDetail2Dto> filterDetails2ByCodeId(List<CalculateOrderDetail2Dto> details2, Integer codeId) {
            List<CalculateOrderDetail2Dto> result = new ArrayList<>();
            for (CalculateOrderDetail2Dto detail2 : details2) {
                if (codeId.equals(detail2.getMainPkId())) {
                    result.add(detail2);
                }
            }
            return result;
        }
    }

}
