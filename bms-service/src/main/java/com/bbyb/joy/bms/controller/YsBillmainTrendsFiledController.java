package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.domain.dto.trends.BmsYsTrendsModel;
import com.bbyb.joy.bms.domain.dto.trends.YsTrendsRequest;
import com.bbyb.joy.bms.service.YsBillmainTrendsFiledService;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.core.context.RequestContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/system/billmain/trends")
public class YsBillmainTrendsFiledController {
    @Resource
    private YsBillmainTrendsFiledService ysBillmainTrendsFiledService;

    /**
     * 库存数据
     *
     * @param trendsRequest 要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/inventory/query")
    public ResponseResult<BmsYsTrendsModel> inventory(@RequestBody YsTrendsRequest trendsRequest) {
        String token = RequestContext.getToken();
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        BmsYsTrendsModel inventory = ysBillmainTrendsFiledService.inventory(token, trendsRequest);
        return new ResponseResult<>(inventory);
    }

    /**
     * 出库数据
     *
     * @param trendsRequest 要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/outbound/query")
    public ResponseResult<BmsYsTrendsModel> outbound(@RequestBody YsTrendsRequest trendsRequest) {
        String token = RequestContext.getToken();
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        BmsYsTrendsModel inventory = ysBillmainTrendsFiledService.outbound(token, trendsRequest);
        return new ResponseResult<>(inventory);
    }

    /**
     * 入库查询(*)
     *
     * @param trendsRequest 要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/warehousing/query")
    public ResponseResult<BmsYsTrendsModel> warehousing(@RequestBody YsTrendsRequest trendsRequest) {
        //获取token
        String token = RequestContext.getToken();
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        BmsYsTrendsModel inventory = ysBillmainTrendsFiledService.warehousing(token, trendsRequest);
        return new ResponseResult<>(inventory);
    }

    /**
     * 运输
     *
     * @param trendsRequest 要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/query/transport")
    public ResponseResult<BmsYsTrendsModel> transport(@RequestBody YsTrendsRequest trendsRequest) {
        String token = RequestContext.getToken();
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        BmsYsTrendsModel inventory = ysBillmainTrendsFiledService.transport(token, trendsRequest);
        return new ResponseResult<>(inventory);
    }

    /**
     * 理赔
     *
     * @param trendsRequest 要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/claim/query")
    public ResponseResult<BmsYsTrendsModel> claim(@RequestBody YsTrendsRequest trendsRequest) {
        //获取token
        String token = RequestContext.getToken();
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        BmsYsTrendsModel inventory = ysBillmainTrendsFiledService.claim(token, trendsRequest);
        return new ResponseResult<>(inventory);
    }

    /**
     * 固定费用
     *
     * @param trendsRequest 要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/fixed/query")
    public ResponseResult<BmsYsTrendsModel> fixedFee(@RequestBody YsTrendsRequest trendsRequest) {
        //获取token
        String token = RequestContext.getToken();
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        BmsYsTrendsModel inventory = ysBillmainTrendsFiledService.fixedFee(token, trendsRequest);
        return new ResponseResult<>(inventory);
    }

    /**
     * 增值
     *
     * @param trendsRequest 要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/valueAdded/query")
    public ResponseResult<BmsYsTrendsModel> valueAdded(@RequestBody YsTrendsRequest trendsRequest) {
        //获取token
        String token = RequestContext.getToken();
        UserBean loginUserInfo = RequestContext.getUserInfo();
        if (loginUserInfo == null || loginUserInfo.getTenantid() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        BmsYsTrendsModel inventory = ysBillmainTrendsFiledService.valueAdded(token, trendsRequest);
        return new ResponseResult<>(inventory);
    }
}
