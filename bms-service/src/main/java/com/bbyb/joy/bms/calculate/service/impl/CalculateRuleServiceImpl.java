package com.bbyb.joy.bms.calculate.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bms.calculate.biz.CalculateRuleBiz;
import com.bbyb.joy.bms.calculate.domain.dto.rule.CalculateFactorInfoDto;
import com.bbyb.joy.bms.calculate.domain.dto.rule.CalculateRuleInfoDto;
import com.bbyb.joy.bms.calculate.domain.model.CalculateFactorInfoModel;
import com.bbyb.joy.bms.calculate.domain.param.CalculateRuleQueryParam;
import com.bbyb.joy.bms.calculate.service.CalculateRuleService;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.ValidationUtil;
import com.bbyb.joy.bms.untis.ValidationTypeEnum;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CalculateRuleServiceImpl implements CalculateRuleService {

    @Resource
    CalculateRuleBiz baseMapper;
    @Resource
    TextConversionUtil textConversionUtil;


    @Override
    public Pair<String,Map<Integer,List<CalculateFactorInfoModel>>> queryValidResult(CalculateRuleQueryParam param) {

        String checkMsg = ValidationUtil.validateMsg(param, ValidationTypeEnum.SEL.class);
        if(StrUtil.isNotEmpty(checkMsg)){
            return new Pair<>(checkMsg, Maps.newHashMap());
        }

        List<CalculateRuleInfoDto> ruleInfoDtos = baseMapper.queryValidList(param);
        if(CollUtil.isEmpty(ruleInfoDtos)){
            return new Pair<>(ServiceError.CALCULATE_NO_RULE.getMessage(),Maps.newHashMap());
        }

        Map<String, String> dicMap = textConversionUtil.getDictByType("cost_dimension", "1");

        List<String> ruleIds = ruleInfoDtos.stream()
                .map(CalculateRuleInfoDto::getId)
                .distinct()
                .collect(Collectors.toList());
        CalculateRuleQueryParam queryFactorParam = CalculateRuleQueryParam.builder()
                .ruleIds(ruleIds)
                .build();
        List<CalculateFactorInfoDto> factorInfos = baseMapper.queryFactorsList(queryFactorParam);
        if(CollUtil.isEmpty(factorInfos)){
            return new Pair<>(ServiceError.CALCULATE_NO_RULE.getMessage(),Maps.newHashMap());
        }

        Map<String, List<CalculateFactorInfoDto>> factorMap = factorInfos.stream()
                .collect(Collectors.groupingBy(CalculateFactorInfoDto::getQuoteruledetailId));

        List<CalculateFactorInfoModel> result = new ArrayList<>();
        for (CalculateRuleInfoDto ruleInfoDto : ruleInfoDtos) {
            if(factorMap.containsKey(ruleInfoDto.getId())){
                CalculateFactorInfoModel initData = new CalculateFactorInfoModel();
                initData.setRelationId(ruleInfoDto.getRelationId());
                initData.setRuleInfo(ruleInfoDto);
                initData.setFactors(factorMap.get(ruleInfoDto.getId()));
                result.add(initData);
            }
            if(ruleInfoDto.getConsolidationRule()!=null && dicMap.containsKey("cost_dimension" + ruleInfoDto.getConsolidationRule())){
                ruleInfoDto.setConsolidationRuleName(dicMap.get("cost_dimension" + ruleInfoDto.getConsolidationRule()));
            }
        }

        if(CollUtil.isEmpty(result)){
            return new Pair<>(ServiceError.CALCULATE_NO_RULE.getMessage(),Maps.newHashMap());
        }

        log.info("自动计费-报价合同总条数:{}",result.size());

        Map<Integer, List<CalculateFactorInfoModel>> resultMap = result.stream().collect(Collectors.groupingBy(CalculateFactorInfoModel::getRelationId));

        return new Pair<>("",resultMap);
    }


    @Override
    public List<CalculateFactorInfoModel> queryValidList(CalculateRuleQueryParam param) {

        String checkMsg = ValidationUtil.validateMsg(param, ValidationTypeEnum.SEL.class);
        if(StrUtil.isNotEmpty(checkMsg)){
            return Collections.emptyList();
        }

        List<CalculateRuleInfoDto> ruleInfoDtos = baseMapper.queryValidList(param);
        if(CollUtil.isEmpty(ruleInfoDtos)){
            return Collections.emptyList();
        }

        List<String> ruleIds = ruleInfoDtos.stream()
                .map(CalculateRuleInfoDto::getId)
                .distinct()
                .collect(Collectors.toList());
        CalculateRuleQueryParam queryFactorParam = CalculateRuleQueryParam.builder()
                .ruleIds(ruleIds)
                .build();
        List<CalculateFactorInfoDto> factorInfos = baseMapper.queryFactorsList(queryFactorParam);
        if(CollUtil.isEmpty(factorInfos)){
            return Collections.emptyList();
        }

        Map<String, List<CalculateFactorInfoDto>> factorMap = factorInfos.stream()
                .collect(Collectors.groupingBy(CalculateFactorInfoDto::getQuoteruledetailId));

        List<CalculateFactorInfoModel> result = new ArrayList<>();
        for (CalculateRuleInfoDto ruleInfoDto : ruleInfoDtos) {
            if(factorMap.containsKey(ruleInfoDto.getId())){
                CalculateFactorInfoModel initData = new CalculateFactorInfoModel();
                initData.setRelationId(ruleInfoDto.getRelationId());
                initData.setRuleInfo(ruleInfoDto);
                initData.setFactors(factorMap.get(ruleInfoDto.getId()));
            }
        }

        if(CollUtil.isEmpty(result)){
            return Collections.emptyList();
        }

        log.info("运输自动计费-报价合同总条数:{}",result.size());

        return Collections.emptyList();
    }
}
