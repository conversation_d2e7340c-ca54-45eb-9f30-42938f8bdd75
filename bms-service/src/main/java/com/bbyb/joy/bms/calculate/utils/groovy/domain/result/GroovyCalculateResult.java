package com.bbyb.joy.bms.calculate.utils.groovy.domain.result;

import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 根据最优计算因子信息计费结果
 */
@Data
public class GroovyCalculateResult {

    /**
     * 服务执行结果编码
     */
    private int code;

    /**
     * 是否成功
     * true:成功
     * false:失败
     */
    private boolean success;


    /**
     * 服务执行结果描述
     */
    private String msg;


    /**
     * 计算因子参数信息
     */
    private BigDecimal result;


    /**
     * 额外结果类型(赋值逻辑查看GroovyCalculateExtraType枚举类,该字段和extraInfo字段配合使用)
     */
    private Integer extraType = GroovyCalculateExtraType.DEFAULT.getCode();


    /**
     * 额外结果信息
     * 自动计费根据业务会存在额外一些输出场景，所以做了额外计费的逻辑,做好了该约定后后续不同维度的计费都应遵循该规则进行实现拓展
     */
    private Map<String, Object> extraInfo = new HashMap<>();


}
