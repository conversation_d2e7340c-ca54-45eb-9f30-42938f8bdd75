package com.bbyb.joy.bms.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.export.BmsInvoiceReportExport;
import com.bbyb.joy.bms.domain.dto.reportdto.*;
import com.bbyb.joy.bms.domain.dto.reportquerybean.*;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.service.IBmsReportService;
import com.bbyb.joy.bms.service.impl.excelout.ExportBusinessImpl;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.enums.PubNumEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表Controller
 */
@RestController
@RequestMapping("/system/report")
public class BmsReportController {

    @Resource
    private IBmsReportService bmsReportService;
    @Resource
    private UserRights userRights;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    private ExportBusinessImpl exportBusiness;

    public static List<BmsInvoiceReportExport> convertToExport(List<BmsInvoiceReportDto> datas) {
        List<BmsInvoiceReportExport> returnDatas = new ArrayList<>();
        if (CollUtil.isNotEmpty(datas)) {
            for (BmsInvoiceReportDto data : datas) {
                BmsInvoiceReportExport dealData = new BmsInvoiceReportExport();
                dealData.setBillCode(data.getBillCode());
                dealData.setCompanyName(data.getCompanyName());
                dealData.setClientName(data.getClientName());
                dealData.setInvoiceTypeName(data.getInvoiceTypeName());
                dealData.setInvoiceProjectName(data.getInvoiceProjectName());
                dealData.setInvoicefee(data.getInvoicefee());
                dealData.setTaxRateStr(data.getTaxRate().doubleValue() + "%");
                dealData.setTaxFee(data.getTaxFee());
                dealData.setInvoiceIncome(data.getInvoiceIncome());
                dealData.setInvoiceRemark(data.getInvoiceRemark());
                dealData.setTicketNum(data.getTicketNum());
                dealData.setReceivableDays(data.getReceivableDays());
                dealData.setCreateBy(data.getCreateBy());
                dealData.setInvoiceTimeDt(data.getInvoiceTimeDt());
                dealData.setInvoiceCode(data.getInvoiceCode());
                dealData.setOpeningName(data.getOpeningName());
                dealData.setTaxpayerNum(data.getTaxpayerNum());
                returnDatas.add(dealData);
            }
        }
        return returnDatas;
    }

    /**
     * 应收台账报表分页查询
     */
    @PostMapping("/selectBmsYsBillReportList")
    @MenuAuthority(code = "应收台账")
    public ResponseResult<PagerDataBean<BmsYsBillReportDto>> selectBmsYsBillReportList(@RequestBody(required = false) BmsYsBillReportBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        String token = RequestContext.getToken();

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CLIENT.getCode(), "clientList", PermissionConfig.FieldType.LIST_STRING, "clientCode"),
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.STRING, "id")
        );
        bean = userRights.applyPermissions(bean, permissionConfigs);

        return new ResponseResult<>(bmsReportService.selectBmsYsBillReportList(token, bean));
    }

    /**
     * 应收台账报表导出
     */
    @PostMapping("/bmsysbillreportexport")
    @MenuAuthority(code = "应收台账-导出")
    public ResponseResult<String> bmsysbillreportexport(@RequestBody(required = false) BmsYsBillReportBean bean) {
        String token = RequestContext.getToken();
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        PagerDataBean<BmsYsBillReportDto> list = bmsReportService.selectBmsYsBillReportList(token, bean);
        Map<String, List<BmsYsBillReportDto>> clienList = list.getRows().stream().collect(Collectors.groupingBy(e -> StrUtil.isEmpty(e.getClientName()) ? "" : e.getClientName()));
        int i = 0;
        String cileName = "应收台账导出";
        for (String clien : clienList.keySet()) {
            i++;
        }
        if (PubNumEnum.one.getIntValue().equals(i)) {
            cileName = list.getRows().get(0).getClientName() + "_应收台账_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        }
        return exportUtil.getOutClassNew(token, cileName, "应收台账", BmsYsBillReportDto.class, userId -> {
            return bmsReportService.selectBmsYsBillReportList(token, bean).getRows();
        });
    }

    /**
     * 应付台账报表分页查询
     */
    @PostMapping("/selectBmsYfBillReportList")
    @MenuAuthority(code = "应付台账")
    public ResponseResult<PagerDataBean<BmsYfBillReportDto>> selectBmsYfBillReportList(@RequestBody(required = false) BmsYfBillReportBean bean) {
        String token = RequestContext.getToken();
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CARRIER.getCode(), "carrierList", PermissionConfig.FieldType.LIST_STRING, "carrierCode"),
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.STRING, "id")
        );
        bean = userRights.applyPermissions(bean, permissionConfigs);

        return new ResponseResult<>(bmsReportService.selectBmsYfBillReportList(token, bean));
    }

    /**
     * 应付台账报表导出
     */
    @PostMapping("/bmsyfbillreportexport")
    @MenuAuthority(code = "应付台账-导出")
    public ResponseResult<String> bmsyfbillreportexport(@RequestBody(required = false) BmsYfBillReportBean bean) {
        String token = RequestContext.getToken();
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        List<BmsYfBillReportDto> list = bmsReportService.selectBmsYfBillReportList2(token, bean);
        Map<String, List<BmsYfBillReportDto>> clientList = list.stream().filter(e -> StrUtil.isNotEmpty(e.getClientName())).collect(Collectors.groupingBy(BmsYfBillReportDto::getClientName));
        int i = 0;
        String cileName = "应付台账导出";
        for (String clien : clientList.keySet()) {
            i++;
        }
        if (PubNumEnum.one.getIntValue().equals(i)) {
            cileName = list.get(0).getCarrierName() + "_" + list.get(0).getClientName() + "_应付台账_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        }
        return exportUtil.getOutClassNew(token, cileName, "应付台账", BmsYfBillReportDto.class, userId -> {
            return bmsReportService.selectBmsYfBillReportList(token, bean).getRows();
        });
    }

    /**
     * 应收回款率台账报表分页查询
     */
    @PostMapping("/selectBmsYsHKReportList")
    @MenuAuthority(code = "应收回款率台账")
    public ResponseResult<PagerDataBean<BmsYsHKReportDto>> selectBmsYsHkReportList(@RequestBody(required = false) BmsYsHKReportBean bean) {
        String token = RequestContext.getToken();
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CLIENT.getCode(), "clientList", PermissionConfig.FieldType.LIST_STRING, "clientCode"),
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.STRING, "id")
        );
        bean = userRights.applyPermissions(bean, permissionConfigs);

        return new ResponseResult<>(bmsReportService.selectBmsYsHkReportList(token, bean));
    }

    /**
     * 应收回款率明细台账报表分页查询
     */
    @PostMapping("/selectBmsYsHKDetailReportList")
    @MenuAuthority(code = "应收回款率台账-明细查询")
    public ResponseResult<PagerDataBean<BmsYsHKReportDto>> selectBmsYsHkDetailReportList(@RequestBody(required = false) BmsYsHKReportBean bean) {
        String token = RequestContext.getToken();
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
        List<ClientInfo> clientList = sysDataPack.getClientinfo();
        if (CollUtil.isEmpty(clientList)) {
            return new ResponseResult<>(new PagerDataBean<>(new ArrayList<>(), 0, new PagerBean(bean.getPage(), bean.getSize())));
        }
        bean.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
        return new ResponseResult<>(bmsReportService.selectBmsYsHkDetailReportList(token, bean));
    }

    /**
     * 应收回款率明细台账报表导出
     */
    @PostMapping("/selectBmsYsHKDetailReportExport")
    @MenuAuthority(code = "应收回款率台账-导出")
    public ResponseResult<String> selectBmsYsHkDetailReportExport(@RequestBody(required = false) BmsYsHKReportBean bean) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        return exportUtil.getOutClassNew(token, "应收回款率明细导出", "应收回款率", BmsYsHKReportDto.class, userId -> {
            SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
            List<ClientInfo> clientList = sysDataPack.getClientinfo();
            if (CollUtil.isEmpty(clientList)) {
                return null;
            }
            bean.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
            return bmsReportService.selectBmsYsHkDetailReportList(token, bean).getRows();
        });
    }

    /**
     * 发票台账报表分页查询
     */
    @PostMapping("/selectBmsInvoiceReportList")
    @MenuAuthority(code = "发票台账")
    public ResponseResult<PagerDataBean<BmsInvoiceReportDto>> selectBmsInvoiceReportList(@RequestBody(required = false) BmsInvoiceReportBean bean) {

        String token = RequestContext.getToken();
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CARRIER.getCode(), "carrierList", PermissionConfig.FieldType.LIST_STRING, "carrierCode"),
                new PermissionConfig(PermissionType.CLIENT.getCode(), "clientList", PermissionConfig.FieldType.LIST_STRING, "clientCode"),
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.STRING, "id")
        );
        bean = userRights.applyPermissions(bean, permissionConfigs);

        return new ResponseResult<>(bmsReportService.selectBmsInvoiceReportList(token, bean));
    }

    /**
     * 发票台账报表导出
     */
    @PostMapping("/selectBmsInvoiceReportExport")
    @MenuAuthority(code = "发票台账-导出")
    public ResponseResult<String> selectBmsInvoiceReportExport(@RequestBody(required = false) BmsInvoiceReportBean bean) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        return exportUtil.getOutClassNew(token, "发票台账导出", "发票台账", BmsInvoiceReportExport.class, userId -> {
            SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client,carrier");
            List<ClientInfo> clientList = sysDataPack.getClientinfo();
            List<Carrierinfo> carrierList = sysDataPack.getCarrierInfo();
            if (CollUtil.isEmpty(clientList) && CollUtil.isEmpty(carrierList)) {
                return null;
            }
            bean.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
            bean.setCarrierList(carrierList.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toList()));
            PagerDataBean<BmsInvoiceReportDto> bmsInvoiceReportDtos = bmsReportService.selectBmsInvoiceReportList(token, bean);
            return convertToExport(bmsInvoiceReportDtos.getRows());
        });
    }

    /**
     * 项目分析台账报表分页查询
     */
    @PostMapping("/selectProjectAnalysisReportList")
    @MenuAuthority(code = "项目分析台账")
    public ResponseResult<PagerDataBean<BmsProjectAnalysisReportDto>> selectProjectAnalysisReportList(@RequestBody(required = false) BmsProjectAnalysisReportBean bean) {
        String token = RequestContext.getToken();
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
        List<ClientInfo> clientList = sysDataPack.getClientinfo();
        if (CollUtil.isEmpty(clientList)) {
            return new ResponseResult<>(new PagerDataBean<>(new ArrayList<>(), 0, new PagerBean(bean.getPage(), bean.getSize())));
        }
        bean.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
        return new ResponseResult<>(bmsReportService.selectProjectAnalysisReportList(token, bean));
    }

    /**
     * 项目分析台账报表导出
     */
    @PostMapping("/selectProjectAnalysisReportExport")
    @MenuAuthority(code = "项目分析台账-导出")
    public ResponseResult<String> selectProjectAnalysisReportExport(@RequestBody BmsProjectAnalysisReportBean bean) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        return exportUtil.getOutClassNew(token, "项目分析台账报表导出", "项目分析台账", BmsProjectAnalysisReportDto.class, userId -> {
            SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
            List<ClientInfo> clientList = sysDataPack.getClientinfo();
            if (CollUtil.isEmpty(clientList)) {
                return null;
            }
            bean.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
            return bmsReportService.selectProjectAnalysisReportList(token, bean).getRows();
        });
    }


    /**
     * 应收应付核销台账报表
     */
    @PostMapping("/selectBillhx")
    public ResponseResult<PagerDataBean<BmsHxReportDto>> selectBillhx(@RequestBody(required = false) BmsHxReportBean bean) {
        List<BmsHxReportDto> list = new ArrayList<>();
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        if (bean.getHxType() != null && "1".equals(bean.getHxType())) {
            //加权限
            Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
            SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
            List<ClientInfo> clientInfos = sysDataPack.getClientinfo();
            bean.setClientCode(clientInfos.stream().map(ClientInfo::getClientCode).collect(Collectors.joining(",")));
            if (StrUtil.isEmpty(bean.getClientCode())) {
                return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(bean.getPage(), bean.getSize())));
            }
        }
        if (bean.getHxType() != null && "2".equals(bean.getHxType())) {
            //加权限
            Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
            SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "carrier");
            List<Carrierinfo> carrierInfos = sysDataPack.getCarrierInfo();
            bean.setCarrierCode(carrierInfos.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.joining(",")));
            if (StrUtil.isEmpty(bean.getCarrierCode())) {
                return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(bean.getPage(), bean.getSize())));
            }
        }

        return new ResponseResult<>(bmsReportService.selectBillhx(RequestContext.getToken(), bean));
    }

    /**
     * 应收应付核销台账报表导出
     */
    @PostMapping("/selectBillhxExport")
    @MenuAuthority(code = "应收应付账单核销-导出")
    public ResponseResult<String> selectBillhxExport(@RequestBody BmsHxReportBean bean) {
        String token = RequestContext.getToken();
        return exportUtil.getOutClassNew(token, "应收应付账单核销导出", "应收应付账单核销", BmsHxReportDto.class, userId -> {
            if (bean.getHxType() != null && "1".equals(bean.getHxType())) {
                //加权限
                SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
                List<ClientInfo> clientInfos = sysDataPack.getClientinfo();
                bean.setClientCode(clientInfos.stream().map(ClientInfo::getClientCode).collect(Collectors.joining(",")));
                if (StrUtil.isEmpty(bean.getClientCode())) {
                    return null;
                }
            }
            if (bean.getHxType() != null && "2".equals(bean.getHxType())) {
                //加权限
                SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "carrier");
                List<Carrierinfo> carrierInfos = sysDataPack.getCarrierInfo();
                bean.setCarrierCode(carrierInfos.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.joining(",")));
                if (StrUtil.isEmpty(bean.getCarrierCode())) {
                    return null;
                }
            }
            return bmsReportService.selectBillhx(token, bean).getRows();
        });
    }


    /**
     * 预估收入台账查询
     */
    @PostMapping("/selectBmsYsCostReportList")
    @MenuAuthority(code = "预估收入台账")
    public ResponseResult<PagerDataBean<BmsYsCostReportDto>> selectBmsYsCostReportList(@RequestBody(required = false) BmsYsCostReportBean bean) {
        String token = RequestContext.getToken();
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
        List<ClientInfo> clientList = sysDataPack.getClientinfo();
        if (CollUtil.isEmpty(clientList)) {
            return new ResponseResult<>(new PagerDataBean<>(new ArrayList<>(), 0, new PagerBean(bean.getPage(), bean.getSize())));
        }
        bean.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
        return new ResponseResult<>(bmsReportService.selectBmsYsCostReportList(token, bean));
    }

    /**
     * 预估收入台账导出
     */
    @PostMapping("/bmsYsCostReportExport")
    @MenuAuthority(code = "")
    public ResponseResult<String> bmsYsCostReportExport(@RequestBody BmsYsCostReportBean bean) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        return exportUtil.getOutClassNew(token, "预估收入台账导出", "预估收入台账", BmsYsCostReportDto.class, userId -> {
            SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client");
            List<ClientInfo> clientList = sysDataPack.getClientinfo();
            if (CollUtil.isEmpty(clientList)) {
                return null;
            }
            bean.setClientList(clientList.stream().map(ClientInfo::getClientCode).collect(Collectors.toList()));
            return bmsReportService.selectBmsYsCostReportList(token, bean).getRows();
        });
    }

    /**
     * 预估支出台账查询
     */
    @PostMapping("/selectBmsYfCostReportList")
    @MenuAuthority(code = "预估支出台账")
    public ResponseResult<PagerDataBean<BmsYfCostReportDto>> selectBmsYfCostReportList(@RequestBody(required = false) BmsYfCostReportBean bean) {
        if (bean == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "carrier");
        List<Carrierinfo> carrierList = sysDataPack.getCarrierInfo();
        if (CollUtil.isEmpty(carrierList)) {
            return new ResponseResult<>(new PagerDataBean<>(new ArrayList<>(), 0, new PagerBean(bean.getPage(), bean.getSize())));
        }
        bean.setCarrierList(carrierList.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toList()));
        return new ResponseResult<>(bmsReportService.selectBmsYfCostReportList(RequestContext.getToken(), bean));
    }

    /**
     * 预估支出台账导出
     */
    @PostMapping("/bmsYfCostReportExport")
    @MenuAuthority(code = "预估支出台账-导出")
    public ResponseResult<String> bmsYfCostReportExport(@RequestBody BmsYfCostReportBean bean) {
        String token = RequestContext.getToken();
        if (StrUtil.isEmpty(bean.getCompanyIds())) {
            bean.setCompanyIds(textConversionUtil.getCompanyIds());
        }
        return exportUtil.getOutClassNew(token, "预估支出台账导出", "预估支出台账", BmsYfCostReportDto.class, userId -> {
            SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "carrier");
            List<Carrierinfo> carrierList = sysDataPack.getCarrierInfo();
            if (CollUtil.isEmpty(carrierList)) {
                return null;
            }
            bean.setCarrierList(carrierList.stream().map(Carrierinfo::getCarrierCode).collect(Collectors.toList()));
            return bmsReportService.selectBmsYfCostReportList(token, bean).getRows();
        });
    }


    /**
     * 库存台账接口查询
     */
    @PostMapping("/selectBmsInventoryLedger")
    public ResponseResult<BmsInventoryLedgerRetDto> selectBmsInventoryLedger(@RequestBody(required = false) BmsInventoryLedgerBean bean) {
        BmsInventoryLedgerRetDto inventoryLedgerRetDto = new BmsInventoryLedgerRetDto();
        inventoryLedgerRetDto.setTotal(0L);
        inventoryLedgerRetDto.setCode(200);
        inventoryLedgerRetDto.setMsg("查询成功");
        inventoryLedgerRetDto.setRows(new ArrayList<>());
        if (bean == null) {
            return new ResponseResult<>(inventoryLedgerRetDto);
        }
        if (StrUtil.isEmpty(bean.getAccountStartTime())) {
            return new ResponseResult<>(inventoryLedgerRetDto);
        }
        if (StrUtil.isEmpty(bean.getAccountEndTime())) {
            return new ResponseResult<>(inventoryLedgerRetDto);
        }
        if (bean.getCustomerId() == null) {
            return new ResponseResult<>(inventoryLedgerRetDto);
        }
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsReportService.selectBmsInventoryLedger(token, bean));

    }

    /**
     * 库存台账导出
     */
    @PostMapping("/selectBmsInventoryLedgerExport")
    public ResponseResult<String> selectBmsInventoryLedgerExport(@RequestBody BmsInventoryLedgerBean bean) {
        String token = RequestContext.getToken();
        if (bean == null) {
            return new ResponseResult<>(400401, "请求参数不可为空");
        }
        if (StrUtil.isEmpty(bean.getAccountStartTime())) {
            return new ResponseResult<>(400401, "台账开始日期不可为空");
        }
        if (StrUtil.isEmpty(bean.getAccountEndTime())) {
            return new ResponseResult<>(400401, "台账结束时间不可为空");
        }
        if (bean.getCustomerId() == null) {
            return new ResponseResult<>(400401, "请选择客户");
        }
        return exportUtil.getOutClassNew(token, "周转率台账导出", "周转率台账", BmsInventoryLedgerDto.class, userId -> {
            BmsInventoryLedgerRetDto retDto = bmsReportService.selectBmsInventoryLedger(token, bean);
            return retDto.getRows();
        });
    }


    @ApiOperation(value = "毛利率分析报表")
    @PostMapping("/queryGrossProfitMarginReport")
    public ResponseResult<PagerDataBean<BmsAnalysisDto>> queryGrossProfitMarginReport(@Valid @RequestBody BmsGrossProfitMarginBean bean) {

        // permission
        List<PermissionConfig> permissionConfigs = List.of(
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.LIST_INTEGER, "id")
        );
        bean = userRights.applyPermissions(bean, permissionConfigs);

        return new ResponseResult<>(bmsReportService.queryGrossProfitMarginReport(null,bean));
    }


    @ApiOperation(value = "利润分析报表")
    @PostMapping("/queryProfitAnalysisReport")
    public ResponseResult<PagerDataBean<BmsAnalysisDto>> queryProfitAnalysisReport(@Valid @RequestBody BmsGrossProfitMarginBean bean) {

        // permission
        List<PermissionConfig> permissionConfigs = List.of(
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.LIST_INTEGER, "id")
        );
        bean = userRights.applyPermissions(bean, permissionConfigs);

        return new ResponseResult<>(bmsReportService.queryProfitAnalysisReport(null,bean));
    }


    @ApiOperation(value = "单票毛利率报表")
    @PostMapping("/queryGrossMarginReport")
    public ResponseResult<PagerDataBean<Dict>> queryGrossMarginReport(@Valid @RequestBody BmsProfitAnalysisQueryBean bean) {

        // permission
        List<PermissionConfig> permissionConfigs = List.of(
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.LIST_INTEGER, "id")
        );
        bean = userRights.applyPermissions(bean, permissionConfigs);

        PagerDataBean<Dict> result = bmsReportService.queryGrossMarginReport(null,bean);
        return new ResponseResult<>(result);
    }

    @ApiOperation(value = "单票毛利率报表导出")
    @PostMapping("/queryGrossMarginReportExport")
    public ResponseResult<String> queryGrossMarginReportExport(@Valid @RequestBody BmsProfitAnalysisQueryBean bean) {
        return new ResponseResult<>(bmsReportService.queryGrossMarginReportExport(bean));
    }

}
