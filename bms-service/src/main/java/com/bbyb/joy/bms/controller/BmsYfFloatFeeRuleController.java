package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.BmsYfFloatFeeRuleRequest;
import com.bbyb.joy.bms.domain.dto.dto.BmsYfFloatFeeRuleDto;
import com.bbyb.joy.bms.service.IBmsYfFloatFeeService;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * bms-yf浮动费用规则控制器
 */
@RestController
@RequestMapping("/system/yfFloat")
public class BmsYfFloatFeeRuleController {

    private final IBmsYfFloatFeeService bmsYfFloatFeeService;

    public BmsYfFloatFeeRuleController(IBmsYfFloatFeeService bmsYfFloatFeeService) {
        this.bmsYfFloatFeeService = bmsYfFloatFeeService;
    }

    /**
     * 增加yf浮动费用规则
     *
     * @param rule 规则
     * @return {@link ResponseResult}
     */
    @PostMapping("/insert")
    @MenuAuthority(code = "应付浮动计费规则-新增")
    public ResponseResult<Boolean> addYfFloatFee(HttpServletRequest request, @RequestBody BmsYfFloatFeeRuleDto rule) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfFloatFeeService.addYfFloatFee(token, rule));
    }


    /**
     * 分页查询
     *
     * @param rule 规则
     * @return {@link PagerDataBean}
     */
    @PostMapping("/query/page")
    public ResponseResult<PagerDataBean<BmsYfFloatFeeRuleDto>> queryPage(HttpServletRequest request, @RequestBody BmsYfFloatFeeRuleRequest rule) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfFloatFeeService.queryPage(token, rule));
    }

    /**
     * 查询详细信息
     *
     * @param rule 规则
     * @return {@link ResponseResult}
     */
    @PostMapping("/query/detail")
    public ResponseResult<BmsYfFloatFeeRuleDto> queryDetail(HttpServletRequest request, @RequestBody BmsYfFloatFeeRuleRequest rule) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfFloatFeeService.queryDetail(token, rule.getId()));
    }

    /**
     * 绑定费用信息
     *
     * @param request 要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/binding/fee")
    @MenuAuthority(code = "应付浮动计费规则-绑定费用类型")
    public ResponseResult<Boolean> bindingFeeInfo(HttpServletRequest requests, @RequestBody BmsYfFloatFeeRuleRequest request) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfFloatFeeService.bindingFeeInfo(token, request));
    }

    /**
     * 绑定承运商信息
     *
     * @param request 要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/binding/carrier")
    @MenuAuthority(code = "应付浮动计费规则-绑定承运商/仓库")
    public ResponseResult<Boolean> bindingCarrierInfo(HttpServletRequest requests, @RequestBody BmsYfFloatFeeRuleRequest request) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfFloatFeeService.bindingCarrierInfo(token, request));
    }

    /**
     * 是否启用(0启用,1不启用)
     *
     * @param request 要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/enabled")
    @MenuAuthority(code = "应付浮动计费规则-启用,应付浮动计费规则-禁用")
    public ResponseResult<Boolean> enabled(HttpServletRequest requests, @RequestBody BmsYfFloatFeeRuleRequest request) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfFloatFeeService.enabled(token, request.getId(), request.getEnabled()));
    }

    /**
     * 作废浮动费用规则
     *
     * @param request 要求
     * @return {@link ResponseResult}
     */
    @PostMapping("/deleted")
    @MenuAuthority(code = "应付浮动计费规则-作废")
    public ResponseResult<Boolean> deletedFloatFeeRule(HttpServletRequest requests, @RequestBody BmsYfFloatFeeRuleRequest request) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfFloatFeeService.deletedFloatFeeRule(token, request.getId()));
    }

    /**
     * 修改信息
     *
     * @param dto 到
     * @return {@link ResponseResult}
     */
    @PostMapping("/modify")
    @MenuAuthority(code = "应付浮动计费规则-修改")
    public ResponseResult<Boolean> modifyInfo(@RequestBody BmsYfFloatFeeRuleDto dto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfFloatFeeService.modifyInfo(token, dto));
    }


}
