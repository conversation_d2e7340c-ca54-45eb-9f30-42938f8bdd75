|-计费模块
|-按规则规则进行计费都需要进入该服务
|-计费模式调参设置:
|- 自动计费参数配置:放到sys_sys_config表(config_name='自动计费参数')中,放到表中是因为实时性,并且操作IO不高
|- calculate.relation.task.min - 客户维度并行计费最小客户数
|- calculate.relation.task.max - 客户维度并行计费最大客户数
|- calculate.rule.task.min - 合同维度并行计费最小客户数
|- calculate.rule.task.max - 合同维度并行计费最大客户数
|- INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) VALUES ('自动计费参数', 'calculate.relation.task.min', '1', 'N', 'admin', NOW(), 'admin', NOW(), '客户维度并行计费最小客户数');
|- INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) VALUES ('自动计费参数', 'calculate.relation.task.max', '3', 'N', 'admin', NOW(), 'admin', NOW(), '客户维度并行计费最大客户数');
|- INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) VALUES ('自动计费参数', 'calculate.rule.task.min', '1', 'N', 'admin', NOW(), 'admin', NOW(), '合同维度并行计费最小客户数');
|- INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) VALUES ('自动计费参数', 'calculate.rule.task.max', '3', 'N', 'admin', NOW(), 'admin', NOW(), '合同维度并行计费最大客户数');
|- 参数说明:
|- 增加了
|-规则配置方面
|-计费规则模块进行表达式配置转译规范
|-性能方面
|-groovy方面
|-主工具类 GroovyUtil
|-辅助拓展类 GroovyMatchResult
|-功能模块目录
|-注意: (该服务都是单体服务设计架构):该模块尽量简单化开发，但是结构一定要清晰
├── service                       // 服务层
│   ├── CalculateService.java
│   └── impl
│       └── CalculateServiceImpl.java
├── domain                        // 领域模型层
│   ├── dto                      // 数据传输对象
│   │   ├── day
│   │   │   └── CalculateDayDto.java        // 天维度计费实体
│   │   ├── goods
│   │   │   └── CalculateGoodsDto.java      // 品维度计费实体
│   │   └── store
│   │       └── CalculateStoreDto.java      // 店维度计费实体
│   └── entity                   // 通用实体
│       └── CalculateEntity.java
├── mapper                        // 数据访问层
│   └── CalculateMapper.java
├── strategy                      // 计费策略
│   ├── CalculateStrategy.java
│   ├── DayCalculateStrategy.java
│   ├── GoodsCalculateStrategy.java
│   └── StoreCalculateStrategy.java
├── factory                       // 工厂类
│   └── CalculateStrategyFactory.java
├── constant                      // 常量定义
│   └── CalculateConstant.java
├── enums                         // 枚举定义
│   └── CalculateTypeEnum.java
└── utils                         // 工具类
└── CalculateUtil.java