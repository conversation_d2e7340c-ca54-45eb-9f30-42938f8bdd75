package com.bbyb.joy.bms.calculate.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class CalculateDebuggerParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "明细报价合同编码")
    private String ruleCode;

}
