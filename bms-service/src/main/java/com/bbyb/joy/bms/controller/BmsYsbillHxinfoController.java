package com.bbyb.joy.bms.controller;

import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.dto.BmsCollectionRecordDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYsBatchhxDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYsbillHxDetailDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYsbillHxDto;
import com.bbyb.joy.bms.domain.dto.querybean.BmsYsbillHxQueryBean;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.service.IBmsYsbillHxinfoService;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应收账单核销Controller
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "应收账单核销接口")
@RestController
@RequestMapping("/system/hxinfo")
public class BmsYsbillHxinfoController {

    @Resource
    private IBmsYsbillHxinfoService bmsYsbillHxinfoService;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private UserRights userRights;

    /**
     * 应收账单核销分页查询
     */
    @ApiOperation(value = "应收账单核销分页查询", response = BmsYsbillHxDto.class)
    @PostMapping("/selectYsbillHx")
    public ResponseResult<PagerDataBean<BmsYsbillHxDto>> selectYsbillHx(@RequestBody BmsYsbillHxQueryBean bean) {
        String token = RequestContext.getToken();

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CLIENT.getCode(), "clientIds", PermissionConfig.FieldType.STRING, "id"),
                new PermissionConfig(PermissionType.COMPANY.getCode(), "companyIds", PermissionConfig.FieldType.STRING, "id")
        );
        bean = userRights.applyPermissions(bean, permissionConfigs);

        return new ResponseResult<>(bmsYsbillHxinfoService.selectYsbillHx(token, bean));
    }

    /**
     * 收款单信息查询
     */
    @ApiOperation(value = "收款单信息查询", response = BmsCollectionRecordDto.class)
    @PostMapping("/selectCollectionRecord")
    public ResponseResult<PagerDataBean<BmsCollectionRecordDto>> selectCollectionRecord(@RequestBody BmsCollectionRecordDto dto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillHxinfoService.selectCollectionRecord(token, dto.getClientName(), dto.getCompanyIds()));
    }

    /**
     * 根据账单编号查询核销明细
     */
    @ApiOperation(value = "根据账单编号查询核销明细", response = BmsYsbillHxDetailDto.class)
    @PostMapping("/hxDetailList")
    public ResponseResult<PagerDataBean<BmsYsbillHxDetailDto>> hxDetailList(@RequestBody BmsYsbillHxDetailDto dto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillHxinfoService.hxDetailList(token, dto.getBillCode()));
    }

    /**
     * 批量核销
     */
    @ApiOperation(value = "批量核销", response = BmsYsBatchhxDto.class)
    @PostMapping("/batchhx")
    @MenuAuthority(code = "应收账单核销-批量核销保存")
    public ResponseResult<String> batchhx(@RequestBody BmsYsBatchhxDto bmsYsBatchhxDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillHxinfoService.batchhx(token, bmsYsBatchhxDto));
    }

    /**
     * 取消核销
     */
    @ApiOperation(value = "取消核销", response = BmsYsBatchhxDto.class)
    @PostMapping("/batchCancel")
    @MenuAuthority(code = "应收账单核销-取消核销")
    public ResponseResult<String> batchCancel(@RequestBody BmsYsBatchhxDto bmsYsBatchhxDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillHxinfoService.batchCancel(token, bmsYsBatchhxDto));
    }
}
