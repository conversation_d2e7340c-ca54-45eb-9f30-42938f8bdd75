package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.biz.BmsPaymentRecordBiz;
import com.bbyb.joy.bms.domain.dto.bill.BmsYfbillmain;
import com.bbyb.joy.bms.domain.dto.pay.BmsPaymentRecord;
import com.bbyb.joy.bms.domain.dto.pay.BmsPaymentRecordDetail;
import com.bbyb.joy.bms.service.IBmsPaymentRecordService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 付款记录Controller
 */
@Api(tags = "付款记录接口")
@Validated
@RestController
@RequestMapping("/system/PayRecord")
public class BmsPaymentRecordController {

    @Resource
    private IBmsPaymentRecordService bmsPaymentRecordService;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    private BmsPaymentRecordBiz bmsPaymentRecordMapper;
    @Resource
    private UserRights userRights;

    /**
     * 查询付款记录列表
     */
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<BmsPaymentRecord>> list(@RequestBody(required = false) BmsPaymentRecord bmsPaymentRecord) {

        // permission
        List<PermissionConfig> permissionConfigs = List.of(
                new PermissionConfig(PermissionType.COMPANY.getCode(), "createDeptIds", PermissionConfig.FieldType.ARRAY_STRING, "id")
        );
        bmsPaymentRecord = userRights.applyPermissions(bmsPaymentRecord, permissionConfigs);

        return new ResponseResult<>(bmsPaymentRecordService.selectBmsPaymentRecordList(RequestContext.getToken(), bmsPaymentRecord));
    }

    //根据付款信息查询账单核销信息
    @PostMapping("/billList")
    public ResponseResult<PagerDataBean<BmsYfbillmain>> billList(@RequestBody(required = false) BmsYfbillmain bmsYfbillmain) {
        UserBean userVO = RequestContext.getUserInfo();
        if (userVO == null || RequestContext.getTenantId() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        //查询该记录下的核销账单
        List<BmsYfbillmain> bmsYfbillmains = bmsPaymentRecordMapper.selectBmsYfbillByRecordId(RequestContext.getTenantId(), bmsYfbillmain);
        PagerDataBean<BmsYfbillmain> bmsYfbillmains1 = new PagerDataBean<>();
        if (CollUtil.isNotEmpty(bmsYfbillmains)) {
            List<String> billCodeList = Arrays.stream(bmsYfbillmains.stream().map(BmsYfbillmain::getBillCode).collect(Collectors.joining(",")).split(",")).distinct().collect(Collectors.toList());
            BmsYfbillmain bmsYfbillmain1 = new BmsYfbillmain();
            bmsYfbillmain1.setIds(billCodeList);
            bmsYfbillmain1.setPageDomain(bmsYfbillmain.getPageDomain());
            bmsYfbillmains1 = bmsPaymentRecordMapper.selectBmsYfbillmainList(RequestContext.getTenantId(), bmsYfbillmain1);
        }
        return new ResponseResult<>(bmsYfbillmains1);
    }

    /**
     * 导出付款记录列表
     */
    @PostMapping("/export")
    @MenuAuthority(code = "应付付款管理-导出")
    public ResponseResult<String> export(@RequestBody(required = false) BmsPaymentRecord bmsPaymentRecord) {
        String token = RequestContext.getToken();
        return exportUtil.getOutClassNew(token, "付款记录信息", "付款管理", BmsPaymentRecord.class, userId -> {
            return bmsPaymentRecordService.selectBmsPaymentRecordList(token, bmsPaymentRecord).getRows();
        });
    }

    /**
     * 获取付款记录详细信息
     */
    @ApiOperation(value = "获取付款记录详细信息", response = BmsPaymentRecordDetail.class)
    @PostMapping(value = "/{id}")
    public ResponseResult<BmsPaymentRecordDetail> getInfo(@PathVariable("id") String id) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsPaymentRecordService.selectBmsPaymentRecordById(token, id));
    }

    /**
     * 新增付款记录
     */
    @PostMapping("/add")
    public ResponseResult<String> add(@RequestBody @Valid List<BmsPaymentRecord> list) {
        return new ResponseResult<>(bmsPaymentRecordService.insertBmsPaymentRecord(RequestContext.getToken(), list));
    }

    /**
     * 修改付款记录
     */
    @PostMapping("/edit")
    public ResponseResult<String> edit(@RequestBody BmsPaymentRecord bmsPaymentRecord) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsPaymentRecordService.updateBmsPaymentRecord(token, bmsPaymentRecord));
    }

    /**
     * 物理作废付款记录
     */
    @DeleteMapping("/{id}")
    @MenuAuthority(code = "应付付款管理-作废")
    public ResponseResult<String> remove(@PathVariable String id) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsPaymentRecordService.deleteBmsPaymentRecordById(token, id));
    }

    /**
     * 逻辑作废/启用付款记录
     */
    @PostMapping(value = "/updateStatus")
    public ResponseResult<Integer> updateStatus(@RequestBody Map<String, Object> idsAndStatus) {
        List<String> idsList = (ArrayList<String>) idsAndStatus.get("ids");
        String[] ids = idsList.toArray(new String[0]);
        String status = String.valueOf(idsAndStatus.get("status"));
        return new ResponseResult<>(bmsPaymentRecordService.updateBmsPaymentRecordStatusByIds(RequestContext.getToken(), ids, status));
    }
}
