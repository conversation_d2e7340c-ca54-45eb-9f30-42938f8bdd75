package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.PubQuoteruleTemplateDetail;
import com.bbyb.joy.bms.service.IPubQuoteruleTemplateDetailService;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.core.context.RequestContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 报价规则模板明细Controller
 */
@RestController
@RequestMapping("/system/templateDetail")
public class PubQuoteruleTemplateDetailController {

    @Resource
    private IPubQuoteruleTemplateDetailService pubQuoteruleTemplateDetailService;

    /**
     * 查询报价规则模板明细主列表
     */
    @GetMapping("/list")
    public ResponseResult<PagerDataBean<PubQuoteruleTemplateDetail>> list(@RequestBody(required = false) PubQuoteruleTemplateDetail pubQuoteruleTemplateDetail) {
        String token = RequestContext.getToken();
        if (pubQuoteruleTemplateDetail == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(pubQuoteruleTemplateDetailService.selectPubQuoteruleTemplateDetailList(token, pubQuoteruleTemplateDetail));
    }

}
