package com.bbyb.joy.bms.calculate.domain.dto.code.batch;

import cn.hutool.core.lang.Dict;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * 批计费共有属性
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateBatchDto implements Serializable {

    private static final long serialVersionUID = 1L;

    // 单据类型 (1:运输单,2仓储单)
    public Integer codeType;

    // 费用维度
    public Integer expensesType;          //计费费用维度(1调度单费2出库类费3入库类费4库存类费5仓储类费)

    // 运输配送信息
    public String lineCode;               // 线路编码
    public String lineName;               // 线路名称
    public String tmsLineCode;            // TMS线路编码
    public String tmsLineName;            // TMS线路名称

    // 仓储信息
    public String networkCode;            // 网点编码
    public String warehouseCode;          // 仓库编码
    public String warehouseName;          // 仓库名称

    // 地址信息
    public String originatingProvince="";    // 始发省
    public String originatingCity="";        // 始发市
    public String originatingArea="";        // 始发区
    public String destinationProvince="";    // 目的省
    public String destinationCity="";        // 目的市
    public String destinationArea="";        // 目的区
    public String receivingStoreCode="";     // 收货门店编码
    public String receivingStoreName="";     // 收货门店名称

    // 客户承运商信息
    public Integer clientId;              // 客户id
    public String clientCode;             // 客户编码
    public String clientName;             // 客户名称
    public Integer carrierId;             // 承运商id
    public String carrierCode;            // 承运商编码
    public String carrierName;            // 承运商名称
    public Integer companyId;             // 机构id
    public String companyName;            // 机构


    /**
     * 单据总数
     */
    public Integer codeNumber = 0;
    /**
     * 明细一总数
     */
    public Integer detailNumber = 0;
    /**
     * 明细一SKU总数
     */
    public Integer detailSkuNumber = 0;
    /**
     * 明细二总数
     */
    public Integer detail2Number = 0;
    /**
     * 总箱数
     */
    public BigDecimal totalBoxes = BigDecimal.ZERO;
    /**
     * 总件数
     */
    public BigDecimal totalNumber = BigDecimal.ZERO;
    /**
     * 总重量
     */
    public BigDecimal totalWeight = BigDecimal.ZERO;
    /**
     * 总体积
     */
    public BigDecimal totalVolume = BigDecimal.ZERO;
    /**
     * 总托数
     */
    public BigDecimal totalPalletNumber = BigDecimal.ZERO;
    /**
     * 总货值
     */
    public BigDecimal totalCargoValue = BigDecimal.ZERO;
    /**
     * 总超件数
     */
    public BigDecimal totalOverNum = BigDecimal.ZERO;
    /**
     * 超店次数
     */
    public BigDecimal totalOverStoreNum = BigDecimal.ZERO;
    /**
     * 总票数
     */
    public BigDecimal totalVotes = BigDecimal.ZERO;
    /**
     * 批涉及的单据id
     */
    public Set<Integer> codePkIds = new HashSet<>();
    public Map<Integer, Dict> codeDataMap = new HashMap<>();
    /**
     * 单据明细id
     */
    public Set<Integer> codeDetailPkIds = new HashSet<>();
    public Map<Integer, Dict> codeDetailDataMap = new HashMap<>();
    /**
     * 单据明细2id
     */
    public Set<Integer> codeDetail2PkIds = new HashSet<>();
    public Map<Integer, Dict> codeDetail2DataMap = new HashMap<>();
    /**
     * 业务日期
     */
    public Date businessTime;
    /**
     * 业务日期(yyyy-MM)
     */
    public String businessTimeMonth;
    /**
     * 业务开始日期(订单日期||配载日期(yyyy-MM-dd HH:mm:ss))
     */
    public Date businessBeginTime;
    /**
     * 业务结束日期(签收日期||完成日期(yyyy-MM-dd HH:mm:ss))
     */
    public Date businessEndTime;

    // 去重维度
    /**
     * 托盘编码去重
     */
    public Integer distinctTotalPalletNumber;



}
