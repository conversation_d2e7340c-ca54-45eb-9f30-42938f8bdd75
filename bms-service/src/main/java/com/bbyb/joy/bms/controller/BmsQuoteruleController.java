package com.bbyb.joy.bms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.biz.ClientQuoteruleBiz;
import com.bbyb.joy.bms.domain.dto.*;
import com.bbyb.joy.bms.domain.dto.dto.BmsQuoteruleDto;
import com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleDto;
import com.bbyb.joy.bms.domain.dto.dto.PubQuoteruleTemplateDto;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.Carrierinfo;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.domain.enums.ReceiptTypeEnum;
import com.bbyb.joy.bms.service.BmsQuoteruleService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import com.bbyb.joy.bms.support.utils.*;
import com.bbyb.joy.bms.support.utils.permission.PermissionConfig;
import com.bbyb.joy.bms.support.utils.permission.PermissionType;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.enums.PubNumEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.awt.*;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "新报价规则模块")
@RestController
@RequestMapping("/system/newquoterule")
public class BmsQuoteruleController {
    private static final Logger logger = LoggerFactory.getLogger(BmsQuoteruleController.class);

    @Resource
    BmsQuoteruleService baseService;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private UserRights userRights;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    private ClientQuoteruleBiz clientQuoteruleMapper;

    /**
     * 主合同分页查询
     *
     * <p> Date 2023-08-04
     */
    @ApiOperation("主合同分页查询")
    @PostMapping("/searchByLimit")
    @MenuAuthority(code = "客户报价设置")
    public ResponseResult<PagerDataBean<PubQuoteruleDto>> selMainContractList(@RequestBody(required = false) PubQuoteruleDto data) {
        UserBean userVO = RequestContext.getUserInfo();
        if (userVO == null || userVO.getId() == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), ServiceError.TOKEN_ERROR.getMessage());
        }
        List<PubQuoteruleDto> failList = new ArrayList<>();
        String checkMsg = ValidationUtil.validateMsg(data, ValidationTypeEnum.SEL.class);
        if (StrUtil.isNotEmpty(checkMsg)) {
            return new ResponseResult<>(new PagerDataBean<>(failList, 0, new PagerBean(1, 20)));
        }

        // permission
        List<PermissionConfig> permissionConfigs = Arrays.asList(
                new PermissionConfig(PermissionType.CLIENT.getCode(), "clientIds", PermissionConfig.FieldType.LIST_STRING, "id"),
                new PermissionConfig(PermissionType.CARRIER.getCode(), "carrierIds", PermissionConfig.FieldType.LIST_STRING, "id")
        );
        data = userRights.applyPermissions(data, permissionConfigs);

        return new ResponseResult<>(baseService.selMainContractList(RequestContext.getToken(), data));
    }

    @ApiOperation("添加合同主信息")
    @PostMapping("/insertContract")
    @MenuAuthority(code = "客户报价设置-新增,客户报价设置-复制,客户报价设置-新增报价,客户报价设置-复制报价")
    public ResponseResult<String> insertContract(@RequestBody(required = false) BmsQuoteruleDto data) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(baseService.insertContractInfo(token, data));
    }


    @ApiOperation("获取合同主信息")
    @PostMapping("/getContract")
    public ResponseResult<BmsQuoteruleDto> getContract(@RequestBody(required = false) BmsQuoteruleDto data) {
        return new ResponseResult<>(baseService.getContractInfo(RequestContext.getToken(), data));
    }

    ;


    @ApiOperation("修改合同主信息")
    @PostMapping("/updateContract")
    @MenuAuthority(code = "客户报价设置-修改,客户报价设置-审核,客户报价设置-修改报价,客户报价设置-审核报价,客户报价设置-取消审核")
    public ResponseResult<String> updateContract(@RequestBody(required = false) BmsQuoteruleDto data) {
        return new ResponseResult<>(baseService.updateContractInfo(RequestContext.getToken(), data));
    }


    @ApiOperation("是否启用")
    @PostMapping("/mainIsEnable")
    @MenuAuthority(code = "客户报价设置-启用,客户报价设置-禁用,客户报价设置-启用报价,客户报价设置-禁用报价")
    public ResponseResult<String> mainIsEnable(@RequestBody(required = false) BmsQuoteruleDto data) {
        return new ResponseResult<>(baseService.mainIsEnable(RequestContext.getToken(), data));
    }


    /**
     * 报价导出功能
     *
     * @return 导出入参条件 分为导出和单条导出带明细
     * 前期需要将兼容承运商的逻辑考虑进去
     * 目前只做了应收
     */
    @ApiOperation("导出")
    @PostMapping("/export")
    public ResponseResult<String> export(@RequestBody(required = false) BmsQuoteruleDto param) {
        String token = RequestContext.getToken();
        String checkMsg = ValidationUtil.validateMsg(param, ValidationTypeEnum.EXPORT.class);
        if (StrUtil.isNotEmpty(checkMsg)) {
            return new ResponseResult<>(40005, checkMsg);
        }
        //不下载明细的时候处理查询条件
        if (param.getDownDetail().equals(BmsConstants.IS_DOWN_DETAIL_1)) {
            PubQuoteruleDto data = param.getMainQuoterule();
            if (StrUtil.isEmpty(data.getCompanyIds())) {
                data.setCompanyIds(textConversionUtil.getCompanyIds());
            }
            Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
            SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client,carrier");
            List<ClientInfo> clientList = sysDataPack.getClientinfo();
            List<Carrierinfo> carrierList = sysDataPack.getCarrierInfo();

            if (data.getRuleType().equals(BmsConstants.RULE_TYPE_1)) {
                if (CollUtil.isNotEmpty(clientList)) {
                    data.setClientIds(clientList.stream().map(k -> k.getId().toString()).collect(Collectors.toList()));
                }
            }
            if (data.getRuleType().equals(BmsConstants.RULE_TYPE_2)) {
                if (CollUtil.isNotEmpty(carrierList)) {
                    data.setCarrierIds(carrierList.stream().map(k -> k.getId().toString()).collect(Collectors.toList()));
                }
            }
            param.setMainQuoterule(data);
        }
        List<PubQuoteruleDto> exportDatas = baseService.export(token, param);
        String excelTitle = "客户主合同信息";
        return exportUtil.getOutClassNew(token, excelTitle, "客户报价设置", PubQuoteruleDto.class, returnData -> {
            return exportDatas;
        });
    }


    /**
     * 报价明细导出
     *
     * @return AjaxResult
     * <AUTHOR>
     */
    @ApiOperation("客户报价设置明细导出")
    @PostMapping("/exportDetail")
    public ResponseResult<String> exportDetail(@RequestBody(required = false) BmsQuoteruleDto param) {
        if (param == null || StrUtil.isEmpty(param.getId())) {
            return new ResponseResult<>(40005, "参数不能为空");
        }
        UserBean userVO = RequestContext.getUserInfo();
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYYMMDDHHMMSS);
        PubQuoteruleDto mainContract = clientQuoteruleMapper.getMainContract(RequestContext.getTenantId(), param.getId());
        String excelTitle = "-" + mainContract.getRelationName() + "-" + mainContract.getRuleCode() + "-" + sdf.format(new Date());
        return exportUtil.getClientQuoteruleDetailSheets(RequestContext.getToken(), excelTitle, "客户报价设置", param.getId());
    }

    public FileBaseDto exportSheets(String token, String id) {
        FileBaseDto fileBaseDto = new FileBaseDto();
        try {
            UserBean userVO = RequestContext.getUserInfo();
            //查询合同主信息
            PubQuoteruleDto mainContract = clientQuoteruleMapper.getMainContract(RequestContext.getTenantId(), id);
            Map<String, String> dicMap = textConversionUtil.getDictByType("ht_type,bms_business_type,is_enable,ht_state,bms_contract_review,quoterule_bill_type,feeType,warm_zone_type", "1");
            //Excel
            SXSSFWorkbook hssfWorkbook = new SXSSFWorkbook();
            /*
             * 第一个sheet
             */
            hssfWorkbook = homePage(hssfWorkbook, mainContract, dicMap, id);
            //查询合同明细，按照明细展示sheet
            List<PubQuoteruleDetail> pubQuoteruleDetails = clientQuoteruleMapper.selectPubQuoteruleDetailList(RequestContext.getTenantId(), id);
            if (CollUtil.isNotEmpty(pubQuoteruleDetails)) {
                for (PubQuoteruleDetail pubQuoteruleDetail : pubQuoteruleDetails) {
                    if (pubQuoteruleDetail.getFeeType() == null && pubQuoteruleDetail.getBillType().equals(PubNumEnum.five.getIntValue())) {
                        pubQuoteruleDetail.setFeeType(BmsConstants.FEE_TYPE_ADD_FEE);
                    }
                }
                //查询所有明细的明细列表
                List<PubQuotationClassificationdetail> pubClass = clientQuoteruleMapper.getPubClass(RequestContext.getTenantId(), pubQuoteruleDetails.stream().map(PubQuoteruleDetail::getId).collect(Collectors.toList()));
                List<String> itemId = pubClass.stream().filter(e -> e.getItemId() != null).map(e -> e.getItemId().toString()).collect(Collectors.toList());
                itemId.addAll(pubClass.stream().map(PubQuotationClassificationdetail::getItemId1).filter(StrUtil::isNotEmpty).collect(Collectors.toList()));
                Map<String, BmsExpenseItemInfo> stringBmsExpenseItemInfoMap = new HashMap<>();
                if (CollUtil.isNotEmpty(itemId)) {
                    stringBmsExpenseItemInfoMap = textConversionUtil.selectPubFeeSubjectMap(itemId, null, null, null, null);
                }
                if (CollUtil.isNotEmpty(pubClass)) {
                    Map<String, List<PubQuotationClassificationdetail>> pubClassMap = pubClass.stream().collect(Collectors.groupingBy(PubQuotationClassificationdetail::getQuoteruledetailId));
                    //查询模板
                    List<PubQuoteruleTemplateDto> quoteruleTemplate = clientQuoteruleMapper.getQuoteruleTemplate(RequestContext.getTenantId(), pubQuoteruleDetails.stream().map(PubQuoteruleDetail::getQuoteruleTemplateId).distinct().collect(Collectors.toList()));

                    if (CollUtil.isNotEmpty(quoteruleTemplate)) {
                        //运输明细
                        List<PubQuoteruleDetail> ysPubQuoteruleDetailList = pubQuoteruleDetails.stream().filter(e -> e.getBillType().equals(ReceiptTypeEnum.TRANS_ORDER.getIntValue())).collect(Collectors.toList());
                        //仓储明细
                        List<PubQuoteruleDetail> ccPubQuoteruleDetailList = pubQuoteruleDetails.stream().filter(e -> !e.getBillType().equals(ReceiptTypeEnum.TRANS_ORDER.getIntValue())).collect(Collectors.toList());
                        //模板 key id
                        Map<String, PubQuoteruleTemplateDto> quoteruleTemplateMap = quoteruleTemplate.stream().collect(Collectors.toMap(PubQuoteruleTemplateDto::getId, Function.identity()));
                        Comparator<PubQuoteruleDetail> byFeeType = Comparator.comparing(PubQuoteruleDetail::getFeeType);
                        ysPubQuoteruleDetailList.sort(byFeeType);
                        //循环运输业务sheet
                        Map<Integer, List<PubQuoteruleDetail>> quoteruleDetailFeeMap = ysPubQuoteruleDetailList.stream().filter(e -> e.getFeeType() != null).collect(Collectors.groupingBy(PubQuoteruleDetail::getFeeType));
                        for (Map.Entry<Integer, List<PubQuoteruleDetail>> integerListEntry : quoteruleDetailFeeMap.entrySet()) {
                            List<PubQuoteruleDetail> value = integerListEntry.getValue();
                            if (value.size() > 1) {
                                int i = 1;
                                for (PubQuoteruleDetail pubQuoteruleDetail : value) {
                                    //模板存在，添加sheet  相同sheet名成则后缀加序号
                                    if (quoteruleTemplateMap.containsKey(pubQuoteruleDetail.getQuoteruleTemplateId()) && pubClassMap.containsKey(pubQuoteruleDetail.getId())) {
                                        String sheetName = dicMap.get("quoterule_bill_type" + pubQuoteruleDetail.getBillType()) + "-" + pubQuoteruleDetail.getFeeTypeStr();
                                        hssfWorkbook = subPage(hssfWorkbook, quoteruleTemplateMap.get(pubQuoteruleDetail.getQuoteruleTemplateId()), pubClassMap.get(pubQuoteruleDetail.getId()), sheetName + i, dicMap, stringBmsExpenseItemInfoMap);
                                        i += 1;
                                    }
                                }
                            } else {
                                for (PubQuoteruleDetail pubQuoteruleDetail : value) {
                                    String sheetName = dicMap.get("quoterule_bill_type" + pubQuoteruleDetail.getBillType()) + "-" + pubQuoteruleDetail.getFeeTypeStr();
                                    hssfWorkbook = subPage(hssfWorkbook, quoteruleTemplateMap.get(pubQuoteruleDetail.getQuoteruleTemplateId()), pubClassMap.get(pubQuoteruleDetail.getId()), sheetName, dicMap, stringBmsExpenseItemInfoMap);
                                }
                            }
                        }

                        //仓储业务员sheet
                        ccPubQuoteruleDetailList.sort(byFeeType);
                        Map<Integer, List<PubQuoteruleDetail>> ccQuoteruleDetailFeeMap = ccPubQuoteruleDetailList.stream().filter(e -> e.getFeeType() != null).collect(Collectors.groupingBy(PubQuoteruleDetail::getFeeType));
                        for (Map.Entry<Integer, List<PubQuoteruleDetail>> integerListEntry : ccQuoteruleDetailFeeMap.entrySet()) {
                            List<PubQuoteruleDetail> value = integerListEntry.getValue();
                            if (value.size() > 1) {
                                int i = 1;
                                for (PubQuoteruleDetail pubQuoteruleDetail : value) {
                                    //模板存在，添加sheet  相同sheet名成则后缀加序号
                                    if (quoteruleTemplateMap.containsKey(pubQuoteruleDetail.getQuoteruleTemplateId()) && pubClassMap.containsKey(pubQuoteruleDetail.getId())) {
                                        String sheetName = dicMap.get("quoterule_bill_type" + pubQuoteruleDetail.getBillType()) + "-" + (StrUtil.isNotEmpty(pubQuoteruleDetail.getFeeTypeStr()) ? pubQuoteruleDetail.getFeeTypeStr() : "");
                                        hssfWorkbook = subPage(hssfWorkbook, quoteruleTemplateMap.get(pubQuoteruleDetail.getQuoteruleTemplateId()), pubClassMap.get(pubQuoteruleDetail.getId()), sheetName + i, dicMap, stringBmsExpenseItemInfoMap);
                                        i += 1;
                                    }
                                }
                            } else {
                                for (PubQuoteruleDetail pubQuoteruleDetail : value) {
                                    String sheetName = dicMap.get("quoterule_bill_type" + pubQuoteruleDetail.getBillType()) + "-" + (StrUtil.isNotEmpty(pubQuoteruleDetail.getFeeTypeStr()) ? pubQuoteruleDetail.getFeeTypeStr() : "");
                                    hssfWorkbook = subPage(hssfWorkbook, quoteruleTemplateMap.get(pubQuoteruleDetail.getQuoteruleTemplateId()), pubClassMap.get(pubQuoteruleDetail.getId()), sheetName, dicMap, stringBmsExpenseItemInfoMap);
                                }
                            }
                        }
                    }
                }

            }
            SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYYMMDDHHMMSS);
            try {
                FileOutputStream out = null;
                String fileName = "_" + mainContract.getRelationName() + "_" + mainContract.getRuleCode() + "_" + sdf.format(new Date()) + ".xlsx";
                String absoluteFile = ExcelExportUtil.getAbsoluteFile(fileName);
                out = new FileOutputStream(absoluteFile);
                try {
                    hssfWorkbook.write(out);
                    out.close();
                    File file = new File(absoluteFile);
                    String readableSize = FileUtil.readableFileSize(file.length());
                    fileBaseDto.setFileName(fileName);
                    fileBaseDto.setFileSize(readableSize);
                    return fileBaseDto;
                } catch (IOException e) {
                    logger.error("系统异常", e);
                    log.error("系统异常", e);
                    fileBaseDto.setFileName("导出异常");
                    return fileBaseDto;
                }

            } catch (FileNotFoundException e) {
                FileOutputStream out = null;
                // 文件名
                String fileName = "_" + mainContract.getRelationName() + "_" + mainContract.getRuleCode() + "_" + sdf.format(new Date()) + ".xlsx";
                String absoluteFileios = ExcelExportUtil.getAbsoluteFileios(fileName);
                out = new FileOutputStream(absoluteFileios);
                try {
                    ResponseResult<String> result = new ResponseResult<>(true, 0, fileName, fileName);
                    hssfWorkbook.write(out);
                    out.close();
                    File file = new File(absoluteFileios);
                    String readableSize = FileUtil.readableFileSize(file.length());
                    fileBaseDto.setFileName(fileName);
                    fileBaseDto.setFileSize(readableSize);
                    return fileBaseDto;
                } catch (IOException ie) {
                    log.error("系统异常", e);
                    fileBaseDto.setErrMsg("导出异常");
                    return fileBaseDto;
                }
            }
        } catch (Exception e) {
            logger.error("系统异常", e);
            log.error("系统异常", e);
            fileBaseDto.setFileName("导出异常");
            return fileBaseDto;
        }
    }

    /**
     * 第一个sheet
     *
     * @param hssfWorkbook 参
     * @param mainContract 参
     * @param dicMap       参
     * @param id           参
     */
    public SXSSFWorkbook homePage(SXSSFWorkbook hssfWorkbook, PubQuoteruleDto mainContract, Map<String, String> dicMap, String id) {

        // 第一个sheet
        Sheet sheet1 = hssfWorkbook.createSheet(mainContract.getRelationName() + "-主合同");
        sheet1.setColumnWidth(0, 15 * 256);
        sheet1.setColumnWidth(1, 25 * 256);

        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);

        final int[] rowNum = {0};
        //第一行 合同编码
        Row r0 = sheet1.createRow(rowNum[0]++);
        String[] rowFirst = {"合同编码", mainContract.getRuleCode()};
        for (int i = 0; i < rowFirst.length; i++) {
            Cell tempCell = r0.createCell(i);
            tempCell.setCellValue(rowFirst[i]);
            tempCell.setCellStyle(commonStyle);
        }
        //第二行 合同类型
        Row r1 = sheet1.createRow(rowNum[0]++);
        String[] row1 = {"合同类型", dicMap.getOrDefault("ht_type" + mainContract.getHtType(), "")};
        for (int i = 0; i < row1.length; i++) {
            Cell tempCell = r1.createCell(i);
            tempCell.setCellValue(row1[i]);
            tempCell.setCellStyle(commonStyle);
        }
        //第三行 客户名称
        Row r2 = sheet1.createRow(rowNum[0]++);
        String[] row2 = {"客户名称", mainContract.getRelationName()};
        for (int i = 0; i < row2.length; i++) {
            Cell tempCell = r2.createCell(i);
            tempCell.setCellValue(row2[i]);
            tempCell.setCellStyle(commonStyle);
        }
        //第四行 业务类型
        Row r3 = sheet1.createRow(rowNum[0]++);
        String[] row3 = {"业务类型", dicMap.getOrDefault("bms_business_type" + mainContract.getBusinessType(), "")};
        for (int i = 0; i < row3.length; i++) {
            Cell tempCell = r3.createCell(i);
            tempCell.setCellValue(row3[i]);
            tempCell.setCellStyle(commonStyle);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        //第五行 报价生效时间
        Row r4 = sheet1.createRow(rowNum[0]++);
        String[] row4 = {"报价生效时间", sdf.format(mainContract.getStartTime())};
        for (int i = 0; i < row4.length; i++) {
            Cell tempCell = r4.createCell(i);
            tempCell.setCellValue(row4[i]);
            tempCell.setCellStyle(commonStyle);
        }
        //第6行 报价生效时间
        Row r5 = sheet1.createRow(rowNum[0]++);
        String[] row5 = {"报价失效时间", sdf.format(mainContract.getEndTime())};
        for (int i = 0; i < row5.length; i++) {
            Cell tempCell = r5.createCell(i);
            tempCell.setCellValue(row5[i]);
            tempCell.setCellStyle(commonStyle);
        }

        //第7行 预警时间
        Row r6 = sheet1.createRow(rowNum[0]++);
        String[] row6 = {"预警时间", sdf.format(mainContract.getWarningTime())};
        for (int i = 0; i < row6.length; i++) {
            Cell tempCell = r6.createCell(i);
            tempCell.setCellValue(row6[i]);
            tempCell.setCellStyle(commonStyle);
        }

        //第8行 是否启用
        Row r7 = sheet1.createRow(rowNum[0]++);
        String[] row7 = {"是否启用", dicMap.getOrDefault("is_enable" + mainContract.getIsEnable(), "")};
        for (int i = 0; i < row7.length; i++) {
            Cell tempCell = r7.createCell(i);
            tempCell.setCellValue(row7[i]);
            tempCell.setCellStyle(commonStyle);
        }

        //第9行 新增人
        Row r8 = sheet1.createRow(rowNum[0]++);
        String[] row8 = {"新增人", mainContract.getCreateBy()};
        for (int i = 0; i < row8.length; i++) {
            Cell tempCell = r8.createCell(i);
            tempCell.setCellValue(row8[i]);
            tempCell.setCellStyle(commonStyle);
        }
        //第10行 新增时间
        Row r9 = sheet1.createRow(rowNum[0]++);
        String[] row9 = {"新增时间", sdf.format(mainContract.getCreateTime())};
        for (int i = 0; i < row9.length; i++) {
            Cell tempCell = r9.createCell(i);
            tempCell.setCellValue(row9[i]);
            tempCell.setCellStyle(commonStyle);
        }
        //第11行 操作人
        Row r10 = sheet1.createRow(rowNum[0]++);
        String[] row10 = {"操作人", mainContract.getOperBy()};
        for (int i = 0; i < row10.length; i++) {
            Cell tempCell = r10.createCell(i);
            tempCell.setCellValue(row10[i]);
            tempCell.setCellStyle(commonStyle);
        }
        //第12行 操作时间
        Row r11 = sheet1.createRow(rowNum[0]++);
        String[] row11 = {"操作时间", mainContract.getOperTime() != null ? sdf.format(mainContract.getOperTime()) : ""};
        for (int i = 0; i < row11.length; i++) {
            Cell tempCell = r11.createCell(i);
            tempCell.setCellValue(row11[i]);
            tempCell.setCellStyle(commonStyle);
        }
        return hssfWorkbook;
    }

    /**
     * 循环子sheet
     *
     * @param hssfWorkbook                      参
     * @param quoteruleTemplateDto              模板数据
     * @param quotationClassificationdetailList 明细数据
     * @param sheetName                         sheet名
     * @param dicMap                            字典
     * @return SXSSFWorkbook
     */
    public SXSSFWorkbook subPage(SXSSFWorkbook hssfWorkbook, PubQuoteruleTemplateDto quoteruleTemplateDto, List<PubQuotationClassificationdetail> quotationClassificationdetailList, String sheetName, Map<String, String> dicMap, Map<String, BmsExpenseItemInfo> stringBmsExpenseItemInfoMap) {
        // sheet名
        Sheet sheet = hssfWorkbook.createSheet(sheetName);

        // 背景色的设定
        //表头样式
        XSSFCellStyle sheet2HeaderStyle = ExcelExportUtil.getDefaultTitleSheet2Style(hssfWorkbook);
        //正文内容样式
        XSSFCellStyle commonStyle = ExcelExportUtil.getDefaultContentStyle(hssfWorkbook);
        //正文样式
        sheet2HeaderStyle.setFillForegroundColor(new XSSFColor(new Color(0, 85, 145)));
        sheet2HeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        //TODO 2、组装数值数据
        //所有行的要汇总的数值
        //行标
        final int[] rowNum = {0};

        //创建首行
        String[] excelHeaderFields = quoteruleTemplateDto.getTemplateDetails().stream().map(PubQuoteruleTemplateDetail::getAnotherName).collect(Collectors.joining(",")).split(",");
        Map<String, String> fieldMap = quoteruleTemplateDto.getTemplateDetails().stream().collect(Collectors.toMap(PubQuoteruleTemplateDetail::getAnotherName, PubQuoteruleTemplateDetail::getFieldEnglish, (k1, k2) -> k1));
        Row headerRow = sheet.createRow(rowNum[0]++);
        headerRow.setHeight((short) 500);

        //设置列头
        String[] fieldsEnglish = new String[excelHeaderFields.length];
        for (int i = 0; i < excelHeaderFields.length; i++) {
            Cell tempCell = headerRow.createCell(i);
            tempCell.setCellValue(excelHeaderFields[i]);
            tempCell.setCellStyle(sheet2HeaderStyle);
            fieldsEnglish[i] = fieldMap.get(excelHeaderFields[i]);
        }
        //循环没行数据
        for (PubQuotationClassificationdetail pubQuotationClassificationdetail : quotationClassificationdetailList) {
            Row dataRow = sheet.createRow(rowNum[0]++);
            dataRow.setHeight((short) 500);
            for (int i = 0; i < fieldsEnglish.length; i++) {
                Cell tempCell = dataRow.createCell(i);
                String tempCellValue = "";
                Field[] declaredFields = pubQuotationClassificationdetail.getClass().getDeclaredFields();
                for (Field field : declaredFields) {
                    try {
                        field.setAccessible(true);
                        //匹配字段与值
                        if (fieldsEnglish[i].equals(field.getName())) {
                            if (field.get(pubQuotationClassificationdetail) != null) {
                                Type genericType = field.getGenericType();
                                //数值类型保留两位小数
                                if (genericType.equals(BigDecimal.class)) {
                                    tempCellValue = new BigDecimal(field.get(pubQuotationClassificationdetail).toString()).setScale(2, RoundingMode.HALF_UP).toString();
                                } else {
                                    tempCellValue = field.get(pubQuotationClassificationdetail).toString();
                                }
                                if (StrUtil.isNotEmpty(tempCellValue)) {
                                    switch (field.getName()) {
                                        case "isRemovezero":
                                            if ("0".equals(tempCellValue)) {
                                                tempCellValue = "否";
                                            } else {
                                                tempCellValue = "是";
                                            }
                                            break;
                                        case "itemId":
                                            //增值费二级类型
                                            if (stringBmsExpenseItemInfoMap.containsKey(tempCellValue)) {
                                                tempCellValue = stringBmsExpenseItemInfoMap.get(tempCellValue).getItemName();
                                            }
                                            break;
                                        case "itemId1":
                                            //增值费一级类型
                                            if (stringBmsExpenseItemInfoMap.containsKey(tempCellValue)) {
                                                tempCellValue = stringBmsExpenseItemInfoMap.get(tempCellValue).getItemName();
                                            }
                                            break;
                                    }
                                }
                            }
                            break;
                        }
                    } catch (Exception e) {
                        logger.error("系统异常", e);
                        log.error("系统异常", e);
                    }

                }
                tempCell.setCellValue(tempCellValue);
                tempCell.setCellStyle(commonStyle);
            }
        }
        return hssfWorkbook;
    }
}
