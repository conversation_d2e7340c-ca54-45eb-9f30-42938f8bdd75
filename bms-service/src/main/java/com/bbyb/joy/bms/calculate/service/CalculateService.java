package com.bbyb.joy.bms.calculate.service;

import com.bbyb.bms.model.po.PubAutomaticLogImplementPO;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderExpandDto;
import com.bbyb.joy.bms.calculate.domain.model.CalculateFactorInfoModel;
import com.bbyb.joy.bms.calculate.domain.param.CalculateDebuggerParam;
import com.bbyb.joy.bms.calculate.domain.result.CalculateYsDbResult;
import com.bbyb.joy.bms.code.domain.param.BmsDispatchCodeInfoParam;
import com.bbyb.joy.bms.code.domain.param.BmsTransCodeInfoParam;
import com.bbyb.joy.bms.code.domain.param.BmsYfStorageCodeInfoParam;
import com.bbyb.joy.bms.code.domain.param.BmsYsStorageCodeInfoParam;
import com.bbyb.joy.bms.domain.enums.AutoCalculateModuleEnum;

import java.util.List;

/**
 * 自动计费服务
 * 服务约定
 * 目前服务理论是支持分布式的,从业务角度做了锁机制
 */
public interface CalculateService {

    /**
     * 加锁
     */
    PubAutomaticLogImplementPO lock(AutoCalculateModuleEnum moduleEnum);
    /**
     * 解锁
     */
    void unlock(PubAutomaticLogImplementPO param);
    /**
     * 检查是否在执行中
     */
    boolean checkLock(AutoCalculateModuleEnum moduleEnum);
    /**
     * 自动计费服务调用(提供赋能)
     * DIRECT_EXECUTE
     * @param codes 自动计费格式单据信息
     * @param ruleLisParam 涉及合同
     * @param debuggerParam 调试参数
     * @return 计费结果
     */
    CalculateYsDbResult calculate(List<CalculateOrderExpandDto> codes,List<CalculateFactorInfoModel> ruleLisParam, CalculateDebuggerParam debuggerParam);
    /**
     * 运输单自动计费
     */
    void transCalculate(BmsTransCodeInfoParam param, CalculateDebuggerParam debuggerParam);
    /**
     * 仓储单自动计费(应收)
     */
    void ysStorageCalculate(BmsYsStorageCodeInfoParam param, CalculateDebuggerParam debuggerParam);
    /**
     * 仓储单自动计费(应付)
     */
    void yfStorageCalculate(BmsYfStorageCodeInfoParam param, CalculateDebuggerParam debuggerParam);
    /**
     * 调度单自动计费
     */
    void dispatchCalculate(BmsDispatchCodeInfoParam param, CalculateDebuggerParam debuggerParam);


}
