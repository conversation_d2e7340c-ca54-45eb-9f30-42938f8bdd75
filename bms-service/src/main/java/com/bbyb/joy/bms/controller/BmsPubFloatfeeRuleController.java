package com.bbyb.joy.bms.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.BmsPubFloatFeeAmountRequest;
import com.bbyb.joy.bms.domain.dto.BmsPubFloatfeeRule;
import com.bbyb.joy.bms.domain.dto.BmsPubFloatfeeRuleMain;
import com.bbyb.joy.bms.domain.dto.dto.BmsExpenseItemByPubFloatFeeDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsMdmClientinfoDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsPubFloatfeeRuleEditDto;
import com.bbyb.joy.bms.service.IBmsPubFloatfeeRuleService;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;


/**
 * 浮动计费规则表;(bms_pub_floatfee_rule)表控制层
 */
@RestController
@RequestMapping("/system/bmsPubFloatfeeRule")
public class BmsPubFloatfeeRuleController {

    @Resource
    private IBmsPubFloatfeeRuleService baseService;


    /**
     * 新增数据
     *
     * @param bmsPubFloatfeeRule 实例对象
     * @return 实例对象
     */
    @PostMapping("/insert")
    @MenuAuthority(code = "应收浮动计费规则-新增")
    public ResponseResult<String> add(@RequestBody(required = false) BmsPubFloatfeeRuleMain bmsPubFloatfeeRule) {
        if (bmsPubFloatfeeRule == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(baseService.insert(RequestContext.getToken(), bmsPubFloatfeeRule));
    }

    /**
     * 查询详情
     *
     * @param id 实例对象
     * @return 实例对象
     */
    @ApiOperation("查询详情")
    @PostMapping("/detail/query/{id}")
    public ResponseResult<BmsPubFloatfeeRuleMain> queryDetail(@PathVariable("id") String id) {
        return new ResponseResult<>(baseService.queryDetail(RequestContext.getToken(), id));
    }


    /**
     * 分页查询浮动计费
     */
    @PostMapping("/searchListByRuleName")
    public ResponseResult<PagerDataBean<BmsPubFloatfeeRule>> searchListByRuleName(@RequestBody(required = false) BmsPubFloatfeeRule bmsPubFloatfeeRule) throws Exception {
        if (bmsPubFloatfeeRule == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(baseService.searchListByRuleName(RequestContext.getToken(), bmsPubFloatfeeRule));
    }


    /**
     * 分页查询浮动计费
     * <p> Date 2023-03-14 14:08
     */
    @PostMapping("/searchByLimit")
    public ResponseResult<PagerDataBean<BmsPubFloatfeeRule>> searchByLimit(@RequestBody(required = false) BmsPubFloatfeeRule bmsPubFloatfeeRule) {
        if (bmsPubFloatfeeRule == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(baseService.searchByLimit(RequestContext.getToken(), bmsPubFloatfeeRule));
    }


    /**
     * 是否启用
     * 是否启用(0启用,1不启用)
     * <p> Date 2023-03-14 14:08
     */
    @PostMapping("/editIsEnable")
    @MenuAuthority(code = "应收浮动计费规则-启用,应收浮动计费规则-禁用")
    public ResponseResult<String> editIsEnable(@RequestBody(required = false) BmsPubFloatfeeRule bmsPubFloatfeeRule) {
        if (bmsPubFloatfeeRule == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(baseService.editIsEnable(RequestContext.getToken(), bmsPubFloatfeeRule));
    }

    /**
     * 作废"
     * <p> Date 2023-03-14 14:08
     */
    @ApiOperation("作废")
    @PostMapping("/deleted")
    @MenuAuthority(code = "应收浮动计费规则-作废")
    public ResponseResult<String> delInfo(@RequestBody(required = false) BmsPubFloatfeeRule bmsPubFloatfeeRule) {
        return new ResponseResult<>(baseService.delInfo(RequestContext.getToken(), bmsPubFloatfeeRule));
    }


    /**
     * 查询客户信息(无需权限)
     * <p> Date 2023-03-14 14:08
     */
    @PostMapping("/selMdmClientInfo")
    public ResponseResult<List<BmsMdmClientinfoDto>> selMdmClientInfo(@RequestBody(required = false) String json) {
        return new ResponseResult<>(baseService.selMdmClientInfo(RequestContext.getToken()));
    }


    /**
     * 只查询浮动计费类型， xml里的sql直接写死
     * <p> Date 2023-03-14 14:08
     */
    @PostMapping("/queryBmsExpenseItemInfo")
    public ResponseResult<List<BmsExpenseItemByPubFloatFeeDto>> queryBmsExpenseItemInfo(@RequestBody(required = false) String json) {
        Integer tag = JSONObject.parseObject(json).getInteger("tag");
        return new ResponseResult<>(baseService.queryBmsExpenseItemInfo(RequestContext.getToken(), tag));
    }

    @PostMapping("/queryBmsExpenseItemUnitInfo")
    public ResponseResult<List<BmsExpenseItemByPubFloatFeeDto>> queryBmsExpenseItemUnitInfo(HttpServletRequest request, @RequestBody BmsPubFloatfeeRuleEditDto pubFloatfeeRuleEditDto) {
        return new ResponseResult<>(baseService.queryBmsExpenseItemUnitInfo(RequestContext.getToken(), pubFloatfeeRuleEditDto.getId()));
    }


    /**
     * 修改
     * <p> Date 2023-03-14 14:08
     */
    @PostMapping("/edit")
    @MenuAuthority(code = "应收浮动计费规则-修改")
    public ResponseResult<String> edit(@RequestBody(required = false) BmsPubFloatfeeRuleEditDto pubFloatfeeRuleEditDto) {
        if (pubFloatfeeRuleEditDto == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(baseService.edit(RequestContext.getToken(), pubFloatfeeRuleEditDto));
    }


    /**
     * 查询
     * <p> Date 2023-03-14 14:08
     */
    @PostMapping("/get")
    public ResponseResult<Map<String, Object>> get(@RequestBody(required = false) String json) {
        if (StrUtil.isEmpty(json)) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        String ruleId = JSONObject.parseObject(json).getString("ruleId");
        return new ResponseResult<>(baseService.get(RequestContext.getToken(), ruleId));
    }


    /**
     * 修改浮动费用基础信息
     * <p> Date 2023-03-28 13:32
     */
    @PostMapping("/update")
    @MenuAuthority(code = "应收浮动计费规则-修改")
    public ResponseResult<String> update(@RequestBody(required = false) BmsPubFloatfeeRule bmsPubFloatfeeRule) {
        if (bmsPubFloatfeeRule == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(baseService.update(RequestContext.getToken(), bmsPubFloatfeeRule));
    }

    @ApiOperation("编辑费用基础信息")
    @PostMapping("/amount/binding")
    @MenuAuthority(code = "应收浮动计费规则-绑定客户,应收浮动计费规则-作废,应收浮动计费规则-按金额浮动费项配置")
    public ResponseResult<String> bindingAmountType(@RequestBody BmsPubFloatFeeAmountRequest request2) {
        if (request2 == null) {
            return new ResponseResult<>(ServiceError.TOKEN_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(baseService.bindingAmountType(RequestContext.getToken(), request2));
    }
}
