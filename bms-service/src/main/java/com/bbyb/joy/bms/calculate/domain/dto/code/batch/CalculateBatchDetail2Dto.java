package com.bbyb.joy.bms.calculate.domain.dto.code.batch;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 批详情2(公共属性)
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CalculateBatchDetail2Dto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总箱数
     */
    private BigDecimal totalBoxes = BigDecimal.ZERO;
    /**
     * 总件数
     */
    private BigDecimal totalNumber = BigDecimal.ZERO;

    /**
     * 总重量
     */
    private BigDecimal totalWeight = BigDecimal.ZERO;

    /**
     * 总体积
     */
    private BigDecimal totalVolume = BigDecimal.ZERO;


}
