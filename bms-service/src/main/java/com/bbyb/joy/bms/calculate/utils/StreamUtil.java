package com.bbyb.joy.bms.calculate.utils;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collector;

public class StreamUtil {


    /**
     * 使用示例：计算订单的多个汇总指标
     */
//    public Map<String, Object> calculateOrderSummary(List<CalculateOrderDto> orders) {
//        return orders.stream()
//                .collect(multiSummarizing(
//                        results -> {
//                            Map<String, Object> summary = new HashMap<>();
//                            summary.put("count", results[0]);
//                            summary.put("totalBoxes", results[1]);
//                            summary.put("totalWeight", results[2]);
//                            return summary;
//                        },
//                        Collectors.counting(),
//                        Collectors.summingInt(o -> o.getTotalBoxes() != null ? o.getTotalBoxes().intValue() : 0),
//                        Collectors.summingDouble(o -> o.getTotalWeight() != null ? o.getTotalWeight().doubleValue() : 0)
//                ));
//    }


    /**
     * 多属性汇总工具方法
     * @param <T> 集合元素类型
     * @param <R> 返回结果类型
     * @param merger 合并函数
     * @param collectors 收集器数组
     * @return 组合收集器
     */
    @SafeVarargs
    public static <T, R> Collector<T, Object[], R> multiSummarizing(
            Function<Object[], R> merger,
            Collector<? super T, ?, ?>... collectors) {

        return new Collector<T, Object[], R>() {
            @Override
            public Supplier<Object[]> supplier() {
                return () -> Arrays.stream(collectors)
                        .map(c -> c.supplier().get())
                        .toArray();
            }

            @Override
            public BiConsumer<Object[], T> accumulator() {
                return (acc, t) -> {
                    for (int i = 0; i < collectors.length; i++) {
                        ((BiConsumer)collectors[i].accumulator()).accept(acc[i], t);
                    }
                };
            }

            @Override
            public BinaryOperator<Object[]> combiner() {
                return (acc1, acc2) -> {
                    Object[] result = new Object[collectors.length];
                    for (int i = 0; i < collectors.length; i++) {
                        result[i] = ((BinaryOperator)collectors[i].combiner()).apply(acc1[i], acc2[i]);
                    }
                    return result;
                };
            }

            @Override
            public Function<Object[], R> finisher() {
                return acc -> {
                    Object[] result = new Object[collectors.length];
                    for (int i = 0; i < collectors.length; i++) {
                        result[i] = ((Function)collectors[i].finisher()).apply(acc[i]);
                    }
                    return merger.apply(result);
                };
            }

            @Override
            public Set<Characteristics> characteristics() {
                return Collections.emptySet();
            }
        };
    }

}
