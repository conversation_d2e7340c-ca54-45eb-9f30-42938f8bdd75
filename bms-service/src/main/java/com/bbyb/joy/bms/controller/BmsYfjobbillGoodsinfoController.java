package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.BmsYfjobbillGoodsinfo;
import com.bbyb.joy.bms.domain.dto.FileBaseDto;
import com.bbyb.joy.bms.domain.enums.BusinessType;
import com.bbyb.joy.bms.service.IBmsYfjobbillGoodsinfoService;
import com.bbyb.joy.bms.support.annotation.Log;
import com.bbyb.joy.bms.support.utils.ExcelUtil;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 应付作业单商品明细Controller
 *
 * <AUTHOR>
 */
@Api(tags = "应付作业单商品明细接口")
@RestController
@RequestMapping("/system/goodsinfo")
public class BmsYfjobbillGoodsinfoController {

    @Resource
    private IBmsYfjobbillGoodsinfoService bmsYfjobbillGoodsinfoService;

    /**
     * 查询应付作业单商品明细列表
     */
    @PostMapping("/list")
    public ResponseResult<PagerDataBean<BmsYfjobbillGoodsinfo>> list(@RequestBody(required = false) BmsYfjobbillGoodsinfo bmsYfjobbillGoodsinfo) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfjobbillGoodsinfoService.selectBmsYfjobbillGoodsinfoList(token, bmsYfjobbillGoodsinfo));
    }


    /**
     * 导出应付作业单商品明细列表
     */
    @Log(title = "应付作业单商品明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public ResponseResult<String> export(@RequestBody(required = false) BmsYfjobbillGoodsinfo bmsYfjobbillGoodsinfo) {
        String token = RequestContext.getToken();
        List<BmsYfjobbillGoodsinfo> list = bmsYfjobbillGoodsinfoService.selectBmsYfjobbillGoodsinfoList(token, bmsYfjobbillGoodsinfo).getRows();
        ExcelUtil<BmsYfjobbillGoodsinfo> util = new ExcelUtil<BmsYfjobbillGoodsinfo>(BmsYfjobbillGoodsinfo.class);
        FileBaseDto fileBaseDto = util.exportExcel(list, "应付作业单商品明细数据");
        return new ResponseResult<>(true, 0, fileBaseDto.getFileName(), fileBaseDto.getFileName());
    }

    /**
     * 获取应付作业单商品明细详细信息
     */
    @PostMapping(value = "/{id}")
    public ResponseResult<BmsYfjobbillGoodsinfo> getInfo(@PathVariable("id") String id) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfjobbillGoodsinfoService.selectBmsYfjobbillGoodsinfoById(token, id));
    }

    /**
     * 新增应付作业单商品明细
     */
    @Log(title = "应付作业单商品明细", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public ResponseResult<Integer> add(@RequestBody(required = false) BmsYfjobbillGoodsinfo bmsYfjobbillGoodsinfo) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfjobbillGoodsinfoService.insertBmsYfjobbillGoodsinfo(token, bmsYfjobbillGoodsinfo));
    }

    /**
     * 修改应付作业单商品明细
     */
    @Log(title = "应付作业单商品明细", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public ResponseResult<Integer> edit(@RequestBody(required = false) BmsYfjobbillGoodsinfo bmsYfjobbillGoodsinfo) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfjobbillGoodsinfoService.updateBmsYfjobbillGoodsinfo(token, bmsYfjobbillGoodsinfo));
    }

    /**
     * 物理作废应付作业单商品明细
     */
    @Log(title = "应付作业单商品明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public ResponseResult<Integer> remove(@PathVariable String[] ids) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfjobbillGoodsinfoService.deleteBmsYfjobbillGoodsinfoByIds(token, ids));
    }

    /**
     * 逻辑作废/启用应付作业单商品明细
     */
    @Log(title = "应付作业单商品明细", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateStatus")
    public ResponseResult<Integer> updateStatus(@RequestBody(required = false) Map<String, Object> idsAndStatus) {
        String token = RequestContext.getToken();
        List<String> idsList = (ArrayList<String>) idsAndStatus.get("ids");
        String[] ids = idsList.toArray(new String[0]);
        String status = String.valueOf(idsAndStatus.get("status"));
        return new ResponseResult<>(bmsYfjobbillGoodsinfoService.updateBmsYfjobbillGoodsinfoStatusByIds(token, ids, status));
    }

    /**
     * 根据作业单id查询作业单商品信息
     */
    @PostMapping("/selectYfjobbillGoodsinfoByYfCodes")
    public ResponseResult<List<BmsYfjobbillGoodsinfo>> selectYfjobbillGoodsinfoByYfCodes(@RequestBody(required = false) List<String> codes) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYfjobbillGoodsinfoService.selectYfjobbillGoodsinfoByYfCodes(token, codes));
    }
}
