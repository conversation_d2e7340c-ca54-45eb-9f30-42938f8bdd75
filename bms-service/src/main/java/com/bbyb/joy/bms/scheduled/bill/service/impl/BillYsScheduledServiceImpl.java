package com.bbyb.joy.bms.scheduled.bill.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.biz.BmsYsbillmainBiz;
import com.bbyb.joy.bms.biz.BmsYscostInfoBiz;
import com.bbyb.joy.bms.biz.MdmClientinfoBiz;
import com.bbyb.joy.bms.domain.dto.MdmClientinfo;
import com.bbyb.joy.bms.domain.dto.SysDept;
import com.bbyb.joy.bms.domain.dto.bill.BmsYsbillmain;
import com.bbyb.joy.bms.domain.dto.dto.BmsYscostExtendDto;
import com.bbyb.joy.bms.scheduled.bill.service.BillYsScheduledService;
import com.bbyb.joy.bms.support.utils.DateUtils;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.getCode.CodeCategory;
import com.bbyb.joy.bms.support.utils.getCode.CodeUtils;
import com.bbyb.joy.core.context.RequestContext;
import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 账单相关定时任务
 */
@Service
@Slf4j
public class BillYsScheduledServiceImpl implements BillYsScheduledService {


    // 客户mapper
    @Resource
    private MdmClientinfoBiz mdmClientinfoMapper;
    @Resource
    private BmsYsbillmainBiz bmsYsbillmainMapper;
    @Resource
    private BmsYscostInfoBiz bmsYscostInfoMapper;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    CodeUtils codeUtils;




    @Override
    public void autoGenerateBill() {

        log.info("应收账单开始自动生成");
        UserBean userVO = RequestContext.getUserInfo();

        System.out.println("应收账单开始自动生成-----");
        try {
            // 查询出所有登记账期的客户
            List<MdmClientinfo> clientList = mdmClientinfoMapper.selectBillDateAndRuleList(userVO.getTenantid().toString());
            if(CollUtil.isEmpty(clientList)){
                return;
            }
            for (MdmClientinfo mdmClientinfo : clientList) {
                // 客户的账单日
                Integer clientDay =  Integer.valueOf(mdmClientinfo.getPaymentDays());
                String accountDay = getAcctDay(clientDay);
                System.out.println("客户名称:"+mdmClientinfo.getClientName()+",账单日:"+mdmClientinfo.getPaymentDays()+",当前账期:"+accountDay);
                // 先更新冲销单
                FeeFlagBill(mdmClientinfo,accountDay);
                //1）仓+运+集团+网点
                if("1".equals(mdmClientinfo.getBillLogic().toString())){
                    GenerateBill1(mdmClientinfo,accountDay);
                }
                if("2".equals(mdmClientinfo.getBillLogic().toString())){
                    GenerateBill2(mdmClientinfo,accountDay);
                }
                if("3".equals(mdmClientinfo.getBillLogic().toString())){
                    GenerateBill3(mdmClientinfo,accountDay);
                }
                if("4".equals(mdmClientinfo.getBillLogic().toString())){
                    GenerateBill4(mdmClientinfo,accountDay);
                }
                if("5".equals(mdmClientinfo.getBillLogic().toString())){
                    GenerateBill5(mdmClientinfo,accountDay);
                }
                if("6".equals(mdmClientinfo.getBillLogic().toString())){
                    GenerateBill6(mdmClientinfo,accountDay);
                }
                if("7".equals(mdmClientinfo.getBillLogic().toString())){
                    GenerateBill7(mdmClientinfo,accountDay);
                }
                if("8".equals(mdmClientinfo.getBillLogic().toString())){
                    GenerateBill8(mdmClientinfo,accountDay);
                }
                if("9".equals(mdmClientinfo.getBillLogic().toString())){
                    GenerateBill9(mdmClientinfo,accountDay);
                }
                if("10".equals(mdmClientinfo.getBillLogic().toString())){
                    GenerateBill10(mdmClientinfo,accountDay);
                }
                if("11".equals(mdmClientinfo.getBillLogic().toString())){
                    GenerateBill11(mdmClientinfo,accountDay);
                }
                if("12".equals(mdmClientinfo.getBillLogic().toString())){
                    GenerateBill12(mdmClientinfo,accountDay);
                }
            }
        }catch (Exception e){
            log.info(e.getMessage());
        }
    }



    public static String getAcctDay(Integer acctDay) {
        if (acctDay == null || acctDay < 1 || acctDay > 31) {
            throw new IllegalArgumentException("账单日必须在1-31之间");
        }

        LocalDate now = LocalDate.now();
        // 固定获取上个月（M-1）
        LocalDate acctDate = now.minusMonths(1);

        // 处理月末日期（如31号在2月不存在的情况）
        try {
            acctDate = acctDate.withDayOfMonth(acctDay);
        } catch (DateTimeException e) {
            // 如果指定日期不存在，使用该月最后一天
            acctDate = acctDate.withDayOfMonth(acctDate.lengthOfMonth());
        }

        return acctDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }


    /**
     * 1）仓+运+集团+网点
     * @param mdmClientinfo 符合条件的客户
     * @param billDate 账单日
     */
    private void GenerateBill1(MdmClientinfo mdmClientinfo, String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 获取这个集团下所有是这个生成规则的客户id （集团下所有的子公司且规则一致）
        List<MdmClientinfo> list = mdmClientinfoMapper.selectSameUnderTheGroup(userVO.getTenantid().toString(),mdmClientinfo);
        // 查询集团名称
        MdmClientinfo clientinfo = mdmClientinfoMapper.selectSameUnderThe(userVO.getTenantid().toString(),mdmClientinfo);
        // 组装客户ids
        List<Long> clientIds = list.stream().map(MdmClientinfo::getId).collect(Collectors.toList());
        String clientName = clientinfo.getClientName();
        String companName="";
        // 查询 按网点(机构)分组
        List<BmsYscostExtendDto> costGroupByclient=bmsYscostInfoMapper.getcodeListGroupcompanyByClientIds(userVO.getTenantid().toString(),clientIds,billDate);
        Map<String, SysDept> dept = textConversionUtil.getCompanyMap("1",costGroupByclient.stream().filter(e->e.getCompanyId()!=null).map(e->e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            if(bmsYscostExtendDto.getCompanyId()!=null && dept.containsKey(bmsYscostExtendDto.getCompanyId().toString())){
                companName = dept.get(bmsYscostExtendDto.getCompanyId().toString()).getDeptName();
            }
            String billName = "【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                BmsYsbillmain bmsYsbillmain = assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                // 更新这个账单对应的费用关联
                bmsYscostInfoMapper.updateBillIdByIds(userVO.getTenantid().toString(),clientIds,billDate,bmsYscostExtendDto.getCompanyId(),bmsYsbillmain.getId().intValue(),bmsYsbillmain.getBillCode());

            }
        }
    }


    /**
     * 2）仓+运+子公司+网点
     * @param mdmClientinfo 符合条件的客户信息
     * @param billDate 账单日
     */
    private void GenerateBill2(MdmClientinfo mdmClientinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();
        // 查询 按网点(机构)分组
        List<BmsYscostExtendDto> costGroupByclient = bmsYscostInfoMapper.getcodeListGroupcompanyByClientAndCompany(userVO.getTenantid().toString(),mdmClientinfo.getId(),billDate);
        Map<String, SysDept> dept = textConversionUtil.getCompanyMap("1",costGroupByclient.stream().filter(e->e.getCompanyId()!=null).map(e->e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        String clientName = mdmClientinfo.getClientName();
        String companName="";
        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            if(bmsYscostExtendDto.getCompanyId()!=null && dept.containsKey(bmsYscostExtendDto.getCompanyId().toString())){
                companName = dept.get(bmsYscostExtendDto.getCompanyId().toString()).getDeptName();
            }
            String billName = "【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                BmsYsbillmain bmsYsbillmain =assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                Integer billId= bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                // 更新这个账单对应的费用关联
                bmsYscostInfoMapper.updateBillIdById(userVO.getTenantid().toString(),mdmClientinfo.getId(),billDate,bmsYscostExtendDto.getCompanyId(),bmsYsbillmain.getId().intValue());

            }
        }
    }

    // 3) 仓+运+集团+全公司
    private void GenerateBill3(MdmClientinfo mdmClientinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 获取这个集团下所有是这个生成规则的客户id （集团下所有的子公司且规则一致）
        List<MdmClientinfo> list = mdmClientinfoMapper.selectSameUnderTheGroup(userVO.getTenantid().toString(),mdmClientinfo);
        // 组装客户ids
        List<Long> clientIds = list.stream().map(MdmClientinfo::getId).collect(Collectors.toList());
        // 查询集团名称
        MdmClientinfo clientinfo = mdmClientinfoMapper.selectSameUnderThe(userVO.getTenantid().toString(),mdmClientinfo);
        // 查询 按网点(机构)分组
        List<BmsYscostExtendDto> costGroupByclient=bmsYscostInfoMapper.getcodeListGroupByIds(userVO.getTenantid().toString(),clientIds,billDate);
        String clientName = clientinfo.getClientName();
        String companName="绝配总公司";
        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            String billName = "【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                BmsYsbillmain bmsYsbillmain =assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                // 更新这个账单对应的费用关联
                bmsYscostInfoMapper.updateBillIdByIds(userVO.getTenantid().toString(),clientIds,billDate,null,bmsYsbillmain.getId().intValue(),bmsYsbillmain.getBillCode());

            }
        }
    }

    // 4) 仓+运+子公司+全公司
    private void GenerateBill4(MdmClientinfo mdmClientinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按全公司(所有的机构)分组
        List<BmsYscostExtendDto> costGroupByclient=bmsYscostInfoMapper.getcodeListGroupcompanyByClient(userVO.getTenantid().toString(),mdmClientinfo.getId(),billDate);
        String clientName = mdmClientinfo.getClientName();
        String companName="绝配总公司";
        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            String billName = "【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                BmsYsbillmain bmsYsbillmain =assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                Integer billId= bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                // 更新这个账单对应的费用关联
                bmsYscostInfoMapper.updateBillIdById(userVO.getTenantid().toString(),mdmClientinfo.getId(),billDate,null,bmsYsbillmain.getId().intValue());
            }
        }
    }

    // 5) 仓/运+集团+网点
    private void GenerateBill5(MdmClientinfo mdmClientinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 获取这个集团下所有是这个生成规则的客户id （集团下所有的子公司且规则一致）
        List<MdmClientinfo> list = mdmClientinfoMapper.selectSameUnderTheGroup(userVO.getTenantid().toString(),mdmClientinfo);
        // 组装客户ids
        List<Long> clientIds = list.stream().map(MdmClientinfo::getId).collect(Collectors.toList());
        // 查询 按网点(机构)分组
        List<BmsYscostExtendDto> costGroupByclient=bmsYscostInfoMapper.getcodeListGroupcompanyByClientAndStockIds(userVO.getTenantid().toString(),clientIds,billDate);
        // 查询集团名称
        MdmClientinfo clientinfo = mdmClientinfoMapper.selectSameUnderThe(userVO.getTenantid().toString(),mdmClientinfo);
        Map<String, SysDept> dept = textConversionUtil.getCompanyMap("1",costGroupByclient.stream().filter(e->e.getCompanyId()!=null).map(e->e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        String clientName = clientinfo.getClientName();
        String companName="绝配总公司";
        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            if(bmsYscostExtendDto.getCompanyId()!=null && dept.containsKey(bmsYscostExtendDto.getCompanyId().toString())){
                companName = dept.get(bmsYscostExtendDto.getCompanyId().toString()).getDeptName();
            }
            String billName = "【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                BmsYsbillmain bmsYsbillmain =assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                Integer billId= bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                // 更新这个账单对应的费用关联
                if("1".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdByIds(userVO.getTenantid().toString(),clientIds,billDate,bmsYscostExtendDto.getCompanyId(),bmsYsbillmain.getId().intValue(),bmsYsbillmain.getBillCode());
                }
                if("2".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdCcByIds(userVO.getTenantid().toString(),clientIds,billDate,bmsYscostExtendDto.getCompanyId(),bmsYsbillmain.getId().intValue(),null);
                }
            }

        }
    }

    // 6) 仓/运+集团+全公司
    private void GenerateBill6(MdmClientinfo mdmClientinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 获取这个集团下所有是这个生成规则的客户id （集团下所有的子公司且规则一致）
        List<MdmClientinfo> list = mdmClientinfoMapper.selectSameUnderTheGroup(userVO.getTenantid().toString(),mdmClientinfo);
        // 组装客户ids
        List<Long> clientIds = list.stream().map(MdmClientinfo::getId).collect(Collectors.toList());
        // 查询 按网点(机构)分组
        List<BmsYscostExtendDto> costGroupByclient=bmsYscostInfoMapper.getcodeListGroupAndSotckByIds(userVO.getTenantid().toString(),clientIds,billDate);
        // 查询集团名称
        MdmClientinfo clientinfo = mdmClientinfoMapper.selectSameUnderThe(userVO.getTenantid().toString(),mdmClientinfo);
        String clientName = clientinfo.getClientName();
        String companName="绝配总公司";

        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            String billName = "【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                BmsYsbillmain bmsYsbillmain =assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                Integer billId= bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                // 更新这个账单对应的费用关联
                if("1".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdYsByIds(userVO.getTenantid().toString(),clientIds,billDate,null,bmsYsbillmain.getId().intValue());
                }
                if("2".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdCcByIds(userVO.getTenantid().toString(),clientIds,billDate,null,bmsYsbillmain.getId().intValue(),null);
                }
            }
        }
    }

    // 7) 仓/运+子公司+网点
    private void GenerateBill7(MdmClientinfo mdmClientinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按网点(机构)分组
        List<BmsYscostExtendDto> costGroupByclient=bmsYscostInfoMapper.getcodeListGroupcompanyByClientAndCompanyAndStock(userVO.getTenantid().toString(),mdmClientinfo.getId(),billDate);
        String clientName = mdmClientinfo.getClientName();
        String companName="";
        Map<String, SysDept> dept = textConversionUtil.getCompanyMap("1",costGroupByclient.stream().filter(e->e.getCompanyId()!=null).map(e->e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            if(bmsYscostExtendDto.getCompanyId()!=null && dept.containsKey(bmsYscostExtendDto.getCompanyId().toString())){
                companName = dept.get(bmsYscostExtendDto.getCompanyId().toString()).getDeptName();
            }
            String billName = "【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                List<Long> clientIds = new ArrayList<>();
                BmsYsbillmain bmsYsbillmain =assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                Integer billId= bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                clientIds.add(mdmClientinfo.getId());
                // 更新这个账单对应的费用关联
                if("1".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdYsByIds(userVO.getTenantid().toString(),clientIds,billDate,bmsYscostExtendDto.getCompanyId(),bmsYsbillmain.getId().intValue());
                }
                if("2".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdCcByIds(userVO.getTenantid().toString(),clientIds,billDate,bmsYscostExtendDto.getCompanyId(),bmsYsbillmain.getId().intValue(),null);
                }
            }

        }
    }

    // 8) 仓/运+子公司+全公司
    private void GenerateBill8(MdmClientinfo mdmClientinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按全公司(所有的机构)分组
        List<BmsYscostExtendDto> costGroupByclient=bmsYscostInfoMapper.getcodeListGroupcompanyAndStockByClient(userVO.getTenantid().toString(),mdmClientinfo.getId(),billDate);
        String clientName = mdmClientinfo.getClientName();
        String companName="绝配总公司";
        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            String billName = "【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                BmsYsbillmain bmsYsbillmain =assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                Integer billId= bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                List<Long> clientIds = new ArrayList<>();
                clientIds.add(mdmClientinfo.getId());
                // 更新这个账单对应的费用关联
                if("1".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdYsByIds(userVO.getTenantid().toString(),clientIds,billDate,null,bmsYsbillmain.getId().intValue());
                }
                if("2".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdCcByIds(userVO.getTenantid().toString(),clientIds,billDate,null,bmsYsbillmain.getId().intValue(),null);
                }
            }
        }

    }

    // 9) 仓/运+集团+网点+仓库
    private void GenerateBill9(MdmClientinfo mdmClientinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 获取这个集团下所有是这个生成规则的客户id （集团下所有的子公司且规则一致）
        List<MdmClientinfo> list = mdmClientinfoMapper.selectSameUnderTheGroup(userVO.getTenantid().toString(),mdmClientinfo);
        // 组装客户ids
        List<Long> clientIds = list.stream().map(MdmClientinfo::getId).collect(Collectors.toList());
        // 查询 按网点(机构)分组
        List<BmsYscostExtendDto> costGroupByclient=bmsYscostInfoMapper.getcodeListGroupWarehousByClientAndStockIds(userVO.getTenantid().toString(),clientIds,billDate);
        // 查询集团名称
        MdmClientinfo clientinfo = mdmClientinfoMapper.selectSameUnderThe(userVO.getTenantid().toString(),mdmClientinfo);
        Map<String, SysDept> dept = textConversionUtil.getCompanyMap("1",costGroupByclient.stream().filter(e->e.getCompanyId()!=null).map(e->e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        String clientName = clientinfo.getClientName();
        String companName="绝配总公司";
        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            if(bmsYscostExtendDto.getCompanyId()!=null && dept.containsKey(bmsYscostExtendDto.getCompanyId().toString())){
                companName = dept.get(bmsYscostExtendDto.getCompanyId().toString()).getDeptName();
            }
            String ware = "";
            if("2".equals(bmsYscostExtendDto.getBillType())){
                ware=bmsYscostExtendDto.getWarehouseName()+"-";
            }
            String billName = ware+"【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                BmsYsbillmain bmsYsbillmain =assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                Integer billId= bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                // 更新这个账单对应的费用关联
                if("1".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdYsByIds(userVO.getTenantid().toString(),clientIds,billDate,bmsYscostExtendDto.getCompanyId(),bmsYsbillmain.getId().intValue());
                }
                if("2".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdCcByIds(userVO.getTenantid().toString(),clientIds,billDate,bmsYscostExtendDto.getCompanyId(),bmsYsbillmain.getId().intValue(),bmsYscostExtendDto.getWarehouseCode());
                }
            }

        }
    }

    // 10) 仓/运+集团+全公司+仓库
    private void GenerateBill10(MdmClientinfo mdmClientinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 获取这个集团下所有是这个生成规则的客户id （集团下所有的子公司且规则一致）
        List<MdmClientinfo> list = mdmClientinfoMapper.selectSameUnderTheGroup(userVO.getTenantid().toString(),mdmClientinfo);
        // 组装客户ids
        List<Long> clientIds = list.stream().map(MdmClientinfo::getId).collect(Collectors.toList());
        // 查询 按仓库分组、
        List<BmsYscostExtendDto> costGroupByclient=bmsYscostInfoMapper.getcodeListGroupAndWarehouseByIds(userVO.getTenantid().toString(),clientIds,billDate);
        // 查询集团名称
        MdmClientinfo clientinfo = mdmClientinfoMapper.selectSameUnderThe(userVO.getTenantid().toString(),mdmClientinfo);
        String clientName = clientinfo.getClientName();
        String companName="绝配总公司";
        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            String ware = "";
            if("1".equals(bmsYscostExtendDto.getBillType())){
                ware = bmsYscostExtendDto.getWarehouseName()+"-";
            }
            String billName = ware+"【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                BmsYsbillmain bmsYsbillmain =assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                Integer billId= bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                // 更新这个账单对应的费用关联
                if("1".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdYsByIds(userVO.getTenantid().toString(),clientIds,billDate,null,bmsYsbillmain.getId().intValue());
                }
                if("2".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdCcByIds(userVO.getTenantid().toString(),clientIds,billDate,null,bmsYsbillmain.getId().intValue(),bmsYscostExtendDto.getWarehouseCode());
                }
            }

        }
    }

    // 11) 仓/运+子公司+网点+仓库
    private void GenerateBill11(MdmClientinfo mdmClientinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按网点(机构)分组
        List<BmsYscostExtendDto> costGroupByclient=bmsYscostInfoMapper.getcodeListGroupWareByClientAndCompanyAndStock(userVO.getTenantid().toString(),mdmClientinfo.getId(),billDate);
        String clientName = mdmClientinfo.getClientName();
        String companName="";
        Map<String, SysDept> dept = textConversionUtil.getCompanyMap("1",costGroupByclient.stream().filter(e->e.getCompanyId()!=null).map(e->e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            if(bmsYscostExtendDto.getCompanyId()!=null && dept.containsKey(bmsYscostExtendDto.getCompanyId().toString())){
                companName = dept.get(bmsYscostExtendDto.getCompanyId().toString()).getDeptName();
            }
            String ware= "";
            if("2".equals(bmsYscostExtendDto.getBillType())){
                ware=bmsYscostExtendDto.getWarehouseName()+"-";
            }
            String billName = ware+"【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                List<Long> clientIds = new ArrayList<>();
                BmsYsbillmain bmsYsbillmain =assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                Integer billId= bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                clientIds.add(mdmClientinfo.getId());
                // 更新这个账单对应的费用关联
                if("1".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdYsByIds(userVO.getTenantid().toString(),clientIds,billDate,bmsYscostExtendDto.getCompanyId(),bmsYsbillmain.getId().intValue());
                }
                if("2".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdCcByIds(userVO.getTenantid().toString(),clientIds,billDate,bmsYscostExtendDto.getCompanyId(),bmsYsbillmain.getId().intValue(),bmsYscostExtendDto.getWarehouseCode());
                }
            }
        }
    }

    // 12) 仓/运+子公司+全公司
    private void GenerateBill12(MdmClientinfo mdmClientinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按全公司(所有的机构)分组
        List<BmsYscostExtendDto> costGroupByclient=bmsYscostInfoMapper.getcodeListGroupcompanyAndWareByClient(userVO.getTenantid().toString(),mdmClientinfo.getId(),billDate);
        String clientName = mdmClientinfo.getClientName();
        String companName="绝配总公司";
        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            String ware = "";
            if("2".equals(bmsYscostExtendDto.getBillType())){
                ware =bmsYscostExtendDto.getWarehouseName()+"-";
            }
            String billName = ware+"【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                BmsYsbillmain bmsYsbillmain =assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                Integer billId= bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                List<Long> clientIds = new ArrayList<>();
                clientIds.add(mdmClientinfo.getId());
                // 更新这个账单对应的费用关联
                if("1".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdYsByIds(userVO.getTenantid().toString(),clientIds,billDate,null,bmsYsbillmain.getId().intValue());
                }
                if("2".equals(bmsYscostExtendDto.getBillType())){
                    bmsYscostInfoMapper.updateBillIdCcByIds(userVO.getTenantid().toString(),clientIds,billDate,null,bmsYsbillmain.getId().intValue(),bmsYscostExtendDto.getWarehouseCode());
                }
            }

        }
    }

    /**
     * 冲消单 单独生成账单
     */
    private void FeeFlagBill(MdmClientinfo mdmClientinfo,String billDate){

        UserBean userVO = RequestContext.getUserInfo();

        // 查询 按全公司(所有的机构)分组
        List<BmsYscostExtendDto> costGroupByclient=bmsYscostInfoMapper.getcodeFeeFlagList(userVO.getTenantid().toString(),mdmClientinfo.getId(),billDate);
        String clientName = mdmClientinfo.getClientName();
        String companName="";
        String ware = "";
        Map<String, SysDept> dept = textConversionUtil.getCompanyMap("1",costGroupByclient.stream().filter(e->e.getCompanyId()!=null).map(e->e.getCompanyId().toString()).distinct().collect(Collectors.joining(",")));
        for (BmsYscostExtendDto bmsYscostExtendDto : costGroupByclient) {
            if(bmsYscostExtendDto.getVotes()==0){
                continue;
            }
            if(bmsYscostExtendDto.getCompanyId()!=null && dept.containsKey(bmsYscostExtendDto.getCompanyId().toString())){
                companName = dept.get(bmsYscostExtendDto.getCompanyId().toString()).getDeptName();
            }
            if("2".equals(bmsYscostExtendDto.getBillType())){
                ware=bmsYscostExtendDto.getWarehouseName()+"-";
            }
            String billName = ware+"【"+clientName+"】"+companName;
            if(StringUtil.isNotEmpty(clientName)){
                BmsYsbillmain bmsYsbillmain =assemblyBillInformation(bmsYscostExtendDto,billDate,mdmClientinfo.getId(),billName);
                // 新增一个账单
                bmsYsbillmain.setBillMarking(2);
                bmsYsbillmain.setBillName(bmsYsbillmain.getBillName()+"-冲销单");
                Integer billId= bmsYsbillmainMapper.insertBmsYsbillmain(userVO.getTenantid().toString(),bmsYsbillmain);
                // 更新这个账单对应的费用关联
                bmsYscostInfoMapper.updateBillIdFeeFlagById(userVO.getTenantid().toString(),mdmClientinfo.getId(),billDate,bmsYscostExtendDto.getCompanyId(),bmsYsbillmain.getId().intValue());
            }
        }
    }

    /**
     * 组装应收账单信息
     * @param cost 费用信息
     * @param accountDay 账单日期
     * @param clientId 客户id
     */
    public BmsYsbillmain assemblyBillInformation(BmsYscostExtendDto cost,String accountDay,Long clientId,String billName){

        UserBean userVO = RequestContext.getUserInfo();

        String billDate = accountDay;
        if(accountDay!=null){
            SimpleDateFormat simp = new SimpleDateFormat("yyyy-MM");
            SimpleDateFormat simp2 = new SimpleDateFormat("yyyy年MM月");
            try {
                // 日期格式（用于账单名称）
                billDate = simp2.format(simp.parse(accountDay));
            } catch (ParseException e) {
                log.info(e.getMessage());
            }
        }
        String name = billName+billDate;
        if(cost.getBillType()!=null && "1".equals(cost.getBillType())){
            name=name+"运输账单";
        }
        if(cost.getBillType()!=null && "2".equals(cost.getBillType())){
            name=name+"仓储账单";
        }
        if(cost.getBillType()==null){
            name=name+"仓配账单";
        }
        BmsYsbillmain bmsYsbillmain = new BmsYsbillmain();
        bmsYsbillmain.setBillName(name);
        bmsYsbillmain.setBillType(0);
        bmsYsbillmain.setBillCode(codeUtils.getCode(CodeCategory.YSCode,userVO.getTenantid().toString()));
        bmsYsbillmain.setBillDate(accountDay);
        bmsYsbillmain.setCompanyId(cost.getCompanyId());
        bmsYsbillmain.setClientId(clientId.intValue());
        bmsYsbillmain.setBillMarking(1);
        bmsYsbillmain.setVotes(cost.getVotes());
        bmsYsbillmain.setBillAmount(cost.getSumAmt());
        bmsYsbillmain.setAdjustedAmount(cost.getSumAmt());
        bmsYsbillmain.setYsAmount(cost.getSumAmt());
        bmsYsbillmain.setBillState(1);
        bmsYsbillmain.setAuditState(1);
        bmsYsbillmain.setTicketState(1);
        bmsYsbillmain.setHxState(1);
        bmsYsbillmain.setTicketApplyState(1);
        bmsYsbillmain.setSubmitStatus("1");
        bmsYsbillmain.setRemark(new SimpleDateFormat("yyyy-MM-dd HH:ss:mm").format(new Date())+"自动生成");
        bmsYsbillmain.setCreateTime(DateUtils.getNowDate());
        bmsYsbillmain.setOperTime(DateUtils.getNowDate());
        return bmsYsbillmain;
    }

}
