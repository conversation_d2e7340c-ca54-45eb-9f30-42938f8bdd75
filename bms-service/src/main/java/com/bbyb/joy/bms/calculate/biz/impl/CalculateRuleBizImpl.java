package com.bbyb.joy.bms.calculate.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import com.bbyb.joy.bms.calculate.biz.CalculateRuleBiz;
import com.bbyb.joy.bms.calculate.domain.dto.rule.CalculateFactorInfoDto;
import com.bbyb.joy.bms.calculate.domain.dto.rule.CalculateRuleInfoDto;
import com.bbyb.joy.bms.calculate.domain.param.CalculateRuleQueryParam;
import com.bbyb.joy.bms.support.datesource.BmsDs;
import com.bbyb.joy.constants.Type;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class CalculateRuleBizImpl implements CalculateRuleBiz {

    private static final String MAPPER = "BmsCalculateRuleMapper";

    @Override
    public List<CalculateRuleInfoDto> queryValidList(CalculateRuleQueryParam param) {
        Map<String, Object> cond = BeanUtil.beanToMap(param);
        return BmsDs.instance().WMSMybatis().xmlMapper(MAPPER, Type.SYSTEM_TYPE_BMS_CODE).selectList("queryValidList", cond);
    }


    @Override
    public List<CalculateFactorInfoDto> queryFactorsList(CalculateRuleQueryParam param) {
        Map<String, Object> cond = BeanUtil.beanToMap(param);
        return BmsDs.instance().WMSMybatis().xmlMapper(MAPPER, Type.SYSTEM_TYPE_BMS_CODE).selectList("queryFactorsList", cond);
    }
}
