package com.bbyb.joy.bms.support.responseApi;

import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bms.domain.ResponseApiDto;
import com.bbyb.joy.bms.support.utils.StringUtils;

/**
 * 接口对接返回
 */
public class responseApiUtil {

    public responseApiUtil() {
    }


    /**
     * 接口对接返回
     *
     * @param success
     * @param code
     * @param msg
     * @param data
     */
    public static String responseMessage(Boolean success, String code, String msg, Object data) {
        ResponseApiDto responseApiDto = new ResponseApiDto();
        responseApiDto.setSuccess(success);
        responseApiDto.setCode(code);
        responseApiDto.setMessage(msg);
        if (StringUtils.isNotNull(data)) {
            String dataJsons = JSONObject.toJSONString(data);
            responseApiDto.setData(dataJsons);
        }
        String responseMsg = JSONObject.toJSONString(responseApiDto);
        return responseMsg;
    }

}
