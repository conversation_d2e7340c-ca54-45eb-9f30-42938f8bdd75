package com.bbyb.joy.bms.support.exception.user;

/**
 * 用户密码不正确或不符合规范异常类
 *
 * <AUTHOR>
 */
public class UserPasswordNotMatchException extends UserException {
    private static final long serialVersionUID = 1L;

    public UserPasswordNotMatchException() {
        super("user.password.not.match", null);
    }

    public UserPasswordNotMatchException(int count) {
        super("user.password.retry.limit.count", new Object[]{count});
    }

    public UserPasswordNotMatchException(int count, String time) {
        super("user.password.retry.limit.exceed.regexp", new Object[]{count, time});
    }

    public UserPasswordNotMatchException(String time) {
        super("user.blocked.time", new Object[]{time});
    }
}
