package com.bbyb.joy.bms.support;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.domain.PubSpeedometerDTO;
import com.bbyb.joy.constants.Key;
import com.bbyb.joy.core.context.RedisContext;
import com.bbyb.joy.core.context.RequestContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Mr Li
 * @description : 高级权限校验工具
 * @since : 2023/5/26 09:03
 */
@Component
public class AdvancePermissionUtil {
    @Resource
    private RedisContext redisContext;

    private static List<PubSpeedometerDTO> treeToList(List<PubSpeedometerDTO> treeList) {
        List<PubSpeedometerDTO> list = new ArrayList<>();
        for (PubSpeedometerDTO tree : treeList) {
            List<PubSpeedometerDTO> child = tree.getBtnChildern();
            list.add(tree);
            if (CollUtil.isNotEmpty(child)) {
                list.addAll(treeToList(child));
                tree.setBtnChildern(null);
            }
        }
        return list;
    }

    /**
     * 检测当前用户是否有所属模块的高级权限
     *
     * @param buttonCode 模块的高级权限编码
     * @return true、false
     */
    public boolean checkAdvancePermission(UserBean userVO, String buttonCode) {
        String s = redisContext.getValueByString(Key.CACHE_USER_MENULIST + RequestContext.getTenantId() + ":" + userVO.getId());
        List<PubSpeedometerDTO> pubBuspopedomdtos = JSON.parseArray(s, PubSpeedometerDTO.class);
        pubBuspopedomdtos = treeToList(pubBuspopedomdtos);
        boolean advancedPermissions = false;
        if (CollUtil.isNotEmpty(pubBuspopedomdtos)) {
            advancedPermissions = pubBuspopedomdtos.stream().anyMatch(c -> buttonCode.equals(c.getMenucode()));
        }
        return advancedPermissions;
    }

}
