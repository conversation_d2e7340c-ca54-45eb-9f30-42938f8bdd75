package com.bbyb.joy.bms.calculate.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bms.calculate.domain.dto.code.day.CalculateDayDto;
import com.bbyb.joy.bms.calculate.domain.dto.code.month.CalculateMonthDto;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDetail2Dto;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDetailDto;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderExpandDto;
import com.bbyb.joy.bms.calculate.domain.dto.rule.CalculateRuleInfoDto;
import com.bbyb.joy.bms.calculate.domain.result.CalculateCodeResult;
import com.bbyb.joy.bms.calculate.domain.result.CalculateCostResult;
import com.bbyb.joy.bms.calculate.domain.result.CalculateRemarkResult;
import com.bbyb.joy.bms.calculate.domain.result.CalculateResult;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyCalculateExtraType;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyCalculateResult;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyMatchResult;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyProcessResult;
import com.bbyb.joy.bms.domain.dto.SettleCompanyInfo;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import com.bbyb.joy.bms.support.utils.TenantInfoUtil;
import com.bbyb.joy.bms.support.utils.cost.StatisticsFieldEnum;
import com.bbyb.joy.bms.support.utils.getCode.CodeCategory;
import com.bbyb.joy.bms.support.utils.getCode.CodeUtils;
import com.bbyb.joy.core.context.RequestContext;
import com.bbyb.joy.enums.PubNumEnum;
import com.google.common.collect.Sets;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 抽象工具类
 */
public abstract class CalculateDomensionAbstractService {


    @Resource
    TenantInfoUtil tenantInfoUtil;
    @Resource
    CodeUtils codeUtils;


    /**
     * 计费匹配失败(按单)
     * @param matchResult 分级匹配结果
     * @param code 单据信息
     * @param ruleInfo 报价规则信息
     * @return 失败结果
     */
    public CalculateResult matchFailFillOrderResult(GroovyMatchResult matchResult
            , CalculateOrderExpandDto code
            , CalculateRuleInfoDto ruleInfo){

        CalculateCodeResult codeResult = new CalculateCodeResult();
        codeResult.setCodeType(code.getCodeType());
        codeResult.setFailCodePkIds(Sets.newHashSet(code.getCode().getPkId()));
        codeResult.setCostResult(CalculateRemarkResult.builder()
                .codeType(code.getCode().getCodeType())
                .calculateFlag(BmsConstants.CALCULATE_FLAG_1)
                .calculateRemark(matchResult.getMsg())
                .expensesDimension(ruleInfo.getConsolidationRule())
                .expensesDimensionName(ruleInfo.getConsolidationRuleName())
                .ruleDetailPkId(ruleInfo.getPkId())
                .ruleDetailName(ruleInfo.getRuleName())
                .ruleMainPkId(ruleInfo.getMainPkId())
                .ruleMainName(ruleInfo.getMainRuleName())
                .build());

        return CalculateResult.builder()
                .codeResult(codeResult)
                .build();
    }


    /**
     * 计费失败(按单)
     * @param calculateResult 计费结果
     * @param code 单据信息
     * @param ruleInfo 报价规则信息
     * @return 失败结果
     */
    public CalculateResult calculateFailFillOrderResult(GroovyCalculateResult calculateResult
            , CalculateOrderExpandDto code
            , CalculateRuleInfoDto ruleInfo){

        CalculateCodeResult codeResult = new CalculateCodeResult();
        codeResult.setCodeType(code.getCodeType());
        codeResult.setFailCodePkIds(Sets.newHashSet(code.getCode().getPkId()));
        codeResult.setCostResult(CalculateRemarkResult.builder()
                .codeType(code.getCode().getCodeType())
                .calculateFlag(BmsConstants.CALCULATE_FLAG_1)
                .calculateRemark(calculateResult.getMsg())
                .expensesDimension(ruleInfo.getConsolidationRule())
                .expensesDimensionName(ruleInfo.getConsolidationRuleName())
                .ruleDetailPkId(ruleInfo.getPkId())
                .ruleDetailName(ruleInfo.getRuleName())
                .ruleMainPkId(ruleInfo.getMainPkId())
                .ruleMainName(ruleInfo.getMainRuleName())
                .build());

        return CalculateResult.builder()
                .codeResult(codeResult)
                .build();
    }


    /**
     * 计费成功(按单)
     * @param calculateResult 计费结果
     * @param processResult 计费过程结果
     * @param code 单据信息
     * @param ruleInfo 报价信息
     */
    public CalculateResult calculateSuccessFillOrderResult(GroovyCalculateResult calculateResult
            , GroovyProcessResult processResult
            , CalculateOrderExpandDto code
            , CalculateRuleInfoDto ruleInfo){

        CalculateCodeResult codeResult = new CalculateCodeResult();
        codeResult.setCodeType(code.getCodeType());
        codeResult.setSuccessCodePkIds(Sets.newHashSet(code.getCode().getPkId()));
        codeResult.setCostResult(CalculateRemarkResult.builder()
                .codeType(code.getCode().getCodeType())
                .calculateFlag(BmsConstants.CALCULATE_FLAG_0)
                .calculateRemark(calculateResult.getMsg())
                .calculateResult(calculateResult.getResult())
                .expensesDimension(ruleInfo.getConsolidationRule())
                .expensesDimensionName(ruleInfo.getConsolidationRuleName())
                .calculateProcessResult(processResult.getResult())
                .ruleDetailPkId(ruleInfo.getPkId())
                .ruleDetailName(ruleInfo.getRuleName())
                .ruleMainPkId(ruleInfo.getMainPkId())
                .ruleMainName(ruleInfo.getMainRuleName())
                .build());

        CalculateCostResult costResult = new CalculateCostResult();
        costResult.setExpenseCode(codeUtils.getCode(CodeCategory.EXPENSECODE, RequestContext.getTenantId()));
        costResult.setExpenseType(code.getCode().getExpenseType());
        costResult.setExpensesDimension(ruleInfo.getConsolidationRule());
        costResult.setExpensesDimensionName(ruleInfo.getConsolidationRuleName());
        costResult.setFeeType(ruleInfo.getFeeType());
        costResult.setShareType(ruleInfo.getShareType());
        costResult.setCalculateResult(calculateResult.getResult());
        // 额外计费场景处理
        if(!Objects.equals(calculateResult.getExtraType(), GroovyCalculateExtraType.DEFAULT.getCode())){
            costResult.setExtraType(calculateResult.getExtraType());
            costResult.setExtraInfo(calculateResult.getExtraInfo());
        }
        costResult.setCalculateRemark(calculateResult.getMsg());
        costResult.setCalculateProcessResult(processResult.getResult());
        costResult.setBusinessTime(code.getCode().getBusinessTime());
        costResult.setBillDate(code.getCode().getBusinessTimeMonth());
        costResult.setBeginTime(code.getCode().getBusinessBeginTime());
        costResult.setEndTime(code.getCode().getBusinessEndTime());
        costResult.setRuleDetailPkId(ruleInfo.getPkId());
        costResult.setRuleDetailName(ruleInfo.getRuleName());
        costResult.setRuleMainPkId(ruleInfo.getMainPkId());
        costResult.setRuleMainName(ruleInfo.getMainRuleName());
        costResult.setCodeType(code.getCodeType());
        costResult.setCodePkIds(Sets.newHashSet(code.getCode().getPkId()));
        Map<Integer, Dict> codeDataMap = new HashMap<>();
        codeDataMap.put(code.getCode().getPkId(),Dict.create()
                .set(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName(), code.getCode().getTotalWeight())
                .set(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName(), code.getCode().getTotalVolume())
                .set(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName(), code.getCode().getTotalNumber()));
        costResult.setCodeDataMap(codeDataMap);
        if(CollUtil.isNotEmpty(code.getDetail())){
            Set<Integer> codeDetailPkIdSet = code.getDetail().stream()
                    .map(CalculateOrderDetailDto::getPkId)
                    .collect(Collectors.toSet());
            costResult.setCodeDetailPkIds(codeDetailPkIdSet);

            Set<String> skuCodeSet = code.getDetail().stream()
                    .map(CalculateOrderDetailDto::getSkuCode)
                    .collect(Collectors.toSet());
            costResult.setTotalSkuNumber(skuCodeSet.size());

            Map<Integer, Dict> detailDataMap = new HashMap<>();
            for (CalculateOrderDetailDto detail : code.getDetail()) {
                detailDataMap.put(detail.getPkId(),Dict.create()
                        .set(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName(), detail.getTotalWeight())
                        .set(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName(), detail.getTotalVolume())
                        .set(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName(), detail.getTotalNumber()));
            }
            costResult.setCodeDetailDataMap(detailDataMap);
        }
        if(CollUtil.isNotEmpty(code.getDetail2())){
            Set<Integer> codeDetail2PkIdSet = code.getDetail2().stream()
                    .map(CalculateOrderDetail2Dto::getPkId)
                    .collect(Collectors.toSet());
            costResult.setCodeDetail2PkIds(codeDetail2PkIdSet);

            Map<Integer, Dict> detail2DataMap = new HashMap<>();
            for (CalculateOrderDetail2Dto detail2 : code.getDetail2()) {
                detail2DataMap.put(detail2.getPkId(),Dict.create()
                        .set(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName(), detail2.getTotalWeight())
                        .set(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName(), detail2.getTotalVolume())
                        .set(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName(), detail2.getTotalNumber()));
            }
            costResult.setCodeDetail2DataMap(detail2DataMap);
        }
        costResult.setCompanyId(code.getCode().getCompanyId());
        costResult.setWarehouseCode(code.getCode().getWarehouseCode());
        costResult.setWarehouseName(code.getCode().getWarehouseName());
        costResult.setTotalWeight(code.getCode().getTotalWeight());
        costResult.setTotalVolume(code.getCode().getTotalVolume());
        costResult.setTotalNumber(code.getCode().getTotalNumber());
        costResult.setTotalBoxes(code.getCode().getTotalBoxes());
        costResult.setTotalCargoValue(code.getCode().getTotalCargoValue());
        costResult.setTotalCodeNumber(PubNumEnum.one.getIntValue());
        costResult.setClientId(code.getCode().getClientId());
        costResult.setCarrierId(code.getCode().getCarrierId());
        costResult.setSettleType(ruleInfo.getSettleType());
        costResult.setSettleSetting(ruleInfo.getSettleSetting());
        costResult.setSettleMainId(ruleInfo.getRelationId());

        SettleCompanyInfo settleCompanyInfo = tenantInfoUtil.tenantSettleCompanyInfo(RequestContext.getTenantId());
        int settleEntityType = settleCompanyInfo == null || StrUtil.isEmpty(settleCompanyInfo.getLogicValue()) ? 1 : Integer.parseInt(settleCompanyInfo.getLogicValue());
        costResult.setSettleEntityType(settleEntityType);

        return CalculateResult.builder()
                .codeResult(codeResult)
                .costResult(costResult)
                .build();
    }




    /**
     * 计费分级匹配失败(按天)
     * @param matchResult 分级匹配结果
     * @param code 单据信息
     * @param ruleInfo 报价规则信息
     * @return 失败结果
     */
    public CalculateResult matchFailFillDayResult(GroovyMatchResult matchResult
            , CalculateDayDto code
            , CalculateRuleInfoDto ruleInfo){

        CalculateCodeResult codeResult = new CalculateCodeResult();
        codeResult.setCodeType(code.getCodeType());
        codeResult.setFailCodePkIds(code.getCodePkIds());
        codeResult.setCostResult(CalculateRemarkResult.builder()
                .codeType(code.getCodeType())
                .calculateFlag(BmsConstants.CALCULATE_FLAG_1)
                .calculateRemark(matchResult.getMsg())
                .expensesDimension(ruleInfo.getConsolidationRule())
                .expensesDimensionName(ruleInfo.getConsolidationRuleName())
                .ruleDetailPkId(ruleInfo.getPkId())
                .ruleDetailName(ruleInfo.getRuleName())
                .ruleMainPkId(ruleInfo.getMainPkId())
                .ruleMainName(ruleInfo.getMainRuleName())
                .build());

        return CalculateResult.builder()
                .codeResult(codeResult)
                .build();
    }


    /**
     * 计费失败(按天)
     * @param calculateResult 计费结果
     * @param code 单据信息
     * @param ruleInfo 报价规则信息
     * @return 失败结果
     */
    public CalculateResult calculateFailFillDayResult(GroovyCalculateResult calculateResult
            , CalculateDayDto code
            , CalculateRuleInfoDto ruleInfo){

        CalculateCodeResult codeResult = new CalculateCodeResult();
        codeResult.setCodeType(code.getCodeType());
        codeResult.setFailCodePkIds(Sets.newHashSet(code.getCodePkIds()));
        CalculateRemarkResult calcRemarkResult = CalculateRemarkResult.builder()
                .codeType(code.getCodeType())
                .calculateFlag(BmsConstants.CALCULATE_FLAG_1)
                .calculateRemark(calculateResult.getMsg())
                .expensesDimension(ruleInfo.getConsolidationRule())
                .expensesDimensionName(ruleInfo.getConsolidationRuleName())
                .ruleDetailPkId(ruleInfo.getPkId())
                .ruleDetailName(ruleInfo.getRuleName())
                .ruleMainPkId(ruleInfo.getMainPkId())
                .ruleMainName(ruleInfo.getMainRuleName())
                .build();
        codeResult.setCostResult(calcRemarkResult);

        return CalculateResult.builder()
                .codeResult(codeResult)
                .build();
    }


    /**
     * 计费成功(按天)
     * @param calculateResult 计费结果
     * @param processResult 计费过程结果
     * @param code 单据信息
     * @param ruleInfo 报价信息
     */
    public CalculateResult calculateSuccessFillDayResult(GroovyCalculateResult calculateResult
            , GroovyProcessResult processResult
            , CalculateDayDto code
            , CalculateRuleInfoDto ruleInfo){

        CalculateCodeResult codeResult = new CalculateCodeResult();
        codeResult.setCodeType(code.getCodeType());
        codeResult.setSuccessCodePkIds(code.getCodePkIds());
        CalculateRemarkResult calcRemarkResult = CalculateRemarkResult.builder()
                .codeType(code.getCodeType())
                .calculateFlag(BmsConstants.CALCULATE_FLAG_0)
                .calculateResult(calculateResult.getResult())
                .calculateRemark(processResult.getMsg())
                .calculateProcessResult(processResult.getResult())
                .expensesDimension(ruleInfo.getConsolidationRule())
                .expensesDimensionName(ruleInfo.getConsolidationRuleName())
                .ruleDetailPkId(ruleInfo.getPkId())
                .ruleDetailName(ruleInfo.getRuleName())
                .ruleMainPkId(ruleInfo.getMainPkId())
                .ruleMainName(ruleInfo.getMainRuleName())
                .build();
        codeResult.setCostResult(calcRemarkResult);

        CalculateCostResult costResult = new CalculateCostResult();
        costResult.setExpenseCode(codeUtils.getCode(CodeCategory.EXPENSECODE, RequestContext.getTenantId()));
        costResult.setExpenseType(code.getExpensesType());
        costResult.setExpensesDimension(ruleInfo.getConsolidationRule());
        costResult.setFeeType(ruleInfo.getFeeType());
        costResult.setCalculateResult(calculateResult.getResult());
        // 额外计费场景处理
        if(!Objects.equals(calculateResult.getExtraType(), GroovyCalculateExtraType.DEFAULT.getCode())){
            costResult.setExtraType(calculateResult.getExtraType());
            costResult.setExtraInfo(calculateResult.getExtraInfo());
        }
        costResult.setCalculateRemark(calculateResult.getMsg());
        costResult.setCalculateProcessResult(processResult.getResult());
        costResult.setBillDate(code.getBusinessTimeMonth());
        costResult.setBusinessTime(code.getBusinessTime());
        costResult.setBeginTime(code.getBusinessBeginTime());
        costResult.setEndTime(code.getBusinessEndTime());
        costResult.setRuleDetailPkId(ruleInfo.getPkId());
        costResult.setRuleDetailName(ruleInfo.getRuleName());
        costResult.setRuleMainPkId(ruleInfo.getMainPkId());
        costResult.setRuleMainName(ruleInfo.getMainRuleName());
        costResult.setCodeType(code.getCodeType());
        costResult.setCodePkIds(code.getCodePkIds());
        costResult.setCodeDataMap(code.getCodeDataMap());
        costResult.setCodeDetailPkIds(code.getCodeDetailPkIds());
        costResult.setCodeDetailDataMap(code.getCodeDetailDataMap());
        costResult.setCodeDetail2PkIds(code.getCodeDetail2PkIds());
        costResult.setCodeDetail2DataMap(code.getCodeDetail2DataMap());
        costResult.setShareType(ruleInfo.getShareType());
        costResult.setCompanyId(code.getCompanyId());
        costResult.setWarehouseCode(code.getWarehouseCode());
        costResult.setWarehouseName(code.getWarehouseName());
        costResult.setCarrierId(code.getCarrierId());
        costResult.setClientId(code.getClientId());
        costResult.setBusinessTime(code.getBusinessTime());
        costResult.setBeginTime(code.getBusinessBeginTime());
        costResult.setEndTime(code.getBusinessEndTime());
        costResult.setWarehouseCode(code.getWarehouseCode());
        costResult.setWarehouseName(code.getWarehouseName());
        costResult.setTotalWeight(code.getTotalWeight());
        costResult.setTotalVolume(code.getTotalVolume());
        costResult.setTotalNumber(code.getTotalNumber());
        costResult.setTotalBoxes(code.getTotalBoxes());
        costResult.setTotalCargoValue(code.getTotalCargoValue());
        costResult.setTotalCodeNumber(PubNumEnum.one.getIntValue());
        costResult.setTotalSkuNumber(code.getDetailSkuNumber());
        costResult.setSettleType(ruleInfo.getSettleType());
        costResult.setSettleSetting(ruleInfo.getSettleSetting());
        costResult.setSettleMainId(ruleInfo.getRelationId());
        SettleCompanyInfo settleCompanyInfo = tenantInfoUtil.tenantSettleCompanyInfo(RequestContext.getTenantId());
        int settleEntityType = settleCompanyInfo == null || StrUtil.isEmpty(settleCompanyInfo.getLogicValue()) ? 1 : Integer.parseInt(settleCompanyInfo.getLogicValue());
        costResult.setSettleEntityType(settleEntityType);
        return CalculateResult.builder()
                .codeResult(codeResult)
                .costResult(costResult)
                .build();
    }



    /**
     * 计费分级匹配失败(按月)
     * @param matchResult 分级匹配结果
     * @param code 单据信息
     * @param ruleInfo 报价规则信息
     * @return 失败结果
     */
    public CalculateResult matchFailFillMonthResult(GroovyMatchResult matchResult
            , CalculateMonthDto code
            , CalculateRuleInfoDto ruleInfo){

        CalculateCodeResult codeResult = new CalculateCodeResult();
        codeResult.setCodeType(code.getCodeType());
        codeResult.setFailCodePkIds(code.getCodePkIds());
        CalculateRemarkResult calcRemarkResult = CalculateRemarkResult.builder()
                .codeType(code.getCodeType())
                .calculateFlag(BmsConstants.CALCULATE_FLAG_1)
                .calculateRemark(matchResult.getMsg())
                .expensesDimension(ruleInfo.getConsolidationRule())
                .expensesDimensionName(ruleInfo.getConsolidationRuleName())
                .ruleDetailPkId(ruleInfo.getPkId())
                .ruleDetailName(ruleInfo.getRuleName())
                .ruleMainPkId(ruleInfo.getMainPkId())
                .ruleMainName(ruleInfo.getMainRuleName())
                .build();
        codeResult.setCostResult(calcRemarkResult);

        return CalculateResult.builder()
                .codeResult(codeResult)
                .build();
    }


    /**
     * 计费失败(按月)
     * @param calculateResult 计费结果
     * @param code 单据信息
     * @param ruleInfo 报价规则信息
     * @return 失败结果
     */
    public CalculateResult calculateFailFillMonthResult(GroovyCalculateResult calculateResult
            , CalculateMonthDto code
            , CalculateRuleInfoDto ruleInfo){

        CalculateCodeResult codeResult = new CalculateCodeResult();
        codeResult.setFailCodePkIds(Sets.newHashSet(code.getCodePkIds()));
        CalculateRemarkResult calcRemarkResult = CalculateRemarkResult.builder()
                .codeType(code.getCodeType())
                .calculateFlag(BmsConstants.CALCULATE_FLAG_1)
                .calculateRemark(calculateResult.getMsg())
                .expensesDimension(ruleInfo.getConsolidationRule())
                .ruleDetailPkId(ruleInfo.getPkId())
                .ruleDetailName(ruleInfo.getRuleName())
                .ruleMainPkId(ruleInfo.getMainPkId())
                .ruleMainName(ruleInfo.getMainRuleName())
                .build();
        codeResult.setCostResult(calcRemarkResult);

        return CalculateResult.builder()
                .codeResult(codeResult)
                .build();
    }


    /**
     * 计费成功(按月)
     * @param calculateResult 计费结果
     * @param processResult 计费过程结果
     * @param code 单据信息
     * @param ruleInfo 报价信息
     */
    public CalculateResult calculateSuccessFillMonthResult(GroovyCalculateResult calculateResult
            , GroovyProcessResult processResult
            , CalculateMonthDto code
            , CalculateRuleInfoDto ruleInfo){

        CalculateCodeResult codeResult = new CalculateCodeResult();
        codeResult.setSuccessCodePkIds(code.getCodePkIds());
        codeResult.setCodeType(code.getCodeType());
        CalculateRemarkResult calcRemarkResult = CalculateRemarkResult.builder()
                .codeType(code.getCodeType())
                .calculateFlag(BmsConstants.CALCULATE_FLAG_0)
                .calculateResult(calculateResult.getResult())
                .calculateRemark(processResult.getMsg())
                .calculateProcessResult(processResult.getResult())
                .expensesDimension(ruleInfo.getConsolidationRule())
                .expensesDimensionName(ruleInfo.getConsolidationRuleName())
                .ruleDetailPkId(ruleInfo.getPkId())
                .ruleDetailName(ruleInfo.getRuleName())
                .ruleMainPkId(ruleInfo.getMainPkId())
                .ruleMainName(ruleInfo.getMainRuleName())
                .build();
        codeResult.setCostResult(calcRemarkResult);

        CalculateCostResult costResult = new CalculateCostResult();
        costResult.setExpenseCode(codeUtils.getCode(CodeCategory.EXPENSECODE, RequestContext.getTenantId()));
        costResult.setExpenseType(code.getExpensesType());
        costResult.setExpensesDimension(ruleInfo.getConsolidationRule());
        costResult.setFeeType(ruleInfo.getFeeType());
        costResult.setCalculateResult(calculateResult.getResult());
        // 额外计费场景处理
        if(!Objects.equals(calculateResult.getExtraType(), GroovyCalculateExtraType.DEFAULT.getCode())){
            costResult.setExtraType(calculateResult.getExtraType());
            costResult.setExtraInfo(calculateResult.getExtraInfo());
        }
        costResult.setCalculateRemark(calculateResult.getMsg());
        costResult.setCalculateProcessResult(processResult.getResult());
        costResult.setBillDate(code.getBusinessTimeMonth());
        costResult.setBusinessTime(code.getBusinessTime());
        costResult.setBeginTime(code.getBusinessBeginTime());
        costResult.setEndTime(code.getBusinessEndTime());
        costResult.setRuleDetailPkId(ruleInfo.getPkId());
        costResult.setRuleDetailName(ruleInfo.getRuleName());
        costResult.setRuleMainPkId(ruleInfo.getMainPkId());
        costResult.setRuleMainName(ruleInfo.getMainRuleName());
        costResult.setCodeType(code.getCodeType());
        costResult.setCodePkIds(code.getCodePkIds());
        costResult.setCodeDataMap(code.getCodeDataMap());
        costResult.setCodeDetailPkIds(code.getCodeDetailPkIds());
        costResult.setCodeDetailDataMap(code.getCodeDetailDataMap());
        costResult.setCodeDetail2PkIds(code.getCodeDetail2PkIds());
        costResult.setCodeDetail2DataMap(code.getCodeDetail2DataMap());
        costResult.setShareType(ruleInfo.getShareType());
        costResult.setCompanyId(code.getCompanyId());
        costResult.setWarehouseCode(code.getWarehouseCode());
        costResult.setWarehouseName(code.getWarehouseName());
        costResult.setClientId(code.getClientId());
        costResult.setCarrierId(code.getCarrierId());
        costResult.setBusinessTime(code.getBusinessTime());
        costResult.setBeginTime(code.getBusinessBeginTime());
        costResult.setEndTime(code.getBusinessEndTime());
        costResult.setWarehouseCode(code.getWarehouseCode());
        costResult.setWarehouseName(code.getWarehouseName());
        costResult.setTotalWeight(code.getTotalWeight());
        costResult.setTotalVolume(code.getTotalVolume());
        costResult.setTotalNumber(code.getTotalNumber());
        costResult.setTotalBoxes(code.getTotalBoxes());
        costResult.setTotalCargoValue(code.getTotalCargoValue());
        costResult.setTotalCodeNumber(PubNumEnum.one.getIntValue());
        costResult.setTotalSkuNumber(code.getDetailSkuNumber());
        costResult.setSettleType(ruleInfo.getSettleType());
        costResult.setSettleSetting(ruleInfo.getSettleSetting());
        costResult.setSettleMainId(ruleInfo.getRelationId());
        SettleCompanyInfo settleCompanyInfo = tenantInfoUtil.tenantSettleCompanyInfo(RequestContext.getTenantId());
        int settleEntityType = settleCompanyInfo == null || StrUtil.isEmpty(settleCompanyInfo.getLogicValue()) ? 1 : Integer.parseInt(settleCompanyInfo.getLogicValue());
        costResult.setSettleEntityType(settleEntityType);
        return CalculateResult.builder()
                .codeResult(codeResult)
                .costResult(costResult)
                .build();
    }


}
