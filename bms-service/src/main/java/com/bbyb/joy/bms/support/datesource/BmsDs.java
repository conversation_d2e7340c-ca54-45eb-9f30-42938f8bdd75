package com.bbyb.joy.bms.support.datesource;


import com.bbyb.joy.core.ds.MultipleDataSource;

/**
 * @description: 数据库配置
 * @author: xzy
 * @date: 2020/8/18 17:37
 * @version: 1.0
 */
public class BmsDs extends MultipleDataSource {

    private static BmsDs instance;

    public static synchronized BmsDs instance() {
        if (instance == null) {
            instance = new BmsDs();
        }
        return instance;
    }
}