package com.bbyb.joy.bms.calculate.convert.impl.dispatch;

import cn.hutool.core.bean.BeanUtil;
import com.bbyb.joy.bms.calculate.convert.OrderConverter;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDto;
import com.bbyb.joy.bms.calculate.utils.CalculateUtil;
import com.bbyb.joy.bms.code.domain.dto.BmsDispatchCodeInfoDto;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 调度单属性转换
 */
@Component
public class DispatchCodeConverter implements OrderConverter<BmsDispatchCodeInfoDto, CalculateOrderDto> {


    @Override
    public List<CalculateOrderDto> convert(List<BmsDispatchCodeInfoDto> sourceList) {
        if(sourceList == null || sourceList.isEmpty()){
            return Collections.emptyList();
        }

        List<CalculateOrderDto> targetList = new ArrayList<>(sourceList.size());
        for (BmsDispatchCodeInfoDto source : sourceList) {
            CalculateOrderDto target = BeanUtil.toBean(source, CalculateOrderDto.class);
            target.setCode(source.getVirtualSchedulingCode());
            target.setCodeType(BmsConstants.CODE_TYPE_1);
            target.setExpenseType(BmsConstants.EXPENSES_TYPE_1);
            // 特殊字段处理
            CalculateUtil.BuildBusinessTimeModel businessTimeModel = CalculateUtil.billDateSetting(source.getDispatchDate(), source.getFinishDate(), source.getCostDateDimension());
            target.setBusinessTime(businessTimeModel.getBusinessTime());
            target.setBusinessTimeStr(businessTimeModel.getBusinessTimeStr());
            target.setBusinessTimeMonth(businessTimeModel.getBusinessTimeMonth());
            target.setBusinessTimeDay(businessTimeModel.getBusinessTimeDay());
            target.setBusinessBeginTime(businessTimeModel.getBusinessBeginTime());
            target.setBusinessBeginStr(businessTimeModel.getBusinessBeginStr());
            target.setBusinessBeginMonth(businessTimeModel.getBusinessBeginMonth());
            target.setBusinessBeginDay(businessTimeModel.getBusinessBeginDay());
            target.setBusinessEndTime(businessTimeModel.getBusinessEndTime());
            target.setBusinessEndStr(businessTimeModel.getBusinessEndStr());
            target.setBusinessEndMonth(businessTimeModel.getBusinessEndMonth());
            target.setBusinessEndDay(businessTimeModel.getBusinessEndDay());
            target.setOrderDate(businessTimeModel.getBusinessBeginStr());
            target.setSigningDate(businessTimeModel.getBusinessEndStr());
            targetList.add(target);
        }

        return targetList;
    }

    @Override
    public Class<BmsDispatchCodeInfoDto> getSourceType() {
        return BmsDispatchCodeInfoDto.class;
    }

    @Override
    public Class<CalculateOrderDto> getTargetType() {
        return CalculateOrderDto.class;
    }


}
