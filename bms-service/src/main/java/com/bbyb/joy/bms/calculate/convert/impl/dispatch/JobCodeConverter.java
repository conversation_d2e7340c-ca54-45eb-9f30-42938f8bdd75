package com.bbyb.joy.bms.calculate.convert.impl.dispatch;

import cn.hutool.core.bean.BeanUtil;
import com.bbyb.joy.bms.calculate.convert.OrderConverter;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDetail2Dto;
import com.bbyb.joy.bms.code.domain.dto.BmsJobCodeInfoDto;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 作业单据转换
 */
@Component
public class JobCodeConverter implements OrderConverter<BmsJobCodeInfoDto, CalculateOrderDetail2Dto> {


    @Override
    public List<CalculateOrderDetail2Dto> convert(List<BmsJobCodeInfoDto> sourceList) {
        if(sourceList == null || sourceList.isEmpty()){
            return Collections.emptyList();
        }

        List<CalculateOrderDetail2Dto> targetList = new ArrayList<>(sourceList.size());
        for (BmsJobCodeInfoDto source : sourceList) {
            CalculateOrderDetail2Dto target = BeanUtil.toBean(source, CalculateOrderDetail2Dto.class);
            target.setCode(source.getRelateCode());
            // 特殊字段处理
            if(target.getTotalBoxes() == null){
                target.setTotalBoxes(BigDecimal.ZERO);
            }
            if(target.getTotalNumber() == null){
                target.setTotalNumber(BigDecimal.ZERO);
            }
            if(target.getTotalWeight() == null){
                target.setTotalWeight(BigDecimal.ZERO);
            }
            if(target.getTotalVolume() == null){
                target.setTotalVolume(BigDecimal.ZERO);
            }
            if(target.getTotalCargoValue() == null){
                target.setTotalCargoValue(BigDecimal.ZERO);
            }
            targetList.add(target);
        }

        return targetList;
    }

    @Override
    public Class<BmsJobCodeInfoDto> getSourceType() {
        return BmsJobCodeInfoDto.class;
    }

    @Override
    public Class<CalculateOrderDetail2Dto> getTargetType() {
        return CalculateOrderDetail2Dto.class;
    }


}
