package com.bbyb.joy.bms.controller;

import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.domain.dto.FileBaseDto;
import com.bbyb.joy.bms.domain.dto.entity.SysDictType;
import com.bbyb.joy.bms.service.ISysDictTypeService;
import com.bbyb.joy.bms.support.ServiceError;
import com.bbyb.joy.bms.support.utils.ExcelUtil;
import com.bbyb.joy.core.context.RequestContext;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping({"/system/dict/type"})
public class SysDictTypeController {
    @Resource
    private ISysDictTypeService dictTypeService;

    public SysDictTypeController() {
    }

    /**
     * 分页查询
     */
    @PostMapping({"/list"})
    public ResponseResult<PagerDataBean<SysDictType>> list(@RequestBody(required = false) SysDictType dictType) {
        String token = RequestContext.getToken();
        if (dictType == null) {
            return new ResponseResult<>(ServiceError.SERVICE_ERROR.getCode(), "参数不可为空");
        }
        return new ResponseResult<>(this.dictTypeService.selectDictTypeList(token, dictType));
    }

    /**
     * 导出
     */
    @PostMapping({"/export"})
    public ResponseResult<String> export(@RequestBody(required = false) SysDictType dictType) {
        String token = RequestContext.getToken();
        PagerDataBean<SysDictType> list = this.dictTypeService.selectDictTypeList(token, dictType);
        ExcelUtil<SysDictType> util = new ExcelUtil<>(SysDictType.class);
        FileBaseDto fileBaseDto = util.exportExcel(list.getRows(), "字典类型");
        return new ResponseResult<>(true, 0, fileBaseDto.getFileName(), fileBaseDto.getFileName());
    }

    /**
     * 查询字典详情
     */
    @PostMapping({"/{dictId}"})
    public ResponseResult<SysDictType> getInfo(@PathVariable Long dictId) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(this.dictTypeService.selectDictTypeById(token, dictId));
    }

    /**
     * 字典新增
     */
    @PostMapping("/add")
    public ResponseResult<Integer> add(@RequestBody(required = false) SysDictType dict) {
        String token = RequestContext.getToken();
        if ("1".equals(this.dictTypeService.checkDictTypeUnique(token, dict))) {
            return new ResponseResult<>(false, 405, "新增字典'" + dict.getDictName() + "'失败，字典类型已存在", null);
        } else {
            UserBean userVO = RequestContext.getUserInfo();
            dict.setCreateBy(userVO.getEmployeename());
            return new ResponseResult<>(this.dictTypeService.insertDictType(token, dict));
        }
    }

    /**
     * 字典修改
     */
    @PostMapping("/edit")
    public ResponseResult<Integer> edit(@RequestBody SysDictType dict) {
        String token = RequestContext.getToken();
        if ("1".equals(this.dictTypeService.checkDictTypeUnique(token, dict))) {
            return new ResponseResult<>(false, 406, "修改字典'" + dict.getDictName() + "'失败，字典类型已存在", null);
        } else {
            UserBean userVO = RequestContext.getUserInfo();
            dict.setCreateBy(userVO.getEmployeename());
            return new ResponseResult<>(this.dictTypeService.updateDictType(token, dict));
        }
    }

    /**
     * 字典作废
     */
    @DeleteMapping({"/{dictIds}"})
    public ResponseResult<Boolean> remove(@PathVariable Long[] dictIds) {
        String token = RequestContext.getToken();
        this.dictTypeService.deleteDictTypeByIds(token, dictIds);
        return new ResponseResult<>(true);
    }

    /**
     * 刷新缓存
     */
    @DeleteMapping({"/refreshCache"})
    public ResponseResult<Boolean> refreshCache() {
        String token = RequestContext.getToken();
        this.dictTypeService.resetDictCache(token);
        return new ResponseResult<>(true);
    }

    /**
     * 查询全部字典
     */
    @PostMapping({"/optionselect"})
    public ResponseResult<List<SysDictType>> optionselect() {
        String token = RequestContext.getToken();
        return new ResponseResult<>(this.dictTypeService.selectDictTypeAll(token));
    }
}
