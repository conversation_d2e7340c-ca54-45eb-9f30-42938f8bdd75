package com.bbyb.joy.bms.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bbyb.joy.bean.PagerBean;
import com.bbyb.joy.bean.PagerDataBean;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.domain.dto.BmsYsbillcodeDetailinfo;
import com.bbyb.joy.bms.domain.dto.MdmWarehouseinfo;
import com.bbyb.joy.bms.domain.dto.SysDataPack;
import com.bbyb.joy.bms.domain.dto.charginglogic.OrderBillingBean;
import com.bbyb.joy.bms.domain.dto.dto.BmsYfbillcodeDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYsbillBtnDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDetailinfoDto;
import com.bbyb.joy.bms.domain.dto.dto.BmsYsbillcodeDto;
import com.bbyb.joy.bms.domain.dto.querybean.BmsYsbillcodeinfoBean;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.ClientInfo;
import com.bbyb.joy.bms.domain.dto.sysdatagroup.WarehouseInfo;
import com.bbyb.joy.bms.service.IBmsYsbillcodeDetailinfoService;
import com.bbyb.joy.bms.service.IBmsYsbillcodeinfoService;
import com.bbyb.joy.bms.service.impl.excelout.ExportUtil;
import com.bbyb.joy.bms.support.utils.TextConversionUtil;
import com.bbyb.joy.bms.support.utils.UserRights;
import com.bbyb.joy.core.annotation.Log;
import com.bbyb.joy.core.annotation.MenuAuthority;
import com.bbyb.joy.core.context.RequestContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应收单据信息主Controller
 */
@Api(tags = "应收仓储计费主接口")
@RestController
@RequestMapping("/system/ysbillcodeinfo2")
public class BmsYsbillstorageChargeController {

    @Resource
    private IBmsYsbillcodeinfoService bmsYsbillcodeinfoService;
    @Resource
    private IBmsYsbillcodeDetailinfoService bmsYsbillcodeDetailinfoService;
    @Resource
    private TextConversionUtil textConversionUtil;
    @Resource
    private UserRights userRights;
    @Resource
    private ExportUtil exportUtil;


    /**
     * 订单计费导入订单
     */
    @ApiOperation(value = "订单计费导入", response = MultipartFile.class)
    @PostMapping("/importOrder")
    @MenuAuthority(code = "应收仓储计费-导入计费")
    public ResponseResult<String> importOrder(@RequestBody MultipartFile file) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeinfoService.importOrder(token, file, "2"));
    }

    /**
     * 订单计费分页查询
     */
    @ApiOperation(value = "订单计费分页查询", response = BmsYsbillcodeDto.class)
    @Log(name = "应收仓储分页查询")
    @PostMapping("/selectOrderBill")
    @MenuAuthority(code = "应收仓储计费,应收仓储计费-批量查询,应收仓储计费-费用账单,应收仓储计费-批量查询")
    public ResponseResult<PagerDataBean<BmsYsbillcodeDto>> selectOrderBill(@RequestBody BmsYsbillcodeinfoBean bean) {
        List<BmsYsbillcodeDto> list = new ArrayList<>();
        String token = RequestContext.getToken();
        //加权限
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client,warehouse");
        List<ClientInfo> clientInfos = sysDataPack.getClientinfo();
        List<WarehouseInfo> warehouseInfos = sysDataPack.getWarehouseInfo();
        bean.setClientCode(clientInfos.stream().map(ClientInfo::getClientCode).collect(Collectors.joining(",")));
        if (StrUtil.isEmpty(bean.getWarehouseCode())) {
            bean.setWarehouseCode(warehouseInfos.stream().map(WarehouseInfo::getWarehouseCode).collect(Collectors.joining(",")));
        }
        if (StrUtil.isEmpty(bean.getWarehouseCode()) || StrUtil.isEmpty(bean.getClientCode())) {
            return new ResponseResult<>(new PagerDataBean<>(list, 0, new PagerBean(bean.getPage(), bean.getSize())));
        }
        // 根据仓库名称查询仓库编码
        if (StrUtil.isNotEmpty(bean.getWarehouseName())) {
            Map<String, MdmWarehouseinfo> warehouseMap = textConversionUtil.selectWarehouseMap(null, null, bean.getWarehouseName());
            if (warehouseMap.isEmpty()) {
                return new ResponseResult<>(new PagerDataBean<>());
            } else {
                List<String> s = warehouseMap.values().stream().map(MdmWarehouseinfo::getWarehouseCode).collect(Collectors.toList());
                List<String> ss = Arrays.asList(bean.getWarehouseCode().split(","));
                String warehouseCode = s.stream().filter(ss::contains).collect(Collectors.joining(","));
                if (StrUtil.isEmpty(warehouseCode)) {
                    return new ResponseResult<>(new PagerDataBean<>());
                }
                bean.setWarehouseCode(warehouseCode);
            }
        }
        return new ResponseResult<>(bmsYsbillcodeinfoService.selectOrderBill(token, bean));
    }

    /**
     * 修改计费
     */
    @ApiOperation(value = "修改计费", response = OrderBillingBean.class)
    @PostMapping("/updateCost")
    @MenuAuthority(code = "应收仓储计费-修改费用")
    public ResponseResult<String> updateCost(@RequestBody OrderBillingBean bean) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeinfoService.updateCost(token, bean));
    }

    /**
     * 导入计费
     */
    @ApiOperation(value = "导入计费", response = OrderBillingBean.class)
    @PostMapping("/importCost")
    @MenuAuthority(code = "应收仓储计费-导入计费")
    public ResponseResult<String> importCost(@RequestBody List<OrderBillingBean> list) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeinfoService.importCost(token, list));
    }

    /**
     * 取消计费
     */
    @ApiOperation(value = "取消计费", response = OrderBillingBean.class)
    @PostMapping("/cancelCost")
    @MenuAuthority(code = "应收仓储计费-取消计费")
    public ResponseResult<String> cancelCost(@RequestBody List<OrderBillingBean> list) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeinfoService.cancelCost(token, list));
    }

    /**
     * 生成账单
     */
    @ApiOperation(value = "生成账单", response = BmsYsbillBtnDto.class)
    @Log(name = "生成账单")
    @PostMapping("/generateBill")
    @MenuAuthority(code = "应收仓储计费-批量生成账单")
    public ResponseResult<String> generateBill(@RequestBody BmsYsbillBtnDto bmsYsbillBtnDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeinfoService.generateBill(token, bmsYsbillBtnDto));
    }

    /**
     * 批量生成账单
     */
    @ApiOperation(value = "批量生成账单", response = BmsYsbillBtnDto.class)
    @PostMapping("/generateBillBatch")
    @MenuAuthority(code = "应收仓储计费-批量生成账单")
    public ResponseResult<String> generateBillBatch(@RequestBody BmsYsbillBtnDto bmsYsbillBtnDto) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeinfoService.generateBillBatch(token, bmsYsbillBtnDto));
    }

    /**
     * 订单计费明细查询
     */
    @ApiOperation(value = "订单计费明细查询", response = BmsYsbillcodeDetailinfoDto.class)
    @PostMapping("/selectOrderDetail")
    public ResponseResult<List<BmsYsbillcodeDetailinfoDto>> selectOrderDetail(HttpServletRequest request, @RequestBody BmsYsbillcodeDetailinfo bmsYsbillcodeDetailinfo) {
        String token = RequestContext.getToken();
        return new ResponseResult<>(bmsYsbillcodeDetailinfoService.selectBmsYsbillcodeDetailinfoList(token, bmsYsbillcodeDetailinfo));
    }

    /**
     * 订单计费导出
     */
    @ApiOperation(value = "订单计费导出", response = BmsYsbillcodeDto.class)
    @PostMapping("/export")
    @MenuAuthority(code = "应收仓储计费-导出")
    public ResponseResult<String> export(@RequestBody BmsYsbillcodeinfoBean bean) {
        String token = RequestContext.getToken();
        Long userId = Long.valueOf(RequestContext.getUserInfo().getId());
        if (StrUtil.isNotEmpty(bean.getWarehouseName())) {
            Map<String, MdmWarehouseinfo> warehouseMap = textConversionUtil.selectWarehouseMap(null, null, bean.getWarehouseName());
            if (warehouseMap.isEmpty()) {
                return null;
            } else {
                bean.setWarehouseCode(warehouseMap.values().stream().map(MdmWarehouseinfo::getWarehouseCode).collect(Collectors.joining(",")));
            }
        }
        // 数据权限
        SysDataPack sysDataPack = userRights.getRightsByUserId(userId, "client,warehouse");
        List<ClientInfo> clientInfos = sysDataPack.getClientinfo();
        List<WarehouseInfo> warehouseInfos = sysDataPack.getWarehouseInfo();
        bean.setClientCode(clientInfos.stream().map(ClientInfo::getClientCode).collect(Collectors.joining(",")));
        bean.setWarehouseCode(warehouseInfos.stream().map(WarehouseInfo::getWarehouseCode).collect(Collectors.joining(",")));

        if (StrUtil.isNotEmpty(bean.getWarehouseName())) {
            Map<String, MdmWarehouseinfo> warehouseMap = textConversionUtil.selectWarehouseMap(null, null, bean.getWarehouseName());
            if (!warehouseMap.isEmpty()) {
                List<String> s = warehouseMap.values().stream().map(MdmWarehouseinfo::getWarehouseCode).collect(Collectors.toList());
                List<String> ss = Arrays.asList(bean.getWarehouseCode().split(","));
                String warehouseCode = s.stream().filter(ss::contains).collect(Collectors.joining(","));
                bean.setWarehouseCode(warehouseCode);
            }
        }
        PagerDataBean<BmsYsbillcodeDto> list = bmsYsbillcodeinfoService.selectOrderBill(token, bean);
        String dateTime = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss").format(new Date());
        String fileNameMsg = "应收仓储计费_" + dateTime;
        boolean f = true;
        String msg = list.getRows().get(0).getClientCode();
        for (BmsYsbillcodeDto bmsYsbillcodeDto : list.getRows()) {
            if (!Objects.equals(bmsYsbillcodeDto.getClientCode(), msg)) {
                f = false;
                break;
            }
        }
        if (f) {
            fileNameMsg = list.getRows().get(0).getClientName() + "_" + fileNameMsg;
        }
        return exportUtil.getOutClassNewSheets2(token, fileNameMsg, "应收仓储计费", "ysbillcodeinfo2", BmsYfbillcodeDto.class, bean, userid -> {
            // 根据仓库名称查询仓库编码
            if (StrUtil.isEmpty(bean.getWarehouseCode()) || StrUtil.isEmpty(bean.getClientCode())) {
                return null;
            }
            return list.getRows();
        });
    }

    /**
     * 根据计费id查询单据信息
     */
    @ApiOperation(value = "根据计费id查询单据信息", response = BmsYsbillcodeDto.class)
    @PostMapping("/selectOrderByExpensesId")
    public ResponseResult<List<BmsYsbillcodeDto>> selectOrderByExpensesId(@RequestBody(required = false) String json) {
        String token = RequestContext.getToken();
        String expensesId = JSONObject.parseObject(json).getString("expensesId");
        return new ResponseResult<>(bmsYsbillcodeinfoService.selectOrderByExpensesId(token, expensesId));
    }
}
