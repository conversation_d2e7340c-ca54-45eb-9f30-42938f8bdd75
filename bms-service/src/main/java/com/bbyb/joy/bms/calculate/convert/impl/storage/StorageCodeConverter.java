package com.bbyb.joy.bms.calculate.convert.impl.storage;

import cn.hutool.core.bean.BeanUtil;
import com.bbyb.joy.bms.calculate.convert.OrderConverter;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDto;
import com.bbyb.joy.bms.calculate.utils.CalculateUtil;
import com.bbyb.joy.bms.code.domain.dto.BmsStorageCodeInfoDto;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 仓储单据转换
 */
@Component
public class StorageCodeConverter implements OrderConverter<BmsStorageCodeInfoDto, CalculateOrderDto> {


    @Override
    @SuppressWarnings("rawtypes")
    public List<CalculateOrderDto> convert(List<BmsStorageCodeInfoDto> sourceList) {
        if(sourceList == null || sourceList.isEmpty()){
            return Collections.emptyList();
        }

        List<CalculateOrderDto> targetList = new ArrayList<>(sourceList.size());
        for (BmsStorageCodeInfoDto source : sourceList) {
            CalculateOrderDto target = BeanUtil.toBean(source, CalculateOrderDto.class);
            target.setCode(source.getRelateCode());
            target.setExpenseType(source.getCodeType());
            // 特殊字段处理
            CalculateUtil.BuildBusinessTimeModel businessTimeModel = CalculateUtil.billDateSetting(source.getOrderDate(), source.getSigningDate(), source.getCostDateDimension());
            target.setBusinessTime(businessTimeModel.getBusinessTime());
            target.setBusinessTimeStr(businessTimeModel.getBusinessTimeStr());
            target.setBusinessTimeMonth(businessTimeModel.getBusinessTimeMonth());
            target.setBusinessTimeDay(businessTimeModel.getBusinessTimeDay());
            target.setBusinessBeginTime(businessTimeModel.getBusinessBeginTime());
            target.setBusinessBeginStr(businessTimeModel.getBusinessBeginStr());
            target.setBusinessBeginMonth(businessTimeModel.getBusinessBeginMonth());
            target.setBusinessBeginDay(businessTimeModel.getBusinessBeginDay());
            target.setBusinessEndTime(businessTimeModel.getBusinessEndTime());
            target.setBusinessEndStr(businessTimeModel.getBusinessEndStr());
            target.setBusinessEndMonth(businessTimeModel.getBusinessEndMonth());
            target.setBusinessEndDay(businessTimeModel.getBusinessEndDay());
            target.setOrderDate(businessTimeModel.getBusinessBeginStr());
            target.setSigningDate(businessTimeModel.getBusinessEndStr());
            targetList.add(target);
        }

        return targetList;
    }

    @SuppressWarnings("rawtypes")
    public Class<BmsStorageCodeInfoDto> getSourceType() {
        return BmsStorageCodeInfoDto.class;
    }

    @Override
    public Class<CalculateOrderDto> getTargetType() {
        return CalculateOrderDto.class;
    }
}
