package com.bbyb.joy.bms.support.annotation;


import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.bms.domain.RequestApiDto;
import com.bbyb.joy.bms.support.utils.responseApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
public class ApiCheckAspect {
    private static final Logger logger = LoggerFactory.getLogger(ApiCheckAspect.class);

    // 定义一个切面，括号内写入第1步中自定义注解的路径
    @Pointcut("@annotation(com.bbyb.joy.bms.support.annotation.ApiCheck)")
    private void pointCut() {

    }

    @Around(value = "pointCut() && @annotation(apiCheck)")
    private Object apiCheck(ProceedingJoinPoint joinPoint, ApiCheck apiCheck) {

        try {
            Object[] objects = joinPoint.getArgs();

            Object obj = objects[0];

            RequestApiDto requestApiDto = (RequestApiDto) obj;

            if (StrUtil.isEmpty(requestApiDto.getServiceName())) {
                return responseApiUtil.responseMessage(false, "XML_ERROR", "方法名称不能为空", (Object) null);
            }
            if (StrUtil.isEmpty(requestApiDto.getTimeStamp())) {
                return responseApiUtil.responseMessage(false, "XML_ERROR", "时间戳不能为空", (Object) null);
            }
            if (StrUtil.isEmpty(requestApiDto.getAppKey())) {
                return responseApiUtil.responseMessage(false, "XML_ERROR", "AppKey不能为空", (Object) null);
            }
            if (ObjUtil.isEmpty(requestApiDto.getData())) {
                return responseApiUtil.responseMessage(false, "XML_ERROR", "请求数据源不能为空不能为空", (Object) null);
            }

            // 验证加密不正确返回失败
//            if(!Objects.equals(requestApiDto.getSign(), Md5Utils.hash(requestApiDto.getTimeStamp() + requestApiDto.getAppKey())))
//            {
//                return responseApiUtil.responseMessage(false,"SIGN_ERROR","商户签名错误", (Object)null);
//            }
            return joinPoint.proceed();
        } catch (Throwable e) {
            logger.error("系统异常", e);
            log.error("系统异常", e);
        }
        return null;
    }
}
