package com.bbyb.joy.bms.calculate.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import com.bbyb.joy.bms.calculate.biz.CalculateBiz;
import com.bbyb.joy.bms.support.datesource.BmsDs;
import com.bbyb.joy.constants.Type;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class CalculateBizImpl implements CalculateBiz {

    private static final String MAPPER = "BmsCalculateMapper";

    /**
     * 费用数据校准
     * 处理middle表，middle_share表,record表的单据以及费用单的缺失id
     * @param codePkIds 单据pkId
     * 策略类型:1:运输单,21:仓储单(应收),22:仓储单(应付),3:调度单
     */
    @Override
    public void calculateDataDalibration(Integer handlerType, List<Integer> codePkIds) {
        Map<String, Object> cond = BeanUtil.beanToMap(codePkIds);
        cond.put("handlerType", handlerType);
        cond.put("codePkIds", codePkIds);
        BmsDs.instance().WMSMybatis().xmlMapper(MAPPER, Type.SYSTEM_TYPE_BMS_CODE).update("calculateDataDalibration",cond);
    }
}
