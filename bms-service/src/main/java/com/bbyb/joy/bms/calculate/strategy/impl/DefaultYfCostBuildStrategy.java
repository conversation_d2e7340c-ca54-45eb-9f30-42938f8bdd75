package com.bbyb.joy.bms.calculate.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import com.bbyb.bms.model.po.BmsYfcostInfoPO;
import com.bbyb.bms.model.po.BmsYfcostMainInfoPO;
import com.bbyb.bms.model.po.BmsYfexpensesMiddlePO;
import com.bbyb.bms.model.po.BmsYfexpensesMiddleSharePO;
import com.bbyb.joy.bean.UserBean;
import com.bbyb.joy.bms.calculate.domain.result.CalculateCostResult;
import com.bbyb.joy.bms.calculate.strategy.YfCostBuildStrategy;
import com.bbyb.joy.bms.calculate.utils.groovy.domain.result.GroovyCalculateExtraType;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import com.bbyb.joy.bms.support.utils.DateUtils;
import com.bbyb.joy.bms.support.utils.cost.CostUtil;
import com.bbyb.joy.bms.support.utils.cost.StatisticsFieldEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 默认应付费用构建策略
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Component
public class DefaultYfCostBuildStrategy implements YfCostBuildStrategy {

    @Override
    public GroovyCalculateExtraType getSupportedType() {
        return GroovyCalculateExtraType.DEFAULT;
    }

    @Override
    public YfCostBuildResult buildYfCostInfo(CalculateCostResult costResult, String costMainId, String mainExpenseCode,
                                           String costId, String expenseCode, Integer optCodePkId, Set<Integer> optCodeDetail2PkIds,
                                           UserBean userInfo, Date now, Map<Integer, Dict> allCodeDetail2DataMap) {
        
        log.info("使用默认策略构建应付费用信息，optCodePkId: {}", optCodePkId);
        
        // 构建主费用信息
        BmsYfcostMainInfoPO buildMainCostInfo = buildMainCostInfo(costResult, costMainId, mainExpenseCode, userInfo, now);
        
        // 构建费用信息列表
        List<BmsYfcostInfoPO> buildCostInfos = Lists.newArrayList();
        BmsYfcostInfoPO buildCostInfo = buildCostInfo(costResult, costId, expenseCode, costMainId, userInfo, now);
        buildCostInfos.add(buildCostInfo);

        // 构建中间表信息
        BmsYfexpensesMiddlePO buildCostMiddle = buildMiddleInfo(costResult, costMainId, optCodePkId);

        // 构建分摊表信息（如果有作业单明细）
        List<BmsYfexpensesMiddleSharePO> buildSharePOList = Lists.newArrayList();
        if(CollUtil.isNotEmpty(optCodeDetail2PkIds)){
            buildSharePOList = buildShareInfoList(costResult, costMainId, optCodeDetail2PkIds,
                                                buildMainCostInfo, allCodeDetail2DataMap);
        }

        return new YfCostBuildResult(buildMainCostInfo, buildCostInfos, buildCostMiddle, buildSharePOList);
    }

    /**
     * 构建主费用信息
     */
    private BmsYfcostMainInfoPO buildMainCostInfo(CalculateCostResult costResult, String costMainId, 
                                                 String mainExpenseCode, UserBean userInfo, Date now) {
        
        BmsYfcostMainInfoPO buildMainCostInfo = new BmsYfcostMainInfoPO();
        buildMainCostInfo.setId(costMainId);
        buildMainCostInfo.setExpensesCode(mainExpenseCode);
        buildMainCostInfo.setExpensesType(costResult.getExpenseType());
        buildMainCostInfo.setExpensesDimension(costResult.getExpensesDimension());
        buildMainCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
        buildMainCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
        buildMainCostInfo.setBusinessTime(costResult.getBusinessTime());
        buildMainCostInfo.setDispatchDate(costResult.getBeginTime());
        buildMainCostInfo.setFinishDate(costResult.getEndTime());
        buildMainCostInfo.setCarrierId(costResult.getCarrierId());
        buildMainCostInfo.setClientId(costResult.getClientId());
        buildMainCostInfo.setCompanyId(costResult.getCompanyId());
        buildMainCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
        buildMainCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
        buildMainCostInfo.setTotalBoxes(costResult.getTotalBoxes());
        buildMainCostInfo.setTotalNumber(costResult.getTotalNumber());
        buildMainCostInfo.setTotalWeight(costResult.getTotalWeight());
        buildMainCostInfo.setTotalVolume(costResult.getTotalVolume());
        buildMainCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
        buildMainCostInfo.setTotalOverNum(costResult.getTotalOverNum());
        buildMainCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
        buildMainCostInfo.setSettleType(costResult.getSettleType());
        buildMainCostInfo.setSettleEntityType(costResult.getSettleEntityType());
        buildMainCostInfo.setSettleSetting(costResult.getSettleSetting());
        buildMainCostInfo.setSettleMainId(costResult.getSettleMainId());
        buildMainCostInfo.setSettleAmount(costResult.getCalculateResult());
        buildMainCostInfo = CostUtil.setCostField(buildMainCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
        buildMainCostInfo.setCreateBy(userInfo.getEmployeename());
        buildMainCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
        buildMainCostInfo.setCreateTime(now);
        buildMainCostInfo.setOperBy(userInfo.getEmployeename());
        buildMainCostInfo.setOperCode(userInfo.getEmployeecode().toString());
        buildMainCostInfo.setOperTime(now);
        buildMainCostInfo.setOptMonth(DateUtils.optMonth());
        buildMainCostInfo.setOptDay(DateUtils.optDay());
        
        return buildMainCostInfo;
    }

    /**
     * 构建费用信息
     */
    private BmsYfcostInfoPO buildCostInfo(CalculateCostResult costResult, String costId, String expenseCode,
                                         String costMainId, UserBean userInfo, Date now) {
        
        BmsYfcostInfoPO buildCostInfo = new BmsYfcostInfoPO();
        buildCostInfo.setId(costId);
        buildCostInfo.setExpensesCode(expenseCode);
        buildCostInfo.setMainCodeId(costMainId);
        buildCostInfo.setExpensesType(costResult.getExpenseType());
        buildCostInfo.setExpensesDimension(costResult.getExpensesDimension());
        buildCostInfo.setExpensesMark(BmsConstants.EXPENSES_MARK_1);
        buildCostInfo.setChargeType(BmsConstants.CHARGE_TYPE_1);
        buildCostInfo.setBusinessTime(costResult.getBusinessTime());
        buildCostInfo.setBillDate(costResult.getBillDate());
        buildCostInfo.setDispatchDate(costResult.getBeginTime());
        buildCostInfo.setFinishDate(costResult.getEndTime());
        buildCostInfo.setClientId(costResult.getClientId());
        buildCostInfo.setCarrierId(costResult.getCarrierId());
        buildCostInfo.setCompanyId(costResult.getCompanyId());
        buildCostInfo.setWarehouseCode(costResult.getWarehouseCode());
        buildCostInfo.setWarehouseName(costResult.getWarehouseName());
        buildCostInfo.setQuoterRuleId(costResult.getRuleMainPkId().toString());
        buildCostInfo.setQuoterRuleName(costResult.getRuleMainName());
        buildCostInfo.setQuoterRuleDetailId(costResult.getRuleDetailPkId().toString());
        buildCostInfo.setQuoterRuleDetailName(costResult.getRuleDetailName());
        buildCostInfo.setTotalCodeNumber(costResult.getTotalCodeNumber());
        buildCostInfo.setTotalSkuNumber(costResult.getTotalSkuNumber());
        buildCostInfo.setTotalBoxes(costResult.getTotalBoxes());
        buildCostInfo.setTotalNumber(costResult.getTotalNumber());
        buildCostInfo.setTotalWeight(costResult.getTotalWeight());
        buildCostInfo.setTotalVolume(costResult.getTotalVolume());
        buildCostInfo.setTotalCargoValue(costResult.getTotalCargoValue());
        buildCostInfo.setTotalOverNum(costResult.getTotalOverNum());
        buildCostInfo.setTotalOverStoreNum(costResult.getTotalOverStoreNum());
        buildCostInfo.setSettleType(costResult.getSettleType());
        buildCostInfo.setSettleEntityType(costResult.getSettleEntityType());
        buildCostInfo.setSettleSetting(costResult.getSettleSetting());
        buildCostInfo.setSettleMainId(costResult.getSettleMainId());
        buildCostInfo.setSettleAmount(costResult.getCalculateResult());
        buildCostInfo = CostUtil.setCostField(buildCostInfo, costResult.getFeeType(), costResult.getCalculateResult());
        buildCostInfo.setCreateCode(userInfo.getEmployeecode().toString());
        buildCostInfo.setCreateBy(userInfo.getEmployeecode().toString());
        buildCostInfo.setCreateTime(now);
        buildCostInfo.setOperBy(userInfo.getEmployeecode().toString());
        buildCostInfo.setOperCode(userInfo.getEmployeecode().toString());
        buildCostInfo.setOperTime(now);
        buildCostInfo.setOptMonth(DateUtils.optMonth());
        buildCostInfo.setOptDay(DateUtils.optDay());
        
        return buildCostInfo;
    }

    /**
     * 构建中间表信息
     */
    private BmsYfexpensesMiddlePO buildMiddleInfo(CalculateCostResult costResult, String costMainId, Integer optCodePkId) {

        Map<Integer, Dict> codeDataMap = costResult.getCodeDataMap();
        BmsYfexpensesMiddlePO buildCostMiddle = new BmsYfexpensesMiddlePO();
        buildCostMiddle.setCodePkId(optCodePkId);
        buildCostMiddle.setCodeType(costResult.getCodeType());
        buildCostMiddle.setMainCodeId(costMainId);
        buildCostMiddle.setShareType(costResult.getShareType());
        buildCostMiddle.setSettleAmount(costResult.getCalculateResult());
        buildCostMiddle.setOptMonth(DateUtils.optMonth());
        buildCostMiddle.setOptDay(DateUtils.optDay());

        if(codeDataMap.containsKey(optCodePkId)){
            Dict dict = codeDataMap.get(optCodePkId);
            BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) ?
                dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) : BigDecimal.ZERO;
            BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ?
                dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
            BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ?
                dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
            buildCostMiddle.setTotalWeight(totalWeight);
            buildCostMiddle.setTotalVolume(totalVolume);
            buildCostMiddle.setTotalNumber(totalNumber);
        }
        buildCostMiddle = CostUtil.setCostField(buildCostMiddle, costResult.getFeeType(), costResult.getCalculateResult());

        return buildCostMiddle;
    }

    /**
     * 构建分摊表信息列表
     */
    private List<BmsYfexpensesMiddleSharePO> buildShareInfoList(CalculateCostResult costResult, String costMainId,
                                                              Set<Integer> optCodeDetail2PkIds, BmsYfcostMainInfoPO buildMainCostInfo,
                                                              Map<Integer, Dict> allCodeDetail2DataMap) {

        Map<Integer, Dict> codeDetail2DataMap = costResult.getCodeDetail2DataMap();
        allCodeDetail2DataMap.putAll(codeDetail2DataMap);
        List<BmsYfexpensesMiddleSharePO> buildSharePOList = Lists.newArrayList();

        for (Integer optCodeDetail2PkId : optCodeDetail2PkIds) {
            BmsYfexpensesMiddleSharePO buildShareData = new BmsYfexpensesMiddleSharePO();
            buildShareData.setCodePkId(optCodeDetail2PkId);
            buildShareData.setMainCodeId(costMainId);
            buildShareData.setShareType(costResult.getShareType());
            buildShareData.setOptMonth(DateUtils.optMonth());
            buildShareData.setOptDay(DateUtils.optDay());

            if(codeDetail2DataMap.containsKey(optCodeDetail2PkId)){
                Dict dict = codeDetail2DataMap.get(optCodeDetail2PkId);
                BigDecimal totalWeight = dict.containsKey(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) ?
                    dict.getBigDecimal(StatisticsFieldEnum.TOTAL_WEIGHT.getFieldName()) : BigDecimal.ZERO;
                BigDecimal totalVolume = dict.containsKey(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) ?
                    dict.getBigDecimal(StatisticsFieldEnum.TOTAL_VOLUME.getFieldName()) : BigDecimal.ZERO;
                BigDecimal totalNumber = dict.containsKey(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) ?
                    dict.getBigDecimal(StatisticsFieldEnum.TOTAL_NUMBER.getFieldName()) : BigDecimal.ZERO;
                buildShareData.setTotalWeight(totalWeight);
                buildShareData.setTotalVolume(totalVolume);
                buildShareData.setTotalNumber(totalNumber);
            }
            buildSharePOList.add(buildShareData);
        }

        // 使用CostUtil进行费用分摊
        buildSharePOList = CostUtil.allocateCost(buildMainCostInfo, buildSharePOList,
            StatisticsFieldEnum.getByValue(costResult.getShareType()),
            CostUtil.YF_COST_SETTLE_SUM_FIELDS, BmsYfexpensesMiddleSharePO.class);

        return buildSharePOList;
    }
}
