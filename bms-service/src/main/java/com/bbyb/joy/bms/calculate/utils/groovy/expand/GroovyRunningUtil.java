package com.bbyb.joy.bms.calculate.utils.groovy.expand;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * Groovy 运行时拓展工具方法类
 * 该类用于在groovy运行编译时调用工具方法
 */
public class GroovyRunningUtil {

    /*
     * 代码调用示例
     */
    // 在创建Binding后添加工具类
    // Binding binding = new Binding();
    // binding.setVariable("util", new GroovyExpandUtil());

    // 然后在表达式中可以这样调用
    // String expression = "util.numberEquals(factor.price, 100)";
    // Object result = shell.evaluate(expression);



    /**
     * 数字比较方法
     * @param num1 数字1
     * @param num2 数字2
     * @return 比较结果
     */
    public static boolean numberEquals(Number num1, Number num2) {
        if (num1 == null || num2 == null) {
            return false;
        }
        return new BigDecimal(num1.toString())
                .compareTo(new BigDecimal(num2.toString())) == 0;
    }

    /**
     * 列表包含判断
     * @param list 列表
     * @param element 元素
     * @return 是否包含
     */
    public static boolean contains(List<?> list, Object element) {
        return list != null && list.contains(element);
    }

    /**
     * 空值检查
     * @param obj 检查对象
     * @return 是否为空
     */
    public static boolean isNull(Object obj) {
        return obj == null;
    }

    /**
     * 加法运算
     * @param num1 数字1
     * @param num2 数字2
     * @return 相加结果
     * 加法调用示例
     * def sum = util.add(1.23, 4.56)
     */
    public static BigDecimal add(Number num1, Number num2) {
        return new BigDecimal(num1.toString()).add(new BigDecimal(num2.toString()));
    }

    /**
     * 减法运算
     * @param num1 数字1
     * @param num2 数字2
     * @return 相减结果
     * 减法调用示例
     * def difference = util.subtract(10.5, 3.2)
     */
    public static BigDecimal subtract(Number num1, Number num2) {
        return new BigDecimal(num1.toString()).subtract(new BigDecimal(num2.toString()));
    }

    /**
     * 乘法运算
     * @param num1 数字1
     * @param num2 数字2
     * @return 相乘结果
     * 乘法调用示例
     * def product = util.multiply(2.5, 3.75)
     */
    public static BigDecimal multiply(Number num1, Number num2) {
        return new BigDecimal(num1.toString()).multiply(new BigDecimal(num2.toString()));
    }

    /**
     * 除法运算
     * @param num1 被除数
     * @param num2 除数
     * @param scale 小数位数
     * @return 相除结果
     * 除法调用示例
     * def quotient = util.divide(10.5, 2.5, 2)
     */
    public static BigDecimal divide(Number num1, Number num2, int scale) {
        return new BigDecimal(num1.toString())
                .divide(new BigDecimal(num2.toString()), scale, RoundingMode.HALF_UP);
    }

    /**
     * 四舍五入
     * @param num 要舍入的数字
     * @param scale 保留小数位数
     * @return 舍入后的结果
     * 四舍五入调用示例
     * def rounded = util.round(12.345, 2)
     */
    public static BigDecimal round(Number num, int scale) {
        return new BigDecimal(num.toString())
                .setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * 比较大小
     * @param num1 数字1
     * @param num2 数字2
     * @return 1:num1>num2, -1:num1<num2, 0:相等
     * 比较大小调用示例
     * def comparison = util.compare(5.2, 3.7)
     */
    public static int compare(Number num1, Number num2) {
        return new BigDecimal(num1.toString())
                .compareTo(new BigDecimal(num2.toString()));
    }




    /**
     * 数值非空检查并转换
     * @param num 要检查的数值
     * @return 非空数值或BigDecimal.ZERO
     * 基本用法：空值转为0
     * def safeNum = util.nullToZero(maybeNullNumber)
     */
    public static BigDecimal nullToZero(Number num) {
        return num == null ? BigDecimal.ZERO : new BigDecimal(num.toString());
    }

    /**
     * 数值非空检查并转换(带默认值)
     * @param num 要检查的数值
     * @param defaultValue 默认值
     * @return 非空数值或指定的默认值
     * 带默认值的用法
     * def safeNum = util.nullToDefault(maybeNullNumber, 100)  // 如果空则返回100
     */
    public static BigDecimal nullToDefault(Number num, Number defaultValue) {
        return num == null ? new BigDecimal(defaultValue.toString()) : new BigDecimal(num.toString());
    }

}
