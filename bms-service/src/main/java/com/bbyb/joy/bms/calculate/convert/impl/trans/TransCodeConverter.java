package com.bbyb.joy.bms.calculate.convert.impl.trans;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.bbyb.joy.bms.calculate.convert.OrderConverter;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDto;
import com.bbyb.joy.bms.calculate.utils.CalculateUtil;
import com.bbyb.joy.bms.code.domain.dto.BmsTransCodeInfoDto;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 运输单据转换器
 */
@Component
public class TransCodeConverter implements OrderConverter<BmsTransCodeInfoDto, CalculateOrderDto> {

    @Override
    public List<CalculateOrderDto> convert(List<BmsTransCodeInfoDto> sourceList) {
        if(CollUtil.isEmpty(sourceList)){
            return Collections.emptyList();
        }

        List<CalculateOrderDto> targetList = new ArrayList<>(sourceList.size());
        for (BmsTransCodeInfoDto source : sourceList) {
            CalculateOrderDto target = BeanUtil.toBean(source,CalculateOrderDto.class);
            // 特殊字段处理
            // target.setXxx(source.getYyy());
            target.setCode(source.getRelateCode());
            target.setCodeType(BmsConstants.CODE_TYPE_01);
            target.setExpenseType(BmsConstants.EXPENSES_TYPE_1);
            target.setCompanyName(source.getCompanyName());

            CalculateUtil.BuildBusinessTimeModel businessTimeModel = CalculateUtil.billDateSetting(source.getOrderDate(), source.getSigningDate(), source.getCostDateDimension());
            target.setBusinessTime(businessTimeModel.getBusinessTime());
            target.setBusinessTimeStr(businessTimeModel.getBusinessTimeStr());
            target.setBusinessTimeMonth(businessTimeModel.getBusinessTimeMonth());
            target.setBusinessTimeDay(businessTimeModel.getBusinessTimeDay());
            target.setBusinessBeginTime(businessTimeModel.getBusinessBeginTime());
            target.setBusinessBeginStr(businessTimeModel.getBusinessBeginStr());
            target.setBusinessBeginMonth(businessTimeModel.getBusinessBeginMonth());
            target.setBusinessBeginDay(businessTimeModel.getBusinessBeginDay());
            target.setBusinessEndTime(businessTimeModel.getBusinessEndTime());
            target.setBusinessEndStr(businessTimeModel.getBusinessEndStr());
            target.setBusinessEndMonth(businessTimeModel.getBusinessEndMonth());
            target.setBusinessEndDay(businessTimeModel.getBusinessEndDay());
            target.setOrderDate(businessTimeModel.getBusinessBeginStr());
            target.setSigningDate(businessTimeModel.getBusinessEndStr());
            targetList.add(target);
        }

        return targetList;
    }

    @Override
    public Class<BmsTransCodeInfoDto> getSourceType() {
        return BmsTransCodeInfoDto.class;
    }

    @Override
    public Class<CalculateOrderDto> getTargetType() {
        return CalculateOrderDto.class;
    }
}
