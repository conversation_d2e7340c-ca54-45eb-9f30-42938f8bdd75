package com.bbyb.joy.bms.support;

import lombok.Getter;

/**
 * <AUTHOR> Mr <PERSON>
 * @since : 2023/5/26 19:20
 */
@Getter
public class CoreServerException extends RuntimeException {

    private final Integer code;
    private final String message;

    public CoreServerException(String message) {
        super(message);
        this.code = ServiceError.PARAMETER_ERROR.getCode();
        this.message = message;
    }

    public CoreServerException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
