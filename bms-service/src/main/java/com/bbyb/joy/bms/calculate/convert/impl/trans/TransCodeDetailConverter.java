package com.bbyb.joy.bms.calculate.convert.impl.trans;

import cn.hutool.core.bean.BeanUtil;
import com.bbyb.joy.bms.calculate.convert.OrderConverter;
import com.bbyb.joy.bms.calculate.domain.dto.code.order.CalculateOrderDetailDto;
import com.bbyb.joy.bms.code.domain.dto.BmsTransCodeDetailInfoDto;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 运输单据详情转换
 */
@Component
public class TransCodeDetailConverter implements OrderConverter<BmsTransCodeDetailInfoDto,CalculateOrderDetailDto> {

    @Override
    public List<CalculateOrderDetailDto> convert(List<BmsTransCodeDetailInfoDto> sourceList) {
        if(sourceList == null || sourceList.isEmpty()){
            return Collections.emptyList();
        }

        List<CalculateOrderDetailDto> targetList = new ArrayList<>(sourceList.size());
        for (BmsTransCodeDetailInfoDto source : sourceList) {
            CalculateOrderDetailDto target = BeanUtil.toBean(source, CalculateOrderDetailDto.class);
            // 特殊字段处理
            // target.setXxx(source.getYyy());
            if(target.getTotalBoxes() == null){
                target.setTotalBoxes(BigDecimal.ZERO);
            }
            if(target.getTotalOddBoxes()==null){
                target.setTotalOddBoxes(BigDecimal.ZERO);
            }
            if(target.getTotalOddBoxes() == null){
                target.setTotalOddBoxes(BigDecimal.ZERO);
            }
            if(target.getTotalNumber() == null){
                target.setTotalNumber(BigDecimal.ZERO);
            }
            if(target.getTotalWeight() == null){
                target.setTotalWeight(BigDecimal.ZERO);
            }
            if(target.getTotalVolume() == null){
                target.setTotalVolume(BigDecimal.ZERO);
            }
            if(target.getTotalAmount() == null){
                target.setTotalAmount(BigDecimal.ZERO);
            }
            targetList.add(target);
        }

        return targetList;
    }

    @Override
    public Class<BmsTransCodeDetailInfoDto> getSourceType() {
        return BmsTransCodeDetailInfoDto.class;
    }

    @Override
    public Class<CalculateOrderDetailDto> getTargetType() {
        return CalculateOrderDetailDto.class;
    }
}
