package com.bbyb.joy.bms.calculate.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.bbyb.bms.model.po.PubAutomaticLogImplementPO;
import com.bbyb.joy.bean.ResponseResult;
import com.bbyb.joy.bms.calculate.domain.param.CalculateDebuggerParam;
import com.bbyb.joy.bms.calculate.service.CalculateService;
import com.bbyb.joy.bms.code.domain.param.BmsDispatchCodeInfoParam;
import com.bbyb.joy.bms.code.domain.param.BmsTransCodeInfoParam;
import com.bbyb.joy.bms.code.domain.param.BmsYfStorageCodeInfoParam;
import com.bbyb.joy.bms.code.domain.param.BmsYsStorageCodeInfoParam;
import com.bbyb.joy.bms.domain.enums.AutoCalculateModuleEnum;
import com.bbyb.joy.bms.support.configuration.BmsConstants;
import com.bbyb.joy.bms.support.exception.BusinessException;
import com.bbyb.joy.bms.support.utils.ValidationUtil;
import com.bbyb.joy.bms.support.utils.thread.ThreadPool2;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;



@Api(tags = "单据自动计费操作")
@RestController
@RequestMapping("/system/calculate")
public class BmsCalculateController {

    @Resource
    CalculateService calculateService;
    @Resource
    private ThreadPool2 threadPool2;
    private static final Logger log = LoggerFactory.getLogger(BmsCalculateController.class);

    /**
     * 运输-自动计费
     */
    @PostMapping("/transCalculate")
    public ResponseResult<String> transCalculate(@RequestBody BmsTransCodeInfoParam param){

        if(calculateService.checkLock(AutoCalculateModuleEnum.TRANS)){
            throw new BusinessException("上次服务未执行完成,请等待！");
        }

        PubAutomaticLogImplementPO automaticTask = calculateService.lock(AutoCalculateModuleEnum.TRANS);

        Thread thread = new Thread(()->{

            CompletableFuture<Void> future = CompletableFuture.completedFuture(null);
            future = future.thenCompose(v -> {
                BmsTransCodeInfoParam param2 = BeanUtil.copyProperties(param, BmsTransCodeInfoParam.class);
                return CompletableFuture.runAsync(() -> {
                    calculateService.transCalculate(param2, CalculateDebuggerParam.builder().build());
                }, threadPool2.getExecutor());
            });

            // 等待所有任务按顺序完成(不然写费用表会出现幻读，导致费用表写错)
            try {
                future.get(); // 阻塞等待完成
            } catch (Exception e) {
                log.error("串行计费任务执行异常", e);
                throw new BusinessException("计费任务执行失败: " + e.getCause().getMessage());
            }finally {
                calculateService.unlock(automaticTask);
            }
        });

        thread.start();
        return new ResponseResult<>("操作成功!");
    }
    /**
     * 应收-仓储-自动计费
     */
    @PostMapping("/ysStorageCalculate")
    public ResponseResult<String> ysStorageCalculate(@RequestBody BmsYsStorageCodeInfoParam param){

        if(calculateService.checkLock(AutoCalculateModuleEnum.STORAGE_YS)){
            throw new BusinessException("上次服务未执行完成,请等待！");
        }

        PubAutomaticLogImplementPO automaticTask = calculateService.lock(AutoCalculateModuleEnum.STORAGE_YS);

        Thread thread = new Thread(()->{

            param.setCostMode(BmsConstants.COST_MODE_01);
            String checkMsg = ValidationUtil.validate(param);
            if(StrUtil.isNotEmpty(checkMsg)){
                throw new BusinessException(checkMsg);
            }

            List<Integer> codeTypes = CollUtil.isEmpty(param.getCodeTypes()) ? new ArrayList<>(BmsConstants.CODE_TYPE_STORAGE_SETS):param.getCodeTypes();
            CompletableFuture<Void> future = CompletableFuture.completedFuture(null);

            // 串行执行，每个任务等待前一个完成
            if(codeTypes.contains(BmsConstants.CODE_TYPE_2)){
                future = future.thenCompose(v -> {
                    BmsYsStorageCodeInfoParam param2 = BeanUtil.copyProperties(param, BmsYsStorageCodeInfoParam.class);
                    param2.setCodeTypes(List.of(BmsConstants.CODE_TYPE_2));
                    return CompletableFuture.runAsync(() -> {
                        calculateService.ysStorageCalculate(param2, CalculateDebuggerParam.builder().build());
                    }, threadPool2.getExecutor());
                });
            }
            if(codeTypes.contains(BmsConstants.CODE_TYPE_3)){
                future = future.thenCompose(v -> {
                    BmsYsStorageCodeInfoParam param3 = BeanUtil.copyProperties(param, BmsYsStorageCodeInfoParam.class);
                    param3.setCodeTypes(List.of(BmsConstants.CODE_TYPE_3));
                    return CompletableFuture.runAsync(() -> {
                        calculateService.ysStorageCalculate(param3, CalculateDebuggerParam.builder().build());
                    }, threadPool2.getExecutor());
                });
            }
            if(codeTypes.contains(BmsConstants.CODE_TYPE_4)){
                future = future.thenCompose(v -> {
                    BmsYsStorageCodeInfoParam param4 = BeanUtil.copyProperties(param, BmsYsStorageCodeInfoParam.class);
                    param4.setCodeTypes(List.of(BmsConstants.CODE_TYPE_4));
                    return CompletableFuture.runAsync(() -> {
                        calculateService.ysStorageCalculate(param4, CalculateDebuggerParam.builder().build());
                    }, threadPool2.getExecutor());
                });
            }

            // 等待所有任务按顺序完成(不然写费用表会出现幻读，导致费用表写错)
            try {
                future.handle((result, throwable) -> {
                    if (throwable != null) {
                        log.error("串行计费任务执行异常", throwable);
                    } else {
                        log.info("计费任务执行成功");
                    }
                    return null; // 返回 null，表示无论成功失败都继续
                }).get();
            } catch (Exception e) {
                log.error("串行计费任务执行异常", e);
                throw new BusinessException("计费任务执行失败: " + e.getCause().getMessage());
            }finally {
                calculateService.unlock(automaticTask);
            }
        });

        thread.start();
        return new ResponseResult<>("操作成功!");
    }
    /**
     * 调度单-自动计费
     */
    @PostMapping("/dispatchCalculate")
    public ResponseResult<String> dispatchCalculate(@RequestBody BmsDispatchCodeInfoParam param){

        if(calculateService.checkLock(AutoCalculateModuleEnum.DISPATCH_YF)){
            throw new BusinessException("上次服务未执行完成,请等待！");
        }

        PubAutomaticLogImplementPO automaticTask = calculateService.lock(AutoCalculateModuleEnum.DISPATCH_YF);

        Thread thread = new Thread(()->{

            CompletableFuture<Void> future = CompletableFuture.completedFuture(null);

            future = future.thenCompose(v -> {
                BmsDispatchCodeInfoParam param2 = BeanUtil.copyProperties(param, BmsDispatchCodeInfoParam.class);
                return CompletableFuture.runAsync(() -> {
                    calculateService.dispatchCalculate(param2, CalculateDebuggerParam.builder().build());
                }, threadPool2.getExecutor());
            });

            // 等待所有任务按顺序完成(不然写费用表会出现幻读，导致费用表写错)
            try {
                future.get(); // 阻塞等待完成
            } catch (Exception e) {
                log.error("串行计费任务执行异常", e);
                throw new BusinessException("计费任务执行失败: " + e.getCause().getMessage());
            }finally {
                calculateService.unlock(automaticTask);
            }
        });

        thread.start();
        return new ResponseResult<>("操作成功!");
    }
    /**
     * 应付-仓储-自动计费
     */
    @PostMapping("/yfStorageCalculate")
    public ResponseResult<String> yfStorageCalculate(@RequestBody BmsYfStorageCodeInfoParam param){

        if(calculateService.checkLock(AutoCalculateModuleEnum.STORAGE_YF)){
            throw new BusinessException("上次服务未执行完成,请等待！");
        }

        PubAutomaticLogImplementPO automaticTask = calculateService.lock(AutoCalculateModuleEnum.STORAGE_YF);

        Thread thread = new Thread(()->{

            param.setCostMode(BmsConstants.COST_MODE_02);
            String checkMsg = ValidationUtil.validate(param);
            if(StrUtil.isNotEmpty(checkMsg)){
                throw new BusinessException(checkMsg);
            }
            List<Integer> codeTypes = CollUtil.isEmpty(param.getCodeTypes()) ? new ArrayList<>(BmsConstants.CODE_TYPE_STORAGE_SETS):param.getCodeTypes();

            CompletableFuture<Void> future = CompletableFuture.completedFuture(null);

            // 串行执行，每个任务等待前一个完成
            if(codeTypes.contains(BmsConstants.CODE_TYPE_2)){
                future = future.thenCompose(v -> {
                    BmsYfStorageCodeInfoParam param2 = BeanUtil.toBean(param, BmsYfStorageCodeInfoParam.class);
                    param2.setCodeTypes(List.of(BmsConstants.CODE_TYPE_2));
                    return CompletableFuture.runAsync(() -> {
                        calculateService.yfStorageCalculate(param2, CalculateDebuggerParam.builder().build());
                        log.info("出库自动计费服务");
                    }, threadPool2.getExecutor());
                });
            }
            if(codeTypes.contains(BmsConstants.CODE_TYPE_3)){
                future = future.thenCompose(v -> {
                    BmsYfStorageCodeInfoParam param3 = BeanUtil.toBean(param, BmsYfStorageCodeInfoParam.class);
                    param3.setCodeTypes(List.of(BmsConstants.CODE_TYPE_3));
                    return CompletableFuture.runAsync(() -> {
                        calculateService.yfStorageCalculate(param3, CalculateDebuggerParam.builder().build());
                        log.info("入库自动计费服务");
                    }, threadPool2.getExecutor());
                });
            }
            if(codeTypes.contains(BmsConstants.CODE_TYPE_4)){
                future = future.thenCompose(v -> {
                    BmsYfStorageCodeInfoParam param4 = BeanUtil.copyProperties(param, BmsYfStorageCodeInfoParam.class);
                    param4.setCodeTypes(List.of(BmsConstants.CODE_TYPE_4));
                    return CompletableFuture.runAsync(() -> {
                        calculateService.yfStorageCalculate(param4, CalculateDebuggerParam.builder().build());
                        log.info("库存自动计费服务");
                    }, threadPool2.getExecutor());
                });
            }
            // 等待所有任务按顺序完成(不然写费用表会出现幻读，导致费用表写错)
            try {
                future.handle((result, throwable) -> {
                    if (throwable != null) {
                        log.error("串行计费任务执行异常", throwable);
                    } else {
                        log.info("计费任务执行成功");
                    }
                    return null; // 返回 null，表示无论成功失败都继续
                }).get();
            } catch (Exception e) {
                log.error("串行计费任务执行异常", e);
                throw new BusinessException("计费任务执行失败: " + e.getCause().getMessage());
            }finally {
                calculateService.unlock(automaticTask);
            }
        });

        thread.start();
        return new ResponseResult<>("操作成功!");
    }

}
